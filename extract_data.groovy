// 获取 content 字段
def content = request.get("content")

// 提取 fileKey
def fileKeyPattern = /fileKey:\s*([^,]+)/
def fileKeyMatcher = content =~ fileKeyPattern
def fileKey = ""
if (fileKeyMatcher.find()) {
    fileKey = fileKeyMatcher.group(1).trim()
}

// 提取 JSON 内容
def jsonPattern = /file fill content:\s*(\{.*\})/
def jsonMatcher = content =~ jsonPattern
def result = [:]

if (jsonMatcher.find()) {
    def jsonContent = jsonMatcher.group(1)

    // 添加 fileKey
    if (fileKey && !fileKey.isEmpty()) {
        result.fileKey = fileKey
    }

    // 定义需要提取的字段
    def fieldsToExtract = [
        'date', 'authResourceOrg', 'effectiveStartTime', 'effectiveEndTime',
        'authResourceTemplateDelete', 'authResourceApi', 'authResourceApproval',
        'authResourceContract', 'authResourceTemplateUse', 'authResourceTemplateAuth',
        'authResourceSeal', 'authResourceTemplate', 'authTenantName'
    ]

    // 提取非空字段
    fieldsToExtract.each { field ->
        def pattern = /"${field}"\s*:\s*"([^"]*)"/
        def matcher = jsonContent =~ pattern
        if (matcher.find()) {
            def value = matcher.group(1).trim()
            if (value && !value.isEmpty()) {
                // 对于以authResource开头的字段，如果值为"-"也不返回
                if (field.startsWith('authResource') && value == '-') {
                    return // 跳过这个字段
                }
                // 移除字段值前后的括号
                def cleanValue = value.replaceAll(/^\(|\)$/, '')
                result[field] = cleanValue
            }
        }
    }

    // 按顺序拼接租户名称字段（不包括authTenantName）
    def tenantNames = []
    ['parentTenantName', 'subTenantName', 'childTenantName'].each { field ->
        def pattern = /"${field}"\s*:\s*"([^"]*)"/
        def matcher = jsonContent =~ pattern
        if (matcher.find()) {
            def value = matcher.group(1).trim()
            if (value && !value.isEmpty()) {
                // 保留原始值（包括括号）
                tenantNames.add(value)
            }
        }
    }

    if (!tenantNames.isEmpty()) {
        result.concatenatedTenantNames = tenantNames.join('')
    }
}

// 返回结果
return result
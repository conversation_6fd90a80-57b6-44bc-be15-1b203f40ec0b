// 获取 content 字段
def content = request.get("content")

// 提取 fileKey
def fileKeyPattern = /fileKey:\s*([^,]+)/
def fileKeyMatcher = content =~ fileKeyPattern
def fileKey = ""
if (fileKeyMatcher.find()) {
    fileKey = fileKeyMatcher.group(1).trim()
}

// 提取 JSON 内容
def jsonPattern = /file fill content:\s*(\{.*\})/
def jsonMatcher = content =~ jsonPattern
def result = [:]

if (jsonMatcher.find()) {
    def jsonContent = jsonMatcher.group(1)

    // 添加 fileKey
    if (fileKey && !fileKey.isEmpty()) {
        result.fileKey = fileKey
    }

    // 定义需要提取的字段（根据实际数据格式）
    def fieldsToExtract = [
        'participantList', 'hashs', 'month', 'year', 'contractNo',
        'remark', 'day', 'fileNames'
    ]

    // 提取非空字段
    fieldsToExtract.each { field ->
        // 尝试字符串值模式
        def stringPattern = /"${field}"\s*:\s*"([^"]*)"/
        def stringMatcher = jsonContent =~ stringPattern

        // 尝试数组值模式
        def arrayPattern = /"${field}"\s*:\s*(\[[^\]]*\])/
        def arrayMatcher = jsonContent =~ arrayPattern

        if (stringMatcher.find()) {
            def value = stringMatcher.group(1).trim()
            if (value && !value.isEmpty()) {
                // 对于contractNo字段，如果值为"-"也不返回
                if (field == 'contractNo' && value == '-') {
                    // 跳过这个字段
                } else {
                    result[field] = value
                }
            }
        } else if (arrayMatcher.find()) {
            def arrayValue = arrayMatcher.group(1).trim()
            if (arrayValue && !arrayValue.isEmpty() && arrayValue != '[]') {
                // 检查数组是否只包含"-"
                if (field == 'contractNo' && arrayValue.contains('"-"')) {
                    // 跳过contractNo字段，如果数组中包含"-"
                } else {
                    // 直接返回数组值
                    result[field] = arrayValue
                }
            }
        }
    }
}

// 返回结果
return result
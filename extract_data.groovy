// 出参: result Map，包含所有非空的字段和拼接后的字符串

// 获取 content 字段
String content = request.get("content")

// 简单的 JSON 解析函数
def parseJsonValue(String jsonContent, String key) {
    def pattern = /"${key}"\s*:\s*"([^"]*)"/
def matcher = jsonContent =~ pattern
if (matcher.find()) {
    return matcher.group(1).trim()
}
return null
}

def extractDataFromLog(String logLine) {
    def result = [:]

    // 提取 fileKey
    def fileKeyPattern = /fileKey:\s*([^,]+)/
    def fileKeyMatcher = logLine =~ fileKeyPattern
    if (fileKeyMatcher.find()) {
        def fileKey = fileKeyMatcher.group(1).trim()
        if (fileKey && !fileKey.isEmpty()) {
            result.fileKey = fileKey
        }
    }

    // 提取 JSON 内容
    def jsonPattern = /file fill content:\s*(\{.*\})/
    def jsonMatcher = logLine =~ jsonPattern
    if (jsonMatcher.find()) {
        def jsonContent = jsonMatcher.group(1)

        // 定义需要提取的字段（不包括用于拼接的租户字段）
        def fieldsToExtract = [
                'date', 'authResourceOrg', 'effectiveStartTime', 'effectiveEndTime',
                'authResourceTemplateDelete', 'authResourceApi', 'authResourceApproval',
                'authResourceContract', 'authResourceTemplateUse', 'authResourceTemplateAuth',
                'authResourceSeal', 'authResourceTemplate', 'authTenantName'
        ]

        // 提取非空字段
        fieldsToExtract.each { field ->
            def value = parseJsonValue(jsonContent, field)
            if (value != null && !value.isEmpty()) {
                // 对于以authResource开头的字段，如果值为"-"也不返回
                if (field.startsWith('authResource') && value.trim() == '-') {
                    return // 跳过这个字段
                }
                // 移除字段值前后的括号
                def cleanValue = value.replaceAll(/^\(|\)$/, '')
                result[field] = cleanValue
            }
        }

        // 按顺序拼接租户名称字段（不包括authTenantName）
        def tenantNames = []
        ['parentTenantName', 'subTenantName', 'childTenantName'].each { field ->
            def value = parseJsonValue(jsonContent, field)
            if (value && !value.isEmpty()) {
                // 保留原始值（包括括号）
                tenantNames.add(value)
            }
        }

        if (!tenantNames.isEmpty()) {
            result.concatenatedTenantNames = tenantNames.join('')
        }
    }

    return result
}

// 执行提取
def extractedData = extractDataFromLog(content)

// 输出结果
println "提取的数据:"
println "=" * 50

extractedData.each { key, value ->
    println "${key}: ${value}"
}

println "\n" + "=" * 50
println "拼接的租户名称: ${extractedData.concatenatedTenantNames ?: '无有效租户名称'}"

// 如果你想要处理多行日志，可以使用这个函数
def processMultipleLogLines(String logContent) {
    def results = []
    logContent.split('\n').each { line ->
        if (line.contains('generateOrganizationAssociationConfirmationFileInfo')) {
            def extracted = extractDataFromLog(line)
            if (!extracted.isEmpty()) {
                results.add(extracted)
            }
        }
    }
    return results
}
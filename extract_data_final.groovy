#!/usr/bin/env groovy

// 示例日志数据（包含值为"-"的authResource字段用于测试）
def logData = '''2025-09-09 17:34:04,479 INFO  impl.AuthRelationCoreServiceImpl - [ConsumeMessageThread_1] [TrackId=10100011220T233T17574104433270893] [MsgId=FE800000000000006C0222FFFEDD1CF5000027002C5A2CF7E5932F79] generateRelationFile fileKey: $b766b14b-f98e-4976-a70d-33aac6e47ab8$4038515047, file fill content: {"date":"2025 年 09 月 09 日","authResourceOrg":"-","effectiveEndTime":"2030 年 07 月 31 日","authResourceTemplateDelete":"","parentTenantName":"(esigntest多组织测试企业D1)","authResourceApi":"","authResourceApproval":"","authResourceContract":"","authResourceTemplateUse":"","effectiveStartTime":"2025 年 09 月 09 日","authResourceTemplateAuth":"","subTenantName":"、(esigntest多组织测试企业S)","authTenantName":"(esigntest多组织测试企业A1)","authResourceSeal":"","authResourceTemplate":"","childTenantName":"、(esigntest多组织测试企业T)"} '''

// 简单的 JSON 解析函数
def parseJsonValue(jsonContent, key) {
    def pattern = /"${key}"\s*:\s*"([^"]*)"/
    def matcher = jsonContent =~ pattern
    if (matcher.find()) {
        return matcher.group(1).trim()
    }
    return null
}

def extractDataFromLog(logLine) {
    def result = [:]
    
    // 提取 fileKey
    def fileKeyPattern = /fileKey:\s*([^,]+)/
    def fileKeyMatcher = logLine =~ fileKeyPattern
    if (fileKeyMatcher.find()) {
        def fileKey = fileKeyMatcher.group(1).trim()
        if (fileKey && !fileKey.isEmpty()) {
            result.fileKey = fileKey
        }
    }
    
    // 提取 JSON 内容
    def jsonPattern = /file fill content:\s*(\{.*\})/
    def jsonMatcher = logLine =~ jsonPattern
    if (jsonMatcher.find()) {
        def jsonContent = jsonMatcher.group(1)
        
        // 定义需要提取的字段（不包括用于拼接的租户字段）
        def fieldsToExtract = [
            'date', 'authResourceOrg', 'effectiveStartTime', 'effectiveEndTime',
            'authResourceTemplateDelete', 'authResourceApi', 'authResourceApproval',
            'authResourceContract', 'authResourceTemplateUse', 'authResourceTemplateAuth',
            'authResourceSeal', 'authResourceTemplate', 'authTenantName'
        ]
        
        // 提取非空字段
        fieldsToExtract.each { field ->
            def value = parseJsonValue(jsonContent, field)
            if (value != null && !value.isEmpty()) {
                // 对于以authResource开头的字段，如果值为"-"也不返回
                if (field.startsWith('authResource') && value.trim() == '-') {
                    return // 跳过这个字段
                }
                // 移除字段值前后的括号
                def cleanValue = value.replaceAll(/^\(|\)$/, '')
                result[field] = cleanValue
            }
        }
        
        // 按顺序拼接租户名称字段（不包括authTenantName）
        def tenantNames = []
        ['parentTenantName', 'subTenantName', 'childTenantName'].each { field ->
            def value = parseJsonValue(jsonContent, field)
            if (value && !value.isEmpty()) {
                // 保留原始值（包括括号）
                tenantNames.add(value)
            }
        }
        
        if (!tenantNames.isEmpty()) {
            result.concatenatedTenantNames = tenantNames.join('')
        }
    }
    
    return result
}

// 执行提取
def extractedData = extractDataFromLog(logData)

// 输出结果
println "提取的数据:"
println "=" * 50

extractedData.each { key, value ->
    println "${key}: ${value}"
}

println "\n" + "=" * 50
println "拼接的租户名称: ${extractedData.concatenatedTenantNames ?: '无有效租户名称'}"

// 验证过滤功能
println "\n验证：authResourceOrg字段值为'-'，应该被过滤掉"
println "authResourceOrg是否存在: ${extractedData.containsKey('authResourceOrg')}"

// 如果你想要处理多行日志，可以使用这个函数
def processMultipleLogLines(logContent) {
    def results = []
    logContent.split('\n').each { line ->
        if (line.contains('generateOrganizationAssociationConfirmationFileInfo') || line.contains('generateRelationFile')) {
            def extracted = extractDataFromLog(line)
            if (!extracted.isEmpty()) {
                results.add(extracted)
            }
        }
    }
    return results
}

// 示例：处理单行日志的函数
def processSingleLogLine(logLine) {
    return extractDataFromLog(logLine)
}

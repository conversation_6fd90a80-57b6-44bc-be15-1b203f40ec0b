package com.timevale.contractmanager.task;

import com.timevale.contractmanager.core.service.component.opponent.detection.OpponentDetectionAdapter;
import com.timevale.framework.schedulerT.client.annotaion.JobHandler;
import com.timevale.framework.schedulerT.core.biz.model.ReturnT;
import com.timevale.framework.schedulerT.core.handler.IJobHandler;
import com.timevale.framework.schedulerT.core.log.JobLogger;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author:jianyang
 * @since 2021-09-01 11:29
 */
@JobHandler
public class OpponentDetectionSendResultJob extends IJobHandler {
	@Autowired
	private OpponentDetectionAdapter detectionAdapter;
	@Override
	public ReturnT<String> execute(String s) throws Exception {
		try {
			JobLogger.log("开始执行任务OpponentDetectionOrgJob");
			detectionAdapter.sendDetectionResult();
			JobLogger.log("任务OpponentDetectionOrgJob执行结束");
		}catch (Exception e){
			JobLogger.log( "job error {}",e.getMessage(), e);
			return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
		}
		return ReturnT.SUCCESS;
	}
}

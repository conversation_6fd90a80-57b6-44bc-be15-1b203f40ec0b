package com.timevale.contractmanager.task;

import com.timevale.contractmanager.common.dal.dao.OfflineContractImportRecordDAO;
import com.timevale.contractmanager.core.service.offlinecontract.OfflineContractService;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.framework.schedulerT.client.annotaion.JobHandler;
import com.timevale.framework.schedulerT.core.biz.model.ReturnT;
import com.timevale.framework.schedulerT.core.handler.IJobHandler;
import com.timevale.framework.schedulerT.core.log.JobLogger;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.MathUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

import static com.timevale.contractmanager.common.utils.config.SystemConfig.RECORD_REMAIN_DAYS;

/**
 * 功能说明：清理超出保存时间范围的线下合同导入记录
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@JobHandler
public class OfflineContractRecordClearJob extends IJobHandler {
    /** 线下合同导入记录分批清理， 每个批次的记录数量 */
    private static final String PROP_CLEAR_BATCH_SIZE = "offline.contract.record.clear.batch.size";

    @Autowired private OfflineContractImportRecordDAO offlineContractImportRecordDAO;
    @Autowired private OfflineContractService offlineContractService;

    @Override
    public ReturnT<String> execute(String s) {
        try {
            JobLogger.log("开始执行任务OfflineContractRecordClearJob");
            clearOfflineContractRecords();
            JobLogger.log("任务OfflineContractRecordClearJob执行结束");
        } catch (Exception e) {
            JobLogger.log("job error {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
        // 返回结果
        return ReturnT.SUCCESS;
    }

    /** 清理线下合同导入记录 */
    private void clearOfflineContractRecords() {
        Integer remainDays = ConfigService.getAppConfig().getIntProperty(RECORD_REMAIN_DAYS, 90);
        Integer batchSize = ConfigService.getAppConfig().getIntProperty(PROP_CLEAR_BATCH_SIZE, 100);
        Date lastRemainDate = DateUtils.addDays(new Date(), -remainDays);
        List<String> recordIds =
                offlineContractImportRecordDAO.selectRecordIdsByLastRemainDate(lastRemainDate);
        if (CollectionUtils.isEmpty(recordIds)) {
            return;
        }
        int times = recordIds.size() / batchSize;
        if (recordIds.size() % batchSize != 0) {
            times += 1;
        }
        for (int i = 0; i < times; i++) {
            int startIndex = i * batchSize;
            int endIndex = MathUtils.min((i + 1) * batchSize, recordIds.size());
            List<String> batchRecordIds = recordIds.subList(startIndex, endIndex);
            offlineContractService.deleteOfflineContractRecords(batchRecordIds);
        }
    }
}

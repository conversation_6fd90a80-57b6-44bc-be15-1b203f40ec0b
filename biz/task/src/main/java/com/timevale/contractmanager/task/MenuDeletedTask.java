package com.timevale.contractmanager.task;

import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.framework.schedulerT.client.annotaion.JobHandler;
import com.timevale.framework.schedulerT.core.biz.model.ReturnT;
import com.timevale.framework.schedulerT.core.handler.IJobHandler;
import com.timevale.mandarin.base.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 处理菜单删除逻辑的任务
 *
 * @author: jinhuan
 * @since: 2019-10-12 10:38
 */
@Slf4j
@JobHandler
public class MenuDeletedTask extends IJobHandler {

    @Autowired private MenuService menuService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        execute();
        return ReturnT.SUCCESS;
    }

    public void execute() {
        long st = System.currentTimeMillis();
        log.info("clear menu job start......");
        try {
            List<String> menuIdList = menuService.getDeletedMenuList();
            if (ListUtils.isEmpty(menuIdList)) {
                return;
            }
            for (String menuId : menuIdList) {
                menuService.deleteMenu(menuId);
            }
        } catch (Exception e) {
            log.warn("clear menu job error ", e);
        }
        long et = System.currentTimeMillis();
        log.info("clear menu job end cost:{} ms", (et - st) / 1000);
    }
}

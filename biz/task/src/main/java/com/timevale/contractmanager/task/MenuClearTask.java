package com.timevale.contractmanager.task;

import com.timevale.contractmanager.common.dal.bean.MenuWaitClearDO;
import com.timevale.contractmanager.common.dal.bean.grouping.GroupingInfoDO;
import com.timevale.contractmanager.common.dal.dao.MenuWaitClearDAO;
import com.timevale.contractmanager.common.dal.dao.grouping.GroupingInfoDAO;
import com.timevale.contractmanager.common.dal.dao.grouping.MenuDAO;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.service.component.grouping.GroupingFileComponent;
import com.timevale.contractmanager.core.service.enums.YesNoEnum;
import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.framework.schedulerT.client.annotaion.JobHandler;
import com.timevale.framework.schedulerT.core.biz.model.ReturnT;
import com.timevale.framework.schedulerT.core.handler.IJobHandler;
import com.timevale.framework.schedulerT.core.log.JobLogger;
import com.timevale.mandarin.base.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Slf4j
@JobHandler
public class MenuClearTask extends IJobHandler {

    private static final int PAGE_SIZE = 100;

    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private MenuWaitClearDAO menuWaitClearDAO;

    @Autowired
    private MenuService menuService;

    @Autowired
    private MenuDAO menuDAO;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private GroupingInfoDAO groupingInfoDAO;

    @Autowired
    private GroupingFileComponent groupingFileComponent;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            JobLogger.log("开始执行任务MenuClearTask");
            doScheduler();
            JobLogger.log("任务MenuClearTask执行结束");
        } catch (Exception e) {
            JobLogger.log( "job error {}",e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
        // 返回结果
        return ReturnT.SUCCESS;
    }

    private void doScheduler() {
        MenuWaitClearDO clearMenuDO = menuWaitClearDAO.getNext();
        if (clearMenuDO == null) {
            log.info("wait clear menu do not exist");
            return;
        }

        AtomicReference<MenuWaitClearDO> currentMenuDO = new AtomicReference<>(clearMenuDO);
        int max = systemConfig.getMenuClearMax();
        int count = 0;
        while (count < max && currentMenuDO.get() != null) {
            //一次性数据清除
            if (YesNoEnum.N.code() == currentMenuDO.get().getPreClear()) {
                transactionTemplate.execute(action -> {
                    menuService.preClearMenu(currentMenuDO.get().getMenuId());
                    menuWaitClearDAO.updatePreClearById(currentMenuDO.get().getId(), YesNoEnum.Y.code());
                    return null;
                });
            }

            //每次100条清除合同分类关系
            List<GroupingInfoDO> groupingInfoDOList =
                    groupingInfoDAO.queryByMenuId(currentMenuDO.get().getMenuId(), PAGE_SIZE);
            if (ListUtils.isEmpty(groupingInfoDOList)) {
                menuClearEnd(currentMenuDO);
                continue;
            }

            //删除合同分类关系
            deleteDBAndEs(groupingInfoDOList, currentMenuDO.get().getMenuId());
            //记录清除的数量
            count += groupingInfoDOList.size();
            if (groupingInfoDOList.size() < PAGE_SIZE) {
                menuClearEnd(currentMenuDO);
            }
        }
        log.info("menu clear job end, count:{}", count);
    }

    private void deleteDBAndEs(List<GroupingInfoDO> groupingInfoDOList, String menuId) {
        List<Long> deleteGroupingIds = new ArrayList<>();
        List<String> processIdList = new ArrayList<>();
        for (GroupingInfoDO groupingInfoDO : groupingInfoDOList) {
            deleteGroupingIds.add(groupingInfoDO.getId());
            processIdList.add(groupingInfoDO.getProcessId());
        }

        groupingInfoDAO.deleteByIds(deleteGroupingIds);

        // es中移出分类
        groupingFileComponent.reMoveArchive(
                menuId, processIdList, groupingInfoDOList.get(0).getSubjectOid());
    }

    private void menuClearEnd(AtomicReference<MenuWaitClearDO> currentMenuDO) {
        log.info("menu clear end, menuId:{}", currentMenuDO.get().getMenuId());
        menuWaitClearDAO.deleteById(currentMenuDO.get().getId());
        menuDAO.deleteByMenuId(currentMenuDO.get().getMenuId());

        //查找下一个分类
        currentMenuDO.set(menuWaitClearDAO.getNext());
    }
}

package com.timevale.contractmanager.task;

import com.timevale.contractmanager.core.service.process.datasource.ProcessStartDataManager;
import com.timevale.framework.schedulerT.client.annotaion.JobHandler;
import com.timevale.framework.schedulerT.core.biz.model.ReturnT;
import com.timevale.framework.schedulerT.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024/9/26 15:19
 */
@JobHandler
@Slf4j
public class ProcessStartDataDeleteJob extends IJobHandler {

    @Autowired
    private ProcessStartDataManager processStartDataManager;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        // todo tianlei 低代码区分更新数据消息和创建之后才可开启
        try {
            processStartDataManager.deleteSuccessData();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("processStartDataDeleteJob", e);
            return ReturnT.FAIL;
        }
    }

}

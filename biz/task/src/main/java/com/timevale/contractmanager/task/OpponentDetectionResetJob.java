package com.timevale.contractmanager.task;

import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionSettingDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionSettingDAO;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.framework.schedulerT.client.annotaion.JobHandler;
import com.timevale.framework.schedulerT.core.biz.model.ReturnT;
import com.timevale.framework.schedulerT.core.handler.IJobHandler;
import com.timevale.framework.schedulerT.core.log.JobLogger;
import com.timevale.mandarin.base.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:jianyang
 * @since 2021-08-25 17:39
 */
@JobHandler
public class OpponentDetectionResetJob extends IJobHandler {

	@Autowired private OpponentDetectionSettingDAO settingDAO;

	@Override
	public ReturnT<String> execute(String s) throws Exception {
		try {
			JobLogger.log("开始执行任务OpponentDetectionResetJob");
			execute();
			JobLogger.log("任务OpponentDetectionResetJob执行结束");
		}catch (Exception e){
			JobLogger.log( "job error {}",e.getMessage(), e);
			return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
		}
		return ReturnT.SUCCESS;
	}

	/**
	 * 执行重置使用次数
	 */
	public void execute(){
		long size = 500;
		long offset = 0;
		boolean stop = true;

		/** 分批重置*/
		do{
			List<OpponentDetectionSettingDO> list = settingDAO.getList(offset, size, DeletedEnum.NO.code());
			if(CollectionUtils.isNotEmpty(list)){
				settingDAO.updateUsedDetectionNum(list.stream().map(OpponentDetectionSettingDO::getTenantGid).collect(Collectors.toList()));
				offset += list.size();
			}else {
				stop = false;
			}
		}while (stop);

	}
}

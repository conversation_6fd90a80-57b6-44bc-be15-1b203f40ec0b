package com.timevale.contractmanager.task;

import com.timevale.contractmanager.core.service.process.ProcessExpireService;
import com.timevale.framework.schedulerT.client.annotaion.JobHandler;
import com.timevale.framework.schedulerT.core.biz.model.ReturnT;
import com.timevale.framework.schedulerT.core.handler.IJobHandler;
import com.timevale.framework.schedulerT.core.log.JobLogger;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 功能说明：处理一天内已过期的流程
 *
 * <AUTHOR>
 * @date 2020-12-13
 */
@JobHandler
public class DealOutOfDateProcessJob extends IJobHandler {
    @Autowired
    private ProcessExpireService processExpireService;

    @Override
    public ReturnT<String> execute(String s) {
        try {
            JobLogger.log("开始执行任务DealOutOfDateProcessJob");
            processExpireService.processExpireJob();
            JobLogger.log("任务DealOutOfDateProcessJob执行结束");
        } catch (Exception e) {
            JobLogger.log( "job error {}",e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
        // 返回结果
        return ReturnT.SUCCESS;

    }
}

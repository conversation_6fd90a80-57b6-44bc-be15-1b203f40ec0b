<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.timevale.contractmanager</groupId>
		<artifactId>contractmanager-parent</artifactId>
		<version>1.0.1</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

	<artifactId>contractmanager-biz-task</artifactId>
	<name>contractmanager/task</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>contractmanager-biz-service-impl</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- schedulerT-client -->
		<dependency>
			<groupId>com.timevale.framework</groupId>
			<artifactId>schedulerT-client</artifactId>
			<version>3.0.8</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.saas-auth-api</groupId>
			<artifactId>ding-spring-boot-starter</artifactId>
			<version>1.2.0-SNAPSHOT</version>
		</dependency>
	</dependencies>
</project>

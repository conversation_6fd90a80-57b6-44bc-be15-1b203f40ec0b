package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.result.ProcessStartInitConfigResult;
import com.timevale.contractmanager.core.model.bo.AttachmentConfigTypeBO;
import com.timevale.contractmanager.core.model.bo.ParticipantsSignConfigBO;
import com.timevale.contractmanager.core.model.bo.StartSuccessCacheBO;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartInitConfigResponse;
import org.apache.commons.collections4.CollectionUtils;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.ProcessConfigDO;
import com.timevale.contractmanager.common.service.api.RpcProcessStartService;
import com.timevale.contractmanager.common.service.bean.ContractProcessGroupBean;
import com.timevale.contractmanager.common.service.bean.ProcessConfigBean;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.model.ProcessStartFlowTemplateModel;
import com.timevale.contractmanager.common.service.model.ProcessStartInitConfigModel;
import com.timevale.contractmanager.common.service.model.StartBizProcessModel;
import com.timevale.contractmanager.common.service.model.StartProcessModel;
import com.timevale.contractmanager.common.service.result.ProcessStartFlowTemplateResult;
import com.timevale.contractmanager.common.service.result.StartProcessResult;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBaseRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartInitConfigRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessStartMode;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.core.service.process.ProcessConfigService;
import com.timevale.contractmanager.core.service.process.door.ProcessDoor;
import com.timevale.contractmanager.core.service.process.impl.ProcessStartHelper;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.tlcache.cache.ThreadLocalCache;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2021-08-19
 */
@Slf4j
@RestService
public class RpcProcessStartServiceImpl implements RpcProcessStartService {

    @Autowired private MapperFactory mapperFactory;

    @Autowired private UserCenterService userCenterService;

    @Autowired private ProcessStartHelper processStartHelper;

    @Autowired private SaasCommonClient saasCommonClient;

    @Autowired private FlowTemplateService flowTemplateService;

    @Autowired private BaseProcessService baseProcessService;
    @Autowired private ProcessDoor processDoor;

    @Autowired private ProcessConfigService processConfigService;

    @Override
    public StartProcessResult startProcess(StartProcessModel model) {
        ProcessStartCoreRequest request = buildProcessStartCoreRequest(model);
        // 前置处理流程发起请求参数
        preHandleProcessStartRequest(model, request);
        // 判断是否支持相对方风控
        boolean checkRiskLevel =
                saasCommonClient.checkFunctionValid(
                        model.getInitiatorSubjectId(), FunctionCodeConstants.RISK_LEVEL, false);
        request.setCheckOpponentRisk(checkRiskLevel);
        // 发起流程
        ProcessStartResult response = processDoor.coreStart(request);

        // 组装返回结果
        StartProcessResult result = new StartProcessResult();
        result.setProcessGroupId(request.getProcessGroupId());
        result.setProcessId(response.getProcessId());
        result.setResultUrl(response.getResultUrl());
        result.setLongResultUrl(response.getLongResultUrl());
        result.setFlowId(response.getFlowId());
        result.setFlowType(response.getFlowType());
        return result;
    }

    @ThreadLocalCache
    @Override
    public StartProcessResult startBizProcess(StartBizProcessModel model) {
        ProcessStartBizRequest request = buildProcessStartBizRequest(model);
        // 前置处理流程发起请求参数
        preHandleProcessStartRequest(model, request);
        // 补充合同编号类型
        flowTemplateService.supplyContractNoType(
                model.getInitiatorSubjectId(),
                request.getFlowTemplateId(),
                null,
                request.getContracts());
        // 发起流程
        ProcessStartResult response = processDoor.start(request);

        // 组装返回结果

        StartProcessResult result = new StartProcessResult();
        result.setProcessGroupId(response.getGroupId());
        result.setProcessId(response.getProcessId());
        result.setResultUrl(response.getResultUrl());
        result.setLongResultUrl(response.getLongResultUrl());
        result.setFlowId(response.getFlowId());
        result.setFlowType(response.getFlowType());
        return result;
    }

    @Override
    public ProcessStartFlowTemplateResult getStartFlowTemplateInfo(
            ProcessStartFlowTemplateModel model) {
        ProcessConfigDO processConfigDO =
                baseProcessService.queryProcessConfig(model.getProcessId());
        ProcessStartFlowTemplateResult result = new ProcessStartFlowTemplateResult();
        if (processConfigDO.getConfigInfo() != null) {
            ProcessConfigBean processConfigBean =
                    JSON.parseObject(processConfigDO.getConfigInfo(), ProcessConfigBean.class);
            result.setStartFlowTemplateId(processConfigBean.getStartFlowTemplateId());
            result.setOriginFlowTemplateId(processConfigBean.getFlowTemplateId());
            result.setIsUnStandardTemplate(processConfigBean.getIsUnStandardTemplate());
        }
        return result;
    }

    @Override
    public ProcessStartInitConfigResult getProcessStartInitConfig(
            ProcessStartInitConfigModel model) {
        ProcessStartInitConfigResponse response =
                processConfigService.getProcessStartInitConfig(
                        buildProcessStartInitConfigRequest(model));
        return  response==null?null:buildProcessStartInitConfigResult(response);
    }

    private ProcessStartInitConfigResult buildProcessStartInitConfigResult(ProcessStartInitConfigResponse response) {
        ProcessStartInitConfigResult result = new ProcessStartInitConfigResult();
        
        // 设置基本属性
        result.setWillTypes(response.getWillTypes());
        result.setParticipantSource(response.getParticipantSource());
        result.setCanRedirectStart(response.isCanRedirectStart());
        result.setCanDownloadAllFile(response.isCanDownloadAllFile());
        result.setPartDownloadFiles(response.getPartDownloadFiles());
        result.setStartSeqNo(response.getStartSeqNo());
        result.setSignMode(response.getSignMode());
        
        // 转换StartSuccessCacheBO到StartSuccessCacheDTO
        StartSuccessCacheBO startSuccessCacheBO = response.getLastStartData();
        if (startSuccessCacheBO != null) {
            ProcessStartInitConfigResult.StartSuccessCacheDTO startSuccessCacheDTO = new ProcessStartInitConfigResult.StartSuccessCacheDTO();
            startSuccessCacheDTO.setSignMode(startSuccessCacheBO.getSignMode());
            startSuccessCacheDTO.setWatermarkTemplateId(startSuccessCacheBO.getWatermarkTemplateId());
            
            // 转换ParticipantsSignConfigBO到ParticipantsSignConfigDTO
            List<ProcessStartInitConfigResult.ParticipantsSignConfigDTO> participantsSignConfigDTOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(startSuccessCacheBO.getParticipantsSignConfigs())) {
                for (ParticipantsSignConfigBO bo : startSuccessCacheBO.getParticipantsSignConfigs()) {
                    ProcessStartInitConfigResult.ParticipantsSignConfigDTO dto = new ProcessStartInitConfigResult.ParticipantsSignConfigDTO();
                    dto.setSignRequirements(bo.getSignRequirements());
                    dto.setSignOrder(bo.getSignOrder());
                    dto.setSealType(bo.getSealType());
                    dto.setSignSealType(bo.getSignSealType());
                    dto.setSignSeal(bo.getSignSeal());
                    dto.setForceReadEnd(bo.getForceReadEnd());
                    dto.setForceReadTime(bo.getForceReadTime());
                    dto.setParticipantSubjectType(bo.getParticipantSubjectType());
                    dto.setWillTypes(bo.getWillTypes());
                    dto.setNeedWill(bo.getNeedWill());
                    dto.setNoticeTypes(bo.getNoticeTypes());
                    dto.setSignTipsTitle(bo.getSignTipsTitle());
                    dto.setSignTipsContent(bo.getSignTipsContent());
                    dto.setAuthWay(bo.getAuthWay());
                    dto.setAccessToken(bo.getAccessToken());
                    participantsSignConfigDTOList.add(dto);
                }
            }
            startSuccessCacheDTO.setParticipantsSignConfigs(participantsSignConfigDTOList);
            result.setLastStartData(startSuccessCacheDTO);
        }
        
        // 转换AttachmentConfigTypeBO到AttachmentConfigTypeDTO
        List<ProcessStartInitConfigResult.AttachmentConfigTypeDTO> attachmentConfigTypeDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(response.getAttachmentConfigTypeList())) {
            for (com.timevale.contractmanager.core.model.bo.AttachmentConfigTypeBO bo : response.getAttachmentConfigTypeList()) {
                ProcessStartInitConfigResult.AttachmentConfigTypeDTO dto = new ProcessStartInitConfigResult.AttachmentConfigTypeDTO();
                dto.setType(bo.getType());
                dto.setName(bo.getName());
                dto.setSubjectCheck(bo.getSubjectCheck());
                dto.setPersonCheck(bo.getPersonCheck());
                attachmentConfigTypeDTOList.add(dto);
            }
        }
        result.setAttachmentConfigTypeList(attachmentConfigTypeDTOList);
        
        return result;
    }

    /**
     * 前置处理流程发起请求参数
     *
     * @param model
     * @param request
     */
    private void preHandleProcessStartRequest(
            StartProcessModel model, ProcessStartBaseRequest request) {
        RequestContextExtUtils.setClientId(model.getClientId());
        RequestContextExtUtils.setIsvAppId(model.getIsvAppId());
        RequestContextExtUtils.setDingCorpId(model.getDingCorpId());
        // 获取流程发起模式， 判断使用直接发起模式还是基于流程模板发起模式
        // 判断条件：是否指定流程模板ID，如果是，则基于流程模板发起，否则默认直接发起
        ProcessStartMode startMode =
                StringUtils.isBlank(request.getFlowTemplateId())
                        ? ProcessStartMode.DIRECT_START
                        : ProcessStartMode.TEMPLATE_START;
        request.setStartMode(startMode.getMode());
        // 获取流程发起场景， 判断使用直接发起还是模板发起
        if (null == request.getStartScene()) {
            // 判断条件：是否指定流程模板ID，如果是，则模板发起，否则默认直接发起
            ProcessStartScene startScene =
                    StringUtils.isBlank(request.getFlowTemplateId())
                            ? ProcessStartScene.DIRECT_START
                            : ProcessStartScene.TEMPLATE_START;
            request.setStartScene(startScene.getScene());
        }
    }

    /**
     * 转换为ProcessStartCoreRequest
     *
     * @param model 发起参数
     * @return
     */
    private ProcessStartCoreRequest buildProcessStartCoreRequest(StartProcessModel model) {
        // 查询发起主体信息
        UserAccountDetail tenant =
                userCenterService.getUserAccountDetailByOid(model.getInitiatorSubjectId());
        // 查询发起人信息
        UserAccountDetail initiator =
                userCenterService.getUserAccountDetailByOid(model.getInitiatorAccountId());
        // 查询付费主体信息
        UserAccount payerAccount;
        if (StringUtils.isNotBlank(model.getPayerAccountId())) {
            payerAccount = userCenterService.getUserAccountBaseByOid(model.getPayerAccountId());
        } else {
            payerAccount = processStartHelper.getBillSubject(tenant);
        }
        // 组装发起参数
        ProcessStartCoreRequest request =
                mapperFactory.getMapperFacade().map(model, ProcessStartCoreRequest.class);
        request.setInitiatorAccount(initiator);
        request.setTenantAccount(tenant);
        request.setPayerAccount(payerAccount);
        request.setSignPlatform(model.getPlatform());
        request.setBizScene(RequestContextExtUtils.getXuanYuanBizScene().getFrom());
        // 读取流程额外配置， 设置合同保密配置
        if (null != model.getExtraConfig()) {
            List<FileBO> files = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(request.getContracts())) {
                files.addAll(request.getContracts());
            }
            if (CollectionUtils.isNotEmpty(request.getAttachments())) {
                files.addAll(request.getAttachments());
            }
            request.setProcessSecretConfig(
                    processStartHelper.buildProcessSecretConfig(
                            model.getExtraConfig().getSecretType(),
                            files,
                            model.getExtraConfig().getVisibleAccounts()));
        }
        // 处理流程组相关参数
        if (StringUtils.isNotBlank(request.getProcessGroupId())) {
            ContractProcessGroupBean groupBean =
                    processStartHelper.queryProcessGroup(request.getProcessGroupId());
            request.setProcessGroupName(groupBean.getProcessGroupName());
            request.setRpcStart(Boolean.TRUE);
        }
        return request;
    }

    /**
     * 转换为ProcessStartCoreRequest
     *
     * @param model 发起参数
     * @return
     */
    private ProcessStartBizRequest buildProcessStartBizRequest(StartBizProcessModel model) {
        // 组装发起参数
        ProcessStartBizRequest request =
                mapperFactory.getMapperFacade().map(model, ProcessStartBizRequest.class);
        request.setAccountId(model.getInitiatorAccountId());
        request.setTenantId(model.getInitiatorSubjectId());
        // 查询付费主体信息
        UserAccount payerAccount;
        if (StringUtils.isNotBlank(model.getPayerAccountId())) {
            payerAccount = userCenterService.getUserAccountBaseByOid(model.getPayerAccountId());
            request.setPayerAccount(payerAccount);
        }
        // 读取流程额外配置
        if (null != model.getExtraConfig()) {
            request.setStartType(model.getExtraConfig().getStartType());
            request.setSecretType(model.getExtraConfig().getSecretType());
            request.setVisibleAccounts(model.getExtraConfig().getVisibleAccounts());
            request.setInitiatorDeptId(model.getExtraConfig().getInitiatorDeptId());
        }
        request.setBizGroupId(model.getBizGroupId());
        request.setRelationProcessIds(model.getRelationProcessIds());
        request.setSignPlatform(model.getPlatform());
        request.setBizScene(RequestContextExtUtils.getXuanYuanBizScene().getFrom());
        request.setSignMode(model.getSignModel());
        // 处理流程组相关参数
        if (StringUtils.isNotBlank(request.getProcessGroupId())) {
            ContractProcessGroupBean groupBean =
                    processStartHelper.queryProcessGroup(request.getProcessGroupId());
            request.setProcessGroupName(groupBean.getProcessGroupName());
        }
        request.setNoticeType(model.getNoticeType());
        return request;
    }

    private ProcessStartInitConfigRequest buildProcessStartInitConfigRequest(
            ProcessStartInitConfigModel model) {
        ProcessStartInitConfigRequest request = new ProcessStartInitConfigRequest();
        request.setFlowTemplateId(model.getFlowTemplateId());
        request.setClientId(model.getClientId());
        request.setTenantId(model.getTenantId());
        request.setResourceTenantId(model.getResourceTenantId());
        request.setOperatorId(model.getOperatorId());
        return request;
    }

}

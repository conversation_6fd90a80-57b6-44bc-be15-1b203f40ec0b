package com.timevale.contractmanager.biz.service.rest.relationcontract;

import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.core.model.dto.request.relationcontract.RelationContractChooseProcessRequest;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractChooseListVO;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractChooseProcessVO;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractListVO;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractProcessVO;
import com.timevale.contractmanager.core.service.relationcontract.RelationContractService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/2/15
 */
@Api(tags = "关联合同")
@ExternalService
@RestMapping(path = "/v1/relation-contract")
public class RelationContractRest {

    @Autowired
    private RelationContractService relationContractService;


    @FunctionPrivilegeCheck(function = FunctionCodeConstants.RELATION_CONTRACT)
    @ApiOperation(value = "可添加为关联合同列表", httpMethod = "GET")
    @RestMapping(path = "/choose-list", method = RequestMethod.GET)
    @MultilingualTranslateMethod
    public BaseResult<RelationContractChooseListVO> chooseList(
            @ApiParam(value = "fuzzyMatching", required = false) @RequestParam(required = false) String fuzzyMatching,
            @ApiParam(value = "title", required = false) @RequestParam(name = "title", required = false) String title,
            @ApiParam(value = "contractNo", required = false) @RequestParam(name = "contractNo", required = false) String contractNo,
            @ApiParam(value = "personName", required = false) @RequestParam(name = "personName", required = false) String personName,
            @ApiParam(value = "subjectName", required = false) @RequestParam(name = "subjectName", required = false) String subjectName,
            @ApiParam(value = "account", required = false) @RequestParam(name = "account", required = false) String account,
            @ApiParam(value = "pageSize", required = true) @RequestParam Integer pageSize,
            @ApiParam(value = "pageNum", required = true) @RequestParam Integer pageNum,
            @ApiParam(value = "processId", required = true) @RequestParam(required = false) String processId,
            @ApiParam(value = "processSearchStatus 0 全部 1 进行中 2 已完成", required = true) @RequestParam Integer processSearchStatus
    ) {

        RelationContractChooseProcessRequest request = new RelationContractChooseProcessRequest();

        request.setTenantOid(RequestContextExtUtils.getTenantId());
        request.setOperatorOid(RequestContextExtUtils.getOperatorId());
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        request.setCurrentProcessId(processId);
        request.setProcessSearchStatus(processSearchStatus);
        request.setFuzzyMatching(fuzzyMatching);
        request.setTitle(title);
        request.setPersonName(personName);
        request.setSubjectName(subjectName);
        request.setAccount(account);
        request.setContractNo(contractNo);

        Pair<List<RelationContractChooseProcessVO>, Long> resultPair = relationContractService.chooseProcessList(request);
        RelationContractChooseListVO response = new RelationContractChooseListVO();
        response.setTotal(resultPair.getRight());
        response.setList(resultPair.getLeft());
        return BaseResult.success(response);
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstants.RELATION_CONTRACT)
    @ApiOperation(value = "某个合同的关联合同列表", httpMethod = "GET")
    @RestMapping(path = "/list", method = RequestMethod.GET)
    @MultilingualTranslateMethod
    public BaseResult<RelationContractListVO> list(
            @ApiParam(value = "processId") @RequestParam(name = "processId") String processId,
            @ApiParam(value = "0 、经办合同 1、未归档合同 2、已归档合同 3、已归档合同具体分类下") @RequestParam(name = "entranceType") Integer entranceType,
            @ApiParam(value = "menuId") @RequestParam(name = "menuId", required = false) String menuId) {
        List<RelationContractProcessVO> list =
                relationContractService.list(
                        RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId(),
                        processId, entranceType, menuId);
        RelationContractListVO listVO = new RelationContractListVO();
        listVO.setList(list);
        return BaseResult.success(listVO);
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstants.RELATION_CONTRACT)
    @ApiOperation(value = "获取流程相关信息", httpMethod = "GET")
    @RestMapping(path = "/process-info", method = RequestMethod.GET)
    @MultilingualTranslateMethod
    public BaseResult<RelationContractListVO> list(
            @ApiParam(value = "processId") @RequestParam(name = "processIds") String processIds) {
        List<RelationContractProcessVO> list = relationContractService.processInfo(
                        RequestContextExtUtils.getTenantId(), Arrays.stream(processIds.split(",")).collect(Collectors.toList()));
        RelationContractListVO listVO = new RelationContractListVO();
        listVO.setList(list);
        return BaseResult.success(listVO);
    }



}

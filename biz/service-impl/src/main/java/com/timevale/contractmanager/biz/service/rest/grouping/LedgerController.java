package com.timevale.contractmanager.biz.service.rest.grouping;

import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.grouping.BookKeepingEnum;
import com.timevale.contractmanager.core.model.dto.request.grouping.file.BatchImageRequest;
import com.timevale.contractmanager.core.model.dto.request.process.QueryLedgerProcessDataRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessExportResponse;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.LedgerExportTaskResponse;
import com.timevale.contractmanager.core.model.dto.response.open.OpenProcessDocumentsResponse;
import com.timevale.contractmanager.core.model.dto.response.process.QueryLedgerProcessDataResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.grouping.LedgerService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;

import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_OPEN_APP_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * @author: huifeng
 * @since: 2021-05-11 16:27
 **/
@Api(tags = "新智能台账")
@ExternalService
@RestMapping(path = "/v2")
public class LedgerController {

    @Autowired
    private LedgerService ledgerService;

    @Autowired
    private UserCenterService userCenterService;


    @FunctionPrivilegeCheck(function = FunctionCodeConstant.INTELLIGENT_STANDBOOK, accountId = "")
    @ApiOperation(value = "换取台账提取图片地址", httpMethod = "GET")
    @RestMapping(path = "/ledger/tenants/tenantId/menus/{menuId}/image", method = RequestMethod.GET)
    public BaseResult listFormImages(
            @ApiParam(value = "分类id",required = true)@PathVariable String menuId,
            @ApiParam(value = "文件id",required = true) @RequestParam(required = true) String fileId,
            @ApiParam(value = "字段id", required = true) @RequestParam(required = true) String fieldId)
    {
        String tenantOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOid);
        UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorOid);

        return BaseResult.success(ledgerService.getLedgerImages(tenantAccount, operatorAccount, menuId, fileId,fieldId));
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstant.INTELLIGENT_STANDBOOK, accountId = "")
    @ApiOperation(value = "批量提取台账图片", httpMethod = "POST")
    @RestMapping(path = "/ledger/tenants/tenantId/column/image", method = RequestMethod.POST)
    public BaseResult  batchFormImages(
            @RequestBody BatchImageRequest request
            ){
        String tenantOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOid);
        UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorOid);

        return BaseResult.success(ledgerService.getLedgerColumnImages(tenantAccount, operatorAccount, request.getMenuId(),
                request.getFileInfoList(), request.getFieldId()));
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstant.INTELLIGENT_STANDBOOK, accountId = "")
    @ApiOperation(value = "创建台账数据导出任务", httpMethod = "POST")
    @RestMapping(path = "/ledger/tenants/tenantId/form/{formId}/exportTask", method = RequestMethod.POST)
    public BaseResult getDownloadTask(
            @RequestHeader(HEADER_TSIGN_OPEN_APP_ID) String appId,
            @ApiParam(value = "台账id",required = true) @PathVariable String formId) {
        String tenantOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String clientId = RequestContextExtUtils.getClientId();
        UserAccount tenantAccount = userCenterService.getUserAccountDetailByOid(tenantOid);
        UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorOid);
        LedgerExportTaskResponse response = ledgerService.addExportTask(formId, tenantAccount, operatorAccount,clientId, operatorOid, appId);

        ProcessExportResponse exportResponse = new ProcessExportResponse(response.getTaskId(), response.getTotalSize(), true, null);
        return BaseResult.success(exportResponse);
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstant.INTELLIGENT_STANDBOOK, accountId = "")
    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_BOOK_KEEPING,
            privilegeKey = BookKeepingEnum.PRIVILEGE_QUERY_BOOK_KEEPING)
    @ApiOperation(value = "从台账测查看合同文件", httpMethod = "GET")
    @RestMapping(path = "/ledger/tenants/tenantId/ledger/{ledgerId}/processes/{processId}/documents", method = RequestMethod.GET)
    public BaseResult<OpenProcessDocumentsResponse> getProcessDocuments(
            @ApiParam(value = "台账id", required = true) @PathVariable String ledgerId,
            @ApiParam(value = "合同流程id", required = true) @PathVariable String processId)
    {
        String tenantOid = RequestContextExtUtils.getTenantId();
        UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOid);
        return BaseResult.success(ledgerService.getLedgerProcessDocuments(tenantAccount, ledgerId, processId));
    }
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.INTELLIGENT_STANDBOOK)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/ledger/process/query", method = RequestMethod.GET)
    @ApiOperation(value = "查看单个台账的合同列表", httpMethod = "GET")
    public BaseResult<QueryLedgerProcessDataResponse> queryProcess(@URIQueryParam QueryLedgerProcessDataRequest request) {
        request.setAccountOid(RequestContextExtUtils.getOperatorId());
        request.setSubjectOid(RequestContextExtUtils.getTenantId());
        return BaseResult.success(ledgerService.queryStandingBookProcessData(request));
    }
}

package com.timevale.contractmanager.biz.service.rpc.impl.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.AiRuleMenuDO;
import com.timevale.contractmanager.common.dal.dao.grouping.AiRuleMenuDAO;
import com.timevale.contractmanager.common.service.api.grouping.RpcGroupTabulationService;
import com.timevale.contractmanager.common.service.bean.CustomGroupHeaderList;
import com.timevale.contractmanager.common.service.bean.MenuBaseInfo;
import com.timevale.contractmanager.common.service.bean.custom.CustomFieldBean;
import com.timevale.contractmanager.common.service.enums.grouping.MenuIdEnum;
import com.timevale.contractmanager.common.service.enums.grouping.RuleVersionEnum;
import com.timevale.contractmanager.common.service.integration.client.AuthRelationRpcServiceClient;
import com.timevale.contractmanager.common.service.model.custom.CustomFieldModel;
import com.timevale.contractmanager.common.service.model.grouping.UserCustomGroupHeaderModel;
import com.timevale.contractmanager.common.service.result.RpcOutput;
import com.timevale.contractmanager.common.service.result.custom.QueryCustomFieldResult;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.CustomListDTO;
import com.timevale.contractmanager.core.service.component.grouping.GroupingPropertyComponent;
import com.timevale.contractmanager.core.service.grouping.CustomListService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: contract-manager
 * @Description: 企业合同表格service
 * @date Date : 2025年02月07日 15:43
 */
@RestService
public class RpcGroupTabulationServiceImpl implements RpcGroupTabulationService {

    @Autowired
    CustomListService customListService;

    @Autowired
    GroupingPropertyComponent groupingPropertyComponent;

    @Autowired
    private AiRuleMenuDAO aiRuleMenuDAO;

    @Autowired
    AuthRelationRpcServiceClient authRelationRpcServiceClient;

    @Autowired
    UserCenterService userCenterService;

    @Override
    public RpcOutput<CustomGroupHeaderList> queryUserCustomHeaders(UserCustomGroupHeaderModel model) {
        //1,判断menuid属于何种分类
        String menuId = model.getMenuId();
        String ruleId = null;
        Integer ruleVersion = RuleVersionEnum.TWO.getVersion();
        if (MenuIdEnum.MENU_ID == MenuIdEnum.getMenuTypeBlankIsUnGrouping(menuId)) {
            AiRuleMenuDO sourceRuleDO = aiRuleMenuDAO.getRuleByMenuId(menuId);
            if(Objects.nonNull(sourceRuleDO)){
                ruleId = sourceRuleDO.getRuleId();
                ruleVersion = sourceRuleDO.getRuleVersion();
            }
        }

        //2,查询用户自定义表头
        CustomListDTO tempCustomListDTO = customListService.queryByRuleId(model.getTenantId(), model.getAccountId(),
                menuId, ruleId, ruleVersion);

        //3,根据场景移除不必要的属性
        CustomListDTO customListDTO = groupingPropertyComponent.removeCustomProperty(tempCustomListDTO,
                model.getTenantId(), model.getClientId(), checkWasParent(model));
        CustomGroupHeaderList customGroupHeaderList = JsonUtils.obj2pojo(customListDTO, CustomGroupHeaderList.class);
        return RpcOutput.with(customGroupHeaderList);
    }

    @Override
    public QueryCustomFieldResult queryCustomHeaderByFormId(CustomFieldModel customFieldModel) {
        QueryCustomFieldResult queryCustomFieldResult = new QueryCustomFieldResult();
        List<CustomFieldBean> displayList = new ArrayList<>();
        List<CustomFieldBean> hiddenList = new ArrayList<>();
        //获取自定义的表头字段
        CustomListDTO customListDTO = customListService.query(
                customFieldModel.getTenantId(),
                customFieldModel.getAccountOid(),
               MenuIdEnum.MENU_LEDGER.getMenuId(),
                customFieldModel.getClientId(),
                customFieldModel.getFormId(),
                customFieldModel.getOperatorId());
        //获取所有的字段
        List<CustomListDTO.CustomField> customListDTODisplayList = customListDTO.getDisplayList();
        List<CustomListDTO.CustomField> customListDTOHiddenList = customListDTO.getHiddenList();
        if (CollectionUtils.isNotEmpty(customListDTODisplayList)) {
            displayList = customListDTODisplayList.stream().map(customField->{
                CustomFieldBean customFieldBean = new CustomFieldBean();
                BeanUtils.copyProperties(customField, customFieldBean);
                return customFieldBean;
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(customListDTOHiddenList)) {
            hiddenList = customListDTOHiddenList.stream().map(customField->{
                CustomFieldBean customFieldBean = new CustomFieldBean();
                BeanUtils.copyProperties(customField, customFieldBean);
                return customFieldBean;
            }).collect(Collectors.toList());
        }
        queryCustomFieldResult.setDisplayList(displayList);
        queryCustomFieldResult.setHiddenList(hiddenList);
        return queryCustomFieldResult;
    }


    private boolean checkWasParent(UserCustomGroupHeaderModel model) {
        boolean wasParent = false;
        String tenantGid = StringUtils.defaultIfBlank(model.getTenantGid(), userCenterService.getAccountBeanByOid(model.getTenantId()).getGid());
        if(StringUtils.isNotBlank(tenantGid)){
            wasParent = authRelationRpcServiceClient.checkTenantWasParent(tenantGid);
        }
        return wasParent;
    }
}

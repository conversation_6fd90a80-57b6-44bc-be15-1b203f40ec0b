package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormSaveInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormUpdateInputDTO;
import com.timevale.contractanalysis.facade.api.enums.LedgerExtractTypeEnum;
import com.timevale.contractmanager.common.service.api.RpcLedgerInnerService;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.LedgerInnerFacadeServiceClient;
import com.timevale.contractmanager.common.service.model.ledger.AiExtractLimitAddModel;
import com.timevale.contractmanager.common.service.model.ledger.AiExtractLimitQueryModel;
import com.timevale.contractmanager.common.service.model.ledger.AiExtractLimitSyncModel;
import com.timevale.contractmanager.common.service.model.ledger.LedgerFormSaveModel;
import com.timevale.contractmanager.common.service.model.ledger.LedgerFormUpdateModel;
import com.timevale.contractmanager.common.service.result.ledger.AiExtractLimitResult;
import com.timevale.contractmanager.common.service.result.ledger.LedgerFormDetailResult;
import com.timevale.contractmanager.common.service.result.ledger.LedgerFormSaveResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.autobind.AutoBindLedgerRuleService;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.grouping.LedgerService;
import com.timevale.contractmanager.core.service.other.AiExtractLimitService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.rule.RuleConditionService;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.RestService;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * RpcLedgerInnerServiceImpl
 *
 * <AUTHOR>
 * @since 2023/8/23 4:14 下午
 */
@RestService
@Slf4j
public class RpcLedgerInnerServiceImpl implements RpcLedgerInnerService {

    @Autowired
    private LedgerInnerFacadeServiceClient ledgerInnerFacadeServiceClient;
    @Autowired
    private RuleConditionService ruleConditionService;
    @Autowired
    private MapperFactory mapperFactory;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private LedgerService ledgerService;
    @Autowired
    private AutoBindLedgerRuleService autoRuleLedgerRuleService;
    @Autowired
    private AiExtractLimitService aiExtractLimitService;
    @Autowired
    private TransactionTemplate transactionTemplate;

    private static final List<Integer> existRuleType = Arrays.asList(LedgerExtractTypeEnum.DIRECT_START.getType(), LedgerExtractTypeEnum.RECEIVE.getType(), LedgerExtractTypeEnum.OFFLINE.getType(), LedgerExtractTypeEnum.CATALOG.getType());

    @Override
    public LedgerFormSaveResult saveForm(LedgerFormSaveModel input) {

        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(input.getTenantId());

        // === ruleAutoBind ---- check exist ===
        autoRuleLedgerRuleService.check(input.getExtractType(), null, input.getConditionIds());
        // === ruleAutoBind ===

        LedgerFormSaveInputDTO formSaveInputDTO = mapperFactory.getMapperFacade().map(input, LedgerFormSaveInputDTO.class);
        String formId = ledgerInnerFacadeServiceClient.saveForm(formSaveInputDTO);
        boolean saveRule = existRuleType.contains(input.getExtractType()) && CollectionUtils.isNotEmpty(input.getConditions());
        if (saveRule) {
            ruleConditionService.saveRuleCondition(userAccountTenant, formId, 2, input.getConditions(), true);
        }

        // === ruleAutoBind
        autoRuleLedgerRuleService.doBind(input.getExtractType(), input.getConditionIds(), formId, input.getTenantId());
        // === ruleAutoBind

        return new LedgerFormSaveResult(formId);
    }

    @Override
    public void updateForm(LedgerFormUpdateModel input) {

        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(input.getTenantId());
        String formId = input.getFormId();

        // === ruleAutoBind
        LedgerFormDetailResult historyDetail = detail(formId, input.getTenantId());
        formTypeCannotModify(historyDetail.getExtractType(), input.getExtractType());
        autoRuleLedgerRuleService.check(input.getExtractType(), historyDetail, input.getConditionIds());
        // === ruleAutoBind

        LedgerFormUpdateInputDTO formUpdateInputDTO = mapperFactory.getMapperFacade().map(input, LedgerFormUpdateInputDTO.class);
        ledgerInnerFacadeServiceClient.updateForm(formUpdateInputDTO);

        //确认条件是否变更
        transactionTemplate.execute((action) -> {
            try {
                boolean ruleUpdate = ruleConditionService.updateRuleCondition(userAccountTenant, formId, 2, input.getConditions(), true);
                if (ruleUpdate) {
                    ledgerInnerFacadeServiceClient.invalidTrialProcessIds(formId);
                }

                // === ruleAutoBind
                autoRuleLedgerRuleService.unBind(formId, historyDetail);
                autoRuleLedgerRuleService.doBind(input.getExtractType(), input.getConditionIds(), formId, input.getTenantId());
                // === ruleAutoBind
                return true;
            } catch (Exception e) {
                log.error("save ledger failed", e);
                action.setRollbackOnly();
                return false;
            }
        });
    }

    private void formTypeCannotModify(Integer sourceType, Integer targetType) {
        if (sourceType == null || !sourceType.equals(targetType)) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.LEGER_TYPE_CANNOT_MODIFY);
        }
    }

    @Override
    public void delete(String formId, String tenantId, String accountId) {

        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantId);
        UserAccount account = new UserAccount();
        account.setAccountOid(accountId);

        LedgerFormDetailResult detail = detail(formId, tenantId);

        // === ruleAutoBind
        autoRuleLedgerRuleService.unBind(formId, detail);
        // === ruleAutoBind

        ledgerService.deleteLedger(tenant, account, formId);
        ruleConditionService.deleteRuleCondition(tenant, formId);

    }

    @Override
    public LedgerFormDetailResult detail(String formId, String tenantId) {
        return ledgerService.detail(formId, tenantId);
    }

    @Override
    public AiExtractLimitResult queryExtractLimit(AiExtractLimitQueryModel model) {
        return aiExtractLimitService.queryLimit(model);
    }

    @Override
    public void addExtractNumLimit(AiExtractLimitAddModel model) {
        aiExtractLimitService.addNum(model);
    }

    @Override
    public void updateExtractMarginLimit(AiExtractLimitSyncModel model) {
        aiExtractLimitService.updateMargin(model);
    }

    @Override
    public AiExtractLimitResult queryExtractMargin(AiExtractLimitQueryModel model) {
        AiExtractLimitResult aiExtractLimitResult = new AiExtractLimitResult();
        String tenantGid;
        if (StringUtils.isNotEmpty(model.getTenantGid())) {
            tenantGid = model.getTenantGid();
        } else {
            UserAccount userAccount = userCenterService.getUserAccountBaseByOid(model.getTenantOid());
            tenantGid = userAccount.getAccountGid();
        }
        String key = CacheUtil.getExtractLimitKey(tenantGid, model.getType());
        Integer cacheValue = TedisUtil.get(key);

        if (cacheValue == null) {
            aiExtractLimitResult = aiExtractLimitService.queryLimit(model);
            return aiExtractLimitResult;
        }
        aiExtractLimitResult.setMargin(cacheValue);
        return aiExtractLimitResult;
    }

    @Override
    public void deductMargin(AiExtractLimitSyncModel model) {
        String limitKey = CacheUtil.getExtractLimitKey(model.getTenantGid(), model.getType());
        TedisUtil.string().increment(limitKey, -1);
        TedisUtil.expire(limitKey, 1, TimeUnit.HOURS);
    }
}

package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.api.RpcPreferenceService;
import com.timevale.contractmanager.common.service.bean.PreferenceModel;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.model.QueryBizPreferenceModel;
import com.timevale.contractmanager.common.service.model.QueryProcessPreferenceModel;
import com.timevale.contractmanager.common.service.result.QueryProcessPreferenceResult;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.process.handler.preferences.PreferenceAfterHandlerManager;
import com.timevale.contractmanager.core.service.process.handler.preferences.PreferenceQueryAfterHandleRequest;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.RestService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2021-05-18 20:45
 **/
@RestService
@Slf4j
public class RpcPreferenceServiceImpl implements RpcPreferenceService{

    @Autowired
    PreferencesService preferencesService;

    @Autowired
    PreferenceAfterHandlerManager preferenceAfterHandlerManager;

    @Override
    public QueryProcessPreferenceResult query(QueryProcessPreferenceModel preferenceModel) {
        if(CollectionUtils.isEmpty(preferenceModel.getPreferenceKeys())){
            preferenceModel.setPreferenceKeys(ProcessPreferenceEnum.preferenceAll());
        }
        QueryProcessPreferenceResult result = preferencesService.listByGidsAndTypes(preferenceModel.getOrgGids(), preferenceModel.getPreferenceKeys());
        if (result == null || CollectionUtils.isEmpty(result.getProcessPreference())) {
            return new QueryProcessPreferenceResult();
        }

        // 后置处理
        for(PreferenceModel preference : result.getProcessPreference()){
            PreferenceQueryAfterHandleRequest request = new PreferenceQueryAfterHandleRequest();
            request.setOrgGid(preference.getOrgGid());
            request.setHandlerResult(preferenceModel.isHandlerResult());
            preferenceAfterHandlerManager.afterProcess(
                    request,
                    preference.getPreferences());
        }
        return result;
    }

    @Override
    public PreferenceModel queryBizPreference(QueryBizPreferenceModel model) {
        return preferencesService.queryBizPreference(model);
    }
}

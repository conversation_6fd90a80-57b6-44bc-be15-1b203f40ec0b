package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.core.model.dto.request.datasource.GetDataSourceDataRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessesStartGetProcessRequest;
import com.timevale.contractmanager.core.model.dto.response.datasource.DataSourceDataAndRulesDTO;
import com.timevale.contractmanager.core.model.dto.response.datasource.GetDataSourceDataResponse;
import com.timevale.contractmanager.core.model.dto.response.process.GetProcessByDataResponse;
import com.timevale.contractmanager.core.service.process.datasource.ProcessDataSourceService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2025-05-19
 */
@Api(tags = "合同流程数据源相关接口")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class ProcessDataSourceRest {

    @Autowired private ProcessDataSourceService processDataSourceService;

    @RestMapping(path = "/processes/data-source-data", method = RequestMethod.POST)
    @ApiOperation(value = "获取数据源具体信息", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<GetDataSourceDataResponse> getDataSourceData(
            @RequestBody GetDataSourceDataRequest request) {
        DataSourceDataAndRulesDTO dataAndRulesDTO =
                processDataSourceService.getProcessDataSource(RequestContextExtUtils.getTenantId(),RequestContextExtUtils.getOperatorId(),request);
        return dataAndRulesDTO == null
                ? BaseResult.success()
                : BaseResult.success(new GetDataSourceDataResponse(dataAndRulesDTO));
    }


    /**
     * 根据dataId查询当前发起流程id
     *
     * @param request 请求参数
     * @return BaseResult<GetProcessByDataResponse>
     */
    @ApiOperation(value = "根据dataId查询当前发起流程id", httpMethod = "POST")
    @RestMapping(path = "/processes/data-process", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<GetProcessByDataResponse> startData(@RequestBody ProcessesStartGetProcessRequest request) {
        String accountId = RequestContextExtUtils.getOperatorId();
        String tenantId = RequestContextExtUtils.getTenantId();
        if (StringUtils.isBlank(request.getPrimaryOid())) {
            request.setPrimaryOid(tenantId);
        }
        GetProcessByDataResponse response = processDataSourceService.getProcessStartDataInfoByDataId(request, accountId);
        return BaseResult.success(response);
    }
}

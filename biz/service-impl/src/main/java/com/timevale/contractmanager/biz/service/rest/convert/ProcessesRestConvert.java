package com.timevale.contractmanager.biz.service.rest.convert;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.contractmanager.core.model.dto.process.ProcessParticipantAuthInfo;
import com.timevale.contractmanager.core.model.dto.process.signsetup.base.BaseSetUpDisplayRuleDTO;
import com.timevale.contractmanager.core.model.dto.process.signsetup.base.SignSetUpDisplayUnitDTO;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessesCheckParticipantSetUpRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessesCheckSignSetUpRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessesSignSetUpParticipantRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessesSignSetUpRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessesStartSetUpRequest;
import com.timevale.contractmanager.core.model.dto.response.process.ParticipantSetUpDisplayRuleVO;
import com.timevale.contractmanager.core.model.dto.response.process.ProcessParticipantAuthWayResponse;
import com.timevale.contractmanager.core.service.process.participantsetup.SignSetUpParticipantBO;
import com.timevale.contractmanager.core.service.process.participantsetup.ProcessSignSetUpBizRequest;
import com.timevale.contractmanager.core.service.process.rule.model.ProcessStartSetUpBizRequest;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/19 10:30
 */
public class ProcessesRestConvert {

    public static ProcessSignSetUpBizRequest convert(ProcessesSignSetUpRequest processesSignSetUpRequest) {
        if (null == processesSignSetUpRequest) {
            return null;
        }
        ProcessSignSetUpBizRequest processSignSetUpBizRequest = new ProcessSignSetUpBizRequest();
        processSignSetUpBizRequest.setScene(processesSignSetUpRequest.getScene());
        processSignSetUpBizRequest.setBusinessType(processesSignSetUpRequest.getBusinessType());
        processSignSetUpBizRequest.setDedicatedCloudId(processesSignSetUpRequest.getDedicatedCloudId());
        processSignSetUpBizRequest.setSignMode(processesSignSetUpRequest.getSignMode());
        processSignSetUpBizRequest.setFlowTemplateId(processesSignSetUpRequest.getFlowTemplateId());
        processSignSetUpBizRequest.setManyToMany(processesSignSetUpRequest.getManyToMany());
        processSignSetUpBizRequest.setParticipants(convertList(processesSignSetUpRequest.getParticipants()));
        processSignSetUpBizRequest.setTags(processesSignSetUpRequest.getTags());
        processSignSetUpBizRequest.setSetUpTypes(processesSignSetUpRequest.getSetUpTypes());
        processSignSetUpBizRequest.setFdaSignatureConfig(processesSignSetUpRequest.getFdaSignatureConfig());
        return processSignSetUpBizRequest;
    }

    public static ProcessSignSetUpBizRequest convert(ProcessesCheckSignSetUpRequest processesSignSetUpRequest) {
        if (null == processesSignSetUpRequest) {
            return null;
        }
        ProcessSignSetUpBizRequest processSignSetUpBizRequest = new ProcessSignSetUpBizRequest();
        processSignSetUpBizRequest.setScene(processesSignSetUpRequest.getScene());
        processSignSetUpBizRequest.setBusinessType(processesSignSetUpRequest.getBusinessType());
        processSignSetUpBizRequest.setSignMode(processesSignSetUpRequest.getSignMode());
        processSignSetUpBizRequest.setFlowTemplateId(processesSignSetUpRequest.getFlowTemplateId());
        processSignSetUpBizRequest.setManyToMany(processesSignSetUpRequest.getManyToMany());
        processSignSetUpBizRequest.setParticipants(convertListCheck(processesSignSetUpRequest.getParticipants()));
        processSignSetUpBizRequest.setTags(processesSignSetUpRequest.getTags());
        processSignSetUpBizRequest.setSetUpTypes(processesSignSetUpRequest.getSetUpTypes());
        processSignSetUpBizRequest.setFdaSignatureConfig(processesSignSetUpRequest.getFdaSignatureConfig());
        processSignSetUpBizRequest.setSignMode(processesSignSetUpRequest.getSignMode());
        return processSignSetUpBizRequest;
    }


    public static List<SignSetUpParticipantBO> convertListCheck(List<ProcessesCheckParticipantSetUpRequest> list) {

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(request -> {
            SignSetUpParticipantBO signSetUpParticipantBO = new SignSetUpParticipantBO();
            signSetUpParticipantBO.setInstances(request.getInstances());
            signSetUpParticipantBO.setParticipantSubjectType(request.getParticipantSubjectType());
            signSetUpParticipantBO.setParticipantMode(request.getParticipantMode());
            signSetUpParticipantBO.setRoleSet(request.getRoleSet());
            signSetUpParticipantBO.setSharable(Boolean.TRUE.equals(request.getSharable()));
            signSetUpParticipantBO.setAuthWay(request.getAuthWay());
            signSetUpParticipantBO.setSignRequirements(request.getSignRequirements());
            signSetUpParticipantBO.setSealType(request.getSealType());
            signSetUpParticipantBO.setWillTypes(request.getWillTypes());
            signSetUpParticipantBO.setNoticeTypes(request.getNoticeTypes());
            signSetUpParticipantBO.setForceReadEnd(request.getForceReadEnd());
            signSetUpParticipantBO.setForceReadTime(request.getForceReadTime());
            signSetUpParticipantBO.setAttachmentConfigs(request.getAttachmentConfigs());
            signSetUpParticipantBO.setParticipantId(request.getParticipantId());
            signSetUpParticipantBO.setParticipantLabel(request.getParticipantLabel());
            signSetUpParticipantBO.setAccessToken(request.getAccessToken());
            signSetUpParticipantBO.setAssignedSubjectId(request.getAssignedSubjectId());
            signSetUpParticipantBO.setAssignedSubjectName(request.getAssignedSubjectName());
            signSetUpParticipantBO.setParticipantNeedCheck(request.isParticipantNeedCheck());
            return signSetUpParticipantBO;
        }).collect(Collectors.toList());

    }

    public static List<SignSetUpParticipantBO> convertList(List<ProcessesSignSetUpParticipantRequest> list) {

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(ProcessesRestConvert::convert).collect(Collectors.toList());

    }

    public static SignSetUpParticipantBO convert(ProcessesSignSetUpParticipantRequest request) {
        if (null == request) {
            return null;
        }
        SignSetUpParticipantBO signSetUpParticipantBO = new SignSetUpParticipantBO();
        signSetUpParticipantBO.setInstances(request.getInstances());
        signSetUpParticipantBO.setParticipantSubjectType(request.getParticipantSubjectType());
        signSetUpParticipantBO.setParticipantMode(request.getParticipantMode());
        signSetUpParticipantBO.setRoleSet(request.getRoleSet());
        signSetUpParticipantBO.setSharable(Boolean.TRUE.equals(request.getSharable()));
        signSetUpParticipantBO.setAuthWay(request.getAuthWay());
        signSetUpParticipantBO.setSignRequirements(request.getSignRequirements());
        signSetUpParticipantBO.setSealType(request.getSealType());
        signSetUpParticipantBO.setWillTypes(request.getWillTypes());
        signSetUpParticipantBO.setNoticeTypes(request.getNoticeTypes());
        signSetUpParticipantBO.setForceReadEnd(request.getForceReadEnd());
        signSetUpParticipantBO.setForceReadTime(request.getForceReadTime());
        signSetUpParticipantBO.setAttachmentConfigs(request.getAttachmentConfigs());
        signSetUpParticipantBO.setParticipantId(request.getParticipantId());
        signSetUpParticipantBO.setParticipantLabel(request.getParticipantLabel());
        return signSetUpParticipantBO;
    }


    public static List<ParticipantSetUpDisplayRuleVO> displayRuleConvert(List<BaseSetUpDisplayRuleDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(ProcessesRestConvert::displayRuleConvert).collect(Collectors.toList());

    }


    public static ParticipantSetUpDisplayRuleVO displayRuleConvert(BaseSetUpDisplayRuleDTO baseSetUpDisplayRuleDTO) {
        if (null == baseSetUpDisplayRuleDTO) {
            return null;
        }
        ParticipantSetUpDisplayRuleVO participantSetUpDisplayRuleVO = new ParticipantSetUpDisplayRuleVO();
        participantSetUpDisplayRuleVO.setKey(baseSetUpDisplayRuleDTO.getKey());
        participantSetUpDisplayRuleVO.setName(baseSetUpDisplayRuleDTO.getName());
        participantSetUpDisplayRuleVO.setDisabled(baseSetUpDisplayRuleDTO.getDisabled());
        participantSetUpDisplayRuleVO.setOpen(baseSetUpDisplayRuleDTO.getOpen());
        participantSetUpDisplayRuleVO.setShow(baseSetUpDisplayRuleDTO.getShow());
        participantSetUpDisplayRuleVO.setPopover(baseSetUpDisplayRuleDTO.getPopover());
        participantSetUpDisplayRuleVO.setRules(baseSetUpDisplayRuleDTO.getRules());
        participantSetUpDisplayRuleVO.setTrialStatus(baseSetUpDisplayRuleDTO.getTrialStatus());
        participantSetUpDisplayRuleVO.setSupportVipFunction(baseSetUpDisplayRuleDTO.getSupportVipFunction());
        participantSetUpDisplayRuleVO.setOptionals(displayUnitConvert(baseSetUpDisplayRuleDTO.getOptionals()));
        participantSetUpDisplayRuleVO.setTooltip(baseSetUpDisplayRuleDTO.getTooltip());
        return participantSetUpDisplayRuleVO;
    }


    public static ParticipantSetUpDisplayRuleVO.ParticipantSetUpDisplayUnitVO displayUnitConvert(SignSetUpDisplayUnitDTO signSetUpDisplayUnitDTO) {
        if (null == signSetUpDisplayUnitDTO) {
            return null;
        }
        ParticipantSetUpDisplayRuleVO.ParticipantSetUpDisplayUnitVO participantSetUpDisplayUnitVO = new ParticipantSetUpDisplayRuleVO.ParticipantSetUpDisplayUnitVO();
        participantSetUpDisplayUnitVO.setType(signSetUpDisplayUnitDTO.getType());
        participantSetUpDisplayUnitVO.setName(signSetUpDisplayUnitDTO.getName());
        participantSetUpDisplayUnitVO.setShow(signSetUpDisplayUnitDTO.getShow());
        participantSetUpDisplayUnitVO.setDisabled(signSetUpDisplayUnitDTO.getDisabled());
        participantSetUpDisplayUnitVO.setCanChoose(signSetUpDisplayUnitDTO.getCanChoose());
        participantSetUpDisplayUnitVO.setTooltip(signSetUpDisplayUnitDTO.getTooltip());
        participantSetUpDisplayUnitVO.setPopover(signSetUpDisplayUnitDTO.getPopover());
        return participantSetUpDisplayUnitVO;
    }


    public static List<ParticipantSetUpDisplayRuleVO.ParticipantSetUpDisplayUnitVO> displayUnitConvert(List<SignSetUpDisplayUnitDTO> signSetUpDisplayUnitDTOList) {
        if (CollectionUtils.isEmpty(signSetUpDisplayUnitDTOList)) {
            return new ArrayList();
        }
        List<ParticipantSetUpDisplayRuleVO.ParticipantSetUpDisplayUnitVO> participantSetUpDisplayUnitVOList = new ArrayList();
        for (SignSetUpDisplayUnitDTO signSetUpDisplayUnitDTO : signSetUpDisplayUnitDTOList) {
            participantSetUpDisplayUnitVOList.add(displayUnitConvert(signSetUpDisplayUnitDTO));
        }
        return participantSetUpDisplayUnitVOList;
    }


    public static ProcessParticipantAuthWayResponse convertProcessParticipantAuthWayResponse(List<ProcessParticipantAuthInfo> authInfos) {
        Map<Integer, ProcessParticipantAuthWayResponse.ProcessParticipantInfo> orSignMap = Maps.newHashMap();
        List<ProcessParticipantAuthWayResponse.ProcessParticipantInfo> participantInfos = Lists.newArrayList();
        authInfos.sort(Comparator.comparingInt(ProcessParticipantAuthInfo::getOrder));
        for (ProcessParticipantAuthInfo authInfo : authInfos) {
            ProcessParticipantAuthWayResponse.ProcessAuthInfo authInfoVO = covertProcessAuthInfo(authInfo);
            ProcessParticipantAuthWayResponse.ProcessParticipantInfo participantInfo;
            if (ParticipantModeEnum.OR_SIGN.getMode().equals(authInfo.getParticipantMode())) {
                participantInfo = orSignMap.computeIfAbsent(authInfo.getOrder(), k -> genPartInfo(authInfo, participantInfos));
            } else {
                participantInfo = genPartInfo(authInfo, participantInfos);
            }
            participantInfo.getParticipant().add(authInfoVO);
        }

        ProcessParticipantAuthWayResponse response = new ProcessParticipantAuthWayResponse();
        response.setList(participantInfos);
        return response;
    }

    private static ProcessParticipantAuthWayResponse.ProcessParticipantInfo genPartInfo(ProcessParticipantAuthInfo authInfo, List<ProcessParticipantAuthWayResponse.ProcessParticipantInfo> participantInfos) {
        ProcessParticipantAuthWayResponse.ProcessParticipantInfo partInfo = new ProcessParticipantAuthWayResponse.ProcessParticipantInfo();
        partInfo.setLabel(authInfo.getLabel());
        partInfo.setParticipantMode(authInfo.getParticipantMode());
        partInfo.setParticipant(Lists.newArrayList());
        participantInfos.add(partInfo);
        return partInfo;
    }

    private static ProcessParticipantAuthWayResponse.ProcessAuthInfo covertProcessAuthInfo(ProcessParticipantAuthInfo authInfo) {
        ProcessParticipantAuthWayResponse.ProcessAuthInfo processAuthInfo = new ProcessParticipantAuthWayResponse.ProcessAuthInfo();
        processAuthInfo.setSubjectOid(authInfo.getSubjectOid());
        processAuthInfo.setSubjectName(authInfo.getSubjectName());
        processAuthInfo.setPersonOid(authInfo.getPersonOid());
        processAuthInfo.setPersonName(authInfo.getPersonName());
        processAuthInfo.setAccessToken(authInfo.getAccessToken());
        processAuthInfo.setAuthWay(authInfo.getAuthWay());
        processAuthInfo.setContract(authInfo.getContract());
        processAuthInfo.setOrgan(authInfo.getOrgan());
        return processAuthInfo;
    }


    public static ProcessStartSetUpBizRequest convert(ProcessesStartSetUpRequest processesStartSetUpRequest) {
        if (null == processesStartSetUpRequest) {
            return null;
        }
        ProcessStartSetUpBizRequest processStartSetUpBizRequest = new ProcessStartSetUpBizRequest();
        processStartSetUpBizRequest.setDedicatedCloudId(processesStartSetUpRequest.getDedicatedCloudId());
        processStartSetUpBizRequest.setSignMode(processesStartSetUpRequest.getSignMode());
        processStartSetUpBizRequest.setScene(processesStartSetUpRequest.getScene());
        processStartSetUpBizRequest.setStartType(processesStartSetUpRequest.getStartType());
        processStartSetUpBizRequest.setBusinessType(processesStartSetUpRequest.getBusinessType());
        processStartSetUpBizRequest.setManyToMany(processesStartSetUpRequest.getManyToMany());
        processStartSetUpBizRequest.setBatchStart(processesStartSetUpRequest.getBatchStart());
        processStartSetUpBizRequest.setFlowTemplateId(processesStartSetUpRequest.getFlowTemplateId());
        processStartSetUpBizRequest.setDynamicFlowTemplate(processesStartSetUpRequest.getDynamicFlowTemplate());
        processStartSetUpBizRequest.setDraftFlowTemplateId(processesStartSetUpRequest.getDraftFlowTemplateId());
        return processStartSetUpBizRequest;
    }

}

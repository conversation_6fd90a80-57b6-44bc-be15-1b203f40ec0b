package com.timevale.contractmanager.biz.service.rest.preferences;

import com.timevale.contractmanager.biz.service.rest.BaseRest;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.dto.request.QueryPreferenceRequest;
import com.timevale.contractmanager.core.model.dto.request.SavePreferenceRequest;
import com.timevale.contractmanager.core.model.dto.response.PreferenceResponse;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.util.CharsetUtil;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2021-05-17 15:49
 **/
@Api(tags = "合同偏好设置")
@ExternalService
@RestMapping(path = "/v2/processes")
public class PreferencesRest extends BaseRest {

    @Autowired
    PreferencesService preferencesService;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = "#request.orgId")
    @RestMapping(path = "/savePreferences", method = RequestMethod.POST)
    @ApiOperation(value = "保存用户合同偏好设置", httpMethod = "POST")
    public BaseResult savePreference(@RequestBody SavePreferenceRequest request){
        //调用service保存偏好设置
        preferencesService.saveOrUpdatePreference(request);

        return BaseResult.success();
    }

    @RestMapping(path = "/queryPreferences", method = RequestMethod.POST)
    @ApiOperation(value = "获取合同偏好设置", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = "#request.orgId")
    public BaseResult<PreferenceResponse> queryPreferences(@RequestBody QueryPreferenceRequest request){
        request.setClientId(RequestContextExtUtils.getClientId());
        request.setAppName(RequestContextExtUtils.getAppName());
        return BaseResult.success(preferencesService.queryByCondition(request));
    }

    @RestMapping(path = "/query-participant-preferences", method = RequestMethod.POST)
    @ApiOperation(value = "获取相对方企业合同偏好设置", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<PreferenceResponse> queryParticipantPreferences(@RequestBody QueryPreferenceRequest request){
        request.setClientId(RequestContextExtUtils.getClientId());
        request.setAppName(RequestContextExtUtils.getAppName());
        if (CollectionUtils.isEmpty(request.getPreferenceKeys())) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "偏好项不能为空");
        }
        return BaseResult.success(preferencesService.queryByCondition(request));
    }
}

package com.timevale.contractmanager.biz.service.rest;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.enums.ProcessTransferTypeEnum;
import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.bo.transfer.RetireTransferBO;
import com.timevale.contractmanager.core.model.bo.transfer.SealTransferNodeCountBO;
import com.timevale.contractmanager.core.model.dto.request.BatchUserProcessCountRequest;
import com.timevale.contractmanager.core.model.dto.request.transfer.*;
import com.timevale.contractmanager.core.model.dto.response.BatchUserProcessCountResponse;
import com.timevale.contractmanager.core.model.dto.response.SearchUserTransferProcessListResponse;
import com.timevale.contractmanager.core.model.dto.response.TransferCountResponse;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.response.transfer.GetCachedTransferredCountResponse;
import com.timevale.contractmanager.core.model.dto.response.transfer.TransferResultResponse;
import com.timevale.contractmanager.core.model.dto.response.transfer.TransferSealNodeCountResponse;
import com.timevale.contractmanager.core.model.dto.transfer.CachedTransferredCountInfoDTO;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.TransferService;
import com.timevale.contractmanager.core.service.transfer.factory.TransferBizServiceFactory;
import com.timevale.contractmanager.core.service.transfer.impl.TransferSealApproveBizServiceImpl;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum.PRIVILEGE_RESOURCE_MEMBER;
import static com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum.PRIVILEGE_DELETE;
import static com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum.PRIVILEGE_UPDATE;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.TRANSFER_ACCOUNT_NOT_MEMBER;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * 转交Rest
 *
 * @author: jinhuan
 * @since: 2019-11-20 14:54
 */
@Api(tags = "转交相关接口")
@ExternalService
@RestMapping(path = "/v2")
public class TransferRest {

    @Autowired private TransferService transferService;
    @Autowired private UserCenterService userCenterService;
    @Resource private TransferBizServiceFactory transferBizServiceFactory;

    /**
     * 判断用户在当前企业下是否有流程
     *
     * @param accountId 用户oid
     * @return 用户在当前企业下是否有流程
     */
    @RestMapping(path = "/transfer/check", method = RequestMethod.GET)
    @ApiOperation(value = "判断用户在当前企业下是否有流程", httpMethod = "GET")
    public BaseResult<TransferCountResponse> checkUserProcess(
            @ApiParam(value = "当前账号oid", required = true) @RequestParam String accountId,
            @ApiParam(value = "查询人账号oid", required = true) @RequestParam String originalAccountId) {
        TransferCountResponse transferCountResponse = new TransferCountResponse();
        transferCountResponse.setCount(
                transferService.countUserProcess(
                        RequestContextExtUtils.getTenantId(), originalAccountId));
        return BaseResult.success(transferCountResponse);
    }

    /**
     * manual转交
     *
     * @param request 转交入参
     * @return 转交
     */
    @RestMapping(path = "/processes/transfer/manual", method = RequestMethod.POST)
    @ApiOperation(value = "手动转交", httpMethod = "POST")
    public BaseResult<Boolean> manualTransfer(
            @RequestBody ManualTransferRequest request,
            @ApiParam("空间id") @RequestHeader(name = "X-Tsign-Open-Tenant-Id", required = false) String tenantId) {
        // 优先获取参数指定的主体id， 如果为空， 默认取空间id
        if (StringUtils.isNotBlank(request.getSubjectId())) {
            tenantId = request.getSubjectId();
        }
        return BaseResult.success(
                transferService.manualTransfer(tenantId, request,false));
    }

    /**
     * 待办任务转交
     *
     * @param request 转交入参
     * @return 转交
     */
    @RestMapping(path = "/processes/transfer/todo", method = RequestMethod.POST)
    @ApiOperation(value = "待办任务转交", httpMethod = "POST")
    public BaseResult<Boolean> todoTransfer(
            @RequestBody TodoTransferRequest request,
            @ApiParam("空间id") @RequestHeader(name = "X-Tsign-Open-Tenant-Id", required = false) String tenantId) {

        // 优先获取参数指定的主体id， 如果为空， 默认取空间id
        if (StringUtils.isNotBlank(request.getSubjectId())) {
            tenantId = request.getSubjectId();
        }

        ManualTransferRequest transferRequest = new ManualTransferRequest();
        transferRequest.setOriginalAccountId(request.getOriginalAccountId());
        transferRequest.setTransferToAccount(request.getTransferToAccount());
        transferRequest.setTransferToAccountName(request.getTransferToAccountName());
        transferRequest.setTransferToAccountId(request.getTransferToAccountId());
        transferRequest.setProcessIds(request.getProcessIds());
        transferRequest.setTransferTypes(
                Lists.newArrayList(ProcessTransferTypeEnum.EXECUTING_TASK_TRANSFER.getType()));
        transferRequest.setAccountId(request.getAccountId());

        return BaseResult.success(transferService.manualTransfer(tenantId, transferRequest,true));
    }

    /**
     * 合同转交
     *
     * @param request 转交入参
     * @return 转交
     */

    @RestMapping(path = "/processes/transfer", method = RequestMethod.POST)
    @ApiOperation(value = "合同转交", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<TransferResultResponse> batchProcessTransfer(@RequestBody ProcessTransferRequest request) {
        RetireTransferBO.RetireTransferBOBuilder builder =
                RetireTransferBO.builder()
                        .operatorOid(RequestContextExtUtils.getOperatorId())
                        .taskId(UUIDUtil.genUUID())
                        .tenantId(RequestContextExtUtils.getTenantId())
                        .transferAccountOidList(request.getTransferUserList())
                        .transferToAccountOid(request.getTransferToAccountId())
                        .transferProcessListInfo(
                                RetireTransferBO.convert2TransferProcessListInfos(
                                        request.getTransferProcessInfo()))
                        .mixTransferApprovalInfo(
                                RetireTransferBO.convert2MixTransferApprovalInfo(
                                        request.getMixTransferApprovalInfo()))
                        .contractApprovalTransferReason(request.getContractApprovalTransferReason())
                        .sealApprovalTransferReason(request.getSealApprovalTransferReason())
                        .transferScene(request.getTransferScene());
        TransferResultResponse response = transferService.batchProcessTransfer(builder.build());
        return BaseResult.success(response);
    }

    /**
     * 获取被转交人接收到的转交合同数量
     *
     * @param transferToAccountId
     * @return
     */
    @RestMapping(path = "/processes/transfer/my-cached-count", method = RequestMethod.GET)
    @ApiOperation(value = "获取缓存中被转交人接收到的转交合同数量", httpMethod = "GET")
    public BaseResult<GetCachedTransferredCountResponse> getCachedTransferredCount(
            @ApiParam("被转交人oid") @RequestParam @NotBlank(message = "被转交人oid不能为空")
                    String transferToAccountId,
            @ApiParam("查询的类型，ALL-查询所有企业，ONLY-查询一个企业，默认ONLY")
                    @RequestParam(required = false, defaultValue = "ONLY")
                    String type) {
        List<CachedTransferredCountInfoDTO> countInfoDTOS =
                transferService.getTransferCacheData(
                        RequestContextExtUtils.getTenantId(), transferToAccountId, type);
        return BaseResult.success(new GetCachedTransferredCountResponse(countInfoDTOS));
    }

    /**
     * 清除被转交人接收到的转交合同数量
     *
     * @param transferToAccountId
     * @return
     */
    @RestMapping(path = "/processes/transfer/del-my-cached-count", method = RequestMethod.DELETE)
    @ApiOperation(value = "清除缓存被转交人收到的转交合同数量", httpMethod = "DELETE")
    public BaseResult<Boolean> clearCachedTransferredCount(
            @ApiParam("被转交人oid") @RequestParam @NotBlank(message = "被转交人oid不能为空")
                    String transferToAccountId,
            @ApiParam("查询的类型，ALL-查询所有企业，ONLY-查询一个企业，默认ONLY")
                    @RequestParam(required = false, defaultValue = "ONLY")
                    String type) {
        transferService.clearTransferCacheData(
                RequestContextExtUtils.getTenantId(), transferToAccountId, type);
        return BaseResult.success(true);
    }

    @RestMapping(path = "/processes/transfer/count", method = RequestMethod.POST)
    @ApiOperation(value = "批量查询用户在当前企业下的流程数量", httpMethod = "POST")
    public BaseResult<BatchUserProcessCountResponse> batchQueryUserProcess(
            @RequestBody BatchUserProcessCountRequest request) {
        BatchUserProcessCountResponse response =
                transferService.countTransferUserProcess(
                        RequestContextExtUtils.getTenantId(), request.getQueryUserOids());
        return BaseResult.success(response);
    }

    @RestMapping(path = "/processes/transfer/list", method = RequestMethod.GET)
    @ApiOperation(value = "用户合同信息列表", httpMethod = "GET")
    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PRIVILEGE_RESOURCE_MEMBER,
            privilegeKey = {PRIVILEGE_UPDATE, PRIVILEGE_DELETE})
    public BaseResult<SearchUserTransferProcessListResponse> searchUserProcessList(
            @RequestParam @ApiParam("查询用户的oid") @NotBlank(message = "查询用户的oid不能为空")
                    String queryAccountOid,
            @RequestParam(required = false) @Max(value = 100, message = "最大支持100页")
                    Integer pageSize,
            @RequestParam(required = false) @Max(value = 10000, message = "每页最多支持10000条")
                    Integer pageNum,
            @RequestParam(required = false) @ApiParam("合同状态,多个状态以逗号分隔，例如2,3,4") String status,
            @RequestParam(required = false) @ApiParam("合同搜索关键字,如合同名称、参与人姓名和账号") String keyword) {
        Boolean memberOrCreator = userCenterService.checkMemberOrCreator(RequestContextExtUtils.getTenantId(), queryAccountOid);
        if (!Boolean.TRUE.equals(memberOrCreator)) {
            throw new BizContractManagerException(TRANSFER_ACCOUNT_NOT_MEMBER);
        }
        SearchUserTransferProcessListResponse response =
                transferService.transferUserProcessSearch(
                        RequestContextExtUtils.getTenantId(),
                        queryAccountOid,
                        keyword,
                        status,
                        pageSize,
                        pageNum);
        return BaseResult.success(response);
    }

    @RestMapping(path = "/processes/transfer/seal-node-count", method = RequestMethod.POST)
    @ApiOperation(value = "用印审批节点数据", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<TransferSealNodeCountResponse> sealNodeCount(
            @RequestBody SealApproveTransferNodeRequest request) {
        if (StringUtils.isBlank(request.getTransferUser())
                && CollectionUtils.isEmpty(request.getSealApproveIds())) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "转交人或用印审批id不能同时为空");
        }
        SealTransferNodeCountBO transferNodeCountBO = new SealTransferNodeCountBO();
        transferNodeCountBO.setTransferUser(request.getTransferUser());
        transferNodeCountBO.setSealApproveIds(request.getSealApproveIds());
        transferNodeCountBO.setTenantId(RequestContextExtUtils.getTenantId());
        return BaseResult.success(
                ((TransferSealApproveBizServiceImpl)
                                transferBizServiceFactory.getService(
                                        TransferSceneEnum.SEAL_APPROVE.getCode()))
                        .sealNodeCount(transferNodeCountBO));
    }

    @RestMapping(path = "/processes/transfer/user-list", method = RequestMethod.POST)
    @ApiOperation(value = "查询可转交人", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<OrgDeptListResponse> transferUserList(
            @RequestBody TransferUserListRequest request) {
        if (TransferSceneEnum.SEAL_APPROVE.getCode() == request.getTransferScene()
                && null == request.getTransferProcessInfo()
                && CollectionUtils.isEmpty(request.getTransferUserList())) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "转交人或用印审批id不能同时为空");
        }
        request.setTenantId(RequestContextExtUtils.getTenantId());
        return BaseResult.success(transferService.transferUserList(request));
    }
}

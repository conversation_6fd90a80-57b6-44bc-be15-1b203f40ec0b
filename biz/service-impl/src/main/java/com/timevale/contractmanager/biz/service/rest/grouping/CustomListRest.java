package com.timevale.contractmanager.biz.service.rest.grouping;

import com.timevale.contractmanager.core.model.dto.request.grouping.standingbook.SetCustomeFieldRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.standingbook.SetFieldRequest;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.CustomListDTO;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.FieldDataSourceDTO;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.FieldSysDataSourceDTO;
import com.timevale.contractmanager.core.service.grouping.CustomListService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 自定义列表配置Rest
 *
 * @author: xuanzhu
 * @since: 2019-10-25 16:20
 */
@Api(tags = "智能台账-自定义列表")
@ExternalService
@RestMapping(path = "/v2")
public class CustomListRest {


    @Autowired
    private CustomListService customListService;
    @RestMapping(path = "/standing_book/custome_list/field_data", method = RequestMethod.GET)
    @ApiOperation(value = "获取指定字段下拉数据", httpMethod = "GET")
    public BaseResult<FieldDataSourceDTO> getFieldData(
            @ApiParam(value = "字段code", required = true) @RequestParam String fieldCode,
            @ApiParam(value = "当前操作人oid", required = true) @RequestParam String accountId) {
        return BaseResult.success(customListService.getFieldData(getTenantId(),accountId,fieldCode));
    }

    @RestMapping(path = "/standing_book/custome_list/sys_data", method = RequestMethod.GET)
    @ApiOperation(value = "获取系统字段下拉数据", httpMethod = "GET")
    public BaseResult<FieldSysDataSourceDTO> getSysData(
            @ApiParam(value = "字段类型,来源为1", required = true) @RequestParam Integer type,
            @ApiParam(value = "当前操作人oid", required = true) @RequestParam String accountId) {
        return BaseResult.success(customListService.getSysData(getTenantId(),accountId,type));
    }

    /**
     * 获取自定义表格配置列表
     *
     * @param menuId
     * @param accountId
     * @return
     */
    @RestMapping(path = "/standing_book/custome_list", method = RequestMethod.GET)
    @ApiOperation(value = "获取自定义表格配置列表", httpMethod = "GET")
    public BaseResult<CustomListDTO> list(
            @ApiParam(value = "菜单id，默认空") @RequestParam String menuId,
            @ApiParam(value = "当前操作人oid", required = true) @RequestParam String accountId,
            @ApiParam(value = "台账Id")@RequestParam(required = false) String formId) {
        String clientId = RequestContextExtUtils.getClientId();
        String  operatorId = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(customListService.query(getTenantId(), accountId, menuId, clientId, formId, operatorId));
    }

    /**
     * 设置自定义表格字段配置
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/standing_book/custome_list", method = RequestMethod.PUT)
    @ApiOperation(value = "设置表格自定义字段", httpMethod = "PUT")
    public BaseResult<Boolean> setConfig(@RequestBody SetCustomeFieldRequest request) {
        String operatorId = RequestContextExtUtils.getOperatorId();
        String clientId = RequestContextExtUtils.getClientId();
        return BaseResult.success(customListService.setConfig(getTenantId(),request, operatorId, clientId));
    }

    @RestMapping(path = "/standing_book/custome_list/field/set", method = RequestMethod.POST)
    @ApiOperation(value = "设置表格自定义字段", httpMethod = "POST")
    public BaseResult<Boolean> setField(@RequestBody SetFieldRequest setFieldRequest) {
        String operateId = RequestContextExtUtils.getOperatorId();
        String tenantId = RequestContextExtUtils.getTenantId();
        String clientId = RequestContextExtUtils.getClientId();
        return BaseResult.success(customListService.setField(setFieldRequest, operateId, tenantId, clientId));
    }


    private String getTenantId() {
        return RequestContextExtUtils.getTenantId();
    }
}

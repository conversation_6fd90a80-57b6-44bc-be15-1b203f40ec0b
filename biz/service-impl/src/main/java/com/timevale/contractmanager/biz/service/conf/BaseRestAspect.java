package com.timevale.contractmanager.biz.service.conf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.core.model.dto.request.grouping.RequestContextRequest;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2020/2/3
 */
@Slf4j
public abstract class BaseRestAspect {
    /** 请求参数长度限制 */
    private static final String PROP_REST_INPUT_LENGTH_LIMIT = "rest.log.input.length.limit";
    /** 响应结果长度限制 */
    private static final String PROP_REST_OUTPUT_LENGTH_LIMIT = "rest.log.output.length.limit";

//    private static Logger logger = LoggerFactory.getLogger("request_logger");

    protected Object handle(ProceedingJoinPoint pjp, Function<Void, Void> function)
            throws Throwable {

        // 方法通知前获取时间,为什么要记录这个时间呢？当然是用来计算模块执行时间的
        long start = System.currentTimeMillis();

        // 拦截的方法参数
        Object[] args = pjp.getArgs();
        Object result = null;
        Exception exception = null;
        try {

            if (function != null) {
                function.apply(null);
            }

            if (null != args) {
                for (Object arg : args) {
                    // 接口参数校验
                    if (arg instanceof ToString) {
                        ValidationUtil.validateBean(arg);
                    }

                    // 设置公共入参
                    if (arg instanceof RequestContextRequest) {
                        ((RequestContextRequest) arg).setOperatorId(RequestContextExtUtils.getOperatorId());
                        ((RequestContextRequest) arg).setTenantId(RequestContextExtUtils.getTenantId());
                        ((RequestContextRequest) arg).setAppId(RequestContextExtUtils.getAppId());
                        ((RequestContextRequest) arg).setClientId(RequestContextExtUtils.getClientId());
                    }
                }
            }

            result = pjp.proceed();
        } catch (Exception e) {
            exception = e;
        }
        long cost = System.currentTimeMillis() - start;
        return handleResultAndSaveRequestLog(pjp, args, result, cost, exception);
    }

    /**
     * 保存请求日志
     *
     * @param pjp
     * @param args
     * @param result
     * @param time
     * @param e
     */
    private Object handleResultAndSaveRequestLog(
            ProceedingJoinPoint pjp, Object[] args, Object result, long time, Exception e) throws Exception {

        // 拦截的实体类，就是当前正在执行的方法
        String requestUrl = "";
        if (null != RequestContextHolder.getRequestAttributes()) {
            HttpServletRequest request =
                    ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                            .getRequest();
            requestUrl = request.getRequestURL().toString();
        }

        Class clazz = pjp.getTarget().getClass();

        // 拦截的方法名称
        String moduleName = clazz.getCanonicalName();
        String methodName = pjp.getSignature().getName();

        // 日志traceId
        String trackId = RequestContext.getTransactionId();
        // 请求入参
        String inputStr = parseInputStr(args);

        // 日志基本信息，接口地址/方法/入参
        String requestBaseLog =
                String.format(
                        "【contextId:%s】RequestUrl: %s, MethodName: %s.%s(), Cost: %sms, Input: %s, Output: {}",
                        trackId, requestUrl, moduleName, methodName, time, inputStr);

        // 接口耗时较长的接口请求日志
        String requestSlowLog = "慢API " + requestBaseLog;
        // 获取响应数据， 如果存在异常， 默认响应为空字符串
        String outputStr = null == e ? parseOutputStr(result) : "";
        if (time > 1000) {
            log.info(requestSlowLog, outputStr);
        } else {
            log.info(requestBaseLog, outputStr);
        }
        // 如果存在异常，抛出
        if (null != e) {
            throw e;
        }
        return result;
    }

    private String parseInputStr(Object[] args) {
        String inputStr = null;
        if (args != null) {
            args = filterHttpServletRequest(args);
            inputStr = JSON.toJSONString(args);
        }
        Integer inputLimit = ConfigService.getAppConfig().getIntProperty(PROP_REST_INPUT_LENGTH_LIMIT, null);
        if (StringUtils.isNotBlank(inputStr) && null != inputLimit && inputStr.length() > inputLimit) {
            inputStr = " IGNORE_INPUT_DATA  length=" + inputStr.length();
        }
        return inputStr;
    }

    private String parseOutputStr(Object result) {
        if (result == null) {
            return null;
        }
        String outputStr;
        try {
            if (result instanceof File) {
                File file = (File) result;
                outputStr = "返回是个文件，不记录,FileName:" + file.getName() + "  FileSize:" + file.length();
            } else if (result instanceof String) {
                outputStr = (String) result;
            } else if (result instanceof ResponseEntity) {
                ResponseEntity entity = (ResponseEntity) result;
                outputStr = objectToJsonString(entity.getBody());
            } else {
                outputStr = objectToJsonString(result);
            }
        } catch (Exception ex) {
            log.error("saveRequestLog exception.", ex);
            outputStr = "保存请求记录异常";
        }
        Integer outputLimit = ConfigService.getAppConfig().getIntProperty(PROP_REST_OUTPUT_LENGTH_LIMIT, null);
        if (StringUtils.isNotBlank(outputStr) && null != outputLimit && outputStr.length() > outputLimit) {
            outputStr = " IGNORE_OUTPUT_DATA  length=" + outputStr.length();
        }
        return outputStr;
    }

    /**
     * 过滤掉不能转换的参数
     *
     * @param args
     * @return
     */
    private Object[] filterHttpServletRequest(Object[] args) {
        List<Object> objects = Lists.newArrayList();
        if (args != null) {
            for (Object obj : args) {
                if (obj instanceof HttpServletRequest
                        || obj instanceof HttpServletResponse
                        || obj instanceof File
                        || obj instanceof byte[]) {
                    continue;
                }
                objects.add(obj);
            }
        }
        return objects.toArray();
    }

    /**
     * 将obj转换成字符串
     *
     * @param obj
     * @return
     */
    private String objectToJsonString(Object obj) {
        String str = "";
        try {
            if (obj instanceof String) {
                str = (String) obj;
            } else {
                str = JSON.toJSONString(obj, SerializerFeature.DisableCircularReferenceDetect);
            }
        } catch (Exception e) {

        }
        return str;
    }
}

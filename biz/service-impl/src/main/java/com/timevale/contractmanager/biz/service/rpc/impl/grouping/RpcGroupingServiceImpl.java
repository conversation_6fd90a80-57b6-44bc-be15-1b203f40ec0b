package com.timevale.contractmanager.biz.service.rpc.impl.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.GroupingInfoDO;
import com.timevale.contractmanager.common.service.api.grouping.RpcGroupingService;
import com.timevale.contractmanager.common.service.model.grouping.file.AddGroupingFileModel;
import com.timevale.contractmanager.common.service.model.grouping.file.GetProcessGroupingInfoByProcessModel;
import com.timevale.contractmanager.common.service.model.grouping.file.GetProcessGroupingInfoModel;
import com.timevale.contractmanager.common.service.model.grouping.file.GroupingFileModel;
import com.timevale.contractmanager.common.service.result.RpcOutput;
import com.timevale.contractmanager.common.service.result.grouping.GetProcessGroupingInfoResult;
import com.timevale.contractmanager.core.model.dto.request.grouping.file.AddGroupingFileRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.file.GroupingFileRequest;
import com.timevale.contractmanager.core.service.grouping.GroupingFileService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.RestService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019/9/27
 */
@RestService
public class RpcGroupingServiceImpl implements RpcGroupingService {

    @Autowired private GroupingFileService groupingFileService;

    @Override
    public GetProcessGroupingInfoResult getProcessGroupingInfoByProcessId(GetProcessGroupingInfoByProcessModel model) {
        GetProcessGroupingInfoResult result = new GetProcessGroupingInfoResult();
        result.setProcessId(model.getProcessId());

        List<GroupingInfoDO> list = groupingFileService.getGroupingInfoList(model.getProcessId(), model.getOid());
        return getProcessGroupingInfoResult(result, list);
    }

    private GetProcessGroupingInfoResult getProcessGroupingInfoResult(GetProcessGroupingInfoResult result, List<GroupingInfoDO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            // 一个流程 只有一个合同编号， 但是可能存在多个菜单
            // 取第一个
            GroupingInfoDO groupingInfoDO = list.get(0);
            result.setContractNo(groupingInfoDO.getContractNo());
            result.setCreateTime(groupingInfoDO.getCreateTime().getTime());
            result.setMenuIds(
                    list.stream().map(GroupingInfoDO::getMenuId).collect(Collectors.toList()));
        }

        return result;
    }

    @Override
    public GetProcessGroupingInfoResult getProcessGroupingInfo(GetProcessGroupingInfoModel model) {

        GetProcessGroupingInfoResult result = new GetProcessGroupingInfoResult();
        result.setProcessId(model.getProcessId());

        List<GroupingInfoDO> list = groupingFileService.getGroupingInfoListByMenu(model.getProcessId(),model.getMenuId());
        return getProcessGroupingInfoResult(result, list);
    }

    @Override
    public RpcOutput<Boolean> addGroupingInfo(AddGroupingFileModel model) {
        AddGroupingFileRequest request = new AddGroupingFileRequest();
        BeanUtils.copyProperties(model, request);
        return RpcOutput.with(groupingFileService.add(model.getTenantId(),request));
    }

    @Override
    public void fileGroup(GroupingFileModel model) {
        GroupingFileRequest request = new GroupingFileRequest();
        BeanUtils.copyProperties(model, request);
        groupingFileService.grouping(request, model.isSkipVerify());
    }
}

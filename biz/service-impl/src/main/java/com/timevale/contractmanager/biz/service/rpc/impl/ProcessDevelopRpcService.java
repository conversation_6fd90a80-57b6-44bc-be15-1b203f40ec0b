package com.timevale.contractmanager.biz.service.rpc.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.contract.notice.dto.ContractNoticeMsgDTO;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO;
import com.timevale.contractmanager.common.dal.bean.ProcessPreferenceDO;
import com.timevale.contractmanager.common.dal.bean.fulfillment.ContractFulfillmentRuleDO;
import com.timevale.contractmanager.common.dal.dao.ProcessDAO;
import com.timevale.contractmanager.common.dal.dao.ProcessDataCollectErrorMsgDAO;
import com.timevale.contractmanager.common.dal.dao.ProcessPreferenceDAO;
import com.timevale.contractmanager.common.dal.dao.fulfillment.ContractFulfillmentRuleDAO;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentNoticeQueryScriptModel;
import com.timevale.contractmanager.common.service.model.rule.RuleConditionModel;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectMsg;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollector;
import com.timevale.contractmanager.core.service.contratc.notice.ContractNoticeService;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.contractmanager.core.service.enums.ProcessFixTagEnum;
import com.timevale.contractmanager.core.service.enums.SealTypeEnum;
import com.timevale.contractmanager.core.service.fulfillment.builder.ContractFulfillmentRuleQueryScriptBuilder;
import com.timevale.contractmanager.core.service.mq.model.ContractFulfillmentRuleChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.model.ProcessStartedMsgEntity;
import com.timevale.contractmanager.core.service.mq.msg.LowcodeFormStructChangeMsg;
import com.timevale.contractmanager.core.service.mq.msg.LowcodePushFormDataMsg;
import com.timevale.contractmanager.core.service.mq.producer.ContractFulfillmentRuleChangeProducer;
import com.timevale.contractmanager.core.service.process.ProcessDispatchService;
import com.timevale.contractmanager.core.service.process.ProcessExpireService;
import com.timevale.contractmanager.core.service.process.ProcessService;
import com.timevale.contractmanager.core.service.process.config.ProcessBizRuleConfigService;
import com.timevale.contractmanager.core.service.process.datasource.FlowTemplateDataSourceStartService;
import com.timevale.contractmanager.core.service.process.datasource.FlowTemplateSetDataSourceService;
import com.timevale.contractmanager.core.service.process.datasource.ProcessDataSourceService;
import com.timevale.contractmanager.core.service.process.datasource.ProcessStartDataManager;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessStartSignParam;
import com.timevale.contractmanager.core.service.processstart.builder.DirectStartSignParamBuilder;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.esign.compontent.simple.encrypt.SimpleCipher;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.shortlink.common.service.api.ShortLinkRpcService;
import com.timevale.shortlink.common.service.request.ShortenCodeRequest;
import com.timevale.shortlink.common.service.result.ShortCodeResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/28 15:52
 *
 * processes 相关工具接口，通过预发网关调用
 */
@Slf4j
@RestService
public class ProcessDevelopRpcService {

    @Autowired
    private DirectStartSignParamBuilder directStartSignParamBuilder;
    @Autowired
    private ShortLinkRpcService shortLinkRpcService;
    @Autowired
    private ContractNoticeService contractNoticeService;
    @Autowired
    private ProcessDispatchService processDispatchService;
    @Autowired
    private ProcessBizRuleConfigService processBizRuleConfigService;
    @Autowired
    private ProcessService processService;
    @Autowired
    private ProcessPreferenceDAO processPreferenceDAO;
    @Autowired
    private ProcessDAO processDAO;
    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessDataCollector processDataCollector;
    @Autowired
    private FlowTemplateSetDataSourceService flowTemplateSetDataSourceService;
    @Autowired
    private FlowTemplateDataSourceStartService dataSourceStartService;
    @Autowired
    private ProcessStartDataManager processStartDataManager;
    @Autowired
    private ProcessExpireService processExpireService;
    @Autowired
    private ProcessDataSourceService processDataSourceService;
    @Autowired
    private ContractFulfillmentRuleDAO contractFulfillmentRuleDAO;
    @Autowired
    private ContractFulfillmentRuleQueryScriptBuilder contractFulfillmentRuleQueryScriptBuilder;
    @Autowired
    private ContractFulfillmentRuleChangeProducer contractFulfillmentRuleChangeProducer;

    /**
     * 校验客户签署偏好设置
     */

    /**
     * 构建填写参数
     */
    public ProcessStartSignParam developBuildStartSignParamFromCooperation(String currentSubProcessId) {
        // 组装并保存发起签署参数
        return directStartSignParamBuilder.buildProcessStartSignBean(currentSubProcessId);
    }


    /**
     * 接受通知发送消息各种流程相关的消息
     */
    public void developSendContractMsg(TempContractNoticeMsgDTO noticeMsg) {
        contractNoticeService.sendContractMsg(noticeMsg.getData());
    }

    @Data
    public static final class TempContractNoticeMsgDTO extends ToString {
        private ContractNoticeMsgDTO data;
    }


    /**
     * 主动触发发起流程
     */
    public String developDispatchNextSubProcess(String processId) {
        processDispatchService.dispatchNextSubProcess(processId, null);
        return "success";
    }


    /**
     * 解密context排查问题使用
     */
    public Map<String, String> developDecodeContext(String context) {
        String encParam = context;
        Map<String, String> map = Maps.newHashMap();
        if (StringUtils.isNotBlank(encParam)) {
            /** 根据短码获取原始的字符串 */
            ShortenCodeRequest shortenCodeRequest = new ShortenCodeRequest();
            shortenCodeRequest.setShortCode(encParam);

            ShortCodeResult shortCodeResult =
                    shortLinkRpcService.getOriginalCode(shortenCodeRequest);
            if (null != shortCodeResult
                    && shortCodeResult.isSuccess()
                    && StringUtils.isNotBlank(shortCodeResult.getLongCode())) {
                encParam = shortCodeResult.getLongCode();
                try {
                    encParam = URLDecoder.decode(encParam, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    log.warn("urlDecode失败:{}", e.getMessage());
                }
            }

            String decParam = simpleCipherDecodeParam(encParam);
            if (!StringUtils.isBlank(decParam)) {
                String[] namedParams = decParam.split("&");
                if (namedParams.length > 0) {
                    for (String namedParam : namedParams) {
                        String[] kvPair = namedParam.split("=");
                        if (kvPair.length != 2) {
                            continue;
                        }
                        map.put(kvPair[0], "null".equals(kvPair[1]) ? "" : kvPair[1]);
                    }
                }
            }
        }
        return map;
    }

    private String simpleCipherDecodeParam(String encParam) {
        try {
            return SimpleCipher.INSTANCE.decode("AES", encParam, "UTF-8");
        } catch (Exception e) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.SERVICE_BIZ_ERROR);
        }
    }

    /**
     * 校验对接发起服务的配置和代码里的配置是否相同，用于配置迁移时check
     */
    public String developCheckProcessConfigSame() {
        return processBizRuleConfigService.checkConfigSame();
    }

    /**
     * 修改合同偏好设置，个人章
     */
    public String developChangeProcessPreferencePersonSeal(String gids) {
        for (String gid : IdsUtil.getIdList(gids)) {
            List<ProcessPreferenceDO> preferenceDOS = processPreferenceDAO.queryByGIdAndTypes(gid,
                    Arrays.asList(ProcessPreferenceEnum.CONTRACT_PERSON_SIGN_METHOD.getKey()));
            if (CollectionUtils.isEmpty(preferenceDOS)) {
                log.info(gid + " 未设置过");
                continue;
            }
            ProcessPreferenceDO preferenceDO = preferenceDOS.get(0);
            if (StringUtils.isBlank(preferenceDO.getPreferenceValue())) {
                log.info(gid + " 未设置过1");
                continue;
            }

            List<String> values = IdsUtil.getIdList(preferenceDO.getPreferenceValue());
            if (values.contains(SealTypeEnum.IMAGE.getType())) {
                log.info(gid + " 已设置过图片签名");
                continue;
            }

            values.add(SealTypeEnum.IMAGE.getType());
            ProcessPreferenceDO updateData = new ProcessPreferenceDO();
            updateData.setId(preferenceDO.getId());
            updateData.setPreferenceValue(IdsUtil.toString(values));
            log.info("developChangeProcessPreferencePersonSeal gid: {} id :{} old: {} new : {}",
                    gid, preferenceDO.getId(), preferenceDO.getPreferenceValue(), updateData.getPreferenceValue());
            processPreferenceDAO.updateValue(updateData);
        }
        return "success";
    }

    /**
     * 恢复别无删除的数据
     */
    public String developRecoverDeleteProcess(String processId) {
        log.info("developRecoverDeleteProcess processId : {}", processId);
        ProcessDO processDO = new ProcessDO();
        processDO.setProcessId(processId);
        processDO.setDeleted(DeletedEnum.NO.code());
        int count = processDAO.updateByIdxProcessId(processDO);

        // 触发重新组装下es数据, 被删除的数据可能会有异常
        ProcessDataCollectMsg msg = new ProcessDataCollectMsg();
        msg.setMsg(new JSONObject().fluentPut("processId", processId).toJSONString());
        msg.setTopic(dataCollectConfigCenter.fixProcessTopicName());
        msg.setTag(ProcessFixTagEnum.DEFAULT.getCode());
        boolean result = processDataCollector.collect(msg);
        return "success update " + count + ";" + processId + "; fixData： " + result;
    }


    //************  start todo tianlei 数据库迁移后移除代码 用于测试步长是否生效
    public String developDeleteProcessPreference(String accountGid, String preferenceType) {
        log.info("developDeleteProcessPreference accountGid : {} preferenceType : {} ", accountGid, preferenceType);
        processPreferenceDAO.delete(accountGid, preferenceType);
        return "success";
    }

    @Autowired
    private ProcessDataCollectErrorMsgDAO processDataCollectErrorMsgDAO;

    /**
     * 测试后续可删除  $665b1ac0-ed4a-4c93-b506-8a76df81f85c$**********
     */
    public String developTestInsertToDB() {
        String id = UUIDUtil.genUUID();
        ProcessDataCollectErrorMsgDO msgDO = new ProcessDataCollectErrorMsgDO();
        msgDO.setProcessId(id);
        msgDO.setTopic(id);
        msgDO.setTag(id);
        msgDO.setContent("{}");
        processDataCollectErrorMsgDAO.save(msgDO);
        return msgDO.getId().toString();
    }
    //************ end  todo tianlei 数据库迁移后移除代码


    /**
     * 处理数据源结构变更
     */
    public String developProcessDataSourceStructChangeMsg(LowcodeFormStructChangeMsg structChangeMsg) {
        flowTemplateSetDataSourceService.processDataSourceStructChangeMsg(structChangeMsg);
        return "success";
    }

    /**
     * 收到数据源消息发起
     */
    public String developDataSourceStart(LowcodePushFormDataMsg msg) {
        dataSourceStartService.receiveData(msg);
        return "success";
    }

    /**
     * 删除采集数据
     */
    public String developProcessStartDataManager() {
        processStartDataManager.deleteSuccessData();
        return "success";
    }

    /**
     * 流程状态变更后置操作
     */
    public String developHandleProcessStatusChange(String processId) {
        processService.handleProcessStatusChange(processId);
        return "操作完成";
    }


    public String developProcessExpireJob() {
        processExpireService.processExpireJob();
        return "操作完成";
    }

    /**
     * 业务关联发起
     */
    public String developRelatedStartCallTopic(ProcessStartedMsgEntity processStartedMsgEntity) {
        processDataSourceService.startSuccessCallback(processStartedMsgEntity);
        return "success";
    }

    public String handleProcessToFormRelation(String processId, String formId, String lowCodeDataId) {
        processDataSourceService.handleProcessToFormRelation(processId, formId, lowCodeDataId);
        return "success";
    }

    /**
     *  构建履约通知查询语句
     * @param ruleData
     * @return
     */
    public String buildNoticeQueryScript(JSONObject ruleData) {
        ContractFulfillmentRuleDO rule = ruleData.getObject("rule", ContractFulfillmentRuleDO.class);
        List<RuleConditionModel> scopeConditions = ruleData.getJSONArray("scopeConditions").toJavaList(RuleConditionModel.class);
        ContractFulfillmentNoticeQueryScriptModel queryScriptModel = contractFulfillmentRuleQueryScriptBuilder.buildNoticeQueryScript(rule, scopeConditions);
        return JSON.toJSONString(queryScriptModel);
    }

    /**
     *  构建历史履约通知查询语句
     * @param ruleId
     * @return
     */
    public String syncHistoryRuleQueryScript(String ruleId, String tenantOid, String tenantGid, boolean all) {
        if (StringUtils.isNoneBlank(ruleId, tenantOid, tenantGid)) {
            ContractFulfillmentRuleChangeMsgEntity msgEntity = new ContractFulfillmentRuleChangeMsgEntity();
            msgEntity.setRuleId(ruleId);
            msgEntity.setTenantOid(tenantOid);
            msgEntity.setTenantGid(tenantGid);
            msgEntity.setNewRule(false);
            return contractFulfillmentRuleChangeProducer.sendMessage(msgEntity);
        }
        if (!all) {
            return "跳过";
        }
        Long count = contractFulfillmentRuleDAO.countAllRules();
        if (null == count || count == 0) {
            return "无履约规则";
        }
        int pageSize = 100;
        long times = count % pageSize == 0 ? count / pageSize : (count / pageSize + 1);
        for (long i = 0; i < times; i++) {
            List<ContractFulfillmentRuleDO> ruleList = contractFulfillmentRuleDAO.queryAllRules(Long.valueOf(i * pageSize).intValue(), pageSize);
            ruleList.forEach(rule -> {
                ContractFulfillmentRuleChangeMsgEntity msgEntity = new ContractFulfillmentRuleChangeMsgEntity();
                msgEntity.setRuleId(rule.getRuleId());
                msgEntity.setTenantOid(rule.getTenantOid());
                msgEntity.setTenantGid(rule.getTenantGid());
                msgEntity.setNewRule(false);
                String msgId = contractFulfillmentRuleChangeProducer.sendMessage(msgEntity);
                log.info("send rule change message success, ruleId: {}, msgId: {}", rule.getRuleId(), msgId);
            });
        }
        return "履约规则查询语句补偿中";
    }
}

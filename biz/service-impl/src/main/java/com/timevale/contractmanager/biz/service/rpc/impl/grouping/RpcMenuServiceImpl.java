package com.timevale.contractmanager.biz.service.rpc.impl.grouping;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.grouping.MenuDO;
import com.timevale.contractmanager.common.service.api.grouping.RpcMenuService;
import com.timevale.contractmanager.common.service.bean.MenuBaseInfo;
import com.timevale.contractmanager.common.service.bean.MenuIPathInfo;
import com.timevale.contractmanager.common.service.enums.grouping.MenuIdEnum;
import com.timevale.contractmanager.common.service.model.grouping.menu.CheckNotExistsMenuIdsRequest;
import com.timevale.contractmanager.common.service.model.grouping.menu.CreateMenuModel;
import com.timevale.contractmanager.common.service.model.grouping.menu.QueryAllMenuModel;
import com.timevale.contractmanager.common.service.result.RpcOutput;
import com.timevale.contractmanager.common.service.result.grouping.CheckNotExistsMenuIdsResult;
import com.timevale.contractmanager.core.model.dto.request.grouping.menu.CreateMenuRequest;
import com.timevale.contractmanager.core.model.dto.response.grouping.menu.MenuPathDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 目录相关-对外RPC接口实现
 *
 * @author: jinhuan
 * @since: 2019-10-10 11:42
 **/
@RestService
public class RpcMenuServiceImpl implements RpcMenuService {

    @Autowired
    private MenuService menuService;

    @Autowired
    UserCenterService userCenterService;

    @Override
    public RpcOutput<String> createMenu(CreateMenuModel model) {
        CreateMenuRequest request = new CreateMenuRequest();
        BeanUtils.copyProperties(model, request);
        return RpcOutput.with(menuService.create(model.getTenantId(),request));
    }

    @Override
    public RpcOutput<MenuBaseInfo> queryBaseInfo(String menuId) {
        if (MenuIdEnum.UNGROUPING.getMenuId().equals(menuId)
                || MenuIdEnum.OPPONENT_ENTITY.getMenuId().equals(menuId)) {
            MenuBaseInfo menuBean = new MenuBaseInfo();
            menuBean.setMenuId(menuId);
            menuBean.setMenuName(menuId);
            return RpcOutput.with(menuBean);
        }

        MenuDO menuDO = menuService.getMenuByMenuId(menuId);
        MenuBaseInfo menuBean = null;
        if (null != menuDO) {
            menuBean = new MenuBaseInfo();
            menuBean.setMenuId(menuDO.getMenuId());
            menuBean.setMenuName(menuDO.getName());
            menuBean.setOwnerOid(menuDO.getOid());
            menuBean.setOwnerGid(menuDO.getGid());
        }
        return RpcOutput.with(menuBean);
    }

    @Override
    public CheckNotExistsMenuIdsResult checkNotExistsMenuIds(CheckNotExistsMenuIdsRequest request) {
        List<String> notExistMenuIds = menuService.getNotExistMenuIds(request.getSubjectOid(), request.getMenuIds());
        CheckNotExistsMenuIdsResult result = new CheckNotExistsMenuIdsResult();
        result.setNotExistsMenuIds(notExistMenuIds);
        return result;
    }

    @Override
    public RpcOutput<List<MenuIPathInfo>> queryTenantMenuInfos(QueryAllMenuModel model) {
        if(StringUtils.isBlank(model.getAccountGid())){
            UserAccount account = userCenterService.getUserAccountBaseByOid(model.getAccountId());
            model.setAccountGid(account.getAccountGid());
        }
        ArrayList<String> accountGidList = Lists.newArrayList(model.getAccountGid());
        List<MenuPathDTO> menuPathDTOS = menuService.listMenuPathByAuthAndChild(model.getTenantId(), model.getAccountId(), accountGidList);
        List<MenuIPathInfo> menuIPathInfos = menuPathDTOS.stream().map(menuPathDTO -> JsonUtils.obj2pojo(menuPathDTO, MenuIPathInfo.class)).collect(Collectors.toList());
        return RpcOutput.with(menuIPathInfos);
    }
}

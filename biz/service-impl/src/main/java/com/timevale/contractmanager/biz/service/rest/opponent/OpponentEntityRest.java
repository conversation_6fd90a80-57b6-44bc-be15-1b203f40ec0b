package com.timevale.contractmanager.biz.service.rest.opponent;

import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentPrivilegeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.dto.request.opponent.*;
import com.timevale.contractmanager.core.model.dto.response.opponent.*;
import com.timevale.contractmanager.core.model.dto.response.opponent.detection.OpponentEnterpriseInfoResponse;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.auditlog.handler.AuditLogHelper;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.component.opponent.OpponentEntityConverter;
import com.timevale.contractmanager.core.service.grouping.CacheService;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityExportService;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityService;
import com.timevale.contractmanager.core.service.opponent.OrganizationQueryService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.dayu.sdk.annotation.AuditLogAnnotation;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_OPEN_OPERATOR_ID;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_OPEN_TENANT_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * @author: huifeng
 * @since: 2021-01-22 11:06
 **/
@Api(tags = "相对方管理-企业/个人")
@ExternalService
@RestMapping(path = "/v2")
public class OpponentEntityRest {

    @Autowired
    private OpponentEntityService opponentEntityService;

    @Autowired
    private OpponentEntityExportService opponentEntityExportService;

    @Autowired
    private OrganizationQueryService organizationQueryService;

    @Autowired
    private CacheService cacheService;

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_QUERY_ENTITY)
    @RestMapping(path = "/opponent/tenants/tenantId/organizations", method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间所有相对方企业列表", httpMethod = "GET")
    public BaseResult<OpponentOrganizationListResponse> listOrganizations(
            @ModelAttribute OpponentOrganizationListRequest listRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityService.listOrganizations(operatorOid, tenantOid, listRequest, true));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/opponent/tenants/tenantId/organizations/permission", method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间所有相对方企业列表,不校验权限", httpMethod = "GET")
    public BaseResult<OpponentOrganizationListResponse> listOrganizationsPermission(
            @ModelAttribute OpponentOrganizationListRequest listRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityService.listOrganizations(operatorOid, tenantOid, listRequest, false));
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_QUERY_ENTITY)
    @RestMapping(
            path = "/opponent/tenants/tenantId/organizations/{organizationId}",
            method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间下某个相对方企业详细信息", httpMethod = "GET")
    public BaseResult<OpponentOrganizationResponse> getOrganization(
            @PathVariable @ApiParam(value = "企业id", required = true) String organizationId) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityService.getOrganization(operatorOid, tenantOid, organizationId));
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_DELETE)
    @RestMapping(path = "/opponent/tenants/tenantId/organizations/{organizationId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除租户空间下某个相对方企业", httpMethod = "DELETE")
    public BaseResult<OpponentBaseResponse> deleteOrganization(
            @PathVariable @ApiParam(value = "企业id", required = true) String organizationId,
            @ApiParam(value = "是否删除企业下的个人联系人(true:删除)",required = true)
            @RequestParam  boolean deleteIndividuals)
    {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        opponentEntityService.deleteOpponentEntity(organizationId,deleteIndividuals,tenantOid);
        return BaseResult.success();
    }


    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_DELETE)
    @RestMapping(path = "/opponent/tenants/tenantId/organizations/batchDelete", method = RequestMethod.POST)
    @ApiOperation(value = "批量删除租户空间下相对方企业", httpMethod = "POST")
    public BaseResult<OpponentBaseResponse> batchDeleteOrganization(
             @RequestBody OpponentEntityBatchDeleteRequest batchDeleteRequest){
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        opponentEntityService.batchDeleteOpponentEntity(batchDeleteRequest.getUuid(),batchDeleteRequest.isDeleteIndividuals(),tenantOid);
        return BaseResult.success();
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_UPDATE)
    @RestMapping(
            path = "/opponent/tenants/tenantId/organizations/{organizationId}",
            method = RequestMethod.PUT)
    @ApiOperation(value = "更新租户空间下某个相对方企业详细信息", httpMethod = "PUT")
    public BaseResult<Boolean> updateOrganization(
            @PathVariable @ApiParam(value = "企业id", required = true) String organizationId,
            @RequestBody OpponentOrganizationUpdateRequest updateRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();
        updateRequest.setOrganizationName(updateRequest.getOrganizationName().trim());
        updateRequest.setCreditCode(updateRequest.getCreditCode().trim());

        opponentEntityService.updateOrganization(
                operatorOid, tenantOid, organizationId, updateRequest);
        return BaseResult.success(true);
    }

    @AuditLogAnnotation(
            enterpriseSpaceUnique1 = "#tenantId",
            userUnique1 = "#operatorId",
            resourceEntSpaceUnique = "#tenantId",
            result = "#_result != null && #_result.code == 0 ? " + AuditLogConstant.RESULT,
            resourceName = "#createRequest.organizationName",
            detailTactics = "1",
            condition = "{{#_result != null && #_result.code == 0}}",
            selfDefiningData =
                    "{{T(com.google.common.collect.ImmutableMap).of(\"opponentCreateMethod\", \"手动录入\")}}",
            postHandle = "auditLogOpponentCreateOrganizationHandle")
    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_ADD)
    @RestMapping(path = "/opponent/tenants/tenantId/organizations", method = RequestMethod.POST)
    @ApiOperation(value = "在租户空间添加相对方企业", httpMethod = "POST")
    public BaseResult<OpponentBaseResponse> createOrganization(
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String operatorId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String tenantId,
            @RequestBody OpponentOrganizationCreateOuterRequest createRequest)
    {
        AuditLogHelper.acceptHeaderFields();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        createRequest.setCreditCode(createRequest.getCreditCode().trim());
        createRequest.setOrganizationName(createRequest.getOrganizationName().trim());
        OpponentBaseResponse response = opponentEntityService.createOrganization(null,operatorOid,tenantOid,
                OpponentEntityConverter.convert(createRequest));
        response.setId(null);

        LogRecordContext.putVariable(AuditLogConstant.Field.OPPONENT_ID, response.getUuid());

        return BaseResult.success(response);
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_QUERY_ENTITY)
    @RestMapping(path = "/opponent/tenants/tenantId/individuals", method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间所有相对方个人列表", httpMethod = "GET")
    public BaseResult<OpponentIndividualListResponse> listIndividuals(
            @ModelAttribute OpponentIndividualListRequest listRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityService.listIndividuals(operatorOid, tenantOid, null, listRequest, true));
    }


    @RestMapping(path = "/opponent/tenants/tenantId/individuals/permission", method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间所有相对方个人列表,不校验权限", httpMethod = "GET")
    public BaseResult<OpponentIndividualListResponse> listIndividualsPermission(
            @ModelAttribute OpponentIndividualListRequest listRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityService.listIndividuals(operatorOid, tenantOid, null, listRequest, false));
    }
    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_QUERY_ENTITY)
    @RestMapping(
            path = "/opponent/tenants/tenantId/individuals/{individualId}",
            method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间下某个相对方个人详细信息", httpMethod = "GET")
    public BaseResult<OpponentIndividualResponse> getIndividual(
            @PathVariable @ApiParam(value = "个人id", required = true) String individualId) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityService.getIndividuals(operatorOid, tenantOid, individualId));
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_DELETE)
    @RestMapping(path = "/opponent/tenants/tenantId/individuals/{individualId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除租户空间下某个相对方个人", httpMethod = "DELETE")
    public BaseResult<OpponentBaseResponse> deleteIndividual(
            @PathVariable @ApiParam(value = "个人id", required = true) String individualId)
    {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();
        opponentEntityService.deleteOpponentEntity(individualId,Boolean.FALSE,tenantOid)  ;
        return BaseResult.success();
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_DELETE)
    @RestMapping(path = "/opponent/tenants/tenantId/individuals/batchDelete", method = RequestMethod.POST)
    @ApiOperation(value = "批量删除租户空间下相对方个人", httpMethod = "POST")
    public BaseResult<OpponentBaseResponse> batchDeleteIndividual(
             @RequestBody OpponentEntityBatchDeleteRequest batchDeleteRequest){
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        opponentEntityService.batchDeleteOpponentEntity(batchDeleteRequest.getUuid(),Boolean.FALSE,tenantOid);
        return BaseResult.success();
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_UPDATE)
    @RestMapping(
            path = "/opponent/tenants/tenantId/individuals/{individualId}",
            method = RequestMethod.PUT)
    @ApiOperation(value = "更新租户空间下某个相对方个人详细信息", httpMethod = "PUT")
    public BaseResult<OpponentBaseResponse> updateIndividual(
            @PathVariable @ApiParam(value = "个人id", required = true) String individualId,
            @RequestBody OpponentIndividualUpdateRequest updateRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        opponentEntityService.updateIndividual(operatorOid, tenantOid, individualId, updateRequest);
        return BaseResult.success();
    }

    @AuditLogAnnotation(
            enterpriseSpaceUnique1 = "#tenantId",
            userUnique1 = "#operatorId",
            resourceEntSpaceUnique = "#tenantId",
            result = "#_result != null && #_result.code == 0 ? " + AuditLogConstant.RESULT,
            resourceName = "#createRequest.individualName",
            detailTactics = "1",
            condition = "{{#_result != null && #_result.code == 0}}",
            selfDefiningData =
                    "{{T(com.google.common.collect.ImmutableMap).of(\"opponentCreateMethod\", \"手动录入\")}}",
            postHandle = "auditLogOpponentCreateIndividualHandle")
    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_ADD)
    @RestMapping(path = "/opponent/tenants/tenantId/individuals", method = RequestMethod.POST)
    @ApiOperation(value = "在租户空间添加相对方个人", httpMethod = "POST")
    public BaseResult<OpponentBaseResponse> createIndividual(
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String operatorId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String tenantId,
            @RequestBody OpponentIndividualCreateOuterRequest createRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        try {
            AuditLogHelper.acceptHeaderFields();
            if (cacheService.queryLimit(CacheUtil.userTwoFactorCheckFailKey(operatorOid), 10)) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.TWO_FACTOR_CHECK_FAILURE_LIMIT);
            }

            OpponentBaseResponse response = opponentEntityService.createIndividual(null, operatorOid, tenantOid,
                    OpponentEntityConverter.convert(createRequest));

            LogRecordContext.putVariable(AuditLogConstant.Field.OPPONENT_ID, response.getUuid());
            return BaseResult.success(response);
        } catch (BizContractManagerException e) {
            if (BizContractManagerResultCodeEnum.TWO_FACTOR_CHECK_FAILURE.getCode().equals(e.getCode())) {
                cacheService.increment(CacheUtil.userTwoFactorCheckFailKey(operatorOid), 5, TimeUnit.MINUTES);
            }
            throw e;
        }
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_QUERY_ENTITY)
    @RestMapping(
            path = "/opponent/tenants/tenantId/organizations/{organizationId}/individuals",
            method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间相对方企业下相对方个人列表", httpMethod = "GET")
    public BaseResult<OpponentIndividualListResponse> listIndividualsInOrganization(
            @PathVariable @ApiParam(value = "企业id", required = true) String organizationId,
            @ModelAttribute OpponentIndividualListRequest listRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityService.listIndividuals(
                        operatorOid, tenantOid, organizationId, listRequest, true));
    }

    @RestMapping(
            path = "/opponent/tenants/tenantId/organizations/{organizationId}/individuals/permission",
            method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间相对方企业下相对方个人列表,不校验权限", httpMethod = "GET")
    public BaseResult<OpponentIndividualListResponse> listIndividualsInOrganizationNoPermission(
            @PathVariable @ApiParam(value = "企业id", required = true) String organizationId,
            @ModelAttribute OpponentIndividualListRequest listRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityService.listIndividuals(
                        operatorOid, tenantOid, organizationId, listRequest, false));
    }

    @RestMapping(path = "/opponent/tenants/tenantId/checkCreateEntity", method = RequestMethod.GET)
    @ApiOperation(value = "获取租户空间下是否还能新增实体", httpMethod = "GET")
    public BaseResult<Void> checkEntityLimit(
            @ApiParam(value = "查询的实体类型(0:个人,1:企业)", required = true)
            @RequestParam Integer opponentEntityType) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();
        opponentEntityService.checkEntityLimit(operatorOid, tenantOid, opponentEntityType);
        return BaseResult.success();
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_EXPORT)
    @RestMapping(path = "/opponent/tenants/tenantId/asyncExport", method = RequestMethod.GET)
    @ApiOperation(value = "相对方导出", httpMethod = "GET")
    public BaseResult<OpponentExportResponse> asyncExport(
            @ModelAttribute OpponentExportRequest request) {
        String tenantOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(opponentEntityExportService.createExportTask(request, tenantOid, operatorOid));
    }


    @RestMapping(path = "/opponent/tenants/{tenantName}/simpleInfo", method = RequestMethod.GET)
    @ApiOperation(value = "查看企业基本信息", httpMethod = "GET")
    public BaseResult<OpponentEnterpriseInfoResponse> getSimpleInfoByOrgName(
            @PathVariable @ApiParam(value = "企业名", required = true) String tenantName)
    {
        Optional<OpponentEnterpriseInfoResponse> opponentEnterpriseInfoResponse = organizationQueryService.getSimpleInfoByOrgName(tenantName);
        OpponentEnterpriseInfoResponse response = new OpponentEnterpriseInfoResponse();
        if (opponentEnterpriseInfoResponse.isPresent()) {
            response = opponentEnterpriseInfoResponse.get();
        }
        return BaseResult.success(response);
    }

    @RestMapping(path = "/opponent/batchSyncCreditCode", method = RequestMethod.GET)
    public BaseResult runOnce(){
        opponentEntityService.batchSyncOpponentOrgans();
        return BaseResult.success();
    }
}

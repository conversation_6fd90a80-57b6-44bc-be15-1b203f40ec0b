package com.timevale.contractmanager.biz.service.rpc.impl;

import com.google.common.collect.Maps;
import com.timevale.contractmanager.biz.service.util.ProcessGroupConverter;
import com.timevale.contractmanager.common.dal.bean.ContractProcessGroupDO;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.dal.dao.ContractProcessGroupDAO;
import com.timevale.contractmanager.common.dal.dao.ProcessDAO;
import com.timevale.contractmanager.common.dal.dao.SubProcessDAO;
import com.timevale.contractmanager.common.service.api.ContractProcessService;
import com.timevale.contractmanager.common.service.bean.ContractProcessBean;
import com.timevale.contractmanager.common.service.bean.ContractProcessDetail;
import com.timevale.contractmanager.common.service.bean.ContractProcessGroupBean;
import com.timevale.contractmanager.common.service.enums.SubProcessTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.model.CreateProcessModel;
import com.timevale.contractmanager.common.service.model.QueryProcessModel;
import com.timevale.contractmanager.common.service.model.UpdateProcessFlowModel;
import com.timevale.contractmanager.common.service.model.UpdateProcessFlowNodeModel;
import com.timevale.contractmanager.common.service.result.CreateProcessResult;
import com.timevale.contractmanager.common.service.result.QueryProcessIdResult;
import com.timevale.contractmanager.common.service.result.QueryProcessListResult;
import com.timevale.contractmanager.common.service.result.QueryProcessResult;
import com.timevale.contractmanager.core.service.mq.producer.ProcessNotifyHandler;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.mandarin.common.result.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_GROUP_NOT_EXIST;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST;

@RestService
@Api(tags = "合同管理", description = "合同管理")
@Slf4j
public class ContractProcessServiceImpl implements ContractProcessService {

    @Autowired
    private ContractProcessGroupDAO contractProcessGroupDao;
    @Autowired
    private SubProcessDAO subProcessDAO;
    @Autowired
    private ProcessDAO processDAO;
    @Autowired
    private ProcessNotifyHandler processNotifyHandler;
    @Autowired
    private BaseProcessService baseProcessService;

    @Deprecated
    @Override
    @ApiOperation(value = "创建合同", httpMethod = "POST")
    public CreateProcessResult createProcess(CreateProcessModel model) {
        ContractProcessGroupDO processGroupDO = contractProcessGroupDao.queryByProcessGroupId(model.getProcessGroupId());
        if (null == processGroupDO) {
            throw new BizContractManagerException(PROCESS_GROUP_NOT_EXIST);
        }
        ContractProcessGroupBean processGroupBean = ProcessGroupConverter.convertGroupBean(processGroupDO);

        ContractProcessDetail processDetail = model.getProcessDetail();
        if (null != processDetail) {
            processDetail.setProcessGroupId(model.getProcessGroupId());
            //processDetail.setProcessId(processId);
            SubProcessDO subProcessDO = subProcessDAO.getByIdxSubprocessId(model.getFlowId());
            if(subProcessDO!=null)
                processDetail.setTrueProcessId(subProcessDO.getProcessId());
            if (null == processDetail.getFlowType()) {
                processDetail.setFlowType(model.getFlowType());
            }
            if (StringUtils.isBlank(processDetail.getFlowId())) {
                processDetail.setFlowId(model.getFlowId());
            }
            processNotifyHandler.processInfoAdd(processDetail, processGroupBean, null);
        }
        return new CreateProcessResult("");
    }

    @Deprecated
    @Override
    @ApiOperation(value = "更新合同业务", httpMethod = "POST")
    public BaseResult updateProcessFlow(UpdateProcessFlowModel model) {
        String processId = model.getProcessId();
        SubProcessDO subProcessDO = null;
        if (StringUtils.isBlank(processId) && StringUtils.isNotBlank(model.getProcessGroupId())) {
            subProcessDO = subProcessDAO.getByIdxSubprocessId(model.getFlowId());
            if (null != subProcessDO) {
                processId = subProcessDO.getProcessId();
            }
        }
        if (StringUtils.isBlank(processId)) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }

        ProcessDO processDO = processDAO.getByIdxProcessId(processId);
        if (null == processDO) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }
        // 获取合同流程组信息
        ContractProcessGroupDO processGroupDO = contractProcessGroupDao.queryByProcessGroupId(processDO.getProcessGroupId());
        if (null == processGroupDO) {
            throw new BizContractManagerException(PROCESS_GROUP_NOT_EXIST);
        }
        // 获取合同流程当前业务信息
        SubProcessDO currentProcessFlowDO = baseProcessService.getCurrentSubProcess(processId);

        String replacedFlowId = null;
        // 添加合同业务
        if (null == currentProcessFlowDO || !StringUtils.equals(model.getFlowId(), currentProcessFlowDO.getSubProcessId()) || !currentProcessFlowDO.getSubProcessType().equals(model.getFlowType())) {
            if (null != currentProcessFlowDO) {
                replacedFlowId = currentProcessFlowDO.getSubProcessId();
            }
        }

        ContractProcessDetail processDetail = model.getProcessDetail();
        if (null != processDetail) {
            if(subProcessDO == null) {
                subProcessDO = subProcessDAO.getByIdxSubprocessId(model.getFlowId());
            }
            processDetail.setProcessGroupId(processDO.getProcessGroupId());
            processDetail.setProcessId(processId);
            if(subProcessDO!=null && StringUtils.isNoneBlank(subProcessDO.getProcessId()))
                processDetail.setTrueProcessId(subProcessDO.getProcessId());
            if (null == processDetail.getFlowType()) {
                processDetail.setFlowType(model.getFlowType());
            }
            if (StringUtils.isBlank(processDetail.getFlowId())) {
                processDetail.setFlowId(model.getFlowId());
            }
            processNotifyHandler.processInfoAdd(processDetail, ProcessGroupConverter.convertGroupBean(processGroupDO), replacedFlowId);
        }
        return new BaseResult();
    }

    @Deprecated
    @Override
    public BaseResult updateProcessFlowNode(UpdateProcessFlowNodeModel model) {
        return new BaseResult();
    }

    @Deprecated
    @Override
    public QueryProcessResult queryProcess(String processId) {
        ProcessDO processDO = processDAO.getByIdxProcessId(processId);
        if (null == processDO) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }
        ContractProcessBean processBean = new ContractProcessBean();
        processBean.setProcessGroupId(processDO.getProcessGroupId());
        processBean.setProcessId(processId);
        SubProcessDO currentFlow = baseProcessService.getCurrentSubProcess(processId);
        if(null != currentFlow){
            processBean.setFlowId(currentFlow.getSubProcessId());
            processBean.setFlowType(SubProcessTypeEnum.valueOf(currentFlow.getSubProcessType()).convert2FlowTypeEnum().getType());
        }

        return new QueryProcessResult(processBean);
    }

    @Override
    @ApiOperation(value = "查询合同流程最新业务信息列表",httpMethod = "GET")
    public QueryProcessListResult queryProcessList(QueryProcessModel queryProcessModel) {
        QueryProcessListResult result = new QueryProcessListResult();
        List<String> processIds = queryProcessModel.getProcessIds();
        if(CollectionUtils.isEmpty(processIds)){
            result.setProcessBeans(Lists.newArrayList());
            return result;
        }
        List<ProcessDO> processDOS = baseProcessService.listBatchProcessByProcessIds(processIds);
        Map<String,ProcessDO> processIdDOMap = processDOS.stream().collect(Collectors.toMap(a->a.getProcessId(),b->b));
        List<SubProcessDO> subProcessDOS = baseProcessService.listBatchSubProcessByProcessIds(processIds);

        List<ContractProcessBean> contractProcessBeans = Lists.newArrayList();
        Map<String,List<SubProcessDO>> processIdListMap = Maps.newHashMap();
        for(SubProcessDO item : subProcessDOS){
            List<SubProcessDO> listByProcessId = processIdListMap.get(item.getProcessId());
            if(listByProcessId == null){
                listByProcessId = Lists.newArrayList();
                processIdListMap.put(item.getProcessId(), listByProcessId);
            }
            listByProcessId.add(item);
        }
        for(Map.Entry<String,List<SubProcessDO>> entry : processIdListMap.entrySet()){
            String processId = entry.getKey();
            ProcessDO processDO = processIdDOMap.get(processId);
            ContractProcessBean processBean = new ContractProcessBean();
            processBean.setProcessId(processId);
            processBean.setProcessGroupId(processDO.getProcessGroupId());
            SubProcessDO currentFlow = baseProcessService.getCurrentSubProcess(entry.getValue());
            if(currentFlow!=null){
                processBean.setFlowId(currentFlow.getSubProcessId());
                processBean.setFlowType(SubProcessTypeEnum.valueOf(currentFlow.getSubProcessType()).convert2FlowTypeEnum().getType());
            }
            contractProcessBeans.add(processBean);
        }

        result.setProcessBeans(contractProcessBeans);
        result.setSuccess(true);
        return result;
    }

    @Deprecated
    @Override
    public QueryProcessResult queryLastProcessFlow(String processId) {
        ProcessDO processDO = processDAO.getByIdxProcessId(processId);
        if (null == processDO) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }
        SubProcessDO prevFlow = baseProcessService.getPrevSubProcess(processId);
        ContractProcessBean processBean = new ContractProcessBean();
        processBean.setProcessGroupId(processDO.getProcessGroupId());
        processBean.setProcessId(processId);
        if (null != prevFlow) {
            processBean.setFlowType(SubProcessTypeEnum.valueOf(prevFlow.getSubProcessType()).convert2FlowTypeEnum().getType());
            processBean.setFlowId(prevFlow.getSubProcessId());
        }
        return new QueryProcessResult(processBean);
    }

    @Deprecated
    @Override
    public QueryProcessIdResult queryProcessId(String processGroupId, Integer flowType, String flowId) {
        SubProcessDO subProcessDO = subProcessDAO.getByIdxSubprocessId(flowId);
        if (null == subProcessDO) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }
        return new QueryProcessIdResult(subProcessDO.getProcessId());
    }
}

package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.api.RpcOfflineContractService;
import com.timevale.contractmanager.common.service.model.offlinecontract.*;
import com.timevale.contractmanager.common.service.result.offlinecontract.*;
import com.timevale.contractmanager.core.service.offlinecontract.OfflineContractService;
import com.timevale.contractmanager.core.service.offlinecontract.bean.input.*;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordContractsOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordInfoOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordsOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.enums.FailCodeEnum;
import com.timevale.mandarin.common.annotation.RestService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线下合同rpc接口实现
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@RestService
public class RpcOfflineContractServiceImpl implements RpcOfflineContractService {

    @Autowired OfflineContractService offlineContractService;

    @Override
    public SaveOfflineContractRecordResult saveOfflineContractRecord(
            SaveOfflineContractRecordModel model) {
        SaveOfflineContractRecordDTO input = new SaveOfflineContractRecordDTO();
        input.setAccountOid(model.getAccountOid());
        input.setAccountGid(model.getAccountGid());
        input.setAccountName(model.getAccountName());
        input.setSubjectOid(model.getSubjectOid());
        input.setSubjectGid(model.getSubjectGid());
        input.setSubjectName(model.getSubjectName());
        input.setMenuId(model.getMenuId());
        input.setImportWay(model.getImportWay());
        input.setExtractWay(model.getExtractWay());
        input.setClientId(model.getClientId());
        input.setContracts(model.getContracts());
        input.setExtractConfig(model.getExtractConfig());
        input.setMasterProcessId(model.getMasterProcessId());
        input.setDedicatedCloudId(model.getDedicatedCloudId());
        String recordId = offlineContractService.saveOfflineContractRecord(input);
        return new SaveOfflineContractRecordResult(recordId);
    }

    @Override
    public void deleteOfflineContractRecord(DeleteOfflineContractRecordsModel model) {
        DeleteOfflineContractRecordsDTO input = new DeleteOfflineContractRecordsDTO();
        input.setSubjectGid(model.getSubjectGid());
        input.setRecordIds(model.getRecordIds());
        offlineContractService.deleteOfflineContractRecords(input);
    }

    @Override
    public void stopImportOfflineContractRecord(StopImportOfflineContractModel model) {
        StopImportOfflineContractDTO input = new StopImportOfflineContractDTO();
        input.setSubjectGid(model.getSubjectGid());
        input.setRecordIds(model.getRecordIds());
        offlineContractService.stopImportOfflineContract(input);
    }

    @Override
    public void recoverImportOfflineContractRecord(RecoverImportOfflineContractModel model) {
        RecoverImportOfflineContractDTO input = new RecoverImportOfflineContractDTO();
        input.setSubjectGid(model.getSubjectGid());
        input.setRecordIds(model.getRecordIds());
        offlineContractService.recoverImportOfflineContract(input);
    }

    @Override
    public void restartImportFailedOfflineContract(RestartImportFailedOfflineContractModel model) {
        RestartImportFailedOfflineContractDTO input = new RestartImportFailedOfflineContractDTO();
        input.setSubjectGid(model.getSubjectGid());
        input.setRecordId(model.getRecordId());
        offlineContractService.restartImportFailedOfflineContract(input);
    }

    @Override
    public QueryOfflineContractRecordsResult queryOfflineContractRecords(
            QueryOfflineContractRecordsModel model) {

        QueryOfflineContractRecordsDTO input = new QueryOfflineContractRecordsDTO();
        input.setSubjectGid(model.getSubjectGid());
        input.setPageNum(model.getPageNum());
        input.setPageSize(model.getPageSize());
        input.setWithMenuPath(model.isWithMenuPath());
        input.setNeedFilterOperator(model.isNeedFilterOperator());
        input.setAccountGid(model.getAccountGid());
        OfflineContractRecordsOutputDTO outputDTO =
                offlineContractService.queryOfflineContractRecords(input);

        QueryOfflineContractRecordsResult result = new QueryOfflineContractRecordsResult();
        result.setTotal(outputDTO.getTotal());
        result.setRecords(outputDTO.getRecords());
        return result;
    }

    @Override
    public QueryOfflineContractRecordInfoResult queryOfflineContractRecordInfo(
            QueryOfflineContractRecordInfoModel model) {

        QueryOfflineContractRecordInfoDTO input = new QueryOfflineContractRecordInfoDTO();
        input.setSubjectGid(model.getSubjectGid());
        input.setRecordId(model.getRecordId());
        input.setWithMenuPath(model.isWithMenuPath());
        OfflineContractRecordInfoOutputDTO outputDTO =
                offlineContractService.queryOfflineContractRecordInfo(input);

        return buildOfflineContractRecordInfoResult(outputDTO);
    }

    /**
     * 组装导入记录基本信息响应数据
     *
     * @param outputDTO
     * @return
     */
    private QueryOfflineContractRecordInfoResult buildOfflineContractRecordInfoResult(
            OfflineContractRecordInfoOutputDTO outputDTO) {
        QueryOfflineContractRecordInfoResult result = new QueryOfflineContractRecordInfoResult();
        result.setRecordId(outputDTO.getRecordId());
        result.setMenuId(outputDTO.getMenuId());
        result.setMenuName(outputDTO.getMenuName());
        result.setMenuPath(outputDTO.getMenuPath());
        result.setImportWay(outputDTO.getImportWay());
        result.setImportTime(outputDTO.getImportTime());
        result.setExtractWay(outputDTO.getExtractWay());
        result.setExtractConfig(outputDTO.getExtractConfig());
        result.setContractSize(outputDTO.getContractSize());
        result.setSuccessSize(outputDTO.getSuccessSize());
        result.setFailedSize(outputDTO.getFailedSize());
        result.setStatus(outputDTO.getStatus());
        return result;
    }

    @Override
    public BatchQueryOfflineContractRecordInfoResult batchQueryOfflineContractRecordInfo(
            BatchQueryOfflineContractRecordInfoModel model) {
        BatchQueryOfflineContractRecordInfoDTO input = new BatchQueryOfflineContractRecordInfoDTO();
        input.setSubjectGid(model.getSubjectGid());
        input.setRecordIds(model.getRecordIds());
        input.setWithMenuPath(model.isWithMenuPath());
        List<OfflineContractRecordInfoOutputDTO> records =
                offlineContractService.batchQueryOfflineContractRecordInfo(input);

        BatchQueryOfflineContractRecordInfoResult result =
                new BatchQueryOfflineContractRecordInfoResult();
        result.setRecords(
                records.stream()
                        .map(i -> buildOfflineContractRecordInfoResult(i))
                        .collect(Collectors.toList()));
        return result;
    }

    @Override
    public QueryOfflineContractRecordContractsResult queryOfflineContractRecordContracts(
            QueryOfflineContractRecordContractsModel model) {

        QueryOfflineContractRecordContractsDTO input = new QueryOfflineContractRecordContractsDTO();
        input.setSubjectGid(model.getSubjectGid());
        input.setRecordId(model.getRecordId());
        input.setStatusList(model.getStatusList());
        input.setWithExtract(model.isWithExtract());
        input.setPageNum(model.getPageNum());
        input.setPageSize(model.getPageSize());
        OfflineContractRecordContractsOutputDTO outputDTO =
                offlineContractService.queryOfflineContractRecordContracts(input);

        QueryOfflineContractRecordContractsResult result =
                new QueryOfflineContractRecordContractsResult();
        result.setTotal(outputDTO.getTotal());
        result.setContracts(outputDTO.getContracts());
        return result;
    }

    @Override
    public OfflineContractCheckFileExistResult checkFileExist(OfflineContractCheckFileExistRequest request) {
        OfflineContractCheckFileExistResult result = new OfflineContractCheckFileExistResult();
        result.setExist(offlineContractService.checkFileExist(request.getFileIds()));
        return result;
    }

    @Override
    public QueryExistsCanRetryImportProcessResult queryExistsArrearsImportProcess(QueryExistsArrearsImportProcessModel model) {
        QueryExistsCanRetryImportProcessResult result = new QueryExistsCanRetryImportProcessResult();
        result.setExists(offlineContractService.existsFailCodeImportProcess(model.getRecordId(), FailCodeEnum.AI_ARREARS, model.getTenantGid()));
        return result;
    }
}

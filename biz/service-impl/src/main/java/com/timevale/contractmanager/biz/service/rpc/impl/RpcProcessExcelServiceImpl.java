package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.api.RpcProcessExcelService;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.model.FormDataModel;
import com.timevale.contractmanager.common.service.model.FormImportModel;
import com.timevale.contractmanager.common.service.model.ParticipantDataImportModel;
import com.timevale.contractmanager.common.service.result.FormImportResult;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.ExcelDownloadRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.ExcelInitDataDTO;
import com.timevale.contractmanager.core.model.dto.response.process.ExcelDownloadResponse;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.process.ProcessExcelService;
import com.timevale.contractmanager.core.service.process.impl.excel.ExcelHelper;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.contractmanager.spi.enums.ExcelHeadMappingEnum;
import com.timevale.contractmanager.spi.invoker.ExcelInvoker;
import com.timevale.contractmanager.spi.model.bean.excel.ExcelHeadBean;
import com.timevale.contractmanager.spi.model.request.excel.BatchProcessExcelExtenderInput;
import com.timevale.contractmanager.spi.model.result.excel.ExcelResult;
import com.timevale.docmanager.service.model.StructComponent;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.core.service.process.impl.excel.ExcelHelper.getFormHeader;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonList;

/**
 * <AUTHOR>
 * @since 2022-09-15 15:41
 */
@Slf4j
@RestService
public class RpcProcessExcelServiceImpl implements RpcProcessExcelService {

    @Autowired ProcessExcelService processExcelService;

    @Autowired private ExcelInvoker excelInvoker;

    @Autowired MapperFactory mapperFactory;

    @Override
    public FormImportResult importFormData(FormImportModel formImportModel) {
        String requireId = UUIDUtil.genUUID();
        RequestContextExtUtils.setTenantId(formImportModel.getTenantId());
        // 将表单数据转换成excel数据
        List<ExcelInitDataDTO> colValues = requireExcelValues(formImportModel);

        // 获取excel下载链接，把表单转换的数据作为excel的预填数据
        ExcelDownloadRequest request =
                mapperFactory.getMapperFacade().map(formImportModel, ExcelDownloadRequest.class);
        request.setExcelInitDataList(colValues);
        request.setExtraHeaderList(singletonList(Arrays.asList(getFormHeader(), getFormHeader())));
        request.setExtraDataList(listExtraData(formImportModel.getFormDataList(), colValues));
        request.setLockPreFill(true);
        ExcelDownloadResponse response = processExcelService.downloadBatchExcel(request);

        // 获取文件对应的fileKey
        String fileKey = response.getFileKey();
        String excelUrl = response.getFileUrl();

        TedisUtil.set(CacheUtil.excelFileKeyCacheKeyV2(formImportModel.getTenantId(), requireId), fileKey, 2, TimeUnit.HOURS);
        TedisUtil.set(CacheUtil.excelUrlCacheKeyV2(formImportModel.getTenantId(), requireId), excelUrl, 2, TimeUnit.HOURS);

        return new FormImportResult(requireId);
    }

    /**
     * 根据表单数据获得excel数据，表单数据分为固定列和控件列
     *
     * @param formImportModel 表单数据
     * @return excel格式数据，list中的对象代表一列，对象中包含本列所有行的数据
     */
    private List<ExcelInitDataDTO> requireExcelValues(FormImportModel formImportModel) {
        // 获取表头信息
        ExcelResult excelResult = getExcelResult(formImportModel);
        // 1. 获取固定列的数据  [{"index":1,"values":["李杰"]},{"index":2,"values":["15757174795"]}}]
        List<ExcelInitDataDTO> columnDataList = getImmutableData(excelResult, formImportModel.getFormDataList());
        // 获取每列对应的控件,用于获取控件列的内容, Map<列, 控件id>  {4:"739e66f93f1b4203b65fdd290eb13cee",5:"9da2fec38d8949949719cbc3c8630ab7",6:"e46ff3ef96c844d89b82208fde4ece83"}
        Map<Integer, String> colStructIdMap =
                getColStructIdMap(formImportModel.getFlowTemplateId(), excelResult);
        if (CollectionUtils.isEmpty(colStructIdMap)) {
            return columnDataList;
        }

        // 2. 填充非固定的列 根据每列的控件id，填充excel每列的数据, 上面把固定的列数据已经取出来了 + 这里在把可变的列塞进去
        List<FormDataModel> formDataList = formImportModel.getFormDataList();
        for (Map.Entry<Integer, String> entry : colStructIdMap.entrySet()) {
            // 把每个表单的数据，按列根据structId取出来
            ExcelInitDataDTO columnData = new ExcelInitDataDTO(new ArrayList<>(), entry.getKey());
            formDataList.stream()
                    .map(formData -> formData.obtainStructData(entry.getValue()))
                    .map(FormDataModel.StructData::getData)
                    .forEach(columnData.getValues()::add);
            columnDataList.add(columnData);
        }
        //  [{"index":1,"values":["李杰"]},{"index":2,"values":["15757174795"]},{"index":3,"values":[""]},{"index":4,"values":["李杰"]},{"index":5,"values":["15757174795"]},{"index":6,"values":[""]}]
        return columnDataList;
    }

    /**
     * 获取额外添加列数据
     *
     * @param formDataModels 表单数据
     * @param colDataList 当前excel数据，额外添加列是放在当前所有列的最后
     * @return 额外添加列数据
     */
    private List<ExcelInitDataDTO> listExtraData(
            List<FormDataModel> formDataModels, List<ExcelInitDataDTO> colDataList) {

        int maxCol = colDataList.stream().mapToInt(ExcelInitDataDTO::getIndex).max().getAsInt();
        List<String> colFormDataIds =
                formDataModels.stream()
                        .map(FormDataModel::getFormDataId)
                        .collect(Collectors.toList());

        return singletonList(new ExcelInitDataDTO(colFormDataIds, maxCol + 1));
    }


    /**
     * 获取excel 固定列数据， 参与方
     */
    private List<ExcelInitDataDTO> getImmutableData(ExcelResult excelResult, List<FormDataModel> formDataList) {
        List<ExcelInitDataDTO> immutableColData = new ArrayList<>();
        for (int i = 1; i <= excelResult.getCustomizeHeaders().size(); i++) {
            ExcelHeadBean excelHeadBean = excelResult.getCustomizeHeaders().get(i - 1);
            String participantLabel = excelHeadBean.getParticipantLabel();
            if (StringUtils.isBlank(participantLabel)) {
                continue;
            }
            List<String> values = new ArrayList<>();
            for (FormDataModel formDataModel : formDataList) {
                ParticipantDataImportModel singleParticipantDataModel =
                        formDataModel.getParticipantLabelDataMap().get(participantLabel);
                values.add(getValue(excelHeadBean, singleParticipantDataModel));
            }
            // 分别获取参与方对应的数据
            immutableColData.add(new ExcelInitDataDTO(values, i));
        }
        return immutableColData;
    }

    private String getValue(ExcelHeadBean excelHeadBean, ParticipantDataImportModel singleParticipantDataModel) {
        if (null == singleParticipantDataModel) {
            return "";
        }
        ExcelHeadMappingEnum excelHeadMapping = excelHeadBean.getExcelHeadData().getExcelHeadMapping();
        if (excelHeadMapping == ExcelHeadMappingEnum.ACCOUNT_NAME) {
            return singleParticipantDataModel.getName();
        } else if (excelHeadMapping == ExcelHeadMappingEnum.ACCOUNT) {
            return singleParticipantDataModel.getContract();
        } else if (excelHeadMapping == ExcelHeadMappingEnum.SUBJECT_NAME) {
            return singleParticipantDataModel.getSubjectName();
        }
        return "";
    }

    /**
     * 获取每列对应的控件id
     *
     * @param flowTemplateId 表单对应的模板
     * @param excelResult 固定列查询结果
     * @return Map<col, structId>
     */
    private Map<Integer, String> getColStructIdMap(String flowTemplateId, ExcelResult excelResult) {
        // 获取到固定列的表头,外层list是列，内层list是一列中每行的数据  [["姓名-签署方2","姓名-签署方2"],["手机/邮箱-签署方2","手机/邮箱-签署方2"],["合同名称（选填）-签署方2","合同名称（选填）-签署方2"]]
        List<List<String>> immutableHeader =
                excelResult.getCustomizeHeaders().stream()
                        .map(ExcelHeadBean::getName)
                        .collect(Collectors.toList())
                        .stream()
                        .map(headers -> Arrays.asList(headers, headers))
                        .collect(Collectors.toList());
        // left = [["姓名-签署方2","姓名-签署方2"],["手机/邮箱-签署方2","手机/邮箱-签署方2"],["合同名称（选填）-签署方2","合同名称（选填）-签署方2"],["文件-test.pdf","姓名（限20字）-签署方1"],["文件-test.pdf","手机号（限20字）-签署方1"],["文件-test.pdf","多出的一列（限20字）-签署方1"]]
        Pair<List<List<String>>, Map<String, List<StructComponent>>> pair =
                processExcelService.buildAndMergeCommonInfo(immutableHeader, flowTemplateId, null);

        // 每列的控件信息Map<"2-列", List<当前列的控件>>
        Map<String, List<StructComponent>> structMap = pair.getSecond();
        if (CollectionUtils.isEmpty(structMap)) {
            return emptyMap();
        }

        // 拼装列和控件id的映射关系 -> Map<col, structId>
        return pair.getSecond().entrySet().stream()
                .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                .filter(entry -> Objects.nonNull(entry.getValue().get(0)))
                .collect(
                        Collectors.toMap(
                                entry -> ExcelHelper.getColNum(entry.getKey()),
                                entry -> entry.getValue().get(0).getId()));
    }

    /**
     * 固定的列的信息
     *
     * @param formImportModel
     * @return
     */
    private ExcelResult getExcelResult(FormImportModel formImportModel) {
        BatchProcessExcelExtenderInput input =
                mapperFactory
                        .getMapperFacade()
                        .map(formImportModel, BatchProcessExcelExtenderInput.class);

        //  调用插件获取Excel表头信息
        ExcelResult excelResult = excelInvoker.getExcelBatchHead(input);
        if (null == excelResult || CollectionUtils.isEmpty(excelResult.getCustomizeHeaders())) {
            log.warn("ProcessExcelExtenderServiceImpl.downloadBatchExcel extender result failed");
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_BATCH_EXCEL_PLUGINS_FAIL.getCode(),
                    "下载excel失败，请稍后重试");
        }
        return excelResult;
    }
}

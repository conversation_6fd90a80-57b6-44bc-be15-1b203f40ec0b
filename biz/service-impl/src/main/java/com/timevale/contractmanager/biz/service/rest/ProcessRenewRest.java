package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.enums.BackFillTypeEnum;
import com.timevale.contractmanager.common.service.enums.RelationTypeEnum;
import com.timevale.contractmanager.core.model.dto.request.*;
import com.timevale.contractmanager.core.model.dto.response.*;
import com.timevale.contractmanager.core.service.dedicatedcloud.DedicatedCloudService;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.enums.SignModeEnum;
import com.timevale.contractmanager.core.service.process.ProcessSearchService;
import com.timevale.contractmanager.core.service.process.ProcessService;
import com.timevale.contractmanager.core.service.process.SignModeService;
import com.timevale.contractmanager.core.service.process.handler.ProcessBackFillHandler;
import com.timevale.contractmanager.core.service.process.handler.ProcessRelationHandler;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * 合同续签管理rest接口
 *
 * <AUTHOR> on 2021/06/21
 */
@Api(tags = "合同续签管理", description = "合同续签管理")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class ProcessRenewRest extends BaseRest {

    @Autowired private ProcessService processService;

    @Autowired private ProcessSearchService processSearchService;

    @Autowired private ProcessBackFillHandler processBackFillHandler;

    @Autowired private ProcessRelationHandler processRelationHandler;
    @Autowired
    private SignModeService signModeService;
    @Autowired
    private DedicatedCloudService dedicatedCloudService;

    /**
     * 流程待续签合同查询
     *
     * @param processId 流程id
     * @return 续签记录
     */
    @RestMapping(path = "/processes/{processId}/renewFileList", method = RequestMethod.GET)
    @ApiOperation(value = "获取原流程待续签文档列表", httpMethod = "GET")
    public BaseResult<QueryRelationFileListResponse> queryRenewFileList(
            @PathVariable(name = "processId") String processId,
            @RequestParam(value = "menuId", required = false) String menuId) {
        processRelationHandler.checkProcessCanRelate(
                processId, menuId, RelationTypeEnum.RENEW.getType());

        return BaseResult.success(
                processRelationHandler.queryRelationFileList(
                        processId, RelationTypeEnum.RENEW.getType()));
    }

    /**
     * 续签流程发起页面原流程信息回填
     *
     * @param processId 流程id
     * @return 解约记录
     */
    @RestMapping(path = "/processes/{processId}/renewBackFill", method = RequestMethod.GET)
    @ApiOperation(value = "续签流程发起页面原流程信息回填", httpMethod = "GET")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ProcessStartDetailResponse> renewBackFill(
            @PathVariable(name = "processId") String processId) {
        ProcessStartDetailResponse response = processBackFillHandler.getBackFillInfo(
                processId,BackFillTypeEnum.RENEW_PROCESS_BACKFILL.getType(),null,
                RequestContextExtUtils.getTenantId(), RequestContextExtUtils.getOperatorId());
        if (SignModeEnum.GLOBAL.equalBiz(response.getSignMode())) {
            signModeService.limitClientOperate(RequestContextExtUtils.getClientId(), RequestContextExtUtils.getAppName());
        }
        if (StringUtils.isNotBlank(response.getDedicatedCloudId())) {
            dedicatedCloudService.limitClientOperate(RequestContextExtUtils.getClientId(), RequestContextExtUtils.getAppName());
        }
        return BaseResult.success(response);
    }

    /**
     * 获取续签管理合同流程列表
     *
     * @param request
     * @return
     */
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.RENEW_MANAGE)
    @RestMapping(path = "/processes/renewalList", method = RequestMethod.GET)
    @ApiOperation(value = "获取续签管理合同流程列表", httpMethod = "GET")
    public BaseResult<ProcessQueryResponse> renewalProcessList(
            @ModelAttribute ProcessRenewalQueryRequest request) {

        return BaseResult.success(processSearchService.queryRenewalList(request));
    }

    /**
     * 修改合同续签配置
     *
     * @param request 请求体
     */
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.RENEW_MANAGE)
    @RestMapping(path = "/processes/updateRenewable", method = RequestMethod.POST)
    @ApiOperation(value = "修改合同续签配置", httpMethod = "POST")
    public BaseResult updateRenewable(@RequestBody UpdateRenewableRequest request) {

        processService.updateRenewable(request);

        return BaseResult.success();
    }

    /**
     * 校验流程模板是否可用于续签
     *
     * @param request 请求体
     */
    @RestMapping(path = "/processes/{processId}/checkTemplateRenewable", method = RequestMethod.GET)
    @ApiOperation(value = "校验流程模板是否可用于续签", httpMethod = "GET")
    public BaseResult<CheckTemplateRenewableResponse> checkTemplateRenewable(
            @PathVariable String processId, @ModelAttribute CheckTemplateRenewableRequest request) {

        return BaseResult.success(processService.checkTemplateRenewable(processId, request));
    }

    @ApiOperation(value = "缓存续签时选择的原文件列表", httpMethod = "POST")
    @RestMapping(path = "/processes/renew-selected-files/cache", method = RequestMethod.POST)
    public BaseResult<CacheSelectedFilesResponse> cacheRenewSelectedFiles(
            @RequestBody CacheSelectedFilesRequest param) {
        String accountId = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(
                processRelationHandler.cacheSelectedFiles(accountId, RelationTypeEnum.RENEW, param));
    }

    @ApiOperation(value = "获取续签时缓存的原文件列表", httpMethod = "GET")
    @RestMapping(path = "/processes/renew-selected-files/get", method = RequestMethod.GET)
    public BaseResult<GetSelectedFilesResponse> getRenewSelectedFiles(
            @RequestParam String serialKey) {
        String accountId = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(
                processRelationHandler.getSelectedFiles(accountId, RelationTypeEnum.RENEW, serialKey));
    }
}

package com.timevale.contractmanager.biz.service.conf;

import com.google.common.collect.Maps;
import com.timevale.saas.common.exception.customize.CustomizeCodeMessageMapping;
import com.timevale.saas.common.exception.customize.bean.CodeConvertBean;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.SERVICE_BIZ_ERROR;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.USER_ACCOUNT_NOT_EXIST;

/**
 * 实现cm自定义的错误码处理逻辑
 *
 * <AUTHOR>
 */
@Component
public class CmCustomizeCodeMessageMapping implements CustomizeCodeMessageMapping {

    @Override
    public int defaultBizErrorCode() {
        return SERVICE_BIZ_ERROR.getnCode();
    }

    @Override
    public Map<Integer, CodeConvertBean> getConvertBeanMapping() {
        Map<Integer, CodeConvertBean> convertBeanMap = Maps.newHashMap();
        convertBeanMap.put(
                ResultEnum.ACCOUNT_ID_INVALID.getCode(),
                new CodeConvertBean(
                        USER_ACCOUNT_NOT_EXIST.getnCode(), USER_ACCOUNT_NOT_EXIST.message()));
        return convertBeanMap;
    }
}

package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.dal.bean.grouping.GroupingInfoDO;
import com.timevale.contractmanager.common.service.api.RpcAutoArchiveInnerService;
import com.timevale.contractmanager.common.service.bean.rule.RuleConditionTreeDTO;
import com.timevale.contractmanager.common.service.enums.autoarchive.ArchiveRuleStatusEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveChangeRuleMenuModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveExecuteModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveQueryListModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveRuleModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveSaveRuleModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveUpdateRuleModel;
import com.timevale.contractmanager.common.service.model.autoarchive.ProcessStatusQueryListModel;
import com.timevale.contractmanager.common.service.result.autoarchive.AutoArchiveFormMenuMappingResult;
import com.timevale.contractmanager.common.service.result.autoarchive.AutoArchiveListResult;
import com.timevale.contractmanager.common.service.result.autoarchive.AutoArchiveMenuResult;
import com.timevale.contractmanager.common.service.result.autoarchive.AutoArchiveRuleResult;
import com.timevale.contractmanager.common.service.result.autoarchive.AutoArchiveRuleTreeResult;
import com.timevale.contractmanager.common.service.result.autoarchive.ProcessStatusResult;
import com.timevale.contractmanager.common.service.result.rule.RuleTemplateResult;
import com.timevale.contractmanager.common.utils.SubListUtil;
import com.timevale.contractmanager.core.model.dto.autoarchive.ArchiveTemplateDTO;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveOperatorRequest;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveUpdateOperatorRequest;
import com.timevale.contractmanager.core.model.dto.response.autoArchive.AutoArchiveMenuDTO;
import com.timevale.contractmanager.core.model.dto.response.autoArchive.AutoArchiveMenuResponse;
import com.timevale.contractmanager.core.model.dto.response.autoArchive.AutoArchiveRuleResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.autoarchive.AutoArchiveService;
import com.timevale.contractmanager.core.service.component.autoarchive.AutoArchiveOperationComponent;
import com.timevale.contractmanager.core.service.component.grouping.GroupingFileComponent;
import com.timevale.contractmanager.core.service.component.grouping.TemplateMatchComponent;
import com.timevale.contractmanager.core.service.grouping.AiRuleService;
import com.timevale.contractmanager.core.service.grouping.GroupingFileService;
import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.rule.RuleConditionService;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.signflow.search.docSearchService.bean.ProcessInfoTotalInfo;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * RpcAutoArchiveServiceImpl
 *
 * <AUTHOR>
 * @since 2023/8/9 5:07 下午
 */
@RestService
@Slf4j
public class RpcAutoArchiveInnerServiceImpl implements RpcAutoArchiveInnerService {

    @Autowired
    private AutoArchiveService autoArchiveService;

    @Autowired
    private MapperFactory mapperFactory;

    @Autowired
    private MenuService menuService;

    @Autowired
    private TemplateMatchComponent templateMatchComponent;

    @Autowired
    private RuleConditionService ruleConditionService;

    @Autowired
    private AutoArchiveOperationComponent autoArchiveOperationComponent;

    @Autowired
    private GroupingFileService groupingFileService;

    @Autowired
    private GroupingFileComponent groupingFileComponent;

    @Autowired
    private AiRuleService aiRuleService;

    @Autowired
    private SensorService sensorService;

    @Autowired
    private UserCenterService userCenterService;

    @Override
    public AutoArchiveListResult pageListRule(AutoArchiveQueryListModel request) {

        AutoArchiveMenuResponse response = autoArchiveService.list(request.getTenantId(), request.getOperatorId());
        List<AutoArchiveMenuDTO> autoArchiveMenuDTOS = response.getMenuList();
        if (CollectionUtils.isEmpty(autoArchiveMenuDTOS)) {
            return new AutoArchiveListResult(0, new ArrayList<>());
        }
        List<AutoArchiveMenuDTO> tiledList = new ArrayList<>();
        buildTiledList(autoArchiveMenuDTOS, tiledList);
        //排除没有归档规则的数据
        tiledList.removeIf(t -> StringUtils.isEmpty(t.getRuleId()));
        if (CollectionUtils.isEmpty(tiledList)) {
            return new AutoArchiveListResult(0, new ArrayList<>());
        }

        if(StringUtils.isNotEmpty(request.getMenuId())){
            tiledList.removeIf(t -> !t.getMenuId().equals(request.getMenuId()));
        }
        if(StringUtils.isNotEmpty(request.getFormName())){
            tiledList.removeIf(t -> StringUtils.isEmpty(t.getBindingFormName()) || !t.getBindingFormName().contains(request.getFormName()));
        }

        if (CollectionUtils.isEmpty(tiledList)) {
            return new AutoArchiveListResult(0, new ArrayList<>());
        }

        List<AutoArchiveMenuDTO> sortList = tiledList.stream().filter(t -> t.getTopTime() != null).sorted(Comparator.comparing(AutoArchiveMenuDTO::getTopTime, Comparator.reverseOrder())).collect(Collectors.toList());
        tiledList.removeIf(t -> t.getTopTime() != null);
        tiledList.sort(Comparator.comparing(AutoArchiveMenuDTO::getUpdateTime, Comparator.reverseOrder()));
        sortList.addAll(tiledList);

        int total = sortList.size();
        sortList = SubListUtil.pageList(sortList, request.getPageNum(), request.getPageSize());

        List<AutoArchiveMenuResult> menuList = mapperFactory.getMapperFacade().mapAsList(sortList, AutoArchiveMenuResult.class);
        //path转换
        Map<String, String> pathIdMap = menuList.stream().collect(Collectors.toMap(AutoArchiveMenuResult::getMenuId, AutoArchiveMenuResult::getPath));
        Map<String, String> pathNameMap = menuService.batchConvertMenuNamePath(pathIdMap);
        for (AutoArchiveMenuResult autoArchiveMenuResult : menuList) {
            autoArchiveMenuResult.setPathName(pathNameMap.get(autoArchiveMenuResult.getMenuId()));
        }
        return new AutoArchiveListResult(total, menuList);
    }

    private void buildTiledList(List<AutoArchiveMenuDTO> menuDTOS, List<AutoArchiveMenuDTO> tiledList) {
        if (CollectionUtils.isEmpty(menuDTOS)) {
            return;
        }
        for (AutoArchiveMenuDTO menuDTO : menuDTOS) {
            if (CollectionUtils.isNotEmpty(menuDTOS)) {
                buildTiledList(menuDTO.getChildNode(), tiledList);
            }
            tiledList.add(menuDTO);
        }
    }

    @Override
    public void topRule(String ruleId, String tenantId, String operatorId) {
        autoArchiveService.updateTopTime(ruleId, tenantId, operatorId);
    }

    @Override
    public void saveRule(AutoArchiveSaveRuleModel model) {
        AutoArchiveOperatorRequest operatorRequest = mapperFactory.getMapperFacade().map(model, AutoArchiveOperatorRequest.class);
        operatorRequest.setRevomeContract(model.getRemoveContract());
        autoArchiveService.createAutoArchiveOperator(model.getTenantId(), operatorRequest, model.getOperatorId());
    }

    @Override
    public void updateRule(AutoArchiveUpdateRuleModel model) {
        AutoArchiveUpdateOperatorRequest operatorRequest = mapperFactory.getMapperFacade().map(model, AutoArchiveUpdateOperatorRequest.class);
        operatorRequest.setRevomeContract(model.getRemoveContract());
        autoArchiveService.updateRule(model.getRuleId(), operatorRequest, model.getTenantId(), model.getOperatorId(), false);
    }

    @Override
    public void rerunRule(String ruleId, String tenantId) {
        autoArchiveService.runRule(tenantId, ruleId);
    }

    @Override
    public void updateRuleStatus(String ruleId, String tenantId, String operatorId, Integer status) {
        String message = autoArchiveService.updateRuleStatus(ruleId, tenantId, operatorId, status, false);
        if (StringUtils.isNotEmpty(message)) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.MENUID_NOT_CONTAIN, message);
        }
    }

    @Override
    public void updateRuleStatusNoCheck(String ruleId, Integer status) {
        autoArchiveService.updateRuleStatus(ruleId, status);
    }

    @Override
    public AutoArchiveRuleResult getRuleDetail(String ruleId, String tenantId) {
        AutoArchiveRuleResponse ruleResponse = autoArchiveService.getAutoArchiveRule(ruleId, tenantId);
        AutoArchiveRuleResult autoArchiveRuleResult = mapperFactory.getMapperFacade().map(ruleResponse, AutoArchiveRuleResult.class);
        autoArchiveRuleResult.setRemoveContract(ruleResponse.getRevomeContract());
        return autoArchiveRuleResult;
    }

    @Override
    public AutoArchiveRuleResult getRuleDetailByMenuId(String menuId, String tenantId) {
        String ruleId = autoArchiveService.getRuleIdByMenuId(menuId, tenantId);
        if(StringUtils.isEmpty(ruleId)){
            return new AutoArchiveRuleResult();
        }
        return getRuleDetail(ruleId, tenantId);
    }

    @Override
    public AutoArchiveFormMenuMappingResult getMappingByMenuId(String menuId, String tenantId) {
        String formId = aiRuleService.getRuleIdByMenuId(menuId);
        if(StringUtils.isEmpty(formId)){
            return new AutoArchiveFormMenuMappingResult();
        }
        return new AutoArchiveFormMenuMappingResult(formId, menuId);
    }

    @Override
    public RuleTemplateResult getTemplateInfo(String processId) {
        ArchiveTemplateDTO archiveTemplateDTO = templateMatchComponent.getProcessTemplate(processId);
        return mapperFactory.getMapperFacade().map(archiveTemplateDTO, RuleTemplateResult.class);
    }

    @Override
    public List<AutoArchiveRuleTreeResult> listEnableRuleTreeByTenantId(String tenantGid) {
        List<AutoArchiveRuleTreeResult> results = new ArrayList<>();

        List<AutoArchiveRuleModel> ruleModels = autoArchiveService.listRuleByTenantIdAndStatus(tenantGid, Arrays.asList(ArchiveRuleStatusEnum.OPENING.getCode(), ArchiveRuleStatusEnum.RUNNING.getCode()));
        if (CollectionUtils.isEmpty(ruleModels)) {
            return new ArrayList<>();
        }
        for (AutoArchiveRuleModel ruleModel : ruleModels) {
            List<RuleConditionTreeDTO> treeDTOS = ruleConditionService.listByRuleId(tenantGid, ruleModel.getUuid());
            if (CollectionUtils.isEmpty(treeDTOS)) {
                continue;
            }
            AutoArchiveRuleTreeResult treeResult = new AutoArchiveRuleTreeResult();
            treeResult.setRuleId(ruleModel.getUuid());
            treeResult.setBindingMenuId(ruleModel.getBindingMenuId());
            treeResult.setTreeDTOS(treeDTOS);
            results.add(treeResult);
        }
        return results;
    }

    @Override
    public void executeArchive(AutoArchiveExecuteModel request) {
        List<ProcessInfoTotalInfo> processInfoTotalInfos = new ArrayList<>();
        for(String processId : request.getProcessIds()){
            ProcessInfoTotalInfo processInfoTotalInfo = new ProcessInfoTotalInfo();
            processInfoTotalInfo.setProcessId(processId);
            processInfoTotalInfos.add(processInfoTotalInfo);
        }
        List<GroupingInfoDO> groupingInfoDOS = autoArchiveOperationComponent.executeArchive(processInfoTotalInfos, request.getMenuId(), request.getTenantOid());
        String ruleId = autoArchiveService.getRuleIdByMenuId(request.getMenuId(), request.getTenantOid());
        if(StringUtils.isEmpty(ruleId) || CollectionUtils.isEmpty(groupingInfoDOS)){
            return;
        }
        addArchiveNum(ruleId, groupingInfoDOS.size());
        UserAccount account = userCenterService.getUserAccountBaseByOid(request.getTenantOid());
        for (GroupingInfoDO ignored : groupingInfoDOS) {
            sensorService.autoArchiveTracking(account.getAccountOid(), account.getAccountName(), request.getMenuId());
        }
    }

    @Override
    public void removeArchive(AutoArchiveExecuteModel request) {

        groupingFileService.deleteGroupingInfo(request.getMenuId(), request.getProcessIds(), request.getTenantOid());
        // 从ES里面删除对应记录
        groupingFileComponent.reMoveArchive(request.getMenuId(), request.getProcessIds(), request.getTenantOid());
    }

    @Override
    public void addArchiveNum(String ruleId, Integer num) {
        autoArchiveService.addArchiveNum(ruleId, num);
    }

    @Override
    public void bindRuleMenu(AutoArchiveChangeRuleMenuModel request) {
        AutoArchiveOperatorRequest operatorRequest = mapperFactory.getMapperFacade().map(request, AutoArchiveOperatorRequest.class);
        operatorRequest.setRevomeContract(request.getRemoveContract());
        operatorRequest.setOldMenuId(request.getBindingMenuId());
        Integer code = autoArchiveService.bindRule(request.getTenantId(), operatorRequest, request.getOperatorId());
        if(BizContractManagerResultCodeEnum.MENUID_BIND.getnCode().equals(code)){
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.MENUID_BIND);
        }
    }

    @Override
    public void unbindRuleMenu(String menuId, String tenantId, String operatorId) {
        autoArchiveService.unbindRuleMenu(menuId, tenantId, operatorId);
    }

    @Override
    public boolean hasArchiveRule(String tenantGid) {
        return autoArchiveService.existArchiveRule(tenantGid);
    }

    @Override
    public ProcessStatusResult getOptionalStatusList(ProcessStatusQueryListModel request) {
        return autoArchiveService.getOptionalStatusList(request.getTenantId());
    }
}

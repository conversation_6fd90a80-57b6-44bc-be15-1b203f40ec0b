package com.timevale.contractmanager.biz.service.rpc.impl.opponent;

import com.timevale.contractmanager.common.service.api.opponent.OpponentRiskLevelRpcService;
import com.timevale.contractmanager.common.service.model.opponent.BatchQueryRiskLevelByProcessIdsModel;
import com.timevale.contractmanager.common.service.model.opponent.QueryRiskLevelByEntityModel;
import com.timevale.contractmanager.common.service.model.opponent.QueryRiskLevelByOidOrGidModel;
import com.timevale.contractmanager.common.service.model.opponent.QueryRiskLevelByProcessIdModel;
import com.timevale.contractmanager.common.service.result.opponent.OpponentRiskLevelByProcessIdsResult;
import com.timevale.contractmanager.core.service.opponent.OpponentBlackListService;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import com.timevale.mandarin.common.annotation.RestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Arrays;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-03-02 17:53
 */
@RestService
public class OpponentRiskLevelRpcServiceImpl implements OpponentRiskLevelRpcService {

	@Autowired
	private OpponentBlackListService OpponentBlackListService;

	@Value("${opponent.risk.level.fusing}")
	private String opponentFusing;

	@Override
	public void queryRiskLevelByProcessId(QueryRiskLevelByProcessIdModel queryRiskLevelByProcessIdModel) {
		List<String> fusing = Arrays.asList(opponentFusing.split(","));
		if(!fusing.contains(String.valueOf(queryRiskLevelByProcessIdModel.getCallTheSource()))){
			return;
		}
		OpponentBlackListService.queryBlackListByProcessId(
				queryRiskLevelByProcessIdModel.getProcessId(),
				queryRiskLevelByProcessIdModel.getFlowId(),
				queryRiskLevelByProcessIdModel.getTenantDatas());
	}

	@Override
	public void queryRiskLevelByEntity(QueryRiskLevelByEntityModel queryRiskLevelByEntityModel) {
		List<String> fusing = Arrays.asList(opponentFusing.split(","));
		if(!fusing.contains(String.valueOf(queryRiskLevelByEntityModel.getCallTheSource()))){
			return;
		}
		OpponentBlackListService.queryBlackListNoAccountByEntity(queryRiskLevelByEntityModel, true);
	}

	@Override
	public void queryRiskLevelByEntity(QueryRiskLevelByOidOrGidModel queryRiskLevelByOidOrGidModel) {
		List<String> fusing = Arrays.asList(opponentFusing.split(","));
		if(!fusing.contains(String.valueOf(queryRiskLevelByOidOrGidModel.getCallTheSource()))){
			return;
		}
		OpponentBlackListService.queryRiskLevelByOidOrGid(queryRiskLevelByOidOrGidModel);
	}

	@Override
	public OpponentRiskLevelByProcessIdsResult batchQueryRiskLevelByProcessIds(BatchQueryRiskLevelByProcessIdsModel model) {
		return OpponentBlackListService.batchQueryRiskLevelByProcessIds(model);
	}

	@PuppeteerConfigChangeListener(value = {"application"})
	private void configChangeListener(ConfigChangeEvent changeEvent) {
		if (changeEvent.isChanged(opponentFusing)) {
			opponentFusing = changeEvent.getChange(opponentFusing).getNewValue();
		}
	}
}

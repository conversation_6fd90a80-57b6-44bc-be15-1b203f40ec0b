package com.timevale.contractmanager.biz.service.conf;

import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.saas.tracking.service.IOperatorService;
import org.springframework.stereotype.Component;

/**
 * 从header中获取埋点常用属性
 *
 * <AUTHOR>
 * @since 2022-12-09
 */
@Component
public class TrackingOperation implements IOperatorService {
    @Override
    public String getOperatorId() {
        return RequestContextExtUtils.getOperatorId();
    }

    @Override
    public String getTenantId() {
        return RequestContextExtUtils.getTenantId();
    }

    @Override
    public String getPlatform() {
        return RequestContextExtUtils.getClientId();
    }

    @Override
    public String getAppId() {
        return RequestContextExtUtils.getAppId();
    }
}

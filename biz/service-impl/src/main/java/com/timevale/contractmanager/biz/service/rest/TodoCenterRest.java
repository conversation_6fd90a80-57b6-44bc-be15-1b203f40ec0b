package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.core.model.dto.request.TodoProcessRequest;
import com.timevale.contractmanager.core.model.dto.response.todocenter.ProcessTodoListResponse;
import com.timevale.contractmanager.core.model.dto.response.todocenter.SubjectsTodoTotalResponse;
import com.timevale.contractmanager.core.model.dto.response.todocenter.TypeTodoHaveResponse;
import com.timevale.contractmanager.core.model.dto.response.todocenter.TypeTodoTotalResponse;
import com.timevale.contractmanager.core.service.todocenter.TodoCenterService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021-11-24 11:33
 */
@Api(tags = "待办中心")
@ExternalService
@RestMapping(path = "/v2/todo")
public class TodoCenterRest {
    private static final String HEADER_OPERATOR_ID = "X-Tsign-Open-Operator-Id";
    private static final String HEADER_TENANT_ID = "X-Tsign-Open-Tenant-Id";

    @Autowired private TodoCenterService todoCenterService;


    @ApiOperation(value = "是否有待办，按照流程所处状态", httpMethod = "GET")
    @RestMapping(path = "/have", method = RequestMethod.GET)
    public BaseResult<TypeTodoHaveResponse> have() {
        return BaseResult.success(todoCenterService.have(RequestContextExtUtils.getTenantId(), RequestContextExtUtils.getOperatorId()));
    }

    @ApiOperation(value = "待办数量统计，按照流程所处状态", httpMethod = "GET")
    @RestMapping(path = "/count", method = RequestMethod.GET)
    @MultilingualTranslateMethod
    public BaseResult<TypeTodoTotalResponse> count(
            @RequestHeader(value = HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(value = HEADER_TENANT_ID) String subjectId,
            @ApiParam(value = "是否查询 用印审批和合同审批总数", required = false) @RequestParam(required = false) Boolean queryApprovalTotalCount,
            @ApiParam(value = "获取当前主体的总数", required = false) @RequestParam(required = false, defaultValue = "false") Boolean queryCurrentSubject) {
        return BaseResult.success(todoCenterService.count(accountId, subjectId, queryApprovalTotalCount, queryCurrentSubject));
    }

    @ApiOperation(value = "待办数量统计，按照流程所属主体", httpMethod = "GET")
    @RestMapping(path = "/subjects/aggregate", method = RequestMethod.GET)
    @MultilingualTranslateMethod
    public BaseResult<SubjectsTodoTotalResponse> subjectsAggregate(
            @RequestHeader(value = HEADER_OPERATOR_ID) String accountId,
            @ApiParam("待办类型: 1-待我签署，2-待我填写，3-待我审批") @RequestParam(value = "type") Integer type) {

        return BaseResult.success(todoCenterService.aggregateBySubject(accountId, type));
    }

    @ApiOperation(value = "待办合同列表", httpMethod = "GET")
    @RestMapping(path = "/processes/list", method = RequestMethod.GET)
    public BaseResult<ProcessTodoListResponse> processList(
            @RequestHeader(value = HEADER_OPERATOR_ID) String accountId,
            @ApiParam("待办查询条件") @ModelAttribute(value = "request") TodoProcessRequest request) {

        return BaseResult.success(todoCenterService.listProcess(accountId, request));
    }
}

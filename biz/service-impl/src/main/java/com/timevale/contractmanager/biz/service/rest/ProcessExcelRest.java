package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.core.model.dto.request.grouping.process.ExcelDownloadRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.ExcelParseRequest;
import com.timevale.contractmanager.core.model.dto.response.ParseMultiBatchExcelResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessExcelResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ExcelDownloadResponse;
import com.timevale.contractmanager.core.service.process.ProcessExcelService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.tlcache.cache.ThreadLocalCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2022-09-21 22:36
 */
@Api(tags = "合同流程excel操作")
@ExternalService
@RestMapping(path = "/v2/processes/excel")
@Slf4j
public class ProcessExcelRest {

    @Autowired private ProcessExcelService processExcelService;

    /**
     * 下载批量导入表格整合接口
     *
     * @param excelDownloadRequest 请求参数
     * @return excel文档地址
     */
    @ApiOperation(value = "平台版-下载批量导入表格", httpMethod = "POST")
    @RestMapping(path = "/download", method = RequestMethod.POST)
    public BaseResult<String> downloadStartBatchExcel(
            @RequestBody ExcelDownloadRequest excelDownloadRequest) {
        ExcelDownloadResponse response = processExcelService.downloadBatchExcel(excelDownloadRequest);
        return BaseResult.success(response.getFileUrl());
    }

    /**
     * 解析批量导入表格
     *
     * @param excelParseRequest 请求参数 {@link ExcelParseRequest}
     * @return 解析文档信息
     */
    @ThreadLocalCache
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "平台版-解析批量导入表格", httpMethod = "POST")
    @RestMapping(path = "/parse", method = RequestMethod.POST)
    public BaseResult<ParseMultiBatchExcelResponse> parseStartBatchExcel(
            @RequestBody ExcelParseRequest excelParseRequest) {
        return BaseResult.success(processExcelService.parseBatchExcel(excelParseRequest));
    }

    /**
     * 获取excel资源信息
     *
     * @param requireId requireId
     * @return
     */
    @ApiOperation(value = "获取excel文件fileKey", httpMethod = "GET")
    @RestMapping(path = "/{requireId}/get", method = RequestMethod.GET)
    public BaseResult<ProcessExcelResponse> getExcelResource(
            @PathVariable String requireId) {
        return BaseResult.success(processExcelService.getExcelResource(RequestContextExtUtils.getTenantId(), requireId));
    }
}

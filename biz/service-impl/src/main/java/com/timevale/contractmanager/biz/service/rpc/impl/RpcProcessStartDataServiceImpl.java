package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.dal.query.processstartdata.ProcessStartDataQuery;
import com.timevale.contractmanager.common.service.api.RpcProcessStartDataService;
import com.timevale.contractmanager.common.service.bean.ProcessStartDataDTO;
import com.timevale.contractmanager.common.service.enums.ProcessStartDataStatusEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStartDataTypeEnum;
import com.timevale.contractmanager.common.service.model.ProcessStartDataDataIdQueryModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataDeleteModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataQueryModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataOuterDataIdAndTemplateIdQueryModel;
import com.timevale.contractmanager.common.service.model.ProcessStartedDataQueryModel;
import com.timevale.contractmanager.common.service.result.ProcessStartDataDeleteResult;
import com.timevale.contractmanager.common.service.result.ProcessStartDataQueryByDataIdResult;
import com.timevale.contractmanager.common.service.result.ProcessStartDataQueryResult;
import com.timevale.contractmanager.common.utils.DateUtil;
import com.timevale.contractmanager.core.model.bo.process.ProcessStartDataModel;
import com.timevale.contractmanager.core.service.process.datasource.ProcessStartDataManager;
import com.timevale.mandarin.common.annotation.RestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/23 11:27
 */
@RestService
@Slf4j
public class RpcProcessStartDataServiceImpl implements RpcProcessStartDataService {

    @Autowired
    private ProcessStartDataManager processStartDataManager;

    @Override
    public ProcessStartDataDeleteResult processStartDataDeleteData(ProcessStartDataDeleteModel deleteRequest) {
        int deleteCount = processStartDataManager.deleteByDataIds(deleteRequest.getDataIdList());
        ProcessStartDataDeleteResult result = new ProcessStartDataDeleteResult();
        result.setDeleteCount(deleteCount);
        return result;
    }

    @Override
    public ProcessStartDataQueryByDataIdResult processStartDataQueryByDataId(ProcessStartDataDataIdQueryModel dataIdQueryRequest) {
        List<ProcessStartDataModel> datas = processStartDataManager.listByDataIdList(dataIdQueryRequest.getDataIdList());
        ProcessStartDataQueryByDataIdResult result = new ProcessStartDataQueryByDataIdResult();
        result.setList(convert(datas));
        return result;
    }

    @Override
    public ProcessStartDataQueryByDataIdResult getByOuterDataIdAndTemplateId(ProcessStartDataOuterDataIdAndTemplateIdQueryModel model) {
        ProcessStartDataQueryByDataIdResult result = new ProcessStartDataQueryByDataIdResult();
        // 如果有模板id会返回唯一发起数据，没有则返回所有和这个三方数据id有关的发起数据
        if (model.getFlowTemplateId() != null) {
            ProcessStartDataModel startDataModel =
                    processStartDataManager.getByFlowTemplateIdAndOuterDataId(model.getFlowTemplateId(), model.getOuterDataId());
            result.setList(convert(Collections.singletonList(startDataModel)));
            return result;
        }
        List<ProcessStartDataModel> processStartDataModels 
                = processStartDataManager.listByOuterDataId(model.getOuterDataId());
        result.setList(convert(processStartDataModels));
        return result;
    }

    @Override
    public ProcessStartDataQueryResult processStartDataListByQuery(ProcessStartDataQueryModel queryRequest) {

        ProcessStartDataQuery query = new ProcessStartDataQuery();
        query.setSubjectGid(queryRequest.getSubjectGid());
        query.setOwnerGid(queryRequest.getOwnerGid());
        query.setDataSourceChannel(queryRequest.getDataSourceChannel());
        query.setStatusList(queryRequest.getStatusList());
        query.setTypeList(Arrays.asList(ProcessStartDataTypeEnum.DATA_SOURCE_FLOW_TEMPLATE.getCode()));
        query.setProcessTitle(queryRequest.getProcessTitle());
        query.setPageNum(queryRequest.getPageNum());
        query.setPageSize(queryRequest.getPageSize());
        query.setOrderByClause("order by create_time desc");
        int count = processStartDataManager.countByQuery(query);
        List<ProcessStartDataModel> list = processStartDataManager.listByQuery(query);

        ProcessStartDataQueryResult result = new ProcessStartDataQueryResult();
        result.setList(convert(list));
        result.setTotal(count);
        return result;
    }

    @Override
    public ProcessStartDataQueryResult processHasStartedDataListByQuery(ProcessStartedDataQueryModel queryRequest) {
        List<String> processIds = queryRequest.getProcessIds();
        List<ProcessStartDataModel> list = processStartDataManager.listByQueryByProcessIds(processIds);
        ProcessStartDataQueryResult result = new ProcessStartDataQueryResult();
        result.setList(convert(list));
        result.setTotal(list.size());
        return result;
    }


    public static ProcessStartDataDTO convert(ProcessStartDataModel before) {
        if (null == before) {
            return null;
        }
        ProcessStartDataDTO after = new ProcessStartDataDTO();
        after.setDataId(before.getDataId());
        after.setCreateTime(before.getCreateTime());
        after.setUpdateTime(before.getUpdateTime());
        after.setDeleted(before.getDeleted());
        after.setSubjectOid(before.getSubjectOid());
        after.setSubjectGid(before.getSubjectGid());
        after.setOwnerOid(before.getOwnerOid());
        after.setOwnerGid(before.getOwnerGid());
        after.setOuterDataId(before.getOuterDataId());
        after.setFlowTemplateId(before.getFlowTemplateId());
        after.setDataSourceId(before.getDataSourceId());
        after.setDataSourceChannel(before.getDataSourceChannel());
        after.setDataSourceName(before.getDataSourceName());
        after.setStatus(before.getStatus());
        after.setProcessTitle(before.getProcessTitle());
        after.setProcessId(before.getProcessId());
        after.setCanStart(ProcessStartDataStatusEnum.CAN_START_STATUS.contains(before.getStatus()));
        if (before.getExtra() != null) {
            after.setDataCreatorOid(before.getExtra().getDataCreatorOid());
            after.setInitAutoStart(before.getExtra().getInitAutoStart());
            if (CollectionUtils.isNotEmpty(before.getExtra().getFailureReasons())) {
                List<ProcessStartDataDTO.Failure> failures = before.getExtra().getFailureReasons().stream().map(elm -> {
                    ProcessStartDataDTO.Failure failure = new ProcessStartDataDTO.Failure();
                    failure.setReason(elm.getReason());
                    failure.setDetail(elm.getDetail());
                    return failure;
                }).collect(Collectors.toList());
                after.setFailureReasons(failures);
            }
            after.setInfoCollectStartTime(DateUtil.long2Date(before.getExtra().getInfoCollectStartTime()));
            after.setInfoCollectEndTime(DateUtil.long2Date(before.getExtra().getInfoCollectEndTime()));
        }
        return after;
    }


    public static List<ProcessStartDataDTO> convert(List<ProcessStartDataModel> beforeList) {
        return Optional.ofNullable(beforeList)
                .orElse(new ArrayList<>()).stream()
                .map(elm -> convert(elm)).collect(Collectors.toList());
    }

}

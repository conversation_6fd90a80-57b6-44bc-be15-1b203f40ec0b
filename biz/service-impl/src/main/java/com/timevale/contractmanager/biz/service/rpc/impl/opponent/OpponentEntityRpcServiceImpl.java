package com.timevale.contractmanager.biz.service.rpc.impl.opponent;

import org.springframework.beans.factory.annotation.Autowired;

import com.timevale.contractmanager.common.service.api.opponent.OpponentEntityRpcService;
import com.timevale.contractmanager.common.service.model.opponent.OpponentIndividualListModel;
import com.timevale.contractmanager.common.service.model.opponent.OpponentOrganizationListModel;
import com.timevale.contractmanager.common.service.result.opponent.OpponentIndividualListResult;
import com.timevale.contractmanager.common.service.result.opponent.OpponentOrganizationListResult;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationListRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationListResponse;
import com.timevale.contractmanager.core.service.component.opponent.OpponentEntityConverter;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityService;
import com.timevale.mandarin.common.annotation.RestService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestService
public class OpponentEntityRpcServiceImpl implements OpponentEntityRpcService {
    @Autowired
    private OpponentEntityService opponentEntityService;

    @Override
    public OpponentIndividualListResult getTenantSpaceOpponentIndividuals(OpponentIndividualListModel request) {
        OpponentIndividualListRequest listRequest = OpponentEntityConverter
                .convert2OpponentIndividualListRequest(request);
        OpponentIndividualListResponse response = opponentEntityService.listIndividuals(request.getOperatorOid(),
                request.getTenantOid(), request.getOpponentOrganizationId(), listRequest, false);
        return OpponentEntityConverter.convert2OpponentIndividualListResult(response);
    }

    @Override
    public OpponentOrganizationListResult getTenantSpaceOpponentOrganizations(OpponentOrganizationListModel request) {
        OpponentOrganizationListRequest listRequest = OpponentEntityConverter
                .convert2OpponentOrganizationListRequest(request);
        OpponentOrganizationListResponse response = opponentEntityService.listOrganizations(request.getOperatorOid(),
                request.getTenantOid(), listRequest, false);
        return OpponentEntityConverter.convert2OpponentOrganizationListResult(response);
    }

}

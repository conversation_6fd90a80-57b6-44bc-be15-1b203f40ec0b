package com.timevale.contractmanager.biz.service.rest.opponent.detection;

import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentPrivilegeEnum;
import com.timevale.contractmanager.core.model.dto.request.opponent.detection.*;
import com.timevale.contractmanager.core.model.dto.response.opponent.detection.*;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.opponent.OpponentDetectionService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;


/**
 * @Author:jianyang
 * @since 2021-08-04 18:01
 */
@Api(tags = "相对方检测")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class OpponentEntityDetectionRest {

	@Autowired private OpponentDetectionService opponentDetectionService;
	
	@Autowired private UserCenterService userCenterService;

	@UserPrivilegeCheck(
			resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
			privilegeKey = OpponentPrivilegeEnum.PRIVILEGE_RESOURCE_OPPONENT_DETECTION_SETTING)
	@RestMapping(path = "/opponent/detection/tenants/tenantId/setting",method = RequestMethod.POST)
	@ApiOperation(value = "保存相对方检测设置",httpMethod = "POST")
	public BaseResult<Void> saveSetting(@RequestBody OpponentDetectionSaveSettingRequest request){
		String tenantOid = RequestContextExtUtils.getTenantId();
		opponentDetectionService.saveSetting(tenantOid, request);
		return BaseResult.success();
	}

	@RestMapping(path = "/opponent/detection/tenants/tenantId/setting", method = RequestMethod.GET)
	@ApiOperation(value = "获取相对方检测配置", httpMethod = "GET")
	public BaseResult<OpponentDetectionSettingResponse> getSetting(){
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentDetectionService.getSetting(tenantOid));
	}


	@RestMapping(path = "/opponent/detection/tenants/tenantId/batch", method = RequestMethod.POST)
	@ApiOperation(value = "开始批量检测企业", httpMethod = "POST")
	public BaseResult<Integer> batchDetection(@RequestBody OpponentDetectioBatchRequest request){
		String tenantOid = RequestContextExtUtils.getTenantId();
		String operatorOid = RequestContextExtUtils.getOperatorId();
		return BaseResult.success(opponentDetectionService.batchDetection(tenantOid, request, operatorOid));
	}

	@RestMapping(path = "/opponent/detection/tenants/tenantId/tasks/{taskId}", method = RequestMethod.GET)
	@ApiOperation(value = "获取任务状态", httpMethod = "GET")
	public BaseResult<OpponentDetectioTaskResponse> getTaskStatus(
			@PathVariable @ApiParam(value = "检测任务id") String taskId){
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentDetectionService.getTaskStatus(tenantOid, taskId));
	}

	@RestMapping(path = "/opponent/detection/tenants/tenantId/reports", method = RequestMethod.GET)
	@ApiOperation(value = "获取检测报告列表", httpMethod = "GET")
	@MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
	@MultilingualTranslateMethod
	public BaseResult<OpponentDetectioTaskReportListResponse> getDetectionReport(
			@ModelAttribute OpponentDetectionReportRequest request){
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentDetectionService.getDetectionReport(tenantOid, request));
	}

	@RestMapping(path = "/opponent/detection/tenants/tenantId/tasks/{taskId}/exportTasks", method = RequestMethod.GET)
	@ApiOperation(value = "导出检测报告", httpMethod = "GET")
	public BaseResult<OpponentDetectionTaskReportExportResponse> exportTaskReport(
			@PathVariable @ApiParam(value = "检测任务id") String taskId,
			@ModelAttribute OpponentDetectionProblemExportRequest request) {
		String tenantOid = RequestContextExtUtils.getTenantId();
		String operatorOid = RequestContextExtUtils.getOperatorId();
		UserAccountDetail tenantAccount = userCenterService.getUserAccountDetailByOid(tenantOid);
		UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorOid);


		try{
			return BaseResult.success(opponentDetectionService.exportTaskReports(tenantAccount, operatorAccount,taskId, request.getOrgName(),
														request.getRiskLevel(), request.getProblemLevel(),true,
														request.getStartDate(), request.getEndDate()));
		}catch (Exception e){
			log.warn("fail to export detection task:{} ", taskId, e);
		}
		return BaseResult.success();
	}

	@RestMapping(path = "/opponent/detection/tenants/tenantId/tasks", method = RequestMethod.GET)
	@ApiOperation(value = "获取检测任务列表", httpMethod = "GET")
	public BaseResult<OpponentDetectioTaskListResponse> getDetectionTasks(
			@ModelAttribute DetectionTaskRequest request){
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentDetectionService.getDetectionTasks(
				request.getPageSize(), request.getPageNum(), tenantOid, request.getStartDate(), request.getEndDate()));
	}

	@RestMapping(path = "/opponent/detection/tenants/tenantId/stop", method = RequestMethod.GET)
	@ApiOperation(value = "停止检测任务", httpMethod = "GET")
	public BaseResult<Void> stopDetection(
			@ApiParam(value = "检测任务id", required = true) @RequestParam String detectionTaskId){
		String tenantOid = RequestContextExtUtils.getTenantId();
		opponentDetectionService.stopTask(tenantOid, detectionTaskId);
		return BaseResult.success();
	}

	@RestMapping(path = "/opponent/detection/tenants/tenantId/info", method = RequestMethod.GET)
	@ApiOperation(value = "获取任务信息", httpMethod = "GET")
	public BaseResult<OpponentDetectioInitDataResponse> getOpponentDetectionTaskInfo(
			@ApiParam(value = "检测任务类型:1:批量检测，2:新增检测", required = true) @RequestParam Integer taskType){
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentDetectionService.getOpponentDetectionTaskInfo(tenantOid,taskType));
	}


	@RestMapping(path = "/opponent/detection/tenants/tenantId/enterpriseData", method = RequestMethod.GET)
	@ApiOperation(value = "获取工商信息", httpMethod = "GET")
	public BaseResult<OpponentEnterpriseInfoResponse> getInfo(
			@ApiParam(value = "企业名称") @RequestParam String orgName,
			@ApiParam(value = "统一社会信用代码") @RequestParam String socialCreditNo){
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentDetectionService.getEnterpriseData(orgName, tenantOid, socialCreditNo));
	}
}

package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.core.model.dto.response.CheckOtherSignerSignedResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessOperationStrategyResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessSubjectExperienceResponse;
import com.timevale.contractmanager.core.service.process.ProcessCommonService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_OPEN_OPERATOR_ID;

/**
 * 合同流程通用rest接口
 *
 * <AUTHOR> on 2022/09/05
 */
@Api(tags = "合同流程通用rest接口")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class ProcessCommonRest extends BaseRest {

    @Autowired private ProcessCommonService processCommonService;

    /**
     * 校验指定主体是否已发起过流程
     *
     * @param subjectId
     * @return
     */
    @RestMapping(path = "/processes/check-subject-initiated", method = RequestMethod.GET)
    @ApiOperation(value = "校验指定主体是否已发起过流程")
    public BaseResult<Boolean> checkSubjectInitiated(@RequestParam String subjectId) {
        return BaseResult.success(processCommonService.checkSubjectInitiated(subjectId));
    }

    /** 查询流程运营策略信息 */
    @RestMapping(path = "/processes/{processId}/operation-strategy", method = RequestMethod.GET)
    @ApiOperation(value = "查询流程运营策略信息", httpMethod = "GET")
    public BaseResult<ProcessOperationStrategyResponse> processOperationStrategy(
            @ApiParam(value = "流程id", required = true)@PathVariable String processId) {
        ProcessOperationStrategyResponse response =
                processCommonService.processOperationStrategy(
                        processId, RequestContextExtUtils.getOperatorId());
        return BaseResult.success(response);
    }

    /** 校验流程是否存在其他已签签署人 */
    @RestMapping(path = "/processes/{processId}/other-signer-signed", method = RequestMethod.GET)
    @ApiOperation(value = "校验流程是否存在其他已签签署人")
    public BaseResult<CheckOtherSignerSignedResponse> checkOtherSignerSigned(
            @ApiParam(value = "流程id", required = true) @PathVariable String processId,
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId) {
        return BaseResult.success(
                processCommonService.checkProcessOtherSignerSigned(processId, accountId));
    }
}

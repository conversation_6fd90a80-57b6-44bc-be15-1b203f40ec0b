package com.timevale.contractmanager.biz.service.rest;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.core.model.dto.response.integrated.IntegratedInformationResponse;
import com.timevale.contractmanager.core.service.integrated.IntegratedInformationService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;

/**
 * 融合信息接口
 *
 * <AUTHOR>
 * @since 2020-10-29 17:29
 */
@Api(tags = "App融合信息")
@ExternalService
@RestMapping(path = "/v2")
public class IntegratedInformationRest {

    @Autowired private IntegratedInformationService integratedInformationService;

    /**
     * 首页综合信息获取（包含最近流程信息，统计信息和套餐信息）
     *
     * @return 首页信息
     */
    @ApiOperation(value = "首页综合信息获取", httpMethod = "GET")
    @RestMapping(path = "integrated/information", method = RequestMethod.GET)
    public BaseResult<IntegratedInformationResponse> integratedInformation(
            @ApiParam(value = "pageSize", required = true)
                    @Valid
                    @Max(100)
                    @RequestParam(name = "pageSize")
                    Integer pageSize,
            @ApiParam(value = "pageNum", required = true)
                    @Valid
                    @Max(10000)
                    @RequestParam(name = "pageNum")
                    Integer pageNum,
            @ApiParam(value = "processStatusList")
                    @RequestParam(name = "processStatusList", required = false)
                    String processStatusList,
            @ApiParam(value = "是否可包含审批状态的流程, 默认不包含", example = "false")
                    @RequestParam(name = "withApproving", required = false)
                    Boolean withApproving,
            @ApiParam(value = "是否统计待我审批数据, 默认不统计", example = "false")
                    @RequestParam(name = "approvingCount", required = false)
                    Boolean approvingCount,
            @ApiParam(value = "docQueryType") @RequestParam(name = "docQueryType", required = false)
                    Integer docQueryType) {
        // 构造前端状态入参
        List<Integer> status = Lists.newArrayList();
        if (StringUtils.isNotEmpty(processStatusList)) {
            String[] split = processStatusList.split(",");
            for (String statusString : split) {
                status.add(Integer.valueOf(statusString));
            }
        } else {
            if (Boolean.TRUE.equals(withApproving)) {
                status = ProcessStatusEnum.getAllProcessStatusList();
            } else {
                status = ProcessStatusEnum.getNoApproveProcessStatusList();
            }
        }
        return BaseResult.success(
                integratedInformationService.getIntegratedInformation(
                        RequestContextExtUtils.getClientId(),
                        pageSize,
                        pageNum,
                        status,
                        docQueryType,
                        approvingCount));
    }
}

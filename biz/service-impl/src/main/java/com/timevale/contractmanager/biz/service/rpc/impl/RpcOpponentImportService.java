package com.timevale.contractmanager.biz.service.rpc.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.contractanalysis.facade.api.dto.common.BatchExportResultDTO;
import com.timevale.contractmanager.common.service.integration.client.FileSystemClient;
import com.timevale.contractmanager.core.service.dto.opponent.OpponentImportResultDTO;
import com.timevale.footstone.base.model.enums.CommonResultEnum;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.mandarin.common.result.BusinessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2024/10/11 17:58
 */
@Slf4j
@RestService
public class RpcOpponentImportService {

    @Autowired
    private FileSystemClient fileSystemClient;

    /**
     * 相对方任务中心回调使用
     */
    public BusinessResult<BatchExportResultDTO> opponentImportTaskCenter(OpponentImportResultDTO request) {
        log.info("opponent task center call req : {}", JSON.toJSONString(request));
        BusinessResult<BatchExportResultDTO> result = new BusinessResult<>(CommonResultEnum.SUCCESS.getCode());
        String url = fileSystemClient.getDownloadUrl(request.getFileKey(), false);
        BatchExportResultDTO resultDTO = new BatchExportResultDTO();
        resultDTO.setExportUrl(url);
        result.setData(resultDTO);
        return result;
    }

}

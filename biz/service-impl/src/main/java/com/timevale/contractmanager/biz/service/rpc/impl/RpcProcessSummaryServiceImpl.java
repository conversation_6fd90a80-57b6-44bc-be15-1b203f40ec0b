package com.timevale.contractmanager.biz.service.rpc.impl;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.api.RpcProcessSummaryService;
import com.timevale.contractmanager.common.service.enums.ProcessSummaryDataTypeEnum;
import com.timevale.contractmanager.common.service.enums.ProcessSummaryOverAllJobStatusEnum;
import com.timevale.contractmanager.common.service.model.processsummary.CreateProcessSummaryModel;
import com.timevale.contractmanager.common.service.model.processsummary.QueryProcessSummaryDetailModel;
import com.timevale.contractmanager.common.service.model.processsummary.QueryProcessSummaryOverAllStatusModel;
import com.timevale.contractmanager.common.service.model.processsummary.RefreshProcessSummaryModel;
import com.timevale.contractmanager.common.service.model.processsummary.UpdateProcessSummaryKeyInfoModel;
import com.timevale.contractmanager.common.service.model.processsummary.UpdateProcessSummaryModel;
import com.timevale.contractmanager.common.service.result.processsummary.ProcessSummaryDetailResult;
import com.timevale.contractmanager.common.service.result.processsummary.ProcessSummaryKeyInfoResult;
import com.timevale.contractmanager.common.service.result.processsummary.ProcessSummaryOverAllStatusResult;
import com.timevale.contractmanager.core.model.bo.processsummary.ProcessSummaryDetailBO;
import com.timevale.contractmanager.core.model.bo.processsummary.ProcessSummaryKeyInfoBO;
import com.timevale.contractmanager.core.model.bo.processsummary.UpdateProcessSummaryBO;
import com.timevale.contractmanager.core.service.processsummary.ProcessSummaryService;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.common.annotation.RestService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同摘要rpc
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@RestService
@Slf4j
public class RpcProcessSummaryServiceImpl implements RpcProcessSummaryService {

    @Autowired private ProcessSummaryService processSummaryService;

    @Override
    public void createProcessSummary(CreateProcessSummaryModel model) {
        processSummaryService.createProcessSummary(
                model.getProcessId(),
                model.getFileId(),
                model.getSubjectId(),
                model.getSubjectGid());
    }

    @Override
    public void createProcessAllFileSummary(CreateProcessSummaryModel model) {
        processSummaryService.createProcessAllFileSummary(
                model.getProcessId(),
                model.getSubjectId(),
                model.getSubjectGid());
    }

    @Override
    public void refreshProcessSummary(RefreshProcessSummaryModel model) {
        processSummaryService.refreshProcessSummary(
                model.getProcessId(),
                model.getFileId(),
                model.getSubjectId(),
                model.getSubjectGid(),
                ProcessSummaryDataTypeEnum.from(model.getDataType()));
    }

    @Override
    public ProcessSummaryDetailResult queryProcessSummaryDetail(
            QueryProcessSummaryDetailModel model) {
        ProcessSummaryDetailBO summaryDetailBO =
                processSummaryService.queryProcessSummary(
                        model.getProcessId(),
                        model.getFileId(),
                        model.getSubjectGid());
        return bo2Result(summaryDetailBO);
    }

    @Override
    public void updateProcessSummary(UpdateProcessSummaryModel model) {
        processSummaryService.updateProcessSummary(model2BO(model));
    }

    @Override
    public ProcessSummaryOverAllStatusResult queryProcessSummaryOverAllStatus(QueryProcessSummaryOverAllStatusModel model) {
        ProcessSummaryOverAllJobStatusEnum status = processSummaryService.queryProcessSummaryOverAllStatus(model.getProcessId(), model.getSubjectGid());
        return new ProcessSummaryOverAllStatusResult(status.getCode());
    }

    private UpdateProcessSummaryBO model2BO(UpdateProcessSummaryModel model) {
        UpdateProcessSummaryBO updateProcessSummaryBO = new UpdateProcessSummaryBO();
        updateProcessSummaryBO.setProcessId(model.getProcessId());
        updateProcessSummaryBO.setFileId(model.getFileId());
        updateProcessSummaryBO.setSummary(model.getSummary());
        updateProcessSummaryBO.setKeyInfo(model2KeyInfoBOs(model.getKeyInfo()));
        updateProcessSummaryBO.setSubjectId(model.getSubjectId());
        updateProcessSummaryBO.setSubjectGid(model.getSubjectGid());
        return updateProcessSummaryBO;
    }


    private List<ProcessSummaryKeyInfoBO> model2KeyInfoBOs(List<UpdateProcessSummaryKeyInfoModel> modelList) {
        if (ListUtils.isEmpty(modelList)) {
            return Lists.newArrayList();
        }
        return modelList.stream().map(this::model2KeyInfoBO).collect(Collectors.toList());
    }

    private ProcessSummaryKeyInfoBO model2KeyInfoBO(UpdateProcessSummaryKeyInfoModel model) {
        ProcessSummaryKeyInfoBO processSummaryKeyInfoBO = new ProcessSummaryKeyInfoBO();
        processSummaryKeyInfoBO.setSeq(model.getSeq());
        processSummaryKeyInfoBO.setName(model.getName());
        processSummaryKeyInfoBO.setValue(model.getValue());
        return processSummaryKeyInfoBO;
    }

    private ProcessSummaryDetailResult bo2Result(ProcessSummaryDetailBO summaryDetailBO) {
        ProcessSummaryDetailResult processSummaryDetailResult = new ProcessSummaryDetailResult();
        processSummaryDetailResult.setStatus(summaryDetailBO.getStatus());
        processSummaryDetailResult.setSummary(summaryDetailBO.getSummary());
        processSummaryDetailResult.setSummaryRefreshCount(summaryDetailBO.getSummaryRefreshCount());
        processSummaryDetailResult.setKeyInfo(keyInfoBOs2Result(summaryDetailBO.getKeyInfo()));
        processSummaryDetailResult.setKeyInfoRefreshCount(summaryDetailBO.getKeyInfoRefreshCount());
        processSummaryDetailResult.setSummaryLastJobSuccess(summaryDetailBO.getSummaryLastJobSuccess());
        processSummaryDetailResult.setKeyInfoLastJobSuccess(summaryDetailBO.getKeyInfoLastJobSuccess());
        processSummaryDetailResult.setCategoryId(summaryDetailBO.getCategoryId());
        processSummaryDetailResult.setCategoryName(summaryDetailBO.getCategoryName());
        return processSummaryDetailResult;
    }

    private List<ProcessSummaryKeyInfoResult> keyInfoBOs2Result(List<ProcessSummaryKeyInfoBO> keyInfoBOs) {
        if (ListUtils.isEmpty(keyInfoBOs)) {
            return Lists.newArrayList();
        }
        return keyInfoBOs.stream().map(this::keyInfoBO2Result).collect(Collectors.toList());
    }

    private ProcessSummaryKeyInfoResult keyInfoBO2Result(ProcessSummaryKeyInfoBO keyInfoBO) {
        ProcessSummaryKeyInfoResult processSummaryKeyInfoResult = new ProcessSummaryKeyInfoResult();
        processSummaryKeyInfoResult.setSeq(keyInfoBO.getSeq());
        processSummaryKeyInfoResult.setName(keyInfoBO.getName());
        processSummaryKeyInfoResult.setValue(keyInfoBO.getValue());
        processSummaryKeyInfoResult.setAiValue(keyInfoBO.getAiValue());
        return processSummaryKeyInfoResult;
    }
}

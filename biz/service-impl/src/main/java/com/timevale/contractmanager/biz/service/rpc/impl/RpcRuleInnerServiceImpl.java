package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.api.RpcRuleInnerService;
import com.timevale.contractmanager.common.service.bean.rule.RuleConditionSysConfigBean;
import com.timevale.contractmanager.common.service.bean.rule.RuleConditionTreeDTO;
import com.timevale.contractmanager.common.service.model.rule.RuleConditionSysConfigQueryModel;
import com.timevale.contractmanager.common.service.result.rule.RuleConditionSysConfigResult;
import com.timevale.contractmanager.core.service.rule.RuleConditionService;
import com.timevale.mandarin.common.annotation.RestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * RpcRuleInnerServiceImpl
 *
 * <AUTHOR>
 * @since 2023/8/9 5:39 下午
 */
@RestService
@Slf4j
public class RpcRuleInnerServiceImpl implements RpcRuleInnerService {

    @Autowired
    private RuleConditionService ruleConditionService;

    @Override
    public RuleConditionSysConfigResult listRuleConditionSysConfig() {
        List<RuleConditionSysConfigBean> sysConfigBeans = ruleConditionService.querySysConfigList();
        RuleConditionSysConfigResult result = new RuleConditionSysConfigResult();
        result.setList(sysConfigBeans);
        return result;
    }

    @Override
    public RuleConditionSysConfigResult listRuleConditionSysConfigByType(RuleConditionSysConfigQueryModel model) {
        List<RuleConditionSysConfigBean> sysConfigBeans = ruleConditionService.querySysConfigListByType(model.getBizType());
        RuleConditionSysConfigResult result = new RuleConditionSysConfigResult();
        result.setList(sysConfigBeans);
        return result;
    }

    @Override
    public List<RuleConditionTreeDTO> listByRuleId(String tenantGid, String ruleId) {
        return ruleConditionService.listByRuleId(tenantGid, ruleId);
    }
}

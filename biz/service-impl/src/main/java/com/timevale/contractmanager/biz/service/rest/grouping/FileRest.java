package com.timevale.contractmanager.biz.service.rest.grouping;

import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.dto.request.grouping.file.*;
import com.timevale.contractmanager.core.model.dto.response.grouping.file.*;
import com.timevale.contractmanager.core.service.grouping.GroupingFileService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 合同文件操作 rest
 *
 * <p>1.归档
 *
 * <p>2.更新归档文件信息
 *
 * <p>3.移动归档文件
 *
 * <p>4.移出归档文件
 *
 * @author: xuanzhu
 * @since: 2019-09-08 22:15
 */
@Api(tags = "合同归档-合同文件相关操作")
@ExternalService
@RestMapping(path = "/v2")
public class FileRest {

    @Autowired private GroupingFileService groupingFileService;

    private String getTenantId() {
        return RequestContextExtUtils.getTenantId();
    }

    /**
     * 合同文件归档
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_files", method = RequestMethod.POST)
    @ApiOperation(value = "合同归档", httpMethod = "POST")
    public BaseResult<Boolean> add(@RequestBody AddGroupingFileRequest request) {
        return BaseResult.success(groupingFileService.add(getTenantId(),request));
    }

    /**
     * 更新已归档的文件信息
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_files/{processId}", method = RequestMethod.PUT)
    @ApiOperation(value = "更新合同信息", httpMethod = "PUT")
    @Deprecated
    public BaseResult<Boolean> modify(
            @PathVariable @ApiParam(value = "流程id", required = true) String processId,
            @RequestBody ModifyGroupingFileRequest request) {
        return BaseResult.success(
                groupingFileService.modify(
                        getTenantId(), processId, RequestContext.getAppId(), request));
    }

    /**
     * 移动合同文件到其他目录下
     *clientId:DING_TALK_LABOR在调用
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_files/{processId}/move", method = RequestMethod.POST)
    @ApiOperation(value = "移动合同到其他目录", httpMethod = "POST")
    public BaseResult<Boolean> move(
            @PathVariable @ApiParam(value = "需要移动的文件id,即processId", required = true)
                    String processId,
            @RequestBody MoveGroupingFileRequest request) {
        return BaseResult.success(
                groupingFileService.moveArchive(getTenantId(), Arrays.asList(processId), request.getSourceMenuId(),
                                                request.getTargetMenuIdList(), request.getAccountId()));
    }

    /**
     * 从文件目录下移出合同文件
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_files/{processId}/remove", method = RequestMethod.POST)
    @ApiOperation(value = "移出合同", httpMethod = "POST")
    @Deprecated
    public BaseResult<Boolean> remove(
            @PathVariable @ApiParam(value = "需要移出的文件id,即processId", required = true)
                    String processId,
            @RequestBody ReMoveGroupingFileRequest request) {
        ReMoveGroupingFileRequest reMoveRequest = new ReMoveGroupingFileRequest();
        reMoveRequest.setTargetMenuId(request.getTargetMenuId());
        reMoveRequest.setAccountId(request.getAccountId());
        return BaseResult.success(
                groupingFileService.reMoveArchive(getTenantId(), Arrays.asList(processId),
                                                    reMoveRequest.getTargetMenuId(),request.getAccountId()));
    }

    /**
     * 合同文件归档
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_files/grouping", method = RequestMethod.POST)
    @ApiOperation(value = "智能台账2.0-流程归档", httpMethod = "POST")
    public BaseResult<GroupingFileResponse> grouping(@RequestBody GroupingFileRequest request) {
        GroupingFileResponse response = new GroupingFileResponse();

        try {
            groupingFileService.grouping(request,false);
        }catch (BizContractManagerException e) {
            if (BizContractManagerResultCodeEnum.PROCESS_GROUPING_STATUS_ERROR.getCode().equals(e.getCode())){
                response.setResult(false);
                response.setBizMessage(BizContractManagerResultCodeEnum.PROCESS_GROUPING_STATUS_ERROR.getMessage());
                return BaseResult.success(response);
            }
            throw e;
        }

        response.setResult(true);
        return BaseResult.success(response);
    }

    @FunctionPrivilegeCheck(
            function = FunctionCodeConstants.EASY_SHUTTLE,
            accountId = "request.tenantId",
            batchSize = "request.processIds")
    @RestMapping(path = "/grouping_files/oneClick/number", method = RequestMethod.GET)
    @ApiOperation(value = "获取可一键归档的流程数量", httpMethod = "GET")
    public BaseResult<OneClickGroupingFileNumberResponse> getGroupingProcessNum(@ModelAttribute OneClickGroupFileRequest request){
        return BaseResult.success(groupingFileService.getOneClickGroupingProcessNum(request));
    }

    @FunctionPrivilegeCheck(
            function = FunctionCodeConstants.EASY_SHUTTLE,
            accountId = "request.subjectId",
            batchSize = "request.processIds")
    @RestMapping(path = "/grouping_files/OneClick/grouping", method = RequestMethod.POST)
    @ApiOperation(value = "一键归档", httpMethod = "POST")
    public BaseResult<OneClickGroupFileResponse> OneClickGroupFile(@RequestBody OneClickGroupFileRequest request){
        return BaseResult.success(groupingFileService.OneClickGroupFile(request, request.getToMenuId()));
    }
    /**
     * 移动合同文件到其他目录下
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_files/{processId}/moveMenu", method = RequestMethod.POST)
    @ApiOperation(value = "智能台账2.0-流程移动分类", httpMethod = "POST")
    public BaseResult<Boolean> moveMenu(
            @PathVariable @ApiParam(value = "需要移动的文件id,即processId", required = true)
                    String processId,
            @RequestBody MoveGroupingFileRequest request) {
        return BaseResult.success(
                groupingFileService.moveArchive(getTenantId(), Arrays.asList(processId), request.getSourceMenuId(),
                                                request.getTargetMenuIdList(), request.getAccountId()));
    }

    /**
     * 批量移动合同文件到其他目录下
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_files/processes/moveMenu", method = RequestMethod.POST)
    @ApiOperation(value = "批量移动合同到其他目录", httpMethod = "POST")
    public BaseResult<Boolean> batchMoveMenu(
            @RequestBody MoveGroupingFileRequestV2 request) {
        return BaseResult.success(
                groupingFileService.moveArchiveNew(getTenantId(), request.getProcessIds(), request.getSourceMenuId(),
                                                request.getTargetMenuIdList(), RequestContextExtUtils.getOperatorId(), request.getMenuIdList()));
    }

    /**
     * 批量从文件目录下移出合同文件
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_files/processes/removeMenu", method = RequestMethod.POST)
    @ApiOperation(value = "智能台账2.0-流程移出分类", httpMethod = "POST")
    public BaseResult<Boolean> batchRemoveMenu(
            @RequestBody ReMoveGroupingFileRequestV2 request) {
        return BaseResult.success(
                groupingFileService.reMoveArchiveNew(getTenantId(), request.getProcessIds(),
                                                    request.getTargetMenuId(), RequestContextExtUtils.getOperatorId(), request.getMenuIdList()));
    }
}

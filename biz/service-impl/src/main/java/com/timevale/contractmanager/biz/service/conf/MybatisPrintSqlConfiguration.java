package com.timevale.contractmanager.biz.service.conf;

import com.timevale.saas.common.base.constant.EnvConstants;
import com.timevale.saas.common.base.utils.EnvUtils;
import com.timevale.saas.common.middleware.mybatis.PrintSqlInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * Created by tianlei on 2022/2/19
 */
@Configuration
public class MybatisPrintSqlConfiguration {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Value("${env}")
    private String env;

    @PostConstruct
    public void setPrintSql() {
        // 设置打印sql插件
        if (EnvUtils.startFromLocal(MybatisPrintSqlConfiguration.class) &&
                (EnvConstants.LOCAL.equals(env) || EnvConstants.TEST.equals(env))) {
            sqlSessionFactory.getConfiguration().addInterceptor(new PrintSqlInterceptor());
        }
    }
}

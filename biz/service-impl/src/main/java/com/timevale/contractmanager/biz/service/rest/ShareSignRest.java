package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.core.model.dto.request.sharesign.*;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartDetailResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResponse;
import com.timevale.contractmanager.core.model.dto.response.sharesign.*;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.core.service.process.bean.FlowTemplateDetailParseConfig;
import com.timevale.contractmanager.core.service.process.door.ProcessDoor;
import com.timevale.contractmanager.core.service.processstart.handler.ProcessStartAdapter;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContextService;
import com.timevale.contractmanager.core.service.sharesign.ShareSignService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import com.timevale.tlcache.cache.ThreadLocalCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2021-02-04
 */
@Api(tags = "扫码签管理")
@ExternalService
@RestMapping(path = "/v2/shareSign")
public class ShareSignRest extends BaseProcessRest {

    @Autowired ShareSignService shareSignService;
    @Autowired ProcessDoor processDoor;

    @Autowired private ProcessStartContextService processStartContextService;

    @Autowired private FlowTemplateService flowTemplateService;

    @Autowired private UserCenterService userCenterService;

    @ApiOperation(value = "根据任务id获取扫码签任务详情", httpMethod = "GET")
    @RestMapping(path = "/getTaskInfo", method = RequestMethod.GET)
    public BaseResult<ShareSignTaskInfoResponse> getTaskInfo(
            @RequestParam("shareSignTaskId") String shareSignTaskId,
            @RequestParam(value = "token", required = false) String token,
            @RequestParam(value = "withProgress", defaultValue = "false") Boolean withProgress) {
        return BaseResult.success(shareSignService.getTaskInfo(shareSignTaskId, token, withProgress));
    }

    @ApiOperation(value = "根据scanId获取扫码签任务详情", httpMethod = "GET")
    @RestMapping(path = "/getTaskInfoByScanId", method = RequestMethod.GET)
    public BaseResult<ShareSignScanInfoResponse> getTaskInfoByScanId(
            @RequestParam("shareScanId") String shareScanId) {
        return BaseResult.success(shareSignService.getTaskInfoByUrlId(shareScanId));
    }

    @ApiOperation(value = "获取扫码签任务分享地址", httpMethod = "GET")
    @RestMapping(path = "/getTaskUrl", method = RequestMethod.GET)
    public BaseResult<ShareSignTaskUrlResponse> getTaskUrl(
            @RequestParam("shareSignTaskId") String shareSignTaskId,
            @RequestParam("accountId") String accountId,
            @RequestParam(value = "participantId", required = false) String participantId) {
        return BaseResult.success(shareSignService.getTaskUrl(shareSignTaskId, participantId, accountId));
    }

    @ApiOperation(value = "获取扫码签任务列表", httpMethod = "GET")
    @RestMapping(path = "/getTaskList", method = RequestMethod.GET)
    public BaseResult<ShareSignTaskListResponse> getTaskList(
            @URIQueryParam ShareSignTaskListRequest request) {
        return BaseResult.success(shareSignService.getTaskList(request));
    }

    @ApiOperation(value = "获取扫码签任务流程列表", httpMethod = "GET")
    @MultilingualTranslateMethod
    @RestMapping(path = "/getProcessList", method = RequestMethod.GET)
    public BaseResult<ShareSignProcessListResponse> getProcessList(
            @URIQueryParam QueryTaskProcessListRequest request) {
        return BaseResult.success(shareSignService.getProcessList(request));
    }

    @ApiOperation(value = "开启/关闭扫码签任务", httpMethod = "POST")
    @RestMapping(path = "/updateTaskStatus", method = RequestMethod.POST)
    public BaseResult<?> updateTaskStatus(@RequestBody ShareSignTaskStatusRequest request) {
        shareSignService.updateTaskStatus(request);
        return BaseResult.success();
    }


    @ApiOperation(value = "删除扫码签任务", httpMethod = "POST")
    @RestMapping(path = "/delete-task", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<?> deleteTask(@RequestBody DeleteShareSignTaskRequest request) {
        request.setOperatorId(RequestContextExtUtils.getOperatorId());
        shareSignService.deleteTask(request);
        return BaseResult.success();
    }


    @ApiOperation(value = "更新扫码签任务信息", httpMethod = "POST")
    @RestMapping(path = "/updateTaskInfo", method = RequestMethod.POST)
    public BaseResult<?> updateTaskInfo(@RequestBody ShareSignTaskInfoRequest request) {
        shareSignService.updateTaskInfo(
                request,
                RequestContextExtUtils.getOperatorId(),
                RequestContextExtUtils.getTenantId());
        return BaseResult.success();
    }

    @ApiOperation(value = "发起扫码签任务", httpMethod = "POST")
    @RestMapping(path = "/startTask", method = RequestMethod.POST)
    public BaseResult<ShareSignTaskUrlResponse> startTask(
            @RequestBody ShareSignTaskStartRequest request) {
        checkHasSignFile(request.getScene(), request.getFiles());
        checkTaskName(request.getTaskName());
        transOperatorIdToHeader(request.getInitiatorAccountId());
        request.checkParams();
        request.setClientId(RequestContextExtUtils.getClientId());
        request.setSubjectOid(RequestContextExtUtils.getTenantId());
        request.setOperatorOid(RequestContextExtUtils.getOperatorId());
        request.setAppId(RequestContextExtUtils.getAppId());
        request.setAppName(RequestContextExtUtils.getAppName());
        if (ProcessStartScene.DIRECT_START.getScene() == request.getScene()
                && request.getShareSignDirectStartByFlowTemplateId() != Boolean.TRUE) {
            request.setEpaasTag(Boolean.FALSE);
        }
        return BaseResult.success(shareSignService.startTask(request));
    }

    @ApiOperation(value = "发起扫码签任务", httpMethod = "POST")
    @RestMapping(path = "/start-task-by-template", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ShareSignTaskUrlResponse> startTaskByTemplate(
            @RequestBody ShareSignTaskStartByTemplateRequest request) {

        UserAccountDetail tenant = userCenterService.getUserAccountDetailByOid(RequestContextExtUtils.getTenantId());
        GetFlowTemplateResult flowTemplateResult =
                processStartContextService.getFlowTemplateResult(
                        request.getFlowTemplateId(), tenant);
        FlowTemplateDetailParseConfig config = new FlowTemplateDetailParseConfig();
        config.setSubject(tenant);
        config.setWithDocSource(false);
        config.setWithFileCategory(true);
        ProcessStartDetailResponse response =
                flowTemplateService.parseFlowTemplateDetail(flowTemplateResult, config);
        ShareSignTaskStartRequest shareSignTaskStartRequest =
                ProcessStartAdapter.buildShareSignTaskStartRequest(response);
        shareSignTaskStartRequest.setInitiatorAccountId(RequestContextExtUtils.getOperatorId());
        shareSignTaskStartRequest.setScene(request.getScene());
        checkHasSignFile(shareSignTaskStartRequest.getScene(), shareSignTaskStartRequest.getFiles());
        checkTaskName(shareSignTaskStartRequest.getTaskName());
        transOperatorIdToHeader(shareSignTaskStartRequest.getInitiatorAccountId());
        shareSignTaskStartRequest.checkParams();
        shareSignTaskStartRequest.setClientId(RequestContextExtUtils.getClientId());
        shareSignTaskStartRequest.setSubjectOid(RequestContextExtUtils.getTenantId());
        shareSignTaskStartRequest.setOperatorOid(RequestContextExtUtils.getOperatorId());
        shareSignTaskStartRequest.setAppId(RequestContextExtUtils.getAppId());
        shareSignTaskStartRequest.setAppName(RequestContextExtUtils.getAppName());
        shareSignTaskStartRequest.setShareSignDirectStartByFlowTemplateId(Boolean.TRUE);
        shareSignTaskStartRequest.setNeedSealAuthCheck(request.isNeedSealAuthCheck());
        return BaseResult.success(shareSignService.startTask(shareSignTaskStartRequest));
    }

    /**
     * 通过流程模板id发起 在控件设置页面 直接发起会进入控件设置页面设置 签署区控件，完了后直接调用此接口发起
     *
     * @param request 发起入参
     */
    @ThreadLocalCache
    @ApiOperation(value = "参与扫码签任务发起流程", httpMethod = "POST")
    @RestMapping(path = "/startProcess", method = RequestMethod.POST)
    public BaseResult<ProcessStartResponse> startProcess(
            @RequestBody ShareSignProcessStartRequest request) {
        return BaseResult.success(processDoor.shareSignStartProcess(request));
    }

    /**
     * 通过流程模板id发起 在控件设置页面 直接发起会进入控件设置页面设置 签署区控件，完了后直接调用此接口发起
     * 异步接口
     * @param request 发起入参
     */
    @ApiOperation(value = "参与扫码签任务发起流程", httpMethod = "POST")
    @RestMapping(path = "/start-process-async", method = RequestMethod.POST)
    public BaseResult<ProcessStartResponse> startProcessAsync(
            @RequestBody ShareSignProcessStartRequest request) {
        request.setAsyncStart(true);
        return BaseResult.success(processDoor.shareSignStartProcess(request));
    }


    /**
     * 获取扫码签任务管理地址
     *
     * @param request 入参
     */
    @ApiOperation(value = "获取扫码签任务管理地址", httpMethod = "GET")
    @RestMapping(path = "/taskManageUrl", method = RequestMethod.GET)
    public BaseResult<ShareSignTaskManageUrlResponse> taskManageUrl(
            @ModelAttribute ShareSignTaskManageUrlRequest request) {
        return BaseResult.success(shareSignService.getTaskManageUrl(request));
    }
    /**
     * 流程发起详情信息
     *
     * @param flowTemplateId 流程模板id
     * @return 流程发起详情信息
     */
    @ApiOperation(value = "判断模板是否支持扫码签", httpMethod = "GET")
    @RestMapping(path = "/{flowTemplateId}/support-scan", method = RequestMethod.GET)
    public BaseResult<ShareSignSupportScanResponse> supportScan(@PathVariable String flowTemplateId) {
        return BaseResult.success(shareSignService.supportScan(flowTemplateId));
    }

    /**
     * 获取进行中任务数量
     */
    @ApiOperation(value = "获取进行中任务列表", httpMethod = "GET")
    @RestMapping(path = "/in-process-list", method = RequestMethod.GET)
    public BaseResult<ShareSignTaskListResponse> inProcessList(@URIQueryParam  ShareSignTaskInProcessListRequest request) {
        return BaseResult.success(shareSignService.inProcessList(request));
    }

    /**
     * 获取进行中任务数量
     */
    @ApiOperation(value = "获取进行中任务数量", httpMethod = "GET")
    @RestMapping(path = "/in-process-num", method = RequestMethod.GET)
    public BaseResult<ShareSignInProcessResponse> inProcessNum() {
        return BaseResult.success(shareSignService.inProcessNum());
    }

    /** 获取任务数量 */
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取任务数量", httpMethod = "GET")
    @RestMapping(path = "/task/count", method = RequestMethod.GET)
    public BaseResult<ShareSignTaskCountResponse> countTask() {
        return BaseResult.success(
                shareSignService.countTask(
                        RequestContextExtUtils.getOperatorId(),
                        RequestContextExtUtils.getTenantId()));
    }

    /** 开始扫码任务 */
    @ApiOperation(value = "根据模板id发起扫码签任务", httpMethod = "POST")
    @RestMapping(path = "/start-by-flow-template", method = RequestMethod.POST)
    public BaseResult<ShareSignTaskUrlResponse> startTaskByFlowTemplate(
            @RequestBody ShareSignTaskStartFlowTemplateRequest request) {
        request.setClientId(RequestContextExtUtils.getClientId());
        request.setSubjectOid(RequestContextExtUtils.getTenantId());
        request.setOperatorOid(RequestContextExtUtils.getOperatorId());
        request.setAppId(RequestContextExtUtils.getAppId());
        request.setAppName(RequestContextExtUtils.getAppName());
        return BaseResult.success(shareSignService.startTaskByFlowTemplate(request));
    }

    /** 开始扫码任务 */
    @ApiOperation(value = "扫码签详情统计数据", httpMethod = "GET")
    @RestMapping(path = "/{shareSignTaskId}/task-info-count", method = RequestMethod.GET)
    public BaseResult<ShareSignTaskDetailCountResponse> taskInfoCount(
            @PathVariable String shareSignTaskId,
            @RequestParam(value = "subjectId", required = false) String subjectId) {
        return BaseResult.success(shareSignService.taskDetailCount(shareSignTaskId, subjectId));
    }

    @ApiOperation(value = "查询扫码签用户在空间下的加入次数", httpMethod = "POST")
    @RestMapping(path = "/getJoinInfo", method = RequestMethod.POST)
    public BaseResult<ShareSignTaskJoinInfoResponse> queryJoinInfo(
            @RequestBody ShareSignTaskJoinInfoRequest request) {
        String accountId = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(
                shareSignService.taskJoinInfo(
                        request.getTaskId(),
                        accountId,
                        request.getSubjectName()));
    }

    @ApiOperation(value = "查看最近加入的扫码签合同", httpMethod = "POST")
    @RestMapping(path = "/query-join-process", method = RequestMethod.POST)
    public BaseResult<ProcessStartResponse> queryJoinProcess(
            @RequestBody QueryJoinProcessRequest request) {
        String accountId = RequestContextExtUtils.getOperatorId();
        request.setAccountOid(accountId);
        return BaseResult.success(shareSignService.queryJoinedTask(request));
    }
}

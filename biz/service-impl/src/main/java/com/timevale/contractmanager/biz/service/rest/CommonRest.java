package com.timevale.contractmanager.biz.service.rest;

import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.service.enums.CommonConfigEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.dto.response.IdempotentResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessSubjectExperienceResponse;
import com.timevale.contractmanager.core.model.dto.response.common.CommonResponse;
import com.timevale.contractmanager.core.model.dto.response.common.MultiBizGrayResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.common.CommonService;
import com.timevale.contractmanager.core.service.configs.CommonBizConfig;
import com.timevale.contractmanager.core.service.grouping.GroupingFileService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.ProcessCommonService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;


/**
 * Created by qianyi on 2019/7/6.
 */
@Api(tags = "公共能力")
@ExternalService
@RestMapping(path = "/v1")
public class CommonRest extends BaseRest{

    @Autowired private UserCenterService userCenterService;
    @Autowired private GroupingFileService groupingFileService;
    @Autowired private SaasCommonClient saasCommonClient;
    @Autowired private ProcessCommonService processCommonService;
    @Autowired private CommonService commonService;

    @ApiOperation(value = "获取通用配置", httpMethod = "GET")
    @RestMapping(path = "/common/getConfig", method = RequestMethod.GET)
    public BaseResult<CommonResponse> getCommonConfig() {
        CommonResponse response = new CommonResponse();
        Map<String,Object> dataMap = Maps.newHashMapWithExpectedSize(1);
        // 会员购买页地址
        dataMap.put(
                CommonConfigEnum.VIP_BUY_URL.getName(),
                String.format(CommonBizConfig.VIP_BUY_URL, RequestContextExtUtils.getTenantId()));
        // 是否支持新版首页
        dataMap.put(
                CommonConfigEnum.SAAS_V2_ENABLE.getName(), CommonBizConfig.SAAS_V2_ENABLE);
        response.setDataMap(dataMap);
        return BaseResult.success(response);
    }

    @ApiOperation(value = "获取当前企业是否拥有已归档合同、其他来源、合同分享记录的数据", httpMethod = "GET")
    @RestMapping(path = "/common/has/advance/data", method = RequestMethod.GET)
    public BaseResult<Boolean> hasAdvancedData() {
        String tenantId = RequestContextExtUtils.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            throw new BizContractManagerException("tenantId不能为空");
        }
        UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantId);
        if (groupingFileService.hasGroupingInfo(tenantAccount.getAccountGid())) {
            return BaseResult.success(true);
        }
        if (saasCommonClient.hasShareRecordInSubject(tenantAccount.getAccountGid())) {
            return BaseResult.success(true);
        }
        return BaseResult.success(false);
    }


    /**
     * 轮询重复提交的幂等请求
     *
     * @param requestNo
     * @return
     */
    @RestMapping(path = "/common/idempotent/check-result", method = RequestMethod.GET)
    @ApiOperation(value = "轮询重复提交的幂等请求")
    public BaseResult<IdempotentResponse> checkIdemRequestResult(
            @ApiParam(value = "唯一请求号", required = true) @RequestParam String requestNo,
            @ApiParam(value = "操作code", required = true) @RequestParam String operation) {
        return BaseResult.success(processCommonService.checkIdempotentResult(operation, requestNo));
    }


    /** 获取用户实名企业体验签署信息 */
    @RestMapping(path = "/common/query-subject-experience", method = RequestMethod.GET)
    @ApiOperation(value = "获取当前用户实名的企业体验签署信息")
    public BaseResult<ProcessSubjectExperienceResponse> querySubjectExperience() {
        return BaseResult.success(
                commonService.querySubjectExperience(
                        RequestContextExtUtils.getOperatorId()));
    }

    @ApiOperation(value = "对业务灰度", httpMethod = "GET")
    @RestMapping(path = "/common/multiBizGray", method = RequestMethod.GET)
    public BaseResult<MultiBizGrayResponse> multiBizGray() {
        MultiBizGrayResponse response = new MultiBizGrayResponse();
        //todo tianlei 确定下全量放开吗, 让前端也下掉逻辑，下架接口
        response.setInGray(true);
        return BaseResult.success(response);
    }
}

package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.api.RpcProcessRescindService;
import com.timevale.contractmanager.common.service.enums.RelationTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.model.rescind.GenerateRescindFileModel;
import com.timevale.contractmanager.common.service.model.rescind.ProcessRescindCheckModel;
import com.timevale.contractmanager.common.service.model.rescind.ProcessRescindCheckResult;
import com.timevale.contractmanager.common.service.model.rescind.QueryRescindRecordModel;
import com.timevale.contractmanager.common.service.result.rescind.GenerateRescindFileResult;
import com.timevale.contractmanager.common.service.result.rescind.QueryRescindRecordResult;
import com.timevale.contractmanager.common.service.result.rescind.RescindFileModel;
import com.timevale.contractmanager.core.model.dto.request.GenerateRescindDocRequest;
import com.timevale.contractmanager.core.model.dto.response.GenerateRescindDocResponse;
import com.timevale.contractmanager.core.model.dto.response.QueryRelationRecordResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.handler.ProcessRelationHandler;
import com.timevale.contractmanager.core.service.process.impl.relation.ProcessRescindRelationServiceImpl;
import com.timevale.mandarin.common.annotation.RestService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-07-18
 */
@RestService
@Slf4j
public class RpcProcessRescindServiceImpl implements RpcProcessRescindService {
    @Autowired private ProcessRelationHandler processRelationHandler;
    @Autowired private ProcessRescindRelationServiceImpl processRescindRelationService;
    @Autowired private UserCenterService userCenterService;

    @Override
    public GenerateRescindFileResult generateRescindFile(GenerateRescindFileModel request) {
        GenerateRescindDocResponse response =
                processRelationHandler.generateRescindFile(
                        request.getAppId(),
                        request.getTenantId(),
                        new GenerateRescindDocRequest(
                                request.getProcessId(),
                                request.getRescindFileIds(),
                                request.getRescindRemark()));
        return response == null
                ? null
                : (response.getFiles() == null
                        ? new GenerateRescindFileResult(Collections.emptyList())
                        : new GenerateRescindFileResult(
                                response.getFiles().stream()
                                        .map(
                                                file ->
                                                        new RescindFileModel(
                                                                file.getFileKey(),
                                                                file.getFileType(),
                                                                file.getFrom(),
                                                                file.getSourceFileName(),
                                                                file.getFileId(),
                                                                file.getFileName()))
                                        .collect(Collectors.toList())));
    }

    @Override
    public QueryRescindRecordResult queryRescindRecord(QueryRescindRecordModel request) {
        QueryRelationRecordResponse response =
                processRelationHandler.queryRelationRecord(
                        request.getProcessId(),
                        request.getFileId(),
                        RelationTypeEnum.RESCIND.getType());
        return response == null
                ? null
                : (response.getRecords() == null
                        ? new QueryRescindRecordResult(Collections.emptyList())
                        : new QueryRescindRecordResult(response.getRecords()));
    }

    @Override
    public ProcessRescindCheckResult checkProcessRescind(ProcessRescindCheckModel request) {
        ProcessRescindCheckResult result = new ProcessRescindCheckResult();

        UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(request.getOperatorId());
        UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(request.getTenantId());
        try {
            processRescindRelationService.customizeCheckProcessCanRelate(request.getProcessId(), request.getMenuId(), operatorAccount, tenantAccount);
        } catch (BizContractManagerException e) {
            result.setCanRescind(false);
            result.setReason(e.getMessage());
            return result;
        } catch (Exception e) {
            log.warn("checkProcessRescind error", e);
            // 如果校验失败，则认为可以解约，最终解约时会拦截
            result.setCanRescind(true);
            return result;
        }
        result.setCanRescind(true);
        return result;
    }
}

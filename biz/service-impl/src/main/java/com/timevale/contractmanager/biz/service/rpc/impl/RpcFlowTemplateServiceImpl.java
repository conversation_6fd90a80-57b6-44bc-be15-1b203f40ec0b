package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.api.RpcFlowTemplateService;
import com.timevale.contractmanager.common.service.bean.FlowTemplateParticipantRelationDataSourceRuleDTO;
import com.timevale.contractmanager.common.service.bean.FlowTemplateRelationDataSourceRuleDTO;
import com.timevale.contractmanager.common.service.model.AuthFlowTemplateModel;
import com.timevale.contractmanager.common.service.model.ParticipantRelationDataSourceRuleModel;
import com.timevale.contractmanager.common.service.model.RelationDataSourceRuleModel;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.core.service.process.datasource.FlowTemplateSetDataSourceService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.mandarin.common.result.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 流程模板rpc
 *
 * @author: jinhuan
 * @since: 2020-08-18 20:20
 */
@RestService
@Slf4j
public class RpcFlowTemplateServiceImpl implements RpcFlowTemplateService {

    @Autowired private FlowTemplateService flowTemplateService;
    @Autowired
    private FlowTemplateSetDataSourceService setDataSourceService;

    @Override
    public BaseResult authFlowTemplate(AuthFlowTemplateModel model) {
        // 设置企业空间id
        RequestContextExtUtils.setTenantId(model.getTenantId());
        // 设置操作人id
        RequestContextExtUtils.setOperatorId(model.getAccountId());
        // 进行流程模板授权
        return flowTemplateService.authFlowTemplate(model.getFlowTemplateId());
    }

    @Override
    public FlowTemplateParticipantRelationDataSourceRuleDTO participantRelationDataSourceRule(ParticipantRelationDataSourceRuleModel model) {
        return setDataSourceService.participantRelationDataSourceFieldAndRule(model.getSubjectOid(), model.getDataSourceIds());
    }

    @Override
    public FlowTemplateRelationDataSourceRuleDTO relationDataSourceRule(
            RelationDataSourceRuleModel model) {
        return setDataSourceService.relationDataSourceFieldAndRule(model.getDataSourceIds());
    }
}

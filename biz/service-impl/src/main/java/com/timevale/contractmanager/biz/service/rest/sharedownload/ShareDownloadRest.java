package com.timevale.contractmanager.biz.service.rest.sharedownload;

import com.timevale.contractmanager.biz.service.rest.sharedownload.convert.ShareDownloadVOConvert;
import com.timevale.contractmanager.common.service.enums.sharedownload.Pdf2ImageTypeEnum;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadPreviewBO;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadShareInfoBO;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.ShareDownloadShareFileDownloadResponse;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.ShareDownloadShareFilePreviewResponse;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.ShareDownloadShareInfoResponse;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.ShareDownloadShareProcessInfoResponse;
import com.timevale.contractmanager.core.model.dto.sharedownload.FlowDocumentCacheDTO;
import com.timevale.contractmanager.core.service.sharedownload.ShareDownloadService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/2/10 分享下载
 */
@Api(tags = "分享下载")
@ExternalService
@RestMapping(path = "/v2/processes/share-download")
@Slf4j
public class ShareDownloadRest {
    @Autowired private ShareDownloadService shareDownloadService;

    /**
     * 获取分享信息
     *
     * @param processId
     * @return
     */
    @RestMapping(path = "/share-info", method = RequestMethod.GET)
    @ApiOperation(value = "获取分享信息", httpMethod = "GET")
    public BaseResult<ShareDownloadShareInfoResponse> getShareDownloadShareInfo(
            @RequestParam("processId") String processId) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantId = RequestContextExtUtils.getTenantId();
        ShareDownloadShareInfoBO shareInfoBO =
                shareDownloadService.getShareDownloadInfo(processId, operatorOid, tenantId);
        return shareInfoBO == null
                ? BaseResult.success()
                : BaseResult.success(
                        new ShareDownloadShareInfoResponse(
                                shareInfoBO.getNewShareDownloadUrl(),
                                shareInfoBO.getIsNewShareDownload(),
                                shareInfoBO.getDownloadPrivilegeExist()));
    }

    /**
     * 获取合同信息
     * @param shareDownloadId
     * @param sign
     * @return
     */
    @RestMapping(path = "/info", method = RequestMethod.GET)
    @ApiOperation(value = "获取合同信息", httpMethod = "GET")
    public BaseResult<ShareDownloadShareProcessInfoResponse> shareDownloadProcessPreview(
            @RequestParam("shareDownloadId") String shareDownloadId,@RequestParam("sign") String sign) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        return BaseResult.success( shareDownloadService.getShareDownloadProcessFiles(shareDownloadId, operatorOid, sign));
    }

    /**
     * 获取单个文件的下载地址
     *
     * @param shareDownloadId
     * @param sign
     * @param fileKey
     * @return
     */
    @RestMapping(path = "/files/{fileKey}/download", method = RequestMethod.GET)
    @ApiOperation(value = "获取单个合同文件下载地址", httpMethod = "GET")
    public BaseResult<ShareDownloadShareFileDownloadResponse> getShareDownloadFileDownloadUrl(
            @RequestParam("shareDownloadId") String shareDownloadId,
            @RequestParam("sign") String sign,
            @PathVariable("fileKey") String fileKey) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        FlowDocumentCacheDTO docCache =
                shareDownloadService.getShareDownloadProcessFileUrl(
                        shareDownloadId, operatorOid, sign, fileKey);
        return docCache == null
                ? BaseResult.success()
                : BaseResult.success(
                        new ShareDownloadShareFileDownloadResponse(
                                docCache.getFileKey(),
                                docCache.getFileUrl(),
                                docCache.getFileName()));
    }

    /**
     * 预览图片
     *
     * @param shareDownloadId
     * @param sign
     * @return
     */
    @RestMapping(path = "/files/{fileKey}/image-preview", method = RequestMethod.GET)
    @ApiOperation(value = "合同文档预览图片", httpMethod = "GET")
    public BaseResult<ShareDownloadShareFilePreviewResponse> shareDownloadProcessPreview(
            @RequestParam("shareDownloadId") String shareDownloadId,
            @RequestParam("sign") String sign,
            @PathVariable("fileKey") String fileKey,
            @ApiParam(value = "转换目标页码,从1开始") @RequestParam(value = "pageNum", required = false)
                    String pageNum,
            @ApiParam(value = "转换图片类型") @RequestParam(value = "imageType", required = false)
                    String imageType,
            @ApiParam(value = "转换方式,optimize时为异步，其余走同步") @RequestParam(value = "convertWay", required = false)
                    String convertWay) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        ShareDownloadPreviewBO.PdfPageImagesBO pageImagesBO =
                shareDownloadService.shareDownloadProcessFilePreview(
                        shareDownloadId,
                        operatorOid,
                        sign,
                        new ShareDownloadPreviewBO(
                                fileKey,
                                Pdf2ImageTypeEnum.convert2Pdf2ImageTypeEnum(imageType),
                                pageNum,
                                convertWay));
        return BaseResult.success(
                        new ShareDownloadShareFilePreviewResponse(
                                pageImagesBO.getPdfPageImagesTotal(),
                                pageImagesBO.getConvertStatus(),
                                pageImagesBO.getPdfPageImages().stream()
                                        .map(ShareDownloadVOConvert::convert2PdfPageImageVo)
                                        .collect(Collectors.toList())));
    }


}

package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.service.util.CharsetUtil;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM;

/**
 * <AUTHOR>
 * @since 2020/1/10
 */
@Slf4j
public abstract class BaseProcessRest {

    /**
     * 在接口中 可能以字段或者body的方式传入，为了统一处理流程，把租户id 设置到header中
     *
     * @param tenantId 租户id
     */
    protected void transTenantIdToHeader(String tenantId) {
        transTenantAndOperatorIdToHeader(tenantId, null);
    }

    /**
     * 在接口中 可能以字段或者body的方式传入，为了统一处理流程，把操作人id 设置到header中
     *
     * @param operatorId 操作人id
     */
    protected void transOperatorIdToHeader(String operatorId) {
        transTenantAndOperatorIdToHeader(null, operatorId);
    }

    /**
     * 在接口中 可能以字段或者body的方式传入，为了统一处理流程，把租户id、操作人id 设置到header中
     *
     * @param tenantId 租户id
     * @param operatorId 租户id
     */
    protected void transTenantAndOperatorIdToHeader(String tenantId, String operatorId) {
        if (StringUtils.isNotBlank(tenantId)) {
            RequestContextExtUtils.setTenantId(tenantId);
        }
        if (StringUtils.isNotBlank(operatorId)) {
            RequestContextExtUtils.setOperatorId(operatorId);
        }
    }

    /**
     * 判断是否为utf8mb4字符并抛出异常消息
     *
     * @param str 字符串
     */
    protected void checkTaskName(String str) {
        // 判断是否包含4字节的字符
        if (CharsetUtil.isContainUtf8mb4(str)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.TASK_NAME_UTF8MB4);
        }
    }

    /**
     * 判断发起参数中是否存在签署文档, 主要针对直接发起进行校验
     *
     * @param files 流程文档
     */
    protected void checkHasSignFile(Integer scene, List<? extends FileBO> files) {
        // 判断是否存在签署文档
        boolean directStart = ProcessStartScene.DIRECT_START.getScene() == scene;
        boolean hasSignFile =
                CollectionUtils.isNotEmpty(files)
                        && files.stream()
                                .anyMatch(item -> ProcessFileType.isContract(item.getFileType()));
        if (directStart && !hasSignFile) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "签署文档不能为空");
        }
    }
}

package com.timevale.contractmanager.biz.service.rest.infocollect;

import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.core.model.dto.request.infocollect.InfoCollectStartRequest;
import com.timevale.contractmanager.core.model.dto.response.infocollect.InfoCollectProcessListResponse;
import com.timevale.contractmanager.core.model.dto.response.infocollect.InfoCollectStartResponse;
import com.timevale.contractmanager.core.service.infocollect.InfoCollectFormDataService;
import com.timevale.contractmanager.core.service.infocollect.InfoCollectProcessListService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * Created by tianlei on 2022/9/19
 */
@Slf4j
@Api(tags = "信息采集")
@ExternalService
@RestMapping(path = "/v1/info-collect")
public class InfoCollectRest {

    @Autowired
    private InfoCollectProcessListService infoCollectProcessListService;

    @Autowired
    private InfoCollectFormDataService infoCollectFormDataService;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @ApiOperation(value = "采集数据-相关流程列表", httpMethod = "GET")
    @RestMapping(path = "/collect-data/process-list", method = RequestMethod.GET)
    public BaseResult<InfoCollectProcessListResponse> collectDataProcessList(@RequestParam String formId,
                                                                             @RequestParam String infoCollectRecordOuterId) {
        InfoCollectProcessListResponse response = infoCollectProcessListService.collectDataProcessList(RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                formId,
                infoCollectRecordOuterId);
        return BaseResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @ApiOperation(value = "发起上传数据获取凭证", httpMethod = "POST")
    @RestMapping(path = "/collect-data/start", method = RequestMethod.POST)
    public BaseResult<InfoCollectStartResponse> start(@RequestBody InfoCollectStartRequest request) {
        try {
            request.setUpgrade(Boolean.TRUE.toString().equals(RequestContext.getRequest().getHeader("X-Tsign-Saas-Info-Collect-Upgrade")));
        } catch (Exception e) {
            log.info("解析请求头失败", e);
        }
        return BaseResult.success(infoCollectFormDataService.start(RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                request));
    }

}

package com.timevale.contractmanager.biz.service.rpc.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.biz.service.util.ProcessGroupConverter;
import com.timevale.contractmanager.common.dal.bean.ContractProcessGroupDO;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.dal.bean.ContractProcessGroupExtra;
import com.timevale.contractmanager.common.dal.dao.ContractProcessGroupDAO;
import com.timevale.contractmanager.common.service.api.ContractProcessGroupService;
import com.timevale.contractmanager.common.service.bean.ContractProcessGroupBean;
import com.timevale.contractmanager.common.service.bean.ContractProcessGroupIdBean;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.model.BindProcessToGroupModel;
import com.timevale.contractmanager.common.service.model.CreateProcessGroupModel;
import com.timevale.contractmanager.common.service.model.QueryGroupIdBySubProcessModel;
import com.timevale.contractmanager.common.service.model.UpdateProcessGroupCreatorModel;
import com.timevale.contractmanager.common.service.result.BindProcessToGroupResult;
import com.timevale.contractmanager.common.service.result.CreateProcessGroupResult;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ProcessChangeProducer;
import com.timevale.contractmanager.core.service.mq.producer.ProcessNotifyHandler;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.mandarin.common.result.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.UUID;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;

@RestService
@Api("合同群组管理")
@Slf4j
public class ContractProcessGroupServiceImpl implements ContractProcessGroupService {

    @Autowired private ProcessNotifyHandler processNotifyHandler;
    @Autowired private ContractProcessGroupDAO contractProcessGroupDao;
    @Autowired
    BaseProcessService baseProcessService;

    @Autowired
    ProcessChangeProducer processChangeProducer;

    @Override
    @ApiOperation(value = "创建合同群组", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public CreateProcessGroupResult createProcessGroup(CreateProcessGroupModel model) {
        ContractProcessGroupDO contractProcessGroupDO = new ContractProcessGroupDO();
        String processGroupId = UUID.randomUUID().toString();
        contractProcessGroupDO.setProcessGroupId(processGroupId);
        contractProcessGroupDO.setProcessGroupName(model.getProcessGroupName());
        contractProcessGroupDO.setProcessGroupCreator(model.getCreatorAccountId());
        contractProcessGroupDO.setProcessGroupAppId(model.getProcessGroupAppId());
        contractProcessGroupDO.setProcessGroupOwner(model.getOwnerAccountId());
        contractProcessGroupDO.setProcessGroupType(model.getProcessGroupType());
        ContractProcessGroupExtra extra = new ContractProcessGroupExtra();
        extra.setSignMode(model.getSignMode());
        extra.setDedicatedCloudId(model.getDedicatedCloudId());
        contractProcessGroupDO.setExtra(JSON.toJSONString(extra));
        contractProcessGroupDao.insert(contractProcessGroupDO);
        return new CreateProcessGroupResult(processGroupId);
    }

    @Override
    public ContractProcessGroupBean queryProcessGroup(String processGroupId) {
        ContractProcessGroupDO processGroupDO =
                contractProcessGroupDao.queryByProcessGroupId(processGroupId);
        if (null != processGroupDO) {
            return ProcessGroupConverter.convertGroupBean(processGroupDO);
        } else {
            throw new BizContractManagerException(PROCESS_GROUP_NOT_EXIST);
        }
    }

    @Deprecated
    @Override
    public ContractProcessGroupIdBean queryGroupIdBySubProcess(QueryGroupIdBySubProcessModel queryGroupIdBySubProcessModel) {
        return new ContractProcessGroupIdBean();
    }

    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult updateProcessGroupCreator(
            UpdateProcessGroupCreatorModel updateProcessGroupCreatorModel) {
        ContractProcessGroupDO contractProcessGroupDO =
                contractProcessGroupDao.queryByProcessGroupId(
                        updateProcessGroupCreatorModel.getProcessGroupId());
        // 判断合同群组的发起人是否和入参的原发起人是否一致，防止越权
        if (!contractProcessGroupDO
                .getProcessGroupCreator()
                .equals(updateProcessGroupCreatorModel.getOldCreatorAccountId())) {
            log.warn(
                    "PROCESS_GROUP_NOT_EQUALS processGroupCreator={},request={}",
                    contractProcessGroupDO.getProcessGroupCreator(),
                    JsonUtils.obj2json(updateProcessGroupCreatorModel));
            throw new BizContractManagerException(PROCESS_GROUP_NOT_EQUALS);
        }
        contractProcessGroupDao.updateProcessGroupCreator(
                updateProcessGroupCreatorModel.getProcessGroupId(),
                updateProcessGroupCreatorModel.getNewCreatorAccountId());
        contractProcessGroupDO.setProcessGroupCreator(updateProcessGroupCreatorModel.getNewCreatorAccountId());
        processNotifyHandler.processInfoGroupUpdate(
                ProcessGroupConverter.convertGroupBean(contractProcessGroupDO));
        return new BaseResult();
    }

    @Override
    public BindProcessToGroupResult bindProcessToGroup(BindProcessToGroupModel model) {

        //1，获取流程ID
        String processId = model.getProcessId();
        if(StringUtils.isBlank(processId)){
            //如果流程ID为空，则从子流程中获取
            if(StringUtils.isBlank(model.getSubProcessId())){
                //子流程ID也空，直接报错
                throw new BizContractManagerException(PROCESS_NOT_EMPTY);
            }
            SubProcessDO subProcess = baseProcessService.getSubProcess(model.getSubProcessId());
            if(Objects.isNull(subProcess)){
                //子流程不存在
                throw new BizContractManagerException(SUB_PROCESS_NOT_EXIST);
            }
            processId = subProcess.getProcessId();
        }


        //2, 获取流程群组ID
        ContractProcessGroupBean contractProcessGroupBean = queryProcessGroup(model.getProcessGroupId());
        if(Objects.isNull(contractProcessGroupBean)){
            //流程群组不存在
            throw new BizContractManagerException(PROCESS_GROUP_NOT_EXIST);
        }

        //3， 绑定
        ProcessDO processDO = new ProcessDO();
        processDO.setProcessId(processId);
        processDO.setProcessGroupId(model.getProcessGroupId());
        baseProcessService.updateProcessGroup(processDO);

        //4，发送流程变更消息
        ProcessChangeMsgEntity entity = new ProcessChangeMsgEntity(processId);
        processChangeProducer.sendMessage(entity, ProcessChangeTagEnum.PROCESS_INFO_CHANGE.getTag());

        //5，返回
        BindProcessToGroupResult bindProcessToGroupResult = new BindProcessToGroupResult();
        bindProcessToGroupResult.setProcessGroupId(model.getProcessGroupId());
        bindProcessToGroupResult.setProcessId(processId);
        return bindProcessToGroupResult;
    }
}

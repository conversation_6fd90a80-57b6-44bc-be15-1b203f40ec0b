package com.timevale.contractmanager.biz.service.rest.opponent;

import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentPrivilegeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentRiskLevelEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.dto.request.opponent.*;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBatchGetBlackListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBlackListOrgCodeRespone;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBlackListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentEnterpriseViewResponse;
import com.timevale.contractmanager.core.service.opponent.OpponentBlackListService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saas.common.validator.util.StringCheckUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-03-02 16:12
 */
@Api(tags = "相对方管理-黑名单/企业全貌")
@ExternalService
@RestMapping(path = "/v2")
public class OpponentEntityBlackListRest {

	@Autowired
	private OpponentBlackListService opponentBlackListService;

	@UserPrivilegeCheck(
			resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
			privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_MANAGE_RISK_LEVEL)
	@RestMapping(path = "/opponent/blacklist/tenants/tenantId", method = RequestMethod.POST)
	@ApiOperation(value = "在租户空间下将实体加入黑名单", httpMethod = "POST")
	public BaseResult<Integer> joinBlackList(@RequestBody OpponentRiskLevelRequest opponentRiskLevelRequest){
		String operatorOid = RequestContextExtUtils.getOperatorId();
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentBlackListService.updateEntityRiskLevel(
				tenantOid, operatorOid,opponentRiskLevelRequest.getUuid(), OpponentRiskLevelEnum.BLACKLIST.getType()));
	}

	@UserPrivilegeCheck(
			resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
			privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_MANAGE_RISK_LEVEL)
	@RestMapping(path = "/opponent/blacklist/tenants/tenantId/", method = RequestMethod.DELETE)
	@ApiOperation(value = "在租户空间下将实体移出黑名单", httpMethod = "DELETE")
	public BaseResult<Void> dropBlackList(@ApiParam(value = "uuid",required = true) @RequestParam String uuid){
		String operatorOid = RequestContextExtUtils.getOperatorId();
		String tenantOid = RequestContextExtUtils.getTenantId();
		opponentBlackListService.updateEntityRiskLevel(
				tenantOid,operatorOid, uuid, OpponentRiskLevelEnum.WHITELIST.getType());
		return BaseResult.success();
	}

	@UserPrivilegeCheck(
			resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
			privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_MANAGE_RISK_LEVEL)
	@RestMapping(path = "/opponent/blacklist/tenants/tenantId/batchCreate", method = RequestMethod.POST)
	@ApiOperation(value = "在租户空间下将实体批量添加进黑名单", httpMethod = "POST")
	public BaseResult<Integer> batchJoinBlackList(
			@RequestBody OpponentBlackListBatchAddRequest OpponentBlackListBatchRequest){
		String operatorOid = RequestContextExtUtils.getOperatorId();
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentBlackListService.batchUpdateEntityRiskLevel(tenantOid,operatorOid,
				OpponentBlackListBatchRequest.getUuids(),
				OpponentRiskLevelEnum.BLACKLIST.getType()));
	}

	@UserPrivilegeCheck(
			resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
			privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_MANAGE_RISK_LEVEL)
	@RestMapping(path = "/opponent/blacklist/tenants/tenantId/batchDelete", method = RequestMethod.POST)
	@ApiOperation(value = "在租户空间下将实体批量移出黑名单", httpMethod = "POST")
	public BaseResult<Void> batchDropBlackList(
			@RequestBody OpponentBlackListBatchDeleteRequest OpponentBlackListBatchRequest){
		String operatorOid = RequestContextExtUtils.getOperatorId();
		String tenantOid = RequestContextExtUtils.getTenantId();
		opponentBlackListService.batchUpdateEntityRiskLevel(tenantOid,operatorOid,OpponentBlackListBatchRequest.getUuids(),
				OpponentRiskLevelEnum.WHITELIST.getType());
		return BaseResult.success();
	}


	@RestMapping(path = "/opponent/blacklist/tenants/tenantId/organizations", method = RequestMethod.GET)
	@ApiOperation(value = "在租户空间下查询企业是否在黑名单中",httpMethod = "GET")
	public BaseResult<Boolean> getBlackListStatusOrganizations(
			@ApiParam(value = "企业名称", required = true) @RequestParam String orgName
	){
        if (StringCheckUtil.containsEmoji(orgName) || StringCheckUtil.containsOverThreeBytes(orgName)) {
			throw new BizContractManagerException(
					BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "企业名称包含不支持的特殊字符或emoji表情！");
		}
		String operatorOid = RequestContextExtUtils.getOperatorId();
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentBlackListService.getOrgRiskLevel(tenantOid,orgName));
	}

	@RestMapping(path = "/opponent/blacklist/tenants/tenantId/individuals", method = RequestMethod.GET)
	@ApiOperation(value = "在租户空间下查询个人是否在黑名单中",httpMethod = "GET")
	public BaseResult<Boolean> getBlackListStatusIndividuals(
			@ApiParam(value = "联系方式，手机号或邮箱", required = true) @RequestParam String contact){
		String operatorOid = RequestContextExtUtils.getOperatorId();
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentBlackListService.getIndividualRiskLevel(tenantOid,contact));
	}

	@RestMapping(path = "/opponent/blacklist/tenants/tenantId/entity", method = RequestMethod.POST)
	@ApiOperation(value = "在租户空间下批量查询实体是否在黑名单中",httpMethod = "POST")
	public BaseResult<List<OpponentBatchGetBlackListResponse>>  batchGetBlackListStatus(@RequestBody OpponentBatchGetBlackListRequest opponentBatchGetBlackListRequest){
		String operatorOid = RequestContextExtUtils.getOperatorId();
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentBlackListService.batchGetBlackList(opponentBatchGetBlackListRequest,tenantOid));
	}

	@UserPrivilegeCheck(
			resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
			privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_CHECK_ORG_INFO)
	@RestMapping(path = "/opponent/blacklist/tenants/tenantId/orgcode", method = RequestMethod.GET)
	@ApiOperation(value = "在租户空间下获取企业编码", httpMethod = "GET")
	public BaseResult<OpponentBlackListOrgCodeRespone> getOrgCode(
			 @ApiParam(value = "企业名称", required = true) @RequestParam String OrgName){
		String operatorOid = RequestContextExtUtils.getOperatorId();
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentBlackListService.getTokenAndOrgCode(OrgName,tenantOid));
	}


	@RestMapping(path = "/opponent/blacklist/tenants/tenantId", method = RequestMethod.GET)
	@ApiOperation(value = "在租户空间下获取黑名单", httpMethod = "GET")
	public BaseResult<OpponentBlackListResponse> getBlackList(
			@RequestParam @ApiParam(value = "流程id", required = true) String processId,
			@RequestParam @ApiParam(value = "流程类型:0-processId,1-flowId",required = true) Integer processType,
			@RequestParam @ApiParam(value = "操作人oid",required = true)String accountId){
		return BaseResult.success(opponentBlackListService.getBlackListByProcess(processId,processType,accountId));
	}

	@RestMapping(path = "/opponent/blacklist/sign/tenants/tenantId", method = RequestMethod.GET)
	@ApiOperation(value = "在租户空间下获取黑名单(签署场景使用)", httpMethod = "GET")
	public BaseResult<OpponentBlackListResponse> getBlackListForSign(
			@RequestParam @ApiParam(value = "流程id", required = true) String processId,
			@RequestParam @ApiParam(value = "流程类型:0-processId,1-flowId",required = true) Integer processType,
			@RequestParam @ApiParam(value = "签署主体",required = true)String authorizerIds){
		return BaseResult.success(opponentBlackListService.getBlackListForSign(processId,processType,authorizerIds));
	}


	@RestMapping(path = "/opponent/tenants/tenantId/enterpriseView",method = RequestMethod.GET)
	@ApiOperation(value = "在租户空间下获取企业全貌的查看次数",httpMethod = "GET")
	public BaseResult<OpponentEnterpriseViewResponse> getNumberOfEnterpriseView(){
		String tenantOid = RequestContextExtUtils.getTenantId();
		return BaseResult.success(opponentBlackListService.getNumberOfEnterpriseView(tenantOid));
	}

	@RestMapping(path = "/opponent/tenants/tenantId/enterpriseView",method = RequestMethod.POST)
	@ApiOperation(value = "在租户空间下赠送企业全貌的查看次数",httpMethod = "POST")
	public BaseResult<Void> resetEnterpriseView(
			@RequestBody ResetEnterpriseViewRequest resetEnterpriseViewRequest){
		opponentBlackListService.resetEnterpriseView(resetEnterpriseViewRequest);
		return BaseResult.success();
	}

}

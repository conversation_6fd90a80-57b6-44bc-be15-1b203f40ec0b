package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.dal.bean.grouping.AiRuleMenuDO;
import com.timevale.contractmanager.common.dal.dao.grouping.AiRuleMenuDAO;
import com.timevale.contractmanager.common.service.api.RpcAiRuleMenuInnerService;
import com.timevale.contractmanager.common.service.model.airulemenu.AiRuleMenuQueryModel;
import com.timevale.contractmanager.common.service.result.autobind.AiRuleMenuResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.mandarin.common.annotation.RestService;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;

import javax.annotation.Resource;

@Slf4j
@RestService
public class RpcAiRuleMenuInnerServiceImpl implements RpcAiRuleMenuInnerService {

    @Resource
    private AiRuleMenuDAO aiRuleMenuDAO;
    @Resource
    private UserCenterService userCenterService;
    @Resource
    private MapperFactory mapperFactory;

    @Override
    public AiRuleMenuResult getByMenuId(AiRuleMenuQueryModel model) {
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(model.getTenantOid());
        AiRuleMenuDO aiRuleMenuDO = aiRuleMenuDAO.getRuleByMenuIdAndGid(model.getMenuId(), userAccount.getAccountGid());
        return mapperFactory.getMapperFacade().map(aiRuleMenuDO, AiRuleMenuResult.class);
    }
}

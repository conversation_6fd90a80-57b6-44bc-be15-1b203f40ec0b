package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.biz.service.rest.convert.ProcessesRestConvert;
import com.timevale.contractmanager.biz.service.rest.relationcontract.RelationContractRest;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartInitConfigRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartRequest;
import com.timevale.contractmanager.core.model.dto.request.StartByFlowTemplateRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.SaveStartInfoRequest;
import com.timevale.contractmanager.core.model.dto.request.process.*;
import com.timevale.contractmanager.core.model.dto.response.*;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.GetStartInfoResponse;
import com.timevale.contractmanager.core.model.dto.response.process.*;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessStartMode;
import com.timevale.contractmanager.core.service.aop.Idempotent;
import com.timevale.contractmanager.core.service.configs.ProcessStartConfig;
import com.timevale.contractmanager.core.service.other.ItsmService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.core.service.process.ProcessCommonService;
import com.timevale.contractmanager.core.service.process.ProcessConfigService;
import com.timevale.contractmanager.core.service.process.bean.FlowTemplateDetailParseConfig;
import com.timevale.contractmanager.core.service.process.datasource.ProcessDataSourceService;
import com.timevale.contractmanager.core.service.process.door.ProcessDoor;
import com.timevale.contractmanager.core.service.process.impl.ProcessStartHelper;
import com.timevale.contractmanager.core.service.process.participantsetup.ProcessSignSetUpBizRequest;
import com.timevale.contractmanager.core.service.process.rule.model.ProcessStartSetUpBizRequest;
import com.timevale.contractmanager.core.service.processstart.handler.ProcessStartAdapter;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContextService;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import com.timevale.tlcache.cache.ThreadLocalCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * 流程发起
 *
 * <p>本地文件直接发起流程 模板发起流程 流程模板创建/设置 以上三种场景合并处理
 *
 * <AUTHOR>
 * @since 2019/11/5
 */
@Api(tags = "合同管理发起合并")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class ProcessStartRest extends BaseProcessRest {

    private static final Integer UNKNOWN_SOURCE = -1;

    @Autowired private ProcessStartConfig processStartConfig;

    @Autowired private ItsmService itsmService;

    @Autowired private SystemConfig systemConfig;

    @Autowired private UserCenterService userCenterService;

    @Autowired private ProcessCommonService processCommonService;

    @Autowired private FlowTemplateService flowTemplateService;

    @Autowired private ProcessStartHelper processStartHelper;

    @Autowired private ProcessConfigService processConfigService;

    @Autowired private ProcessStartContextService processStartContextService;

    @Autowired private ProcessDoor processDoor;
    @Autowired
    private ProcessDataSourceService processDataSourceService;

    /**
     * 流程发起 包括本地直接发起和模板发起
     *
     * @param request 发起入参
     */
    @ThreadLocalCache
    @Idempotent(keyPath = "request.requestNo", operation = "process_start")
    @ApiOperation(value = "流程发起", httpMethod = "POST")
    @RestMapping(path = "/processes/start", method = RequestMethod.POST)
    public BaseResult<ProcessStartResponse> start(@RequestBody ProcessStartRequest request) {
        // 发起前置校验并处理请求参数
        checkAndPrepareStartRequest(request);
        // 组装发起业务请求参数
        ProcessStartBizRequest bizRequest =
                ProcessStartAdapter.buildProcessStartBizRequest(request);
        // 发起流程
        ProcessStartResult startResult = processDoor.start(bizRequest);
        // 组装返回结果
        ProcessStartResponse startResponse =
                processStartHelper.buildProcessStartResponse(startResult);
        return BaseResult.success(startResponse);
    }

    /**
     * 流程异步发起 仅限模板发起（由前端控制）
     *
     * @param request 发起入参
     */
    @ThreadLocalCache
    @Idempotent(keyPath = "request.requestNo", operation = "process_start")
    @ApiOperation(value = "流程发起--异步", httpMethod = "POST")
    @RestMapping(path = "/processes/start-async", method = RequestMethod.POST)
    public BaseResult<AsyncProcessStartResponse> startAsync(@RequestBody ProcessStartRequest request) {
        // 发起前置校验并处理请求参数
        checkAndPrepareStartRequest(request);
        // 组装发起业务请求参数
        ProcessStartBizRequest bizRequest =
                ProcessStartAdapter.buildProcessStartBizRequest(request);
        bizRequest.setAsyncStart(true);
        // 发起流程
        ProcessStartResult startResponse = processDoor.start(bizRequest);
        AsyncProcessStartResponse response = new AsyncProcessStartResponse();
        response.setProcessId(startResponse.getProcessId());
        // 如果是批量发起，这里填入的是发起时传入的任务中心的跳转地址（从入参拿到的）
        response.setResultUrl(startResponse.getResultUrl());
        response.setLongResultUrl(startResponse.getLongResultUrl());
        return BaseResult.success(response);
    }

    /**
     * 查询异步发起结果--轮询
     * @param processId
     * @return
     */
    @ApiOperation(value = "查询异步发起结果", httpMethod = "GET")
    @RestMapping(path = "/processes/{processId}/result-poll", method = RequestMethod.GET)
    public BaseResult<AsyncStartFlowResponse> getAsyncStartResult(
            @PathVariable @ApiParam(value = "流程Id", required = true) String processId) {
        String accountId = RequestContextExtUtils.getOperatorId();
        String tenantId = RequestContextExtUtils.getTenantId();
        return BaseResult.success(processCommonService.getAsyncStartResult(processId, accountId, tenantId));
    }

    /**
     * 发起前置校验并处理请求参数
     *
     * @param request
     */
    private void checkAndPrepareStartRequest(@RequestBody ProcessStartRequest request) {
        // 校验是否指定签署文件
        checkHasSignFile(request.getScene(), request.getFiles());
        // 判断流程名称是否包含4字节的字符
        checkTaskName(request.getTaskName());
        // 校验合同到期日期和签署截止日期参数
        request.checkValidityParam();
        // 检查参与方信息
        request.checkParams();
        // 初始化处理请求头数据
        transOperatorIdToHeader(request.getInitiatorAccountId());
        // 设置合同发起端
        int source = ClientEnum.getClientNo(RequestContextExtUtils.getClientId());
        request.setSource(UNKNOWN_SOURCE.equals(source) ? request.getSource() : source);
        // 补充合同编号类型
        String tenantId = RequestContextExtUtils.getTenantId();
        flowTemplateService.supplyContractNoType(
                tenantId, request.getFlowTemplateId(), null, request.getFiles());
    }

    /**
     * 通过流程模板id发起 在控件设置页面 直接发起会进入控件设置页面设置 签署区控件，完了后直接调用此接口发起
     *
     * @param request 发起入参
     */
    @ThreadLocalCache
    @ApiOperation(value = "通过流程模板id发起", httpMethod = "POST")
    @Idempotent(keyPath = "request.requestNo", operation = "process_start")
    @RestMapping(path = "/processes/startByFlowTemplate", method = RequestMethod.POST)
    public BaseResult<ProcessStartResponse> startByFlowTemplate(
            @RequestBody StartByFlowTemplateRequest request) {

        // 原本为initiatorAccountId,但是saas-webserver 会强制替换成
        // 当前登录用户，轩辕开放服务指定位置页面开放不行了，所以重新命名为initiatorAccountOid
        if (StringUtils.isBlank(request.getInitiatorAccountOid())) {
            request.setInitiatorAccountOid(RequestContextExtUtils.getOperatorId());
        }

        log.info(
                "start by template, operatorId = {} , initiatorAccountId = {}",
                RequestContextExtUtils.getOperatorId(),
                request.getInitiatorAccountOid());
        if (!StringUtils.equals(
                RequestContextExtUtils.getOperatorId(), request.getInitiatorAccountOid())) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_INITIATOR_NOT_PAIR);
        }
        String tenantId = RequestContextExtUtils.getTenantId();
        // 查询主体账号
        UserAccountDetail tenant = userCenterService.getUserAccountDetailByOid(tenantId);
        // 查询流程模板详情
        GetFlowTemplateResult flowTemplateResult =
                processStartContextService.getFlowTemplateResult(
                        request.getFlowTemplateId(), tenant);
        FlowTemplateDetailParseConfig config = new FlowTemplateDetailParseConfig();
        config.setSubject(tenant);
        config.setWithDocSource(false);
        config.setWithFileCategory(true);
        ProcessStartDetailResponse response =
                flowTemplateService.parseFlowTemplateDetail(flowTemplateResult, config);
        response.setInitiatorAccountId(request.getInitiatorAccountOid());
        response.setScene(request.getScene());
        response.setSkipFill(request.getSkipFill());
        if(!response.isEpaasTag()){
            response.setBusinessType(request.getBusinessType());
            response.setOriginProcessId(request.getOriginProcessId());
            response.setOriginFileIds(request.getOriginFileIds());
        }

        response.setBatchDropSeal(request.getBatchDropSeal());
        response.setSkipStartValid(ProcessStartType.skipStartValid(request.getSpecificScene()));

        ProcessStartBizRequest bizRequest =
                ProcessStartAdapter.buildProcessStartBizRequest(response);
        bizRequest.setAsyncStart(response.isEpaasTag());
        // 当前场景均为基于流程模板发起，因此统一模板发起模式为基于流程模板发起，ProcessStartMode.TEMPLATE_START
        bizRequest.setStartMode(ProcessStartMode.TEMPLATE_START.getMode());
        bizRequest.setInstanceId(request.getInstanceId());
        // 补充合同编号类型
        flowTemplateService.supplyContractNoType(
                tenantId,
                response.getRefFlowTemplateId(),
                response.getFiles(),
                bizRequest.getContracts());
        bizRequest.setStartDataSource(request.getDataSource());
        bizRequest.setNeedSealAuthCheck(request.isNeedSealAuthCheck());
        // 发起流程
        ProcessStartResult startResult = processDoor.start(bizRequest);
        // 组装返回结果
        ProcessStartResponse startResponse =
                processStartHelper.buildProcessStartResponse(startResult);
        return BaseResult.success(startResponse);
    }

    /**
     * 获取流程发起的配置信息
     *
     * @return 返回配置信息
     */
    @ApiOperation(value = "获取流程发起的配置信息", httpMethod = "GET")
    @RestMapping(path = "/processes/getStartConfig", method = RequestMethod.GET)
    public BaseResult<ProcessStartConfigResponse> getProcessStartConfig() {
        ProcessStartConfigResponse response = new ProcessStartConfigResponse();
        response.setDraftStartUrl(processStartConfig.getDraftStartUrl());
        response.setEditFlowTemplateUrl(processStartConfig.getEditFlowTemplateUrl());
        response.setUseFlowTemplateUrl(processStartConfig.getUseFlowTemplateUrl());
        response.setFlowTemplateRestartUrl(processStartConfig.getFlowTemplateRestartUrl());
        response.fortmat(processStartConfig.getProcessStartDomainUrl());
        response.setExperienceSignFile(processStartConfig.getExperienceSignFile());
        response.setOrg(
                !StringUtils.equals(
                        RequestContextExtUtils.getOperatorId(),
                        RequestContextExtUtils.getTenantId()));
        return BaseResult.success(response);
    }

    /**
     * 保存发起流程数据(缓存保存)
     *
     * @param request 文件id列表
     * @return 业务Id
     */
    @RestMapping(path = "/processes/saveStartInfo", method = RequestMethod.POST)
    @ApiOperation(value = "保存发起流程数据", httpMethod = "POST")
    public BaseResult<String> saveStartInfo(@RequestBody SaveStartInfoRequest request) {
        return BaseResult.success(processCommonService.saveStartInfo(request));
    }

    /**
     * 获取发起流程数据
     *
     * @param bizId 保存发起流程数据返回的业务Id
     * @return 文件列表
     */
    @RestMapping(path = "/processes/getStartInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取发起流程数据", httpMethod = "GET")
    public BaseResult<GetStartInfoResponse> getStartInfo(
            @ApiParam(value = "业务Id", required = true) @RequestParam String bizId) {
        return BaseResult.success(processCommonService.getStartInfo(bizId));
    }

    @RestMapping(path = "/processes/start/initConfig", method = RequestMethod.GET)
    @ApiOperation(value = "获取合同流程发起初始化配置", httpMethod = "GET")
    @MultilingualTranslateMethod
    public BaseResult<ProcessStartInitConfigResponse> getProcessStartInitConfig(
            @URIQueryParam @ModelAttribute ProcessStartInitConfigRequest request) {
        request.setClientId(RequestContextExtUtils.getClientId());
        request.setTenantId(RequestContextExtUtils.getTenantId());
        request.setResourceTenantId(RequestContextExtUtils.getResourceTenantId());
        request.setOperatorId(RequestContextExtUtils.getOperatorId());
        return BaseResult.success(processConfigService.getProcessStartInitConfig(request));
    }


    @ApiOperation(value = "参与方配置", httpMethod = "POST")
    @RestMapping(path = "/processes/participant-init-config", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ProcessesParticipantInitConfigResponse> participantInitConfig(@RequestBody ProcessesSignSetUpRequest request) {
        ProcessSignSetUpBizRequest bizRequest = ProcessesRestConvert.convert(request);
        bizRequest.setSubjectOid(RequestContextExtUtils.getTenantId());
        bizRequest.setOperatorOid(RequestContextExtUtils.getOperatorId());
        bizRequest.setClientId(RequestContextExtUtils.getClientId());
        bizRequest.setAppName(RequestContextExtUtils.getAppName());
        ProcessesParticipantInitConfigResponse response = new ProcessesParticipantInitConfigResponse();
        response.setDisplayRule(ProcessesRestConvert.displayRuleConvert(processDoor.participantSetUpDisplayRule(bizRequest)));
        return BaseResult.success(response);
    }

    @ApiOperation(value = "校验参与方配置", httpMethod = "POST")
    @RestMapping(path = "/processes/check-participant-config", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<Void> checkParticipantConfig(@RequestBody ProcessesCheckSignSetUpRequest request) {
        ProcessSignSetUpBizRequest bizRequest = ProcessesRestConvert.convert(request);
        bizRequest.setSubjectOid(RequestContextExtUtils.getTenantId());
        bizRequest.setOperatorOid(RequestContextExtUtils.getOperatorId());
        bizRequest.setClientId(RequestContextExtUtils.getClientId());
        processDoor.checkParticipantSetUpDisplayRule(bizRequest);
        return BaseResult.success(null);
    }

    @ApiOperation(value = "发起配置", httpMethod = "POST")
    @RestMapping(path = "/processes/start-config", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ProcessesStartConfigResponse> startConfig(@RequestBody ProcessesStartSetUpRequest request) {
        ProcessStartSetUpBizRequest bizRequest = ProcessesRestConvert.convert(request);
        bizRequest.setSubjectOid(RequestContextExtUtils.getTenantId());
        bizRequest.setResourceTenantId(RequestContextExtUtils.getResourceTenantId());
        bizRequest.setOperatorOid(RequestContextExtUtils.getOperatorId());
        bizRequest.setClientId(RequestContextExtUtils.getClientId());
        bizRequest.setAppName(RequestContextExtUtils.getAppName());
        return BaseResult.success(processDoor.startConfigDisplayRule(bizRequest));
    }

    // 1.数据源直接发起时获取填充的数据
    @ApiOperation(value = "发起时获取填充数据", httpMethod = "POST")
    @RestMapping(path = "/processes/start-data", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ProcessesStartGetDataResponse> startData(@RequestBody ProcessesStartGetDataRequest request) {
        List<FileBO> fileList = processDataSourceService.getStartData(RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                request.getDataSourceDataId());
        ProcessesStartGetDataResponse response = new ProcessesStartGetDataResponse();
        response.setFiles(fileList);
        return BaseResult.success(response);
    }


}

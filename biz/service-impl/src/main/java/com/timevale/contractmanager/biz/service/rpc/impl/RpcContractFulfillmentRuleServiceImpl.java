package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.api.RpcContractFulfillmentRuleService;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentTypeEnum;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleQueryListModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleSaveModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleSyncModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleUpdateModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentShardingRuleQueryListModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleDetailResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleListResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleSaveResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleTypeListResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentShardingRuleListResult;
import com.timevale.contractmanager.core.service.fulfillment.ContractFulfillmentRuleService;
import com.timevale.mandarin.common.annotation.RestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * RpcContractFulfillmentRuleServiceImpl
 *
 * <AUTHOR>
 * @since 2023/10/12 10:09 上午
 */
@Slf4j
@RestService
public class RpcContractFulfillmentRuleServiceImpl implements RpcContractFulfillmentRuleService {

    @Autowired
    private ContractFulfillmentRuleService contractFulfillmentRuleService;

    @Override
    public ContractFulfillmentRuleSaveResult save(ContractFulfillmentRuleSaveModel model) {
        return contractFulfillmentRuleService.save(model);
    }

    @Override
    public void update(ContractFulfillmentRuleUpdateModel model) {
        contractFulfillmentRuleService.update(model);
    }

    @Override
    public void delete(String ruleId, String tenantId, String accountId) {
        contractFulfillmentRuleService.delete(ruleId, tenantId, accountId);
    }

    @Override
    public void updateStatus(String ruleId, String status, String tenantId, String accountId) {
        contractFulfillmentRuleService.updateStatus(ruleId, status, tenantId, accountId);
    }

    @Override
    public ContractFulfillmentRuleDetailResult detail(String ruleId, String tenantId) {
        return contractFulfillmentRuleService.detail(ruleId, tenantId);
    }

    @Override
    public ContractFulfillmentRuleListResult pageList(ContractFulfillmentRuleQueryListModel model) {
        return contractFulfillmentRuleService.pageList(model);
    }

    @Override
    public ContractFulfillmentRuleTypeListResult typeList(String tenantId) {
        List<String> customTypeNameList = contractFulfillmentRuleService.queryCustomTypeNameList(tenantId);
        List<String> sysTypeNameList =  FulfillmentTypeEnum.getSystemTypeDesc();
        sysTypeNameList.addAll(customTypeNameList);
        sysTypeNameList = sysTypeNameList.stream().distinct().collect(Collectors.toList());
        return new ContractFulfillmentRuleTypeListResult((long) sysTypeNameList.size(), sysTypeNameList);
    }

    @Override
    public ContractFulfillmentShardingRuleListResult shardingList(ContractFulfillmentShardingRuleQueryListModel model) {
        return contractFulfillmentRuleService.shardingList(model);
    }

    @Override
    public void syncHistoryNoticeRule(ContractFulfillmentRuleSyncModel model) {
        contractFulfillmentRuleService.syncHistoryNoticeRule(model);
    }
}

package com.timevale.contractmanager.biz.service.rest;

import static com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum.PRIVILEGE_RESOURCE_ORG_ARCHIVE;
import static com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum.PRIVILEGE_UPDATE_OFFLINE_PROCESS_INFO;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_OPEN_OPERATOR_ID;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_OPEN_TENANT_ID;
import static com.timevale.footstone.rpc.enums.SignPlatformEnum.STANDARD_H5;
import static com.timevale.footstone.rpc.enums.SignPlatformEnum.STANDARD_WEB;
import static com.timevale.mandarin.weaver.utils.RequestContext.getAppId;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

import com.timevale.contractmanager.common.service.bean.Preference;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.enums.SigningDownloadTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.core.model.dto.process.ProcessCanDownloadDTO;
import com.timevale.contractmanager.core.model.dto.request.BatchSignNumberRequest;
import com.timevale.contractmanager.core.model.dto.request.BatchSignRequest;
import com.timevale.contractmanager.core.model.dto.request.MultiCloudDownloadHaveDataRequest;
import com.timevale.contractmanager.core.model.dto.request.QueryPreferenceRequest;
import com.timevale.contractmanager.core.model.dto.request.QueryProcessFilesRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.BatchSignAllRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessBatchCountRequestV3;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessBatchUpdateAllFilesCategoryRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessQueryRequestV3;
import com.timevale.contractmanager.core.model.dto.request.process.SomeProcessDownloadRequest;
import com.timevale.contractmanager.core.model.dto.request.process.UpdateOfflineProcessInfoRequest;
import com.timevale.contractmanager.core.model.dto.request.process.UpdateProcessContractCategoryRequest;
import com.timevale.contractmanager.core.model.dto.response.BatchSignAllNumberResponse;
import com.timevale.contractmanager.core.model.dto.response.BatchSignAllResponse;
import com.timevale.contractmanager.core.model.dto.response.BatchSignNumberResponse;
import com.timevale.contractmanager.core.model.dto.response.BatchSignResponse;
import com.timevale.contractmanager.core.model.dto.response.MultiCloudDownloadHaveDataResponse;
import com.timevale.contractmanager.core.model.dto.response.PreferenceResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessCountResponse;
import com.timevale.contractmanager.core.model.dto.response.QueryProcessFilesResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ApprovalGroupListQueryResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ApprovalGroupResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ProcessPreviewUrlResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ProcessQueryResponseV3;
import com.timevale.contractmanager.core.model.dto.response.process.QueryOfflineProcessInfoResponse;
import com.timevale.contractmanager.core.model.dto.response.processdownload.ProcessDownloadCheckCanDownloadResponse;
import com.timevale.contractmanager.core.model.dto.response.processdownload.ProcessDownloadCheckPermissionResponse;
import com.timevale.contractmanager.core.model.dto.response.processdownload.ProcessDownloadNoPermissionMsgResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.aop.SaasDataMasking;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.OfflineProcessService;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.process.ProcessCommonService;
import com.timevale.contractmanager.core.service.process.ProcessConfigService;
import com.timevale.contractmanager.core.service.process.ProcessDownloadService;
import com.timevale.contractmanager.core.service.process.ProcessSearchService;
import com.timevale.contractmanager.core.service.process.batchsign.BatchSignService;
import com.timevale.contractmanager.core.service.process.bean.GetProcessPreviewUrlInput;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: duhui
 * @since: 2021/9/23 2:40 下午
 **/
@Api(tags = "合同管理流程v3", description = "合同管理流程v3")
@ExternalService
@RestMapping(path = "/v3")
@Slf4j
public class ProcessRestV3 extends BaseRest {

    @Autowired private ProcessDownloadService processDownloadService;
    @Autowired private ProcessSearchService processSearchService;
    @Autowired private UserCenterService userCenterService;
    @Autowired private ProcessConfigService processConfigService;
    @Autowired private ProcessCommonService processCommonService;
    @Autowired private OfflineProcessService offlineProcessService;
    @Autowired private PreferencesService preferencesService;
    @Resource private BatchSignService batchSignService;

    @RestMapping(path = "/processes/list", method = RequestMethod.POST)
    @SaasDataMasking
    @ApiOperation(value = "processList", httpMethod = "POST")
    public BaseResult<ProcessQueryResponseV3> processList(@RequestBody ProcessQueryRequestV3 request) {
        request.setAccountId(RequestContextExtUtils.getOperatorId());
        Integer pageNum = request.getPageNum();
        Integer pageSize = request.getPageSize();

        Boolean result = userCenterService.checkMemberOrCreator(request.getSubjectId(), request.getAccountId());
        if (result == null || !result) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.ORG_MEMBER_NOT_EXIST_ERROR);
        }

        //查询条数校验(ES不支持20000条之后的查询),后端兜底
        if (Constants.ES_MAX_SUPPORT_TOTAL < (pageNum * pageSize)) {
            log.info("processes list request data is over the limit, pageNo:{}, pageSize:{}", pageNum, pageSize);
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_REQUEST_OVER_MAX_LIMIT);
        }
        ProcessQueryResponseV3 response = processSearchService.processQueryByType(request);
        return BaseResult.success(response);
    }

    @RestMapping(path = "/processes/list/count", method = RequestMethod.GET)
    @SaasDataMasking
    @ApiOperation(value = "经办合同各分栏的统计接口", httpMethod = "GET")
    public BaseResult<ProcessCountResponse> processListCount(
            @ApiParam(value = "docQueryTypes", required = true) @RequestParam(name = "docQueryTypes", required = true) String docQueryTypes,
            @ApiParam(value = "accountId", required = true) @RequestParam(name = "accountId", required = true) String accountId,
            @ApiParam(value = "subjectId", required = true) @RequestParam(name = "subjectId", required = true) String subjectId) {

        Boolean result = userCenterService.checkMemberOrCreator(subjectId, accountId);
        if (result == null || !result) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.ORG_MEMBER_NOT_EXIST_ERROR);
        }
        ProcessCountResponse countResponse = new ProcessCountResponse();
        countResponse.setCountList(new ArrayList<>());
        String[] docQueryTypeArr = docQueryTypes.split(",");
        List<Integer> docQueryTypeList = Arrays.stream(docQueryTypeArr)
                .filter(StringUtils::isNumeric)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(docQueryTypeList)) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "docQueryTypes必须为,拼接的数字");
        }
        ProcessBatchCountRequestV3 request = new ProcessBatchCountRequestV3();
        request.setAccountId(accountId);
        request.setSubjectId(subjectId);
        request.setPageSize(1);
        request.setPageNum(1);
        request.setDocQueryTypes(docQueryTypeList);
        return BaseResult.success(processSearchService.processBatchCount(request));
    }

    @RestMapping(path = "/approvalGroup/list", method = RequestMethod.GET)
    @SaasDataMasking
    @ApiOperation(value = "合同审批组查询", httpMethod = "GET")
    public BaseResult<ApprovalGroupListQueryResponse> approvalGroupList(
            @ApiParam(value = "pageSize", required = true) @Valid @Max(100) @RequestParam(name = "pageSize", required = true) Integer pageSize,
            @ApiParam(value = "pageNum", required = true) @Valid @Max(10000) @RequestParam(name = "pageNum", required = true) Integer pageNum,
            @ApiParam(value = "accountId", required = true) @RequestParam(name = "accountId", required = true) String accountId,
            @ApiParam(value = "subjectId", required = true) @RequestParam(name = "subjectId", required = true) String subjectId,
            @ApiParam(value = "docQueryType", required = true) @RequestParam(name = "docQueryType", required = true) Integer docQueryType) {
        Boolean result = userCenterService.checkMemberOrCreator(subjectId, accountId);
        if (result == null || !result) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.ORG_MEMBER_NOT_EXIST_ERROR);
        }
        ApprovalGroupListQueryResponse response = processSearchService.approvalGroupQuery(pageSize, pageNum, accountId, subjectId, docQueryType);
        return BaseResult.success(response);
    }

    @RestMapping(path = "/approvalGroup/{approvalGroupId}/list", method = RequestMethod.GET)
    @SaasDataMasking
    @ApiOperation(value = "合同审批组合同查询", httpMethod = "GET")
    public BaseResult<ApprovalGroupResponse> approvalGroupProcessList(
            @ApiParam(value = "合同审批流程组id", required = true) @PathVariable String approvalGroupId,
            @ApiParam(value = "pageSize", required = true) @Valid @Max(100) @RequestParam(name = "pageSize", required = true) Integer pageSize,
            @ApiParam(value = "pageNum", required = true) @Valid @Max(10000) @RequestParam(name = "pageNum", required = true) Integer pageNum,
            @ApiParam(value = "accountId", required = true) @RequestParam(name = "accountId", required = true) String accountId,
            @ApiParam(value = "subjectId", required = true) @RequestParam(name = "subjectId", required = true) String subjectId) {
        accountId = RequestContextExtUtils.getOperatorId();
        Boolean result = userCenterService.checkMemberOrCreator(subjectId, accountId);
        if (result == null || !result) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.ORG_MEMBER_NOT_EXIST_ERROR);
        }
        ApprovalGroupResponse response = processSearchService.queryProcessApprovalListByGroupId(approvalGroupId, pageSize, pageNum, accountId, subjectId);
        return BaseResult.success(response);
    }

    @RestMapping(path = "/processes/download/no-permission-msg", method = RequestMethod.GET)
    @ApiOperation(value = "合同下载无权限提示", httpMethod = "GET")
    public BaseResult<ProcessDownloadNoPermissionMsgResponse> processDownloadNoPermissionMsg(
            @ApiParam(name = "下载来源") @RequestParam Integer downloadSource) {
        return BaseResult.success(processDownloadService.processDownloadNoPermissionMsg(RequestContextExtUtils.getTenantId(), downloadSource));
    }

    // 代码里已有当前成员所属空间鉴权
    @RestMapping(path = "/processes/download/check-permission", method = RequestMethod.GET)
    @ApiOperation(value = "是否有合同下载权限", httpMethod = "GET")
    public BaseResult<ProcessDownloadCheckPermissionResponse> processDownloadCheckPermission(
            @ApiParam(name = "下载来源 1-经办合同") @RequestParam Integer downloadSource,
            @ApiParam(name = "分类Id", required = false) @RequestParam(required = false) String menuId) {
        boolean hasPermission = processDownloadService.checkHaveDownloadPermission(downloadSource, menuId,
                RequestContextExtUtils.getTenantId(), RequestContextExtUtils.getOperatorId());
        ProcessDownloadCheckPermissionResponse response = new ProcessDownloadCheckPermissionResponse();
        response.setHavePermission(hasPermission);
        return BaseResult.success(response);
    }

    // 代码里已有当前成员所属空间鉴权
    @RestMapping(path = "/processes/download/check-can-download", method = RequestMethod.POST)
    @ApiOperation(value = "是否有合同下载权限", httpMethod = "POST")
    public BaseResult<ProcessDownloadCheckCanDownloadResponse> processDownloadCheckCanDownload(
            @RequestBody SomeProcessDownloadRequest request) {

        List<ProcessCanDownloadDTO> checkResultList =
                processDownloadService.checkCanDownload(RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId(),
                        request);

        List<ProcessDownloadCheckCanDownloadResponse.ProcessCanDownload> list = checkResultList.stream().map(elm -> {
            ProcessDownloadCheckCanDownloadResponse.ProcessCanDownload canDownload =
                    new ProcessDownloadCheckCanDownloadResponse.ProcessCanDownload();
            canDownload.setCanDownload(elm.getCanDownload());
            canDownload.setProcessId(elm.getProcessId());
            if (elm.getCanDownload()) {
                canDownload.setDone(ProcessStatusEnum.isAllDoneStatusInRescind(elm.getProcessData().getProcessStatus()));
            } else {
                canDownload.setDone(Boolean.FALSE);
                canDownload.setCannotDownloadReason(elm.getCannotDownloadReason().getCode());
            }
            return canDownload;
        }).collect(Collectors.toList());

        if (request.getProcessIds().size() == 1 && !checkResultList.isEmpty()) {
            Optional.ofNullable(checkResultList.get(0))
                    .map(ProcessCanDownloadDTO::getCannotDownloadReason)
                    .ifPresent(reason -> {
                        switch (reason) {
                            case INITIATOR_PREFERENCE_SIGNING_FORBIDDEN_DOWNLOAD:
                                throw new BizContractManagerException(BizContractManagerResultCodeEnum.INITIATOR_PREFERENCE_SIGNING_FORBIDDEN_DOWNLOAD);
                            case APPID_STETTING_SIGNING_FORBIDDEN_DOWNLOAD:
                                throw new BizContractManagerException(BizContractManagerResultCodeEnum.APPID_STETTING_SIGNING_FORBIDDEN_DOWNLOAD);
                            default:
                                break;
                        }
                    });
        }
        // 判断合同下载带章偏好设置
        QueryPreferenceRequest queryPreferenceRequest = new QueryPreferenceRequest();
        queryPreferenceRequest.setClientId(RequestContextExtUtils.getClientId());
        queryPreferenceRequest.setPreferenceKeys(Collections.singletonList(ProcessPreferenceEnum.SIGNING_DOWNLOAD_TYPE.getKey()));
        queryPreferenceRequest.setOrgId(RequestContextExtUtils.getTenantId());
        PreferenceResponse preferenceResponse = preferencesService.queryByCondition(queryPreferenceRequest);
        return BaseResult.success(new ProcessDownloadCheckCanDownloadResponse(list, Optional.ofNullable(preferenceResponse)
                .map(PreferenceResponse::getPreferences)
                .filter(preferences -> !preferences.isEmpty())
                .map(preferences -> preferences.get(0))
                .map(Preference::getPreferenceValue)
                .orElse(SigningDownloadTypeEnum.PLATFORM_DEFAULT.getType())));
    }

    /** 获取流程文件列表 */
    @RestMapping(path = "/processes/query-files", method = RequestMethod.GET)
    @ApiOperation(value = "查询流程文件列表", httpMethod = "GET")
    public BaseResult<QueryProcessFilesResponse> queryProcessFiles(
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId,
            @RequestParam("processId") String processId,
            @RequestParam(value = "menuId", required = false) String menuId,
            @RequestParam(value = "resourceShareId", required = false) String resourceShareId,
            @RequestParam(value = "withFileKey", required = false) Boolean withFileKey,
            @RequestParam(value = "withAttachment", required = false) Boolean withAttachment,
            @RequestParam(value = "withCategory", required = false) Boolean withCategory) {
        QueryProcessFilesRequest request = new QueryProcessFilesRequest();
        request.setAccountId(accountId);
        request.setSubjectId(subjectId);
        request.setProcessId(processId);
        request.setMenuId(menuId);
        request.setResourceShareId(resourceShareId);
        request.setWithFileKey(Boolean.TRUE.equals(withFileKey));
        request.setWithAttachment(Boolean.TRUE.equals(withAttachment));
        request.setWithCategory(Boolean.TRUE.equals(withCategory));
        return BaseResult.success(processCommonService.queryProcessFiles(request));
    }

    /** 查看线下流程合同信息 */
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/processes/{processId}/offline-process-info", method = RequestMethod.GET)
    @ApiOperation(value = "查看线下流程合同信息", httpMethod = "GET")
    public BaseResult<QueryOfflineProcessInfoResponse> queryOfflineProcessInfo(
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId,
            @PathVariable String processId) {
        UserAccount subject = userCenterService.getUserAccountBaseByOid(subjectId);
        QueryOfflineProcessInfoResponse response =
                offlineProcessService.queryOfflineProcessInfo(subject.getAccountGid(), processId);
        return BaseResult.success(response);
    }

    /** 修改线下流程合同信息 */
    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PRIVILEGE_RESOURCE_ORG_ARCHIVE,
            privilegeKey = PRIVILEGE_UPDATE_OFFLINE_PROCESS_INFO)
    @RestMapping(
            path = "/processes/{processId}/update-offline-process-info",
            method = RequestMethod.POST)
    @ApiOperation(value = "修改线下流程合同信息", httpMethod = "POST")
    public BaseResult updateOfflineProcessInfo(
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId,
            @PathVariable String processId,
            @RequestBody UpdateOfflineProcessInfoRequest request) {
        UserAccount subject = userCenterService.getUserAccountBaseByOid(subjectId);
        offlineProcessService.updateOfflineProcessInfo(
                subject.getAccountOid(), subject.getAccountGid(), processId, request);
        return BaseResult.success();
    }

    /** 修改流程合同类型 */
    @RestMapping(path = "/processes/update-contract-category", method = RequestMethod.POST)
    @ApiOperation(value = "修改流程合同类型", httpMethod = "POST")
    public BaseResult updateContractCategory(
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId,
            @RequestBody UpdateProcessContractCategoryRequest request) {
        processConfigService.updateProcessContractCategory(accountId, subjectId, request);
        return BaseResult.success();
    }

    /** 一键修改流程所有文件为指定合同类型 */
    @RestMapping(path = "/processes/update-contract-category-all", method = RequestMethod.POST)
    @ApiOperation(value = "一键修改流程所有文件为指定合同类型", httpMethod = "POST")
    public BaseResult batchUpdateAllFilesCategory(
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId,
            @RequestBody ProcessBatchUpdateAllFilesCategoryRequest request) {
        processConfigService.batchUpdateProcessAllFilesCategory(accountId, subjectId, request);
        return BaseResult.success();
    }

    /** 获取流程预览地址 */
    @RestMapping(path = "/processes/preview-url", method = RequestMethod.GET)
    @ApiOperation(value = "获取流程预览地址", httpMethod = "GET")
    public BaseResult<ProcessPreviewUrlResponse> getPreviewUrl(
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String tenantId,
            @ApiParam(value = "流程id", required = true) @RequestParam String processId,
            @ApiParam(value = "指定主体id") @RequestParam(required = false) String subjectId,
            @ApiParam(value = "流程菜单目录id") @RequestParam(required = false) String menuId,
            @ApiParam(value = "资源分享id") @RequestParam(required = false) String resourceShareId,
            @ApiParam(value = "回跳地址") @RequestParam(required = false) String redirectUrl,
            @ApiParam(value = "token") @RequestParam(required = false) String token,
            @ApiParam(value = "是否返回h5地址， 默认返回pc") @RequestParam(required = false) Boolean h5) {
        GetProcessPreviewUrlInput input = new GetProcessPreviewUrlInput();
        input.setProcessId(processId);
        input.setAppId(getAppId());
        input.setClientId(getClientId());
        input.setAccountId(accountId);
        input.setSubjectId(StringUtils.isNotBlank(subjectId) ? subjectId : tenantId);
        input.setMenuId(menuId);
        input.setResourceShareId(resourceShareId);
        input.setPlatform(
                Boolean.TRUE.equals(h5) ? STANDARD_H5.getPlatform() : STANDARD_WEB.getPlatform());
        input.setRedirectUrl(redirectUrl);
        input.setToken(token);
        String previewUrl = processCommonService.getProcessPreviewUrl(input);
        return BaseResult.success(new ProcessPreviewUrlResponse(previewUrl));
    }

    /**
     * 异步批量下载
     *
     * @param request 异步批量下载请求
     * @return 异步批量下载结果
     */
    @FunctionPrivilegeCheck(
            function = FunctionCodeConstant.BATCH_DOWNLOAD,
            accountId = "request.subjectId",
            batchSize = "request.processIds")
    @RestMapping(path = "/processes/multi-cloud-download-have-data", method = RequestMethod.POST)
    @ApiOperation(value = "多云下载给出数量提示", httpMethod = "POST")
    public BaseResult<MultiCloudDownloadHaveDataResponse> multiCloudDownloadHaveData(
            @RequestBody MultiCloudDownloadHaveDataRequest request) {
        return BaseResult.success(processDownloadService.multiCloudDownloadHaveData(
                RequestContextExtUtils.getTenantId(), RequestContextExtUtils.getOperatorId(), request));
    }

    /**
     * 发起批量签署
     */
    @RestMapping(path = "/processes/batch-sign", method = RequestMethod.POST)
    @ApiOperation(value = "发起批量签署", httpMethod = "POST")
    public BaseResult<BatchSignResponse> batchSign(@RequestBody BatchSignRequest request) {
        request.setTenantId(RequestContextExtUtils.getTenantId());
        request.setOperatorId(RequestContextExtUtils.getOperatorId());
        return BaseResult.success(batchSignService.startBatchSign(request));
    }

    @RestMapping(path = "/processes/batch-sign/number", method = RequestMethod.POST)
    @ApiOperation(value = "获取批量签署数量", httpMethod = "POST")
    public BaseResult<BatchSignNumberResponse> batchSignNumber(@RequestBody BatchSignNumberRequest request) {
        request.setTenantId(RequestContextExtUtils.getTenantId());
        request.setOperatorId(RequestContextExtUtils.getOperatorId());
        return BaseResult.success(batchSignService.startBatchSignNumber(request));
    }

    @RestMapping(path = "/processes/batch-all-sign", method = RequestMethod.POST)
    @ApiOperation(value = "全量签署符合条件的合同", httpMethod = "POST")
    public BaseResult<BatchSignAllResponse> processBatchAllSign(@RequestBody BatchSignAllRequest request){
        request.setTenantId(RequestContextExtUtils.getTenantId());
        request.setOperatorId(RequestContextExtUtils.getOperatorId());
        return BaseResult.success(batchSignService.manageBatchAllSign(request));
    }

    @RestMapping(path = "/processes/batch-all-sign/number", method = RequestMethod.POST)
    @ApiOperation(value = "全量签署符合条件的合同数量", httpMethod = "POST")
    public BaseResult<BatchSignAllNumberResponse> processBatchAllSignNumber(@RequestBody BatchSignAllRequest request){
        request.setTenantId(RequestContextExtUtils.getTenantId());
        request.setOperatorId(RequestContextExtUtils.getOperatorId());
        return BaseResult.success(batchSignService.manageBatchAllSignNum(request));
    }

}

package com.timevale.contractmanager.biz.service.rpc.impl.opponent;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.mq.consumer.opponent.OpponentEntityAdapter;
import com.timevale.contractmanager.core.service.mq.model.SaasInviteMsg;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityService;
import com.timevale.contractmanager.core.service.opponent.OpponentSyncEsService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.enums.DocQueryEnum;
import com.timevale.signflow.search.docSearchService.enums.TimeRangeTypeEnum;
import com.timevale.signflow.search.docSearchService.param.DocQueryParam;
import com.timevale.signflow.search.docSearchService.result.DocQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * Created by tianlei on 2022/11/7
 * 开发专用 相对方 工具接口
 */
@Slf4j
@RestService
public class OpponentDevelopRpcService {

    @Autowired
    private OpponentEntityService opponentEntityService;
    @Autowired
    private OpponentEntityDAO opponentEntityDAO;
    @Autowired
    private EsClient esClient;
    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private OpponentEntityAdapter opponentEntityAdapter;
    @Autowired
    private OpponentSyncEsService opponentSyncEsService;


    /**
     * 同步oid 的gid, 修复同一个用户 不同账户 有的有gid 有的无
     */
    public String opponentDevelopSyncOpponentEntityGid(String oid) {
        AssertX.isTrue(StringUtils.isNotBlank(oid), "oid 必传");
        opponentEntityService.syncOpponentEntityGid(oid);
        return "success";
    }

    /**
     * 修复 个人相对方和企业向对方未关联的问题
     */
    public String opponentDevelopBindTenantPerson(Long tenantOpponentEntityId,
                                                  Long personOpponentEntityId) {
        if (null == personOpponentEntityId || null == tenantOpponentEntityId) {
            return "参数未传";
        }

        OpponentEntityDO tenantOpponentEntity = opponentEntityDAO.getById(tenantOpponentEntityId);
        OpponentEntityDO personOpponentEntity = opponentEntityDAO.getById(personOpponentEntityId);
        AssertX.isTrue(null != tenantOpponentEntity, "tenantOpponentEntity 不存在");
        AssertX.isTrue(null != personOpponentEntity, "personOpponentEntity 不存在");

        AssertX.isTrue(OpponentEntityTypeEnum.ORGANIZATION.getType() == tenantOpponentEntity.getEntityType(),
                "tenantOpponentEntity EntityType 错误");
        AssertX.isTrue(OpponentEntityTypeEnum.INDIVIDUAL.getType() == personOpponentEntity.getEntityType(),
                "personOpponentEntity EntityType 错误");
        AssertX.isTrue(Objects.equals(tenantOpponentEntity.getTenantOid(), personOpponentEntity.getTenantOid()) ||
                Objects.equals(tenantOpponentEntity.getTenantGid(), personOpponentEntity.getTenantGid()), "相对方不是同一个企业");
        AssertX.isTrue(null == personOpponentEntity.getAttachedEntityId(), "personOpponentEntity.getAttachedEntityId 已存在");

        OpponentEntityDO updatePersonOpponentEntity = new OpponentEntityDO();
        updatePersonOpponentEntity.setId(personOpponentEntity.getId());
        updatePersonOpponentEntity.setAttachedEntityId(tenantOpponentEntityId);
        log.info("opponentDevelopBindTenantPerson data : {}", JSON.toJSONString(updatePersonOpponentEntity));
        opponentEntityDAO.updateById(updatePersonOpponentEntity);
        return "success";
    }

    /**
     * 防止  opponentDevelopBindTenantPerson  修复数据修错了，在把这个取消掉
     */
    public String opponentDevelopUnbindTenantPerson(Long personOpponentEntityId) {
        if (null == personOpponentEntityId) {
            return "参数未传";
        }

        OpponentEntityDO personOpponentEntity = opponentEntityDAO.getById(personOpponentEntityId);
        AssertX.isTrue(null != personOpponentEntity, "personOpponentEntity 不存在");
        AssertX.isTrue(OpponentEntityTypeEnum.INDIVIDUAL.getType() == personOpponentEntity.getEntityType(),
                "personOpponentEntity EntityType 错误");
        AssertX.isTrue(null != personOpponentEntity.getAttachedEntityId(), "personOpponentEntity.getAttachedEntityId 不存在");
        log.info("opponentDevelopUnbindTenantPerson id : {}", personOpponentEntityId);
        opponentEntityDAO.clearAttachedEntityId(personOpponentEntityId);
        return "success";
    }

    /**
     * 通过一批历史合同补相对方数据
     */
    public String opponentDevelopRetryAddOpponentByProcess(String tenantOid, Long minCreateTime, Long maxCreateTime) {

        AssertX.isTrue(StringUtils.isNotBlank(tenantOid), "tenantOid 不存在");
        AssertX.isTrue(null != minCreateTime, "minCreateTime 不存在");
        AssertX.isTrue(null != maxCreateTime, "maxCreateTime 不存在");

        UserAccount account = userCenterService.getUserAccountBaseByOid(tenantOid);
        Account tenantAccount = new Account();
        tenantAccount.setOid(account.getAccountOid());
        tenantAccount.setGid(account.getAccountGid());


        String scrollId = null;
        for (int i = 0; i < 1000; i++) {
            if (StringUtils.isBlank(scrollId)) {
                DocQueryParam build = DocQueryParam
                        .builder()
                        .docQueryType(DocQueryEnum.INITIATE.getType())
                        .subject(tenantAccount)
                        .userScroll(true)
                        .timeRangeType(TimeRangeTypeEnum.PROCESS_START_TIME.getType())
                        .beginTimeInMillSec(minCreateTime)
                        .endTimeInMillSec(maxCreateTime)
                        .build();
                build.setPageNum(1);
                build.setPageSize(200);
                DocQueryResult query = esClient.query(build);
                if (null == query || CollectionUtils.isEmpty(query.getProcessInfoList())) {
                    break;
                }
                // 修复数据
                query.getProcessInfoList().forEach(elm -> opponentEntityService.addOpponentByProcess(elm.getProcessId()));
                scrollId = query.getScrollId();
            } else {
                DocQueryParam build = DocQueryParam
                        .builder()
                        .docQueryType(1)
                        .userScroll(true)
                        .build();
                build.setScrollId(scrollId);
                DocQueryResult query = esClient.query(build);
                if (null == query || CollectionUtils.isEmpty(query.getProcessInfoList())) {
                    break;
                }
                // 修复数据
                query.getProcessInfoList().forEach(elm -> opponentEntityService.addOpponentByProcess(elm.getProcessId()));
                scrollId = query.getScrollId();
            }
        }
        return "success";
    }

    /**
     * 重试单个processId
     */
    public String opponentDevelopRetryAddOpponentByOneProcess(String processId) {
        AssertX.isTrue(StringUtils.isNotBlank(processId), "processId 必传");
        opponentEntityService.addOpponentByProcess(processId);
        return "success";
    }

    /**
     * 通过邀请信息添加相对方
     */
    public void opponentDevelopCreateBySaasInvite(SaasInviteMsg inviteMsg) {
        log.info("saas invite add opponent by util req : {}", JSON.toJSONString(inviteMsg));
        opponentEntityAdapter.createBySaasInvite(inviteMsg);
    }

    public String opponentSyncEsDefaultSyncDevelop(String opponentEntityId) {
        opponentSyncEsService.syncEsByResourceBelongChange(opponentEntityId);
        return "success";
    }
}

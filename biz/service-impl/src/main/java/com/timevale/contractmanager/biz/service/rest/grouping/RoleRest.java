package com.timevale.contractmanager.biz.service.rest.grouping;

import com.timevale.contractmanager.common.service.result.grouping.RoleResult;
import com.timevale.contractmanager.core.service.grouping.RoleService;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.getTenantId;

/**
 * @author: xuanzhu
 * @since: 2019-10-09 14:52
 */
@Api(tags = "合同归档-角色相关服务")
@ExternalService
@RestMapping(path = "/v2")
public class RoleRest {

    @Autowired private RoleService roleService;

    @RestMapping(path = "/grouping_permission/role", method = RequestMethod.GET)
    @ApiOperation(value = "获取可操作的角色列表", httpMethod = "GET")
    public BaseResult<List<RoleResult>> getRoleList(
            @ApiParam(value = "当前操作人账号oid", required = true) @RequestParam String accountId) {
        return BaseResult.success(roleService.getAllRoleList(getTenantId(),accountId, null, null));
    }

    @RestMapping(path = "/grouping_permission/roleByAuthorizer", method = RequestMethod.GET)
    @ApiOperation(value = "获取可操作的角色列表", httpMethod = "GET")
    public BaseResult<List<RoleResult>> getRoleListByAuthorizer(
            @ApiParam(value = "当前操作人账号oid", required = true) @RequestParam String accountId,
            @ApiParam(value = "操作人oid/deptId", required = false) @RequestParam String authorizer,
            @ApiParam(value = "操作人类型 1-oid;2-deptId", required = false) @RequestParam Integer authorizeType) {
        return BaseResult.success(roleService.getAllRoleList(getTenantId(),accountId, authorizer, authorizeType));
    }

    @RestMapping(path = "/role/getRoleList", method = RequestMethod.GET)
    @ApiOperation(value = "根据分组获取可操作的角色列表", httpMethod = "GET")
    @MultilingualTranslateMethod
    public BaseResult<List<RoleResult>> getRoleListByGroup(
            @ApiParam(value = "角色分组,例如DOC_GROUP", required = true) @RequestParam String group) {
        return BaseResult.success(roleService.getRoleListByGroup(group));
    }
}

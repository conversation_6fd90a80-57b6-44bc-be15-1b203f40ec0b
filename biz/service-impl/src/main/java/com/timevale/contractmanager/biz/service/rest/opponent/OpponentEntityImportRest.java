package com.timevale.contractmanager.biz.service.rest.opponent;

import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentPrivilegeEnum;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentBatchCreateRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentEntityExcelResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentEntityImportResponse;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityImportService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @author: huifeng
 * @since: 2021-01-22 20:23
 */
@Api(tags = "相对方管理-批量导入")
@ExternalService
@RestMapping(path = "/v2")
public class OpponentEntityImportRest {

    @Autowired private OpponentEntityImportService opponentEntityImportService;

    @RestMapping(path = "/opponent/downloadUrls/organization", method = RequestMethod.GET)
    @ApiOperation(value = "获取相对方企业批量导入模版文件下载地址", httpMethod = "GET")
    public BaseResult<OpponentEntityImportResponse> getOrganizationDownloadUrl() {
        OpponentEntityImportResponse response = new OpponentEntityImportResponse();
        response.setDownloadUrl(
                opponentEntityImportService.getDownloadUrl(OpponentEntityTypeEnum.ORGANIZATION));
        return BaseResult.success(response);
    }

    @RestMapping(path = "/opponent/downloadUrls/individual", method = RequestMethod.GET)
    @ApiOperation(value = "获取相对方个人批量导入模版文件下载地址", httpMethod = "GET")
    public BaseResult<OpponentEntityImportResponse> getIndividualDownloadUrl() {
        OpponentEntityImportResponse response = new OpponentEntityImportResponse();
        response.setDownloadUrl(
                opponentEntityImportService.getDownloadUrl(OpponentEntityTypeEnum.INDIVIDUAL));
        return BaseResult.success(response);
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_ADD)
    @RestMapping(
            path = "/opponent/tenants/tenantId/organizations/batchImport",
            method = RequestMethod.POST)
    @ApiOperation(value = "通过上传模版批量添加相对方企业", httpMethod = "POST")
    public BaseResult<OpponentEntityExcelResponse> batchImportOrganizations(
            @RequestBody OpponentBatchCreateRequest batchCreateRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityImportService.batchImport(
                        operatorOid,
                        tenantOid,
                        batchCreateRequest.getTemplateFileKey(),
                        OpponentEntityTypeEnum.ORGANIZATION));
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT,
            privilegeKey = OpponentPrivilegeEnum.OPPONENT_PRIVILEGE_ADD)
    @RestMapping(
            path = "/opponent/tenants/tenantId/individuals/batchImport",
            method = RequestMethod.POST)
    @ApiOperation(value = "通过上传模版批量添加相对方个人", httpMethod = "POST")
    public BaseResult<OpponentEntityExcelResponse> batchImportIndividuals(
            @RequestBody OpponentBatchCreateRequest batchCreateRequest) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(
                opponentEntityImportService.batchImport(
                        operatorOid,
                        tenantOid,
                        batchCreateRequest.getTemplateFileKey(),
                        OpponentEntityTypeEnum.INDIVIDUAL));
    }
}

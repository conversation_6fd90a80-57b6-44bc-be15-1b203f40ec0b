package com.timevale.contractmanager.biz.service.rest.grouping;

import static com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum.PRIVILEGE_RESOURCE_ORG_ARCHIVE;
import static com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum.PRIVILEGE_MOVE;
import static com.timevale.contractmanager.core.service.tracking.consts.TrackingKeyConstant.MENU_SAVE;
import static com.timevale.contractmanager.core.service.tracking.consts.TrackingServiceConstant.MENU_SAVE_TRACKING;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_OPEN_OPERATOR_ID;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_OPEN_TENANT_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.dto.request.grouping.menu.*;
import com.timevale.contractmanager.core.model.dto.response.grouping.menu.MenuListResponse;
import com.timevale.contractmanager.core.model.dto.response.grouping.menu.MenuUserListResponse;
import com.timevale.contractmanager.core.model.dto.response.process.CheckMenuAuthDeptBelongMultiBizResponse;
import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;

import com.timevale.saas.tracking.annotation.Tracking;
import com.timevale.saas.tracking.constants.TrackingModeConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;

/**
 * 菜单操作相关REST
 *
 * <p>1.创建目录
 *
 * <p>2.更新目录
 *
 * <p>3.移动目录
 *
 * <p>4.删除目录
 *
 * <p>5.针对目录添加对应操作用户权限
 *
 * @author: xuanzhu
 * @since: 2019-09-08 22:02
 */
@Api(tags = "合同归档-目录相关操作")
@ExternalService
@RestMapping(path = "/v2")
public class MenuRest {

    @Autowired private MenuService menuService;

    @Autowired private UserCenterService userCenterService;

    private String getTenantOid() {
        return RequestContextExtUtils.getTenantId();
    }

    /**
     * 获取当前空间下的当前操作人具备权限的目录列表
     *
     * @param accountId 当前操作人账号
     * @return
     */
    @RestMapping(path = "/grouping_menus/list", method = RequestMethod.GET)
    @ApiOperation(value = "获取当前用户的目录列表", httpMethod = "GET")
    @MultilingualTranslateMethod
    public BaseResult<MenuListResponse> list(
            @ApiParam(value = "查询人账号oid", required = true) @RequestParam String accountId) {
        return BaseResult.success(menuService.list(getTenantOid(), accountId));
    }

    /**
     * 创建分类目录
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_menus", method = RequestMethod.POST)
    @ApiOperation(value = "创建目录", httpMethod = "POST")
    @MemberCheck(accountId = "#request.accountId", subjectId = SPEL_HEADER_TENANT_ID)
    @Tracking(
            trackingKey = MENU_SAVE,
            trackingService = MENU_SAVE_TRACKING,
            trackingData = "{{#request}}",
            operatorId = "{{#accountId}}",
            tenantId = "{{#tenantId}}",
            condition = "{{#_success}}",
            mode = TrackingModeConstants.MODE_MQ)
    public BaseResult<String> create(@RequestHeader(value = HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
                                     @RequestHeader(value = HEADER_TSIGN_OPEN_TENANT_ID) String tenantId, @RequestBody CreateMenuRequest request) {
        request.setAccountId(accountId);
        return BaseResult.success(menuService.create(getTenantOid(), request));
    }

    /**
     * 更新目录信息
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_menus/{menuId}", method = RequestMethod.PUT)
    @ApiOperation(value = "重命名目录", httpMethod = "PUT")
    public BaseResult<Boolean> modify(
            @PathVariable @ApiParam(value = "需要更新的菜单Id", required = true) String menuId,
            @RequestBody ModifyMenuRequest request) {
        return BaseResult.success(menuService.modify(getTenantOid(), menuId, request));
    }

    /**
     * 移动目录
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_menus/{menuId}/move", method = RequestMethod.POST)
    @ApiOperation(value = "移动目录", httpMethod = "POST")
    public BaseResult<Boolean> move(
            @PathVariable @ApiParam(value = "需要移动的菜单Id", required = true) String menuId,
            @RequestBody @Valid MoveMenuRequest request) {
        return BaseResult.success(menuService.move(getTenantOid(), menuId, request));
    }

    /**
     * 删除目录
     *
     * @param menuId
     * @param accountId
     * @return
     */
    @RestMapping(path = "/grouping_menus/{menuId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除目录", httpMethod = "DELETE")
    public BaseResult<Boolean> delete(
            @PathVariable @ApiParam(value = "需要删除的菜单Id", required = true) String menuId,
            @RequestParam String accountId)
    {
        return BaseResult.success(menuService.delete(getTenantOid(), menuId, accountId));
    }

    /**
     * 对目录添加对应操作用户
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_menus/{menuId}/adduser", method = RequestMethod.POST)
    @ApiOperation(value = "添加目录用户及授权", httpMethod = "POST")
    public BaseResult<Boolean> addUser(
            @PathVariable @ApiParam(value = "需要添加用户的菜单Id", required = true) String menuId,
            @RequestBody @Valid AddUserMenuRequest request) {
        return BaseResult.success(menuService.addUser(getTenantOid(), menuId, request, false));
    }

    /**
     * 删除目录对应用户
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/grouping_menus/{menuId}/deleteuser", method = RequestMethod.POST)
    @ApiOperation(value = "删除目录用户", httpMethod = "POST")
    public BaseResult<Boolean> deleteUser(
            @PathVariable @ApiParam(value = "需要删除用户的菜单Id", required = true) String menuId,
            @RequestBody @Valid DeleteUserMenuRequest request) {
        return BaseResult.success(menuService.deleteUser(getTenantOid(), menuId, request));
    }

    @RestMapping(path = "/grouping_menus/show/child", method = RequestMethod.POST)
    @ApiOperation(value = "修改分类显示子分类合同设置", httpMethod = "POST")
    public BaseResult<Void> updateMenuShowChild(@RequestBody @Valid UpdateMenuShowChildRequest request){
        menuService.updateMenuShowChild(RequestContextExtUtils.getTenantId(), request.getMenuId(), request.getShowChild());
        return BaseResult.success();
    }

    /**
     * 分页查询对应目录下的授权用户列表
     *
     * @param keyWord 查询关键词，只支持姓名（模糊匹配）
     * @param menuId 目录ID
     * @param accountId 当前操作人Oid
     * @param pageSize 每页大小
     * @param pageNum 当前页数
     * @return
     */
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/grouping_menus/{menuId}/queryuser", method = RequestMethod.GET)
    @ApiOperation(value = "查询目录下的授权用户列表", httpMethod = "GET")
    public BaseResult<List<MenuUserListResponse>> queryUser(
            @PathVariable @ApiParam(value = "菜单Id", required = true) String menuId,
            @ApiParam(value = "搜索关键词，只支持姓名", required = false) @RequestParam String keyWord,
            @ApiParam(value = "当前操作人", required = true) @RequestParam String accountId,
            @ApiParam(value = "每页大小", required = true) @RequestParam Integer pageSize,
            @ApiParam(value = "当前页数", required = true) @RequestParam Integer pageNum) {
        List<MenuUserListResponse> list = new ArrayList<>();
        int offset = (pageNum - 1) * pageSize;
        // 如果是第一页,则显示有全部管理权限的成员且显示在权限列表前列
        if (1 == pageNum) {
            //addAdminForMenuUserList(list, keyWord);
            // 如果管理员数量大于当前页大小 则第一页显示10个管理员即可
            if (list.size() >= pageSize) {
                return BaseResult.success(list);
            }
            // 第一页显示的正常成员数量减去管理员数量
            pageSize -= list.size();
        } else {
            // 如果没有查询条件,则分页参数需减去第一页的有全部管理权限的成员数量
            int count = userCenterService.queryUserHasAdminAllPrivilege(getTenantOid()).size();
            // 管理员数量超出2页的情况不显示第二页的管理员 第二页之后显示正常数据 需减去一页
            if (count > pageSize) {
                count = pageSize;
            }
            offset -= count;
        }
        List<MenuUserListResponse> menuUserListResponseList =
                menuService.getMenuUserList(menuId, keyWord, pageSize, offset, getTenantOid(), accountId);
        if (!ListUtils.isEmpty(menuUserListResponseList)) {
            list.addAll(menuUserListResponseList);
        }
        return BaseResult.success(list);
    }


//    /**
//     * 分类是否授权给了业务空间
//     */
//    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
//    @RestMapping(path = "/grouping_menus/{menuId}/authMultiBiz", method = RequestMethod.GET)
//    @ApiOperation(value = "查询分类是否授权给了业务空间", httpMethod = "GET")
//    public BaseResult<CheckMenuAuthMultiBizResponse> checkMenuAuthMultiBiz(
//            @PathVariable @ApiParam(value = "菜单Id", required = true) String menuId) {
//        boolean havePermission = menuService.checkMenuAuthMultiBiz(getTenantOid(),
//                RequestContextExtUtils.getOperatorId(), menuId);
//        CheckMenuAuthMultiBizResponse response = new CheckMenuAuthMultiBizResponse();
//        response.setAuthorized(havePermission);
//        return BaseResult.success(response);
//    }


    /**
     * 查询对应会员版本下是否还能新增目录
     */
    @RestMapping(path = "/grouping_menus/checkCreateCatalog", method= RequestMethod.GET)
    @ApiOperation(value = "归档文件夹是否超出会员版本上限",httpMethod = "GET")
    public BaseResult<Void> checkCreateCatalog(){
        menuService.checkCreateCatalog(getTenantOid(), RequestContextExtUtils.getOperatorId());
        return BaseResult.success();
    }

    /**
     * 校验目录名称是否重复
     *
     * @param menuId 目录id
     * @param parentId 父级目录id
     * @return 返回是否重复 同级父目录中不允许目录名称重复
     */
    @RestMapping(path = "/grouping_menus/checkMenuName", method = RequestMethod.GET)
    @ApiOperation(value = "校验目录名称是否重复", httpMethod = "GET")
    public BaseResult<Boolean> checkMenuName(
            @ApiParam(value = "目录id", required = true) @RequestParam String menuId,
            @ApiParam(value = "父级目录id", required = true) @RequestParam String parentId,
            @ApiParam(value = "目录名称", required = true) @RequestParam String menuName) {
        return BaseResult.success(
                menuService.checkMenuName(getTenantOid(), menuId, menuName, parentId));
    }

    @RestMapping(path = "/grouping_menus/{processId}", method = RequestMethod.GET)
    @ApiOperation(value = "查询合同所在有权限的分类", httpMethod = "GET")
    public BaseResult<List<String>> getProcessMenu(
            @PathVariable @ApiParam(value = "processId", required = true) String processId,
            @ApiParam(value = "目录id", required = false) @RequestParam String menuId) {
        Boolean result = userCenterService.checkMemberOrCreator(getTenantOid(), RequestContextExtUtils.getOperatorId());
        if (result == null || !result) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.ORG_MEMBER_NOT_EXIST_ERROR);
        }
        return BaseResult.success(
                menuService.getProcessMenuList(getTenantOid(), RequestContextExtUtils.getOperatorId(), menuId, processId));
    }


    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/grouping_menus/checkMenuAuthDeptBelongMultiBiz", method = RequestMethod.POST)
    @ApiOperation(value = "获取某些部门是否有隔离部门", httpMethod = "POST")
    public BaseResult<CheckMenuAuthDeptBelongMultiBizResponse> checkMenuAuthDeptBelongMultiBiz(
            @RequestBody CheckMenuAuthDeptBelongMultiBizRequest request) {
        boolean have = menuService.checkMenuAuthDeptBelongMultiBiz(RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(), request.getDeptIds());
        CheckMenuAuthDeptBelongMultiBizResponse response = new CheckMenuAuthDeptBelongMultiBizResponse();
        response.setHave(have);
        return BaseResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PRIVILEGE_RESOURCE_ORG_ARCHIVE,
            privilegeKey = PRIVILEGE_MOVE)
    @RestMapping(path = "/grouping_menus/childMenuSort", method = RequestMethod.POST)
    @ApiOperation(value = "子分类排序", httpMethod = "POST")
    public BaseResult<Void> childMenuSort(@RequestBody ChildMenuSortRequest request) {
        String tenantId = RequestContextExtUtils.getTenantId();
        String operatorId = RequestContextExtUtils.getOperatorId();
        request.setAccountOid(operatorId);
        request.setSubjectOid(tenantId);
        menuService.childMenuSort(request);
        return BaseResult.success();
    }



}

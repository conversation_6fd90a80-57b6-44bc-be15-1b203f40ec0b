package com.timevale.contractmanager.biz.service.rest.grouping;

import com.timevale.contractmanager.core.model.dto.request.grouping.process.GroupingListRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.QueryGroupingProcessListRequest;
import com.timevale.contractmanager.core.model.dto.response.grouping.dept.DeptTreeResponse;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.GroupingProcessListResponse;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.v2.GroupingProcessListResponseV2;
import com.timevale.contractmanager.core.service.grouping.GroupingListService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.getClientId;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.getTenantId;

/**
 * 归档列表操作Rest
 *
 * @author: xuanzhu
 * @since: 2019-09-17 17:01
 */
@Api(tags = "合同归档-已归档+待归档列表搜索")
@ExternalService
@RestMapping(path = "/v2")
public class ListRest {

    @Autowired private GroupingListService groupingListService;

    /**
     * 已归档+搜索列表
     *
     * @return
     */
    @RestMapping(path = "/grouping_lists/groupingQuery", method = RequestMethod.GET)
    @ApiOperation(value = "已归档列表查询", httpMethod = "GET")
    public BaseResult<GroupingProcessListResponse> groupingQuery(
            @ModelAttribute QueryGroupingProcessListRequest request) {
        request.valid();
        request.setSubject(
                StringUtils.isEmpty(getTenantId()) ? request.getAccountId() : getTenantId());
        request.setClientId(getClientId());
        GroupingProcessListResponse groupingProcessList =
                groupingListService.getGroupingProcessList(request);
        return BaseResult.success(groupingProcessList);
    }

    /**
     * 待归档搜索列表
     *
     * @return
     */
    @RestMapping(path = "/grouping_lists/ungroupedQuery", method = RequestMethod.GET)
    @ApiOperation(value = "待归档列表查询", httpMethod = "GET")
    public BaseResult<GroupingProcessListResponse> ungroupedQuery(
            @ModelAttribute QueryGroupingProcessListRequest request) {
        request.valid();
        request.setSubject(
                StringUtils.isEmpty(getTenantId()) ? request.getAccountId() : getTenantId());
        request.setClientId(getClientId());
        GroupingProcessListResponse unGroupingProcessList =
                groupingListService.getUnGroupingProcessList(request,null);
        return BaseResult.success(unGroupingProcessList);
    }

    @RestMapping(path = "/grouping_lists/list", method = RequestMethod.GET)
    @ApiOperation(value = "智能台账2.0-企业合同列表", httpMethod = "GET")
    @MultilingualTranslateMethod
    public BaseResult<GroupingProcessListResponseV2> list(@ModelAttribute GroupingListRequest request){
        return BaseResult.success(groupingListService.getProcessList(request));
    }

    @RestMapping(path = "/grouping_lists/getDptTree", method = RequestMethod.GET)
    @ApiOperation(value = "获取是部门负责人的部门树", httpMethod = "GET")
    public BaseResult<DeptTreeResponse>  getDptTree(){
        String tenantId = RequestContextExtUtils.getTenantId();
        String operatorId = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(groupingListService.getDptTree(tenantId,operatorId));
    }
}

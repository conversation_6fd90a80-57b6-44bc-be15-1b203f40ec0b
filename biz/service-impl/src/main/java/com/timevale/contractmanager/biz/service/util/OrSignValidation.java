package com.timevale.contractmanager.biz.service.util;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.APP_NOT_SUPPORT_OR_SIGN;

import com.timevale.contractmanager.common.service.enums.SourceEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023-09-19 17:09
 */
public class OrSignValidation {

    private static final String DING_MOBILE_FRONT = "ding-treaty-front";

    public static void validClient(String clientId, String clientVersion, String appName) {
        // 是否是钉签移动端
        boolean isDingMobile =
                SourceEnum.DING_TALK.getCode().equalsIgnoreCase(clientId)
                        && DING_MOBILE_FRONT.equalsIgnoreCase(appName);

        // 判断端是否支持或签，目前钉签移动端不支持，ios、安卓5.5.6版本以下不支持，
        if (!ConfigService.getAppConfig().getBooleanProperty("appSupportOrSign", false)
                && (isDingMobile || checkClientSupport(clientId, clientVersion))) {
            throw new BizContractManagerException(APP_NOT_SUPPORT_OR_SIGN);
        }
    }

    private static boolean checkClientSupport(String clientId, String clientVersion) {
        if (StringUtils.isBlank(clientId) || StringUtils.isBlank(clientVersion)) {
            return false;
        }

        String androidMinVersion =
                ConfigService.getAppConfig().getProperty("androidOrSignVersion", "5.5.6");
        String iosMinVersion =
                ConfigService.getAppConfig().getProperty("iosOrSignVersion", "5.5.6");

        if (SourceEnum.APP_IOS.getCode().equalsIgnoreCase(clientId)
                && lowerVersion(clientVersion, iosMinVersion)) {
            return false;
        } else if (SourceEnum.APP_ANDROID.getCode().equalsIgnoreCase(clientId)
                && lowerVersion(clientVersion, androidMinVersion)) {
            return false;
        }

        return true;
    }

    /** 判断是否低于某个版本，版本号格式 0.0.0（版本格式错误或出现非数字也会返回false） */
    private static boolean lowerVersion(String currentVersion, String minVersion) {
        String[] versionCodes = currentVersion.split("\\.");
        String[] minVersionCodes = minVersion.split("\\.");
        if (versionCodes.length != 3 || minVersionCodes.length != 3) {
            return false;
        }

        for (int i = 0; i < 3; i++) {
            String versionCode = versionCodes[i];
            String minVersionCode = minVersionCodes[i];
            if (!StringUtils.isNumeric(versionCode) || !StringUtils.isNumeric(minVersionCode)) {
                return false;
            } else if (Integer.parseInt(versionCode) < Integer.parseInt(minVersionCode)) {
                return false;
            }
        }

        return true;
    }
}

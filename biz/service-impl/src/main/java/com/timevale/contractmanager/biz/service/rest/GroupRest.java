package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.common.service.bean.GroupListQueryRequest;
import com.timevale.contractmanager.common.service.bean.GroupListQueryResponse;
import com.timevale.contractmanager.common.service.bean.ProcessListQueryRequest;
import com.timevale.contractmanager.common.service.bean.ProcessListQueryResponse;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.core.model.dto.process.DeleteProcessGroupDTO;
import com.timevale.contractmanager.core.model.dto.request.DeleteProcessGroupRequest;
import com.timevale.contractmanager.core.model.dto.request.GetBatchSignUrl4GroupRequest;
import com.timevale.contractmanager.core.model.dto.request.group.ProcessRevokeRequest;
import com.timevale.contractmanager.core.model.dto.response.GetBatchSignUrl4GroupResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessExportResponse;
import com.timevale.contractmanager.core.model.dto.response.group.TaskCenterBaseResponse;
import com.timevale.contractmanager.core.service.group.GroupSearchService;
import com.timevale.contractmanager.core.service.group.GroupService;
import com.timevale.contractmanager.core.service.process.ProcessService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @author: huifeng
 * @since: 2020-07-22 22:36
 **/

@Api(tags = "批量流程合同管理", description = "批量流程合同搜索")
@ExternalService
@Slf4j
public class GroupRest extends BaseRest{

    @Autowired
    private GroupSearchService groupSearchService;

    @Autowired
    private ProcessService processService;

    @Autowired
    private GroupService groupService;

    /**
     * 查询合同流程组列表
     *
     * @return
     * @throws BaseRuntimeException
     */
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.BATCH_INITIATE_MANAGE, accountId = "param.authorizedAccountId")
    @ApiOperation(value = "查询流程列表", httpMethod = "GET")
    @RestMapping(path = "/v1/processGroups/list", method = RequestMethod.GET)
    public BaseResult<GroupListQueryResponse> queryProcessGroupList(
            @URIQueryParam @ModelAttribute GroupListQueryRequest param)
    {
        //查询条数校验(ES不支持20000条之后的查询),后端兜底
        if(Constants.ES_MAX_SUPPORT_TOTAL < (param.getPage() * param.getPageSize())){
            log.info("The request data is over the limit, pageNo:{}, pageSize:{}", param.getPage(), param.getPageSize());
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_REQUEST_OVER_MAX_LIMIT);
        }
        String tenantId = getTenantId();
        if (StringUtils.isBlank(param.getAuthorizedAccountId())) {
            param.setAuthorizedAccountId(tenantId);
        }

        return BaseResult.success(groupSearchService.searchGroup(param));
    }

    /**
     * 查询合同流程组列表
     *
     * @return
     * @throws BaseRuntimeException
     */
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.BATCH_INITIATE_MANAGE, accountId = "param.authorizedAccountId")
    @ApiOperation(value = "查询合同流程组的子合同流程列表", httpMethod = "GET")
    @RestMapping(path = "/v1/processGroups/{processGroupId}/list", method = RequestMethod.GET)
    public BaseResult<ProcessListQueryResponse> queryProcessList(
            @ApiParam(value = "合同流程组id", required = true) @PathVariable String processGroupId,
            @URIQueryParam @ModelAttribute ProcessListQueryRequest param)
    {
        String tenantId = getTenantId();
        if (StringUtils.isBlank(param.getAuthorizedAccountId())) {
            param.setAuthorizedAccountId(tenantId);
        }
        ProcessListQueryResponse response = groupSearchService.searchProcessByGroup(param, processGroupId);

        return BaseResult.success(response);
    }

    /**
     * 获取批量签署的地址
     * todo cleanup
     *
     * @param request 请求
     * @return 批量签署地址
     */
    @Deprecated
    @RestMapping(path = "/v1/processGroups/getBatchSignUrl", method = RequestMethod.POST)
    @ApiOperation(value = "获取批量签署的地址", httpMethod = "POST")
    public BaseResult<GetBatchSignUrl4GroupResponse> getBatchSignUrl(
            @RequestBody GetBatchSignUrl4GroupRequest request) {

        String tenantOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();

        GetBatchSignUrl4GroupResponse response = processService.getBatchSignUrl4Group(request, operatorOid, tenantOid);

        return BaseResult.success(response);
    }

    /**
     * 批量合同任务-撤回
     *
     * @return
     * @throws BaseRuntimeException
     */
    @ApiOperation(value = "撤销合同流程组", httpMethod = "PUT")
    @RestMapping(path = "/v2/processGroups/{processGroupId}/revoke", method = RequestMethod.PUT)
    public BaseResult<TaskCenterBaseResponse> revokeProcessGroup(
                                    @ApiParam(value = "合同流程组id", required = true) @PathVariable String processGroupId,
                                    @RequestBody(required = false) ProcessRevokeRequest param)
    {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(groupService.revokeProcessGroup(processGroupId, param.getRevokeReason(), operatorOid, tenantOid));
    }

    @ApiOperation(value = "催办合同流程组", httpMethod = "PUT")
    @RestMapping(path = "/v2/processGroups/{processGroupId}/rush", method = RequestMethod.PUT)
    public BaseResult<TaskCenterBaseResponse> rushProcessGroup(
            @ApiParam(value = "合同流程组id", required = true) @PathVariable String processGroupId)
    {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(groupService.rushProcessGroup(processGroupId, operatorOid, tenantOid));
    }

    @ApiOperation(value = "导出合同流程组明细", httpMethod = "GET")
    @RestMapping(path = "/v2/processGroups/{processGroupId}/export", method = RequestMethod.GET)
    public BaseResult<ProcessExportResponse> exportProcessGroup(
            @ApiParam(value = "合同流程组id", required = true) @PathVariable String processGroupId)
    {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();

        return BaseResult.success(groupService.exportProcessGroup(processGroupId,operatorOid,tenantOid));
    }

    @RestMapping(path = "/v1/processGroups/delete", method = RequestMethod.POST)
    @ApiOperation(value = "删除流程组", httpMethod = "POST")
    public BaseResult<?> deleteProcessGroup(@RequestBody DeleteProcessGroupRequest request) {
        DeleteProcessGroupDTO processGroupDTO = new DeleteProcessGroupDTO();
        processGroupDTO.setProcessGroupId(request.getProcessGroupId());
        processGroupDTO.setOperateType(request.getOperateType());
        processGroupDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        processGroupDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        groupService.deleteProcessGroupByOperateType(processGroupDTO);
        return BaseResult.success();
    }
}

package com.timevale.contractmanager.biz.service.rest;

import com.timevale.contractmanager.biz.service.util.OrSignValidation;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.dto.request.SaveFlowTemplateRequest;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.*;
import com.timevale.contractmanager.core.model.dto.response.ProcessFlowTemplateDetailResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResponse;
import com.timevale.contractmanager.core.model.dto.response.flowtemplate.*;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.service.aop.FunctionPrivilegeAuth;
import com.timevale.contractmanager.core.service.process.FlowTemplatePlusService;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.core.service.process.door.ProcessDoor;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.RelationAndPrivilegeCheck;
import com.timevale.saas.tracking.annotation.Tracking;
import com.timevale.saas.tracking.constants.TrackingModeConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.timevale.contractmanager.core.service.tracking.consts.TrackingKeyConstant.FLOW_TEMPLATE_SAVE;
import static com.timevale.contractmanager.core.service.tracking.consts.TrackingServiceConstant.FLOW_TEMPLATE_SAVE_TRACKING;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_CLIENT_ID;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.HEADER_TSIGN_CLIENT_VERSION;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.HEADER_TSIGN_OPEN_RESOURCE_TENANT_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_RESOURCE_TENANT_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2020/1/16
 */
@Api(tags = "合同管理流程模板管理")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class FlowTemplateRest extends BaseProcessRest {

    private static final String HEADER_KEY_OPERATOR_ID = "X-Tsign-Open-Operator-Id";
    private static final String HEADER_KEY_TENANT_ID = "X-Tsign-Open-Tenant-Id";
    private static final String HEADER_KEY_APP_NAME = "X-Tsign-Client-AppName";
    private static final String TEMPLATE_MANAGE = "templateManage";

    @Autowired private FlowTemplateService flowTemplateService;
    @Autowired private FlowTemplatePlusService flowTemplatePlusService;
    @Autowired private SystemConfig systemConfig;
    @Autowired private ProcessDoor processDoor;

    /**
     * 保存流程模板 新增/设置
     *
     * @param request 发起入参
     * @return 保存地址url
     */
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = TEMPLATE_MANAGE)
    @FunctionPrivilegeAuth(functionNames = {FunctionCodeConstant.AI_HAND_DRAW})
    @Tracking(
            trackingKey = FLOW_TEMPLATE_SAVE,
            trackingService = FLOW_TEMPLATE_SAVE_TRACKING,
            trackingData = "{{#request}}",
            operatorId = "{{#operatorId}}",
            tenantId = "{{#tenantId}}",
            condition = "{{#_success}}",
            mode = TrackingModeConstants.MODE_MQ)
    @ApiOperation(value = "保存流程模板", httpMethod = "POST")
    @RestMapping(path = "/processes/flowTemplates", method = RequestMethod.POST)
    public BaseResult<ProcessStartResponse> saveFlowTemplate(
            @RequestBody SaveFlowTemplateRequest request,
            @RequestHeader(value = HEADER_KEY_OPERATOR_ID, required = false) String operatorId,
            @RequestHeader(value = HEADER_KEY_TENANT_ID, required = false) String tenantId) {
        // 预处理请求参数
        request.preHandleRequest();
        transOperatorIdToHeader(request.getInitiatorAccountId());
        // 简单参数校验
        checkTaskName(request.getTaskName());
        request.checkParams();

        // 保存流程模板
        request.setClientId(RequestContextExtUtils.getClientId());
        request.setSpaceOid(RequestContextExtUtils.getTenantId());
        request.setSubjectOid(RequestContextExtUtils.getResourceTenantId());
        request.setOperatorOid(RequestContextExtUtils.getOperatorId());
        request.setAppId(RequestContextExtUtils.getAppId());
        request.setAppName(RequestContextExtUtils.getAppName());
        ProcessStartResponse response = processDoor.flowTemplate(request);

        // 新增流程模板进行默认授权
        boolean isCreate =
                ProcessStartScene.CREATE_SET_TEMPLATE.getScene() == request.getScene()
                        || ProcessStartScene.CREATE_SET_THIRD_TEMPLATE.getScene()
                                == request.getScene();
        if (isCreate && StringUtils.isBlank(request.getFlowTemplateId())) {
            // 这里的processId就是新增模板后的流程模板id
            flowTemplateService.authFlowTemplate(response.getProcessId());
        }
        return BaseResult.success(response);
    }

    /**
     * 流程发起详情信息
     *
     * @param flowTemplateId 流程模板id
     * @return 流程发起详情信息
     */
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = TEMPLATE_MANAGE)
    @ApiOperation(value = "流程发起详情信息", httpMethod = "GET")
    @RestMapping(path = "/processes/flowTemplates/{flowTemplateId}", method = RequestMethod.GET)
    public BaseResult<ProcessFlowTemplateDetailResponse> startDetail(
            @RequestHeader(value = HEADER_TSIGN_CLIENT_ID, required = false) String clientId,
            @RequestHeader(value = HEADER_TSIGN_CLIENT_VERSION, required = false) String clientVersion,
            @RequestHeader(value = HEADER_KEY_APP_NAME, required = false) String appName,
            @PathVariable String flowTemplateId,
            @RequestParam(required = false) String dataId) {

        FlowTemplateGetStartDetailRequest request =  new FlowTemplateGetStartDetailRequest();
        request.setFlowTemplateId(flowTemplateId);
        request.setDataId(dataId);
        request.setSubjectOid(RequestContextExtUtils.getResourceTenantId());
        request.setClientId(clientId);
        request.setAppName(appName);

        ProcessFlowTemplateDetailResponse processStartDetailResponse = flowTemplateService.templateStartDetail(request);

        // 检查或签使用端，移动端暂不支持
        if (processStartDetailResponse.isOrSign()) {
            OrSignValidation.validClient(clientId, clientVersion, appName);
        }
        return BaseResult.success(processStartDetailResponse);
    }

    /**
     * 复制流程模板
     *
     * @param request 入参
     * @return 返回复制后的新流程模板id
     */
    @FunctionPrivilegeCheck(function = "template_copy")
    @ApiOperation(value = "复制流程模板", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/copy", method = RequestMethod.POST)
    @Deprecated
    public BaseResult<CopyFlowTemplateResponse> copy(@RequestBody CopyFlowTemplateRequest request) {
        CopyFlowTemplateResponse response = flowTemplatePlusService.copy(request);
        return BaseResult.success(response);
    }

    /**
     * 复制流程模板
     *
     * @param request 入参
     * @return 返回复制后的新流程模板id
     */
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = TEMPLATE_MANAGE)
    @ApiOperation(value = "导入流程模板", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/import", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ImportFlowTemplateResponse> importFlowTemplate(
            @RequestBody ImportFlowTemplateRequest request) {
        ImportFlowTemplateResponse response = flowTemplatePlusService.importFlowTemplate(request);
        return BaseResult.success(response);
    }

    /**
     * 复制流程模板
     *
     * @param shareCode 共享码
     * @return 返回复制后的新流程模板id
     */
    @ApiOperation(value = "根据分享码获取流程模板分享信息", httpMethod = "GET")
    @RestMapping(path = "/flowTemplates/getShareInfo", method = RequestMethod.GET)
    public BaseResult<FlowTemplateShareInfoResponse> getShareInfo(
            @ApiParam(value = "共享码", required = true) @RequestParam(value = "shareCode")
                    String shareCode) {
        FlowTemplateShareInfoResponse response = flowTemplatePlusService.getShareInfo(shareCode);
        return BaseResult.success(response);
    }

    /**
     * 获取合同流程模板数据链接，后续可以将这个链接导入到其他环境<br>
     * （比如从模拟环境导出的链接，可以导入到线上环境。为了解决API客户在对接完成后，需要在线上环境重新录入合同模板的难题，因为控件很多，客户不想重新录）
     *
     * @return
     */
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = TEMPLATE_MANAGE)
    @ApiOperation(value = "导出流程模板（可用于跨环境导入、导出模板）", httpMethod = "GET")
    @RestMapping(path = "/flowTemplates/{flowTemplateId}/exportUrl", method = RequestMethod.GET)
    public BaseResult<FlowTemplateExportResponse> flowTemplateExportUrl(
            @PathVariable String flowTemplateId,
            @RequestHeader(HEADER_KEY_OPERATOR_ID) String operatorId,
            @RequestHeader(HEADER_KEY_TENANT_ID) String tenantId) {
        String downloadUrl =
                flowTemplatePlusService.exportFlowTemplateInfo(
                        flowTemplateId, tenantId, operatorId);
        return BaseResult.success(
                new FlowTemplateExportResponse(
                        downloadUrl, systemConfig.getFlowTemplateExportUrlExpireMillis()));
    }

    /**
     * 导入合同流程模板，跟{@link #flowTemplateExportUrl(String, String, String)} 接口搭配使用
     *
     * @return
     */
    @ApiOperation(value = "导入流程模板（可用于跨环境导入、导出模板）", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/importByUrl", method = RequestMethod.POST)
    public BaseResult<FlowTemplateImportResponse> flowTemplateImportByUrl(
            @RequestBody FlowTemplateImportRequest request,
            @RequestHeader(HEADER_KEY_OPERATOR_ID) String operatorId,
            @RequestHeader(HEADER_KEY_TENANT_ID) String tenantId) {

        String flowTemplateId =
                flowTemplatePlusService.importFlowTemplateByUrl(request, operatorId, tenantId);
        return BaseResult.success(new FlowTemplateImportResponse(flowTemplateId));
    }

    /**
     * 构建非标模板的临时模板，用于发起时更改模板文件与控件
     * @return
     */
    @ApiOperation(value = "构建非标模板的临时模板（用于发起时更改模板文件与控件）", httpMethod = "POST")
    @RestMapping(path = "/processes/flowTemplates/{flowTemplateId}/build-unstandard-temp", method = RequestMethod.POST)
    public BaseResult<ProcessStartResponse> buildUnStandardTempFlowTemplate(
            @PathVariable String flowTemplateId,
            @RequestBody  SaveFlowTemplateRequest request)

    {
        //以路径上的为准
        request.setRefFlowTemplateId(flowTemplateId);
        //
        request.setSubjectOid(RequestContextExtUtils.getTenantId());
        request.setOperatorOid(RequestContextExtUtils.getOperatorId());
        request.setAppId(RequestContextExtUtils.getAppId());
        request.setAppName(RequestContextExtUtils.getAppName());
        ProcessStartResponse response =
                flowTemplatePlusService.buildUnStandardTempFlowTemplate(request);
        return BaseResult.success(response);
    }


    @ApiOperation(value = "epaas组建下拉列表配置", httpMethod = "POST")
    @RestMapping(path = "/processes/flowTemplates/epaas-struct-choice-list-config", method = RequestMethod.POST)
    public BaseResult<EpaasStructChoiceListConfigResponse> epaasStructChoiceListConfig() {
            return BaseResult.success(flowTemplatePlusService.queryDocTemplatesByFlowTemplateId());
    }
    

}

package com.timevale.contractmanager.biz.service.rest.opponent;

import com.timevale.contractmanager.common.dal.query.base.BaseQuery;
import com.timevale.contractmanager.core.service.opponent.OpponentDevelopService;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.concurrent.CompletableFuture;

/**
 * Created by tianlei on 2022/8/8
 */
@ExternalService
@RestMapping(path = "/v2/opponent/develop")
public class OpponentDevelopRest {

    @Autowired
    private OpponentDevelopService opponentDevelopService;

    @RestMapping(path = "/fixData", method = RequestMethod.GET)
    public BaseResult<String> fixOneTenantRealNameData(
            @RequestParam(required = false) String tenantGid,
            @RequestParam(required = false) Integer entityType,
            @RequestParam Integer size,
            @RequestParam String minCreateTime,
            @RequestParam String maxCreateTime) {
        // 为了集测
        BaseQuery baseQuery = new BaseQuery();
        baseQuery.setPageNum(1);
        baseQuery.setPageSize(1);
        baseQuery.setOrderByClause("order");
        baseQuery.getPageNum();
        baseQuery.getPageSize();
        baseQuery.getStart();
        baseQuery.getOrderByClause();
        CompletableFuture.runAsync(() -> {
            opponentDevelopService.fixData(tenantGid, entityType, size, minCreateTime, maxCreateTime);
        });
        return BaseResult.success("success");
    }
}

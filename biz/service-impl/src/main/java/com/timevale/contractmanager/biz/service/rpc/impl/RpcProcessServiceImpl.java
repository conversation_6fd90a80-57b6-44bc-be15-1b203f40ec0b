package com.timevale.contractmanager.biz.service.rpc.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.timevale.contractmanager.common.dal.bean.*;
import com.timevale.contractmanager.common.dal.bean.grouping.GroupingInfoDO;
import com.timevale.contractmanager.common.service.api.RpcProcessService;
import com.timevale.contractmanager.common.service.bean.*;
import com.timevale.contractmanager.common.service.bean.process.Contract;
import com.timevale.contractmanager.common.service.bean.process.ProcessFileContractCategory;
import com.timevale.contractmanager.common.service.enums.*;
import com.timevale.contractmanager.common.service.enums.grouping.DownloadSourceEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.AuthRelationRpcServiceClient;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.common.service.integration.client.FileSystemClient;
import com.timevale.contractmanager.common.service.model.*;
import com.timevale.contractmanager.common.service.result.*;
import com.timevale.contractmanager.common.utils.DateUtil;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.transfer.RetireTransferBO;
import com.timevale.contractmanager.core.model.dto.process.HandleProcess;
import com.timevale.contractmanager.core.model.dto.process.ProcessDownloadConfigDTO;
import com.timevale.contractmanager.core.model.dto.process.ProcessDownloadConfigInputDTO;
import com.timevale.contractmanager.core.model.dto.request.GetProcessUrlRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessSecretConfigUpdateRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.ProcessDownloadRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessBatchDownloadRequest;
import com.timevale.contractmanager.core.model.dto.response.GetProcessUrlResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessDownloadMapStatsEnum;
import com.timevale.contractmanager.core.service.aop.ProcessChangeNotice;
import com.timevale.contractmanager.core.service.aop.ProcessChangeNoticeContext;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.component.ProcessConfigConverter;
import com.timevale.contractmanager.core.service.contractrelation.ContractFileBizRelationService;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.flow.FlowOperationFactory;
import com.timevale.contractmanager.core.service.flow.bean.param.BatchQueryFlowFilesDTO;
import com.timevale.contractmanager.core.service.flow.bean.param.QueryFlowFilesDTO;
import com.timevale.contractmanager.core.service.flow.bean.result.BatchQueryFlowFilesResult;
import com.timevale.contractmanager.core.service.flow.bean.result.QueryFlowFilesResult;
import com.timevale.contractmanager.core.service.grouping.GroupingFileService;
import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.contractmanager.core.service.other.DocManagerService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.*;
import com.timevale.contractmanager.core.service.process.bean.ProcessDownloadResult;
import com.timevale.contractmanager.core.service.process.bean.ProcessStartRemarkBizRequest;
import com.timevale.contractmanager.core.service.process.datasource.ProcessDataSourceService;
import com.timevale.contractmanager.core.service.process.handler.ProcessDetailHandler;
import com.timevale.contractmanager.core.service.process.handler.ProcessRelationHandler;
import com.timevale.contractmanager.core.service.process.handler.ProcessUpdateHandler;
import com.timevale.contractmanager.core.service.process.handler.bean.ProcessDetailBean;
import com.timevale.contractmanager.core.service.process.impl.factory.ProcessFileAuthServiceFactory;
import com.timevale.contractmanager.core.service.util.ProcessUtils;
import com.timevale.docmanager.service.input.DocListResult;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.footstone.base.model.enums.CommonResultEnum;
import com.timevale.footstone.rpc.enums.SignPlatformEnum;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.*;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.mandarin.common.result.BusinessResult;
import com.timevale.saas.common.enums.SignModeEnum;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationHistoryLastEffectiveTimeDTO;
import com.timevale.signflow.search.docSearchService.bean.ProcessInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdResult;
import com.timevale.threadPoolManager.service.api.ThreadPoolService;
import com.timevale.threadPoolManager.service.model.batchDownload.GetDownloadUrlByCodeModel;
import com.timevale.threadPoolManager.service.result.PackDownLoadResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;
import static com.timevale.contractmanager.core.service.process.impl.fileauth.converter.ProcessFileAuthConverter.buildBatchQueryProcessFileAuthModel;
import static com.timevale.contractmanager.core.service.process.impl.fileauth.converter.ProcessFileAuthConverter.buildDownloadFiles;

/** <AUTHOR> on 2019/7/29 */

@RestService
@Slf4j
public class RpcProcessServiceImpl implements RpcProcessService {

    @Value("${saasAppId}")
    private String saasAppId;

    @Autowired private BaseProcessService baseProcessService;
    @Autowired private ProcessService processService;
    @Autowired private ProcessConfigService processConfigService;
    @Resource private SystemConfig systemConfig;
    @Autowired private FileSystemClient fileSystemClient;
    @Autowired private ProcessRelationHandler processRelationHandler;
    @Autowired private ThreadPoolService threadPoolService;
    @Autowired private ProcessDetailHandler processDetailHandler;
    @Autowired private MapperFactory mapperFactory;
    @Autowired private TransferService transferService;
    @Autowired private ContractFileBizRelationService contractFileBizRelationService;
    @Autowired private FlowTemplateService flowTemplateService;
    @Autowired private ProcessUpdateHandler processUpdateHandler;
    @Autowired private FlowOperationFactory flowOperationFactory;
    @Autowired private DocManagerService docManagerService;
    @Autowired private EsClient esClient;
    @Autowired private ProcessDownloadService processDownloadService;
    @Autowired private UserCenterService userCenterService;
    @Autowired private AuthRelationRpcServiceClient authRelationRpcServiceClient;
    @Autowired private ContractAnalysisClient contractAnalysisClient;
    @Autowired private MenuService menuService;
    @Autowired private GroupingFileService groupingFileService;
    @Autowired private ProcessFileAuthServiceFactory fileAuthServiceFactory;
    @Autowired private ProcessFileAuthService processFileAuthService;
    @Autowired private ProcessDataSourceService dataSourceService;
    @Autowired private ProcessRemarkService processRemarkService;

    @ProcessChangeNotice
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreateProcessResult createNewProcess(AddProcessModel model) {

        String processId = UUIDUtil.genUUID();

        // 处理默认值
        createProcessDefaultValue(model);
        // process
        ProcessDO processDO = buildProcessDO(model, processId);
        ProcessConfigDO processConfigDO = buildProcessConfigDO(model, processId);
        baseProcessService.saveProcess(processDO, processConfigDO);
        // 保存自定义配置
        ProcessCustomizeConfigDO customizeConfigDO = buildProcessCustomizeConfigDO(model, processId);
        if (null != customizeConfigDO) {
            baseProcessService.addCustomizeConfigs(Lists.newArrayList(customizeConfigDO));
        }

        // 保存合同关联信息
        if (CollectionUtils.isNotEmpty(model.getContracts())) {
            saveContractBizRelations(
                    processDO.getSubjectOid(), processDO.getProcessId(), model.getContracts());
        }
        //保存文件权限
        if (CollectionUtils.isNotEmpty(model.getFileAuths())) {
            BatchUpdateProcessFileAuthModel fileAuthModel = new BatchUpdateProcessFileAuthModel();
            AccountSimpleModel subject = new AccountSimpleModel();
            subject.setOid(model.getSubjectOid());
            subject.setGid(model.getSubjectGid());
            fileAuthModel.setProcessId(processId);
            fileAuthModel.setFiles(model.getFileAuths());
            fileAuthModel.setSubject(subject);
            addProcessFileAuth(fileAuthModel);
        }

        saveRemarks(model, processId);

        // 同步合同流程发起后补充的流程信息
        ProcessChangeNoticeContext.setNoticeData(
                processDO.getProcessId(), ProcessChangeTagEnum.PROCESS_INFO_CHANGE);

        return new CreateProcessResult(processId);
    }

    private void saveRemarks(AddProcessModel model, String processId) {
        if (CollectionUtils.isEmpty(model.getRemarks())) {
            return;
        }
        UserAccountDetail userAccountDetailByOid = userCenterService.getUserAccountDetailByOid(model.getInitiatorOid());
        ProcessStartRemarkBizRequest remarkBizRequest = new ProcessStartRemarkBizRequest();
        remarkBizRequest.setProcessId(processId);
        remarkBizRequest.setRemarks(model.getRemarks());
        remarkBizRequest.setAccountId(model.getInitiatorOid());
        remarkBizRequest.setAccountGid(model.getInitiatorGid());
        remarkBizRequest.setAccountName(userAccountDetailByOid.getAccountName());
        remarkBizRequest.setAccountLoginMobile(userAccountDetailByOid.getAccountLoginMobile());
        remarkBizRequest.setAccountLoginEmail(userAccountDetailByOid.getAccountLoginEmail());
        remarkBizRequest.setSubjectId(model.getSubjectOid());
        remarkBizRequest.setSubjectGid(model.getSubjectGid());
        try {
            processRemarkService.processStartAddRemark(remarkBizRequest);
        } catch (Exception e) {
            // 异常不卡流程
            log.error("createNewProcess processStartAddRemark error:{}, processId:{}", e.getMessage(), processId);
        }
    }

    private void createProcessDefaultValue(AddProcessModel model) {
        if (StringUtils.isBlank(model.getSignMode())) {
            model.setSignMode(SignModeEnum.NORMAL.getCode());
        }
    }

    /**
     * 保存合同文件关联信息
     *
     * @param tenantId
     * @param processId
     * @param contracts 合同文件列表
     */
    private void saveContractBizRelations(
            String tenantId, String processId, List<Contract> contracts) {
        try {
        MapperFacade mapperFacade = mapperFactory.getMapperFacade();
        List<FileBO> files = mapperFacade.mapAsList(contracts, FileBO.class);
        flowTemplateService.supplyContractNoType(tenantId, null, null, files);

        // Map<fileId, contractNo>
        Map<String, String> map = contractFileBizRelationService.genContractNo(files);
        if (MapUtils.isEmpty(map)) {
            return;
        }
        Consumer<FileBO> populateContractNo =
                x ->
                        x.setContractNo(
                                StringUtils.isNotBlank(x.getContractNo())
                                        ? x.getContractNo()
                                        : map.get(x.getFileId()));

        // 保存合同文件关联信息
        List<ContractFileBizRelationDO> relations =
                files.stream()
                        .peek(contractFileBizRelationService::amendContractNoType)
                        .peek(populateContractNo)
                        .map(x -> mapperFacade.map(x, ContractFileBizRelationDO.class))
                        .peek(relation -> relation.setOriginFileId(relation.getFileId()))
                        .peek(relation -> relation.setProcessId(processId))
                        .collect(Collectors.toList());
        contractFileBizRelationService.saveBatch(relations);
        } catch (Exception e) {
            log.info("save contract file biz relation error, processId:{}", processId, e);
        }
    }

    private ProcessDO buildProcessDO(AddProcessModel model, String processId) {
        ProcessDO processDO = new ProcessDO();
        processDO.setProcessId(processId);
        processDO.setProcessTitle(model.getProcessTitle());
        processDO.setStatus(model.getProcessStatus());
        processDO.setCreateType(model.getCreateType());

        processDO.setAppId(model.getAppId());
        processDO.setInitiatorOid(model.getInitiatorOid());
        processDO.setInitiatorGid(model.getInitiatorGid());
        processDO.setInitiatorUid(model.getInitiatorUid());
        processDO.setSubjectOid(model.getSubjectOid());
        processDO.setSubjectGid(model.getSubjectGid());
        processDO.setSubjectUid(model.getSubjectUid());
        processDO.setProcessEndTime(DateUtil.long2Date(model.getProcessEndTime()));
        processDO.setContractEndTime(DateUtil.long2Date(model.getContractEndTime()));
        if(StringUtils.isNoneBlank(model.getProcessGroupId())){
            processDO.setProcessGroupId(model.getProcessGroupId());
        }

        return processDO;
    }

    private ProcessConfigDO buildProcessConfigDO(AddProcessModel model, String processId) {
        ProcessConfigDO processConfigDO = new ProcessConfigDO();
        processConfigDO.setProcessId(processId);
        processConfigDO.setProcessComment(model.getProcessComment());
        ProcessConfigBean configBean = new ProcessConfigBean();
        configBean.setRedirectUrl(model.getRedirectUrl());
        if (null != model.getRedirectDelay()) {
            configBean.setRedirectDelay(model.getRedirectDelay());
        }
        configBean.setCanProcessCheck(model.getCanProcessCheck());
        configBean.setContractValidityConfig(model.getContractValidityConfig());
        configBean.setNotifyUrl(model.getProcessNotifyUrl());
        configBean.setNoticeType(model.getNoticeType());
        configBean.setExpectGroupingIds(Lists.newArrayList(model.getGroupingIds()));
        configBean.setPayAccountOid(model.getPayAccountOid());
        configBean.setSignMode(model.getSignMode());
        configBean.setDedicatedCloudId(model.getDedicatedCloudId());
        configBean.setAllowRescind(model.getAllowRescind());
        // 发起端
        ClientEnum clientEnum = ClientEnum.valueOfType(model.getInitiateClient());
        if (null != clientEnum) {
            configBean.setSource(clientEnum.getClientNo());
        }
        //fda
        if (Objects.nonNull(model.getFdaSignatureConfig())) {
            configBean.setFdaSignatureBean(model.getFdaSignatureConfig());
        }
        processConfigDO.setConfigInfo(JSONObject.toJSONString(configBean));
        return processConfigDO;
    }

    /**
     * 组装自定义配置数据库实例
     * @param model
     * @param processId
     * @return
     */
    private ProcessCustomizeConfigDO buildProcessCustomizeConfigDO(AddProcessModel model, String processId) {
        if (StringUtils.isBlank(model.getSubjectGid())) {
            return null;
        }
        ProcessCustomizeConfigDO processConfigDO = new ProcessCustomizeConfigDO();
        processConfigDO.setProcessId(processId);
        processConfigDO.setSubjectOid(model.getSubjectOid());
        processConfigDO.setSubjectGid(model.getSubjectGid());

        ProcessCustomizeConfigBean configBean = new ProcessCustomizeConfigBean();
        configBean.setRenewable(false);
        if (null != model.getSecretType()) {
            configBean.setSecretType(model.getSecretType());
        } else {
            configBean.setSecretType(ProcessSecretEnum.NONE_SECRET.getType());
        }
        checkAndBuildContractCategory(configBean, model.getContracts(), model.getSubjectGid());
        configBean.setSecretFileIds(model.getSecretFileIds());
        configBean.setVisibleAccounts(model.getVisibleAccounts());
        processConfigDO.setConfigInfo(JSONObject.toJSONString(configBean));
        return processConfigDO;
    }

    private void checkAndBuildContractCategory(ProcessCustomizeConfigBean configBean, List<Contract> contracts, String subjectGid) {
        if (CollectionUtils.isEmpty(contracts) || StringUtils.isBlank(subjectGid)) {
            return;
        }

        List<Contract> setCategoryList = contracts.stream().filter(x -> StringUtils.isNotBlank(x.getContractCategoryId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(setCategoryList)) {
            return;
        }

        List<String> categoryIds = setCategoryList.stream().map(Contract::getContractCategoryId).collect(Collectors.toList());
        List<String> notExistCategoryIdList = contractAnalysisClient.getNotExistCategoryIdList(subjectGid, categoryIds);
        categoryIds.removeAll(notExistCategoryIdList);
        if (CollectionUtils.isEmpty(categoryIds)) {
            return;
        }

        List<ProcessFileContractCategory> contractCategories = setCategoryList.stream()
            .filter(x -> categoryIds.contains(x.getContractCategoryId()))
            .map(this::contract2ContractCategory)
            .collect(Collectors.toList());
        configBean.setFileContractCategories(contractCategories);
    }

    private ProcessFileContractCategory contract2ContractCategory(Contract contract) {
        ProcessFileContractCategory processFileContractCategory = new ProcessFileContractCategory();
        processFileContractCategory.setFileId(contract.getFileId());
        processFileContractCategory.setCategoryId(contract.getContractCategoryId());
        return processFileContractCategory;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreateProcessResult createAndAddSubProcess(CreateAndAddSubProcessModel model) {

        String processId = UUIDUtil.genUUID();

        // process
        ProcessDO processDO = buildProcessDO(model, processId);
        ProcessConfigDO processConfigDO = buildProcessConfigDO(model, processId);

        // subProcess
        SubProcessDO subProcessDO = new SubProcessDO();
        subProcessDO.setProcessId(processId);
        subProcessDO.setSubProcessId(model.getSubProcessId());
        subProcessDO.setSubProcessType(model.getSubProcessType());

        baseProcessService.saveProcess(processDO, processConfigDO, subProcessDO);
        // 保存自定义配置
        ProcessCustomizeConfigDO customizeConfigDO = buildProcessCustomizeConfigDO(model, processId);
        if (null != customizeConfigDO) {
            baseProcessService.addCustomizeConfigs(Lists.newArrayList(customizeConfigDO));
        }

        return new CreateProcessResult(processId);
    }

    @Transactional(rollbackFor = Exception.class)
    @ProcessChangeNotice
    @Override
    public void addSubProcess(AddSubProcessModel model) {
        processService.addSubProcess(model.getProcessId(), model.getSubProcessId(), model.getSubProcessType(), model.getProcessStatus());
    }

    @Override
    public ProcessDetailResult detail(ProcessDetailModel model) {
        // 获取主流程id, 如果请求参数只传了子流程id, 需要转换成主流程id
        String processId = getProcessId(model.getByProcessId(), model.getBySubProcessId());
        // 启用缓存模式
        if (systemConfig.getProcessDetailResultCacheSwitch()) {
            // 获取主流程基本信息, 缓存信息中包含子流程信息
            ProcessDetailResult result = getCachedProcessDetailResult(processId);
            // 如果不需要子流程信息， 则清空子流程后返回
            if (model.isWithoutSubProcess()) {
                result.setSubProcesses(null);
            }
            return result;
        }
        // 实时查询模式
        // 返回主流程信息
        return getProcessDetailResult(processId, model.isWithoutSubProcess());
    }

    @Override
    public ProcessBaseResult base(QueryProcessBaseModel model) {
        ProcessDO processDO = baseProcessService.getProcess(model.getProcessId(), null);
        return do2BaseResult(processDO);
    }

    @Override
    public Boolean hasSubProcess(QueryHasSubProcessModel model) {
        return baseProcessService.hasSubProcess(model.getProcessId(), SubProcessTypeEnum.valueOf(model.getSubProcessType()));
    }

    private ProcessBaseResult do2BaseResult(ProcessDO processDO) {
        ProcessBaseResult processBaseResult = new ProcessBaseResult();
        processBaseResult.setAppId(processDO.getAppId());
        processBaseResult.setProcessId(processDO.getProcessId());
        processBaseResult.setThirdProcessNo(processDO.getThirdProcessNo());
        processBaseResult.setProcessTitle(processDO.getProcessTitle());
        processBaseResult.setProcessVersion(processDO.getProcessVersion());
        processBaseResult.setProcessEndTime(processDO.getProcessEndTime());
        processBaseResult.setContractEndTime(processDO.getContractEndTime());
        processBaseResult.setInitiatorId(processDO.getInitiatorOid());
        processBaseResult.setSubjectId(processDO.getSubjectOid());
        processBaseResult.setCreateType(processDO.getCreateType());
        processBaseResult.setStatus(processDO.getStatus());
        return processBaseResult;
    }

    /**
     * 组装详情中需要主流程基本信息
     *
     * @param processId
     * @return
     */
    private ProcessDetailResult getCachedProcessDetailResult(String processId) {
        // 主流程信息缓存key
        String processDetailResultCacheKey =
                CacheUtil.getProcessDetailResultCacheKey(processId);
        // 获取缓存的主流程信息（包含配置信息）
        String cacheData = CacheUtil.degradeGet(processDetailResultCacheKey);
        // 如果缓存信息不为空，直接返回
        if (StringUtils.isNotBlank(cacheData)) {
            log.info("getCachedProcessDetailResult success, processId:{}", processId);
            try {
                return JSONObject.parseObject(cacheData, ProcessDetailResult.class);
            } catch (Exception e) {
                log.warn("ProcessDetailResult parseObject failed", e);
                return null;
            }
        }
        ProcessDetailResult result = getProcessDetailResult(processId, false);
        // 缓存查询数据
        CacheUtil.degradeSet(
                processDetailResultCacheKey,
                JSONObject.toJSONString(result),
                systemConfig.getProcessDetailResultCacheMinutes(),
                TimeUnit.MINUTES);
        return result;
    }

    /**
     * 组装详情中需要返回的主流程信息
     * @param processId
     * @return
     */
    private ProcessDetailResult getProcessDetailResult(String processId, boolean withoutSubProcess) {
        ProcessDetailResult result = new ProcessDetailResult();
        // 获取主流程基本信息
        ProcessDO processDO = baseProcessService.getProcess(processId, null);
        result.setAppId(processDO.getAppId());
        result.setProcessId(processId);
        result.setThirdProcessNo(processDO.getThirdProcessNo());
        result.setProcessTitle(processDO.getProcessTitle());
        result.setInitiatorId(processDO.getInitiatorOid());
        result.setSubjectId(processDO.getSubjectOid());
        result.setCreateType(processDO.getCreateType());
        result.setStatus(processDO.getStatus());
        result.setProcessEndTime(processDO.getProcessEndTime());
        result.setContractEndTime(processDO.getContractEndTime());
        result.setProcessVersion(processDO.getProcessVersion());
        // 获取主流程配置信息
        ProcessConfigDO processConfig = baseProcessService.queryProcessConfig(processId);
        if (null != processConfig) {
            result.setProcessComment(processConfig.getProcessComment());
            result.setTerminateReason(processConfig.getTerminateReason());
            if (StringUtils.isNotBlank(processConfig.getConfigInfo())) {
                result.setProcessConfig(JSONObject.parseObject(processConfig.getConfigInfo(), ProcessConfigBean.class));
            }
        }
        // 判断是否不需要子流程， 若不需要，直接返回
        if (withoutSubProcess) {
            return result;
        }

        // get subProcess
        List<SubProcessDO> subProcessDOS = baseProcessService.getSubProcesses(processId);
        if (CollectionUtils.isNotEmpty(subProcessDOS)) {
            result.setSubProcesses(
                    subProcessDOS.stream()
                            .map(
                                    subProcessDO -> {
                                        ProcessDetailResult.SubProcess subProcess =
                                                new ProcessDetailResult.SubProcess();
                                        subProcess.setSubProcessId(subProcessDO.getSubProcessId());
                                        subProcess.setSubProcessType(
                                                subProcessDO.getSubProcessType());
                                        return subProcess;
                                    })
                            .collect(Collectors.toList()));
        }

        return result;
    }

    /**
     * 查询详情
     *
     * @param processId
     * @return
     */
    @Override
    public QueryProcessDetailResult queryProcessDetail(String processId) {
        // 查询流程详情
        ProcessDetailBean processDetail = processDetailHandler.queryProcessDetail(processId);
        // 组装返回数据
        return mapperFactory.getMapperFacade().map(processDetail, QueryProcessDetailResult.class);
    }

    @Override
    public void updateStatus(UpdateProcessStatusModel model) {
        // 获取合同流程id
        String processId = model.getByProcessId();
        // 如果合同流程id为空 且 合同子流程id不为空，则根据合同子流程id获取合同流程id
        if (StringUtils.isBlank(processId) && StringUtils.isNotBlank(model.getBySubProcessId())) {
            SubProcessDO subProcess = baseProcessService.getSubProcess(model.getBySubProcessId());
            if (null == subProcess) {
                throw new BizContractManagerException(SUB_PROCESS_NOT_EXIST);
            }
            processId = subProcess.getProcessId();
        }
        // 判断最终的合同流程id是否为空， 如果是，报错
        if (StringUtils.isBlank(processId)) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "合同流程id不能为空");
        }
        // 更新合同流程状态
        processUpdateHandler.updateProcessStatus(processId, model.getStatus(), model.getReason());
    }

    @Override
    public void delete(BaseProcessModel model) {
        ProcessDO processDO =
                baseProcessService.getProcess(model.getByProcessId(), model.getBySubProcessId());
        processDO.setStatus(null);
        processDO.setDeleted(1);
        baseProcessService.updateProcess(processDO);
    }

    @Override
    public GenProcessShortUrlResult genShortUrl(GenProcessShortUrlModel model) {

        ProcessDO processDO =
                baseProcessService.getProcess(model.getByProcessId(), model.getBySubProcessId());

        return new GenProcessShortUrlResult(
                processService.genShortUrl(
                        processDO.getProcessId(), model.getAccountId(), model.getSubjectId()));
    }

    @Override
    public void hide(HideProcessModel model) {
        ProcessDO processDO =
                baseProcessService.getProcess(model.getByProcessId(), model.getBySubProcessId());
        SubProcessDO subProcess = baseProcessService.getSubProcess(processDO.getProcessId(), SubProcessTypeEnum.valueOf(model.getSubProcessType()));

        HandleProcess handleProcess = new HandleProcess();
        handleProcess.setAppId(model.getAppId());
        handleProcess.setProcessId(model.getByProcessId());
        handleProcess.setSubProcessId(subProcess.getSubProcessId());
        handleProcess.setSubProcessType(model.getSubProcessType());
        handleProcess.setAccountId(model.getAccountId());
        handleProcess.setSubjectId(model.getSubjectId());

        processService.delete(handleProcess);
    }

    @Override
    public ConvertProcessIdResult convertProcessIds(ConvertProcessIdModel model) {
        List<SubProcessDO> subProcess = baseProcessService.getSubProcess(model.getSubProcessIds());
        ConvertProcessIdResult result = new ConvertProcessIdResult();
        if (!CollectionUtils.isEmpty(subProcess)) {
            Map<String, String> map =
                    subProcess.stream()
                            .collect(
                                    Collectors.toMap(
                                            SubProcessDO::getSubProcessId,
                                            SubProcessDO::getProcessId));
            result.setProcessIds(map);
        }
        return result;
    }

    @Override
    public BusinessResult<BatchExportResult> batchExportResult(BatchExportKeyModel model) {
        BusinessResult<BatchExportResult> result = new BusinessResult<>(CommonResultEnum.SUCCESS.getCode());
        try{
            BatchExportResult batchExport = new BatchExportResult();
            if (StringUtils.isNotBlank(model.getExportFileKey())) {
                batchExport.setExportUrl(fileSystemClient.getDownloadUrl(model.getExportFileKey()));
            }
            if (model.getExportFile()) {
                List<BatchExportKeyModel.ExportFileZipInfo> exportFileZipInfos = model.getExportFileZipInfos();
                if(CollectionUtils.isEmpty(exportFileZipInfos)) {
                    return result;
                }
                List<DownloadContractFileResult> contractFileResults = Lists.newArrayList();
                exportFileZipInfos.forEach(exportFileZipInfo -> {
                    String downloadUrl = getDownloadUrlByLogNo(exportFileZipInfo.getLogNo(), null);
                    DownloadContractFileResult contractFileResult = new DownloadContractFileResult();
                    contractFileResult.setDownloadUrl(downloadUrl);
                    contractFileResult.setZipName(exportFileZipInfo.getZipName());
                    contractFileResults.add(contractFileResult);
                });
                // 将Excel内容下载加入下载列表
                if(StringUtils.isNotBlank(model.getExportFileName()) && StringUtils.isNotBlank(model.getExportFileKey())) {
                    DownloadContractFileResult contractFileResult = new DownloadContractFileResult();
                    contractFileResult.setDownloadUrl(StringUtils.isBlank(batchExport.getExportUrl()) ?
                            fileSystemClient.getDownloadUrl(model.getExportFileKey()) : batchExport.getExportUrl());
                    contractFileResult.setZipName(model.getExportFileName());
                    contractFileResults.add(contractFileResult);
                    batchExport.setExportUrl(contractFileResult.getDownloadUrl());
                }
                batchExport.setContractFiles(contractFileResults);
            }
            result.setData(batchExport);
        }catch (Exception e){
            result.setCode(CommonResultEnum.PARTIAL_FAILURE.getCode());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public BusinessResult<BatchDownloadContractFileResult> batchDownloadContractFiles(BatchDownloadContractFileModel model) {
        /**BusinessResult定义的通用code和前端定义的不一致，使用footstone定义的errorCode */
        BusinessResult<BatchDownloadContractFileResult> result = new BusinessResult<>(CommonResultEnum.SUCCESS.getCode());
        BatchDownloadContractFileResult contractFiles = new BatchDownloadContractFileResult();
        result.setData(contractFiles);

        try{
            if(CollectionUtils.isEmpty(model.getLogNos())){
                return result;
            }
            Map<String, String> logNosDedicatedCloudIdMap =
                    Optional.ofNullable(model.getLogNosDedicatedCloudIdMap()).orElse(new HashMap<>());
            List<String> downloadUrls = Lists.newArrayList();
            for(String logNo:model.getLogNos()){
                String dedicatedCloudId = logNosDedicatedCloudIdMap.get(logNo);
                String downloadUrl = getDownloadUrlByLogNo(logNo, dedicatedCloudId);
                downloadUrls.add(downloadUrl);
            }
            List<DownloadContractFileResult> contractFileResults = Lists.newArrayList();
            for(int idx=0;idx<downloadUrls.size();idx++){
                DownloadContractFileResult item = new DownloadContractFileResult();
                item.setDownloadUrl(downloadUrls.get(idx));
                item.setZipName(model.getZipNames().get(idx));
                contractFileResults.add(item);
            }

            contractFiles.setContractFiles(contractFileResults);
        }catch (Exception e){
            result.setCode(CommonResultEnum.PARTIAL_FAILURE.getCode());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public BusinessResult<QueryProcessDownloadConfigResult> queryProcessDownloadConfig(QueryProcessDownloadConfigModel model) {
        ProcessDownloadConfigInputDTO inputDTO = new ProcessDownloadConfigInputDTO();
        inputDTO.setAccountId(model.getOperatorOid());
        inputDTO.setSubjectId(model.getSubjectOid());
        inputDTO.setDefaultFileNameRuleIfNoSetting(model.isDefaultFileNameRuleIfNoSetting());
        List<ProcessDownloadConfigDTO> downloadConfigDTOS =
                processDownloadService.queryProcessDownloadConfigs(model.getProcessIds(), inputDTO);

        QueryProcessDownloadConfigResult result = new QueryProcessDownloadConfigResult();
        result.setProcessDownloadConfigList(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(downloadConfigDTOS)) {
            downloadConfigDTOS.forEach(i ->{
                QueryProcessDownloadConfigResult.ProcessDownloadConfig config =
                        new QueryProcessDownloadConfigResult.ProcessDownloadConfig();
                config.setProcessId(i.getProcessId());
                config.setDownloadFileName(i.getDownloadFileName());
                config.setSigningDownloadWithSeal(i.isSigningDownloadWithSeal());
                config.setSuitableForFile(i.isSuitableForFile());
                result.getProcessDownloadConfigList().add(config);
            });
        }
        return new BusinessResult<>(CommonResultEnum.SUCCESS.getCode(), null, result);
    }

    @Deprecated
    @Override
    public BusinessResult<QueryRescindProcessInfoResult> queryRescindProcessInfo(QueryRescindProcessInfoModel model){

        QueryRelationProcessInfoModel relationModel = new QueryRelationProcessInfoModel();
        relationModel.setProcessId(model.getProcessId());
        relationModel.setRelationType(RelationTypeEnum.RESCIND.getType());

        QueryRelationProcessInfoResult relationProcessInfo =
                processRelationHandler.queryRelationProcessInfo(relationModel);

        BusinessResult<QueryRescindProcessInfoResult> result =
                new BusinessResult<>(CommonResultEnum.SUCCESS.getCode());
        QueryRescindProcessInfoResult queryRescindProcessInfoResult =
                new QueryRescindProcessInfoResult();
        queryRescindProcessInfoResult.setRescindFlag(
                StringUtils.isNotBlank(relationProcessInfo.getOriginProcessId()));
        queryRescindProcessInfoResult.setOriginFlowId(relationProcessInfo.getOriginFlowId());
        queryRescindProcessInfoResult.setRescindDocList(Lists.newArrayList());
        result.setData(queryRescindProcessInfoResult);

        if (CollectionUtils.isNotEmpty(relationProcessInfo.getRelateDocList())) {
            relationProcessInfo
                    .getRelateDocList()
                    .forEach(
                            i -> {
                                RescindDocInfoBean bean = new RescindDocInfoBean();
                                bean.setFileId(i.getFileId());
                                bean.setStatus(i.getRelateStatus());
                                bean.setStatusDesc(i.getRelateStatusDesc());
                                queryRescindProcessInfoResult.getRescindDocList().add(bean);
                            });
        }

        return result;
    }

    @Override
    public QueryRelationProcessInfoResult queryRelationProcessInfo(
            QueryRelationProcessInfoModel model) {
        return processRelationHandler.queryRelationProcessInfo(model);
    }

    @Override
    public QueryProcessSecretConfigResult queryProcessSecretConfig(
            QueryProcessSecretConfigModel model) {
        // 查询合同保密配置
        ProcessSecretConfigBean secretConfig =
                processConfigService.queryProcessSecretConfig(model.getProcessId());
        // 组装rpc接口返回数据
        QueryProcessSecretConfigResult result = new QueryProcessSecretConfigResult();
        result.setSecretType(secretConfig.getSecretType());
        result.setSecretFileIds(secretConfig.getSecretFileIds());
        result.setVisibleAccounts(secretConfig.getVisibleAccounts());
        return result;
    }

    @Override
    public void updateProcessSecretConfig(UpdateProcessSecretConfigModel model) {
        ProcessDO process = baseProcessService.getProcess(model.getProcessId());
        if (null == process) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST);
        }
        // 更新合同保密配置
        ProcessSecretConfigUpdateRequest request = new ProcessSecretConfigUpdateRequest();
        request.setProcessIds(Lists.newArrayList(process.getProcessId()));
        request.setSecretType(model.getSecretType());
        request.setSecretFileIds(model.getSecretFileIds());
        request.setVisibleAccounts(model.getVisibleAccounts());
        processConfigService.updateProcessSecretConfig(process.getInitiatorOid(), process.getSubjectOid(), request);
    }

    @Override
    public void batchProcessTransfer(BatchProcessTransferModel model) {
        RetireTransferBO.RetireTransferBOBuilder builder =
                RetireTransferBO.builder()
                        .operatorOid(model.getOperatorOid())
                        .taskId(UUIDUtil.genUUID())
                        .tenantId(model.getTenantId())
                        .transferAccountOidList(model.getTransferUserList())
                        .transferToAccountOid(model.getTransferToAccountId())
                        .transferProcessListInfo(
                                model.getTransferProcessInfo() == null
                                        ? null
                                        : new RetireTransferBO.TransferProcessListInfo(
                                                model.getTransferProcessInfo()
                                                        .getTransferAccountOid(),
                                                model.getTransferProcessInfo()
                                                        .getTransferProcessList()))
                        .transferScene(TransferSceneEnum.PROCESS.getCode());
        transferService.batchProcessTransfer(builder.build());
    }

    @Override
    public QueryProcessLogResult queryProcessLog(QueryProcessLogModel model) {
        QueryProcessLogResult result = new QueryProcessLogResult();
        result.setLogs(
                processService.getProcessLogDetails(model.getProcessId(), model.getOperatorId()));
        return result;
    }

    @Override
    public QueryProcessLogResult queryProcessSignLog(QueryProcessSignLogModel model) {
        QueryProcessLogResult result = new QueryProcessLogResult();
        result.setLogs(
                processService.getProcessSignLogDetails(model.getFlowId(), model.getOperatorId()));
        return result;
    }

    @Override
    public void createContractBizRelations(List<ContractBizRelationModel> list) {
        if (CollectionUtils.isEmpty(list) || list.size() > 100) {
            throw new BizContractManagerException("批量操作数量大于100");
        }

        List<ContractFileBizRelationDO> relations =
                mapperFactory.getMapperFacade().mapAsList(list, ContractFileBizRelationDO.class);

        contractFileBizRelationService.saveBatch(relations);
    }

    @Override
    public void invalidContractBizRelations(String processId) {
        if (StringUtils.isBlank(processId)) {
            return;
        }

        contractFileBizRelationService.invalidByProcessId(processId);
    }

    @Override
    public ProcessContractResult listProcessContract(String processId) {
        List<ContractFileBizRelationDO> relations = contractFileBizRelationService.listByProcessId(processId);

        return buildProcessContractResult(processId, relations);
    }

    @Override
    public ProcessContractResult listProcessContract(String processId, List<String> fileIds) {
        List<ContractFileBizRelationDO> relations = contractFileBizRelationService.listByFileIdsAndStatus(processId, fileIds, DeletedEnum.NO.code());

        return buildProcessContractResult(processId, relations);
    }

    @Override
    public QueryProcessFilesResult queryProcessFiles(String processId) {
        QueryProcessFilesResult result = new QueryProcessFilesResult();
        List<SubProcessDO> subProcesses = baseProcessService.getSubProcesses(processId);
        if (CollectionUtils.isEmpty(subProcesses)) {
            log.warn("not exist sub process, processId: {}", processId);
            return result;
        }
        SubProcessDO currentSubProcess = baseProcessService.getCurrentSubProcess(subProcesses);
        QueryFlowFilesResult flowFilesResult = queryFlowFilesResult(currentSubProcess);
        appendFlowFiles(currentSubProcess, flowFilesResult, result, false);
        // 如果当前子流程为填写流程， 直接返回填写流程文件列表即可
        if (SubProcessTypeEnum.COOPERATION.getType() == currentSubProcess.getSubProcessType()) {
            return handleResultFileKeys(result);
        }
        // 获取其他子流程的文件信息
        for (SubProcessDO subProcess : subProcesses) {
            // 如果是当前子流程， 跳过
            if (subProcess.getSubProcessType().equals(currentSubProcess.getSubProcessType())) {
                continue;
            }
            // 查询并追加合同文件
            flowFilesResult = queryFlowFilesResult(subProcess);
            appendFlowFiles(subProcess, flowFilesResult, result, true);
        }
        return handleResultFileKeys(result);
    }

    @Override
    public QueryProcessCheckResult canProcessCheck(QueryProcessCheckModel model) {
        ProcessConfigDO processConfigDO =
                baseProcessService.queryProcessConfig(model.getProcessId());
        if (Objects.isNull(processConfigDO)) {
            return new QueryProcessCheckResult(false);
        }

        ProcessConfigBean processConfigBean =
                ProcessConfigConverter.convertProcessConfig(processConfigDO, false);
        Boolean canProcessCheck =
                Optional.ofNullable(processConfigBean)
                        .map(ProcessConfigBean::isCanProcessCheck)
                        .orElse(false);

        return new QueryProcessCheckResult(canProcessCheck);
    }

    @Override
    public Boolean checkProcessRelevant(CheckProcessRelevantModel model) {
        //查询合同流程
        QueryByProcessIdResult processIdResult = esClient.queryById(model.getProcessId());
        ProcessInfoTotalInfo processInfo = Optional.ofNullable(processIdResult)
                .map(QueryByProcessIdResult::getProcessInfoTotalInfo)
                .orElseThrow(() -> new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST));

        //查询用户数据
        Map<String, UserAccountDetail> accountMap =
                userCenterService.queryAccountMapByAccountIds(
                        Sets.newHashSet(model.getAccountId(), model.getSubjectId()));
        UserAccount operator = accountMap.get(model.getAccountId());
        UserAccount tenant = accountMap.get(model.getSubjectId());

        //子企业列表
        List<AuthRelationHistoryLastEffectiveTimeDTO> childTenantList =
                authRelationRpcServiceClient.queryAuthRelationLastEffectiveTimeByTenantGid(
                        tenant.getAccountGid());
        List<UserAccount> childAccounts =
                childTenantList.stream()
                        .map(
                                child -> {
                                    UserAccount userAccount = new UserAccount();
                                    userAccount.setAccountGid(child.getChildTenantGid());
                                    userAccount.setAccountName(child.getChildTenantName());
                                    userAccount.setAccountOid(child.getChildTenantOid());
                                    return userAccount;
                                })
                        .collect(Collectors.toList());

        //检验是否为合同相关人
        return processService.checkRelevantPerson(processInfo, operator, tenant, childAccounts, BooleanUtils.isTrue(model.getInvolveCC()));
    }

    @Override
    public Boolean checkProcessSubjectRelevant(CheckProcessSubjectRelevantModel model) {
        CheckRelevantParam param = queryRelevantParam(model.getProcessId(), model.getSubjectId());

        //检验是否为合同相关人
        return ProcessUtils.checkRelevantSubject(param.getTotalInfo(), param.getTenantAccount(), param.getChildTenantAccounts());
    }

    @Override
    public Boolean checkInitiatorSubject(CheckProcessSubjectRelevantModel model) {
        CheckRelevantParam param = queryRelevantParam(model.getProcessId(), model.getSubjectId());

        //检验是否为发起方企业
        return ProcessUtils.checkInitiatorSubject(param.getTotalInfo(), param.getTenantAccount(), param.getChildTenantAccounts());
    }

    @Override
    public ProcessConfigResult queryProcessConfig(String processId) {
        ProcessConfigDO processConfig = baseProcessService.queryProcessConfig(processId);
        if (processConfig == null) {
            return null;
        }
        // 组装返回值并返回
        return convert2ProcessConfigResult(processConfig);
    }

    /**
     * 组装流程配置信息返回值
     * @param processConfig
     * @return
     */
    private ProcessConfigResult convert2ProcessConfigResult(ProcessConfigDO processConfig) {
        ProcessConfigResult processConfigResult = new ProcessConfigResult();
        processConfigResult.setProcessId(processConfig.getProcessId());
        processConfigResult.setProcessComment(processConfig.getProcessComment());
        processConfigResult.setTerminateReason(processConfig.getTerminateReason());
        if (StringUtils.isNotBlank(processConfig.getConfigInfo())) {
            processConfigResult.setConfigInfo(JSONObject.parseObject(processConfig.getConfigInfo(), ProcessConfigBean.class));
        }
        return processConfigResult;
    }

    @Override
    public CheckContractResourceOwnerResult checkContractResourceOwner(CheckContractResourceOwnerModel model) {
        CheckContractResourceOwnerResult result = new CheckContractResourceOwnerResult();

        AtomicReference<String> subjectGid = new AtomicReference<>(model.getSubjectGid());
        // 合同类型
        if (CollectionUtils.isNotEmpty(model.getContractCatagoryList())) {
            queryNotExistsCategoryList(model, result, subjectGid);
        }

        // 归档菜单
        if (CollectionUtils.isNotEmpty(model.getGroupingIds())) {
            result.setNotOwnerGroupingId(menuService.getNotExistMenuIds(model.getSubjectOid(), model.getGroupingIds()));
        }
        return result;
    }

    private void queryNotExistsCategoryList(CheckContractResourceOwnerModel model, CheckContractResourceOwnerResult result, AtomicReference<String> subjectGid) {
        if (StringUtils.isBlank(subjectGid.get())) {
            subjectGid.set(userCenterService.getUserAccountBaseByOid(model.getSubjectOid()).getAccountGid());
        }
        if (StringUtils.isBlank(subjectGid.get())) {
            result.setNotOwnerContractCategoryId(model.getContractCatagoryList());
            return;
        }
        List<String> notExistCategoryIdList = contractAnalysisClient.getNotExistCategoryIdList(subjectGid.get(), model.getContractCatagoryList());
        result.setNotOwnerContractCategoryId(notExistCategoryIdList);
    }

    @Override
    public QueryProcessContractRelInfoResult queryProcessContractRelInfo(QueryProcessContractRelInfoModel model) {
        QueryProcessContractRelInfoResult result = new QueryProcessContractRelInfoResult();
        if (BooleanUtils.isTrue(model.getWithContracts())) {
            List<ContractFileBizRelationDO> relations = contractFileBizRelationService.listByProcessId(model.getProcessId());
            List<ProcessContractModel> contracts =
                mapperFactory
                        .getMapperFacade()
                        .mapAsList(relations, ProcessContractModel.class);
            result.setContracts(contracts);
        }

        if (BooleanUtils.isTrue(model.getWithGrouping())) {
            List<GroupingInfoDO> groupingInfoList = groupingFileService.getGroupingInfoList(model.getProcessId(), model.getSubjectOid());
            List<String> groupingIds = groupingInfoList.stream().map(GroupingInfoDO::getMenuId).collect(Collectors.toList());
            result.setGroupingIds(groupingIds);
        }
        String subjectGid = model.getSubjectGid();
        if (BooleanUtils.isTrue(model.getWithContractCategory())) {
            if (StringUtils.isBlank(subjectGid)) {
                subjectGid = userCenterService.getUserAccountBaseByOid(model.getSubjectOid()).getAccountGid();
            }
            if (CollectionUtils.isEmpty(result.getContracts())) {
                result.setContracts(Lists.newArrayList());
            }
            buildContractCategory(result.getContracts(), model.getProcessId(), subjectGid);
        }
        return result;
    }

    private CheckRelevantParam queryRelevantParam(String processId, String subjectId) {
        CheckRelevantParam param = new CheckRelevantParam();

        //查询合同流程
        QueryByProcessIdResult processIdResult = esClient.queryById(processId);
        ProcessInfoTotalInfo processInfo = Optional.ofNullable(processIdResult)
                .map(QueryByProcessIdResult::getProcessInfoTotalInfo)
                .orElseThrow(() -> new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST));

        UserAccount tenant = userCenterService.getUserAccountBaseByOid(subjectId);

        //子企业列表
        List<AuthRelationHistoryLastEffectiveTimeDTO> childTenantList =
                authRelationRpcServiceClient.queryAuthRelationLastEffectiveTimeByTenantGid(
                        tenant.getAccountGid());
        List<UserAccount> childAccounts =
                childTenantList.stream()
                        .map(
                                child -> {
                                    UserAccount userAccount = new UserAccount();
                                    userAccount.setAccountGid(child.getChildTenantGid());
                                    userAccount.setAccountName(child.getChildTenantName());
                                    userAccount.setAccountOid(child.getChildTenantOid());
                                    return userAccount;
                                })
                        .collect(Collectors.toList());
        param.setTotalInfo(processInfo);
        param.setChildTenantAccounts(childAccounts);
        param.setTenantAccount(tenant);
        return param;
    }

    //注释
    private void buildContractCategory(List<ProcessContractModel> contracts, String processId, String subjectGid) {
        if (StringUtils.isBlank(subjectGid)) {
            return;
        }
        ProcessCustomizeConfigDO configDO = baseProcessService.queryCustomizeConfigs(processId, subjectGid);
        List<ProcessFileContractCategory> contractCategories = ProcessConfigConverter.convertFileContractCategories(configDO);
        if (CollectionUtils.isEmpty(contractCategories)) {
            return;
        }

        Map<String, String> fileIdMap = contractCategories.stream()
                .collect(Collectors.toMap(ProcessFileContractCategory::getFileId, ProcessFileContractCategory::getCategoryId, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(contracts)) {
            contracts.stream()
                    .filter(contract -> fileIdMap.containsKey(contract.getFileId()))
                    .forEach(contract -> {
                        contract.setContractCategoryId(fileIdMap.get(contract.getFileId()));
                    });
            return;
        }

        //合同文件列表为空时需要组装列表
        contracts.addAll(fileIdMap.entrySet().stream()
              .map(entry -> {
                    ProcessContractModel contract = new ProcessContractModel();
                    contract.setFileId(entry.getKey());
                    contract.setContractCategoryId(entry.getValue());
                    return contract;
                })
              .collect(Collectors.toList()));
    }

    @Override
    public BatchProcessConfigResult batchQueryProcessConfig(BatchQueryProcessConfigModel model) {
        List<ProcessConfigDO> processConfigDOS =
                baseProcessService.queryProcessConfigs(model.getProcessIds());
        BatchProcessConfigResult result = new BatchProcessConfigResult();
        result.setProcessConfigs(Lists.newArrayList());
        if (CollectionUtils.isEmpty(processConfigDOS)) {
            return result;
        }
        result.setProcessConfigs(
                processConfigDOS.stream()
                        .map(i -> convert2ProcessConfigResult(i))
                        .collect(Collectors.toList()));
        return result;
    }

    @Override
    public ProcessFileAuthResult queryProcessFileAuth(QueryProcessFileAuthModel model) {
        return processService.queryProcessFileAuth(model);
    }

    @Override
    public void addProcessFileAuth(BatchUpdateProcessFileAuthModel model) {
        processFileAuthService.updateProcessFileAuth(model);
    }

    @Override
    public void deleteProcessFileAuth(BatchDeleteProcessFileAuthModel model) {
        processFileAuthService.deleteProcessFileAuth(model);
    }

    @Override
    public void appendAttachmentFileAuth(AppendProcessAttachmentAuthModel model) {
        processFileAuthService.appendAttachmentFileAuth(model.getProcessId(), model.getSubject(), model.getFlowId());
    }

    @Override
    public List<ProcessFileAuthBean> queryFileAuthByProcessId(String processId) {
        return processFileAuthService.queryFileAuthByProcessId(processId);
    }

    @Override
    public SubProcessResult getCurrentSubProcess(QueryProcessBaseModel model) {
        SubProcessDO currentSubProcess = baseProcessService.getCurrentSubProcess(model.getProcessId());
        return mapperFactory.getMapperFacade().map(currentSubProcess, SubProcessResult.class);
    }

    @Override
    public BatchQueryDownloadFilesResult batchQueryDownloadFiles(BatchQueryDownloadFilesModel model) {
        BatchQueryDownloadFilesResult result = new BatchQueryDownloadFilesResult();
        List<String> processIds = Lists.newArrayList();
        Map<Integer, List<BatchQueryDownloadFilesModel.DownloadFlow>> flowIdMap =
                model.getDownloadFlows().stream().peek(i -> processIds.add(i.getProcessId())).collect(Collectors.groupingBy(i -> i.getFlowType()));
        UserAccount account = userCenterService.getUserAccountBaseByOid(model.getAccountId());
        UserAccount subject = userCenterService.getUserAccountBaseByOid(model.getSubjectId());
        // 查询附属文件权限配置
        BatchQueryProcessFileAuthModel attachmentFileAuthModel = buildBatchQueryProcessFileAuthModel(ProcessAuthFileTypeEnum.ATTACHMENT, processIds, false, account, subject);
        Map<String, ProcessFileAuthResult> attachmentFileAuthMap = fileAuthServiceFactory.getFileAuthTypeService(ProcessAuthFileTypeEnum.ATTACHMENT.getType()).batchQueryAuthResult(attachmentFileAuthModel);
        // 查询签署文件权限配置
        BatchQueryProcessFileAuthModel signFileAuthModel = buildBatchQueryProcessFileAuthModel(ProcessAuthFileTypeEnum.SIGN, processIds, false, account, subject);
        Map<String, ProcessFileAuthResult> signFileAuthMap = fileAuthServiceFactory.getFileAuthTypeService(ProcessAuthFileTypeEnum.SIGN.getType()).batchQueryAuthResult(signFileAuthModel);

        // 目前最多循环3次， 线下合同流程、审批流程及签署流程
        flowIdMap.forEach((flowType, downloadFlows) -> {
            List<String> flowIds = downloadFlows.stream().map(i -> i.getFlowId()).collect(Collectors.toList());
            BatchQueryFlowFilesDTO input = new BatchQueryFlowFilesDTO();
            input.setFlowIds(flowIds);
            BatchQueryFlowFilesResult filesResult = flowOperationFactory.getService(flowType).batchQueryFlowFiles(input);
            downloadFlows.forEach(flow -> {
                QueryFlowFilesResult flowFiles = filesResult.getFlowFilesMap().get(flow.getFlowId());
                if (null == flowFiles) {
                    return;
                }
                ProcessFileAuthResult attachmentFileAuth = attachmentFileAuthMap.get(flow.getProcessId());
                ProcessFileAuthResult signFileAuth = signFileAuthMap.get(flow.getProcessId());
                DownloadFilesBean downloadFilesBean = new DownloadFilesBean();
                downloadFilesBean.setProcessId(flow.getProcessId());
                downloadFilesBean.setFlowId(flow.getFlowId());
                downloadFilesBean.setFlowType(flow.getFlowType());
                downloadFilesBean.setFiles(buildDownloadFiles(flowFiles, attachmentFileAuth, signFileAuth));
                result.getDownloadFiles().add(downloadFilesBean);
            });
        });
        // 处理补充fileKey
        handleResultFileKeys(result);

        return result;
    }

    @Override
    public ProcessBatchDownloadResult processBatchDownload(ProcessBatchDownloadModel model) {
        // 构建下载参数
        ProcessBatchDownloadRequest batchDownloadRequest = new ProcessBatchDownloadRequest();
        batchDownloadRequest.setProcessIds(model.getProcessIds());
        batchDownloadRequest.setSource(DownloadSourceEnum.RPC.getType());

        ProcessDownloadRequest downloadRequest = new ProcessDownloadRequest();
        downloadRequest.setClientId(model.getClientId());
        downloadRequest.setDownloadScene(ProcessDownloadScene.BATCH.getScene());
        downloadRequest.setDownloadMapStatus(ProcessDownloadMapStatsEnum.ALL.getCode());
        downloadRequest.setDownloadParam(JsonUtils.obj2pojo(batchDownloadRequest, JSONObject.class));
        // 执行下载
        String appId = Optional.ofNullable(model.getAppId()).orElse(saasAppId);
        ProcessDownloadResult downloadResult =
                processDownloadService.download(appId, model.getAccountId(), model.getSubjectId(), downloadRequest);
        // 返回下载码
        ProcessBatchDownloadResult result = new ProcessBatchDownloadResult();
        result.setDownloadCode(downloadResult.getDownloadCode());
        return result;
    }

    @Override
    public ProcessBatchDownloadUrlResult processBatchDownloadUrl(ProcessBatchDownloadUrlModel model) {
        // 获取下载地址
        String downloadScene = ProcessDownloadScene.BATCH.getScene();
        ProcessBatchDownloadUrlResult result = new ProcessBatchDownloadUrlResult();
        try {
            String downloadUrl =
                    processDownloadService.downloadUrl(model.getAccountId(), model.getSubjectId(), downloadScene, model.getDownloadCode());
            result.setComplete(StringUtils.isNotBlank(downloadUrl));
            result.setDownloadUrl(downloadUrl);
        } catch (BizContractManagerException e) {
            result.setComplete(true);
            result.setFailReason(e.getMessage());
        }
        // 返回下载地址
        return result;
    }

    @Override
    public GenProcessContractNoResult genContractNo(GenProcessContractNoModel model) {

        MapperFacade mapperFacade = mapperFactory.getMapperFacade();
        List<FileBO> files = mapperFacade.mapAsList(model.getContracts(), FileBO.class);
        Map<String, String> map = contractFileBizRelationService.genContractNo(files);
        GenProcessContractNoResult result = new GenProcessContractNoResult();
        result.setContractNoMap(map);
        return result;
    }

    /**
     * 兜底填充文件fileKey
     *
     * @param result
     */
    private QueryProcessFilesResult handleResultFileKeys(QueryProcessFilesResult result) {
        List<String> fileIds =
                result.getProcessFiles().stream()
                        .filter(i -> StringUtils.isBlank(i.getFileKey()))
                        .map(item -> item.getFileId())
                        .collect(Collectors.toList());
        // 如果不存在fileKey为空的文件列表， 直接返回
        if (CollectionUtils.isEmpty(fileIds)) {
            return result;
        }
        // 查询文件信息
        List<DocListResult> docList = docManagerService.getDocListByIds(fileIds);
        // 填充文件fileKey
        if (CollectionUtils.isNotEmpty(docList)) {
            Map<String, String> docMap =
                    docList.stream()
                            .collect(Collectors.toMap(i -> i.getDocId(), i -> i.getFileKey()));
            result.getProcessFiles().stream()
                    .filter(i -> StringUtils.isBlank(i.getFileKey()))
                    .forEach(i -> i.setFileKey(docMap.get(i.getFileId())));
        }
        return result;
    }

    /**
     * 兜底填充文件fileKey
     *
     * @param result
     */
    private BatchQueryDownloadFilesResult handleResultFileKeys(BatchQueryDownloadFilesResult result) {
        List<String> fileIds = Lists.newArrayList();
        result.getDownloadFiles().forEach(f -> fileIds.addAll(f.getFiles().stream()
                        .filter(i -> StringUtils.isBlank(i.getFileKey()))
                        .map(item -> item.getFileId())
                        .collect(Collectors.toList())));
        // 如果不存在fileKey为空的文件列表， 直接返回
        if (CollectionUtils.isEmpty(fileIds)) {
            return result;
        }
        log.info("handleResultFileKeys queryDocList , size: {}", fileIds.size());
        // 查询文件信息
        List<DocListResult> docList = docManagerService.getDocListByIds(fileIds);
        // 填充文件fileKey
        if (CollectionUtils.isNotEmpty(docList)) {
            Map<String, String> docMap =
                    docList.stream()
                            .collect(Collectors.toMap(i -> i.getDocId(), i -> i.getFileKey()));
            result.getDownloadFiles().forEach(f -> f.getFiles().stream()
                    .filter(i -> StringUtils.isBlank(i.getFileKey()))
                    .forEach(i -> i.setFileKey(docMap.get(i.getFileId()))));
        }
        return result;
    }

    /**
     * 查询子流程文件列表
     * @param currentSubProcess
     * @return
     */
    private QueryFlowFilesResult queryFlowFilesResult(SubProcessDO currentSubProcess) {
        QueryFlowFilesDTO filesParam = new QueryFlowFilesDTO();
        filesParam.setProcessId(currentSubProcess.getProcessId());
        filesParam.setFlowId(currentSubProcess.getSubProcessId());
        return flowOperationFactory.getService(currentSubProcess).queryFlowFiles(filesParam);
    }

    /**
     * 追加流程文件
     *
     * @param flowFilesResult
     * @param result
     */
    private void appendFlowFiles(
            SubProcessDO subProcess,
            QueryFlowFilesResult flowFilesResult,
            QueryProcessFilesResult result,
            boolean skipAttachment) {
        if (CollectionUtils.isEmpty(flowFilesResult.getFlowFiles())) {
            return;
        }
        for (QueryFlowFilesResult.FlowFile flowFile : flowFilesResult.getFlowFiles()) {
            if (skipAttachment && flowFile.isAttachment()) {
                continue;
            }
            QueryProcessFilesResult.ProcessFile processFile = new QueryProcessFilesResult.ProcessFile();
            processFile.setFlowType(subProcess.getSubProcessType());
            processFile.setFileId(flowFile.getFileId());
            processFile.setFileKey(flowFile.getFileKey());
            processFile.setFileName(flowFile.getFileName());
            processFile.setAttachment(flowFile.isAttachment());
            result.getProcessFiles().add(processFile);
        }
    }

    /**
     * 构建合同文件信息查询结果
     *
     * @param processId
     * @param relations
     * @return
     */
    private ProcessContractResult buildProcessContractResult(
            String processId, List<ContractFileBizRelationDO> relations) {
        if (CollectionUtils.isEmpty(relations)) {
            return new ProcessContractResult(processId, Collections.emptyList());
        }

        List<ProcessContractResult.ContractModel> contracts =
                mapperFactory
                        .getMapperFacade()
                        .mapAsList(relations, ProcessContractResult.ContractModel.class);

        return new ProcessContractResult(processId, contracts);
    }

    /**
     * logNo对应的下载地址会过期，为了避免中间件压力，缓存logNo与downloadUrl
     * @param logNo
     * @return
     */
    private String getDownloadUrlByLogNo(String logNo, String dedicatedCloudId){
        Object exist = TedisUtil.get(logNo);
        if (exist == null) {
            GetDownloadUrlByCodeModel getDownloadUrlByCodeModel = new GetDownloadUrlByCodeModel();
            getDownloadUrlByCodeModel.setDownLoadCode(logNo);
            if (StringUtils.isNotBlank(dedicatedCloudId)) {
                getDownloadUrlByCodeModel.setDedicatedCloudId(dedicatedCloudId);
            }
            PackDownLoadResult downLoadResult =  threadPoolService.getDownloadUrlByCode(getDownloadUrlByCodeModel);
            if(!downLoadResult.isSuccess() || StringUtils.isBlank(downLoadResult.getDownloadUrl())){
                throw new BizContractManagerException("文件准备中，请稍后。。。");
            }
            TedisUtil.set(logNo, downLoadResult.getDownloadUrl(), 5, TimeUnit.DAYS);
            /**避免被限流 每次请求成功后sleep 5ms */
            try{
                TimeUnit.MILLISECONDS.sleep(5);
            }catch (Exception e){}

            return downLoadResult.getDownloadUrl();
        }
        return exist.toString();
    }

    /**
     * 获取主流程id
     * @param processId
     * @param subProcessId
     * @return
     */
    private String getProcessId(String processId, String subProcessId) {
        if (StringUtils.isNotBlank(processId)) {
            return processId;
        }
        if (StringUtils.isBlank(subProcessId)) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }
        SubProcessDO subProcessDO = baseProcessService.getSubProcess(subProcessId);
        if (subProcessDO == null) {
            throw new BizContractManagerException(SUB_PROCESS_NOT_EXIST);
        }
        return subProcessDO.getProcessId();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class CheckRelevantParam{
        private ProcessInfoTotalInfo totalInfo;
        private UserAccount tenantAccount;
        private List<UserAccount> childTenantAccounts;
    }

    @Override
    public DataSourceStartUrlResult dataSourceStartUrl(DataSourceStartUrlModel request) {
        return new DataSourceStartUrlResult(dataSourceService.url(request.getDataId(), request.getDataSourceDataId()));
    }

    @Override
    public ProcessStartUrlResult processStartUrl(ProcessStartUrlModel request) {
        return new ProcessStartUrlResult(dataSourceService.url(request.getDataId(), request.getDataSourceDataId()));
    }

    @Override
    public ProcessUrlResult processUrl(ProcessUrlModel request) {
        GetProcessUrlRequest getProcessUrlRequest = new GetProcessUrlRequest();
        getProcessUrlRequest.setProcessId(request.getProcessId());
        getProcessUrlRequest.setAppId(Optional.ofNullable(request.getAppId()).orElse(saasAppId));
        getProcessUrlRequest.setAccountId(request.getOperatorOid());
        getProcessUrlRequest.setSubjectId(request.getSubjectOid());
        getProcessUrlRequest.setToken(request.getToken());
        getProcessUrlRequest.setClient(request.getClientId() == null ? ClientEnum.WEB.getClientId() : request.getClientId());
        getProcessUrlRequest.setPlatform(request.getPlatform() == null ? SignPlatformEnum.STANDARD_WEB.getPlatform() : request.getPlatform());
        GetProcessUrlResponse response = processService.getUrl(getProcessUrlRequest, request.getDetail() == null ? Boolean.TRUE : request.getDetail());
        ProcessUrlResult result = new ProcessUrlResult();
        result.setUrl(response.getUrl());
        result.setLongUrl(response.getLongUrl());
        return result;
    }

}

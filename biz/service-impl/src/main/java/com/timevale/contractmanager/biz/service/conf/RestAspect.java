package com.timevale.contractmanager.biz.service.conf;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能说明：
 *
 * @return <br>
 *     修改历史：<br>
 *     1.[2018年05月04日上午13:51] 创建方法 by chenxing
 */
@Aspect
@Component
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RestAspect extends BaseRestAspect {

    /** 定义一个切入点 */
    @Pointcut("execution (* com.timevale.contractmanager.biz.service.rest..*.*(..))")
    public void doAspect() {}

    /**
     * 功能说明：
     *
     * @return <br>
     *     修改历史<br>
     *     [2018年12月05日 10:15:56 10:15] 创建方法by fengqingyang
     * @params
     */
    @Around("doAspect()")
    public Object doAspect(ProceedingJoinPoint pjp) throws Throwable {
        return handle(pjp, null);
    }
}

package com.timevale.contractmanager.biz.service.rest;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.DocRelateInfo;
import com.timevale.contractmanager.common.service.bean.DocRescindInfo;
import com.timevale.contractmanager.common.service.enums.BackFillTypeEnum;
import com.timevale.contractmanager.common.service.enums.RelationTypeEnum;
import com.timevale.contractmanager.core.model.dto.request.CacheSelectedFilesRequest;
import com.timevale.contractmanager.core.model.dto.request.GenerateRescindDocRequest;
import com.timevale.contractmanager.core.model.dto.response.*;
import com.timevale.contractmanager.core.service.process.handler.ProcessBackFillHandler;
import com.timevale.contractmanager.core.service.process.handler.ProcessRelationHandler;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * 合同解约管理rest接口
 *
 * <AUTHOR> on 2021/06/21
 */
@Api(tags = "合同解约管理", description = "合同解约管理")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class ProcessRescindRest extends BaseRest {

    @Autowired private ProcessBackFillHandler processBackFillHandler;

    @Autowired private ProcessRelationHandler processRelationHandler;

    /**
     * 流程待解约合同查询
     *
     * @param processId 流程id
     * @return 解约记录
     */
    @RestMapping(path = "/processes/{processId}/rescindFileList", method = RequestMethod.GET)
    @ApiOperation(value = "获取原流程待解约文档列表", httpMethod = "GET")
    public BaseResult<QueryRescindFileListResponse> queryRescindFileList(
            @PathVariable(name = "processId") String processId,
            @RequestParam(value = "menuId", required = false) String menuId) {
        processRelationHandler.checkProcessCanRelate(
                processId, menuId, RelationTypeEnum.RESCIND.getType());

        QueryRelationFileListResponse relationFileListResponse =
                processRelationHandler.queryRelationFileList(
                        processId, RelationTypeEnum.RESCIND.getType());

        QueryRescindFileListResponse rescindFileListResponse = new QueryRescindFileListResponse();
        rescindFileListResponse.setFiles(Lists.newArrayList());
        rescindFileListResponse.setOriginProcessStatus(
                relationFileListResponse.getOriginProcessStatus());

        for (DocRelateInfo file : relationFileListResponse.getFiles()) {
            DocRescindInfo rescindInfo = new DocRescindInfo();
            rescindInfo.setDocId(file.getDocId());
            rescindInfo.setFileKey(file.getFileKey());
            rescindInfo.setFileId(file.getFileId());
            rescindInfo.setFileName(file.getFileName());
            rescindInfo.setRelateType(file.getRelateType());
            rescindInfo.setRelateStatus(file.getRelateStatus());
            rescindInfo.setRescindStatus(file.getRelateStatus());
            rescindInfo.setFrom(file.getFrom());
            rescindFileListResponse.getFiles().add(rescindInfo);
        }

        return BaseResult.success(rescindFileListResponse);
    }

    /**
     * 解约流程发起页面原流程信息回填
     *
     * @param processId 流程id
     * @return 解约记录
     */
    @RestMapping(path = "/processes/{processId}/rescindBackfill", method = RequestMethod.GET)
    @ApiOperation(value = "解约流程发起页面原流程信息回填", httpMethod = "GET")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ProcessStartDetailResponse> rescindBackfill(
            @PathVariable(name = "processId") String processId,
            @RequestParam(value = "menuId", required = false) String menuId) {
        String subjectOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(processBackFillHandler.getBackFillInfo(processId,
                BackFillTypeEnum.RESCIND_PROCESS_BACKFILL.getType(),menuId,subjectOid,operatorOid));
    }

    /**
     * 生成解约文件
     *
     * @param request 请求体
     * @return 解约记录
     */
    @RestMapping(path = "/processes/{processId}/generateRescindFile", method = RequestMethod.POST)
    @ApiOperation(value = "生成解约流程合同文件", httpMethod = "POST")
    public BaseResult<GenerateRescindDocResponse> generateRescindFile(
            @RequestHeader(value = "X-Tsign-Open-App-Id") String appId,
            @RequestHeader(value = "X-Tsign-Open-Tenant-Id") String tenantId,
            @RequestBody GenerateRescindDocRequest request) {
        return BaseResult.success(
                processRelationHandler.generateRescindFile(appId, tenantId, request));
    }

    @ApiOperation(value = "缓存解约时选择的原文件列表", httpMethod = "POST")
    @RestMapping(path = "/processes/rescind-selected-files/cache", method = RequestMethod.POST)
    public BaseResult<CacheSelectedFilesResponse> cacheRescindSelectedFiles(
            @RequestBody CacheSelectedFilesRequest param) {
        String accountId = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(
                processRelationHandler.cacheSelectedFiles(accountId, RelationTypeEnum.RESCIND, param));
    }

    @ApiOperation(value = "获取解约时缓存的原文件列表", httpMethod = "GET")
    @RestMapping(path = "/processes/rescind-selected-files/get", method = RequestMethod.GET)
    public BaseResult<GetSelectedFilesResponse> getRescindSelectedFiles(
            @RequestParam String serialKey) {
        String accountId = RequestContextExtUtils.getOperatorId();
        return BaseResult.success(
                processRelationHandler.getSelectedFiles(accountId, RelationTypeEnum.RESCIND, serialKey));
    }
}

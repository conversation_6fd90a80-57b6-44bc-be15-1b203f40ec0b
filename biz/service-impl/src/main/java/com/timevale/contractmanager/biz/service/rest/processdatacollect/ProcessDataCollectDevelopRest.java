package com.timevale.contractmanager.biz.service.rest.processdatacollect;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectMsg;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollector;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSaveParam;
import io.swagger.annotations.Api;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Created by tianlei on 2022/5/17
 */
@Api(tags = "分享下载")
@ExternalService
@RestMapping(path = "/v1/process-data-collect")
@Slf4j
public class ProcessDataCollectDevelopRest {

    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ProcessDataCollector processDataCollector;

    // 查询原始数据
    @RestMapping(path = "/build-init-data", method = RequestMethod.GET)
    public BaseResult<ContractProcessSaveParam> buildInitData(@RequestParam String processId) {
        ContractProcessSaveParam param = processDataBuilder.buildProcessInfoParam(processId);
        return BaseResult.success(param);
    }

    @RestMapping(path = "/receive-msg", method = RequestMethod.POST)
    public BaseResult<Boolean> receiveMsg(@RequestBody ProcessDataCollectMsgTmp msgTmp) {

        ProcessDataCollectMsg msg = new ProcessDataCollectMsg();
        msg.setMsg(JSON.toJSONString(msgTmp.getMsg()));
        msg.setTopic(msgTmp.getTopic());
        msg.setTag(msgTmp.getTag());
        if (msgTmp.getReconsumeTimes() != null) {
            msg.setReconsumeTimes(msgTmp.getReconsumeTimes());
        }
        if (msgTmp.getShadowTopic() != null) {
            msg.setShadowTopic(msgTmp.getShadowTopic());
        }
        boolean result = processDataCollector.collect(msg);
        return BaseResult.success(result);
    }


    @RestMapping(path = "/delete-redis-cache", method = RequestMethod.GET)
    public BaseResult<String> deleteRedisCache(@RequestParam String key) {
        if (StringUtils.isBlank(key)) {
            return BaseResult.success("无效key");
        }

        String[] keyArr =  key.split(",");
        TedisUtil.delete(keyArr);
        return BaseResult.success("success");
    }


    @Data
    public static class ProcessDataCollectMsgTmp {
        private String topic;
        private String tag;
        private String msgId;
        private Object msg;
        private Integer reconsumeTimes;
        private Boolean shadowTopic;
    }

}

package com.timevale.contractmanager.biz.service.rpc.impl;

import com.timevale.contractmanager.common.service.api.RpcContractFulfillmentRecordService;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchDeleteModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchSaveModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchUpdateModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchUpdateStatusModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRecordBatchSaveResult;
import com.timevale.contractmanager.core.service.fulfillment.ContractFulfillmentRecordService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.RestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * RpcContractFulfillmentRecordServiceImpl
 *
 * <AUTHOR>
 * @since 2023/10/12 3:51 下午
 */
@RestService
public class RpcContractFulfillmentRecordServiceImpl implements RpcContractFulfillmentRecordService {

    @Autowired
    private ContractFulfillmentRecordService contractFulfillmentRecordService;

    @Override
    public ContractFulfillmentRecordBatchSaveResult batchSave(ContractFulfillmentRecordBatchSaveModel model) {
       return contractFulfillmentRecordService.batchSave(model);
    }

    @Override
    public void batchDelete(ContractFulfillmentRecordBatchDeleteModel model) {
        contractFulfillmentRecordService.batchDelete(model.getRecordIdList());
    }

    @Override
    public void batchUpdate(ContractFulfillmentRecordBatchUpdateModel model) {
        contractFulfillmentRecordService.batchUpdate(model);
    }

    @Override
    public void batchUpdateStatus(ContractFulfillmentRecordBatchUpdateStatusModel model) {
        contractFulfillmentRecordService.batchUpdateStatus(model.getTenantId(), model.getAccountId(), model.getRecordIdList(), model.getStatus());
    }

    @Override
    public void updateStatus(String tenantId, String accountId, String recordId, String status) {
        contractFulfillmentRecordService.updateStatus(tenantId, accountId, recordId, status);
    }
}

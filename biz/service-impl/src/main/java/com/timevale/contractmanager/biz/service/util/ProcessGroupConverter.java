package com.timevale.contractmanager.biz.service.util;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.dal.bean.ContractProcessGroupDO;
import com.timevale.contractmanager.common.dal.bean.ContractProcessGroupExtra;
import com.timevale.contractmanager.common.service.bean.ContractProcessGroupBean;
import com.timevale.contractmanager.common.service.enums.GroupStatusEnum;
import com.timevale.mandarin.base.util.StringUtils;

public class ProcessGroupConverter {

    public static ContractProcessGroupBean convertGroupBean(ContractProcessGroupDO processGroupDO) {
        ContractProcessGroupBean processGroupBean = new ContractProcessGroupBean();
        processGroupBean.setProcessGroupId(processGroupDO.getProcessGroupId());
        processGroupBean.setProcessGroupType(processGroupDO.getProcessGroupType());
        processGroupBean.setProcessGroupName(processGroupDO.getProcessGroupName());
        processGroupBean.setProcessGroupStatus(GroupStatusEnum.ENABLE.getStatus());
        processGroupBean.setAppId(processGroupDO.getProcessGroupAppId());
        processGroupBean.setCreateTime(processGroupDO.getGmtCreate().getTime());
        processGroupBean.setCreatorAccountId(processGroupDO.getProcessGroupCreator());
        processGroupBean.setOwnerAccountId(processGroupDO.getProcessGroupOwner());
        if (StringUtils.isNotBlank(processGroupDO.getExtra())) {
            ContractProcessGroupExtra extra = JSON.parseObject(processGroupDO.getExtra(), ContractProcessGroupExtra.class);
            processGroupBean.setSignMode(extra.getSignMode());
            processGroupBean.setDedicatedCloudId(extra.getDedicatedCloudId());
        }
        return processGroupBean;
    }
}

package com.timevale.contractmanager.biz.service.rest;

import com.timevale.mandarin.weaver.utils.RequestContext;

/** <AUTHOR> on 2019/7/30 */
public abstract class BaseRest {

    public BaseRest() {}

    protected String getClientId() {
        return RequestContext.getRequest() == null
                ? null
                : RequestContext.getRequest().getHeader("X-Tsign-Client-Id");
    }

    protected String getTenantId() {

        return RequestContext.getRequest() == null
                ? null
                : RequestContext.getRequest().getHeader("X-Tsign-Open-Tenant-Id");
    }
}

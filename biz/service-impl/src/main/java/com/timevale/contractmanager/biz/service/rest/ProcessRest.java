package com.timevale.contractmanager.biz.service.rest;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.AUTHENTICATION_USER_OR_SUBJECT_ACCOUNT_MISSING;
import static com.timevale.contractmanager.common.utils.config.Constants.BATCH_SIGN_VERSION_2;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.*;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.timevale.contractmanager.biz.service.rest.convert.ProcessesRestConvert;
import com.timevale.contractmanager.biz.service.rest.sharedownload.convert.ShareDownloadVOConvert;
import com.timevale.contractmanager.biz.service.util.OrSignValidation;
import com.timevale.contractmanager.common.service.bean.ProcessSecretConfigBean;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.enums.BackFillTypeEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.common.service.enums.ProcessTabEnum;
import com.timevale.contractmanager.common.service.enums.QueryWayEnum;
import com.timevale.contractmanager.common.service.enums.sharedownload.Pdf2ImageTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadPreviewBO;
import com.timevale.contractmanager.core.model.dto.process.GenBatchSignProcessUrlDTO;
import com.timevale.contractmanager.core.model.dto.process.ProcessButtonConfigInputDTO;
import com.timevale.contractmanager.core.model.dto.process.ProcessButtonConfigOutputDTO;
import com.timevale.contractmanager.core.model.dto.process.ProcessParticipantAuthInfo;
import com.timevale.contractmanager.core.model.dto.process.ProcessParticipantStatusInputDTO;
import com.timevale.contractmanager.core.model.dto.process.ProcessParticipantStatusOutputDTO;
import com.timevale.contractmanager.core.model.dto.request.*;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessButtonConfigRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessChangeSignerRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessListGlobalConfigRequest;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessQueryRequest;
import com.timevale.contractmanager.core.model.dto.response.*;
import com.timevale.contractmanager.core.model.dto.response.process.GetProcessTerminateDetailResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ProcessButtonConfigResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ProcessParticipantAuthWayResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ProcessParticipantStatusResponse;
import com.timevale.contractmanager.core.model.dto.response.process.ProcessResetCheckResponse;
import com.timevale.contractmanager.core.model.dto.response.process.*;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.ShareDownloadShareFilePreviewResponse;
import com.timevale.contractmanager.core.service.dedicatedcloud.DedicatedCloudService;
import com.timevale.contractmanager.core.service.process.impl.ProcessStartHelper;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.saas.common.enums.SignModeEnum;
import com.timevale.contractmanager.core.service.aop.SaasDataMasking;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.auditlog.handler.AuditLogHelper;
import com.timevale.contractmanager.core.service.process.*;
import com.timevale.contractmanager.core.service.process.handler.ProcessBackFillHandler;
import com.timevale.contractmanager.core.service.process.handler.ProcessRelationHandler;
import com.timevale.contractmanager.core.service.relationcontract.RelationContractService;
import com.timevale.contractmanager.core.service.sharedownload.ShareDownloadService;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.dayu.sdk.annotation.AuditLogAnnotation;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.doccooperation.service.constans.ParamConstant;
import com.timevale.esign.compontent.simple.encrypt.SimpleCipher;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.footstone.rpc.enums.SignPlatformEnum;
import com.timevale.mandarin.base.exception.BaseIllegalArgumentException;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.enums.DocQueryEnum;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;

/**
 * 合同管理流程rest接口
 *
 * <AUTHOR> on 2019/7/30
 */
@Api(tags = "合同管理流程", description = "合同管理流程")
@ExternalService
@Validated
@RestMapping(path = "/v2")
@Slf4j
public class ProcessRest extends BaseRest {

    private static final String HEADER_KEY_OPERATOR_ID = "X-Tsign-Open-Operator-Id";
    private static final String HEADER_KEY_TENANT_ID = "X-Tsign-Open-Tenant-Id";
    private static final String HEADER_KEY_APP_NAME = "X-Tsign-Client-AppName";
    private static final String PROP_PROCESS_URL_EXTRA_PARAM_FIELDS = "process.url.extra.param.fields";

    @Autowired private ProcessService processService;
    @Autowired private ProcessBizLogService processBizLogService;
    @Autowired private ProcessConfigService processConfigService;
    @Autowired private ProcessSearchService processSearchService;
    @Autowired private FlowTemplateService flowTemplateService;
    @Autowired private ProcessBackFillHandler processBackFillHandler;
    @Autowired private ProcessRelationHandler processRelationHandler;
    @Autowired private ProcessCcService processCcService;
    @Autowired private SensorService sensorService;
    @Autowired private ProcessDraftService processDraftService;
    @Autowired private ProcessDispatchService processDispatchService;
    @Resource private ShareDownloadService shareDownloadService;
    @Autowired private RelationContractService relationContractService;
    @Autowired private ProcessCommonService processCommonService;
    @Autowired private MapperFactory mapperFactory;
    @Autowired private SignModeService signModeService;
    @Autowired private DedicatedCloudService dedicatedCloudService;
    @Autowired private ProcessStartHelper processStartHelper;
    
    @Autowired private ProcessAccountService processAccountService;
    @Autowired private ProcessButtonService processButtonService;

    private Map<String, String> buildUrlExtraParams() {
        if (null == RequestContext.getRequest()) {
            return Maps.newHashMap();
        }
        String fieldProperty = ConfigService.getAppConfig().getProperty(PROP_PROCESS_URL_EXTRA_PARAM_FIELDS, "[]");
        List<String> fields = JSON.parseArray(fieldProperty, String.class);
        if (CollectionUtils.isEmpty(fields)) {
            return Maps.newHashMap();
        }
        Map<String, String> extraParams = Maps.newHashMap();
        fields.forEach(field -> {
            String parameter = RequestContext.getRequest().getParameter(field);
            if (null != parameter) {
                extraParams.put(field, parameter);
            }
        });
        return extraParams;
    }

    /**
     * 批量撤回
     *
     * @param request 批量撤回请求
     * @return 批量撤回结果
     */
    @RestMapping(path = "/processes/batchRevoke", method = RequestMethod.PUT)
    @ApiOperation(value = "批量撤回", httpMethod = "PUT")
    public BaseResult<?> batchRevoke(@RequestBody BatchRevokeRequest request) {

        processService.batchRevoke(request);

        return BaseResult.success();
    }

    /**
     * 单个撤回
     *
     * @param request 批量撤回请求
     * @return 批量撤回结果
     */
    @Deprecated
    @RestMapping(path = "/process/revoke", method = RequestMethod.PUT)
    @ApiOperation(value = "单个撤回", httpMethod = "PUT")
    public BaseResult<?> singleRevoke(@RequestBody SingleRevokeRequest request) {
        processService.singleRevoke(RequestContextExtUtils.getAppId(), request);
        return BaseResult.success();
    }

    /**
     * 批量催办
     *
     * @param request 批量催办请求
     * @return 批量催办结果
     */
    @FunctionPrivilegeCheck(
            function = FunctionCodeConstant.BATCH_RUSH,
            accountId = "request.subjectId",
            batchSize = "request.processIds")
    @RestMapping(path = "/processes/batchRush", method = RequestMethod.POST)
    @ApiOperation(value = "批量催办", httpMethod = "POST")
    public BaseResult<?> batchRush(@RequestBody BaseBatchRequest request) {

        processService.batchRush(request);

        return BaseResult.success();
    }

    /**
     * 批量移动文件夹
     *
     * @param request 批量移动文件夹请求
     * @return 结果
     */
    @RestMapping(path = "/processes/batchMove", method = RequestMethod.PUT)
    @ApiOperation(value = "批量移动文件夹", httpMethod = "PUT")
    public BaseResult<?> batchMove(@RequestBody BatchMoveRequest request) {

        processService.batchMove(request);

        return BaseResult.success();
    }

    /**
     * 批量删除
     *
     * @param request 批量删除请求
     * @return 删除结果
     */
    @RestMapping(path = "/processes/batchDelete", method = RequestMethod.PUT)
    @ApiOperation(value = "批量删除", httpMethod = "PUT")
    public BaseResult<?> batchDelete(@RequestBody BaseBatchRequest request) {
        long begin = System.currentTimeMillis()/1000;
        processService.batchDelete(request);

        sensorService.processBatchDeleteTracking(request.getSubjectId(), "", (long) request.getProcessIds().size(),
                System.currentTimeMillis()/1000 - begin, "通过",
                "", "成功");
        return BaseResult.success();
    }

    /**
     * 获取批量签署的地址
     * todo cleanup
     *
     * @param request 请求
     * @return 批量签署地址
     */
    @Deprecated
    @RestMapping(path = "/processes/getBatchSignUrl", method = RequestMethod.POST)
    @ApiOperation(value = "获取批量签署的地址", httpMethod = "POST")
    public BaseResult<GetBatchSignUrlResponse> getBatchSignUrl(
            @RequestBody GetBatchSignUrlRequest request) {

        GetBatchSignUrlResponse response = processService.getBatchSignUrl(request);

        return BaseResult.success(response);
    }

    /**
     * 获取批量签署流程列表
     * todo cleanup
     *
     * @param request 请求
     * @return 流程列表
     */
    @Deprecated
    @FunctionPrivilegeCheck(
            function = FunctionCodeConstant.BATCH_SIGN,
            accountId = "request.subjectId",
            batchSize = "request.processIds")
    @RestMapping(path = "/processes/batchSign/list", method = RequestMethod.POST)
    @ApiOperation(value = "获取批量签署可执行流程列表", httpMethod = "POST")
    public BaseResult<GenBatchSignProcessListResponse> genBatchSignProcessList(
            @RequestBody GenBatchSignProcessListRequest request) {

        GenBatchSignProcessListResponse response = processService.genBatchSignProcessList(request);

        return BaseResult.success(response);
    }


    /**
     * 获取批量签署的url地址
     * todo cleanup
     *
     * @param request 请求
     * @return 流程列表
     */
    @Deprecated
    @FunctionPrivilegeCheck(
            function = FunctionCodeConstant.BATCH_SIGN,
            accountId = "request.subjectId",
            batchSize = "request.processIds")
    @RestMapping(path = "/processes/batchSign/url", method = RequestMethod.POST)
    @ApiOperation(value = "获取批量签署的url地址", httpMethod = "POST")
    public BaseResult<GenBatchSignProcessUrlResponse> genBatchSignProcessUrl(
            @ApiParam("操作人id") @RequestHeader("X-Tsign-Open-Operator-Id") String accountId,
            @ApiParam("空间id") @RequestHeader(name = "X-Tsign-Open-Tenant-Id", required = false) String tenantId,
            @RequestBody GenBatchSignProcessUrlRequest request) {
        GenBatchSignProcessUrlDTO genBatchSignProcessUrlDTO = new GenBatchSignProcessUrlDTO();
        genBatchSignProcessUrlDTO.setProcessIds(request.getProcessIds());
        if(StringUtils.isBlank(request.getSubjectId())){
            genBatchSignProcessUrlDTO.setSubjectId(tenantId);
        }else{
            genBatchSignProcessUrlDTO.setSubjectId(request.getSubjectId());
        }
        genBatchSignProcessUrlDTO.setVersion(BATCH_SIGN_VERSION_2);
        genBatchSignProcessUrlDTO.setAccountId(accountId);
        String url = processService.genBatchSignProcessUrl(genBatchSignProcessUrlDTO);
        GenBatchSignProcessUrlResponse result = new GenBatchSignProcessUrlResponse();
        result.setUrl(url);
        return BaseResult.success(result);
    }

    /**
     * 获取流程跳转地址
     *
     * @param processId 流程id
     * @param menuId 流程菜单目录id
     * @param accountId 查询人账号id
     * @param subjectId 查询人所属主体id
     * @param platform 渠道 {@link com.timevale.doccooperation.service.enums.PlatfromEnum}
     * @param redirectUrl 回跳地址
     * @param entrance 来源 微应用main,待办todo,通知notice,审批approval
     * @param token token
     * @param dnsAppId X-Tsign-Dns-App-Id
     * @return 流程跳转地址
     */
    @AuditLogAnnotation(
            firstModule = AuditLogConstant.SpEL.CONTRACT_SIGN_FIRST_MODULE,
            secondaryModule = AuditLogConstant.SpEL.CONTRACT_SIGN_SECONDARY_MODULE,
            appid = "#dnsAppId",
            resourceId = "#processId",
            detailTactics = "1",
            condition = "{{#accountId != null && #accountId != '' && #_result != null && #_result.code == 0}}",
            result = "#_result != null && #_result.code == 0 ? " + AuditLogConstant.RESULT,
            postHandle = "auditLogViewProcessHandle")
    @RestMapping(path = "/processes/{processId}/getUrl", method = RequestMethod.GET)
    @ApiOperation(value = "获取流程跳转地址,直接跳详情页", httpMethod = "GET")
    public BaseResult<GetProcessUrlResponse> getUrl(
            @ApiParam(value = "流程id", required = true) @PathVariable String processId,
            @ApiParam(value = "流程菜单目录id") @RequestParam(required = false) String menuId,
            @ApiParam(value = "查询人账号id", required = true) @RequestParam String accountId,
            @ApiParam(value = "查询人所属主体id，为空时默认个人主体") @RequestParam(required = false) String subjectId,
            @ApiParam(
                            value =
                                    "获取哪个端的地址,1-开放服务h5 2-支付宝小程序 3-微信小程序 4-标准签H5 5-标准签WEB 6-开放服务WEB 7-IOS标准签APP 8-Android标准签APP，默认标准签WEB")
                    @RequestParam(required = false)
                    Integer platform,
            @ApiParam(value = "回跳地址") @RequestParam(required = false) String redirectUrl,
            @ApiParam(value = "来源(微应用main  待办todo 通知 notice 审批approval 填写页复制催办链接 fillCopyRushLink)")
                    @RequestParam(name = "entrance", required = false)
                    String entrance,
            @ApiParam(value = "token") @RequestParam(required = false) String token,
            @ApiParam(value = "是否从指定空间下获取详情地址， 如果是，url中将带上空间id，用于前端业务判断使用")
                    @RequestParam(required = false)
                    Boolean fromTenant,
            @RequestHeader(name = "X-Tsign-Dns-App-Id", required = false) String dnsAppId) {

        // 如果未指定主体id, 默认个人主体
        if (StringUtils.isBlank(subjectId)) {
            subjectId = accountId;
        }

        GetProcessUrlRequest request = new GetProcessUrlRequest();
        request.setProcessId(processId);
        request.setAppId(RequestContext.getAppId());
        request.setAccountId(accountId);
        request.setSubjectId(subjectId);
        request.setPlatform(
                null == platform ? SignPlatformEnum.STANDARD_WEB.getPlatform() : platform);
        request.setClient(getClientId());
        request.setRedirectUrl(redirectUrl);
        request.setMenuId(menuId);
        request.setDnsAppId(dnsAppId);
        request.setToken(token);
        request.setFromTenant(Boolean.TRUE.equals(fromTenant));
        request.setEntrance(entrance);
        request.setExtraParams(buildUrlExtraParams());
        GetProcessUrlResponse response = processService.getUrl(request, true);

        // 如果来源不为空，拼接在地址后
        if (StringUtils.isNotBlank(response.getLongUrl()) && StringUtils.isNotBlank(entrance)) {
            String longUrl = response.getLongUrl();
            StringBuilder urlBuild = new StringBuilder(longUrl);
            urlBuild.append("&entrance=");
            urlBuild.append(entrance);
            response.setLongUrl(urlBuild.toString());
        }
        if (StringUtils.isNotBlank(response.getLongUrl()) && StringUtils.isNotBlank(token)) {
            String urlBuild = response.getLongUrl() + "&token=" + token;
            response.setLongUrl(urlBuild);
        }

        AuditLogHelper.acceptHeaderFields();
        LogRecordContext.putVariable(AuditLogConstant.Field.ACCOUNT_OID, accountId);
        LogRecordContext.putVariable(AuditLogConstant.Field.SUBJECT_OID, subjectId);

        return BaseResult.success(response);
    }

    /**
     * 获取流程详情地址（支持子流程自动调度流转）
     *
     * @param processId 流程id
     * @param request 获取详情地址请求参数
     * @param dnsAppId X-Tsign-Dns-App-Id
     * @return 流程跳转地址
     */
    @RestMapping(path = "/processes/{processId}/dispatch-url", method = RequestMethod.GET)
    @ApiOperation(value = "获取流程详情地址（支持子流程自动调度流转）", httpMethod = "GET")
    public BaseResult<GetProcessUrlResponse> getDispatchUrl(
            @RequestHeader(HEADER_KEY_OPERATOR_ID) @NotBlank(message = "用户id不能为空") String accountId,
            @RequestHeader(HEADER_KEY_TENANT_ID) @NotBlank(message = "主体id不能为空") String tenantId,
            @RequestHeader(name = "X-Tsign-Dns-App-Id", required = false) String dnsAppId,
            @ApiParam(value = "流程id", required = true) @PathVariable String processId,
            @ModelAttribute @URIQueryParam GetProcessDispatchUrlRequest request) {

        GetProcessUrlRequest urlRequest = new GetProcessUrlRequest();
        urlRequest.setProcessId(processId);
        urlRequest.setAppId(RequestContext.getAppId());
        urlRequest.setAccountId(accountId);
        urlRequest.setSubjectId(tenantId);
        urlRequest.setPlatform(request.getPlatform());
        urlRequest.setClient(getClientId());
        urlRequest.setRedirectUrl(request.getRedirectUrl());
        urlRequest.setDnsAppId(dnsAppId);
        urlRequest.setToken(request.getToken());
        urlRequest.setExtraParams(buildUrlExtraParams());

        GetProcessUrlResponse response = processDispatchService.getDispatchUrl(urlRequest);

        return BaseResult.success(response);
    }

    /**
     * 通过context获取流程跳转地址
     *
     * @param processId 流程id
     * @param context 上下文对象
     * @param platform 渠道 {@link com.timevale.doccooperation.service.enums.PlatfromEnum}
     * @param redirectUrl 回跳地址
     * @param dnsAppId X-Tsign-Dns-App-Id
     * @return 流程跳转地址
     */
    @AuditLogAnnotation(
            firstModule = AuditLogConstant.SpEL.CONTRACT_SIGN_FIRST_MODULE,
            secondaryModule = AuditLogConstant.SpEL.CONTRACT_SIGN_SECONDARY_MODULE,
            appid = "#dnsAppId",
            resourceId = "#processId",
            detailTactics = "1",
            condition = "{{#_result != null && #_result.code == 0}}",
            result = "{{#_result != null && #_result.code == 0 ? " + AuditLogConstant.RESULT + "}}",
            postHandle = "auditLogViewProcessHandle")
    @RestMapping(path = "/processes/{processId}/getUrlByContext", method = RequestMethod.GET)
    @ApiOperation(value = "通过context获取流程跳转地址", httpMethod = "GET")
    public BaseResult<GetProcessUrlResponse> getUrlByContext(
            @ApiParam(value = "流程id", required = true) @PathVariable String processId,
            @ApiParam(value = "上下文信息", required = true) @RequestParam String context,
            @ApiParam(
                            value =
                                    "获取哪个端的地址,1-开放服务h5 2-支付宝小程序 3-微信小程序 4-标准签H5 5-标准签WEB 6-开放服务WEB 7-IOS标准签APP 8-Android标准签APP")
                    @RequestParam(required = false)
                    Integer platform,
            @ApiParam(value = "回跳地址") @RequestParam(required = false) String redirectUrl,
            @RequestHeader(name = "X-Tsign-Dns-App-Id", required = false) String dnsAppId,
            @ApiParam(value = "是否获取详情页") @RequestParam(required = false) boolean detailPage) {

        Map<String, String> paramMap;
        try {
            String params = SimpleCipher.INSTANCE.decode("AES", context, "UTF-8");
            paramMap = Splitter.on("&").withKeyValueSeparator("=").split(params);
        } catch (Exception e) {
            throw new BaseIllegalArgumentException("参数不合法");
        }
        String accountId = paramMap.get("accountId");
        String subjectId = paramMap.get("subjectId");
        String openId = paramMap.get("openId");

        if (StringUtils.isBlank(accountId) || StringUtils.isBlank(subjectId)) {
            throw new BaseIllegalArgumentException("缺少参数");
        }

        GetProcessUrlRequest request = new GetProcessUrlRequest();
        request.setProcessId(processId);
        request.setAppId(RequestContext.getAppId());
        request.setAccountId(accountId);
        request.setSubjectId(subjectId);
        request.setClient(getClientId());
        request.setRedirectUrl(redirectUrl);
        request.setPlatform(platform);
        request.setDnsAppId(dnsAppId);
        request.setMenuId(paramMap.get("menuId"));
        request.setOpenId(openId);
        String needSaasLoginStr = paramMap.get(ParamConstant.NEED_SAAS_LOGIN);
        if (StringUtils.isNotBlank(needSaasLoginStr)) {
            request.setNeedSaasLogin(Boolean.parseBoolean(needSaasLoginStr));
            String expireTime = paramMap.get(ParamConstant.EXPIRE_TIME);
            if (StringUtils.isNotBlank(expireTime)) {
                request.setExpireTime(Long.valueOf(expireTime));
            }
        }
        GetProcessUrlResponse response = processService.getUrl(request, detailPage);

        AuditLogHelper.acceptHeaderFields();
        LogRecordContext.putVariable(AuditLogConstant.Field.ACCOUNT_OID, accountId);
        LogRecordContext.putVariable(AuditLogConstant.Field.SUBJECT_OID, subjectId);

        return BaseResult.success(response);
    }

    /**
     * 获取流程列表
     * 注： 后续需求切换到POST请求， GET请求不再调整，逐渐废弃
     */
    private static final String DEFAULT_INVALID_MESSAGE = "当前版本已不支持查看流程列表，请升级到最新版本";
    @Deprecated
    @RestMapping(path = "/processes/list", method = RequestMethod.GET)
    @SaasDataMasking
    @ApiOperation(value = "processList", httpMethod = "GET")
    public BaseResult<ProcessQueryResponse> processList() {
        throw new BizContractManagerException(BizContractManagerResultCodeEnum.SERVICE_BIZ_ERROR.getCode(), DEFAULT_INVALID_MESSAGE);
    }

    @RestMapping(path = "/processes/list", method = RequestMethod.POST)
    @SaasDataMasking
    @ApiOperation(value = "流程列表查询", httpMethod = "POST")
    public BaseResult<ProcessQueryResponse> processList(
            @RequestHeader("X-Tsign-Open-Operator-Id") String accountId,
            @RequestBody ProcessListSearchRequest listRequest) {
        long beginTime = System.currentTimeMillis()/1000;
        ProcessQueryRequest request = new ProcessQueryRequest();
        BeanUtils.copyProperties(listRequest, request);
        request.setProcessStatusList(IdsUtil.getIntegerSet(listRequest.getProcessStatusList()));
        request.setProcessBizTypeList(IdsUtil.getIntegerSet(listRequest.getProcessBizTypeList()));
        if (CollectionUtils.isEmpty(request.getProcessStatusList())) {
            if (Boolean.TRUE.equals(request.getWithApproving())) {
                request.getProcessStatusList().addAll(ProcessStatusEnum.getAllProcessStatusList());
            } else {
                request.getProcessStatusList().addAll(ProcessStatusEnum.getNoApproveProcessStatusList());
            }
        }
        // 设置查询用户id
        request.setAccountId(accountId);
        // 前置处理请求参数
        preHandleProcessQueryRequest(request);
        // 查询流程列表
        ProcessQueryResponse res = processSearchService.query(request);
        //数据埋点
        try {
            sensorService.processesListTracking(request.getSubjectId(),
                    DocQueryEnum.getByType(request.getDocQueryType()).getDesc(),
                    request.getProcessStatusList(),
                    request.getBeginTimeInMillSec() == null ? Boolean.FALSE : Boolean.TRUE,
                    request.getEndTimeInMillSec() == null ? Boolean.FALSE : Boolean.TRUE,
                    request.getProcessBizTypeList(), res.getTotal(), System.currentTimeMillis()/1000 - beginTime);
        }catch (Exception e){
            log.info("经办合同列表加载埋点异常", e);
        }
        return BaseResult.success(res);
    }

    /**
     * 前置处理流程列表查询参数
     * @param request
     */
    private void preHandleProcessQueryRequest(ProcessQueryRequest request) {
        int pageNum = request.getPageNum();
        int pageSize = request.getPageSize();
        //查询条数校验(ES不支持20000条之后的查询),后端兜底
        if(Constants.ES_MAX_SUPPORT_TOTAL < (pageNum * pageSize)){
            log.info("The request data is over the limit, pageNo:{}, pageSize:{}", pageNum, pageSize);
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_REQUEST_OVER_MAX_LIMIT);
        }
        // 流程状态处理
        if (CollectionUtils.isEmpty(request.getProcessStatusList())) {
            request.setProcessStatusList(Sets.newHashSet(ProcessStatusEnum.getAllProcessStatusList()));
        }
        // queryWay处理
        handleRequestQueryWay(request);
    }

    /**
     * 处理请求参数中的queryWay
     * @param request
     */
    private void handleRequestQueryWay(ProcessQueryRequest request) {
        // 查询类型处理
        Integer queryWay = request.getQueryWay();
        if (null == queryWay) {
            return;
        }
        if (QueryWayEnum.WITHOUT_SUBJECT_QUERY.getWay() == queryWay) {
            request.setSubjectId(null);
            return;
        }
        // 待他人操作处理， 其他场景不处理
        if (!DocQueryEnum.KS_WAIT_TA.getType().equals(request.getDocQueryType())) {
            return;
        }
        if (QueryWayEnum.WITHOUT_WAIT_TA_SIGN_QUERY.getWay() == queryWay) {
            // 如果queryWay忽略待他人签署数据， 则设置searchDocQueryType为KS_WAIT_TA_APPROVE
            request.setDocQueryType(DocQueryEnum.KS_WAIT_TA_APPROVE.getType());
            return;
        }
        if (QueryWayEnum.WITHOUT_WAIT_TA_SEAL_APPROVE_QUERY.getWay() == queryWay) {
            // 如果queryWay忽略待他人用印审批数据， 则设置searchDocQueryType为KS_WAIT_TA_SIGN
            request.setDocQueryType(DocQueryEnum.KS_WAIT_TA_SIGN.getType());
        }
    }

    /**
     * 经办合同各分栏的统计接口
     * 现在只支持待我操作和待他人操作
     * @return`
     */
    @RestMapping(path = "/processes/list/count", method = RequestMethod.GET)
    @SaasDataMasking
    @ApiOperation(value = "经办合同各分栏的统计接口", httpMethod = "GET")
    public BaseResult<ProcessCountResponse> processListCount(
            @ApiParam(value = "docQueryTypes", required = true) @RequestParam(name = "docQueryTypes", required = true) String docQueryTypes,
            @ApiParam(value = "是否可包含审批状态的流程, 默认包含", example = "true") @RequestParam(name = "withApproving", required = false) Boolean withApproving) {
        String accountId = RequestContextExtUtils.getOperatorId();
        String subjectId = RequestContextExtUtils.getTenantId();

        String[] docQueryTypeArr = docQueryTypes.split(",");
        List<Integer> docQueryTypeList = Arrays.stream(docQueryTypeArr)
                .filter(StringUtils::isNumeric)
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        if (ListUtils.isEmpty(docQueryTypeList)){
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "docQueryTypes必须为,拼接的数字");
        }

        List<Integer> status;
        if (Boolean.FALSE.equals(withApproving)) {
            status = ProcessStatusEnum.getNoApproveProcessStatusList();
        } else {
            status = ProcessStatusEnum.getAllProcessStatusList();
        }
        ProcessCountResponse countResponse = processSearchService.countByTypes(accountId, subjectId, status, docQueryTypeList);

        return BaseResult.success(countResponse);
    }

    /**
     * 获取可出证合同流程列表
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/processes/issueList", method = RequestMethod.GET)
    @ApiOperation(value = "获取可出证合同流程列表", httpMethod = "GET")
    @MemberCheck(accountId = "#request.accountId", subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ProcessEvidenceQueryResponse> evidenceProcessList(
            @ModelAttribute ProcessEvidenceQueryRequest request) {
        //查询条数校验(ES不支持20000条之后的查询),后端兜底
        if(Constants.ES_MAX_SUPPORT_TOTAL < (request.getPage() * request.getPageSize())){
            log.info("The request data is over the limit, pageNo:{}, pageSize:{}", request.getPage(), request.getPageSize());
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_REQUEST_OVER_MAX_LIMIT);
        }
        return BaseResult.success(
                processSearchService.queryEvidenceProcessList(
                        RequestContextExtUtils.getTenantId(), request));
    }

    /**
     * 获取流程数量
     *
     * @param accountId 账号id
     * @param subjectId 主体id
     * @param withApproving 是否包含审批状态的流程
     * @return 流程数量
     *
     * <AUTHOR> J un
     * @date 2019/8/6 1:22 PM
     * 修改记录 2019/8/6 1:22 PM : initial
     */
    @RestMapping(path = "/processes/count", method = RequestMethod.GET)
    @ApiOperation(value = "processCount", httpMethod = "GET")
    public BaseResult<ProcessCountResponse> processCount(
            @ApiParam(value = "accountId", required = true)@RequestParam(name = "accountId",required = true) String accountId,
            @ApiParam(value = "subjectId", required = true)@RequestParam(name = "subjectId",required = true) String subjectId,
            @ApiParam(value = "是否可包含审批状态的流程, 默认不包含", example = "false")    @RequestParam(name = "withApproving",required = false) Boolean withApproving,
            @ApiParam(value = "是否查询扫码签数量", required = false) @RequestParam(name = "queryShareSign",required = false) Boolean queryShareSign) {
        List<Integer> status;
        if (Boolean.TRUE.equals(withApproving)) {
            status = ProcessStatusEnum.getAllProcessStatusList();
        } else {
            status = ProcessStatusEnum.getNoApproveProcessStatusList();
        }
        return BaseResult.success(Boolean.TRUE.equals(queryShareSign)? processSearchService.countV2(accountId, subjectId, status):processSearchService.count(accountId, subjectId, status));
    }

    @RestMapping(path = "/processes/home/<USER>", method = RequestMethod.GET)
    @ApiOperation(value = "首页count聚合", httpMethod = "GET")
    public BaseResult<ProcessCountResponse> queryCount(
            @ApiParam(value = "accountId", required = true)@RequestParam(name = "accountId",required = true) String accountId,
            @ApiParam(value = "subjectId", required = true)@RequestParam(name = "subjectId",required = true) String subjectId,
            @ApiParam(value = "统计状态 ,分割 1.待我签署 2.待我填写 3.待我审批（用印审批） 4.我发起待签署 5.我发起待审批（用印审批） 6.我的草稿 " )@RequestParam(name = "type")String type) {
        List<ProcessTabEnum> tabEnumList;

        if(Strings.isNotBlank(type)){
            try{
                Set<Integer> typeList = Arrays.asList(type.split(",")).stream().map(Integer::new).collect(Collectors.toSet());
                tabEnumList = ProcessTabEnum.getProcessTabEnumByStatus(typeList);
                if(typeList.size() != tabEnumList.size()){
                    throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM,"状态类型");
                }
            }catch (Exception e){
                throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM,"状态类型");
            }
        } else{
            tabEnumList = Arrays.asList(ProcessTabEnum.values());
        }

        ProcessCountResponse res = processSearchService.queryCount(accountId, subjectId, tabEnumList);
        return BaseResult.success(res);
    }

    @RestMapping(path = "/processes/home/<USER>", method = RequestMethod.GET)
    @ApiOperation(value = "首页列表聚合", httpMethod = "GET")
    @MemberCheck(accountId = "#accountId", subjectId = "#subjectId")
    public BaseResult<ProcessHomeListResponse> queryHomeList(@ApiParam(value = "accountId", required = true) @RequestHeader(HEADER_KEY_OPERATOR_ID) String accountId,
            @ApiParam(value = "subjectId", required = true) @RequestParam(name = "subjectId", required = true) String subjectId,
            @ApiParam(value = "统计状态  1.待我签署 2.待我填写 3.待我审批（用印审批） 4.我发起待签署 5.我发起待审批（用印审批） 6.我的草稿 ") @RequestParam(name = "type") Integer type) {
        ProcessTabEnum tabEnum = ProcessTabEnum.getProcessTabEnumByStatus(type);

        if (tabEnum == null) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "状态类型");
        }
        ProcessHomeListResponse response = processSearchService.queryList(accountId, subjectId, tabEnum);
        setMobileNull(response);
        return BaseResult.success(response);
    }

    private void setMobileNull(ProcessHomeListResponse response) {
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            return;
        }
        //手机号前端未使用，安全问题直接置为null
        for (ProcessHomeListResponse.ProcessHomeInfo processHomeInfo : response.getData()) {
            if (processHomeInfo.getProcessAccount() != null && processHomeInfo.getProcessAccount().getPerson() != null) {
                processHomeInfo.getProcessAccount().getPerson().setMobile(null);
            }
            if (CollectionUtils.isNotEmpty(processHomeInfo.getParticipantAccountList())) {
                for (ProcessAccount processAccount : processHomeInfo.getParticipantAccountList()) {
                    if (processAccount.getPerson() != null) {
                        processAccount.getPerson().setMobile(null);
                    }
                }
            }
        }
    }

    /**
     * 获取草稿列表
     *
     * @param docQueryType 查询类型 {@link com.timevale.signflow.search.docSearchService.enums.DocQueryEnum}
     * @param fuzzyMatching 模糊查询字符串
     * @param pageSize 每页大小
     * @param pageNum 页码
     * @param accountId 账号id
     * @param subjectId 主体id
     * @return 草稿列表
     *
     * <AUTHOR>
     * @date 2019/8/6 1:22 PM
     * 修改记录 2019/8/6 1:22 PM : initial
     */
    @RestMapping(path = "/processes/draft/list", method = RequestMethod.GET)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "processDraftList", httpMethod = "GET")
    public BaseResult<DraftQueryResponse> processDraftList(
            @RequestHeader(HEADER_KEY_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_KEY_TENANT_ID) String tenantId,
            @ModelAttribute ProcessDraftQueryRequest request) {
        
        request.setAccountId(accountId);
        request.setSubjectId(tenantId);
        
        //查询条数校验(ES不支持20000条之后的查询),后端兜底
        if (Constants.ES_MAX_SUPPORT_TOTAL < (request.getPageNum() * request.getPageSize())) {
            log.info("The request data is over the limit, pageNo:{}, pageSize:{}", request.getPageNum(), request.getPageSize());
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_REQUEST_OVER_MAX_LIMIT);
        }
        DraftQueryResponse res = processSearchService.draftQuery(request);
        return BaseResult.success(res);
    }

    /**
     * 批量删除草稿
     * 注意:
     * 1.之前的草稿仅限于发起签署产生的草稿，草稿id 就是签署流程id
     * 2.接口迁移到此次，是为了统一合同管理的操作，之前调用的是签署单独的删除草稿接口
     * 3.在发起合并整合后，老的草稿已经迁移进流程模板，草稿就是类型为草稿类型的流程模板id，删除草稿就是删除流程模板
     * @param request 入参
     * @return 无返回值
     */
    @MemberCheck(accountId = "#accountId", subjectId = "#request.subjectId")
    @RestMapping(path = "/processes/drafts/deleteBatch", method = RequestMethod.POST)
    @ApiOperation(value = "批量删除草稿", httpMethod = "POST")
    public BaseResult<?> deleteDrafts(
            @RequestHeader(HEADER_KEY_OPERATOR_ID) String accountId,
            @RequestBody BatchDeleteDraftRequest request) {
        request.setAccountId(accountId);
        flowTemplateService.batchDeleteDraftTemplate(request);
        return BaseResult.success();
    }

    @ApiOperation(value = "获取24小时内最新草稿", httpMethod = "GET")
    @RestMapping(path = "/processes/draft/latest", method = RequestMethod.GET)
    public BaseResult<String> processLatestDraft(
            @RequestHeader(HEADER_KEY_OPERATOR_ID) String operatorId,
            @RequestHeader(HEADER_KEY_TENANT_ID) String tenantId) {

        // 从缓存获取最新草稿templateId
        String latestDraftId = processDraftService.safeGetDailyLatestDraftId(operatorId, tenantId);

        return BaseResult.success(latestDraftId);
    }

    @RestMapping(path = "/processes/asyncExport/number", method = RequestMethod.POST)
    @ApiOperation(value = "一键异步导出成excel获取数量", httpMethod = "POST")
    public BaseResult<ProcessExportResponse> asyncAsyncExportNumber(
            @RequestBody ProcessExportRequest request) {
        updateSubjectIdByHeader(request);
        return BaseResult.success(processService.clickAsyncExportExcelNumber(request));
    }

    /**
     * 异步提交批量导出任务
     *
     * @param request 导出请求
     * @return 导出任务id
     */
    @RestMapping(path = "/processes/asyncExport", method = RequestMethod.POST)
    @ApiOperation(value = "文档数据异步导出成excel", httpMethod = "POST")
    public BaseResult<ProcessExportResponse> asyncExportProcesses(
            @RequestBody ProcessExportRequest request) {
        updateSubjectIdByHeader(request);
        request.setClientId(getClientId());
        return BaseResult.success(processService.clickAsyncExportExcel(request));
    }

    /**
     * 获取异步批量导出下载地址
     *
     * @param request 导出请求
     * @return oss地址
     */
    @ApiOperation(value = "获取异步批量导出excel下载地址")
    @RestMapping(path = "/processes/asyncExportUrl", method = RequestMethod.GET)
    public BaseResult<ProcessExportUrlResponse> getAsyncExportUrl(
            @ModelAttribute ProcessExportUrlRequest request) {
        ProcessExportUrlResponse response =
                processService.getAsyncBatchExportUrl(request.getExportTaskId(), request.getAccountId());

        return BaseResult.success(response);
    }

    /** 拒绝流程， 拒签/拒填 */
    @ApiOperation(value = "拒绝流程， 拒签/拒填")
    @RestMapping(path = "/processes/{processId}/refuse", method = RequestMethod.POST)
    public BaseResult<ProcessLogsResponse> refuseProcess(
            @PathVariable("processId") String processId,
            @RequestHeader(HEADER_KEY_OPERATOR_ID) String accountId,
            @RequestBody ProcessRefuseRequest request) {
        request.setAccountId(accountId);
        request.setClientId(RequestContextExtUtils.getClientId());
        processService.refuseProcess(processId, request);
        return BaseResult.success();
    }

    /** 获取流程操作日志 */
    @ApiOperation(value = "获取流程操作日志")
    @RestMapping(path = "/processes/{processId}/logs", method = RequestMethod.GET)
    public BaseResult<ProcessBizLogsResponse> getProcessLogs(
            @PathVariable("processId") String processId,
            @RequestParam(value = "menuId", required = false) String menuId,
            @RequestParam(value = "resourceShareId", required = false) String resourceShareId) {
        String tenantId = RequestContextExtUtils.getTenantId();
        String operatorId = RequestContextExtUtils.getOperatorId();
        // 查询流程日志列表
        ProcessBizLogsResponse response = processBizLogService.getProcessBizLogs(processId, tenantId, operatorId, menuId, resourceShareId);
        // 返回日志列表
        return BaseResult.success(response);
    }

    private void updateSubjectIdByHeader(ProcessExportRequest request) {
        if (StringUtils.isNotEmpty(request.getSubjectId())) {
            return;
        }
        String tenantId = getTenantId();
        request.setSubjectId(tenantId);
    }

    /**
     * 重新发起回填信息查询
     *
     * @param processId 流程id
     * @return 查询结果
     */
    @ApiOperation(value = "重新发起回填信息查询", httpMethod = "GET")
    @RestMapping(path = "/processes/{processId}/backfill", method = RequestMethod.GET)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ProcessStartDetailResponse> getBackfillInfo(
            @RequestHeader(value = HEADER_TSIGN_CLIENT_ID, required = false) String clientId,
            @RequestHeader(value = HEADER_TSIGN_CLIENT_VERSION, required = false) String clientVersion,
            @RequestHeader(value = HEADER_KEY_APP_NAME, required = false) String appName,
            @PathVariable(value = "processId") String processId) {
        String subjectOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        ProcessStartDetailResponse response = processBackFillHandler.getBackFillInfo(processId, 
                BackFillTypeEnum.START_PROCESS_BACKFILL.getType(),null,subjectOid,operatorOid);

        if (response.isOrSign()) {
            OrSignValidation.validClient(clientId, clientVersion, appName);
        }
        if (SignModeEnum.GLOBAL.equalBiz(response.getSignMode())) {
            signModeService.limitClientOperate(clientId, appName);
        }
        if (StringUtils.isNotBlank(response.getDedicatedCloudId())) {
            dedicatedCloudService.limitClientOperate(clientId, appName);
        }
        return BaseResult.success(response);
    }

    /**
     * 获取原流程已发起文档查询关联记录
     *
     * @param processId 流程id
     * @param fileId 文档id
     * @param relateType 关联类型
     * @return 关联记录
     */
    @RestMapping(
            path = "/processes/{processId}/files/{fileId}/queryRelateRecord",
            method = RequestMethod.GET)
    @ApiOperation(value = "获取原流程已发起文档查询关联记录", httpMethod = "GET")
    public BaseResult<QueryRelationRecordResponse> queryRelateRecord(
            @PathVariable(name = "processId") String processId,
            @PathVariable(name = "fileId") String fileId,
            @ApiParam(value = "关联类型，0-所有，1-解约，2-续签", example = "1")
            @RequestParam(name = "relateType", required = false)
                    Integer relateType) {

        return BaseResult.success(
                processRelationHandler.queryRelationRecord(processId, fileId, relateType));
    }

    /**
     * 修改合同到期时间
     *
     * @param request 请求体
     */
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.MODIFY_CONTRACT_DEADLINE)
    @RestMapping(path = "/processes/contractValidity/update", method = RequestMethod.POST)
    @ApiOperation(value = "修改合同到期时间", httpMethod = "POST")
    public BaseResult updateContractValidityNew(@RequestBody UpdateValidityRequest request) {
        String operatorId = RequestContextExtUtils.getOperatorId();
        String tenantId = RequestContextExtUtils.getTenantId();
        processService.updateContractValidity(request, true, operatorId, tenantId);

        return BaseResult.success();
    }


    /**
     * 修改合同到期时间
     *
     * @param request 请求体
     */
    @RestMapping(path = "/processes/batchHiddenProcess", method = RequestMethod.POST)
    @ApiOperation(value = "企业合同-批量删除合同", httpMethod = "POST")
    public BaseResult batchHiddenProcess(@RequestBody DeleteProcessRequest request) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String tenantOid = RequestContextExtUtils.getTenantId();
        processConfigService.updateProcessHiddenConfig(operatorOid, tenantOid, request);

        return BaseResult.success();
    }

    /**
     * 查询合同保密配置
     *
     * @param processId
     */
    @RestMapping(path = "/processes/{processId}/querySecretConfig", method = RequestMethod.GET)
    @ApiOperation(value = "查询合同保密配置", httpMethod = "GET")
    public BaseResult<QueryProcessSecretConfigResponse> queryProcessSecretConfig(
            @PathVariable String processId) {

        ProcessSecretConfigBean secretConfigBean =
                processConfigService.queryProcessSecretConfig(processId);
        QueryProcessSecretConfigResponse response = new QueryProcessSecretConfigResponse();
        response.setSecretType(secretConfigBean.getSecretType());
        response.setSecretFileIds(secretConfigBean.getSecretFileIds());
        response.setVisibleAccounts(secretConfigBean.obtainVisibleAccountOids());
        return BaseResult.success(response);
    }

    /**
     * 修改合同保密配置
     *
     * @param request
     */
    @RestMapping(path = "/processes/updateSecretConfig", method = RequestMethod.POST)
    @ApiOperation(value = "更新合同保密配置", httpMethod = "POST")
    public BaseResult updateProcessSecretConfig(
            @RequestHeader("X-Tsign-Open-Operator-Id") String operatorId,
            @RequestHeader("X-Tsign-Open-Tenant-Id") String tenantId,
            @RequestBody ProcessSecretConfigUpdateRequest request) {

        processConfigService.updateProcessSecretConfig(operatorId, tenantId, request);

        return BaseResult.success();
    }

    @RestMapping(path = "/processes/list/globalConfig", method = RequestMethod.GET)
    @ApiOperation(value = "获取合同流程列表相关配置", httpMethod = "GET")
    public BaseResult<ProcessListGlobalConfigResponse> getProcessStartInitConfig(
            @RequestHeader("X-Tsign-Open-Operator-Id") String operatorId,
            @RequestHeader("X-Tsign-Open-Tenant-Id") String tenantId) {
        ProcessListGlobalConfigRequest request = new ProcessListGlobalConfigRequest();
        request.setAccountId(operatorId);
        request.setTenantId(tenantId);
        request.setCorpId(RequestContextExtUtils.getDingCorpId());
        request.setClientId(RequestContextExtUtils.getClientId());
        request.setIsvAppId(RequestContextExtUtils.getIsvAppId());
        return BaseResult.success(processConfigService.getProcessListGlobalConfig(request));
    }

    @RestMapping(path = "/processes/{processId}/ccs", method = RequestMethod.GET)
    @ApiOperation(value = "查询合同抄送人列表", httpMethod = "GET")
    public BaseResult<ProcessCcsResponse> getProcessCcs(
            @PathVariable String processId,
            @RequestHeader("X-Tsign-Open-Operator-Id") String operatorId,
            @RequestHeader("X-Tsign-Open-Tenant-Id") String tenantId,
            @URIQueryParam @ModelAttribute QueryProcessCcRequest request) {
        // 查看流程抄送人
        return BaseResult.success(processCcService.queryProcessCcs(processId, operatorId, tenantId, request));
    }

    @RestMapping(path = "/processes/{processId}/ccs", method = RequestMethod.PUT)
    @ApiOperation(value = "修改合同抄送人列表", httpMethod = "PUT")
    public BaseResult updateCcs(
            @PathVariable String processId,@RequestBody UpdateCcRequest request) {
        String accountId = RequestContextExtUtils.getOperatorId();
        String tenantId = RequestContextExtUtils.getTenantId();
        processCcService.updateCc(processId, accountId, tenantId, request);
        return BaseResult.success();
    }

    @RestMapping(path = "/processes/batch-update-ccs", method = RequestMethod.POST)
    @ApiOperation(value = "批量修改合同抄送人列表",httpMethod = "POST")
    public BaseResult<BatchUpdateCcsResponse> batchUpdateCcs(@RequestHeader(HEADER_KEY_OPERATOR_ID) String accountId,
                                                             @RequestHeader(HEADER_KEY_TENANT_ID) String tenantId,
                                                             @RequestBody BatchUpdateCcsRequest request) {
        return BaseResult.success(processCcService.batchUpdateCcs(accountId,tenantId,request));
    }
    @RestMapping(path = "/processes/filter-update-cc-list",method = RequestMethod.POST)
    @ApiOperation(value = "查询合同可修改抄送人列表", httpMethod = "POST")
    public BaseResult<CanUpdateCcProcessListResponse> filterCanUpdateCcList(
            @RequestHeader(HEADER_KEY_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_KEY_TENANT_ID) String tenantId,
            @RequestBody CanUpdateCcProcessListRequest request) {
        return BaseResult.success(processService.filterCanUpdateCcList(accountId,tenantId,request));
    }

    @RestMapping(path = "/processes/{processId}/status", method = RequestMethod.GET)
    @ApiOperation(value = "查询发起后流程状态", httpMethod = "GET")
    public BaseResult<ProcessStartStatusResponse> processStartStatus(
            @PathVariable String processId) {
        return BaseResult.success(processService.getProcessStartStatus(processId));
    }

    @RestMapping(path = "/processes/terminate-info", method = RequestMethod.GET)
    @ApiOperation(value = "获取合同终止详细信息", httpMethod = "GET")
    public BaseResult<GetProcessTerminateDetailResponse> getProcessTerminateDetail(
            @RequestParam(value = "processId") String processId) {
        return BaseResult.success(
                processService.getProcessTerminateDetail(
                        RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId(),
                        processId));
    }

    /** 获取合同签署信息 */
    @RestMapping(path = "/processes/{processId}/sign-info", method = RequestMethod.GET)
    @ApiOperation(value = "获取合同签署信息", httpMethod = "GET")
    public BaseResult<ProcessSignInfoResponse> processSignInfo(@PathVariable String processId) {
        return BaseResult.success(
                shareDownloadService.processSignInfo(
                        RequestContextExtUtils.getOperatorId(),
                        StringUtils.isNotBlank(RequestContextExtUtils.getTenantId())
                                ? RequestContextExtUtils.getTenantId()
                                : RequestContextExtUtils.getOperatorId(),
                        processId));
    }

    @RestMapping(path = "/processes/{processId}/config", method = RequestMethod.POST)
    @ApiOperation(value = "查询流程配置信息", httpMethod = "POST")
    public BaseResult<ProcessConfigResponse> processConfig(
            @PathVariable String processId,
            @RequestBody QueryProcessConfigRequest request) {
        return BaseResult.success(processConfigService.queryProcessConfig(processId,request));
    }

    /**
     * 单个重置合同流程
     *
     * @return
     * @throws BaseRuntimeException
     */
    @ApiOperation(value = "重置合同流程, 回退到上一流程节点", httpMethod = "PUT")
    @RestMapping(path = "/processes/{processId}/reset", method = RequestMethod.PUT)
    public BaseResult resetProcess(
            @ApiParam(value = "合同流程id", required = true) @PathVariable String processId,
            @RequestBody @Valid ProcessResetRequest param) {

        processService.singleReset(processId, param.getAccountId(), param.getAuthorizedAccountId());

        return BaseResult.success();
    }

    /**
     * 重置合同流程校验
     *
     * @return
     * @throws BaseRuntimeException
     */
    @MemberCheck(accountId = "#accountId", subjectId = "#subjectId")
    @ApiOperation(value = "重置合同流程校验", httpMethod = "GET")
    @RestMapping(path = "/processes/{processId}/reset-check", method = RequestMethod.GET)
    public BaseResult<ProcessResetCheckResponse> resetProcess(
            @ApiParam(value = "合同流程id", required = true) @PathVariable String processId,
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId) {

        ProcessResetCheckResponse response = processService.singleResetCheck(processId, accountId, subjectId);

        return BaseResult.success(response);
    }

    /**
     * 获取发起类型
     *
     * @return
     * @see ProcessStartType
     * @throws BaseRuntimeException
     */
    @ApiOperation(value = "获取发起类型", httpMethod = "GET")
    @RestMapping(path = "/processes/{processId}/start-type", method = RequestMethod.GET)
    public BaseResult getResetType(
            @ApiParam(value = "合同流程id", required = true) @PathVariable String processId) {

        Integer startType = processStartHelper.getStartType(processId);

        return BaseResult.success(new ProcessStartInfoResponse(startType));
    }

    /**
     * 单个撤回
     *
     * @param request 单个撤回请求
     * @return 单个撤回结果
     */
    @RestMapping(path = "/processes/{processId}/revoke", method = RequestMethod.PUT)
    @ApiOperation(value = "单个撤回", httpMethod = "PUT")
    public BaseResult<?> revokeProcess(
            @ApiParam(value = "合同流程id", required = true) @PathVariable String processId,
            @RequestHeader(HEADER_KEY_OPERATOR_ID) String accountId,
            @RequestBody ProcessRevokeRequest request) {
        if (StringUtils.isBlank(request.getSubjectId())) {
            request.setSubjectId(RequestContextExtUtils.getTenantId());
        }
        request.setAccountId(accountId);
        processService.singleRevoke(processId, RequestContextExtUtils.getAppId(), request);
        return BaseResult.success();
    }

    /**
     * 单个催办
     *
     * @return
     * @throws BaseRuntimeException
     */
    @RestMapping(path = "/processes/{processId}/rush", method = RequestMethod.PUT)
    @ApiOperation(value = "单个催办", httpMethod = "PUT")
    public BaseResult rushProcess(
            @ApiParam(value = "合同流程id", required = true) @PathVariable String processId,
            @RequestBody ProcessRushRequest param)
            throws BaseRuntimeException {

        if (StringUtils.isBlank(param.getAccountId())) {
            param.setAccountId(RequestContextExtUtils.getOperatorId());
        }
        if (StringUtils.isBlank(param.getSubjectId())) {
            param.setSubjectId(RequestContextExtUtils.getTenantId());
        }
        processService.singleRush(processId, RequestContextExtUtils.getAppId(), param, true);
        return BaseResult.success();
    }


    @RestMapping(path = "/processes/more-action-data", method = RequestMethod.GET)
    @ApiOperation(value = "查询流程配置信息", httpMethod = "GET")
    public BaseResult<ProcessMoreActionDataResponse> moreActionData(@RequestParam String processId) {
        Integer count = relationContractService.relationCount(RequestContextExtUtils.getTenantId(), processId);
        ProcessMoreActionDataResponse response = new ProcessMoreActionDataResponse();
        response.setRelationContractCount(count);
        return BaseResult.success(response);
    }


    /** 合同签署单个文件pdf转图片 */
    @RestMapping(path = "/processes/{processId}/files/{fileKey}/view-image", method = RequestMethod.GET)
    @ApiOperation(value = "合同签署文件pdf转图片", httpMethod = "GET")
    public BaseResult<ShareDownloadShareFilePreviewResponse> processViewImage(
            @PathVariable String processId,
    @PathVariable("fileKey") String fileKey,
            @ApiParam(value = "转换目标页码,从1开始") @RequestParam(value = "pageNum", required = false)
                    String pageNum,
            @ApiParam(value = "转换图片类型") @RequestParam(value = "imageType", required = false)
                    String imageType,
            @ApiParam(value = "转换方式,optimize时为异步，其余走同步") @RequestParam(value = "convertWay", required = false)
                    String convertWay) {

        ShareDownloadPreviewBO.PdfPageImagesBO pageImagesBO =
                shareDownloadService.processViewImage(
                        RequestContextExtUtils.getOperatorId(),
                        StringUtils.isNotBlank(RequestContextExtUtils.getTenantId())
                                ? RequestContextExtUtils.getTenantId()
                                : RequestContextExtUtils.getOperatorId(),
                        processId,
                        new ShareDownloadPreviewBO(
                                fileKey,
                                Pdf2ImageTypeEnum.convert2Pdf2ImageTypeEnum(imageType),
                                pageNum,
                                convertWay));
        return BaseResult.success(
                new ShareDownloadShareFilePreviewResponse(
                        pageImagesBO.getPdfPageImagesTotal(),
                        pageImagesBO.getConvertStatus(),
                        pageImagesBO.getPdfPageImages().stream()
                                .map(ShareDownloadVOConvert::convert2PdfPageImageVo)
                                .collect(Collectors.toList())));
    }

    /**
     * 查看流程节点信息
     *
     * @param processId
     * @return
     */
    @RestMapping(path = "/processes/{processId}/detail", method = RequestMethod.GET)
    @ApiOperation(value = "查看流程用户详情")
    public BaseResult<ProcessNodeUsersResponse> getProcessDetail(
            @ApiParam(value = "流程id", required = true) @PathVariable String processId) {

        ProcessNodeUsersResponse response =
                processCommonService.getProcessNodeUserList(
                        processId,
                        RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId());
        return BaseResult.success(response);
    }

    /**
     * 修改合同流程签订截止时间
     *
     * @param processId
     * @param request
     * @return
     */
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/processes/{processId}/update-sign-validity", method = RequestMethod.POST)
    @ApiOperation(value = "修改合同流程签订截止时间")
    public BaseResult updateSignValidity(
            @ApiParam(value = "流程id", required = true) @PathVariable String processId,
            @RequestBody UpdateSignValidityRequest request) {
        request.setAccountId(RequestContextExtUtils.getOperatorId());
        request.setSubjectId(RequestContextExtUtils.getTenantId());
        processService.updateSignValidity(processId, request);
        return BaseResult.success();
    }

    /**
     * 批量修改合同流程签订截止时间
     *
     * @param request
     * @return
     */
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/processes/batch-update-sign-validity", method = RequestMethod.POST)
    @ApiOperation(value = "批量修改合同流程签订截止时间")
    public BaseResult batchUpdateSignValidity(@RequestBody BatchUpdateSignValidityRequest request) {
        request.setAccountId(RequestContextExtUtils.getOperatorId());
        request.setSubjectId(RequestContextExtUtils.getTenantId());
        processService.batchUpdateProcessSignValidity(request);
        return BaseResult.success();
    }

    @RestMapping(path = "/processes/{processId}/participant-status", method = RequestMethod.GET)
    @ApiOperation(value = "获取流程参与方状态", httpMethod = "GET")
    public BaseResult<ProcessParticipantStatusResponse> participantStatus(
            @PathVariable String processId, @RequestParam(value = "menuId", required = false) String menuId) {
        // 创建业务层入参
        ProcessParticipantStatusInputDTO inputDTO = new ProcessParticipantStatusInputDTO();
        inputDTO.setProcessId(processId);
        inputDTO.setMenuId(menuId);
        inputDTO.setAccountOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        // 调用业务层
        ProcessParticipantStatusOutputDTO outputDTO =
                processService.queryParticipantStatus(inputDTO);
        // 依据业务层出参组装响应对象
        ProcessParticipantStatusResponse response =
                mapperFactory
                        .getMapperFacade()
                        .map(outputDTO, ProcessParticipantStatusResponse.class);
        return BaseResult.success(response);
    }


    @RestMapping(path = "/processes/participant-auth-way", method = RequestMethod.GET)
    @ApiOperation(value = "查询签署方口令", httpMethod = "GET")
    public BaseResult<ProcessParticipantAuthWayResponse> participantAuthWay(
            @ApiParam(value = "合同流程id", required = true) @RequestParam String processId
    ) {
        List<ProcessParticipantAuthInfo> authInfos = processService.queryParticipantAuthWay(processId, RequestContextExtUtils.getOperatorId(), RequestContextExtUtils.getTenantId());
        //转换结果
        ProcessParticipantAuthWayResponse response = ProcessesRestConvert.convertProcessParticipantAuthWayResponse(authInfos);
        return BaseResult.success(response);
    }

    @ApiOperation(value = "修改签署人", httpMethod = "POST")
    @RestMapping(path = "/processes/change-signer", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<Boolean> changeSigner(@RequestBody ProcessChangeSignerRequest request) {
        return BaseResult.success(processAccountService.changeSigner(request));
    }

    @ApiOperation(value = "获取按钮配置", httpMethod = "POST")
    @RestMapping(path = "/processes/button-config", method = RequestMethod.POST)
    public BaseResult<ProcessButtonConfigResponse> buttonConfig(@RequestBody ProcessButtonConfigRequest request) {
        String tenantOid = RequestContextExtUtils.getTenantId();
        String operatorId = RequestContextExtUtils.getOperatorId();
        String appId = RequestContextExtUtils.getAppId();
        ProcessButtonConfigInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, ProcessButtonConfigInputDTO.class);
        inputDTO.setOperationOid(operatorId);
        inputDTO.setAppId(appId);
        inputDTO.setTenantOid(tenantOid);
        ProcessButtonConfigOutputDTO outputDTO = processButtonService.buttonConfig(inputDTO);
        return BaseResult.success(mapperFactory.getMapperFacade().map(outputDTO, ProcessButtonConfigResponse.class));
    }

}

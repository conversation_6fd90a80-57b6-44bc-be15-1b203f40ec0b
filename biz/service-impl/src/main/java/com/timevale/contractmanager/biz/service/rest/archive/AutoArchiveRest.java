package com.timevale.contractmanager.biz.service.rest.archive;

import com.timevale.contractmanager.core.model.dto.request.autoArchive.ArchiveRuleAccountIdRequest;
import com.timevale.contractmanager.core.model.dto.response.autoArchive.AutoArchiveMenuResponse;
import com.timevale.contractmanager.core.model.dto.user.SimpleUserAccountInfoResponse;
import com.timevale.contractmanager.core.service.autoarchive.AutoArchiveService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * @Author:jianyang
 * @since 2021-05-06 16:36
 */
@Api(tags = "台账2.0-自动分类")
@ExternalService
@RestMapping(path = "/v2")
public class AutoArchiveRest {

	@Autowired
	private AutoArchiveService autoArchiveService;

	@RestMapping(path = "/autoArchiveRule/tenants/tenantId",method = RequestMethod.GET)
	@ApiOperation(value = "分类管理",httpMethod = "GET")
	public BaseResult<AutoArchiveMenuResponse> list(){
		String operatorId = RequestContextExtUtils.getOperatorId();
		String tenantId = RequestContextExtUtils.getTenantId();
		return BaseResult.success(autoArchiveService.list(tenantId,operatorId));
	}

	@RestMapping(path = "/autoArchiveRule/tenants/tenantId/accountInfo",method = RequestMethod.POST)
	@ApiOperation(value = "批量获取用户信息",httpMethod = "POST")
	@MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
	public BaseResult<List<SimpleUserAccountInfoResponse>> getAccountDetail(@RequestBody ArchiveRuleAccountIdRequest accountIdRequest){
		return BaseResult.success(autoArchiveService.getAccountDetail(accountIdRequest.getAccountId()));
	}
}

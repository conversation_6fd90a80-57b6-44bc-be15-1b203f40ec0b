package com.timevale.contractmanager.biz.service.rest.grouping;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.enums.grouping.DownloadConfigTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.bo.UserDownloadConfigBO;
import com.timevale.contractmanager.core.model.bo.UserDownloadConfigDetail;
import com.timevale.contractmanager.core.model.dto.request.grouping.file.ProcessDownloadConfigRequest;
import com.timevale.contractmanager.core.model.dto.response.file.ProcessDownloadConfigResponse;
import com.timevale.contractmanager.core.service.enums.FileDownNamingFormatEnum;
import com.timevale.contractmanager.core.service.enums.FileDownParticipantsFormatEnum;
import com.timevale.contractmanager.core.service.grouping.UserDownloadConfigService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: duhui
 * @since: 2021/11/15 5:37 下午
 */
@Api(tags = "用户下载自定义配置")
@ExternalService
@RestMapping(path = "/v2")
public class UserDownloadConfigRest {

    @Autowired private UserDownloadConfigService userDownloadConfigService;

    @RestMapping(path = "/processes/download-config/save", method = RequestMethod.POST)
    @ApiOperation(value = "保存用户下载设置", httpMethod = "POST")
    public BaseResult<Void> saveConfig(ProcessDownloadConfigRequest request) {
        //防止错误枚举输入
        List<FileDownNamingFormatEnum> namingFormatEnums = Lists.newArrayList();
        if (!ListUtils.isEmpty(request.getNamingFormat())) {
            namingFormatEnums =
                    request.getNamingFormat().stream()
                            .map(FileDownNamingFormatEnum::from)
                            .distinct()
                            .collect(Collectors.toList());
        }
        List<FileDownParticipantsFormatEnum> participantsFormatEnums = Lists.newArrayList();
        if (!ListUtils.isEmpty(request.getParticipantsFormat())) {
            participantsFormatEnums =
                    request.getParticipantsFormat().stream()
                            .map(FileDownParticipantsFormatEnum::from)
                            .distinct()
                            .collect(Collectors.toList());
        }
        checkParam(namingFormatEnums, participantsFormatEnums);

        UserDownloadConfigBO bo = new UserDownloadConfigBO();
        String oid = RequestContextExtUtils.getOperatorId();
        bo.setOid(oid);
        bo.setType(DownloadConfigTypeEnum.from(request.getDownloadConfigType()).getType());
        UserDownloadConfigDetail configDetail = new UserDownloadConfigDetail();
        configDetail.setNamingFormats(
                namingFormatEnums.stream()
                        .map(FileDownNamingFormatEnum::getType)
                        .collect(Collectors.toList()));
        configDetail.setParticipantsFormats(
                participantsFormatEnums.stream()
                        .map(FileDownParticipantsFormatEnum::getType)
                        .collect(Collectors.toList()));
        configDetail.setSuitableForFile(request.isSuitableForFile());
        bo.setConfigDetail(configDetail);
        userDownloadConfigService.saveConfig(bo);
        return BaseResult.success();
    }

    @RestMapping(path = "/processes/download-config/get", method = RequestMethod.GET)
    @ApiOperation(value = "获取用户下载设置", httpMethod = "GET")
    public BaseResult<ProcessDownloadConfigResponse> queryConfig() {
        String oid = RequestContextExtUtils.getOperatorId();

        ProcessDownloadConfigResponse response = new ProcessDownloadConfigResponse();
        UserDownloadConfigBO bo = userDownloadConfigService.getByOid(oid);
        if (bo == null) {
            return BaseResult.success(response);
        }

        response.setDownloadConfigType(bo.getType());
        UserDownloadConfigDetail configDetail = bo.getConfigDetail();
        if (configDetail == null) {
            response.setNamingFormat(FileDownNamingFormatEnum.getDefaultTypes());
            response.setParticipantsFormat(FileDownParticipantsFormatEnum.getDefaultTypes());
            response.setSuitableForFile(false);
        } else {
            response.setNamingFormat(configDetail.getNamingFormats());
            response.setParticipantsFormat(configDetail.getParticipantsFormats());
            response.setSuitableForFile(configDetail.isSuitableForFile());
        }
        return BaseResult.success(response);
    }

    private void checkParam(
            List<FileDownNamingFormatEnum> namingFormatEnums,
            List<FileDownParticipantsFormatEnum> participantsFormatEnums) {
        if (namingFormatEnums.contains(FileDownNamingFormatEnum.PARTICIPANTS)
                && ListUtils.isEmpty(participantsFormatEnums)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PARTICIPANTS_FORMAT_IS_EMPTY);
        }
    }
}

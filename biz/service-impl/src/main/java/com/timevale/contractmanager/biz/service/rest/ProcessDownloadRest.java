package com.timevale.contractmanager.biz.service.rest;

import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.*;

import com.timevale.contractmanager.core.model.dto.request.grouping.process.ProcessDownloadRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.ProcessDownloadUrlRequest;
import com.timevale.contractmanager.core.model.dto.response.processdownload.*;
import com.timevale.contractmanager.core.model.dto.response.processdownload.ProcessDownloadCodeResponse;
import com.timevale.contractmanager.core.service.process.ProcessDownloadService;
import com.timevale.contractmanager.core.service.process.bean.ProcessDownloadNumberResult;
import com.timevale.contractmanager.core.service.process.bean.ProcessDownloadResult;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2025-01-14
 **/
@Api(tags = "合同流程下载相关接口", description = "合同流程下载相关接口")
@ExternalService
@RestMapping(path = "/v3/processes/download")
@Slf4j
public class ProcessDownloadRest extends BaseRest {

    @Autowired private ProcessDownloadService processDownloadService;

    /**
     * 下载所有符合搜索条件的合同
     *
     * @param request
     * @return
     */
    @RestMapping(path = "/code", method = RequestMethod.POST)
    @ApiOperation(value = "下载满足条件的合同", httpMethod = "POST")
    public BaseResult<ProcessDownloadCodeResponse> download(
            @RequestHeader(HEADER_TSIGN_OPEN_APP_ID) String appId,
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId,
            @RequestBody ProcessDownloadRequest request){
        request.setClientId(RequestContextExtUtils.getClientId());
        ProcessDownloadResult downloadResult = processDownloadService.download(appId, accountId, subjectId, request);
        ProcessDownloadCodeResponse response = new ProcessDownloadCodeResponse();
        response.setDownloadCode(downloadResult.getDownloadCode());
        response.setGoToJobCenter(downloadResult.isGoToTaskCenter());
        return BaseResult.success(response);
    }

    @RestMapping(path = "/number", method = RequestMethod.POST)
    @ApiOperation(value = "统计满足条件的下载合同数", httpMethod = "POST")
    public BaseResult<ProcessDownloadNumberResponse> downloadNumber(
            @RequestHeader(HEADER_TSIGN_OPEN_APP_ID) String appId,
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId,
            @RequestBody ProcessDownloadRequest request) {
        request.setClientId(RequestContextExtUtils.getClientId());
        ProcessDownloadNumberResult downloadResult = processDownloadService.downloadNum(appId, accountId, subjectId, request);
        ProcessDownloadNumberResponse response = new ProcessDownloadNumberResponse();
        response.setDownloadNumber(downloadResult.getTotalSize());
        return BaseResult.success(response);
    }

    @RestMapping(path = "/url", method = RequestMethod.GET)
    @ApiOperation(value = "获取下载地址", httpMethod = "GET")
    public BaseResult<ProcessDownloadUrlResponse> downloadUrl(
            @RequestHeader(HEADER_TSIGN_OPEN_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String subjectId,
            @ModelAttribute ProcessDownloadUrlRequest request){
        String downloadUrl = processDownloadService.downloadUrl(accountId, subjectId, request.getDownloadScene(), request.getDownloadCode());
        ProcessDownloadUrlResponse response = new ProcessDownloadUrlResponse();
        response.setDownloadUrl(downloadUrl);
        return BaseResult.success(response);
    }
}

package com.timevale.contractmanager.biz.service.rest.grouping;

import com.timevale.contractmanager.core.model.dto.request.grouping.RequestContextRequest;
import com.timevale.contractmanager.core.service.grouping.AiRuleService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 智能台账Rest
 *
 * @author: xuanzhu
 * @since: 2019-10-24 14:16
 */
@Api(tags = "智能台账")
@ExternalService
@RestMapping(path = "/v2")
public class StandingBookRest {

    @Autowired private AiRuleService aiRuleService;

    @RestMapping(path = "/standing_book/rule/template/check", method = RequestMethod.GET)
    @ApiOperation(value = "智能台账2.0-流程模板是否绑定台账规则", httpMethod = "GET")
    public BaseResult<Boolean> templateCheck(
            @ApiParam(value = "流程模板id", required = true) @RequestParam String flowTemplate) {
        RequestContextRequest request = RequestContextExtUtils.getRequestContextRequest();
        return BaseResult.success(aiRuleService.checkFlowTemplateRule(flowTemplate, request));
    }
}

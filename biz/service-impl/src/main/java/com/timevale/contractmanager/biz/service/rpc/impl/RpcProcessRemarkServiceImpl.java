package com.timevale.contractmanager.biz.service.rpc.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.dal.bean.ProcessRemarkDO;
import com.timevale.contractmanager.common.service.api.RpcProcessRemarkService;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkAddRequest;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkListResult;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkResult;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkVisibleListRequest;
import com.timevale.contractmanager.common.service.enums.ProcessRemarkPermissionType;
import com.timevale.contractmanager.common.service.enums.ProcessRemarkSourceEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.EnvelopeClient;
import com.timevale.contractmanager.common.service.model.QueryProcessRemarkModel;
import com.timevale.contractmanager.common.service.model.BatchQueryProcessRemarksModel;
import com.timevale.contractmanager.common.service.result.BatchProcessRemarkResult;
import com.timevale.contractmanager.core.service.convert.EpaasConvertor;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.ProcessRemarkService;
import com.timevale.contractmanager.core.service.process.bean.ProcessRemarkBizRequest;
import com.timevale.contractmanager.core.service.util.EpaasUuidBase32Util;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.mandarin.common.result.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.SERVICE_BUSY;

/**
 * 合同备注服务接口实现类
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@RestService
@Slf4j
public class RpcProcessRemarkServiceImpl implements RpcProcessRemarkService {

    @Autowired private ProcessRemarkService processRemarkService;

    @Autowired private EnvelopeClient envelopeClient;

    @Autowired private BaseProcessService baseProcessService;

    @Override
    public BaseResult addRemark(ProcessRemarkAddRequest request) {
        processRemarkService.addRemark(convertToBizRequest(request));
        return new BaseResult(true);
    }

    @Override
    public ProcessRemarkListResult listByProcessId(String processId) {
        List<ProcessRemarkDO> processRemarkDOS = processRemarkService.listByProcessId(processId);
        List<ProcessRemarkResult> remarkResults =
                processRemarkDOS.stream().map(this::convertToResult).collect(Collectors.toList());
        ProcessRemarkListResult result = new ProcessRemarkListResult();
        result.setList(remarkResults);
        return result;
    }

    @Override
    public ProcessRemarkListResult visibleListByProcessId(ProcessRemarkVisibleListRequest request) {
        List<ProcessRemarkDO> processRemarkDOS;
        if (baseProcessService.isEpaas(request.getProcessId()) && request.getSubjectOids().size() == 1) {
            String envelopeId = EpaasUuidBase32Util.decodeBase32ToUuidStr(request.getProcessId(), true);
            processRemarkDOS = EpaasConvertor.toProcessRemarkDOList(request.getProcessId(), envelopeClient.queryRemark(envelopeId, request.getSubjectOids().get(0)));
        } else {
            processRemarkDOS = processRemarkService.listByProcessId(request.getProcessId());
        }
        List<ProcessRemarkResult> remarkResults =
                processRemarkDOS.stream()
                        .filter(remarkDO -> request.getSubjectOids().stream()
                                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).contains(remarkDO.getSubjectOid()))
                        .map(this::convertToResult).collect(Collectors.toList());
        // 过滤合同备注不可见内容
        remarkResults = handleCanViewPermission(request,remarkResults);
        ProcessRemarkListResult result = new ProcessRemarkListResult();
        result.setList(remarkResults);
        return result;
    }

    @Override
    public ProcessRemarkListResult querySubjectRemarks(QueryProcessRemarkModel model) {
        List<ProcessRemarkDO> processRemarkDOS = processRemarkService.querySubjectRemarks(model);
        List<ProcessRemarkResult> remarkResults =
                processRemarkDOS.stream().map(this::convertToResult).collect(Collectors.toList());
        return new ProcessRemarkListResult(remarkResults);
    }

    @Override
    public BatchProcessRemarkResult batchQueryProcessRemark(BatchQueryProcessRemarksModel model) {
        BatchProcessRemarkResult batchProcessRemarkResult = new BatchProcessRemarkResult();
        if (CollectionUtils.isEmpty(model.getProcessIds())) {
            batchProcessRemarkResult.setProcessRemarkResultMap(Maps.newHashMap());
            return batchProcessRemarkResult;
        }
        List<ProcessRemarkDO> processRemarkDOS = processRemarkService.listByProcessIds(model.getProcessIds());

        Map<String, List<ProcessRemarkResult>> resultMap = Optional.ofNullable(processRemarkDOS).orElse(Lists.newArrayList()).stream()
                .filter(Objects::nonNull)
                .filter(processRemarkDO -> StringUtils.isBlank(model.getSubjectId()) || model.getSubjectId().equals(processRemarkDO.getSubjectOid()))
                .map(this::convertToResult)
                .collect(Collectors.groupingBy(ProcessRemarkResult::getProcessId));
        batchProcessRemarkResult.setProcessRemarkResultMap(resultMap);
        return batchProcessRemarkResult;
    }

    private ProcessRemarkResult convertToResult(ProcessRemarkDO processRemarkDO) {
        if(processRemarkDO == null){
            return new ProcessRemarkResult();
        }
        ProcessRemarkResult processRemarkResult = new ProcessRemarkResult();
        processRemarkResult.setProcessId(processRemarkDO.getProcessId());
        processRemarkResult.setRemark(processRemarkDO.getRemark());
        processRemarkResult.setAccountName(processRemarkDO.getAccountName());
        processRemarkResult.setAccountLoginMobile(processRemarkDO.getAccountLoginMobile());
        processRemarkResult.setAccountLoginEmail(processRemarkDO.getAccountLoginEmail());
        processRemarkResult.setAccountOid(processRemarkDO.getAccountOid());
        processRemarkResult.setAccountGid(processRemarkDO.getAccountGid());
        processRemarkResult.setSubjectOid(processRemarkDO.getSubjectOid());
        processRemarkResult.setSubjectGid(processRemarkDO.getSubjectGid());
        processRemarkResult.setCreateTime(processRemarkDO.getCreateTime());
        processRemarkResult.setModifyTime(processRemarkDO.getModifyTime());
        if (StringUtils.isNotBlank(processRemarkDO.getExt())) {
            ProcessRemarkResult.ProcessRemarkExt remarkExt = 
                    JsonUtils.json2pojo(processRemarkDO.getExt(), ProcessRemarkResult.ProcessRemarkExt.class);
            processRemarkResult.setExt(remarkExt);
        } else {
            // 兼容历史数据，为空默认全部可见
            ProcessRemarkResult.ProcessRemarkExt remarkExt = new ProcessRemarkResult.ProcessRemarkExt();
            remarkExt.setPermissionType(ProcessRemarkPermissionType.NO_LIMIT_PERMISSION.getType());
            processRemarkResult.setExt(remarkExt);
        }
        return processRemarkResult;
    }

    private ProcessRemarkBizRequest convertToBizRequest(ProcessRemarkAddRequest request) {
        ProcessRemarkBizRequest processRemarkBizRequest = new ProcessRemarkBizRequest();
        processRemarkBizRequest.setRemark(request.getRemark());
        processRemarkBizRequest.setProcessId(request.getProcessId());
        processRemarkBizRequest.setAccountName(request.getAccountName());
        processRemarkBizRequest.setAccountLoginMobile(request.getAccountLoginMobile());
        processRemarkBizRequest.setAccountLoginEmail(request.getAccountLoginEmail());
        processRemarkBizRequest.setAccountId(request.getAccountId());
        processRemarkBizRequest.setAccountGid(request.getAccountGid());
        processRemarkBizRequest.setSubjectId(request.getSubjectId());
        processRemarkBizRequest.setSubjectGid(request.getSubjectGid());
        return processRemarkBizRequest;
    }
    
    private List<ProcessRemarkResult> handleCanViewPermission(ProcessRemarkVisibleListRequest request, List<ProcessRemarkResult> remarkResults) {
        if (CollectionUtils.isEmpty(remarkResults)) {
            return new ArrayList<>();
        }

        ProcessRemarkSourceEnum processRemarkSourceEnum = ProcessRemarkSourceEnum.form(request.getSource());
        if (processRemarkSourceEnum == null) {
            return new ArrayList<>();
        }
            
        // 更具来源配置的过滤条件过滤
        return remarkResults.stream()
                .filter(remarkResult -> !processRemarkSourceEnum.getFilter().contains(remarkResult.getExt().getPermissionType()))
                .collect(Collectors.toList());
    }
}

package com.timevale.contractmanager.biz.service.rpc.impl.grouping;

import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.grouping.FilePermissionEnum;
import com.timevale.contractmanager.common.service.integration.client.ShortUrlClient;
import com.timevale.contractmanager.core.model.dto.user.OrgDeptRichDTO;
import com.timevale.contractmanager.core.service.grouping.GroupingFileService;
import com.timevale.contractmanager.core.service.grouping.PermissionService;
import com.timevale.contractmanager.core.service.mq.model.ProcessOneClickGroupFilesMsgEntity;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.ProcessMultiBizService;
import com.timevale.contractmanager.core.service.process.ProcessSearchService;
import com.timevale.contractmanager.core.service.process.download.rebirth.ProcessExportAllService;
import com.timevale.easun.service.model.account.output.DeptBaseInfo;
import com.timevale.easun.service.model.account.output.EasunSpaceDeptOutput;
import com.timevale.esign.compontent.simple.encrypt.SimpleCipher;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/10/24
 * 归档相关工具接口
 */
@Slf4j
@RestService
public class GroupingDevelopRpcService {

    /**
     * 归档相关工具接口
     */
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ProcessSearchService processSearchService;
    @Autowired
    private ProcessExportAllService processExportAllService;
    @Autowired
    private ProcessMultiBizService processMultiBizService;
    @Autowired
    private GroupingFileService groupingFileService;
    @Autowired
    private ShortUrlClient shortUrlClient;


    // 查询是否有归档权限
    public Boolean groupingDevelopCheckGlobalPermission(String tenantOid, String operatorOid) {
        return permissionService.checkGlobalPermission(
                tenantOid,
                operatorOid,
                PrivilegeResourceEnum.ORG_ARCHIVE.name(),
                FilePermissionEnum.QUERY.name());
    }

    // 用户所属部门
    public List<String> groupingDevelopGetUserAllDepIdList(String tenantOid, String operatorOid) {
        return userCenterService.getUserAllDepIdList(operatorOid, tenantOid);
    }

    // 创建资源归属
    public void multiBizResourceBelongDevelop(String processId) {
        processMultiBizService.processDataInit(processId);
    }

    public List<OrgDeptRichDTO> developGetDeptTreeByDeptId(String orgId, String deptId, boolean multiOrgan) {
        return userCenterService.getDeptTreeByDeptId(orgId, deptId, multiOrgan);
    }

    public boolean developHaveMultiBizZone(String subjectOid) {
        return userCenterService.haveMultiBizZone(subjectOid);
    }

    /**
     * 返回所有业务空间部门,  包含子企业 + 当前企业
     * @param subjectOid 主体oid
     * @return <subjectGid, Lis<对应空间部门id>>
     */
    public Map<String, List<DeptBaseInfo>> developAllMultiBizDept(String subjectOid) {
        return userCenterService.allMultiBizDept(subjectOid);
    }

    /**
     * 返回当前用户，在当前主体的业务空间
     * 当前主体，直接在的部门，空间管理员是所有部门。关联企业返回，所有与业务空间关联的部门
     * @return  <subjectGid, Lis<对应空间部门id>>
     */
    public Map<String, List<DeptBaseInfo>> developCurrentUserMultiBizDept(String subjectOid, String operatorOid) {
        return userCenterService.currentUserMultiBizDept(subjectOid, operatorOid);
    }

    /**
     * 批量查询一批部门，在指定企业 指定用户角度是否有权限
     * 部门id 为 uuid
     */
    public Map<String, EasunSpaceDeptOutput> checkMultiBizZonePermission(String subjectOid, String operatorOid, String deptIds) {
        return userCenterService.checkMultiBizZonePermission(subjectOid, operatorOid, Arrays.stream(deptIds.split(",")).collect(Collectors.toList()));
    }

    /**
     * 任务中心出发归档
     * @param request
     */
    public String developConsumerOneClickGroupFile(ProcessOneClickGroupFilesMsgEntity request) {
        groupingFileService.consumerOneClickGroupFile(request);
        return "success";
    }




    @Data
    public static final class DevelopGenShortUrlRequest extends ToString {
        private Map<String, String> request;
    }

    public String developGenShortUrl(DevelopGenShortUrlRequest request) throws Exception {
        StringBuilder sb = new StringBuilder();
        request.getRequest().forEach((key, value) -> {
            sb.append(key).append("=").append(value).append("&");
        });
        //
        String str =  sb.toString();
        if (str.endsWith("&")) {
            str = str.substring(0, str.length() - 1);
        }

        String encodedString = SimpleCipher.INSTANCE.encode("AES", str, "UTF-8");
            encodedString = URLEncoder.encode(encodedString, "UTF-8");
        log.info("context = {}", encodedString);
        return shortUrlClient.convertShortCode(encodedString);
    }

}

package com.timevale.contractmanager.biz.service.rpc.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.api.RpcProcessPermissionService;
import com.timevale.contractmanager.common.service.enums.grouping.FilePermissionEnum;
import com.timevale.contractmanager.common.service.model.processpermission.CheckUserProcessViewableModel;
import com.timevale.contractmanager.common.service.model.processpermission.CheckUserProcessesPermissionModel;
import com.timevale.contractmanager.common.service.model.processpermission.GetProcessAccountRoleModel;
import com.timevale.contractmanager.common.service.model.processpermission.GetUserProcessAllPermissionsModel;
import com.timevale.contractmanager.common.service.result.processpermission.CheckUserProcessViewableResult;
import com.timevale.contractmanager.common.service.result.processpermission.CheckUserProcessesPermissionResult;
import com.timevale.contractmanager.common.service.result.processpermission.GetProcessAccountRoleResult;
import com.timevale.contractmanager.common.service.result.processpermission.GetUserProcessAllPermissionsResult;
import com.timevale.contractmanager.core.service.process.ProcessPermissionService;
import com.timevale.contractmanager.core.service.process.bean.CheckUserProcessViewableParam;
import com.timevale.contractmanager.core.service.process.bean.GetProcessAccountRoleParam;
import com.timevale.mandarin.common.annotation.RestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/2
 */
@RestService
@Slf4j
public class RpcProcessPermissionServiceImpl implements RpcProcessPermissionService {

    @Autowired private ProcessPermissionService processPermissionService;

    @Override
    public GetUserProcessAllPermissionsResult getUserProcessAllPermissions(
            GetUserProcessAllPermissionsModel model) {
        GetUserProcessAllPermissionsResult result = new GetUserProcessAllPermissionsResult();
        List<String> permissions = processPermissionService.getProcessPermissionList(
                model.getMenuId(),
                model.getProcessId(),
                model.getAccountId(),
                model.getSubjectId(),
                model.getOperatorSubjectId());
        result.setPermissionList(permissions);
        return result;
    }

    @Override
    public CheckUserProcessesPermissionResult checkUserProcessesPermission(
            CheckUserProcessesPermissionModel model) {
        log.info("checkUserProcessesPermission req : {}", JSON.toJSONString(model));
        CheckUserProcessesPermissionResult result = new CheckUserProcessesPermissionResult();
        result.setHasPermission(
                processPermissionService.getProcessPermissionMap(
                        model.getMenuId(),
                        model.getProcessIds(),
                        model.getAccountId(),
                        model.getSubjectId(),
                        model.getPermission(),
                        false));
        log.info("checkUserProcessesPermission result : {}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public CheckUserProcessViewableResult checkUserProcessViewable(
            CheckUserProcessViewableModel model) {
        CheckUserProcessViewableParam param = new CheckUserProcessViewableParam();
        param.setProcessId(model.getProcessId());
        param.setAccountId(model.getAccountId());
        param.setSubjectId(model.getSubjectId());
        param.setMenuId(model.getMenuId());
        param.setResourceShareId(model.getResourceShareId());
        // 校验用户是否有查看权限
        return processPermissionService.checkUserProcessViewable(param);
    }

    @Override
    public GetProcessAccountRoleResult getProcessAccountRoles(GetProcessAccountRoleModel model) {
        GetProcessAccountRoleParam param = new GetProcessAccountRoleParam();
        param.setAccountId(model.getAccountId());
        param.setInvolueSubTenant(model.getInvolueSubTenant());
        param.setProcessId(model.getProcessId());
        param.setSubjectId(model.getSubjectId());
        return processPermissionService.getProcessAccountRoles(param);
    }
}

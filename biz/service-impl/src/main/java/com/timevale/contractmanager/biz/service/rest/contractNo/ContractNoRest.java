package com.timevale.contractmanager.biz.service.rest.contractNo;

import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum;
import com.timevale.contractmanager.core.model.dto.request.contractNo.ContractNoRuleAddRequest;
import com.timevale.contractmanager.core.model.dto.request.contractNo.ContractNoRuleUpdateRequest;
import com.timevale.contractmanager.core.model.dto.response.contractNo.ContractNoIntervalResultVO;
import com.timevale.contractmanager.core.model.dto.response.contractNo.ContractNoRuleListResponse;
import com.timevale.contractmanager.core.model.dto.response.contractNo.ContractNoRuleSimpleVO;
import com.timevale.contractmanager.core.model.dto.response.contractNo.ContractNoRuleVO;
import com.timevale.contractmanager.core.service.process.ProcessContractNoService;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.manage.common.service.model.base.BaseResult;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * ContractNoRest
 *
 * <AUTHOR>
 * @since 2022/10/19 4:56 下午
 */
@Api(tags = "合同编号")
@ExternalService
@RestMapping(path = "/v1/contractNo")
public class ContractNoRest {

    @Autowired
    private ProcessContractNoService contractNoService;

    @FunctionPrivilegeCheck(function = FunctionCodeConstant.CONTRACT_NO)
    @ApiOperation(value = "合同编号规则列表", httpMethod = "GET")
    @RestMapping(path = "/rule/list", method = RequestMethod.GET)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ContractNoRuleListResponse> list(
            @ApiParam(value = "每页数量", required = true) @RequestParam Integer pageSize,
            @ApiParam(value = "当前页数", required = true) @RequestParam Integer pageNum) {
        String tenantOid = RequestContextExtUtils.getResourceTenantId();
        ContractNoRuleListResponse response = contractNoService.pageQuery(tenantOid, pageSize, pageNum);
        return BaseResult.success(response);
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstant.CONTRACT_NO)
    @ApiOperation(value = "合同编号规则详情", httpMethod = "GET")
    @RestMapping(path = "/rule/{ruleId}", method = RequestMethod.GET)
    public BaseResult<ContractNoRuleVO> detail(
            @ApiParam(value = "规则id", required = true) @PathVariable String ruleId, @ApiParam(value = "是否查询模板关联关系", required = true) Boolean mappingQuery) {
        String tenantOid = RequestContextExtUtils.getTenantId();
        ContractNoRuleVO ruleVO = contractNoService.getRuleDetail(tenantOid, ruleId, mappingQuery);
        return BaseResult.success(ruleVO);
    }

    @ApiOperation(value = "合同编号规则简要信息", httpMethod = "GET")
    @RestMapping(path = "/rule/{ruleId}/simple", method = RequestMethod.GET)
    public BaseResult<ContractNoRuleSimpleVO> simple(
            @ApiParam(value = "规则id", required = true) @PathVariable String ruleId) {
        ContractNoRuleSimpleVO ruleVO = contractNoService.getRuleSimple(ruleId);
        return BaseResult.success(ruleVO);
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_CONTRACT_NO,
            privilegeKey = PrivilegeOperationEnum.PRIVILEGE_CREATE)
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.CONTRACT_NO)
    @ApiOperation(value = "合同编号规则保存", httpMethod = "POST")
    @RestMapping(path = "/rule/save", method = RequestMethod.POST)
    public BaseResult<String> save(@RequestBody ContractNoRuleAddRequest request) {
        String tenantOid = RequestContextExtUtils.getResourceTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        request.setAccountId(operatorOid);
        request.setSubjectId(tenantOid);
        return BaseResult.success(contractNoService.saveRule(request));
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_CONTRACT_NO,
            privilegeKey = PrivilegeOperationEnum.PRIVILEGE_UPDATE)
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.CONTRACT_NO)
    @ApiOperation(value = "合同编号规则修改", httpMethod = "PUT")
    @RestMapping(path = "/rule/{ruleId}", method = RequestMethod.PUT)
    public BaseResult<Void> update(@ApiParam(value = "规则id", required = true) @PathVariable String ruleId, @RequestBody ContractNoRuleUpdateRequest request) {
        String tenantOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        request.setAccountId(operatorOid);
        request.setSubjectId(tenantOid);
        request.setRuleId(ruleId);
        contractNoService.updateRule(request);
        return BaseResult.success();
    }

    @UserPrivilegeCheck(
            resourceKey = PrivilegeResourceEnum.PRIVILEGE_RESOURCE_CONTRACT_NO,
            privilegeKey = PrivilegeOperationEnum.PRIVILEGE_DELETE)
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.CONTRACT_NO)
    @ApiOperation(value = "合同编号规则删除", httpMethod = "DELETE")
    @RestMapping(path = "/rule/{ruleId}", method = RequestMethod.DELETE)
    public BaseResult<Void> delete(@ApiParam(value = "规则id", required = true) @PathVariable String ruleId) {
        String tenantOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        contractNoService.deleteRule(tenantOid, operatorOid, ruleId);
        return BaseResult.success();
    }

    @ApiOperation(value = "获取合同编号间隔符", httpMethod = "GET")
    @RestMapping(path = "/rule/contract-no-interval", method = RequestMethod.GET)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ContractNoIntervalResultVO> simple() {
        ContractNoIntervalResultVO intervals = contractNoService.getIntervals();
        return BaseResult.success(intervals);
    }
}

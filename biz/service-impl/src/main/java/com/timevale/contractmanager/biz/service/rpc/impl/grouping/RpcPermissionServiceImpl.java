package com.timevale.contractmanager.biz.service.rpc.impl.grouping;

import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.dal.bean.grouping.MenuDO;
import com.timevale.contractmanager.common.dal.dao.grouping.MenuDAO;
import com.timevale.contractmanager.common.service.api.grouping.RpcPermissionService;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.bean.permission.GetAllMenuPermissionsModel;
import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.grouping.FilePermissionEnum;
import com.timevale.contractmanager.common.service.enums.grouping.MenuIdEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.AuthRelationRpcServiceClient;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.common.service.model.grouping.permission.CheckProcessPermissionModel;
import com.timevale.contractmanager.common.service.model.grouping.permission.CheckProcessSecretModel;
import com.timevale.contractmanager.common.service.model.grouping.permission.GetProcessAllPermissionModel;
import com.timevale.contractmanager.common.service.result.grouping.CheckProcessPermissionResult;
import com.timevale.contractmanager.common.service.result.grouping.CheckProcessSecretResult;
import com.timevale.contractmanager.common.service.result.grouping.GetProcessAllPermissionResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.authrelation.AuthRelationBizService;
import com.timevale.contractmanager.core.service.authrelation.AuthRelationPrivilegeConstant;
import com.timevale.contractmanager.core.service.grouping.PermissionService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationHistoryLastEffectiveTimeDTO;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.AccountBase;
import com.timevale.signflow.search.docSearchService.bean.ProcessInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.bean.TaskInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.param.QueryByProcessIdsParam;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdResult;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdsResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.ORG_MEMBER_NOT_EXIST;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.USER_ACCOUNT_NOT_EXIST;

/**
 * <AUTHOR>
 * @since 2019/9/26
 */
@Slf4j
@RestService
public class RpcPermissionServiceImpl implements RpcPermissionService {

    @Autowired private PermissionService permissionService;
    @Autowired private UserCenterService userCenterService;
    @Autowired private MenuDAO menuDAO;
    @Autowired private EsClient esClient;
    @Autowired private AuthRelationBizService authRelationBizService;
    @Autowired private AuthRelationRpcServiceClient authRelationRpcServiceClient;

    @Override
    public GetProcessAllPermissionResult getProcessAllPermissions(
            GetProcessAllPermissionModel model) {
        //如果总部看子企业合同，则返回总部企业， 子企业/独立企业则返回自身
        UserAccount tenantAccount = getOperatorTenant(model);
        UserAccount operatorAccount = userCenterService.getFatUserAccountDetailByOid(model.getAccountId());

        /** 1.查询全局权限 */
        Set<String> processAllPermissions = Sets.newHashSet();
        try {
            processAllPermissions =
                    permissionService.getMenuAllPermissions(
                            operatorAccount.getAccountOid(),
                            tenantAccount.getAccountOid(),
                            model.getMenuId(),
                            operatorAccount.getAccountGid(),
                            tenantAccount.getAccountGid());
        } catch (BizContractManagerException e) {
            handlePermissionException("getProcessAllPermissions", e);
        }

        if (StringUtils.isNotBlank(model.getProcessId())) {
            /** 2.查询是否是流程参与人 */
            QueryByProcessIdResult processInfo = esClient.queryById(model.getProcessId());
            if (processInfo == null) {
                return new GetProcessAllPermissionResult(new ArrayList<>(processAllPermissions));
            }
            Map<String, Set<String>> processIdPermissionCodes =
                    getProcessPermissions(
                            Arrays.asList(processInfo.getProcessInfoTotalInfo()), operatorAccount);

            if (processIdPermissionCodes.containsKey(model.getProcessId())) {
                processAllPermissions.addAll(processIdPermissionCodes.get(model.getProcessId()));
            }
        }

        return new GetProcessAllPermissionResult(new ArrayList<>(processAllPermissions));
    }

    private UserAccount getOperatorTenant(GetProcessAllPermissionModel model) {
        UserAccount tenantAccount = userCenterService.getFatUserAccountDetailByOid(model.getSubjectId());
        //操作主体和参与主体不同时，优先拿操作主体，存在场景例如API创建的企业和标准签企业，gid相同，oid不同，可能导致鉴权失败
        if (StringUtils.isNotBlank(model.getOperatorSubjectId()) && !model.getOperatorSubjectId().equals(model.getSubjectId())) {
            UserAccount operatorSubject = userCenterService.getFatUserAccountDetailByOid(model.getOperatorSubjectId());
            if (operatorSubject == null || StringUtils.isEmpty(operatorSubject.getAccountGid())) {
                return tenantAccount;
            }
            return operatorSubject;
        }
        return tenantAccount;
    }

    @Override
    public CheckProcessPermissionResult checkProcessPermissions(CheckProcessPermissionModel model) {
        String menuId = model.getMenuId();
        UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(model.getAccountId());
        UserAccount tenantAccount = null;
        if (StringUtils.isNotBlank(model.getSubjectId())) {
            tenantAccount = userCenterService.getUserAccountBaseByOid(model.getSubjectId());
        }
        if (MenuIdEnum.MENU_ALL.getMenuId().equals(model.getMenuId())) {
            // 所有已归档分类
            boolean hasPermission = false;
            try {
                hasPermission = permissionService.checkGlobalPermission(model.getSubjectId(), model.getAccountId(),
                        PrivilegeResourceEnum.ORG_ARCHIVE.getType(), model.getPermission());
            } catch (BizContractManagerException e){
                handlePermissionException("MENU_ALL checkGlobalPermission", e);
            }
            // 组装合同流程权限Map
            Map<String, Boolean> processPermissionMap =
                    buildProcessPermissionMap(model, null == tenantAccount ? operatorAccount : tenantAccount, hasPermission);
            return buildCheckProcessPermissionResult(
                    model, operatorAccount, tenantAccount, processPermissionMap);
        } else if (model.getMenuId().equals(MenuIdEnum.OPPONENT_ENTITY.getMenuId())) {
            //相对方场景
            boolean hasPermission = false;
            try {
                hasPermission = permissionService.checkGlobalPermission(model.getSubjectId(), model.getAccountId(),
                        PrivilegeResourceEnum.PRIVILEGE_RESOURCE_OPPONENT, model.getPermission());
            } catch (BizContractManagerException e){
                handlePermissionException("MENU_ID_OPPONENT checkGlobalPermission", e);
            }
            // 组装合同流程权限Map
            Map<String, Boolean> processPermissionMap =
                    buildProcessPermissionMap(model, null == tenantAccount ? operatorAccount : tenantAccount, hasPermission);
            return buildCheckProcessPermissionResult(
                    model, operatorAccount, tenantAccount, processPermissionMap);
        } else if (model.getMenuId().equals(MenuIdEnum.UNGROUPING.getMenuId())) {
            /**对于待归档的合同流程鉴权，只check全局数据权限*/
            boolean hasPermission = false;
            try {
                hasPermission = permissionService.checkGlobalPermission(model.getSubjectId(), model.getAccountId(),
                        PrivilegeResourceEnum.ORG_WAITING_ARCHIVE.name(), model.getPermission());
            } catch (BizContractManagerException e) {
                handlePermissionException("MENU_ID_WAITING_ARCHIVE checkGlobalPermission", e);
            }
            if(!hasPermission){
                List<ProcessInfoTotalInfo> processInfoTotalInfos = Lists.newArrayList();

                /**调用方都是单个流程查询，暂时loop查*/
                for(String processId : model.getProcessIds()){
                    processInfoTotalInfos.add(esClient.queryById(processId).getProcessInfoTotalInfo());
                }
                // <processId, <permissionCode>>
                Map<String,Set<String>> processIdPermissionCodes =
                        getProcessPermissions(processInfoTotalInfos, operatorAccount);

                Map<String, Boolean> processPermissionMap = Maps.newHashMap();
                model.getProcessIds().stream()
                        .forEach(
                                i -> {
                                    processPermissionMap.put(
                                            i,
                                            (processIdPermissionCodes.get(i) == null
                                                            || !processIdPermissionCodes.get(i)
                                                                    .contains(model.getPermission())) ? false : true);
                                });
                return new CheckProcessPermissionResult(processPermissionMap);
            }
            // 组装合同流程权限Map
            Map<String, Boolean> processPermissionMap =
                    buildProcessPermissionMap(model, null == tenantAccount ? operatorAccount : tenantAccount, true);
            return buildCheckProcessPermissionResult(
                    model, operatorAccount, tenantAccount, processPermissionMap);
        } else if (MenuIdEnum.MENU_AUTH_RELATION.getMenuId().equals(menuId)) {
            // 多企业管理授权关系
            return authRelationCheckProcessPermission(model);
        }

        // 已归档流程场景
        MenuDO menu = menuDAO.getByMenuId(model.getMenuId());
        // 如果menuId不存在， 默认没有权限
        if (null == menu) {
            log.warn("menuId is invalid: {}", model.getMenuId());
            Map<String,Boolean> processPermissionMap = Maps.newHashMap();
            model.getProcessIds().stream().forEach(i->processPermissionMap.put(i,false));
            return new CheckProcessPermissionResult(processPermissionMap);
        }
        // 如果主体信息为空,根据菜单id获取主体信息
        if (StringUtils.isBlank(model.getSubjectId())) {
            // 查询主体账号信息
            tenantAccount = userCenterService.getUserAccountBaseByOid(menu.getOid());
        }
        // 查询流程用户权限
        Map<String, Boolean> hasPermissions =
                permissionService.checkProcessPermissions(
                        model.getProcessIds(),
                        operatorAccount,
                        tenantAccount,
                        model.getMenuId(),
                        model.getPermission(),
                        model.isCheckSecret());

        return new CheckProcessPermissionResult(hasPermissions);
    }

    /**
     * 组装合同流程权限MAP
     * @param model
     * @param tenantAccount
     * @param hasPermission
     * @return
     */
    private Map<String, Boolean> buildProcessPermissionMap(CheckProcessPermissionModel model, UserAccount tenantAccount, boolean hasPermission) {
        Map<String, Boolean> processPermissionMap = Maps.newHashMap();
        // 如果没有权限， 则默认所有合同流程均无权限
        if (!hasPermission) {
            model.getProcessIds().forEach(processId -> processPermissionMap.put(processId, false));
            return processPermissionMap;
        }
        // 判断当前登录企业是否是主企业
        List<AuthRelationHistoryLastEffectiveTimeDTO> historyEffectiveAuthRelations =
                searchHistoryEffectiveAuthRelations(tenantAccount.getAccountGid());
        // 如果有权限， 过滤获取当前主体相关的合同流程
        List<String> subjectProcessIds = filterSubjectProcessIds(model.getProcessIds(), tenantAccount, historyEffectiveAuthRelations);
        // 相关合同流程默认有权限， 其他流程默认无权限
        model.getProcessIds().forEach(processId -> processPermissionMap.put(processId, subjectProcessIds.contains(processId)));
        return processPermissionMap;
    }

    /**
     * 查询历史生效的子企业列表
     * @param tenantGid
     * @return
     */
    private List<AuthRelationHistoryLastEffectiveTimeDTO> searchHistoryEffectiveAuthRelations(String tenantGid) {
        if (StringUtils.isBlank(tenantGid)) {
            return Lists.newArrayList();
        }
        return authRelationRpcServiceClient.queryAuthRelationLastEffectiveTimeByTenantGid(tenantGid);
    }

    /**
     * 过滤获取主体相关的合同流程id列表
     *
     * @param processIds
     * @param subjectAccount
     * @return
     */
    private List<String> filterSubjectProcessIds(
            List<String> processIds,
            UserAccount subjectAccount,
            List<AuthRelationHistoryLastEffectiveTimeDTO> historyEffectiveAuthRelations) {
        QueryByProcessIdsParam processIdsParam = new QueryByProcessIdsParam();
        processIdsParam.setProcessId(processIds);
        QueryByProcessIdsResult processes = esClient.queryByProcessIds(processIdsParam);
        if (null == processes || CollectionUtils.isEmpty(processes.getProcessInfoTotalInfo())) {
            return Lists.newArrayList();
        }
        List<String> subjectProcessIds = Lists.newArrayList();
        for(ProcessInfoTotalInfo processInfo : processes.getProcessInfoTotalInfo()){
            // 需要校验的主体gid列表
            List<String> subjectGids = Lists.newArrayList();
            if (StringUtils.isNotBlank(subjectAccount.getAccountGid())) {
                subjectGids.add(subjectAccount.getAccountGid());
            }
            // 获取发起时间段范围内生效的子企业gid列表
            List<String> subSubjectGids =
                    historyEffectiveAuthRelations.stream()
                            .filter(i -> null == i.getLastEffectiveTime() || i.getLastEffectiveTime().getTime() >= processInfo.getProcessCreateTime())
                            .map(i -> i.getChildTenantGid())
                            .collect(Collectors.toList());
            subjectGids.addAll(subSubjectGids);
            // 判断是否参与主体、抄送主体、发起主体
            if (containsSubjectIn(subjectGids, processInfo.getParticipant())
                    || containsSubjectIn(subjectGids, processInfo.getCc())
                    || containsSameSubject(subjectGids, processInfo.getInitiator())) {
                subjectProcessIds.add(processInfo.getProcessId());
                continue;
            }
            // 判断是否任务相关主体
            List<TaskInfoTotalInfo> taskInfos = processInfo.getTaskInfo();
            if (CollectionUtils.isNotEmpty(taskInfos)
                    && taskInfos.stream().anyMatch(i -> containsSubjectIn(subjectGids, Arrays.asList(i.getExecute(), i.getOperator())))) {
                subjectProcessIds.add(processInfo.getProcessId());
            }
        }
        return subjectProcessIds;

    }

    /**
     * 校验合同保密性并组装返回结果
     * @param model
     * @param operator
     * @param tenant
     * @param processPermissionMap
     * @return
     */
    private CheckProcessPermissionResult buildCheckProcessPermissionResult(
            CheckProcessPermissionModel model,
            UserAccount operator,
            UserAccount tenant,
            Map<String, Boolean> processPermissionMap) {
        if (!model.isCheckSecret()) {
            return new CheckProcessPermissionResult(processPermissionMap);
        }
        return new CheckProcessPermissionResult(
                permissionService.checkMenuSecretPermission(
                        model.getPermission(), operator, tenant, processPermissionMap));
    }

    @Override
    public CheckProcessSecretResult checkProcessSecret(CheckProcessSecretModel model) {
        CheckProcessSecretResult secretResult = new CheckProcessSecretResult();
        secretResult.setHasSecret(false);
        //menuId为空认为不是从企业合同进入
        if (StringUtils.isBlank(model.getMenuId())){
            return secretResult;
        }

        //主体id过滤
        String accountId = model.getAccountId();
        String subjectId = filterSubjectId(model.getSubjectId(), accountId);

        //主企业法人也可以查看子企业的保密合同
        //合同保密需要针对管理员特殊处理 prd: http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********
        if (permissionService.checkLegalPerson(subjectId, accountId)){
            secretResult.setHasSecret(true);
        }

        return secretResult;
    }

    @Override
    public Set<String> getAllMenuPermissions(GetAllMenuPermissionsModel model) {
        return permissionService.getMenuAllPermissions(model.getOperatorOid(), model.getTenantOid(), model.getMenuId(),
                model.getOperatorGid(), model.getTenantGid());
    }

    private String filterSubjectId(String subjectId, String accountId){
        if (!subjectId.contains(",")){
            return subjectId;
        }

        ArrayList<String> subjectIds = Lists.newArrayList(subjectId.split(","));
        return subjectIds.stream().distinct()
                .filter(e -> !e.equals(accountId))
                .findFirst()
                .orElse(null);
    }

    private Map<String, Set<String>> getProcessPermissions(List<ProcessInfoTotalInfo> processInfos, UserAccount userAccount){
        Map<String,Set<String>> processIdPermissionCodes = Maps.newHashMap();
        for(ProcessInfoTotalInfo processInfo : processInfos){
            if(isAccountIn(userAccount, processInfo.getParticipant())
                    || isAccountIn(userAccount, processInfo.getCc())
                    || isSameAccount(userAccount, processInfo.getInitiator()))
            {
                Set<String> permissionCodes = processIdPermissionCodes.get(processInfo.getProcessId());
                if(permissionCodes==null){
                    permissionCodes = Sets.newHashSet();
                    processIdPermissionCodes.put(processInfo.getProcessId(), permissionCodes);
                }
                permissionCodes.add(FilePermissionEnum.QUERY.name());
            }

            if(processInfo.getTaskInfo()!=null){
                for(TaskInfoTotalInfo task:processInfo.getTaskInfo()){
                    if(isAccountIn(userAccount, Arrays.asList(task.getExecute(),task.getOperator()))){
                        Set<String> permissionCodes = processIdPermissionCodes.get(processInfo.getProcessId());
                        if(permissionCodes==null){
                            permissionCodes = Sets.newHashSet();
                            processIdPermissionCodes.put(processInfo.getProcessId(), permissionCodes);
                        }
                        permissionCodes.add(FilePermissionEnum.QUERY.name());
                    }
                }
            }
        }
        return processIdPermissionCodes;
    }

    /**
     * 判断指定主体是否在列表中
     * @param subjectGids
     * @param accounts
     * @return
     */
    private Boolean containsSubjectIn(List<String> subjectGids, List<? extends AccountBase> accounts){
        return accounts==null?false: accounts.stream().anyMatch(item->containsSameSubject(subjectGids, item));
    }

    /**
     * 判断是否包含相同主体
     * @param subjectGids
     * @param account
     * @return
     */
    private Boolean containsSameSubject(List<String> subjectGids, AccountBase account){
        String accountGid = Optional.ofNullable(account)
                .map(AccountBase::getSubject)
                .map(Account::getGid)
                .orElse(null);
        return Optional.ofNullable(subjectGids).orElse(Lists.newArrayList())
                .stream().anyMatch(gid -> gid.equals(accountGid));
    }

    private Boolean isAccountIn(UserAccount targetAccount, List<? extends AccountBase> accounts){
        return accounts==null?false: accounts.stream().anyMatch(item->isSameAccount(targetAccount, item));
    }

    private Boolean isSameAccount(UserAccount targetAccount, AccountBase account){
        String accountGid = Optional.ofNullable(account)
                .map(AccountBase::getPerson)
                .map(Account::getGid)
                .orElse(null);
        return Optional.ofNullable(targetAccount)
                .map(UserAccount::getAccountGid)
                .map(gid -> gid.equals(accountGid))
                .orElse(false);
    }

    private void handlePermissionException(String method, BizContractManagerException e) {
        if (ORG_MEMBER_NOT_EXIST.getCode().equals(e.getCode()) || USER_ACCOUNT_NOT_EXIST.getCode().equals(e.getCode())) {
            log.warn(method +" failed!", e);
            return;
        }
        throw e;
    }


    private CheckProcessPermissionResult authRelationCheckProcessPermission(CheckProcessPermissionModel model) {

        String oid = model.getAccountId();
        String tenantOid = model.getSubjectId();

        boolean hasPermission = false;
        try {
            hasPermission = permissionService.checkGlobalPermission(tenantOid, oid,
                    PrivilegeResourceEnum.AUTH_RELATION.getType(),  AuthRelationPrivilegeConstant.LOOK);
        } catch (BizContractManagerException e) {
            handlePermissionException(MenuIdEnum.MENU_AUTH_RELATION.name() + " checkGlobalPermission", e);
        }

        AccountBean accountBean = userCenterService.getAccountBeanByOid(tenantOid);

        // 授权关系这里每次只会查询一个
        Map<String, Boolean> processIdHasPermissionMap = new HashMap<>();
        for (String processId : model.getProcessIds()) {
            hasPermission = hasPermission && authRelationBizService.aboutAuthRelationProcess(processId, accountBean.getGid());
            processIdHasPermissionMap.put(processId, hasPermission);
        }
        return new CheckProcessPermissionResult(processIdHasPermissionMap);
    }


}

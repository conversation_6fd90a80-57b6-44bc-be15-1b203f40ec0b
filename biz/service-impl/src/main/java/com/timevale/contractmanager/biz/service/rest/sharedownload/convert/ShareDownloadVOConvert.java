package com.timevale.contractmanager.biz.service.rest.sharedownload.convert;

import com.timevale.contractmanager.core.model.dto.response.sharedownload.PdfPageImageVO;
import com.timevale.contractmanager.core.model.dto.sharedownload.PdfPageImageDTO;

/**
 * <AUTHOR>
 * @since 2022/2/12
 */
public abstract class ShareDownloadVOConvert {
    public static PdfPageImageVO convert2PdfPageImageVo(PdfPageImageDTO imageDTO) {
        PdfPageImageVO vo = new PdfPageImageVO();
        vo.setImageUrl(imageDTO.getImageUrl());
        vo.setPageNo(imageDTO.getPage());
        vo.setPageHeight(imageDTO.getPageHeight());
        vo.setPageWidth(imageDTO.getPageWidth());
        return vo;
    }
}

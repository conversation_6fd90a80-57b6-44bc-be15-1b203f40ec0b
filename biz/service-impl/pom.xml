<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.timevale.contractmanager</groupId>
		<artifactId>contractmanager-parent</artifactId>
		<version>1.0.1</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

	<artifactId>contractmanager-biz-service-impl</artifactId>
	<name>contractmanager/service-impl</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>contractmanager-biz-shared</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!--多语言工具包-->
		<dependency>
			<groupId>com.timevale.saas-utils</groupId>
			<artifactId>multilingual-translate-util</artifactId>
		</dependency>

		<dependency>
			<groupId>com.timevale.saas</groupId>
			<artifactId>common-privilege-util</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.timevale.saas-common-manage</groupId>
					<artifactId>saas-common-manage-common-service-facade</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.timevale.saas</groupId>
			<artifactId>common-util</artifactId>
		</dependency>
	</dependencies>
</project>

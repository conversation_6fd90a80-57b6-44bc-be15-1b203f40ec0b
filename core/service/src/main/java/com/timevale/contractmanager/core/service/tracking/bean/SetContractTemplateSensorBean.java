package com.timevale.contractmanager.core.service.tracking.bean;


import com.timevale.contractmanager.core.model.enums.SensorEnum;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import com.timevale.doccooperation.service.enums.CooperationerRoleEnum;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.doccooperation.service.input.BaseFlowTemplateInput;
import com.timevale.doccooperation.service.model.Cooperationer;
import com.timevale.docmanager.service.enums.StructComponentTypeEnum;
import com.timevale.docmanager.service.model.SignAreaContextExt;
import com.timevale.docmanager.service.model.StructComponent;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 设置合同模板埋点模版
 * <AUTHOR>
 *
 * @date 2022/2/23
 */
@Data
public class SetContractTemplateSensorBean extends SensorBaseBean {

    private BaseFlowTemplateInput request;
    private List<Cooperationer> cooperationers;
    private List<StructComponent> structList;

    @Override
    public SensorEventEnum sensorKey() {
        return SensorEventEnum.SET_CONTRACT_TEMPLATE_SEVER;
    }

    @Override
    public List<SensorEnum> sensorTemplate() {
        return Arrays.asList(
                SensorEnum.SET_TYPE,
                SensorEnum.VERSION,
                SensorEnum.CREATE_TYPE,
                SensorEnum.TEMPLATE_NAME,
                SensorEnum.NUMBER_OF_CONTRACT_FILE,
                SensorEnum.NUMBER_OF_ATTACHED_FILE,
                SensorEnum.NUMBER_OF_FILL_ENTERPRISE_FILE,
                SensorEnum.NUMBER_OF_SIGN_ENTERPRISE_FILE,
                SensorEnum.NUMBER_OF_CC_ENTERPRISE_FILE,
                SensorEnum.NUMBER_OF_FILL_PERSON_FILE,
                SensorEnum.NUMBER_OF_SIGN_PERSON_FILE,
                SensorEnum.NUMBER_OF_CC_PERSON_FILE,
                SensorEnum.NUMBER_OF_FIXED_PARTICIPANTS,
                SensorEnum.NUMBER_OF_INITIATOR,
                SensorEnum.NUMBER_OF_FAQISHIZHIDING,
                SensorEnum.NUMBER_OF_HAND_SEAL,
                SensorEnum.NUMBER_OF_AI_SEAL,
                SensorEnum.NUMBER_OF_TEMPLATE_SEAL,
                SensorEnum.NUMBER_OF_FACE_AUTHENTICATION,
                SensorEnum.NUMBER_OF_MESSAGE_AUTHENTICATION,
                SensorEnum.NUMBER_OF_PASSWORD_AUTHENTICATION,
                SensorEnum.NUMBER_OF_EMAIL_AUTHENTICATION,
                SensorEnum.NUMBER_OF_VEDIO_AUTHENTICATION,
                SensorEnum.IS_SIGN_ORDER,
                SensorEnum.SIGN_DEADLINE_TYPE,
                SensorEnum.NUMBER_OF_SIGN_DEADLINE_DAYS,
                SensorEnum.VALIDITY_DEADLINE_TYPE,
                SensorEnum.NUMBER_OF_VALIDITY_DEADLINE_DAYS,
                SensorEnum.NUMBER_OF_SEAL,
                SensorEnum.NUMBER_OF_PAGING_SEAL,
                SensorEnum.NUMBER_OF_PICTURE,
                SensorEnum.NUMBER_OF_WIDGET);
    }

    @Override
    public void initData() {

        setOperatorOid(request.getInitiatorOid());
        setOperatorGid(request.getInitiatorGid());
        setAuthorizedOid(request.getOid());
        setAuthorizedGid(request.getGid());
        setAppId(request.getAppId());
        //企业填写方数量
        int numberOfFillEnterpriseFile = 0;
        //企业签署方数量
        int numberOfSignEnterpriseFile = 0;
        //个人填写方数量
        int numberOfFillPersonFile = 0;
        //个人签署方数量
        int numberOfSignPersonFile = 0;
        //参与方为固定成员的数量
        int numberOfFixedParticipants = 0;
        //参与方为发起人本人的数量
        int numberOfInitiator = 0;
        //参与方为使用模板时指定的数量
        int numberOfFaqishizhiding = 0;
        //人脸认证的参与方数量
        int numberOfFaceAuthentication = 0;
        //短信认证的参与方数量
        int numberOfMessageAuthentication= 0;
        //密码认证的参与方数量
        int numberOfPasswordAuthentication = 0;
        //邮箱认证的参与方数量
        int numberOfEmailAuthentication =0;
        //视频认证的参与方数量
        int numberOfVedioAuthentication =0;
        //签署区数量
        int numberOfSeal = 0;
        //骑缝章数量
        int numberOfPagingSeal=0;
        //图片控件数量
        int numberOfPicture=0;

        boolean isSignOrder = false;

        if (CollectionUtils.isNotEmpty(cooperationers)){
            for (Cooperationer cooperationer: cooperationers) {
                // 参与方设置类型统计
                switch (cooperationer.getRoleSet()) {
                    case 0:
                        numberOfFixedParticipants++;
                        break;
                    case 1:
                        numberOfFaqishizhiding++;
                        break;
                    case 2:
                        numberOfInitiator++;
                        break;
                }
                if(cooperationer.getExt() != null) {
                    String cooperationerSubjectType = cooperationer.getExt().getCooperationerSubjectType();

                    // 是否顺序签署
                    if (cooperationer
                            .getRole()
                            .contains(CooperationerRoleEnum.SIGNER.getRole().toString())
                            && cooperationer.getExt().getSignOrder() > 1) {
                        isSignOrder = true;
                    }
                    // 参与方人数统计
                    if (Objects.equals(
                            cooperationerSubjectType, SubjectTypeEnum.ORG.getType().toString())) {
                        if (cooperationer
                                .getRole()
                                .contains(CooperationerRoleEnum.FORMULATER.getRole().toString())) {
                            numberOfFillEnterpriseFile++;
                        }
                        if (cooperationer
                                .getRole()
                                .contains(CooperationerRoleEnum.SIGNER.getRole().toString())) {
                            numberOfSignEnterpriseFile++;
                        }
                    } else if (Objects.equals(
                            cooperationerSubjectType, SubjectTypeEnum.PERSON.getType().toString())) {
                        if (cooperationer
                                .getRole()
                                .contains(CooperationerRoleEnum.FORMULATER.getRole().toString())) {
                            numberOfFillPersonFile++;
                        }
                        if (cooperationer
                                .getRole()
                                .contains(CooperationerRoleEnum.SIGNER.getRole().toString())) {
                            numberOfSignPersonFile++;
                        }
                    }
                    //意愿认证方式统计
                    List<String> willTypeList = cooperationer.getExt().getWillTypes();
                    if (CollectionUtils.isEmpty(willTypeList)) {
                        numberOfFaceAuthentication++;
                        numberOfMessageAuthentication++;
                        numberOfPasswordAuthentication++;
                        numberOfEmailAuthentication++;
                        numberOfVedioAuthentication++;
                    }else{
                        for (String type:willTypeList){
                            switch (type){
                                case "SIGN_PWD":
                                    numberOfPasswordAuthentication++;
                                    break;
                                case "FACE" :
                                    numberOfFaceAuthentication++;
                                    break;
                                case "EMAIL":
                                    numberOfEmailAuthentication++;
                                    break;
                                case "CODE_SMS":
                                    numberOfMessageAuthentication++;
                                    break;
                                case "FACE_AUDIO_VIDEO_DUAL":
                                    numberOfVedioAuthentication++;
                                    break;
                            }
                        }
                    }
                }
            }
        }

        //控件类型统计
        if(CollectionUtils.isNotEmpty(structList)){
            for (StructComponent struct:structList) {
                if (StructComponentTypeEnum.SIGN_POS.getType().equals(struct.getType())) {
                    numberOfSeal++;
                    String extJSon = struct.getContext().getExt();
                    if(StringUtils.isNotBlank(extJSon)){
                        SignAreaContextExt ext = JsonUtils.json2pojo(extJSon, SignAreaContextExt.class);
                        if(Objects.equals(ext.isQiFeng(),true)){
                            numberOfPagingSeal++;
                        }
                    }
                }else if (StructComponentTypeEnum.IMAGE.getType().equals(struct.getType())){
                    numberOfPicture ++;
                }
            }
        }

        getTempData().put(SensorEnum.NUMBER_OF_FILL_ENTERPRISE_FILE.getKey(),numberOfFillEnterpriseFile);
        getTempData().put(SensorEnum.NUMBER_OF_SIGN_ENTERPRISE_FILE.getKey(),numberOfSignEnterpriseFile);
        getTempData().put(SensorEnum.NUMBER_OF_FILL_PERSON_FILE.getKey(),numberOfFillPersonFile);
        getTempData().put(SensorEnum.NUMBER_OF_SIGN_PERSON_FILE.getKey(),numberOfSignPersonFile);
        getTempData().put(SensorEnum.NUMBER_OF_FIXED_PARTICIPANTS.getKey(),numberOfFixedParticipants);
        getTempData().put(SensorEnum.NUMBER_OF_INITIATOR.getKey(),numberOfInitiator);
        getTempData().put(SensorEnum.NUMBER_OF_FAQISHIZHIDING.getKey(),numberOfFaqishizhiding);
        getTempData().put(SensorEnum.NUMBER_OF_FACE_AUTHENTICATION.getKey(),numberOfFaceAuthentication);
        getTempData().put(SensorEnum.NUMBER_OF_MESSAGE_AUTHENTICATION.getKey(),numberOfMessageAuthentication);
        getTempData().put(SensorEnum.NUMBER_OF_PASSWORD_AUTHENTICATION.getKey(),numberOfPasswordAuthentication);
        getTempData().put(SensorEnum.NUMBER_OF_EMAIL_AUTHENTICATION.getKey(),numberOfEmailAuthentication);
        getTempData().put(SensorEnum.NUMBER_OF_VEDIO_AUTHENTICATION.getKey(),numberOfVedioAuthentication);
        getTempData().put(SensorEnum.IS_SIGN_ORDER.getKey(),isSignOrder);
        getTempData().put(SensorEnum.NUMBER_OF_SEAL.getKey(),numberOfSeal);
        getTempData().put(SensorEnum.NUMBER_OF_PAGING_SEAL.getKey(),numberOfPagingSeal);
        getTempData().put(SensorEnum.NUMBER_OF_PICTURE.getKey(),numberOfPicture);
    }

    @Override
    public void setRequest(Object request) {
        this.request = (BaseFlowTemplateInput) request;
    }

    public void setRequest(BaseFlowTemplateInput request) {
        this.request = request;
    }
}

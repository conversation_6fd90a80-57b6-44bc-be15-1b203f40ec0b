package com.timevale.contractmanager.core.service.contractapproval.param;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 预发起合同审批请求参数
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
public class VirtualStartApprovalFlowDTO extends ToString {
    /** 合同审批模板id */
    private String approvalTemplateId;
    /** 合同审批模板条件类型 {@link com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum} */
    private Integer approvalTemplateConditionType;
    /** 合同审批模板条件值 */
    private String approvalTemplateConditionValue;
    /** 发起主体oid */
    private String subjectOid;
    /** 发起主体gid */
    private String subjectGid;
    /** 发起主体名称 */
    private String subjectName;
    /** 发起人oid */
    private String accountOid;
    /** 发起人gid */
    private String accountGid;
    /** 发起人姓名 */
    private String accountName;
    /** 发起人所属部门id */
    private String accountDeptId;
    /** 填写流程id */
    private String cooperationId;
}

package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessFixTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessIdMsgEntity;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.enums.TaskStatusEnum;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessCooperationTaskDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.request.datacollect.AccountParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessCooperationTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessFileInfoParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSaveParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSignTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessAccountParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ProcessInfoFixDataCollectProcessor
 *
 * <AUTHOR>
 * @since 2022/7/4 6:03 下午
 */
@Slf4j
@Component
public class ProcessInfoFixDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;

    @Autowired
    private ProcessDataBuilder processDataBuilder;

    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;

    @Autowired
    private ContractProcessReadClient contractProcessQueryClient;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.fixProcessTopicName(), ProcessFixTagEnum.DEFAULT.getCode());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessIdMsgEntity entity = JsonUtils.json2pojo(data, ProcessIdMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        log.info("ProcessInfoFixDataCollectProcessor start processId:{}", collectContext.getProcessId());
        ContractProcessDTO processDTO = contractProcessQueryClient.getByProcessId(collectContext.getProcessId());
        ContractProcessSaveParam initInput =
                processDataBuilder.buildProcessInfoParam(collectContext.getProcessId());
        if (null == initInput) return;
        ContractProcessUpdateParam updateInput = new ContractProcessUpdateParam();
        updateInput.setProcessId(initInput.getProcessId());
        updateInput.setTitle(initInput.getTitle());
        updateInput.setProcessStatus(initInput.getProcessStatus());
        updateInput.setProcessDesc(initInput.getProcessDesc());
        updateInput.setProcessCreateTime(initInput.getProcessCreateTime());
        updateInput.setTemplateInfo(initInput.getTemplateInfo());
        updateInput.setCustomTags(initInput.getCustomTags());
        updateInput.setRescindRemarks(initInput.getRescindRemarks());
        updateInput.setBizScene(initInput.getBizScene());
        updateInput.setCreateWay(initInput.getCreateWay());
        updateInput.setSignCompleteTime(initInput.getSignCompleteTime());
        updateInput.setContractExpireTime(initInput.getContractExpireTime());
        updateInput.setSignExpireTime(initInput.getSignExpireTime());
        updateInput.setFileInfo(initInput.getFileInfo());
        updateInput.setSignMode(initInput.getSignMode());
        updateInput.setCustomizeConfig(initInput.getCustomizeConfig());
        ProcessAccount initiator = processDTO.getInitiator();
        initInput.getInitiator().setHidden(initiator.getHidden());
        initInput.getInitiator().setTransfer(initiator.getTransfer());
        updateInput.setInitiator(initInput.getInitiator());

        List<ProcessAccount> ccList = processDTO.getCc();
        if (CollectionUtils.isNotEmpty(initInput.getCc())) {
            for (ProcessAccountParam processAccountParam : initInput.getCc()) {
                for (ProcessAccount cc : ccList) {
                    if (oidOrGidEqual(cc.getPerson(), processAccountParam.getPerson()) &&
                            oidOrGidEqual(cc.getSubject(), processAccountParam.getTenant())) {
                        processAccountParam.setHidden(cc.getHidden());
                        processAccountParam.setTransfer(cc.getTransfer());
                    }
                }
            }
            updateInput.setCc(initInput.getCc());
        }

        List<ContractProcessCooperationTaskParam> dbCooperationTaskParams = initInput.getCooperationTasks();
        List<ContractProcessCooperationTaskDTO> cooperationTaskDTOS = processDTO.getCooperationTasks();
        if (CollectionUtils.isNotEmpty(dbCooperationTaskParams)) {
            for (ContractProcessCooperationTaskParam dbCooperation : dbCooperationTaskParams) {
                for (ContractProcessCooperationTaskDTO cooperationTaskParam : cooperationTaskDTOS) {
                    if (oidOrGidEqual(cooperationTaskParam.getExecute().getPerson(), dbCooperation.getExecute().getPerson()) &&
                            oidOrGidEqual(cooperationTaskParam.getExecute().getSubject(), dbCooperation.getExecute().getTenant())) {
                        //db中不存在的数据，从hbase中保留
                        dbCooperation.setTransfer(cooperationTaskParam.getTransfer());
                        dbCooperation.setHidden(cooperationTaskParam.getHidden());
                        dbCooperation.getExecute().setHidden(cooperationTaskParam.getExecute().getHidden());
                    }
                }
            }
            updateInput.setCooperationTasks(dbCooperationTaskParams);
        }

        List<ContractProcessSignTaskParam> dbSignTaskParams = initInput.getSignTasks();
        List<ContractProcessSignTaskDTO> signTaskDTOS = processDTO.getSignTasks();
        if (CollectionUtils.isNotEmpty(dbSignTaskParams)) {
            for (ContractProcessSignTaskParam dbSign : dbSignTaskParams) {
                for (ContractProcessSignTaskDTO signTaskParam : signTaskDTOS) {
                    boolean sameExecute = oidOrGidEqual(signTaskParam.getExecute().getPerson(), dbSign.getExecute().getPerson()) &&
                            oidOrGidEqual(signTaskParam.getExecute().getSubject(), dbSign.getExecute().getTenant());
                    boolean sameOperator = signTaskParam.getOperator() != null && dbSign.getOperator() != null && oidOrGidEqual(signTaskParam.getOperator().getPerson(), dbSign.getOperator().getPerson()) &&
                            oidOrGidEqual(signTaskParam.getOperator().getSubject(), dbSign.getOperator().getTenant());
                    if (sameExecute || sameOperator) {
                        dbSign.setSource(signTaskParam.getSource());
                        dbSign.setOperateTime(signTaskParam.getOperateTime());
                        //db中不存在的数据，从hbase中保留
                        dbSign.setTransfer(signTaskParam.getTransfer());
                        dbSign.setHidden(signTaskParam.getHidden());
                        dbSign.getExecute().setHidden(signTaskParam.getExecute().getHidden());
                        if (dbSign.getOperator() != null && signTaskParam.getOperator() != null) {
                            dbSign.getOperator().setHidden(signTaskParam.getOperator().getHidden());
                        }
                    }
                }
            }
            Optional<ContractProcessSignTaskParam> doneSignTaskOptional = dbSignTaskParams.stream()
                    .filter(signTask -> TaskStatusEnum.SIGN_DONE.getStatus().equals(signTask.getStatus())).findFirst();
            List<String> flowIdList = dbSignTaskParams.stream().filter(t -> StringUtils.isNotEmpty(t.getFlowId())).map(ContractProcessTaskParam::getFlowId).collect(Collectors.toList());
            if (doneSignTaskOptional.isPresent() && CollectionUtils.isNotEmpty(flowIdList)) {
                // 部分需要填充印章信息
                processDataBuilder.fillSealInfo(dbSignTaskParams, flowIdList.get(0));
            }
            updateInput.setSignTasks(dbSignTaskParams);
        }

        updateInput.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_INFO_FIX);
        contractProcessWriteClient.updateByProcessId(updateInput);
        log.info("ProcessInfoFixDataCollectProcessor end processId:{}", collectContext.getProcessId());
    }

    private boolean oidOrGidEqual(Account accountParam, AccountParam newPersonAccount) {
        return Objects.equals(accountParam.getOid(), newPersonAccount.getOid()) ||
                Objects.equals(accountParam.getGid(), newPersonAccount.getGid());
    }

}

package com.timevale.contractmanager.core.service.integrated.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.timevale.contractmanager.common.service.integration.client.BillClient;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.core.model.dto.response.integrated.CountInfoListModel;
import com.timevale.contractmanager.core.model.dto.response.integrated.IntegratedInformationResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.integrated.IntegratedInformationService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.enums.DocQueryEnum;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import com.timevale.signflow.search.docSearchService.param.DocCountParam;
import com.timevale.signflow.search.docSearchService.param.DocQueryParam;
import com.timevale.signflow.search.docSearchService.result.DocCountResult;
import com.timevale.signflow.search.docSearchService.result.DocQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 融合服务Impl
 *
 * <AUTHOR>
 * @since 2020-11-04 16:46
 */
@Slf4j
@Service
public class IntegratedInformationServiceImpl implements IntegratedInformationService {

    @Autowired private UserCenterService userCenterService;

    @Autowired private BillClient billClient;
    @Autowired private EsClient esClient;

    @Override
    public IntegratedInformationResponse getIntegratedInformation(
            String clientId,
            Integer pageSize,
            Integer pageNum,
            List<Integer> statusList,
            Integer docQueryType,
            Boolean approvingCount) {
        IntegratedInformationResponse response = new IntegratedInformationResponse();
        String tenantId = RequestContextExtUtils.getTenantId();
        String operatorId = RequestContextExtUtils.getOperatorId();

        UserAccount account = userCenterService.getUserAccountBaseByOid(operatorId);
        UserAccount subject = userCenterService.getUserAccountBaseByOid(tenantId);
        String subjectGid = subject.getAccountGid();
        // 非空校验
        if (StringUtils.isNotBlank(subjectGid)) {
            // 设置套餐信息
            response.setOrderInfo(billClient.queryAggreEffectiveOrderList(clientId, subjectGid));
        }

        Account esAccount = buildAccount(account);
        Account esSubject = buildAccount(subject);
        // 获取流程数量统计信息
        DocCountParam countParam = new DocCountParam();
        countParam.setPerson(esAccount);
        countParam.setSubject(esSubject);
        countParam.setDefaultStatusList(null);
        DocCountResult countResult = esClient.docCount(countParam);
        CountInfoListModel countInfoListModel = new CountInfoListModel();
        countInfoListModel.setCountList(countResult.getCountList());
        response.setCountInfo(countInfoListModel);

        // 默认为首页信息展示
        if (null == docQueryType) {
            docQueryType = DocQueryEnum.SY_SHOW.getType();
        }
        // 默认查询所有状态 查询类型为首页时该参数无效
        if (CollectionUtils.isEmpty(statusList)) {
            statusList = ProcessStatusEnum.getAllProcessStatusList();
        }
        // 获取流程信息
        DocQueryParam queryParam =
                DocQueryParam.builder()
                        .docQueryType(docQueryType)
                        .processStatusList(statusList)
                        .person(esAccount)
                        .subject(esSubject)
                        .build();
        queryParam.setPageNum(pageNum);
        queryParam.setPageSize(pageSize);
        DocQueryResult queryResult = esClient.query(queryParam);
        response.setProcessInfoList(queryResult.getProcessInfoList());
        return response;
    }

    /**
     * 构造Account
     *
     * @param userAccount UserAccount
     * @return Account
     */
    private Account buildAccount(UserAccount userAccount) {
        Account account = new Account();
        account.setGid(userAccount.getAccountGid());
        account.setOid(userAccount.getAccountOid());
        account.setRid(userAccount.getAccountRid());
        return account;
    }
}

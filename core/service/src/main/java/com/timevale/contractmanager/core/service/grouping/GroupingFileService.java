package com.timevale.contractmanager.core.service.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.GroupingInfoDO;
import com.timevale.contractmanager.core.model.dto.request.grouping.file.*;
import com.timevale.contractmanager.core.model.dto.response.grouping.file.OneClickGroupFileResponse;
import com.timevale.contractmanager.core.model.dto.response.grouping.file.OneClickGroupingFileNumberResponse;
import com.timevale.contractmanager.core.service.mq.model.GroupingFileInfo;
import com.timevale.contractmanager.core.service.mq.model.ProcessOneClickGroupFilesMsgEntity;

import java.util.List;

/**
 * 文件归档服务类
 *
 * @author: xuanzhu
 * @since: 2019-09-09 09:50
 */
public interface GroupingFileService {

    /**
     * 智能台账2.0-流程归档
     * @param request
     * @param open 是否校验权限
     * @return
     */
    public void grouping(GroupingFileRequest request,Boolean open);

    /**
     * 智能台账2.0-流程移动
     *
     * @param tenantId 空间ID
     * @param processIds 合同流程id
     * @param sourceMenuId
     * @param targetMenuIds
     * @param operatorOid
     * @return
     */
    public boolean moveArchive(String tenantId, List<String> processIds, String sourceMenuId, List<String> targetMenuIds,
                               String operatorOid);

    public boolean moveArchiveNew(String tenantId, List<String> processIds, String sourceMenuId, List<String> targetMenuIds,
                               String operatorOid, List<String> menuIdList);

    /**
     * 智能台账2.0-流程移出
     *
     * @param tenantId 空间ID
     * @param processId 合同流程id
     * @param targetMenuId
     * @param operatorOid
     * @return
     */
    public boolean reMoveArchive(String tenantId, List<String> processId, String targetMenuId, String operatorOid);

    public boolean reMoveArchiveNew(String tenantId, List<String> processId, String targetMenuId, String operatorOid, List<String> menuIdList);

    /**
     * 合同文件归档-支持事务处理
     *
     * @param tenantId 空间ID
     * @param request
     * @return
     */
    public boolean add(String tenantId, AddGroupingFileRequest request);

    /**
     * 编辑归档文件信息，只允许修改 合同主体+有效时间
     *
     * @param tenantId 空间ID
     * @param processId 合同流程id
     * @param appId 应用id
     * @param request 请求入参
     * @return
     */
    public boolean modify(String tenantId, String processId, String appId, ModifyGroupingFileRequest request);

    /**
     * 根据目录列表及流程列表获取归档合同信息
     * @param menuIds 目录列表
     * @param processIds 流程列表
     * @param limit 获取数量
     * @return 归档合同流程数量
     */
    List<String> getGroupingInfoList(List<String> menuIds, List<String> processIds, Integer limit);

    /**
     * 根据流程获取归档合同信息
     * @param processId 流程ID
     * @param subjectOid 空间id
     * @return 归档合同信息
     */
    public List<GroupingInfoDO> getGroupingInfoList(String processId,String subjectOid);

    /**
     * 根据流程获取归档合同信息
     * @param processId 流程ID
     * @param menuId 目录id
     * @return 归档合同信息
     */
    public List<GroupingInfoDO> getGroupingInfoListByMenu(String processId,String menuId);

    /**
     * 判断该企业是否有已归档合同
     * @param subjectGid
     * @return
     */
    public boolean hasGroupingInfo(String subjectGid);

    /**
     * 获取一键归档数量
     * @param request
     * @return
     */
    public OneClickGroupingFileNumberResponse getOneClickGroupingProcessNum(OneClickGroupFileRequest request);

    /**
     * 一键归档
     * @param request
     * @param menuId
     * @return
     */
    OneClickGroupFileResponse OneClickGroupFile(OneClickGroupFileRequest request, String menuId);

    /**
     * mq执行一键归档任务
     *
     * @param msgEntitys
     */
    void consumerOneClickGroupFile(ProcessOneClickGroupFilesMsgEntity msgEntitys);

    void deleteGroupingInfo(String menuId, List<String> processIdList, String subjectOid);
    /**
     * 内部批量流程归档， 不鉴权
     *
     * @param request
     * @return
     */
    List<GroupingFileInfo.FileInfo> innerBatchProcessGrouping(
            BatchProcessGroupingInnerRequest request, boolean sendEsArchive);

    List<GroupingInfoDO> queryAllByProcessId(String processId);
    /**
     * 过滤查询条件
     */
    String filterDoneProcessStatusParam(String matching);

    void deleteGroupingInfoByIds(List<Long> ids);

}

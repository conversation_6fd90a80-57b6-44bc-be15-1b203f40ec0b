package com.timevale.contractmanager.core.service.contractprocess.processor.cooperation;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ContractProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.enums.CooperationChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.CooperationChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.model.ProcessChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ContractProcessChangeProducer;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSaveParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by tianlei on 2022/5/11
 */
@Component
public class CooperationStartDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessChangeProducer contractProcessChangeProducer;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.cooperationTopicName(), CooperationChangeTagEnum.COOPERATION_START.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        CooperationChangeMsgEntity entity =
                JsonUtils.json2pojo(data, CooperationChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return false;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        ContractProcessSaveParam param = processDataBuilder.buildProcessInfoParam(processId);
        if (null == param) return;
        param.setBizScene(ProcessDataCollectBizSceneConstants.COOPERATION_START);
        contractProcessWriteClient.contractProcessSave(param);
    }

    @Override
    public void postProcessAfter(ProcessDataCollectContext collectContext) {
        if (Boolean.TRUE.equals(dataCollectConfigCenter.newSelfStartMsgSwitch())) {
            ProcessChangeMsgEntity processChangeMsgEntity = new ProcessChangeMsgEntity();
            processChangeMsgEntity.setProcessId(collectContext.getProcessId());
            contractProcessChangeProducer.sendMessageDelay(processChangeMsgEntity,
                    ContractProcessChangeTagEnum.COOPERATION_START.getTag());
        }

    }

}

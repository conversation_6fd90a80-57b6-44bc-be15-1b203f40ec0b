package com.timevale.contractmanager.core.service.offlinecontract.bean.input;

import com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 查询线下合同导入记录合同信息列表
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Data
public class UpdateStatusByRecordProcessIdDTO extends ToString {

    @NotBlank(message = "导入流程id不能为空")
    private String recordProcessId;

    /** 状态 */
    @NotNull(message = "状态不能为空")
    private OfflineContractStatusEnum status;

    /** 失败原因 */
    private String failMessage;

    /** 合同流程id */
    private String processId;
}

package com.timevale.contractmanager.core.service.transfer.impl.context;

import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModelProperty;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/30
 */
@Setter
@Getter
public class TransferBizContext extends ToString {
    /** 此次转交任务id */
    private String taskId;

    /** 此次转交任务是否对接任务中心 */
    private boolean useTaskCenter;

    /** 转交相关用户信息 */
    private TransferUserInfo transferUserInfo;

    /** 转交的合同/用印审批列表 */
    private TransferProcessListInfo transferProcessListInfo;

    /** 转交审批流程信息,用于审批mix的场景下 */
    private MixTransferApprovalInfo mixTransferApprovalInfo;
    /** 转交的合同审批信息 */
    private TransferApprovalInfo transferContractApprovalInfo;

    /** 是否为系统转交 */
    private boolean isSystemTransfer = false;

    /** 合同转交原因 */
    private String contractApprovalTransferReason;

    /** 用印转交原因 */
    private String sealApprovalTransferReason;

    /** 是否批量部分转交 */
    public boolean isPartTransfer() {
        return null != transferProcessListInfo
                && CollectionUtils.isNotEmpty(transferProcessListInfo.getTransferProcessList());
    }

    /** 转交流程列表信息 */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TransferProcessListInfo {
        /** 转交人oid */
        private String transferAccountOid;
        /** 转交的合同/用印审批列表 */
        private List<String> transferProcessList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TransferApprovalInfo {
        /** 转交人oid */
        private String transferAccountOid;
        /** 转交的合同审批/用印审批列表 */
        private List<String> transferApprovalIdList;
    }

    @Data
    public static class TransferUserInfo {
        /** 转交主体信息 */
        private UserAccountDetail tenantAccount;
        /** 当前操作人信息 */
        private UserAccountDetail operatorAccount;
        /** 批量转交转交人信息 */
        private List<UserAccountDetail> transferAccount;
        /** 被转交人人信息 */
        private UserAccountDetail transferToAccount;
        /** 部分转交-转交人信息 */
        private UserAccountDetail transferProcessAccount;

        public boolean checkAnyBaseAccountIsNull() {
            return this.tenantAccount == null
                    || this.operatorAccount == null
                    || this.transferToAccount == null;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MixTransferApprovalInfo {
        @ApiModelProperty("转交人oid")
        private String transferAccountOid;

        @ApiModelProperty("转交审批流程列表")
        private List<TransferApproval> transferApprovalList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransferApproval {
        @ApiModelProperty("审批流程id")
        private String approvalId;

        /** {@link com.timevale.account.flow.service.enums.ApprovalFlowTemplateTypeEnum} */
        @ApiModelProperty("审批流程类型")
        private String approvalType;
    }
}

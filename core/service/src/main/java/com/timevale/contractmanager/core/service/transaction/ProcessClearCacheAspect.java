package com.timevale.contractmanager.core.service.transaction;

import com.alibaba.fastjson.JSONObject;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saas.common.manage.common.service.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.assertj.core.util.Sets;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Aspect
@Component
public class ProcessClearCacheAspect {

    @Resource private SystemConfig systemConfig;
    @Resource private ApplicationEventPublisher publishEvent;

    @Pointcut("@annotation(com.timevale.contractmanager.core.service.transaction.ProcessClearCache)")
    private void pointCut() {}

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();

        if (signature instanceof MethodSignature) {
            Method method = ((MethodSignature) signature).getMethod();
            // 如果不包含注解， 直接返回
            if (!method.isAnnotationPresent(ProcessClearCache.class)
                    && !systemConfig.getProcessDetailResultCacheSwitch()) {
                return joinPoint.proceed();
            }
            ProcessClearCache listener =
                    method.getAnnotation(ProcessClearCache.class);
            long start = System.currentTimeMillis();
            // 解析processId列表
            Set<String> processIds = getProcessIds(joinPoint, method, listener.processIds());
            // 推送事件
            if (CollectionUtils.isNotEmpty(processIds)) {
                publishEvent(listener, processIds);
                log.info("process transaction publishEvent cost:{}ms", System.currentTimeMillis() - start);
            }
        }
        return joinPoint.proceed();
    }

    /**
     * 推送事件
     * @param listener
     * @param processIds
     */
    private void publishEvent(ProcessClearCache listener, Set<String> processIds) {
        try {
            publishEvent.publishEvent(
                    new ProcessClearCacheEvent(listener.sceneDesc(), processIds, listener.cacheType()));
        } catch (Exception e) {
            log.error("publish event failed", e);
        }
    }

    /**
     * 解析processId列表
     * @param pjp
     * @param method
     * @param paramPath
     * @return
     */
    private Set<String> getProcessIds(ProceedingJoinPoint pjp, Method method, String paramPath) {
        // 如果未指定参数位置，直接返回null
        if (StringUtils.isBlank(paramPath)) {
            return null;
        }
        // 解析指定的参数地址
        String[] split = paramPath.split("\\.");
        Object param = "";
        for (int i = 0; i < method.getParameters().length; i++) {
            if (method.getParameters()[i].getName().equals(split[0])) {
                param = pjp.getArgs()[i];
                break;
            }
        }
        Set<String> processIds = Sets.newHashSet();
        if (split.length == 1) {
            if (param instanceof Collection) {
                processIds.addAll(
                        ((Collection<?>) param)
                                .stream().map(i -> i.toString()).collect(Collectors.toSet()));
            } else if (param instanceof String) {
                processIds.add(param.toString());
            }
            return processIds;
        }
        if (param instanceof Collection) {
            for (Object p : ((Collection) param)) {
                String processId =
                        parseProcessId(p, ArrayUtils.subarray(split, 1, split.length));
                if (StringUtils.isNotBlank(processId)) {
                    processIds.add(processId);
                }
            }
            return processIds;
        }
        String processId =
                parseProcessId(param, ArrayUtils.subarray(split, 1, split.length));
        if (StringUtils.isNotBlank(processId)) {
            processIds.add(processId);
        }
        return processIds;
    }

    /**
     * 解析processId
     * @param param
     * @param split
     * @return
     */
    private String parseProcessId(Object param, String[] split) {
        String processId = "";
        JSONObject jsonObject = JSONObject.parseObject(JsonUtils.obj2json(param));
        for (int i = 0; i < split.length; ++i) {
            String value = jsonObject.getString(split[i]);
            if (!JSONUtils.isJson(value)) {
                processId = value;
                break;
            }

            if (i == split.length - 1) {
                processId = "";
                break;
            }
            jsonObject = jsonObject.getJSONObject(split[i]);
        }
        return processId;
    }
}

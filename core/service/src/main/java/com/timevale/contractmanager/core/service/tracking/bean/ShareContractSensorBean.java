package com.timevale.contractmanager.core.service.tracking.bean;

import com.timevale.contractmanager.core.model.enums.SensorEnum;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import com.timevale.saas.common.manage.spi.dto.request.share.DoResourceShareActionRequestDTO;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * 分享的链接生成埋点模版
 * <AUTHOR>
 *
 * @date 2022/2/22
 */
@Data
public class ShareContractSensorBean extends SensorBaseBean {
    private DoResourceShareActionRequestDTO request;


    @Override
    public SensorEventEnum sensorKey() {
        return SensorEventEnum.SHARE_CONTRACT_SEVER;
    }

    @Override
    public List<SensorEnum> sensorTemplate() {
        return Arrays.asList(
                SensorEnum.PROCESS_STATUS,
                SensorEnum.SHARE_TYPE,
                SensorEnum.AUTHENTICATION_FAILURE_REASON,
                SensorEnum.AUTHENTICATION_RESULT);
    }

    @Override
    public void initData() {
        setOperatorOid(request.getAccountId());
        setOperatorGid(request.getAccountGid());
        setAuthorizedOid(request.getSubjectId());
        setAuthorizedGid(request.getSubjectGid());
        getTempData().put(SensorEnum.SHARE_TYPE.getKey(), request.getShareOperateType());
    }

    @Override
    public String getProcessId() {
        return request.getResourceId();
    }

    @Override
    public void setRequest(Object request) {
        this.request = (DoResourceShareActionRequestDTO) request;
    }

    public void setRequest(DoResourceShareActionRequestDTO request) {
        this.request = request;
    }
}

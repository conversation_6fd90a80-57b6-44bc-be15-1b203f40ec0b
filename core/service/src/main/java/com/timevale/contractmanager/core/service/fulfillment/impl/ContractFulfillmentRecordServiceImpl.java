package com.timevale.contractmanager.core.service.fulfillment.impl;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.fulfillment.ContractFulfillmentRecordDO;
import com.timevale.contractmanager.common.dal.dao.fulfillment.ContractFulfillmentRecordDAO;
import com.timevale.contractmanager.common.service.bean.CountDetail;
import com.timevale.contractmanager.common.service.enums.ProcessBusinessType;
import com.timevale.contractmanager.common.service.enums.ProcessTabEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentRecordStatusEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentTypeEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchSaveModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchUpdateModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRecordBatchSaveResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRecordSaveResult;
import com.timevale.contractmanager.core.model.dto.response.ProcessHomeListResponse;
import com.timevale.contractmanager.core.model.dto.response.todocenter.SubjectTodoCountDTOHolder;
import com.timevale.contractmanager.core.model.dto.response.todocenter.TypeTodoCountDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.enums.TodoTypeEnum;
import com.timevale.contractmanager.core.service.fulfillment.ContractFulfillmentRecordService;
import com.timevale.contractmanager.core.service.fulfillment.converter.ContractFulfillmentRecordConverter;
import com.timevale.contractmanager.core.service.other.UserCenterCachedService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import com.timevale.signflow.search.service.api.v2.RpcContractFulfillmentSearchService;
import com.timevale.signflow.search.service.enums.FulfillmentStatisticsTypeEnum;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessCustomizeConfigDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.v2.contractfulfillment.ContractFulfillmentQueryModel;
import com.timevale.signflow.search.service.model.v2.contractfulfillment.ContractFulfillmentStatisticsModel;
import com.timevale.signflow.search.service.result.v2.contractfulfillment.ContractFulfillmentQueryResult;
import com.timevale.signflow.search.service.result.v2.contractfulfillment.ContractFulfillmentRecord;
import com.timevale.signflow.search.service.result.v2.contractfulfillment.ContractFulfillmentStatisticsCount;
import com.timevale.signflow.search.service.result.v2.contractfulfillment.ContractFulfillmentStatisticsResult;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ContractFulfillmentRecordServiceImpl
 *
 * <AUTHOR>
 * @since 2023/10/11 5:09 下午
 */
@Service
public class ContractFulfillmentRecordServiceImpl implements ContractFulfillmentRecordService {

    @Autowired
    private ContractFulfillmentRecordDAO contractFulfillmentRecordDAO;

    @Autowired
    private RpcContractFulfillmentSearchService fulfillmentSearchService;

    @Autowired
    private UserCenterCachedService userCenterCachedService;

    @Autowired
    private ContractProcessReadClient contractProcessReadClient;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private MapperFactory mapperFactory;

    @Override
    public ContractFulfillmentRecordBatchSaveResult batchSave(ContractFulfillmentRecordBatchSaveModel model) {

        List<String> processIds = model.getList().stream().map(ContractFulfillmentRecordModel::getProcessId).collect(Collectors.toList());;
        List<ContractFulfillmentRecordDO> recordDOList = contractFulfillmentRecordDAO.list(processIds, FulfillmentRecordStatusEnum.UNMARKED.getStatus());

        Iterator<ContractFulfillmentRecordModel> iterator = model.getList().iterator();
        while(iterator.hasNext()){
            ContractFulfillmentRecordModel recordModel = iterator.next();
            for(ContractFulfillmentRecordDO contractFulfillmentRecordDO : recordDOList){
                if(recordModel.getProcessId().equals(contractFulfillmentRecordDO.getProcessId()) &&
                        recordModel.getRuleId().equals(contractFulfillmentRecordDO.getRuleId()) &&
                        (recordModel.getNoticeOid().equals(contractFulfillmentRecordDO.getNoticeOid()) ||
                                (StringUtils.isNotEmpty(recordModel.getNoticeGid()) && StringUtils.isNotEmpty(contractFulfillmentRecordDO.getNoticeGid()) &&
                                recordModel.getNoticeGid().equals(contractFulfillmentRecordDO.getNoticeGid())))){
                    iterator.remove();
                }
            }
        }

        if(CollectionUtils.isEmpty(model.getList())){
            return new ContractFulfillmentRecordBatchSaveResult();
        }

        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(model.getTenantId());
        List<ContractFulfillmentRecordDO> list = ContractFulfillmentRecordConverter.convertSaveRecordDOList(model.getList(), userAccountTenant);
        contractFulfillmentRecordDAO.batchInsert(list);

        ContractFulfillmentRecordBatchSaveResult result = new ContractFulfillmentRecordBatchSaveResult();
        List<ContractFulfillmentRecordSaveResult> recordSaveResults = mapperFactory.getMapperFacade().mapAsList(list, ContractFulfillmentRecordSaveResult.class);
        result.setList(recordSaveResults);
        return result;
    }

    @Override
    public void batchDelete(List<String> recordIds) {
        contractFulfillmentRecordDAO.batchDelete(recordIds);
    }

    @Override
    public void batchUpdate(ContractFulfillmentRecordBatchUpdateModel model) {
        List<ContractFulfillmentRecordDO> list = ContractFulfillmentRecordConverter.convertUpdateRecordDOList(model.getList());
        contractFulfillmentRecordDAO.batchUpdate(list);
    }

    @Override
    public void updateStatus(String tenantId, String accountId, String recordId, String status) {

        FulfillmentRecordStatusEnum fulfillmentRecordStatusEnum = FulfillmentRecordStatusEnum.getStatusEnum(status);
        AssertX.isTrue(fulfillmentRecordStatusEnum != null, "履约记录状态不存在");

        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
        contractFulfillmentRecordDAO.updateStatus(userAccountTenant.getAccountGid(), recordId, status);
    }

    @Override
    public void batchUpdateStatus(String tenantId, String accountId, List<String> recordIdList, String status) {
        FulfillmentRecordStatusEnum fulfillmentRecordStatusEnum = FulfillmentRecordStatusEnum.getStatusEnum(status);
        AssertX.isTrue(fulfillmentRecordStatusEnum != null, "履约记录状态不存在");

        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
        contractFulfillmentRecordDAO.batchUpdateStatus(userAccountTenant.getAccountGid(), recordIdList, status);
    }

    @Override
    public TypeTodoCountDTO queryFulfillmentCount(String userOid, String userGid) {
        return querySubjectFulfillmentCount(userOid,userGid,null, false);
    }

    @Override
    public TypeTodoCountDTO querySubjectFulfillmentCount(String userOid, String userGid, String subjectGid, boolean queryCurrentSubject) {
        ContractFulfillmentQueryModel model = new ContractFulfillmentQueryModel();
        if (queryCurrentSubject && subjectGid != null) {
            model.setGidList(Collections.singletonList(subjectGid));
        }
        model.setPageSize(1);
        model.setPageNum(1);
        model.setStatus(FulfillmentRecordStatusEnum.UNMARKED.getStatus());
        model.setNoticeOid(userOid);
        model.setNoticeGid(userGid);
        ContractFulfillmentQueryResult result = fulfillmentSearchService.pageQueryFulfillment(model);
        return new TypeTodoCountDTO(
                TodoTypeEnum.FULFILLMENT.getLabel(),
                TodoTypeEnum.FULFILLMENT.getType(),
                result.getTotal());
    }
    @Override
    public List<SubjectTodoCountDTOHolder> aggregateFulfillment(
            String userOid, String userGid) {

        ContractFulfillmentStatisticsModel queryModel = new ContractFulfillmentStatisticsModel();
        queryModel.setNoticeOid(userOid);
        queryModel.setNoticeGid(userGid);
        queryModel.setStatus(FulfillmentRecordStatusEnum.UNMARKED.getStatus());
        queryModel.setStatisticsType(FulfillmentStatisticsTypeEnum.STATISTICS_SUBJECT.getType());
        ContractFulfillmentStatisticsResult contractFulfillmentStatisticsResult = fulfillmentSearchService.statisticsRecord(queryModel);
        if(CollectionUtils.isEmpty(contractFulfillmentStatisticsResult.getList())){
            return new ArrayList<>();
        }
        List<SubjectTodoCountDTOHolder> subjectTodoCountDTOHolders = new ArrayList<>();
        for(ContractFulfillmentStatisticsCount statisticsSubjectCount : contractFulfillmentStatisticsResult.getList()){
            if(StringUtils.isEmpty(statisticsSubjectCount.getKey()) || statisticsSubjectCount.getCount() == 0){
                continue;
            }
            SubjectTodoCountDTOHolder subjectTodoCountDTOHolder = new SubjectTodoCountDTOHolder();
            subjectTodoCountDTOHolder.setSubjectGid(statisticsSubjectCount.getKey());
            subjectTodoCountDTOHolder.setCount(statisticsSubjectCount.getCount());
            subjectTodoCountDTOHolders.add(subjectTodoCountDTOHolder);
        }
        if(CollectionUtils.isEmpty(subjectTodoCountDTOHolders)){
            return new ArrayList<>();
        }

        Set<String> gidSet =
                subjectTodoCountDTOHolders.stream()
                        .map(SubjectTodoCountDTOHolder::getSubjectGid)
                        .collect(Collectors.toSet());
        Map<String, String> gidOidMap = userCenterCachedService.mGetRealNameOid(gidSet);
        for(SubjectTodoCountDTOHolder subjectTodoCountDTOHolder : subjectTodoCountDTOHolders){
            String oid = gidOidMap.get(subjectTodoCountDTOHolder.getSubjectGid());
            subjectTodoCountDTOHolder.setSubjectOid(oid);
        }
        return subjectTodoCountDTOHolders;
    }


    @Override
    public ProcessHomeListResponse queryFulfillmentRecordList(UserAccount person, UserAccount subject, String type) {
        ContractFulfillmentQueryModel model = new ContractFulfillmentQueryModel();
        model.setGidList(Lists.newArrayList(subject.getAccountGid()));
        model.setNoticeOid(person.getAccountOid());
        model.setNoticeGid(person.getAccountGid());
        model.setStatus(FulfillmentRecordStatusEnum.UNMARKED.getStatus());
        if (Objects.equals(type, FulfillmentTypeEnum.ALL.getType())) {
            model.setTypes(FulfillmentTypeEnum.getAllSystemTypeDesc());
            if (type != null) {
                model.getTypes().add(type);
            }
        } else {
            model.setType(type);
        }
        model.setPageNum(1);
        model.setPageSize(3);
        ContractFulfillmentQueryResult contractFulfillmentQueryResult = fulfillmentSearchService.pageQueryFulfillment(model);
        if(CollectionUtils.isEmpty(contractFulfillmentQueryResult.getList())){
            return new ProcessHomeListResponse();
        }
        List<String> processIds = contractFulfillmentQueryResult.getList().stream().map(ContractFulfillmentRecord::getProcessId).distinct().collect(Collectors.toList());
        List<ContractProcessDTO> contractProcessDTOList = contractProcessReadClient.listByProcessIds(processIds);
        Map<String, ContractProcessDTO> processMap = contractProcessDTOList.stream().collect(Collectors.toMap(ContractProcessDTO::getProcessId, Function.identity(), (o, n) -> n));
        ProcessHomeListResponse response = new ProcessHomeListResponse();
        response.setTotal(contractFulfillmentQueryResult.getTotal());
        response.setData(
                contractFulfillmentQueryResult.getList().stream().map(t -> convertProcessHomeInfo(t, processMap)).collect(Collectors.toList()));
        return response;
    }


    public ProcessHomeListResponse.ProcessHomeInfo convertProcessHomeInfo(ContractFulfillmentRecord record,  Map<String, ContractProcessDTO> map){
        if(record == null){
            return new ProcessHomeListResponse.ProcessHomeInfo();
        }
        ContractProcessDTO contractProcessDTO = map.get(record.getProcessId());
        if(contractProcessDTO == null){
            return new ProcessHomeListResponse.ProcessHomeInfo();
        }
        ProcessHomeListResponse.ProcessHomeInfo processHomeInfo = new ProcessHomeListResponse.ProcessHomeInfo();
        processHomeInfo.setProcessId(record.getProcessId());
        processHomeInfo.setTitle(contractProcessDTO.getTitle());
        processHomeInfo.setProcessAccount(contractProcessDTO.getInitiator());
        processHomeInfo.setRecordId(record.getRecordId());
        processHomeInfo.setTypeName(record.getTypeName());
        processHomeInfo.setRenewable(fillRenewable(record, contractProcessDTO));
        return processHomeInfo;
    }

    private boolean fillRenewable(ContractFulfillmentRecord record, ContractProcessDTO contractProcessDTO){

        if(!FulfillmentTypeEnum.EXPIRE.getType().equals(record.getType())){
            return false;
        }
        if(!ProcessStatusEnum.DONE.getStatus().equals(contractProcessDTO.getProcessStatus())){
            return false;
        }
        if(contractProcessDTO.getContractExpireTime() == null){
            return false;
        }
        if (ProcessBusinessType.RESCIND.getBusinessType().equals(contractProcessDTO.getProcessBizType())) {
            return false;
        }
        List<ContractProcessCustomizeConfigDTO> contractProcessCustomizeConfigDTOS = contractProcessDTO.getCustomizeConfig();
        for(ContractProcessCustomizeConfigDTO customizeConfigDTO : contractProcessCustomizeConfigDTOS){
            if(!customizeConfigDTO.getTenantOid().equals(record.getTenantOid()) &&
                    !customizeConfigDTO.getTenantGid().equals(record.getTenantGid())){
                continue;
            }
            if(customizeConfigDTO.getRenewable() != null && !customizeConfigDTO.getRenewable()){
                return false;
            }
        }
        return true;
    }

    @Override
    public List<CountDetail> queryFulfillmentCount(UserAccount person, UserAccount subject) {
        List<CountDetail> fulfillmentList = new ArrayList<>();
        if(subject == null || StringUtils.isEmpty(subject.getAccountGid())){
            return new ArrayList<>();
        }

        ContractFulfillmentStatisticsModel model = new ContractFulfillmentStatisticsModel();
        model.setGidList(Lists.newArrayList(subject.getAccountGid()));
        model.setNoticeOid(person.getAccountOid());
        model.setNoticeGid(person.getAccountGid());
        model.setStatus(FulfillmentRecordStatusEnum.UNMARKED.getStatus());
        model.setStatisticsType(FulfillmentStatisticsTypeEnum.STATISTICS_TYPE.getType());
        ContractFulfillmentStatisticsResult statisticsTypeResult = fulfillmentSearchService.statisticsRecord(model);
        Map<String, Long> countMap = new HashMap<>(4);
        if(CollectionUtils.isNotEmpty(statisticsTypeResult.getList())){
            countMap = statisticsTypeResult.getList().stream().collect(Collectors.toMap(ContractFulfillmentStatisticsCount::getKey, ContractFulfillmentStatisticsCount::getCount));
        }
        for(ProcessTabEnum processTabEnum : ProcessTabEnum.getFulfillmentList()){
            CountDetail countDetail = new CountDetail();
            fulfillmentList.add(countDetail);
            countDetail.setType(processTabEnum.getStatus());
            switch (processTabEnum){
                case FULFILLMENT_EXPIRE:
                    countDetail.setCount(getCountDefault0(countMap, FulfillmentTypeEnum.EXPIRE.getType()));
                    break;
                case FULFILLMENT_COLLECTION:
                    countDetail.setCount(getCountDefault0(countMap, FulfillmentTypeEnum.COLLECTION.getType()));
                    break;
                case FULFILLMENT_PAYMENT:
                    countDetail.setCount(getCountDefault0(countMap, FulfillmentTypeEnum.PAYMENT.getType()));
                    break;
                case FULFILLMENT_CUSTOM:
                    countDetail.setCount(getCountDefault0(countMap, FulfillmentTypeEnum.CUSTOM.getType()));
                    break;
                default:
                    break;
            }
        }
        return fulfillmentList;
    }

    private Long getCountDefault0(Map<String, Long> countMap, String type){
        if(countMap.size() == 0 || countMap.get(type) == null){
            return 0L;
        }
        return countMap.get(type);
    }
}

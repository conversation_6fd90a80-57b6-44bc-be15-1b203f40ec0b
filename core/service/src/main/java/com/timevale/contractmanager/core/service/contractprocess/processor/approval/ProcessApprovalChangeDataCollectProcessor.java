package com.timevale.contractmanager.core.service.contractprocess.processor.approval;

import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.mq.model.ProcessApprovalChangeMsgEntity;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ProcessApprovalDataCollectProcessor
 *
 * <AUTHOR>
 * @since 2022/8/9 2:36 下午
 */
@Component
public class ProcessApprovalChangeDataCollectProcessor extends BaseProcessApprovalChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;

    @Autowired
    private ProcessDataBuilder processDataBuilder;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.getContractApprovalChangeTopicName(), null);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return false;
    }

    @Override
    public ContractProcessUpdateParam buildUpdateParam(ProcessApprovalChangeMsgEntity entity) {
        return processDataBuilder.buildNewProcessApproval(entity.getApprovalId());
    }
}

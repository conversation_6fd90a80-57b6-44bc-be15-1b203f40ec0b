package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.timevale.contractmanager.common.dal.bean.grouping.GroupingInfoDO;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.model.dto.request.process.ProcessCheckParticipantRequest;
import com.timevale.contractmanager.core.service.component.grouping.GroupingFileComponent;
import com.timevale.contractmanager.core.service.contractprocess.*;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.grouping.GroupingFileService;
import com.timevale.contractmanager.core.service.mq.consumer.searchsync.ProcessSearchParamBuilder;
import com.timevale.contractmanager.core.service.mq.model.ProcessChangeMsgEntity;
import com.timevale.contractmanager.core.service.util.ProcessUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.param.UpdateProcessInfoParam;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateCCParam;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/5/10
 */
@Slf4j
@Component
public class ProcessCCChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ProcessSearchParamBuilder processSearchParamBuilder;
    @Autowired
    private ContractProcessReadClient processQueryClient;
    @Autowired
    private GroupingFileService groupingFileService;
    @Autowired
    private GroupingFileComponent groupingFileComponent;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processTopicName(), ProcessChangeTagEnum.PROCESS_CC_CHANGE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessChangeMsgEntity entity = JsonUtils.json2pojo(data, ProcessChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();

        //
        UpdateProcessInfoParam updateProcessInfoParam =
                processSearchParamBuilder.buildUpdateProcessCcParam(processId);
        if (null == updateProcessInfoParam) {
            log.info(LOG_PREFIX + "update cc para is null");
            return;
        }

        // 这里查询的就是所有抄送人
        List<ProcessAccount> ccList = updateProcessInfoParam.getProcessAccountInfoList();

        // 数据更新
        ContractProcessUpdateCCParam param = new ContractProcessUpdateCCParam();
        param.setProcessId(processId);
        // 这个值 为 null 或者空集合，底层存储会把这个字段删掉
        param.setCc(ProcessDataCollectConverter.account2ParamList(ccList));
        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);
        contractProcessWriteClient.updateCC(param);
        // 删除归档信息
        contractProcessDTO.setCc(ccList);
        // 基于contractProcessDTO 判断主体中
        List<GroupingInfoDO> groupingInfoDOList = groupingFileService.queryAllByProcessId(processId);
        List<Long> needRemoveGroupingInfoIds = Lists.newArrayList();
        List<GroupingInfoDO> needRemoveGroupingInfos = groupingInfoDOList.stream().filter(groupingInfoDO -> {
            ProcessCheckParticipantRequest request = new ProcessCheckParticipantRequest();
            request.setContractProcessDTO(contractProcessDTO);
            request.setPersonGid(groupingInfoDO.getOperatorGid());
            request.setPersonOid(groupingInfoDO.getOperatorOid());
            request.setTenantGid(groupingInfoDO.getSubjectGid());
            request.setTenantOid(groupingInfoDO.getSubjectOid());
            return !ProcessUtils.isParticipantSubject(request);
        }).peek(i -> needRemoveGroupingInfoIds.add(i.getId())).collect(Collectors.toList());
        log.info("needRemoveGroupingInfos：{}", needRemoveGroupingInfos);
        //删除归档信息
        if(CollectionUtils.isNotEmpty(needRemoveGroupingInfoIds)){
            groupingFileService.deleteGroupingInfoByIds(new ArrayList<>(needRemoveGroupingInfoIds));
        }
        // 发送归档移除消息
        if(CollectionUtils.isNotEmpty(needRemoveGroupingInfos)){
            needRemoveGroupingInfos.forEach(i -> {
                groupingFileComponent.reMoveArchive(i.getMenuId(), Collections.singletonList(i.getProcessId()), i.getSubjectOid());
            });
        }
    }

}

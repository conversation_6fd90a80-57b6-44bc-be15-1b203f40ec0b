package com.timevale.contractmanager.core.service.watermark.impl;

import com.timevale.contractmanager.common.service.enums.WatermarkBizTypeEnum;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.service.other.DocManagerService;
import com.timevale.contractmanager.core.service.watermark.WatermarkStrategy;
import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkModel;
import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkResult;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootModel;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootResult;
import com.timevale.docmanager.service.input.watermark.AddWatermarkInput;
import com.timevale.saas.common.manage.common.service.model.bean.watermark.WatermarkTemplate;
import com.timevale.saas.common.manage.common.service.model.input.watermark.GenerateWatermarkSnapShootInput;
import com.timevale.saas.common.manage.common.service.model.input.watermark.WatermarkTemplateDetailInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.AbstractMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.IntFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> 初霁
 * @version V1.0 @Project: contract-manager-project @Description: 文件水印--通用水印策略处理器
 * @date Date : 2023年04月23日 14:47
 */
@Slf4j
@Service
public abstract class DocCommonWatermarkStrategy implements WatermarkStrategy {

    @Qualifier("watermarkAsyncResultExecutor")
    @Autowired
    AsyncTaskExecutor watermarkAsyncResultExecutor;

    @Autowired SaasCommonClient saasCommonClient;

    @Autowired DocManagerService docManagerService;

    @Override
    public GenerateWatermarkSnapShootResult generateWatermarkSnapShoot(GenerateWatermarkSnapShootModel model) {

        GenerateWatermarkSnapShootInput snapShootInput = new GenerateWatermarkSnapShootInput();
        snapShootInput.setWatermarkTemplateId(model.getWatermarkId());
        snapShootInput.setBizType(WatermarkBizTypeEnum.CONTRACT_PROCESS.getBizType());
        snapShootInput.setBizId(model.getProcessId());
        snapShootInput.setOid(model.getTenantId());
        snapShootInput.setOperatorOid(model.getOperatorId());
        String watermarkSnapShootId = saasCommonClient.generateWatermarkSnapShoot(snapShootInput);
        Map<String, String> fileIdAndWatermarkSSIdMap = model.getFiles().stream().collect(Collectors.toMap(FileBO::getFileId, fileBO -> watermarkSnapShootId));

        GenerateWatermarkSnapShootResult result = new GenerateWatermarkSnapShootResult();
        result.setFileIdAndWatermarkSSIdMap(fileIdAndWatermarkSSIdMap);
        return result;
    }

    @Override
    public AddWatermarkResult addWatermark(AddWatermarkModel model) {
        AddWatermarkResult result = new AddWatermarkResult();
        result.setScreenWatermark(false);

        //普通文本/图片的文件水印,只需要查一次详情即可
        WatermarkTemplateDetailInput input = new WatermarkTemplateDetailInput();
        input.setWatermarkId(model.getFiles().get(0).getWatermarkId());
        input.setOid(model.getTenantId());
        input.setOperatorOid(model.getOperatorId());
        WatermarkTemplate watermarkTemplateDetail =
                saasCommonClient.getWatermarkTemplateDetail(input);

        // 批量异步添加水印
        CompletableFuture<Map.Entry<String, String>>[] completableFutures =
                model.getFiles().stream()
                        .map(
                                fileBO ->
                                        CompletableFuture.supplyAsync(
                                                new SupplierWrapper<Map.Entry<String, String>>(
                                                        () -> {
                                                            AddWatermarkInput addWatermarkInput = convertWatermarkTemplate2Input(fileBO.getFileId(), watermarkTemplateDetail);
                                                            addWatermarkInput.setOperatorId(model.getOperatorId());
                                                            String watermarkFileId = docManagerService.addWaterMarkToFile(addWatermarkInput);
                                                            return new AbstractMap.SimpleEntry<>(fileBO.getFileId(), watermarkFileId);
                                                        }),
                                                watermarkAsyncResultExecutor))
                        .toArray((IntFunction<CompletableFuture<Map.Entry<String, String>>[]>)CompletableFuture[]::new);
        // 等待全部添加完成
        CompletableFuture.allOf(completableFutures).join();
        Map<String, String> originalAndWatermarkFileIdMap = Stream.of(completableFutures)
                .map(CompletableFuture::join)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        result.setOriginalAndWatermarkFileIdMap(originalAndWatermarkFileIdMap);
        //结束
        return result;

    }

}

package com.timevale.contractmanager.core.service.processstart.bean;

import com.timevale.doccooperation.service.model.startflow.StartSignFlowData;
import com.timevale.mandarin.common.result.ToString;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流程调度签署流程请求参数
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDispatchSignFlowParam extends ToString {

    /** 发起签署参数 */
    private StartSignFlowData input;

    /** 操作人oid */
    private String operatorId;
}

package com.timevale.contractmanager.core.service.component;

import com.timevale.contractmanager.common.service.integration.client.ShortUrlClient;
import com.timevale.esign.compontent.simple.encrypt.SimpleCipher;

import com.timevale.mandarin.base.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/1/17
 */
@Slf4j
@Component
public class PageContextHelper {

    @Autowired private ShortUrlClient shortUrlClient;

    /**
     * 重定向地址encode处理
     *
     * @param redirectUrl
     * @return
     */
    public String encodeRedirectUrl(String redirectUrl) {
        // 如果重定向地址为空， 返回空字符串
        if (StringUtils.isBlank(redirectUrl)) {
            return "";
        }
        // encode处理
        try {
            return URLEncoder.encode(redirectUrl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.warn("previewRedirectUrl encode failed");
            return "";
        }
    }

    /**
     * 生成页面所需要的context，短的context
     *
     * @param params 参数
     * @param isLong 标识是生成短的context还是长的context
     * @return 短context
     */
    public String genContext(String params, boolean isLong) {

        String encodedString = SimpleCipher.INSTANCE.encode("AES", params, "UTF-8");

        try {
            encodedString = URLEncoder.encode(encodedString, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.info("genShortUrl UnsupportedEncodingException");
        }

        if (isLong) {
            return encodedString;
        }
        String shortContext = shortUrlClient.convertShortCode(encodedString);
        log.info("context = {}, short = {}", encodedString, shortContext);

        return shortContext;
    }
}

package com.timevale.contractmanager.core.service.tracking.bean;

import com.timevale.contractmanager.core.model.dto.request.BatchRevokeRequest;
import com.timevale.contractmanager.core.model.enums.SensorEnum;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * 撤回埋点模版
 * <AUTHOR>
 *
 * @date 2022/2/22
 */
@Data
public class WithdrawContractSensorBean extends SensorBaseBean {
    private BatchRevokeRequest request;

    @Override
    public SensorEventEnum sensorKey() {
        return SensorEventEnum.WITHDRAW_CONTRACT_SEVER;
    }

    @Override
    public List<SensorEnum> sensorTemplate() {
        return Arrays.asList(
                SensorEnum.NUMBER_OF_PROCESS,
                SensorEnum.WITHDRAW_REASON,
                SensorEnum.RETURN_TIME,
                SensorEnum.PROCESSING_RESULT);
    }

    @Override
    public void initData() {
        setOperatorGid(request.getAccountId());
        setAuthorizedOid(request.getSubjectId());

        getTempData().put(SensorEnum.NUMBER_OF_PROCESS.getKey(), request.getProcessIds().size());
        getTempData().put(SensorEnum.WITHDRAW_REASON.getKey(), request.getRevokeReason());
    }

    @Override
    public void setRequest(Object request) {
        this.request = (BatchRevokeRequest) request;
    }

    public void setRequest(BatchRevokeRequest request) {
        this.request = request;
    }
}

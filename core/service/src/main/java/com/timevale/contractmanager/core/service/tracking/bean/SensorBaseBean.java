package com.timevale.contractmanager.core.service.tracking.bean;

import com.timevale.contractmanager.core.model.enums.SensorEnum;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * @date 2022/2/18
 *     <p>埋点基础信息
 */
@Data
@Slf4j
public abstract class SensorBaseBean extends ToString {

    /** 操作人oid */
    private String operatorOid = RequestContextExtUtils.getOperatorId();
    /** 操作人gid */
    private String operatorGid;
    /** 操作主体oid */
    private String authorizedOid = RequestContextExtUtils.getTenantId();
    /** 操作主体gid */
    private String authorizedGid;
    /** 应用id */
    private String appId = RequestContextExtUtils.getAppId();
    /** 产品端 */
    private String platformType = RequestContextExtUtils.getClientId();

    /** 临时埋点数据 */
    private Map<String, Object> tempData = new HashMap<>();

    public void addTempData(String sensorKey, Object sensorValue) {
        tempData.put(sensorKey, sensorValue);
    }

    /**
     * 设置埋点事件key
     *
     * @return
     */
    public abstract SensorEventEnum sensorKey();

    /**
     * 设置埋点事件模版字段
     *
     * @return
     */
    public abstract List<SensorEnum> sensorTemplate();

    /**
     * 埋点数据加载逻辑
     */
    public abstract void initData();

    /**
     * 设置入参
     */
    public abstract void setRequest(Object request);

    /**
     * 获取需要查询流程信息的processId
     * @return
     */
    public String getProcessId(){
        return null;
    };

    public Map<String, Object> getSensorData() {
        Map<String, Object> sensorData = new HashMap<>();
        // 设置必填数据
        sensorData.put(SensorEnum.OPERATOR_OID.getKey(), SensorConstants.sensorString(operatorOid));
        sensorData.put(SensorEnum.OPERATOR_GID.getKey(), SensorConstants.sensorString(operatorGid));
        sensorData.put(SensorEnum.AUTHORIZED_OID.getKey(), SensorConstants.sensorString(authorizedOid));
        sensorData.put(SensorEnum.AUTHORIZED_GID.getKey(), SensorConstants.sensorString(authorizedGid));
        sensorData.put(SensorEnum.APP_ID.getKey(), SensorConstants.sensorString(appId));
        sensorData.put(SensorEnum.PLATFORM_TYPE.getKey(), SensorConstants.sensorString(platformType));

        // 设置事件埋点模版字段
        List<SensorEnum> sensorTemplate = sensorTemplate();
        if (sensorTemplate != null) {
            for (SensorEnum filed : sensorTemplate) {
                sensorData.put(filed.getKey(), SensorConstants.sensorString(tempData.get(filed.getKey())));
            }
        }
        return sensorData;
    }

    public static Class getInstance(SensorEventEnum eventEnum){
        switch (eventEnum){
            case SET_CONTRACT_TEMPLATE_SEVER:
                return SetContractTemplateSensorBean.class;
            case  EDIT_CC_SEVER:
                return EditCcSensorBean.class;
            case EDIT_SECRECY_SEVER:
                return EditSecrecySensorBean.class;
            case SHARE_CONTRACT_SEVER:
                return ShareContractSensorBean.class;
            case URGE_CONTRACT_SEVER:
                return UrgeContractSensorBean.class;
            case VIEW_CONTRACT_SEVER:
                return ViewContractSensorBean.class;
            case WITHDRAW_CONTRACT_SEVER:
                return WithdrawContractSensorBean.class;
            default:
                return SensorBaseBean.class;
        }
    }
}

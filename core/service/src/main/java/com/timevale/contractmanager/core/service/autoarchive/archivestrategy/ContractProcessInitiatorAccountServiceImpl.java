package com.timevale.contractmanager.core.service.autoarchive.archivestrategy;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.service.enums.autoarchive.OperationConditionStrategyEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.dto.autoarchive.ArchiveDeptPersonDTO;
import com.timevale.contractmanager.core.service.autoarchive.OperationConditionService;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-05-10 18:06
 */
@Service
@Slf4j
public class ContractProcessInitiatorAccountServiceImpl extends AbstractOperationService implements OperationConditionService {

	@Override
	public Boolean checkCondition(String fieldId,String tenantOid, String tenantGid,String ruleId, String conditionParams) {
		List<ArchiveDeptPersonDTO> deptPersonDTOS = JSON.parseArray(conditionParams, ArchiveDeptPersonDTO.class);
		if(deptPersonDTOS ==null || CollectionUtils.isEmpty(deptPersonDTOS)){
			return false;
		}
		return true;
	}

	@Override
	public String getFieldId() {
    	return OperationConditionStrategyEnum.CONTRACT_PROCESS_ACCOUNT_LIST.getFieldId();
	}

	@Override
	public BizContractManagerException getErrorException() {
    return new BizContractManagerException(
			OperationConditionStrategyEnum.CONTRACT_PROCESS_ACCOUNT_LIST.getFieldId(),
        BizContractManagerResultCodeEnum.INITIATOR_NOT_EXIST.getMessage());
	}


}

package com.timevale.contractmanager.core.service.tracking.bean;

/**
 * <AUTHOR>
 * @since 2021-03-31
 */
public class SensorConstants {
    /**
     * 产品端
     *
     * <p>微信小程序：WE_CHAT
     *
     * <p>支付宝小程序：ALI_PAY_MINI，
     *
     * <p>安卓app：APP_ANDROID
     *
     * <p>ios app：APP_IOS，
     *
     * <p>H5：H5
     *
     * <p>WEB：WEB
     *
     * <p>钉钉微应用：DING_TALK
     *
     * <p>开放服务：OPEN_SERVICE
     */
    public static final String PLATFORM_TYPE = "PlatformType";

    public static final String ENTITY_TYPE = "entityType";
    public static final String ENTITY_NAME = "entityName";
    public static final String USER_ROLE = "user_role";
    public static final String IS_FIRST_TIME = "is_first_time";
    public static final String OPERATOR_ENTITY_NUM = "operatorEntityNum";
    public static final String MODULE = "Module";

    /** 操作人oid */
    public static final String OID = "oid";

    /** 操作人oid */
    public static final String OPERATOR_OID = "operatorOid";

    /** 操作人gid */
    public static final String OPERATOR_GID = "operatorGid";

    /** 操作主体oid */
    public static final String AUTHORIZED_OID = "authorizedOid";

    /** 操作人主体gid */
    public static final String AUTHORIZED_GID = "authorizedGid";

    /** 合同持续时长 */
    public static final String DURATION = "duration";

    /** 会员版本 */
    public static final String VIP_VERSION ="vipVersion";

    /** 流程id */
    public static final String PROCESS_ID = "processId";

    /** 流程类型， 普通流程、复制重发流程、解约流程 */
    public static final String PROCESS_TYPE = "processType";

    /** 应用id */
    public static final String APP_ID = "appId";

    /** 流程发起方式,直接发起、使用模板发起 */
    public static final String PROCESS_START_TYPE = "processStartType";


    /** 是否外部模板，即主企业授权的共享模板 */
    public static final String TEMPLATE_OUTSIDE = "is_template_outside";

    /** 是否外部模板，即主企业授权的共享模板 */
    public static final String IS_UN_STANDARD_TEMPLATE = "is_un_standard_template";

    /** 是否外部模板，即主企业授权的共享模板 */
    public static final String IS_REPLACE_FILES = "is_replace_files";

    /** 是否外部模板，即主企业授权的共享模板 */
    public static final String IS_ADD_FILES = "is_add_files";

    /** 流程发起模式, 单任务普通发起、单任务扫码发起、批量普通发起、批量扫码签发起 */
    public static final String PROCESS_START_WAY = "processStartWay";

    /** 签署文件数 */
    public static final String FILE_AMOUNT = "fileamount";

    /** 附属材料数 */
    public static final String ATTACHMENT_AMOUNT = "attachmentAmount";

    /** 填写方数量 */
    public static final String FILL_AMOUNT = "fillAmount";

    /** 个人主体签署方数量 */
    public static final String PERSON_SIGNATORY_AMOUNT = "personSignatoryAmount";

    /** 企业主体签署方数量 */
    public static final String ORG_SIGNATORY_AMOUNT = "orgSignatoryAmount";

    /** 当前空间主体是否是签署方 */
    public static final String AUTHORIZED_OID_SIGN = "authorizedOidSign";
    /** 抄送方数量 */
    public static final String CC_AMOUNT = "ccAmount";

    /** 是否设置签署截止日期 */
    public static final String SIGN_DATE_LIMIT = "signdatelimit";

    /** 是否设置合同到期日期 */
    public static final String CONTRACT_DATE_LIMIT = "contractdatelimit";

    /** 是否指定了顺序 */
    public static final String ORDER_SIGN = "ordersign";
    /** 指定模板章数 */
    public static final String TEMPLATE_SEAL_AMOUNT = "templateSealAmount";

    /** 指定AI手绘章数 */
    public static final String HAND_DRAW_AMOUNT = "handdrawAmount";

    /** 指定普通手绘章数 */
    public static final String AI_HAND_DRAW_AMOUNT = "aiHanddrawAmount";

    /** 合同审批流程id */
    public static final String CONTRACT_APPROVAL_ID = "contractApprovalId";

    /** 填写流程id */
    public static final String FILLING_ID = "fillingId";

    /** 签署流程id */
    public static final String FLOW_ID = "flowId";

    /** 合同流程状态 */
    public static final String PROCESS_ID_STATUS = "processIdStatus";

    public static final String ORG_NAME = "orgName";

    public static final String TENANT_ID = "tenantId";

    public static final String FLOW_TYPE = "flowType";

    /** 转交类型， 自动转交 or 手动转交 */
    public static final String TRANSFER_TYPE = "transferType";

    /** 转交模式， 待办任务转交 or 全流程转交 */
    public static final String TRANSFER_WAY = "transferWay";

    public static final String SCAN_INITIATE_SUBJECT = "scan_initiate_subject";

    public static final String SCAN_CODE_NUMBER_MAXIMUM = "scan_code_number_maximum";

    public static final String MENU_ID = "menu_id";

    public static final String RECEIVER_ID = "receiver_id";

    public static final String PROBLEM_TYPE = "problem_type";

    public static final String LIST_NAME = "list_name";

    public static final String CHOSEN_PROCESS_STATUS = "choosen_process_status";

    public static final String IS_INITIATE_TIME = "is_initiate_time";

    public static final String IS_FINISH_TIME = "is_finish_time";

    public static final String PROCESS_LIST_TYPE = "process_type";

    public static final String NUMBER_OF_PROCESS = "number_of_process";

    public static final String RETURN_TIME = "return_time";

    public static final String AUTHENTICATION_RESULT = "authentication_result";

    public static final String AUTHENTICATION_FAILURE_REASON = "authentication_failure_reason";

    public static final String PROCESSING_RESULT = "processing_result";

    public static final String FIELD = "field";

    public static final String NUMBER_OF_FIELD = "number_of_field";

    public static final String SET_TYPE = "set_type";

    public static final String FOLDER_NAME = "folder_name";

    public static final String FATHER_FOLDER_NAME = "father_folder_name";

    public static final String FOLDER_CLASS = "folder_class";

    public static final String NUMBER_OF_VIEW_ABLE_DEPARTMENT = "number_of_view_able_department";

    public static final String NUMBER_OF_VIEW_ABLE_STAFF = "number_of_view_able_staff";

    public static final String NUMBER_OF_DOWNLOAD_ABLE_DEPARTMENT = "number_of_download_able_department";

    public static final String NUMBER_OF_DOWNLOAD_ABLE_STAFF = "number_of_download_able_staff";

    public static final String NUMBER_OF_EDIT_ABLE_DEPARTMENT = "number_of_edit_able_department";

    public static final String NUMBER_OF_EDIT_ABLE_STAFF = "number_of_edit_able_staff";
    /** 流程保密类型 */
    public static final String PROCESS_IS_SECRECY = "is_secrecy";

    public static final String NUMBER_OF_FIXED_SEAL_TYPE = "number_of_fixed_seal_type";

    //设置指定印章的签署方数量
    public static final String NUMBER_FIXED_SEAL = "number_fixed_seal";

    //设置最低阅读时长的签署方数量
    public static final String NUMBER_OF_ZDYDSC = "number_of_zdydsc";

    //设置必须阅读到底的签署方数量
    public static final String NUMBER_OF_BXYDDD = "number_of_bxyddd";

    // 设置签署声明签署方数量
    public static final String NUMBER_OF_SIGN_STATEMENT = "number_of_sign_statement";

    //流程使用的水印类型
    public static final String CONTRACT_WATERMARK_TYPE = "watermark_type";

    // 指定附件参与方个数
    public static final String DESIGNATED_ATTACHMENTS_NUMBER = "Designated_Attachments_Number";
    // 指定附件核验参与方个数
    public static final String DESIGNATED_ATTACHMENTS_VERIFICATION_NUMBER = "Designated_Attachments_Verification_Number";
    // 使用动态模板签署文件个数
    public static final String USE_DYNAMIC_HTML_TEMPLATE = "Use_Dynamic_HTML_Template";
    // 意愿-人脸参与方个数
    public static final String WILLINGNESS_FACE = "Willingness_Face";
    // 意愿-短信参与方个数
    public static final String WILLINGNESS_MESSAGE = "Willingness_Message";
    // 意愿-邮箱参与方个数
    public static final String WILLINGNESS_EMAIL = "Willingness_eMail";
    // 意愿-密码参与方个数
    public static final String WILLINGNESS_PASSWORD = "Willingness_Password";
    // 钉钉组织id
    public static final String CORP_ID = "corp_id";
    // 钉钉组织名称
    public static final String CORP_NAME = "corpName";
    // 钉钉主企业名称
    public static final String MAIN_ENTERPRISE = "main_enterprise";
    // 姓名+身份证发起
    public static final String PSN_IDCARD_INITIATION = "psn_idcard_initiation";
    // 姓名+手机号发起
    public static final String PSN_MOBILE_INITIATION = "psn_mobile_initiation";
    // 姓名+手机号+身份证发起
    public static final String PSN_COMPLETE_INITIATION = "psn_complete_initiation";
    // 指定企业经办人发起
    public static final String ORG_OPERATOR_INITIATION = "org_operator_initiation";
    // 指定企业名称发起
    public static final String ORG_NAME_INITIATION = "org_name_initiation";
    // 指定企业内部角色发起
    public static final String ORG_ROLE_INITIATION = "org_role_initiation";
    //是否开启该配置项
    public static final String CONFIGURATION_ITEMS = "configurationItems";

    public static String sensorString(Object str) {
        return null == str ? "" : str.toString();
    }

    public static String sensorInt(String str) {
        return null == str ? "0" : str;
    }

    public static String sensorString(Object str, String defaultStr) {
        return null == str ? sensorString(defaultStr) : str.toString();
    }
}

package com.timevale.contractmanager.core.service.resourceshare.actions.impl;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.service.resourceshare.actions.DoResourceShareActionService;
import com.timevale.saas.common.manage.common.service.enums.share.ResourceTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.share.ShareOperateTypeEnum;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceUrlActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoProcessParticipantAuthRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceShareActionResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceUrlActionResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoProcessParticipantAuthResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoResourceShareActionResponseDTO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/04/14
 */
@Service
public class DoFlowTemplateShareActionServiceImpl implements DoResourceShareActionService {

    @Override
    public String getResourceType() {
        return ResourceTypeEnum.FLOW_TEMPLATE.name();
    }

    @Override
    public DoResourceShareActionResponseDTO doResourceShareAction(
            DoResourceShareActionRequestDTO request) {

        if (ShareOperateTypeEnum.COPY.name().equals(request.getShareOperateType())) {
            return new DoResourceShareActionResponseDTO();
        }
        throw new BizContractManagerException(
                BizContractManagerResultCodeEnum.FLOW_TEMPLATE_SHARE_TYPE_NOT_SUPPORT);
    }

    @Override
    public DoGetResourceShareActionResponseDTO doGetResourceShareAction(
            DoGetResourceShareActionRequestDTO request) {

        if (ShareOperateTypeEnum.COPY.name().equals(request.getShareOperateType())) {
            return DoGetResourceShareActionResponseDTO.builder()
                    .canChangeShareConfig(false)
                    .build();
        }
        throw new BizContractManagerException(
                BizContractManagerResultCodeEnum.FLOW_TEMPLATE_SHARE_TYPE_NOT_SUPPORT);
    }

    @Override
    public DoGetResourceUrlActionResponseDTO doGetResourceUrlAction(
            DoGetResourceUrlActionRequestDTO request) {
        if (ShareOperateTypeEnum.COPY.name().equals(request.getShareOperateType())) {
            return new DoGetResourceUrlActionResponseDTO();
        }
        throw new BizContractManagerException(
                BizContractManagerResultCodeEnum.FLOW_TEMPLATE_SHARE_TYPE_NOT_SUPPORT);
    }

    @Override
    public DoProcessParticipantAuthResponseDTO doProcessParticipantAuth(DoProcessParticipantAuthRequestDTO requestDTO) {
        return null;
    }
}

package com.timevale.contractmanager.core.service.tracking.enums;

import com.google.common.collect.Maps;
import com.timevale.contractmanager.core.model.dto.request.SaveFlowTemplateRequest;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveOperatorRequest;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveUpdateOperatorRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.menu.CreateMenuRequest;
import com.timevale.contractmanager.core.service.tracking.consts.TrackingKeyConstant;


import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-12-13 18:19
 */
public enum TrackingEventEnum {
    FLOW_TEMPLATE_SAVE(TrackingKeyConstant.FLOW_TEMPLATE_SAVE, "TemplateManagement_Save", SaveFlowTemplateRequest.class),
    MENU_SAVE(TrackingKeyConstant.MENU_SAVE, "set_folder_sever", CreateMenuRequest.class),
    CREATE_AUTO_ARCHIVE_SAVE_MENU(TrackingKeyConstant.CREATE_AUTO_ARCHIVE_SAVE_MENU, "set_folder_sever", AutoArchiveOperatorRequest.class),
    UPDATE_RULE_SAVE_MENU(TrackingKeyConstant.UPDATE_RULE_SAVE_MENU, "set_folder_sever", AutoArchiveUpdateOperatorRequest.class)


    ;

    /** 埋点标识 */
    private String key;
    /** 埋点事件 */
    private String event;
    /** 埋点数据类型 */
    private Class beanType;

    private static Map<String, TrackingEventEnum> items;

    static {
        items = Maps.newHashMap();
        for (TrackingEventEnum item : values()) {
            items.put(item.getKey(), item);
        }
    }

    TrackingEventEnum(String key, String event, Class beanType) {
        this.key = key;
        this.event = event;
        this.beanType = beanType;
    }

    public String getKey() {
        return key;
    }

    public String getEvent() {
        return event;
    }

    public Class getBeanType() {
        return beanType;
    }

    /**
     * 获取埋点事件
     *
     * @param key
     * @return
     */
    public static TrackingEventEnum valueOfKey(String key) {
        return items.get(key);
    }
}

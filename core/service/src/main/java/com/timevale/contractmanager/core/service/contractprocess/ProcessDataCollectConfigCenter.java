package com.timevale.contractmanager.core.service.contractprocess;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by t<PERSON><PERSON><PERSON> on 2022/5/10
 */
@Component
public class ProcessDataCollectConfigCenter {


    // procces change
    @Value("${mq.process.change.topic:cm_process_change_topic}")
    private String processTopicName;

    @Value("${mq.process.change.data.collect.group:cm_process_change_data_collect_group}")
    private String processGroupName;

    @Value("${mq.process.change.data.collect.consume.threadNum:5}")
    private Integer processConsumeThreadNums;

    // cooperation
    @Value("${mq.process.cooperation.change.topic:process_cooperation_change_topic}")
    private String cooperationTopicName;

    @Value("${mq.process.cooperation.change.data.collect.group:cm_process_cooperation_change_data_collect_group}")
    private String cooperationGroupName;

    @Value("${mq.process.cooperation.change.data.collect.consume.threadNum:5}")
    private Integer cooperationConsumeThreadNums;

    // sign
    @Value("${mq.process.sign.change.data.collect.topic:sign_flow_change_topic}")
    private String signTopicName;

    @Value("${mq.process.sign.change.data.collect.topic:sign_flow_change_saas_degrade_topic}")
    public String signDegradeTopicName;

    @Value("${mq.process.sign.change.data.collect.group:cm_process_sign_change_data_collect_group}")
    private String signGroupName;

    @Value("${mq.process.sign.change.data.collect.degradeGroup:cm_process_sign_change_data_collect_degrade_group}")
    public String signDegradeGroupName;

    @Value("${mq.process.sign.change.data.collect.consume.threadNum:5}")
    private Integer signConsumeThreadNums;

    @Value("${mq.process.sign.change.data.collect.consume.degradeThreadNum:2}")
    public Integer degradeSignConsumeThreadNums;

    // 审批整合
    @Value("${mq.approval.change.topic:approval_change_topic}")
    private String approvalChangeTopicName;

    @Value("${mq.approval.change.group:cm_approval_change_group}")
    private String approvalChangeGroupName;

    @Value("${mq.approval.change.consume.thread.nums:2}")
    private Integer approvalChangeThreadNum;

    @Value("${mq.approval.change.update.biz.exts.group:cm_approval_change_update_biz_exts_group}")
    private String approvalChangeUpdateBizExtsGroupName;

    @Value("${mq.approval.change.update.biz.exts.consume.thread.nums:1}")
    private Integer approvalChangeUpdateBizExtsThreadNum;

    @Value("${mq.approval.task.change.topic:approval_task_change_topic}")
    private String approvalTaskChangeTopicName;

    @Value("${mq.approval.task.change.group:cm_approval_task_change_group}")
    private String approvalTaskChangeGroupName;

    @Value("${mq.approval.task.change.consume.thread.nums:2}")
    private Integer approvalTaskChangeThreadNum;

    //合同审批
    @Value("${mq.contract.approval.change.topic:contract_approval_change_topic}")
    private String contractApprovalChangeTopicName;

    @Value("${mq.contract.approval.change.group:cm_contract_approval_change_group}")
    private String contractApprovalChangeGroupName;

    @Value("${mq.contract.approval.change.consume.threadNum:5}")
    private Integer contractApprovalChangeConsumeThreadNums;

    //用印审批-审批流程维度
    @Value("${mq.seal.approval.change.topic:approval_flows_finish}")
    private String sealApprovalChangeTopicName;

    @Value("${mq.seal.approval.change.group:cm_seal_approval_change_group}")
    private String sealApprovalChangeGroupName;

    @Value("${mq.seal.approval.change.consume.threadNum:5}")
    private Integer sealApprovalChangeConsumeThreadNums;

    //用印审批-审批流程节点维度
    @Value("${mq.seal.approval.task.change.topic:approval_flows_change}")
    private String sealApprovalTaskChangeTopicName;

    @Value("${mq.seal.approval.task.change.group:cm_seal_approval_task_change_group}")
    private String sealApprovalTaskChangeGroupName;

    @Value("${mq.seal.approval.task.change.consume.threadNum:5}")
    private Integer sealApprovalTaskChangeConsumeThreadNums;

    //用印审批-审批流程补全数据
    @Value("${mq.seal.approval.fill.topic:SEAL_APPROVAL_FULL}")
    private String sealApprovalFillTopicName;

    @Value("${mq.seal.approval.fill.group:cm_seal_approval_fill_group}")
    private String sealApprovalFillGroupName;

    @Value("${mq.seal.approval.fill.consume.threadNum:5}")
    private Integer sealApprovalFillConsumeThreadNums;

    // fix process
    @Value("${mq.process.fix.data.collect.topic:cm_process_fix_topic}")
    private String fixProcessTopicName;

    @Value("${mq.process.fix.data.collect.group:cm_process_fix_data_collect_group}")
    private String fixProcessGroupName;

    @Value("${mq.process.fix.data.collect.consume.threadNum:1}")
    private Integer fixConsumeThreadNums;

    // temp group
    @Value("${mq.process.group.change.topic:cm_group_change_topic}")
    private String tempGroupTopicName;

    @Value("${mq.process.tempGroup.change.data.collect.group:cm_process_tempgroup_change_data_collect_group}")
    private String tempGroupGroupName;

    @Value("${mq.process.tempGroup.change.data.collect.consume.threadNum:1}")
    private Integer tempGroupConsumeThreadNums;


    // 归档提取专用topic
    @Value("${mq.process.change.manage.topic:cm_process_change_manage_topic}")
    public String processManageTopicName;

    @Value("${mq.process.change.manage.collect.group:cm_process_change_manage_data_collect_group}")
    public String processManageGroupName;

    @Value("${mq.process.change.manage.collect.consume.threadNum:3}")
    public Integer processManageConsumeThreadNums;

    //
    private static final String skipCollectProcessIdConfigKey = "processDataCollectSkipProcessId";
    private static final String PROCESS_PROCESSOR_NOT_EXIST_PRINT_LOG_CONFIG_KEY = "processDataCollect:processNotExitPrintLog";

    //
    public String processTopicName() {
        return processTopicName;
    }

    public String processGroupName() {
        return processGroupName;
    }

    public Integer processThreadNums() {
        return processConsumeThreadNums;
    }

    public String fixProcessTopicName() {
        return fixProcessTopicName;
    }

    public String fixProcessGroupName() {
        return fixProcessGroupName;
    }

    public Integer fixThreadNum() {
        return fixConsumeThreadNums;
    }

    //
    public String cooperationTopicName() {
        return cooperationTopicName;
    }

    public String cooperationGroupName() {
        return cooperationGroupName;
    }

    public Integer cooperationThreadNums() {
        return cooperationConsumeThreadNums;
    }


    //
    public String signTopicName() {
        return signTopicName;
    }

    public String signGroupName() {
        return signGroupName;
    }

    public Integer signThreadNums() {
        return signConsumeThreadNums;
    }

    public String getApprovalChangeTopicName() {
        return approvalChangeTopicName;
    }

    public String getApprovalChangeGroupName() {
        return approvalChangeGroupName;
    }

    public Integer getApprovalChangeThreadNum() {
        return approvalChangeThreadNum;
    }

    public String getApprovalChangeUpdateBizExtsGroupName() {
        return approvalChangeUpdateBizExtsGroupName;
    }

    public Integer getApprovalChangeUpdateBizExtsThreadNum() {
        return approvalChangeUpdateBizExtsThreadNum;
    }

    public String getApprovalTaskChangeTopicName() {
        return approvalTaskChangeTopicName;
    }

    public String getApprovalTaskChangeGroupName() {
        return approvalTaskChangeGroupName;
    }

    public Integer getApprovalTaskChangeThreadNum() {
        return approvalTaskChangeThreadNum;
    }

    public String getContractApprovalChangeTopicName() {
        return contractApprovalChangeTopicName;
    }

    public String getContractApprovalChangeGroupName() {
        return contractApprovalChangeGroupName;
    }

    public Integer getContractApprovalChangeConsumeThreadNums() {
        return contractApprovalChangeConsumeThreadNums;
    }

    public String getSealApprovalChangeTopicName() {
        return sealApprovalChangeTopicName;
    }

    public String getSealApprovalChangeGroupName() {
        return sealApprovalChangeGroupName;
    }

    public Integer getSealApprovalChangeConsumeThreadNums() {
        return sealApprovalChangeConsumeThreadNums;
    }

    public String getSealApprovalTaskChangeTopicName() {
        return sealApprovalTaskChangeTopicName;
    }

    public String getSealApprovalTaskChangeGroupName() {
        return sealApprovalTaskChangeGroupName;
    }

    public Integer getSealApprovalTaskChangeConsumeThreadNums() {
        return sealApprovalTaskChangeConsumeThreadNums;
    }

    public String getSealApprovalFillTopicName() {
        return sealApprovalFillTopicName;
    }

    public String getSealApprovalFillGroupName() {
        return sealApprovalFillGroupName;
    }

    public Integer getSealApprovalFillConsumeThreadNums() {
        return sealApprovalFillConsumeThreadNums;
    }

    //
    public String tempGroupTopicName() {
        return tempGroupTopicName;
    }

    public String tempGroupGroupName() {
        return tempGroupGroupName;
    }

    public Integer tempGroupThreadNums() {
        return tempGroupConsumeThreadNums;
    }

    public Integer maxReconsumeTimes() {
        return 16;
    }

    public int printErrorReconsumeTimes() {
        return 3;
    }

    public String skipCollectProcessId() {

        String configStr = ConfigService.getAppConfig().getProperty(skipCollectProcessIdConfigKey, "");
        return configStr;
    }

    public boolean newSelfStartMsgSwitch() {
        return ConfigService.getAppConfig().getBooleanProperty("newSelfStartMsgSwitch", true);
    }

    public boolean newTransferMsgSwitch() {
        return ConfigService.getAppConfig().getBooleanProperty("newTransferHelpMsgSwitch", true);
    }

    public Set<String> processNotExitNotPrintLog() {
        String notPrintConfigValue = ConfigService.getAppConfig()
                .getProperty(PROCESS_PROCESSOR_NOT_EXIST_PRINT_LOG_CONFIG_KEY, "[]");
        if (StringUtils.isBlank(notPrintConfigValue)) {
            return new HashSet<>();
        }
        return JSONObject.parseObject(notPrintConfigValue, new TypeReference<Set<String>>() {});

    }

}

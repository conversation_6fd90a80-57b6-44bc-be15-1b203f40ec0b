package com.timevale.contractmanager.core.service.offlinecontract.bean.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量删除线下合同导入记录
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class DeleteOfflineContractRecordsDTO extends ToString {

    @NotBlank(message = "主体gid不能为空")
    private String subjectGid;

    @NotEmpty(message = "导入记录id列表不能为空")
    private List<String> recordIds;
}

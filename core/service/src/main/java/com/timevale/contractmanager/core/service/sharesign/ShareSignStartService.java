package com.timevale.contractmanager.core.service.sharesign;

import com.timevale.contractmanager.core.model.dto.request.sharesign.ShareSignTaskStartRequest;
import com.timevale.contractmanager.core.model.dto.response.sharesign.ShareSignTaskUrlResponse;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContext;

/**
 * 批量流程及单个流程的统一发起入口，从前端
 * @author: huifeng
 * @since: 2020-08-18 14:11
 **/
public interface ShareSignStartService {

    ShareSignTaskUrlResponse start(ProcessStartContext processStartContext, ShareSignTaskStartRequest startBO);

    void deleteTempTemplateId(String tempTemplateId);
}

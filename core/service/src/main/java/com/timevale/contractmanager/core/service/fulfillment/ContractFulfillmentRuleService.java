package com.timevale.contractmanager.core.service.fulfillment;

import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleQueryListModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleSaveModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleSyncModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleUpdateModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentShardingRuleQueryListModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleDetailResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleListResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleSaveResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentShardingRuleListResult;

import java.util.List;

/**
 * ContractFulfillmentRuleService
 *
 * <AUTHOR>
 * @since 2023/10/11 4:31 下午
 */
public interface ContractFulfillmentRuleService {

    ContractFulfillmentRuleSaveResult save(ContractFulfillmentRuleSaveModel model);

    void update(ContractFulfillmentRuleUpdateModel model);

    void delete(String ruleId, String tenantId, String accountId);

    void updateStatus(String ruleId, String status, String tenantId, String accountId);

    ContractFulfillmentRuleDetailResult detail(String ruleId, String tenantId);

    ContractFulfillmentRuleListResult pageList(ContractFulfillmentRuleQueryListModel model);

    List<String> queryCustomTypeNameList(String tenantId);

    ContractFulfillmentShardingRuleListResult shardingList(ContractFulfillmentShardingRuleQueryListModel model);

    void syncHistoryNoticeRule(ContractFulfillmentRuleSyncModel model);

    void syncNoticeRule(String gid);

    /**
     * 生成并保存履约规则查询语句
     * @param ruleId
     * @param tenantGid
     */
    void generateAndSaveRuleQueryScript(String ruleId, String tenantGid);
}


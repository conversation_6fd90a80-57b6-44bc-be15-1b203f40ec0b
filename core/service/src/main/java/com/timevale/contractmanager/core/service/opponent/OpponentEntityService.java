package com.timevale.contractmanager.core.service.opponent;

import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualCreateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualUpdateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationCreateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationUpdateRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBaseResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.dto.account.AccountUpdateDTO;
import com.timevale.contractmanager.core.service.dto.opponent.OpponentEntityBO;
import com.timevale.signflow.search.service.model.v2.OpponentEntityQueryModel;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-01-26 10:53
 */
public interface OpponentEntityService {

	OpponentBaseResponse createOrganization(String processId,String operatorOid, String tenantOid, OpponentOrganizationCreateRequest createRequest);

	// 通过消息自动添加
	OpponentBaseResponse autoCreateOrganizationByMsg(String processId, String operatorOid, String tenantOid,
													 String organizationName);

	OpponentBaseResponse createIndividual(String processId,String operatorOid, String tenantOid, OpponentIndividualCreateRequest createRequest);

    /**
     * 创建企业
     *
     * @param userAccount 个人账号
     * @param tenant 企业账号
     * @param createRequest 入参
     * @return OpponentBaseResponse
     */
    OpponentBaseResponse createOrganization(
			String processId,
            UserAccount userAccount,
            UserAccount tenant,
            OpponentOrganizationCreateRequest createRequest);

    /**
     * 创建个人
     *
     * @param userAccount 个人账号
     * @param tenant 企业账号
     * @param createRequest 入参
     * @param relationEntity
	 * @return OpponentBaseResponse
     */
    OpponentBaseResponse createIndividual(
            String processId,
            UserAccount userAccount,
            UserAccount tenant,
            OpponentIndividualCreateRequest createRequest,
            List<OpponentEntityDO> relationEntity);

	/**
     * 修改企业相对方信息
     *
     * @param operatorOid 当前操作人
     * @param tenantOid 企业空间id
     * @param organizationId 相对方实体id
     * @param request 修改信息入参
     */
    void updateOrganization(
            String operatorOid,
            String tenantOid,
            String organizationId,
            OpponentOrganizationUpdateRequest request);

    /**
     * 修改个人相对方信息
     *
     * @param operatorOid 当前操作人
     * @param tenantOid 企业空间id
     * @param individualId 相对方实体id
     * @param request 修改信息入参
     */
    void updateIndividual(
            String operatorOid,
            String tenantOid,
            String individualId,
            OpponentIndividualUpdateRequest request);

    /**
	 * 获取企业空间下合同相对方-企业列表
	 *
	 * @param operatorOid 当前操作人
	 * @param tenantOid 企业空间id
	 * @param request 列表入参
	 * @return 合同相对方-企业列表
	 */
	OpponentOrganizationListResponse listOrganizations(
			String operatorOid, String tenantOid, OpponentOrganizationListRequest request, Boolean calculateFlag);

    /**
     * 获取租户空间下某个相对方企业详细信息
     *
     * @param operatorOid 当前操作人
     * @param tenantOid 企业空间id
     * @param organizationId 企业实体uuid
     * @return 相对方企业详细信息
     */
    OpponentOrganizationResponse getOrganization(
            String operatorOid, String tenantOid, String organizationId);

    /**
     * 获取租户空间所有相对方个人列表
     *
	 * @param operatorOid 当前操作人
	 * @param tenantOid 企业空间id
	 * @param organizationId 企业相对方实体id
	 * @param listRequest 列表入参
	 * @return 相对方个人列表
	 */
	OpponentIndividualListResponse listIndividuals(
			String operatorOid,
			String tenantOid,
			String organizationId,
			OpponentIndividualListRequest listRequest,
			Boolean calculateFlag);

    /**
     * 获取租户空间下某个相对方个人详细信息
     *
     * @param operatorOid 当前操作人
     * @param tenantOid 企业空间id
     * @param individualId 相对方个人uuid
     * @return 相对方个人详细信息
     */
    OpponentIndividualResponse getIndividuals(
            String operatorOid, String tenantOid, String individualId);

	/**
	 * 删除
	 * @param id
	 * @param deleteIndividuals
	 */
	void deleteOpponentEntity(String id,boolean deleteIndividuals,String tenantOid);

	/**
	 * 批量删除
	 * @param ids
	 * @param deleteIndividuals
	 */
	void batchDeleteOpponentEntity(List<String> ids,boolean deleteIndividuals,String tenantOid);


	/**
	 * 获取相对方实体
	 * @param uuid
	 * @return
	 */
	OpponentEntityBO getEntity(String uuid);

	/**
	 * 根据手机/邮箱更新个人oid
	 *
	 * @param phone
	 * @param email
	 * @param entityOid
	 */
	void updateIndividualOid(String phone, String email, String entityOid);

	/**
	 * 已实名企业变更企业名称，更新相对方企业名称
	 *
	 * @param organizationOid
	 * @param organizationGid
	 * @param organizationName
	 */
	void renameOpponentOrganizations(String organizationOid, String organizationGid, String organizationName);

	/**
	 * 个人账户修改，更新相对方个人信息
	 * 1、个人账户解绑联系方式（如：有手机号、邮箱两种联系方式，解绑手机号）
	 *		a、查询entity_unique_id为accountUpdateDTO.sourceAccount的所有相对方记录
	 *	    b、查询personAccountOid的其他联系方式another_contact
	 *		c、对每个相对方记录record执行以下操作
	 *			c.1、another_contact在该记录租户下是否注册相对方个人，如果是：删除record；如果否：更新record的unique_entity_id为another_contact
	 *		d、结束
	 * 2、个人账户换绑联系方式（手机号联系方式从xx换成yy）
	 *		a、查询entity_unique_id为accountUpdateDTO.sourceAccount的所有相对方记录
	 *	    b、对每个相对方记录record执行以下操作
	 *	    	b.1、accountUpdateDTO.targetAccount是否在该记录租户下注册为相对方个人，如果是：删除record；如果否：更新record的unique_entity_id为accountUpdateDTO.targetAccount
	 * 3、个人账户新绑联系方式（如：已有一个联系方式手机号，新增联系方式邮箱）
	 * 		不处理
	 * 4、个人账户注销
	 * 		todo 把相对方个人记录设置为注销状态
	 *
	 * @param personAccountOid
	 * @param personAccountGid
	 * @param accountUpdateDTO
	 */
	void personAccountUpdateOnChange(String personAccountOid, String personAccountGid, AccountUpdateDTO accountUpdateDTO);

	/**
	 * 查询还能否新增实体
	 * @param opponentEntityType
	 */
	void checkEntityLimit(String operatorOid, String tenantOid,Integer opponentEntityType);

	/**
	 * 校验新增是否超过新增上限
	 *
	 * @param tenantId    企业oid
	 * @param tenantGid   企业gid
	 * @param operatorOid 操作人oid
	 * @param entityType  企业/个人
	 * @param count       新增数量
	 * @param clientId
	 */
	void checkCreateLimit(String tenantId, String tenantGid, String operatorOid, Integer entityType, Integer count, String clientId);

	/**
	 * 更新实名状态
	 *
	 * @param oid
	 * @param gid
	 * @param name
	 */
	void updateOpponentEntityRealNameStatus(String oid, String gid, String name, Integer authorizeType);

	/**
	 * 删除个人信息
	 */
	void deleteOpponentIndividual(List<String> uuidList, List<Long> idList,Boolean hasDel);

	/**
	 * 临时代码 同步库里没有统一社会信用代码的相对方企业
	 */
	void batchSyncOpponentOrgans();

	/**
	 * 法人信息变更
	 * @param tenantOid
	 * @param name
	 */
	void updateLegalInfo(String tenantOid, String name);

	void validOrganizationCreateRequest(OpponentOrganizationCreateRequest createRequest);

	/**
	 * 给手机号码 发合同 生成 oid1 未实名无gid, 用户在支付包签署 生成oid2 实名生成gid
	 * 此时用户中心会把 gid 同步到oid1, 但是相对方里存储的oid1无gid
	 */
	void syncOpponentEntityGid(String oid);

	/**
	 * 通过process 未发起企业添加相对方
	 */
	boolean addOpponentByProcess(String processId);


	/**
	 * 增加多业务查询条件
	 */
	void populateOpponentMultiBizQuery(String tenantOid, String tenantGid,
									   String personOid, OpponentEntityQueryModel query);

}

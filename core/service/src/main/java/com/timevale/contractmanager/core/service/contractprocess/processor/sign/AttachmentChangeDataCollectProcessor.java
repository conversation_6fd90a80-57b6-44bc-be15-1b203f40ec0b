package com.timevale.contractmanager.core.service.contractprocess.processor.sign;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.common.service.model.AccountSimpleModel;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.SignChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessAttachmentChangeEntity;
import com.timevale.contractmanager.core.service.process.ProcessFileAuthService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.base.util.ExceptionLogUtil;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessFileDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessFileInfoDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/5/11
 */
@Slf4j
@Component
public class AttachmentChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessFileAuthService processFileAuthService;
    @Autowired
    private ContractProcessWriteClient processWriteClient;
    @Autowired
    private ContractProcessReadClient processQueryClient;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.signTopicName(), SignChangeTagEnum.ATTACHMENT_CHANGE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessAttachmentChangeEntity entity = JsonUtils.json2pojo(data, ProcessAttachmentChangeEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        doProcess(collectContext);
    }


    private void doProcess(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();

        // 这个消息有点特殊，是签署的消息，但是修改的是附件
        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);

        ProcessAttachmentChangeEntity entity = (ProcessAttachmentChangeEntity) collectContext.getData();

        if (CollectionUtils.isEmpty(entity.getAttachmentNames())) {
            return;
        }
        List<ContractProcessFileDTO> fileDTOList = entity.getAttachmentNames().stream().map(fileName -> {
            ContractProcessFileDTO fileDTO = new ContractProcessFileDTO();
            fileDTO.setFileName(fileName);
            return fileDTO;
        }).collect(Collectors.toList());

        // 数据回填
        ContractProcessFileInfoDTO fileInfoDTO = contractProcessDTO.getFileInfo();
        fileInfoDTO.setAttachmentFiles(fileDTOList);

        // 修改数据
        ContractProcessUpdateParam input = new ContractProcessUpdateParam();
        input.setProcessId(processId);
        input.setFileInfo(ProcessDataCollectConverter.fileDTO2FileInfoParam(fileInfoDTO));
        input.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_ATTACHMENT_CHANGE);
        processWriteClient.updateByProcessId(input);

        try {
            AccountSimpleModel accountSimpleModel = new AccountSimpleModel();
            accountSimpleModel.setOid(contractProcessDTO.getInitiator().getSubject().getOid());
            accountSimpleModel.setGid(StringUtils.isEmpty(contractProcessDTO.getInitiator().getSubject().getGid()) ? "" : contractProcessDTO.getInitiator().getSubject().getGid());
            processFileAuthService.appendAttachmentFileAuth(processId, accountSimpleModel, entity.getFlowId());
        } catch (Exception e) {
            ExceptionLogUtil.messageLogIfWarn(log, e, "appendAttachmentFileAuth error, processId:{}", collectContext.getProcessId());
        }
    }

}

package com.timevale.contractmanager.core.service.contractprocess.processor.approval;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.mq.model.SealApprovalChangeMsgEntity;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.SealApprovalDTO;
import com.timevale.signflow.search.service.model.contractprocess.SealApprovalTaskInfoDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import com.timevale.signflow.search.service.request.datacollect.SealApprovalParam;
import com.timevale.signflow.search.service.request.datacollect.SealApprovalTaskInfoParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SealApprovalComponent
 *
 * <AUTHOR>
 * @since 2022/8/10 2:40 下午
 */
@Slf4j
@Component
public class SealApprovalComponent {

    @Autowired
    private ProcessDataBuilder processDataBuilder;

    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;

    @Autowired
    private ContractProcessReadClient processQueryClient;

    public void doProcess(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        SealApprovalChangeMsgEntity entity = (SealApprovalChangeMsgEntity) collectContext.getData();
        SealApprovalParam sealApproval = processDataBuilder.buildSealApproval(entity.getApprovalId());
        if (sealApproval == null) {
            log.info(ProcessDataCollectSupport.LOG_PREFIX + "sealApproval is null skip, msg:{}", JsonUtils.obj2json(entity));
            return;
        }
        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);


        //  准备回填已删除的， 防止已删除人数据更新丢失
        Map<String, Map<String, List<String>>> approvalIdTaskIdHiddenPersonMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(contractProcessDTO.getSealApproval())) {
            for (SealApprovalDTO sealApprovalDTO : contractProcessDTO.getSealApproval()) {
                if (CollectionUtils.isEmpty(sealApprovalDTO.getApprovalTaskInfo())) {
                    continue;
                }
                for (SealApprovalTaskInfoDTO task : sealApprovalDTO.getApprovalTaskInfo()) {
                    if (CollectionUtils.isEmpty(task.getHiddenPersonGid())) {
                        continue;
                    }
                    approvalIdTaskIdHiddenPersonMap.computeIfAbsent(sealApprovalDTO.getApprovalId(), key -> new HashMap<>())
                            .put(task.getTaskId(), task.getHiddenPersonGid());
                }
            }
        }

        List<SealApprovalDTO> sealApprovalDTOList = contractProcessDTO.getSealApproval();
        List<SealApprovalParam> sealApprovalParamList = ProcessDataCollectConverter.sealApprovalDTO2ParamList(sealApprovalDTOList);
        boolean exist = false;
        for (int i = 0; i < sealApprovalParamList.size(); i++) {
            SealApprovalParam param = sealApprovalParamList.get(i);
            if (StringUtils.isNotEmpty(param.getApprovalId()) && param.getApprovalId().equals(entity.getApprovalId())) {
                sealApprovalParamList.set(i, sealApproval);
                exist = true;
                // 回填已删除的
                Map<String, List<String>> taskIdHiddenPersonGidMap = approvalIdTaskIdHiddenPersonMap.get(param.getApprovalId());
                if (MapUtils.isNotEmpty(taskIdHiddenPersonGidMap) &&
                        CollectionUtils.isNotEmpty(sealApproval.getApprovalTaskInfo())) {
                    for (SealApprovalTaskInfoParam task : sealApproval.getApprovalTaskInfo()) {
                        task.setHiddenPersonGid(taskIdHiddenPersonGidMap.get(task.getTaskId()));
                    }
                }
            }
        }

        if (!exist) {
            sealApprovalParamList.add(sealApproval);
        }
        ContractProcessUpdateParam param = new ContractProcessUpdateParam();
        param.setProcessId(processId);
        param.setSealApproval(sealApprovalParamList);
        param.setBizScene(ProcessDataCollectBizSceneConstants.SEAL_APPROVAL_CHANGE);
        contractProcessWriteClient.updateByProcessId(param);
    }
}

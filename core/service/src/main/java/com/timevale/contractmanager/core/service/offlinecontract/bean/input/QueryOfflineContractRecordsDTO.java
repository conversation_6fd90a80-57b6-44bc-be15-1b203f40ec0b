package com.timevale.contractmanager.core.service.offlinecontract.bean.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 查询线下合同导入记录列表
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class QueryOfflineContractRecordsDTO extends ToString {

    private String accountGid;
    
    @NotBlank(message = "主体Gid不能为空")
    private String subjectGid;

    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @NotNull(message = "每页数据大小不能为空")
    private Integer pageSize;

    /** 是否返回菜单路径 */
    private boolean withMenuPath;
    
    /** 是否只展示当前操作人有关的数据 */
    private boolean needFilterOperator;
}

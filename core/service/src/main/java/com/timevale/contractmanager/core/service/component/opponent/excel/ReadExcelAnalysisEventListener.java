package com.timevale.contractmanager.core.service.component.opponent.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.bo.opponent.ExcelInfoBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEntityExcelReadBO;
import com.timevale.contractmanager.core.service.component.opponent.excel.reader.IndividualEntityReader;
import com.timevale.contractmanager.core.service.component.opponent.excel.reader.OpponentEntityReader;
import com.timevale.contractmanager.core.service.component.opponent.excel.reader.OrganizationsEntityReader;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 解析表格事件监听
 *
 * <AUTHOR>
 * @since 2021-02-02 11:43
 */
@Getter
public class ReadExcelAnalysisEventListener
        extends AnalysisEventListener<LinkedHashMap<Integer, Object>> {

    private List<ExcelInfoBO> list = new ArrayList<>();
    private OpponentEntityReader reader;
    private List<List<Object>> dataList = new ArrayList<>();

    public ReadExcelAnalysisEventListener(OpponentEntityTypeEnum entityType) {
        // 判断读取的表格类型,使用不同的读取器
        if (OpponentEntityTypeEnum.ORGANIZATION == entityType) {
            reader = new OrganizationsEntityReader();
        } else {
            reader = new IndividualEntityReader();
        }
    }

    @Override
    public void invoke(LinkedHashMap<Integer, Object> data, AnalysisContext context) {
        OpponentEntityExcelReadBO excelReadBO = reader.read(data);
        list.add(excelReadBO.getEntity());
        dataList.add(excelReadBO.getData());
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        super.invokeHeadMap(headMap, context);
        if (reader != null
                && reader instanceof OrganizationsEntityReader
                && headMap.size() != OrganizationsEntityReader.HEAD_COUNT) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.OPPONENT_ENTITY_IMPORT_TEMPLATE_ERROR);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {}
}

package com.timevale.contractmanager.core.service.offlinecontract.handler;

import com.timevale.contractmanager.common.dal.bean.OfflineContractImportProcessDO;
import com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum.IMPORTED;
import static com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum.IMPORTING;
import static com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum.IMPORT_FAILED;
import static com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum.IMPORT_STOPPED;
import static com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum.PART_IMPORTED;

/**
 * 线下合同状态处理器
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public class OfflineContractStatusHandler {

    /**
     * 计算线下合同状态
     *
     * @param importProcessDOS
     * @return
     */
    public static OfflineContractStatusEnum calculateStatusByImportProcessDOs(
            List<OfflineContractImportProcessDO> importProcessDOS) {
        Map<String, List<OfflineContractImportProcessDO>> statusFileMap =
                importProcessDOS.stream().collect(Collectors.groupingBy(i -> i.getStatus()));
        // 导入中
        if (statusFileMap.containsKey(IMPORTING)) {
            return IMPORTING;
        }
        // 全部完成
        if (statusFileMap.size() == 1 && statusFileMap.containsKey(IMPORTED.getStatus())) {
            return IMPORTED;
        }
        // 全部失败
        if (statusFileMap.size() == 1 && statusFileMap.containsKey(IMPORT_FAILED.getStatus())) {
            return IMPORT_FAILED;
        }
        // 部分成功
        if (statusFileMap.size() == 2
                && statusFileMap.containsKey(IMPORTED.getStatus())
                && statusFileMap.containsKey(IMPORT_FAILED.getStatus())) {
            return PART_IMPORTED;
        }
        // 停止导入
        if (statusFileMap.containsKey(IMPORT_STOPPED.getStatus())) {
            return IMPORT_STOPPED;
        }
        return IMPORTING;
    }
}

package com.timevale.contractmanager.core.service.todocenter;

import com.timevale.contractmanager.core.model.dto.request.TodoProcessRequest;
import com.timevale.contractmanager.core.model.dto.response.todocenter.ProcessTodoListResponse;
import com.timevale.contractmanager.core.model.dto.response.todocenter.SubjectsTodoTotalResponse;
import com.timevale.contractmanager.core.model.dto.response.todocenter.TypeTodoHaveResponse;
import com.timevale.contractmanager.core.model.dto.response.todocenter.TypeTodoTotalResponse;
import com.timevale.contractmanager.core.model.enums.TodoTypeEnum;

/**
 * 待办中心服务
 *
 * <AUTHOR>
 * @since 2021-11-24 11:42
 */
public interface TodoCenterService {

    TypeTodoHaveResponse have(String tenantOid, String operatorOid);


    /**
     * 待办任务数量统计，按照流程状态统计
     *
     * @param userOid
     * @return 按照流程状态统计的待办数量
     */
    TypeTodoTotalResponse count(String userOid, String subjectOid,Boolean queryApprovalTotalCount, Boolean queryCurrentSubject);

    /**
     * 待办任务数量统计，按照主体统计
     *
     * @param userOid
     * @param type    待办类型，TodoTypeEnum.getType()
     * @return 按照主体维度统计的待办数量
     * @see TodoTypeEnum
     */
    SubjectsTodoTotalResponse aggregateBySubject(String userOid, Integer type);

    /**
     * 合同待办列表查询
     *
     * @param userOid
     * @param request
     * @return
     */
    ProcessTodoListResponse listProcess(
            String userOid, TodoProcessRequest request);
}

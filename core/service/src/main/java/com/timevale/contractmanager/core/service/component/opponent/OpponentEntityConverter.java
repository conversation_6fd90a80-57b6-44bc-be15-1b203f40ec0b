package com.timevale.contractmanager.core.service.component.opponent;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.service.enums.opponent.AuthorizeTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.model.opponent.OpponentIndividualListModel;
import com.timevale.contractmanager.common.service.model.opponent.data.OpponentIndividualData;
import com.timevale.contractmanager.common.service.model.opponent.data.OrganizationInfoData;
import com.timevale.contractmanager.common.service.model.opponent.data.OrgNameContactData;
import com.timevale.contractmanager.common.service.result.opponent.OpponentIndividualListResult;
import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.contractmanager.core.model.bo.opponent.ExcelInfoBO;
import com.timevale.contractmanager.core.model.bo.opponent.OrganizationInfoBO;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualCreateOuterRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualCreateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationCreateOuterRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationCreateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationListRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationResponse;
import com.timevale.contractmanager.common.service.model.opponent.OpponentOrganizationListModel;
import com.timevale.contractmanager.common.service.result.opponent.OpponentOrganizationListResult;
import com.timevale.contractmanager.common.service.model.opponent.data.OpponentOrganizationData;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.bean.v2.OpponentEntityInfo;
import lombok.experimental.UtilityClass;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 相对方实体转换
 *
 * <AUTHOR>
 * @since 2021-01-29 15:30
 */
@UtilityClass
public class OpponentEntityConverter {

    /**
     * toIndividualResponse
     *
     * @param entity OpponentEntityDO
     * @return OpponentIndividualResponse
     */
    public OpponentIndividualResponse toIndividualResponse(OpponentEntityDO entity) {
        Integer authType = null;

        return OpponentIndividualResponse.builder()
                .individualId(entity.getUuid())
                .individualAccountId(entity.getEntityOid())
                .individualName(entity.getEntityName())
                .authorizeType(convertShowAuthorizeType(entity.getAuthorizeType()))
                .desc(entity.getDescription())
                .contact(entity.getEntityUniqueId())
                .createProcessId(entity.getCreateProcessId())
                .createTime(entity.getCreateTime())
                .riskLevel(entity.getRiskLevel())
                .build();
    }

    public OpponentIndividualResponse toIndividualResponse(OpponentEntityInfo entity) {
        List<OrganizationInfoBO> infoList = CollectionUtils.isNotEmpty(entity.getOrganizationInfoList())
                ? entity.getOrganizationInfoList().stream()
                        .map(
                                p -> {
                                    OrganizationInfoBO info = new OrganizationInfoBO();
                                    info.setOrganizationAccountId(p.getOid());
                                    info.setOrganizationId(p.getUuid());
                                    info.setOrganizationName(p.getOrgName());
                                    return info;
                                })
                        .collect(Collectors.toList())
                : new ArrayList<>();
        return OpponentIndividualResponse.builder()
                .individualId(entity.getUuid())
                .individualAccountId(entity.getEntityOid())
                .individualName(entity.getEntityName())
                .authorizeType(convertShowAuthorizeType(entity.getAuthorizeType()))
                .desc(entity.getDescription())
                .contact(entity.getEntityUniqueId())
                .createProcessId(entity.getCreateProcessId())
                .createTime(entity.getCreateTime())
                .riskLevel(entity.getRiskLevel())
                .organizationList(infoList)
                .processingContractCount(0)
                .build();
    }

    /**
     * toOrganizationCreate
     *
     * @param excelInfo ExcelInfoBO
     * @return OpponentIndividualResponse
     */
    public OpponentOrganizationCreateRequest toOrganizationCreate(ExcelInfoBO excelInfo) {
        OpponentOrganizationCreateRequest request = new OpponentOrganizationCreateRequest();
        request.setDesc(excelInfo.getDescription());
        request.setOrganizationName(excelInfo.getOrganizationName().trim());
        request.setCreditCode(StringUtils.isBlank(excelInfo.getSocialCreditCode()) ? excelInfo.getSocialCreditCode()
                : excelInfo.getSocialCreditCode().trim());
        request.setCreditCodeType(excelInfo.getCreditCodeType());
        request.setLegalPersonName(StringUtils.isBlank(excelInfo.getLegalPersonName()) ? excelInfo.getLegalPersonName()
                : excelInfo.getLegalPersonName().trim());

        return request;
    }

    /**
     * toIndividualCreate
     *
     * @param excelInfo ExcelInfoBO
     * @return OpponentIndividualCreateRequest
     */
    public OpponentIndividualCreateRequest toIndividualCreate(ExcelInfoBO excelInfo) {
        OpponentIndividualCreateRequest request = new OpponentIndividualCreateRequest();
        request.setContact(excelInfo.getEntityUniqueId());
        request.setIndividualName(excelInfo.getUserName());
        request.setDesc(excelInfo.getDescription());
        return request;
    }

    public List<OrgNameContactData> parseOpponentEntityDatas(List<? extends UserBO> users) {
        List<OrgNameContactData> dataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(users)) {
            return dataList;
        }
        users.forEach(
                i -> {
                    if (StringUtils.isNotBlank(i.getAccount())) {
                        OrgNameContactData data = new OrgNameContactData();
                        data.setEntityType(OpponentEntityTypeEnum.INDIVIDUAL.getType());
                        data.setUniqueId(i.getAccount());
                        dataList.add(data);
                    }
                    if (StringUtils.isNotBlank(i.getSubjectName())
                            && SubjectTypeEnum.ORG.getType().equals(i.getSubjectType())) {
                        OrgNameContactData data = new OrgNameContactData();
                        data.setEntityType(OpponentEntityTypeEnum.ORGANIZATION.getType());
                        data.setUniqueId(i.getSubjectName());
                        dataList.add(data);
                    }
                });
        return dataList;
    }

    public List<OpponentEntityDO> toOpponentEntityDOList(List<OpponentEntityInfo> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        List<OpponentEntityDO> opponentEntityDOList = new ArrayList<>();
        for (OpponentEntityInfo opponentEntityInfo : resultList) {
            OpponentEntityDO opponentEntityDO = new OpponentEntityDO();
            BeanUtils.copyProperties(opponentEntityInfo, opponentEntityDO);
            opponentEntityDOList.add(opponentEntityDO);
        }
        return opponentEntityDOList;
    }

    public Integer convertShowAuthorizeType(Integer authorizeType) {
        if (authorizeType != null && AuthorizeTypeEnum.SUPPOSE_ACCEPT.getType() == authorizeType) {
            return AuthorizeTypeEnum.ACCEPT.getType();
        }
        return authorizeType;
    }

    public static OpponentIndividualCreateRequest convert(
            OpponentIndividualCreateOuterRequest opponentIndividualCreateOuterRequest) {
        if (null == opponentIndividualCreateOuterRequest) {
            return null;
        }
        OpponentIndividualCreateRequest opponentIndividualCreateRequest = new OpponentIndividualCreateRequest();
        opponentIndividualCreateRequest.setContact(opponentIndividualCreateOuterRequest.getContact());
        opponentIndividualCreateRequest.setIndividualName(opponentIndividualCreateOuterRequest.getIndividualName());
        opponentIndividualCreateRequest.setDesc(opponentIndividualCreateOuterRequest.getDesc());
        opponentIndividualCreateRequest.setOrganizationIds(opponentIndividualCreateOuterRequest.getOrganizationIds());
        return opponentIndividualCreateRequest;
    }

    public static OpponentOrganizationCreateRequest convert(
            OpponentOrganizationCreateOuterRequest opponentOrganizationCreateOuterRequest) {
        if (null == opponentOrganizationCreateOuterRequest) {
            return null;
        }
        OpponentOrganizationCreateRequest opponentOrganizationCreateRequest = new OpponentOrganizationCreateRequest();
        opponentOrganizationCreateRequest
                .setOrganizationName(opponentOrganizationCreateOuterRequest.getOrganizationName());
        opponentOrganizationCreateRequest.setCreditCode(opponentOrganizationCreateOuterRequest.getCreditCode());
        opponentOrganizationCreateRequest.setCreditCodeType(opponentOrganizationCreateOuterRequest.getCreditCodeType());
        opponentOrganizationCreateRequest
                .setLegalPersonName(opponentOrganizationCreateOuterRequest.getLegalPersonName());
        opponentOrganizationCreateRequest.setDesc(opponentOrganizationCreateOuterRequest.getDesc());
        return opponentOrganizationCreateRequest;
    }

    public static OpponentIndividualListRequest convert2OpponentIndividualListRequest(
            OpponentIndividualListModel model) {
        OpponentIndividualListRequest request = new OpponentIndividualListRequest();
        request.setPageNum(model.getPageNum());
        request.setPageSize(model.getPageSize());
        request.setAuthorizeType(model.getAuthorizeType());
        request.setRiskLevel(model.getRiskLevel());
        request.setFuzzyDesc(model.getFuzzyDesc());
        request.setFuzzyIndividualName(model.getFuzzyIndividualName());
        request.setFuzzyEntityUniqueId(model.getFuzzyEntityUniqueId());
        request.setAttachedEntityType(model.getAttachedEntityType());
        request.setScrollId(model.getScrollId());
        request.setUseScroll(model.getUseScroll());
        return request;
    }

    public static OpponentOrganizationListRequest convert2OpponentOrganizationListRequest(
            OpponentOrganizationListModel model) {
        OpponentOrganizationListRequest request = new OpponentOrganizationListRequest();
        request.setPageNum(model.getPageNum());
        request.setPageSize(model.getPageSize());
        request.setAuthorizeType(model.getAuthorizeType());
        request.setRiskLevel(model.getRiskLevel());
        request.setFuzzyDesc(model.getFuzzyDesc());
        request.setFuzzyOrganizationName(model.getFuzzyOrganizationName());
        request.setCreditCode(model.getCreditCode());
        request.setLegalPersonName(model.getLegalPersonName());
        request.setScrollId(model.getScrollId());
        request.setUseScroll(model.getUseScroll());
        return request;
    }

    public static OpponentIndividualListResult convert2OpponentIndividualListResult(
            OpponentIndividualListResponse response) {
        if (response == null) {
            return null;
        }

        OpponentIndividualListResult result = new OpponentIndividualListResult();
        result.setIndividuals(convertIndividualResponseListToDataList(response.getIndividuals()));
        result.setTotalCount(response.getTotalSize() != null ? response.getTotalSize().intValue() : 0);
        result.setPageSize(0); // 默认值，可根据需要调整
        result.setPageNumber(0); // 默认值，可根据需要调整

        return result;
    }

    public static OpponentOrganizationListResult convert2OpponentOrganizationListResult(
            OpponentOrganizationListResponse response) {
        if (response == null) {
            return null;
        }

        OpponentOrganizationListResult result = new OpponentOrganizationListResult();
        result.setOrganizations(convertOrganizationResponseListToDataList(response.getOrganizations()));
        result.setTotalCount(response.getTotalSize() != null ? response.getTotalSize().intValue() : 0);
        result.setPageSize(0); // 默认值，可根据需要调整
        result.setPageNumber(0); // 默认值，可根据需要调整

        return result;
    }

    private static List<OpponentIndividualData> convertIndividualResponseListToDataList(
            List<OpponentIndividualResponse> responseList) {
        if (responseList == null) {
            return null;
        }

        return responseList.stream()
                .map(OpponentEntityConverter::convertIndividualResponseToData)
                .collect(Collectors.toList());
    }

    private static OpponentIndividualData convertIndividualResponseToData(OpponentIndividualResponse response) {
        if (response == null) {
            return null;
        }

        OpponentIndividualData data = new OpponentIndividualData();
        data.setIndividualId(response.getIndividualId());
        data.setIndividualAccountId(response.getIndividualAccountId());
        data.setIndividualName(response.getIndividualName());
        data.setContact(response.getContact());
        data.setAuthorizeType(response.getAuthorizeType());
        data.setDesc(response.getDesc());
        data.setProcessingContractCount(response.getProcessingContractCount());
        data.setCreateProcessId(response.getCreateProcessId());
        data.setCreateName(response.getCreateName());
        data.setCreateContact(response.getCreateContact());
        data.setCreateTime(response.getCreateTime());
        data.setRiskLevel(response.getRiskLevel());
        data.setOrganizationList(convertOrganizationInfoBOListToDataList(response.getOrganizationList()));

        return data;
    }

    private static List<OrganizationInfoData> convertOrganizationInfoBOListToDataList(List<OrganizationInfoBO> boList) {
        if (boList == null) {
            return null;
        }

        return boList.stream()
                .map(OpponentEntityConverter::convertOrganizationInfoBOToData)
                .collect(Collectors.toList());
    }

    private static OrganizationInfoData convertOrganizationInfoBOToData(OrganizationInfoBO bo) {
        if (bo == null) {
            return null;
        }

        OrganizationInfoData data = new OrganizationInfoData();
        data.setOrganizationAccountId(bo.getOrganizationAccountId());
        data.setOrganizationId(bo.getOrganizationId());
        data.setOrganizationName(bo.getOrganizationName());
        data.setAuthorizeType(bo.getAuthorizeType());

        return data;
    }

    private static List<OpponentOrganizationData> convertOrganizationResponseListToDataList(
            List<OpponentOrganizationResponse> responseList) {
        if (responseList == null) {
            return null;
        }

        return responseList.stream()
                .map(OpponentEntityConverter::convertOrganizationResponseToData)
                .collect(Collectors.toList());
    }

    private static OpponentOrganizationData convertOrganizationResponseToData(OpponentOrganizationResponse response) {
        if (response == null) {
            return null;
        }

        OpponentOrganizationData data = new OpponentOrganizationData();
        data.setOrganizationId(response.getOrganizationId());
        data.setOrganizationName(response.getOrganizationName());
        data.setAuthorizeType(response.getAuthorizeType());
        data.setDesc(response.getDesc());
        data.setMemberCount(response.getMemberCount());
        data.setProcessingContractCount(response.getProcessingContractCount());
        data.setCreateProcessId(response.getCreateProcessId());
        data.setCreateName(response.getCreateName());
        data.setCreateContact(response.getCreateContact());
        data.setCreateTime(response.getCreateTime());
        data.setRiskLevel(response.getRiskLevel());
        data.setOrganizationAccountId(response.getOrganizationAccountId());
        data.setOrganizationAccountGid(response.getOrganizationAccountGid());
        data.setSocialCreditCode(response.getSocialCreditCode());
        data.setLegalPersonName(response.getLegalPersonName());
        data.setCreditCodeType(response.getCreditCodeType());

        return data;
    }

}

package com.timevale.contractmanager.core.service.fulfillment.builder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.fulfillment.ContractFulfillmentRuleDO;
import com.timevale.contractmanager.common.service.bean.rule.condition.TemplateDTO;
import com.timevale.contractmanager.common.service.enums.SourceEnum;
import com.timevale.contractmanager.common.service.enums.autoarchive.OperationConditionStrategyEnum;
import com.timevale.contractmanager.common.service.enums.autoarchive.ProcessStartConditionTypeEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentMiddleTypeEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentNoticeTimeKeyEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentScopeEnum;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentNoticeQueryScriptModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentNoticeRuleModel;
import com.timevale.contractmanager.common.service.model.rule.RuleConditionModel;
import com.timevale.contractmanager.common.service.bean.rule.condition.CategoryDTO;
import com.timevale.contractmanager.common.service.bean.rule.condition.DeptPersonDTO;
import com.timevale.contractmanager.common.service.bean.rule.condition.EnterpriseDTO;
import com.timevale.contractmanager.common.service.bean.rule.condition.SourceDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.other.UserCenterCachedService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同履约数仓查询语句构建
 * <AUTHOR>
 * @since 2025-04-01
 */
@Component
public class ContractFulfillmentRuleQueryScriptBuilder {

    private static final String CONNECT_OR = " or ";
    private static final String CONNECT_AND = " and ";

    @Autowired UserCenterService userCenterService;
    @Autowired UserCenterCachedService userCenterCachedService;

    /**
     * 组装履约通知查询语句
     * @param ruleDO
     * @param scopeConditions
     * @return
     */
    public ContractFulfillmentNoticeQueryScriptModel buildNoticeQueryScript(ContractFulfillmentRuleDO ruleDO, List<RuleConditionModel> scopeConditions) {

        ContractFulfillmentNoticeRuleModel noticeRuleModel = JSON.parseObject(ruleDO.getNoticeRule(), ContractFulfillmentNoticeRuleModel.class);
        // 构建提醒时间查询语句
        String noticeTimeScript = buildNoticeTimeConditionScript(ruleDO.getFormId(), noticeRuleModel);
        // 构建提醒范围查询语句
        String noticeRuleConditionScript = buildNoticeScopeConditionScript(ruleDO.getTenantOid(), ruleDO.getFormId(), ruleDO.getScopeType(), scopeConditions);

        List<String> queryScripts =
                Lists.newArrayList(noticeRuleConditionScript, noticeTimeScript)
                        .stream().filter(i -> StringUtils.isNotBlank(i))
                        .distinct().collect(Collectors.toList());

        ContractFulfillmentNoticeQueryScriptModel queryScriptModel = new ContractFulfillmentNoticeQueryScriptModel();
        queryScriptModel.setQueryCondition(StringUtils.join(queryScripts, CONNECT_AND));
        return queryScriptModel;
    }

    /**
     * 构建提醒范围查询语句
     * @param tenantId
     * @param scopeType
     * @param scopeConditions
     * @return
     */
    private String buildNoticeScopeConditionScript(String tenantId, String formId, String scopeType, List<RuleConditionModel> scopeConditions) {
        List<String> noticeRuleScripts = Lists.newArrayList();
        // 按台账范围
        if (FulfillmentScopeEnum.FORM.getType().equals(scopeType) && StringUtils.isNotBlank(formId)) {
            noticeRuleScripts.add(String.format("form_id = '%s'", formId));
            // 组装查询语句
            return buildQueryScript(noticeRuleScripts, CONNECT_AND);
        }
        // 按自定义范围
        if(FulfillmentScopeEnum.FORCE.getType().equals(scopeType) && CollectionUtils.isNotEmpty(scopeConditions)){
            scopeConditions.forEach(condition -> {
                if (StringUtils.isBlank(condition.getFieldId())) {
                    return;
                }
                OperationConditionStrategyEnum strategyEnum = OperationConditionStrategyEnum.convert(condition.getFieldId());
                if (strategyEnum == null) {
                    return;
                }
                switch (strategyEnum) {
                    case CONTRACT_START_TYPE:
                        condition.getChildOperators().forEach(x -> {
                            List<String> conditionList = JSON.parseArray(x.getConditionParams(), String.class);
                            if (conditionList == null || conditionList.isEmpty()) {
                                return;
                            }
                            String startType = conditionList.get(0);
                            if(ProcessStartConditionTypeEnum.DIRECT_START.getCode().equals(startType)){
                                noticeRuleScripts.add(String.format("flow_template_id is null or flow_template_id = ''"));
                            } else if(ProcessStartConditionTypeEnum.TEMPLATE_START.getCode().equals(startType)){
                                noticeRuleScripts.add(String.format("flow_template_id is not null and flow_template_id != ''"));
                            }
                        });
                        break;
                    case CONTRACT_TITLE:
                        List<String> titles = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            List<String> paramsArray = JSONObject.parseArray(x.getConditionParams(), String.class);
                            titles.addAll(paramsArray);
                        });
                        List<String> titleScripts = Lists.newArrayList();
                        titles.forEach(title -> {
                            titleScripts.add("title like '%" + title + "%'");
                        });
                        noticeRuleScripts.add(StringUtils.join(titleScripts, CONNECT_OR));
                        break;
                    case CONTRACT_TEMPLATE:
                        List<String> flowTemplateIds = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            List<TemplateDTO> paramsArray = JSONObject.parseArray(x.getConditionParams(), TemplateDTO.class);
                            flowTemplateIds.addAll(paramsArray.stream().map(i -> i.getTemplateId()).collect(Collectors.toList()));
                        });
                        noticeRuleScripts.add(String.format("flow_template_id in('%s')", StringUtils.join(flowTemplateIds, "','"), StringUtils.join(flowTemplateIds, "','")));
                        break;
                    case CONTRACT_PROCESS_COMPLETE_TIME:
                        List<String> completeTimes = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            completeTimes.addAll(JSON.parseArray(x.getConditionParams(), String.class));
                        });
                        noticeRuleScripts.add(String.format("complete_time is not null and complete_time between to_date(from_unixtime(CAST(%s/1000 AS BIGINT))) and to_date(from_unixtime(CAST(%s/1000 AS BIGINT)))", completeTimes.get(0), completeTimes.get(1)));
                        break;
                    case CONTRACT_PROCESS_CREATE_TIME:
                        List<String> createTimes = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            createTimes.addAll(JSON.parseArray(x.getConditionParams(), String.class));
                        });
                        noticeRuleScripts.add(String.format("create_time is not null and create_time between to_date(from_unixtime(CAST(%s/1000 AS BIGINT))) and to_date(from_unixtime(CAST(%s/1000 AS BIGINT)))", createTimes.get(0), createTimes.get(1)));
                        break;
                    case CONTRACT_OPPONENT:
                        List<String> oids = Lists.newArrayList();
                        List<String> gids = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            List<EnterpriseDTO> paramsArray = JSONObject.parseArray(x.getConditionParams(), EnterpriseDTO.class);
                            for(EnterpriseDTO enterpriseDTO : paramsArray){
                                oids.add(enterpriseDTO.getTenantOid());
                                if(StringUtils.isNotEmpty(enterpriseDTO.getTenantGid())){
                                    gids.add(enterpriseDTO.getTenantGid());
                                }
                            }
                        });
                        StringBuilder opponentScript = new StringBuilder();
                        opponentScript.append("tenant_oid in('").append(StringUtils.join(oids, "','")).append("')");
                        opponentScript.append(" or tenant_gid in('").append(StringUtils.join(gids, "','")).append("')");
                        opponentScript.append(" or op_tenant_oid in('").append(StringUtils.join(oids, "','")).append("')");
                        opponentScript.append(" or op_tenant_gid in('").append(StringUtils.join(gids, "','")).append("')");
                        noticeRuleScripts.add(opponentScript.toString());
                        break;
                    case CONTRACT_CATEGORY:
                        List<String> categoryIds = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            List<CategoryDTO> paramsArray = JSONObject.parseArray(x.getConditionParams(), CategoryDTO.class);
                            for(CategoryDTO categoryDTO : paramsArray){
                                categoryIds.add(categoryDTO.getId());
                            }
                        });
                        List<String> categoryScripts = Lists.newArrayList();
                        categoryIds.forEach(categoryId -> {
                            categoryScripts.add("category_id like '%" + categoryId + "%'");
                        });
                        noticeRuleScripts.add( StringUtils.join(categoryScripts, CONNECT_OR));

                        break;
                    case CONTRACT_SOURCE:
                        List<Integer> sources = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            List<SourceDTO> paramsArray = JSONObject.parseArray(x.getConditionParams(), SourceDTO.class);
                            for(SourceDTO sourceDTO : paramsArray){
                                int type = SourceEnum.getSource(sourceDTO.getKey());
                                sources.add(type);
                            }
                        });
                        noticeRuleScripts.add(String.format("source in('%s')", StringUtils.join(sources, "','")));
                        break;
                    case CONTRACT_MENU:
                        List<String> menuIdList = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            List<String> paramsArray = JSONObject.parseArray(x.getConditionParams(), String.class);
                            menuIdList.addAll(paramsArray);
                        });
                        List<String> menuScripts = Lists.newArrayList();
                        menuIdList.forEach(menuId -> {
                            menuScripts.add("menu_id like '%" + menuId + "%'");
                        });
                        noticeRuleScripts.add(StringUtils.join(menuScripts, CONNECT_OR));
                        break;
                    case CONTRACT_PROCESS_ACCOUNT_LIST:
                        // 发起人查询条件
                        List<String> initiatorScripts = Lists.newArrayList();
                        // 发起人oid列表
                        List<String> personIds = Lists.newArrayList();
                        condition.getChildOperators().forEach(x -> {
                            List<DeptPersonDTO> deptPersonDTOS = JSON.parseArray(x.getConditionParams(), DeptPersonDTO.class);
                            deptPersonDTOS.forEach(deptPersonDTO -> {
                                if (StringUtils.isNotBlank(deptPersonDTO.getDepartment())) {
                                    initiatorScripts.add("dept_id like '%" + deptPersonDTO.getDepartment() + "%'");
                                    return;
                                }
                                personIds.add(deptPersonDTO.getPerson());
                            });
                        });
                        if (CollectionUtils.isNotEmpty(personIds)) {
                            initiatorScripts.add(String.format("initiator_person_oid in ('%s')", StringUtils.join(personIds, "','")));
                            // 批量查询发起人账号信息
                            Map<String, UserAccountDetail> accountMap = userCenterService.queryFatAccountMapByAccountIds(personIds);
                            // 发起人gid列表
                            List<String> personGids = accountMap.values().stream().map(i -> i.getAccountGid()).filter(i -> StringUtils.isNotBlank(i)).distinct().collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(personGids)) {
                                initiatorScripts.add(String.format("initiator_person_gid in('%s')", StringUtils.join(personGids, "','")));
                            }
                        }
                        String tenantGid = userCenterCachedService.getAccountGid(tenantId);
                        // 发起主体查询条件
                        StringBuilder initiatorTenantScript = new StringBuilder(String.format("initiator_tenant_oid = '%s'", tenantId));
                        if (StringUtils.isNotBlank(tenantGid)) {
                            initiatorTenantScript.append(String.format(" or initiator_tenant_gid = '%s'", tenantGid));
                        }
                        // 合同发起人最终查询条件
                        noticeRuleScripts.add(String.format("(%s) and (%s)", initiatorTenantScript, StringUtils.join(initiatorScripts, CONNECT_OR)));
                        break;
                    default:
                        break;
                }
            });
        }
        // 组装查询语句
        return buildQueryScript(noticeRuleScripts, CONNECT_AND);
    }

    /**
     * 构建提醒时间查询语句
     * @param formId
     * @param noticeRuleModel
     * @return
     */
    private String buildNoticeTimeConditionScript(String formId, ContractFulfillmentNoticeRuleModel noticeRuleModel) {
        List<ContractFulfillmentNoticeRuleModel.NoticeRuleCondition> conditions = noticeRuleModel.getCondition();
        List<String> noticeTimeScripts = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(conditions)) {
            conditions.forEach(condition -> {
                FulfillmentNoticeTimeKeyEnum noticeTimeKey = FulfillmentNoticeTimeKeyEnum.getEnumByKey(condition.getKey());
                FulfillmentMiddleTypeEnum middleType = FulfillmentMiddleTypeEnum.getEnumByKey(condition.getMiddleType());
                switch (noticeTimeKey) {
                    case SIGN_TIME:
                        if (FulfillmentMiddleTypeEnum.BEFORE.equals(middleType)) {
                            noticeTimeScripts.add("complete_time is not null and datediff(to_date(complete_time),to_date(now())) = " + condition.getValue());
                        } else {
                            noticeTimeScripts.add("complete_time is not null and datediff(to_date(now()),to_date(complete_time)) = " + condition.getValue());
                        }
                        break;
                    case CREATE_TIME:
                        if (FulfillmentMiddleTypeEnum.AFTER.equals(middleType)) {
                            noticeTimeScripts.add("create_time is not null and datediff(to_date(now()),to_date(create_time)) = " + condition.getValue());
                        }
                        break;
                    case SIGN_VALIDITY:
                        if (FulfillmentMiddleTypeEnum.BEFORE.equals(middleType)) {
                            noticeTimeScripts.add("sign_validity is not null and datediff(to_date(sign_validity),to_date(now())) = " + condition.getValue());
                        } else {
                            noticeTimeScripts.add("sign_validity is not null and datediff(to_date(now()),to_date(sign_validity)) = " + condition.getValue());
                        }
                        break;
                    case CONTRACT_VALIDITY:
                        if (FulfillmentMiddleTypeEnum.BEFORE.equals(middleType)) {
                            noticeTimeScripts.add("contract_validity is not null and datediff(to_date(contract_validity),to_date(now())) = " + condition.getValue());
                        } else {
                            noticeTimeScripts.add("contract_validity is not null and datediff(to_date(now()),to_date(contract_validity)) = " + condition.getValue());
                        }
                        break;
                    case FORM_TIME:
                        if (FulfillmentMiddleTypeEnum.BEFORE.equals(middleType)) {
                            noticeTimeScripts.add("form_id = '" + formId + "' and field_id = '" + condition.getKey() + "' and datediff(to_date(from_unixtime(CAST(field_value/1000 AS BIGINT))),to_date(now())) = " + condition.getValue());
                        } else {
                            noticeTimeScripts.add("form_id = '" + formId + "' and field_id = '" + condition.getKey() + "' and datediff(to_date(now()),to_date(from_unixtime(CAST(field_value/1000 AS BIGINT)))) = " + condition.getValue());
                        }
                        break;
                    default:
                        break;
                }
            });
        }
        // 组装查询语句
        return buildQueryScript(noticeTimeScripts, CONNECT_OR);
    }

    /**
     * 组装查询语句
     * @param subScripts
     * @param connectSeparator
     * @return
     */
    private String buildQueryScript(List<String> subScripts, String connectSeparator) {
        if (CollectionUtils.isEmpty(subScripts)) {
            return "";
        }
        List<String> queryScripts = subScripts.stream().map(script -> complexConditionScript(script) ? String.format("(%s)", script) : script).collect(Collectors.toList());
        if (queryScripts.size() == 1) {
            return queryScripts.get(0);
        }

        return String.format("(%s)", StringUtils.join(queryScripts, connectSeparator));
    }

    /**
     * 判断是否复杂查询语句， 只要查询语句中包含 and 或 or 语句的都算
     * @param script
     * @return
     */
    private boolean complexConditionScript(String script) {
        return StringUtils.isNotBlank(script) && (script.contains(CONNECT_OR) || script.contains(CONNECT_AND));
    }

}

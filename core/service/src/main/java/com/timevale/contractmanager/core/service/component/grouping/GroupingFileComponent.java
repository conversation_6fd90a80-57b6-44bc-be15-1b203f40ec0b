package com.timevale.contractmanager.core.service.component.grouping;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.GroupingFileInfo;
import com.timevale.contractmanager.core.service.mq.model.ProcessArchiveMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ProcessChangeProducer;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.enums.GroupingTypeEnum;
import com.timevale.signflow.search.service.bean.v2.UpdateProcessGroupingInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程归档组件
 *
 * @author: jinhuan
 * @since: 2020-05-08 17:25
 */
@Slf4j
@Component
public class GroupingFileComponent {

    @Autowired private SaasCommonClient saasCommonClient;
    @Autowired private ProcessChangeProducer processChangeProducer;

    /**
     * 流程批量归档至es,归档人信息有
     *
     * @param menuId 分类id
     * @param tenantId 企业空间id
     * @param fileInfoList 归档流程
     * @param open 是否对外开放
     */
    @Transactional
    public void batchEsArchiveForGroupInfo(
            String menuId, String tenantId, List<GroupingFileInfo.FileInfo> fileInfoList,
            AccountBean accountBeanPerson,Boolean open) {
        List<UpdateProcessGroupingInfo> groupingInfoList = Lists.newArrayList();
        fileInfoList.forEach(x -> {
            groupingInfoList.add(convertFileInfoForGroupInfo(x, accountBeanPerson, open));
        });
        sendArchiveMsg(menuId, tenantId, groupingInfoList, accountBeanPerson, open, ProcessChangeTagEnum.PROCESS_ARCHIVE.getTag());
    }

    /**
     * 流程批量归档至es
     *
     * @param menuId 分类id
     * @param tenantId 企业空间id
     * @param fileInfoList 归档流程
     */
    @Transactional
    public void batchEsArchive(
            String menuId, String tenantId, List<GroupingFileInfo.FileInfo> fileInfoList) {
        List<UpdateProcessGroupingInfo> groupingInfoList = Lists.newArrayList();
        fileInfoList.forEach(x -> {
            groupingInfoList.add(convertFileInfoForGroupInfo(x, null, false));
        });
        sendArchiveMsg(menuId, tenantId, groupingInfoList, null, false, ProcessChangeTagEnum.PROCESS_ARCHIVE.getTag());

    }
    /**
     * 流程批量移动
     *
     * @param fromMenuId 移动分类id
     * @param toMenuId 移入分类id
     * @param fileInfoList 移动流程列表
     * @param tenantId 企业空间id
     */
    @Transactional
    public void moveArchive(
            String fromMenuId,
            String toMenuId,
            List<GroupingFileInfo.FileInfo> fileInfoList,
            String tenantId,AccountBean accountBeanPerson) {
        List<UpdateProcessGroupingInfo> groupingInfoList = Lists.newArrayList();
        fileInfoList.forEach(x -> {
            groupingInfoList.add(convertFileInfoForGroupInfo(x, accountBeanPerson, false));
        });
        //移除消息和添加消息
        sendArchiveMsg(fromMenuId, tenantId, groupingInfoList, accountBeanPerson, false, ProcessChangeTagEnum.PROCESS_ARCHIVE_REMOVE.getTag());
        sendArchiveMsg(toMenuId, tenantId, groupingInfoList, accountBeanPerson, false, ProcessChangeTagEnum.PROCESS_ARCHIVE.getTag());

    }

    /**
     * 流程批量移出
     *
     * @param menuId 分类id
     * @param processList 流程列表
     * @param tenantId 企业空间id
     */
    @Transactional
    public void reMoveArchive(String menuId, List<String> processList, String tenantId) {
        List<UpdateProcessGroupingInfo> groupingInfoList =
                processList.stream().map(this::convertFileInfo).collect(Collectors.toList());
        sendArchiveMsg(menuId, tenantId, groupingInfoList, null, false, ProcessChangeTagEnum.PROCESS_ARCHIVE_REMOVE.getTag());
    }

    /**
     * 判断在该企业下是否有对这些流程归档的权限
     *
     * @param tenant 企业主体
     * @param processIds 流程id列表
     * @return 是否有权限 true-有 false-无
     */
    public boolean checkGroupingPermission(UserAccount tenant, List<String> processIds) {
        // 批量归档校验会员版本
        checkBatchLimit(tenant.getAccountOid(), processIds.size());
        return true;
    }

    /**
     * 校验批量归档数量限制
     *
     * @param tenantId 企业oid
     * @param batchSize 批量操作数量
     */
    public void checkBatchLimit(String tenantId, int batchSize) {
        saasCommonClient.checkFunctionBatchLimit(
                tenantId,
                FunctionCodeConstant.BATCH_ARCHIVE,
                batchSize,
                RequestContextExtUtils.getClientId());
    }

    /**
     * 类型转换有归档人信息
     *
     * @param fileInfo FileInfo
     * @return UpdateProcessGroupingInfo
     */
    private UpdateProcessGroupingInfo convertFileInfoForGroupInfo(GroupingFileInfo.FileInfo fileInfo,AccountBean accountBeanPerson,Boolean open) {
        UpdateProcessGroupingInfo groupingInfo = new UpdateProcessGroupingInfo();
        groupingInfo.setContractNo(fileInfo.getContractNo());
        groupingInfo.setProcessId(fileInfo.getProcessId());
        groupingInfo.setArchiveTime(System.currentTimeMillis());
        if(open){
            groupingInfo.setArchiveType(String.valueOf(GroupingTypeEnum.API_GROUPING.getType()));
        }else {
            groupingInfo.setArchiveType(String.valueOf(GroupingTypeEnum.PERSON_GROUPING.getType()));
        }
        if(accountBeanPerson == null){
            accountBeanPerson = new AccountBean();
            accountBeanPerson.setName("系统自动归档");
            groupingInfo.setArchiveType(String.valueOf(GroupingTypeEnum.SYSTEM_GROUPING.getType()));
        }
        Account account = new Account();
        BeanUtils.copyProperties(accountBeanPerson,account);
        groupingInfo.setArchiverPerson(account);
        return groupingInfo;
    }
    /**
     * 类型转换
     *
     * @param processId 流程id
     * @return UpdateProcessGroupingInfo
     */
    private UpdateProcessGroupingInfo convertFileInfo(String processId) {
        UpdateProcessGroupingInfo groupingInfo = new UpdateProcessGroupingInfo();
        groupingInfo.setProcessId(processId);
        return groupingInfo;
    }

    /**
     * 发送归档消息
     *
     * @param menuId
     * @param tenantId
     * @param groupingInfoList
     * @param accountBeanPerson
     * @param open
     * @param tag
     */
    private void sendArchiveMsg(String menuId, String tenantId, List<UpdateProcessGroupingInfo> groupingInfoList, AccountBean accountBeanPerson, Boolean open, String tag) {
        log.info("sendArchiveMsg menuId :{}, tenantId:{} processId:{}", menuId, tenantId, JsonUtils.obj2json(groupingInfoList));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                for (UpdateProcessGroupingInfo groupingInfo : groupingInfoList) {
                    ProcessArchiveMsgEntity processArchiveMsgEntity = new ProcessArchiveMsgEntity();
                    processArchiveMsgEntity.setProcessId(groupingInfo.getProcessId());
                    processArchiveMsgEntity.setMenuId(menuId);
                    processArchiveMsgEntity.setTenantId(tenantId);

                    processArchiveMsgEntity.setArchiveTime(groupingInfo.getArchiveTime());
                    processArchiveMsgEntity.setArchiverPerson(groupingInfo.getArchiverPerson());
                    processArchiveMsgEntity.setContractNo(groupingInfo.getContractNo());

                    if (open) {
                        processArchiveMsgEntity.setArchiveType(GroupingTypeEnum.API_GROUPING.getType());
                    } else {
                        processArchiveMsgEntity.setArchiveType(GroupingTypeEnum.PERSON_GROUPING.getType());
                    }
                    if (accountBeanPerson == null) {
                        processArchiveMsgEntity.setArchiveType(GroupingTypeEnum.SYSTEM_GROUPING.getType());
                    }
                    processChangeProducer.sendManageShardingMessage(
                            JsonUtils.obj2json(processArchiveMsgEntity), tag, groupingInfo.getProcessId());
                }
            }
        });
    }
}

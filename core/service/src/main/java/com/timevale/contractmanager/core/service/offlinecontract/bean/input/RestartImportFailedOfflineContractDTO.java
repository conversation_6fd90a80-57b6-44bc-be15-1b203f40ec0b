package com.timevale.contractmanager.core.service.offlinecontract.bean.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 导入失败的线下合同批量重新导入
 *
 * <AUTHOR>
 * @since 2023-08-27
 */
@Data
public class RestartImportFailedOfflineContractDTO extends ToString {

    @NotBlank(message = "主体gid不能为空")
    private String subjectGid;

    @NotBlank(message = "导入记录id不能为空")
    private String recordId;
}

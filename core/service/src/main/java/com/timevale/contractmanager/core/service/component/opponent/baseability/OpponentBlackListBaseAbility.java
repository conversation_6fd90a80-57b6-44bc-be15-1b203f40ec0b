package com.timevale.contractmanager.core.service.component.opponent.baseability;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.core.service.dto.opponent.OpponentAccountInfoBO;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.service.bean.v2.ProcessAccountInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 黑名单查询基础类
 * @Author:jianyang
 * @since 2021-06-03 11:36
 */
@Slf4j
@Component
public class OpponentBlackListBaseAbility {

	private static final Integer BATCH_QUERY_COUNT = 50;


	@Autowired
	private OpponentEntityDAO opponentEntityDAO;

	/**
	 * 通过租户tenantGid返回黑名单(不区分是发起方、参与方、抄送方)
	 * @param tenantGid
	 * @param entityOid
	 * @param entityGid
	 * @return
	 */
	public List<OpponentEntityDO> getBlackListInfoByGid(String tenantGid,List<String> entityOid, List<String> entityGid){
		List<OpponentEntityDO> result = new ArrayList<>();
		if(entityOid != null && !entityOid.isEmpty()){
			result.addAll(opponentEntityDAO.getTenantBlackListByEntityOids(tenantGid,entityOid));
		}
		if(entityGid != null && !entityGid.isEmpty()){
			result.addAll(this.listBatchTenantBlackListByGid(tenantGid,entityGid));
		}
		return result;
	}
	/**
	 * 获取进行黑名单查询的oid，gid信息(不区分是发起方、参与方、抄送方,未过滤当前主体)
	 * @param processAccountInfos
	 * @return
	 */
	public OpponentAccountInfoBO batchConvertToAccountInfos(List<ProcessAccountInfo> processAccountInfos){
		OpponentAccountInfoBO opponentAccountInfoBO = new OpponentAccountInfoBO();
		List<String> entityOids = new ArrayList<>();
		List<String> entityGids = new ArrayList<>();
		for (ProcessAccountInfo processAccountInfo : processAccountInfos){
			/**获取参与方中的个人和企业oid或gid(gid优先),以participant为主,participant为null,才从taskInfo获取**/
			if(!Objects.isNull(processAccountInfo.getParticipant()) && ! processAccountInfo.getParticipant().isEmpty()){
				processAccountInfo.getParticipant().stream()
						.forEach(x ->{
							if(Objects.isNull(x.getPerson().getGid())){
								entityOids.add(x.getPerson().getOid());
							}else {
								entityGids.add(x.getPerson().getGid());
							}
							if(x.getSubject().getOrgan()){
								if(Objects.isNull(x.getSubject().getGid())){
									entityOids.add(x.getSubject().getOid());
								}else {
									entityGids.add(x.getSubject().getGid());
								}
							}
						});
			}else {
				processAccountInfo.getTaskInfo().stream()
						.forEach(x ->{
							if(Objects.isNull(x.getExecute().getPerson().getGid())){
								entityOids.add(x.getExecute().getPerson().getOid());
							}else {
								entityGids.add(x.getExecute().getPerson().getGid());
							}
							if(x.getExecute().getSubject().getOrgan()){
								if(Objects.isNull(x.getExecute().getSubject().getGid())){
									entityOids.add(x.getExecute().getSubject().getOid());
								}else {
									entityGids.add(x.getExecute().getSubject().getGid());
								}
							}
						});
			}


			/***获取抄送方中的个人和企业oid或gid(gid优先),以participant为主,participant为null,才从taskInfo获取***/
			if(!Objects.isNull(processAccountInfo.getCc()) && !processAccountInfo.getCc().isEmpty()){
				processAccountInfo.getCc().stream()
						.forEach(x ->{
							if(Objects.isNull(x.getPerson().getGid())){
								entityOids.add(x.getPerson().getOid());
							}else {
								entityGids.add(x.getPerson().getGid());
							}
							if(x.getSubject().getOrgan()){
								if(Objects.isNull(x.getSubject().getGid())){
									entityOids.add(x.getSubject().getOid());
								}else {
									entityGids.add(x.getSubject().getGid());
								}
							}
						});
			}

			ProcessAccount initiator = processAccountInfo.getInitiator();
			if(Objects.isNull(initiator.getPerson().getGid())){
				entityOids.add(initiator.getPerson().getOid());
			}else {
				entityGids.add(initiator.getPerson().getGid());
			}
			if(initiator.getSubject().getOrgan()){
				if(Objects.isNull(initiator.getSubject().getGid())){
					entityOids.add(initiator.getSubject().getOid());
				}else {
					entityGids.add(initiator.getSubject().getGid());
				}
			}
		}
		entityOids.stream().distinct().collect(Collectors.toList());
		entityGids.stream().distinct().collect(Collectors.toList());
		opponentAccountInfoBO.setEntityOid(entityOids);
		opponentAccountInfoBO.setEntityGid(entityGids);
		return opponentAccountInfoBO;
	}

	public List<OpponentEntityDO> listBatchTenantBlackListByGid(String tenantGid,
																List<String> entityGids) {
		if (StringUtils.isBlank(tenantGid) || CollectionUtils.isEmpty(entityGids)) {
			return new ArrayList<>();
		}
		if (entityGids.size() > BATCH_QUERY_COUNT) {
			log.info("listTenantBlackListByGid size large");
		}
		List<String> finalEntityGids = entityGids.stream().distinct().collect(Collectors.toList());
		return Lists.partition(finalEntityGids, BATCH_QUERY_COUNT)
				.stream()
				.map(batchQueryIds -> opponentEntityDAO.getTenantBlackListByGid(tenantGid, batchQueryIds))
				.flatMap(List::stream)
				.collect(Collectors.toList());
	}

	public List<OpponentEntityDO> listBatchTenantBlackListByEntityUniqueIds(String tenantGid,
																	  List<String> entityUniqueIds,
																	  Integer entityType) {
		if (StringUtils.isBlank(tenantGid) || CollectionUtils.isEmpty(entityUniqueIds)) {
			return new ArrayList<>();
		}
		List<String> finalEntityUniqueIds = entityUniqueIds.stream().distinct().collect(Collectors.toList());
		return Lists.partition(finalEntityUniqueIds, BATCH_QUERY_COUNT)
				.stream()
				.map(batchQueryIds -> opponentEntityDAO.getTenantBlackListByEntityUniqueIds(tenantGid, batchQueryIds, entityType))
				.flatMap(List::stream)
				.collect(Collectors.toList());
	}
}

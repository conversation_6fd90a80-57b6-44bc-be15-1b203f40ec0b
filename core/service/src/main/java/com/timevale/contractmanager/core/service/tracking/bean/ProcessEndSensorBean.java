package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;

/**
 * <AUTHOR>
 * @since 2021-03-25
 */
@Data
public class ProcessEndSensorBean extends ToString {

    /** 流程id */
    private String processId;

    /** 合同审批流程id */
    private String contractApprovalId;

    /** 填写流程id */
    private String fillingId;

    /** 签署流程id */
    private String flowId;

    /** 合同流程状态 */
    private String processIdStatus;

    private String appId;
    /**
     * 合同签署耗时
     */
    private Long duration;

    /** 是否设置截止签署时间 */
    private boolean signdatelimit;

    /** 操作人oid */
    private String operatorOid;

    /** 操作人gid */
    private String operatorGid;

    /** 操作主体oid */
    private String authorizedOid;

    /** 操作主体gid */
    private String authorizedGid;


    public Map<String, Object> sensorData() {
        Map<String, Object> sensorData = Maps.newHashMap();
        sensorData.put(PROCESS_ID,  sensorString(processId));
        sensorData.put(PROCESS_ID_STATUS, sensorString(processIdStatus));
        sensorData.put(CONTRACT_APPROVAL_ID, sensorString(contractApprovalId));
        sensorData.put(FILLING_ID, sensorString(fillingId));
        sensorData.put(FLOW_ID, sensorString(flowId));
        sensorData.put(APP_ID,sensorString(appId));
        sensorData.put(SIGN_DATE_LIMIT,sensorString(signdatelimit));
        sensorData.put(DURATION,sensorString(duration));
        sensorData.put(OPERATOR_OID,sensorString(operatorOid));
        sensorData.put(OPERATOR_GID,sensorString(operatorGid));
        sensorData.put(AUTHORIZED_OID,sensorString(authorizedOid));
        sensorData.put(AUTHORIZED_GID,sensorString(authorizedGid));
        return sensorData;
    }
}

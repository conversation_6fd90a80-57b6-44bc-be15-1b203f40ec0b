package com.timevale.contractmanager.core.service.contractprocess;

/**
 * Created by t<PERSON><PERSON><PERSON> on 2022/6/9
 */
public class ProcessDataCollectBizSceneConstants {

    public static final String DATA_INIT = "dataInit";

    // 填写
    public static final String COOPERATION_TASK_CHANGE = "cooperationTaskChange";
    public static final String COOPERATION_START = "cooperationStart";

    // 签署
    public static final String SIGN_START = "signStart";
    public static final String SIGN_TASK_CHANGE = "signTaskChange";
    public static final String SIGN_APPEND_SIGN = "signAppendSign";
    public static final String SIGN_DELETE_SIGN = "signDeleteSign";
    public static final String SIGN_ATTACHMENT_CHANGE = "signAttachmentChange";
    public static final String SIGN_CONTRACT_APPROVAL_AGREE = "signContractApprovalAgree";

    // 变更
    public static final String PROCESS_STATUS_CHANGE = "processStatusChange";
    public static final String PROCESS_START = "processStart";

    public static final String PROCESS_TRANSFER = "processTransfer";
    public static final String PROCESS_INFO_CHANGE = "processInfoChange";
    public static final String PROCESS_CUSTOMIZE_CONFIG_CHANGE = "processCCC";
    public static final String PROCESS_HIDDEN = "processHiddenScene";
    public static final String PROCESS_AI_TEMPLATE = "aiTemplate";
    public static final String PROCESS_ARCHIVE = "processArchive";
    public static final String PROCESS_BELONG = "belong";


    public static final String PROCESS_ARCHIVE_REMOVE = "processArchiveRemove";

    public static final String PROCESS_EXTRACT_DATA = "processExtract";

    public static final String PROCESS_EXTRACT_DATA_REMOVE = "processExtractRemove";

    public static final String PROCESS_INFO_FIX = "processInfoFix";

    public static final String FIX_SEAL_INFO = "fixSealInfo";

    public static final String TEMP_GROUP = "tempGroupBuild";

    public static final String PROCESS_APPROVAL_CHANGE = "processApprovalChange";

    public static final String SEAL_APPROVAL_CHANGE = "sealApprovalChange";

}

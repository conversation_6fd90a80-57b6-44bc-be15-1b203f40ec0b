package com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.impl;

import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionProblemEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentSimpleInfoListBO;
import com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.DetectionHandler;
import com.timevale.contractmanager.core.service.opponent.EnterpriseInformationService;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-12-29 14:43
 */
@Component
@Slf4j
public class SocialCreditCodeDetectionHandler implements DetectionHandler {

	@Autowired
	private SensorService sensorService;
	
	@Autowired
	private EnterpriseInformationService enterpriseInformationService;
	
	public static final Integer DETECTION_NO = 6;
	public static final String DESC = "最新的工商信息显示名称为:%s，而在e签宝登记的为:%s";

	@Override
	public List<OpponentDetectionChainResultBO> handler(DetectionChainBO chainBO, List<OpponentDetectionChainResultBO> chainResults) {
		log.info("开始相对方检测(名称一致性检测) taskId:{} taskType:{} tenantGid:{} orgName:{}",
				chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
		OpponentSimpleInfoListBO result = enterpriseInformationService.enterpriseSimpleInfo(chainBO.getSocialCreditCode());
		if(CollectionUtils.isNotEmpty(result.getItems()) && StringUtils.isNotBlank(result.getItems().get(0).getName())
				&& StringUtils.isNotBlank(chainBO.getSocialCreditCode()) && !chainBO.getSocialCreditCode().equals("-")
				&& !chainBO.getOrgName().equals(result.getItems().get(0).getName())){

			OpponentDetectionChainResultBO chainResultBO = new OpponentDetectionChainResultBO();
			chainResultBO.setOrgName(chainResultBO.getOrgName());
			String orgName = String.format("%s%s%s","“",chainBO.getOrgName(),"”");
			String name = String.format("%s%s%s","“",result.getItems().get(0).getName(),"”");
			chainResultBO.setProblemDesc(String.format(DESC, orgName, name));
			chainResultBO.setRiskLevel(OpponentDetectionProblemEnum.ORG_NAME_DISCORDANCE.getProblemRiskLevel());
			chainResultBO.setProblemNo(OpponentDetectionProblemEnum.ORG_NAME_DISCORDANCE.getProblemNo());
			chainResultBO.setProblemNo(DETECTION_NO);
			chainResultBO.setSuggestDesc(OpponentDetectionProblemEnum.ORG_NAME_DISCORDANCE.getSuggestDesc());
			chainResults.add(chainResultBO);
			log.info("相对方检测企业工商名称与e签宝不一致 taskId:{} taskType:{} tenantGid:{} orgName:{}",
					chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
			sensorService.opponentDetectionProblemReport(
					chainBO.getTenantGid(), chainBO.getOrgName(),
					OpponentDetectionProblemEnum.ORG_NAME_DISCORDANCE.getProblemNo());
		}
		return chainResults;
	}

	@Override
	public boolean filter(List<Integer> items) {
		if(items.contains(DETECTION_NO)){
			return true;
		}
		return false;
	}
}

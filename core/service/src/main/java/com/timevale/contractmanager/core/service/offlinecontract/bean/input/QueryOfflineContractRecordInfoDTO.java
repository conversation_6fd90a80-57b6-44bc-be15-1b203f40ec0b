package com.timevale.contractmanager.core.service.offlinecontract.bean.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询线下合同导入记录基本信息
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class QueryOfflineContractRecordInfoDTO extends ToString {

    @NotBlank(message = "主体gid不能为空")
    private String subjectGid;

    @NotBlank(message = "导入记录id不能为空")
    private String recordId;

    /** 是否返回菜单路径 */
    private boolean withMenuPath;
}

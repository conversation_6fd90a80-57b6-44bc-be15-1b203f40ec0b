package com.timevale.contractmanager.core.service.tracking.service.base;

import static com.timevale.saas.tracking.bean.TrackingCollectBean.trackingString;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.timevale.cat.toolkit.datacollect.DataCollector;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.tracking.enums.TrackingEventEnum;
import com.timevale.contractmanager.core.service.tracking.enums.TrackingFieldEnum;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.saas.tracking.bean.TrackingCollectBean;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023-12-13 18:11
 */
@Slf4j
@Data
public abstract class BaseTrackingService {
    
    @Autowired private UserCenterService userCenterService;

    /**
     * 主体类型，区分是企业用户还是个人用户
     */
    private String entityType;
    /**
     * 主体名称，如果是企业记录企业名称，如果是个人记录个人姓名
     */
    private String entityName;
    /**
     * 操作主体oid
     */
    private String authorizedOid;
    /**
     * 操作主体gid
     */
    private String authorizedGid;
    /**
     * 操作人oid
     */
    private String operatorOid;
    /**
     * 操作人gid
     */
    private String operatorGid;
    /**
     * SaaS用户角色, 主体为企业时，记录操作人在该改企业的角色，不同角色之间逗号隔开，如果是个人则记录为“个人”
     */
    private String userRole;
    /**
     * 是否首次触发事件
     */
    private Boolean isFirstTime;
    /**
     * 操作应用id
     */
    private String appId;
    /**
     * 产品端
     */
    private String platformType;
    /**
     * 功能模块
     */
    private String module;
    /**
     * 会员版本
     */
    private String vipVersion;
    /**
     * 操作人归属主体数量
     */
    private Integer operatorEntityNum;

    /**
     * 埋点distinctId
     */
    public abstract String distinctId();

    /**
     * 埋点字段模板列表
     */
    public abstract List<TrackingFieldEnum> trackingFields();

    /**
     * 组装埋点数据
     *
     * @param trackingBean
     * @return
     */
    public abstract Map<String, Object> buildTrackingFieldData(TrackingCollectBean trackingBean);

    /**
     * 触发埋点
     *
     * @param trackingBean
     */
    public void tracking(TrackingCollectBean trackingBean) {
        // 初始化埋点基本数据
        initBaseTrackingData(trackingBean);
        // 获取埋点事件
        TrackingEventEnum trackingEvent = TrackingEventEnum.valueOfKey(trackingBean.getTrackingKey());
        // 组装埋点属性数据
        Map<String, Object> trackingFieldData = buildTrackingFieldData(trackingBean);
        // 埋点
        doTracking(distinctId(), trackingEvent.getEvent(), trackingFieldData);
    }

    /**
     * 初始化埋点基本信息
     *
     * @param trackingBean
     */
    protected void initBaseTrackingData(TrackingCollectBean trackingBean) {
        setPlatformType(trackingBean.getPlatform());
        setModule(trackingBean.getModule());
        setAppId(trackingBean.getAppId());
        setIsFirstTime(true);

        String tenantOId = trackingBean.getTenantId();
        if (StringUtils.isNotBlank(tenantOId)) {
            AccountBean tenant = userCenterService.getAccountBeanByOid(tenantOId);
            setEntityType(BooleanUtils.isTrue(tenant.getOrgan()) ? "企业" : "个人");
            setEntityName(tenant.getName());
            setAuthorizedGid(tenant.getGid());
            setAuthorizedOid(tenant.getOid());
        }
        String operatorId = trackingBean.getOperatorId();
        if (StringUtils.equals(operatorId, tenantOId)) {
            setOperatorOid(getAuthorizedOid());
            setOperatorGid(getAuthorizedGid());
        } else if (StringUtils.isNotBlank(operatorId)) {
            UserAccount operator = userCenterService.getUserAccountBaseByOid(operatorId);
            setOperatorOid(operator.getAccountOid());
            setOperatorGid(operator.getAccountGid());
        }
    }

    /**
     * 组装埋点通用属性
     *
     * @return
     */
    protected Map<String, Object> trackingFieldData() {
        Map<String, Object> trackingFieldData = Maps.newHashMap();
        trackingFieldData.put(TrackingFieldEnum.ENTITY_TYPE.getKey(), trackingString(entityType));
        trackingFieldData.put(TrackingFieldEnum.ENTITY_NAME.getKey(), trackingString(entityName));
        trackingFieldData.put(TrackingFieldEnum.AUTHORIZED_OID.getKey(), trackingString(authorizedOid));
        trackingFieldData.put(TrackingFieldEnum.AUTHORIZED_GID.getKey(), trackingString(authorizedGid));
        trackingFieldData.put(TrackingFieldEnum.OPERATOR_OID.getKey(), trackingString(operatorOid));
        trackingFieldData.put(TrackingFieldEnum.OPERATOR_GID.getKey(), trackingString(operatorGid));
        trackingFieldData.put(TrackingFieldEnum.USER_ROLE.getKey(), trackingString(userRole));
        trackingFieldData.put(TrackingFieldEnum.IS_FIRST_TIME.getKey(), trackingString(isFirstTime));
        trackingFieldData.put(TrackingFieldEnum.PLATFORM_TYPE.getKey(), trackingString(platformType));
        trackingFieldData.put(TrackingFieldEnum.MODULE.getKey(), trackingString(module));
        trackingFieldData.put(TrackingFieldEnum.VIP_VERSION.getKey(), trackingString(vipVersion));
        trackingFieldData.put(TrackingFieldEnum.OPERATOR_ENTITY_NUM.getKey(), trackingString(operatorEntityNum));
        // 设置事件埋点模版字段默认值
        List<TrackingFieldEnum> trackingFields = trackingFields();
        if (trackingFields != null) {
            for (TrackingFieldEnum filed : trackingFields) {
                trackingFieldData.put(filed.getKey(), "");
            }
        }
        return trackingFieldData;
    }

    /**
     * 触发埋点
     *
     * @param distinctId
     * @param event
     * @param data
     */
    protected void doTracking(String distinctId, String event, Map<String, Object> data) {
        boolean isLoginId = StringUtils.isNotEmpty(distinctId);
        if (!isLoginId) {
            distinctId = UUID.randomUUID().toString();
        }
        log.info("埋点数据，distinctId:{}, event:{}, data:{}", distinctId, event, JSONObject.toJSONString(data));
        DataCollector.sensorsCollect(distinctId, isLoginId, event, data);
    }
}

package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.service.enums.ProcessSecretEnum;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;

/**
 * <AUTHOR>
 * @since 2021-03-25
 */
@Data
public class ProcessStartSensorBean extends ToString {

    /**
     * 产品端
     *
     * <p>微信小程序：WE_CHAT
     *
     * <p>支付宝小程序：ALI_PAY_MINI，
     *
     * <p>安卓app：APP_ANDROID
     *
     * <p>ios app：APP_IOS，
     *
     * <p>H5：H5
     *
     * <p>WEB：WEB
     *
     * <p>钉钉微应用：DING_TALK
     *
     * <p>开放服务：OPEN_SERVICE
     */
    private String platformType;

    /** 操作人oid */
    private String oid;

    /** 操作人oid (新增)*/
    private String operatorOid;

    /** 操作人gid */
    private String operatorGid;

    /** 操作主体oid */
    private String authorizedOid;

    /** 操作主体gid */
    private String authorizedGid;

    /** 主体名称 */
    private String entityName;
    /** 会员版本 */
    private Integer vipVersion;

    /** 流程id */
    private String processId;

    /** 流程类型，普通流程、复制重发流程、解约流程 */
    private String processType;

    /** 应用id */
    private String appid;

    /** 流程发起方式,直接发起、使用模板发起 */
    private String processStartType;

    /** 模板是否是外部授权共享给子企业的模板,模板发起有效 */
    private boolean templateOutside;

    /** 是否非标模板 */
    private boolean unStandardTemplate;

    /** 是否替换过文件 */
    private boolean replaceFiles;

    /** 是否添加了文件 */
    private boolean addFiles;

    /** 签署文件数 */
    private int fileamount;

    /** 附属材料数 */
    private int attachmentAmount;

    /** 填写方数量 */
    private int fillAmount;

    /** 个人主体签署方数量 */
    private int personSignatoryAmount;

    /** 企业主体签署方数量 */
    private int orgSignatoryAmount;

    /** 当前空间主体是否是签署方 */
    private boolean authorizedOidSign;
    /** 抄送方数量 */
    private int ccAmount;

    /** 是否设置签署截止日期 */
    private boolean signdatelimit;

    /** 是否设置合同到期日期 */
    private boolean contractdatelimit;

    /** 是否指定了顺序 */
    private boolean ordersign;
    /** 指定模板章数 */
    private int templateSealAmount;

    /** 指定AI手绘章数 */
    private int handdrawAmount;

    /** 指定普通手绘章数 */
    private int aiHanddrawAmount;

    /** 流程发起模式, 单任务普通发起、单任务扫码发起、批量普通发起、批量扫码签发起 */
    private String processStartWay;

    //设置指定印章类型的签署方数量
    private int numberOfFixedSealType = 0;

    //设置指定印章的签署方数量
    private int numberFixedSeal = 0;

    //设置最低阅读时长的签署方数量
    private int numberOfZdydsc = 0;

    //设置必须阅读到底的签署方数量
    private int numberOfBxyddd = 0;

    //设置添加了签署声明
    private int numberOfSignStatement = 0;

    //发起使用的水印类型（wenjian 或 pingmu）; 未使用为空字符串
    private String watermarkType = "";

    // 指定附件参与方个数
    private int numberOfAttachments = 0;

    // 指定附件核验参与方个数
    private int numberOfAttachmentsVerification = 0;

    // 是否使用动态模板
    private boolean useDynamicTemplate = false;

    // 意愿-人脸参与方个数
    private int numberOfWillingnessFace = 0;
    // 意愿-短信参与方个数
    private int numberOfWillingnessMessage = 0;
    // 意愿-邮箱参与方个数
    private int numberOfWillingnessEMail = 0;
    // 意愿-密码参与方个数
    private int numberOfWillingnessPassword = 0;

    /**
     * 流程保密类型
     *
     * @see ProcessSecretEnum
     */
    private Integer processSecretType;

    /** 钉钉组织id */
    private String corpId;

    /** 钉钉组织名称 */
    private String corpName;

    /** 钉钉主企业名称 */
    private String mainEnterpriseName;
    
    /** 姓名+身份证发起 */
    private String psnIdCardInitiation;
    /** 姓名+手机号发起 */
    public String psnMobileInitiation;
    /** 姓名+手机号+身份证发起 */
    public String psnCompleteInitiation;
    /** 指定企业经办人发起 */
    public String orgOperatorInitiation;
    /** 指定企业名称发起 */
    public String orgNameInitiation;
    /** 指定企业内部角色发起 */
    public String orgRoleInitiation;

    public Map<String, Object> sensorData() {
        Map<String, Object> sensorData = Maps.newHashMap();
        sensorData.put(PLATFORM_TYPE,  sensorString(platformType, ClientEnum.OPEN_SERVICE.getClientId()));
        sensorData.put(OID, oid);
        sensorData.put(ENTITY_NAME, entityName);
        sensorData.put(AUTHORIZED_OID, authorizedOid);
        sensorData.put(VIP_VERSION, sensorString(vipVersion,"1" ));
        sensorData.put(PROCESS_ID, processId);
        sensorData.put(PROCESS_TYPE, processType);
        sensorData.put(APP_ID, appid);
        sensorData.put(PROCESS_START_TYPE, processStartType);
        sensorData.put(FILE_AMOUNT, fileamount);
        sensorData.put(ATTACHMENT_AMOUNT, attachmentAmount);
        sensorData.put(FILL_AMOUNT, fillAmount);
        sensorData.put(PERSON_SIGNATORY_AMOUNT, personSignatoryAmount);
        sensorData.put(ORG_SIGNATORY_AMOUNT, orgSignatoryAmount);
        sensorData.put(AUTHORIZED_OID_SIGN, authorizedOidSign);
        sensorData.put(CC_AMOUNT, ccAmount);
        sensorData.put(SIGN_DATE_LIMIT, signdatelimit);
        sensorData.put(CONTRACT_DATE_LIMIT, contractdatelimit);
        sensorData.put(ORDER_SIGN, ordersign);
        sensorData.put(TEMPLATE_SEAL_AMOUNT, templateSealAmount);
        sensorData.put(HAND_DRAW_AMOUNT, handdrawAmount);
        sensorData.put(AI_HAND_DRAW_AMOUNT, aiHanddrawAmount);
        sensorData.put(PROCESS_START_WAY, processStartWay);
        sensorData.put(OPERATOR_OID,operatorOid);
        sensorData.put(OPERATOR_GID,operatorGid);
        sensorData.put(AUTHORIZED_GID,authorizedGid);
        sensorData.put(TEMPLATE_OUTSIDE,templateOutside);
        sensorData.put(IS_UN_STANDARD_TEMPLATE,unStandardTemplate);
        sensorData.put(IS_REPLACE_FILES,replaceFiles);
        sensorData.put(IS_ADD_FILES,addFiles);
        sensorData.put(PROCESS_IS_SECRECY, ProcessSecretEnum.isSecret(processSecretType));
        sensorData.put(NUMBER_OF_FIXED_SEAL_TYPE, numberOfFixedSealType);
        sensorData.put(NUMBER_FIXED_SEAL, numberFixedSeal);
        sensorData.put(NUMBER_OF_ZDYDSC, numberOfZdydsc);
        sensorData.put(NUMBER_OF_BXYDDD, numberOfBxyddd);
        sensorData.put(CONTRACT_WATERMARK_TYPE, watermarkType);
        sensorData.put(NUMBER_OF_SIGN_STATEMENT, numberOfSignStatement);
        sensorData.put(DESIGNATED_ATTACHMENTS_NUMBER, numberOfAttachments);
        sensorData.put(DESIGNATED_ATTACHMENTS_VERIFICATION_NUMBER, numberOfAttachmentsVerification);
        sensorData.put(USE_DYNAMIC_HTML_TEMPLATE, useDynamicTemplate);
        sensorData.put(WILLINGNESS_FACE, numberOfWillingnessFace);
        sensorData.put(WILLINGNESS_MESSAGE, numberOfWillingnessMessage);
        sensorData.put(WILLINGNESS_EMAIL, numberOfWillingnessEMail);
        sensorData.put(WILLINGNESS_PASSWORD, numberOfWillingnessPassword);
        sensorData.put(CORP_ID, sensorString(corpId));
        sensorData.put(CORP_NAME, sensorString(corpName));
        sensorData.put(MAIN_ENTERPRISE, sensorString(mainEnterpriseName));
        sensorData.put(PSN_IDCARD_INITIATION,sensorInt(psnIdCardInitiation));
        sensorData.put(PSN_MOBILE_INITIATION,sensorInt(psnMobileInitiation));
        sensorData.put(PSN_COMPLETE_INITIATION,sensorInt(psnCompleteInitiation));
        sensorData.put(ORG_OPERATOR_INITIATION,sensorInt(orgOperatorInitiation));
        sensorData.put(ORG_NAME_INITIATION,sensorInt(orgNameInitiation));
        sensorData.put(ORG_ROLE_INITIATION,sensorInt(orgRoleInitiation));
        return sensorData;
    }
}

package com.timevale.contractmanager.core.service.contractapproval.result;

import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalNode;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 预发起合同审批响应数据
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
public class VirtualStartApprovalFlowResult extends ToString {

    /** 抄送节点 */
    private List<ApprovalNode> ccNodes;

    /** 全部审批节点 */
    private List<ApprovalNode> taskNodes;
}

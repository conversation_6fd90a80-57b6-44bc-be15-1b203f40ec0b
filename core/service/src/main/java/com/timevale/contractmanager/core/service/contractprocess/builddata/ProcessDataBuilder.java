package com.timevale.contractmanager.core.service.contractprocess.builddata;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.SUB_PROCESS_NOT_EXIST;
import static com.timevale.contractmanager.core.service.process.converter.AccountBeanConverter.convertToAccountBean;
import static com.timevale.signflow.search.docSearchService.enums.AccountTypeEnum.*;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.timevale.account.flow.service.enums.ApprovalStatusEnum;
import com.timevale.account.flow.service.model.approval.model.ApprovalGroupModel;
import com.timevale.account.flow.service.model.approval.model.ApprovalInfoModel;
import com.timevale.account.flow.service.model.approval.output.ApprovalInfoOutput;
import com.timevale.account.flow.service.model.approval.output.task.QueryApprovalTaskOutput;
import com.timevale.contractapproval.facade.dto.ApprovalAccountDTO;
import com.timevale.contractapproval.facade.dto.ApprovalGroupDTO;
import com.timevale.contractapproval.facade.dto.ApprovalSyncDataDetailDTO;
import com.timevale.contractapproval.facade.dto.ApprovalSyncDataTaskDTO;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateNodeTypeEnum;
import com.timevale.contractmanager.common.dal.bean.*;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignTaskDO;
import com.timevale.contractmanager.common.dal.dao.ContractProcessGroupDAO;
import com.timevale.contractmanager.common.dal.dao.SubProcessDAO;
import com.timevale.contractmanager.common.dal.dao.sharesign.ShareSignTaskDAO;
import com.timevale.contractmanager.common.service.api.ContractProcessGroupService;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.bean.ContractProcessGroupBean;
import com.timevale.contractmanager.common.service.bean.ProcessCustomizeConfigBean;
import com.timevale.contractmanager.common.service.bean.process.ProcessFileContractCategory;
import com.timevale.contractmanager.common.service.enums.*;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.enums.sharesign.ShareSignTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.*;
import com.timevale.contractmanager.common.utils.DateUtil;
import com.timevale.contractmanager.core.model.dto.seal.SealDetailDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.common.service.enums.ProcessBusinessType;
import com.timevale.contractmanager.core.service.component.ProcessConfigConverter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractrelation.ContractFileBizRelationService;
import com.timevale.contractmanager.core.service.mq.consumer.searchsync.ProcessSearchParamBuilder;
import com.timevale.contractmanager.core.service.mq.model.ProcessAccountBindMsgEntity;
import com.timevale.contractmanager.core.service.mq.model.SignChangeMsgEntity;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.ProcessFileAuthService;
import com.timevale.contractmanager.core.service.process.converter.TaskInfoConverter;
import com.timevale.contractmanager.core.service.process.handler.ProcessDetailHandler;
import com.timevale.contractmanager.core.service.process.handler.bean.ProcessDetailBean;
import com.timevale.contractmanager.core.service.seal.SealService;
import com.timevale.contractmanager.core.service.util.ProcessUtils;
import com.timevale.contractmanager.core.service.util.TaskIdUtil;
import com.timevale.doccooperation.service.enums.CooperationerRoleEnum;
import com.timevale.doccooperation.service.input.GetCoooperatorWithTaskInput;
import com.timevale.doccooperation.service.model.CooperatorInfoWithTask;
import com.timevale.doccooperation.service.result.GetCooperatorWithTaskResult;
import com.timevale.doccooperation.service.util.LambdaUtil;
import com.timevale.flowmanager.common.service.bean.AccountIdBean;
import com.timevale.flowmanager.common.service.bean.FlowNodeFieldBean;
import com.timevale.flowmanager.common.service.enums.AccountIdTypeEnum;
import com.timevale.flowmanager.common.service.enums.ServiceKeyEnum;
import com.timevale.flowmanager.common.service.result.flowqueryresult.QueryFlowNodeFieldResult;
import com.timevale.footstone.rpc.enums.DocSourceEnum;
import com.timevale.footstone.rpc.model.flowmodel.QueryTaskInfoInput;
import com.timevale.footstone.rpc.result.flowresult.QueryTaskInfoOutput;
import com.timevale.footstone.rpc.result.flowresult.SignInfoOutput;
import com.timevale.footstone.rpc.result.flowresult.bean.SignInfo;
import com.timevale.footstone.seal.facade.enums.SealBizTypeDesc;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringEscapeUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.bean.SealInfo;
import com.timevale.signflow.search.docSearchService.enums.*;
import com.timevale.signflow.search.docSearchService.enums.ProcessSecretEnum;
import com.timevale.signflow.search.docSearchService.param.UpdateAccountParam;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.request.datacollect.*;

import lombok.extern.slf4j.Slf4j;

import ma.glasnost.orika.MapperFactory;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/5/10
 */
@Slf4j
@Component
public class ProcessDataBuilder {

    @Autowired private ProcessDetailHandler processService;
    @Autowired private ProcessSearchParamBuilder paramBuilder;
    @Autowired private BaseProcessService baseProcessService;
    @Autowired private SignClient signClient;
    @Autowired private UserCenterService userCenterService;
    @Autowired private DocCooperationClient docCooperationClient;
    @Autowired private ProcessDetailHandler processDetailHandler;
    @Autowired private ContractProcessGroupDAO contractProcessGroupDAO;
    @Autowired private ContractProcessGroupService processGroupService;
    @Autowired private FlowQueryClient flowQueryClient;
    @Autowired private SealService sealService;
    @Autowired private ShareSignTaskDAO shareSignTaskDAO;
    @Autowired private ApprovalClient approvalClient;
    @Autowired private SubProcessDAO subProcessDAO;
    @Autowired private ContractProcessReadClient processQueryClient;
    @Autowired private ContractFileBizRelationService contractFileBizRelationService;
    @Autowired private MapperFactory mapperFactory;
    @Autowired private ProcessApprovalClient processApprovalClient;
    @Autowired private ContractAnalysisClient contractAnalysisClient;
    @Autowired private ProcessFileAuthService processFileAuthService;
    /**
     * 组装创建索引请求参数
     *
     * @param processId
     * @return
     */
    public ContractProcessSaveParam buildProcessInfoParam(String processId) {
        ProcessDetailBean response = processService.queryProcessDetail(processId);
        // 子流程
        List<ProcessDetailBean.SubProcess> subProcessList = response.getSubProcessList();
        // 如果不存在子流程， 则直接返回
        if (CollectionUtils.isEmpty(response.getSubProcessList())) return null;

        Integer createWay = response.getProcessCreateType();
        if (response.getProcessCreateType().equals(ProcessCreateTypeEnum.BY_TEMPLATE.getCreateType())) {
            if (null != response.getProcessGroup() && StringUtils.isNotEmpty(response.getProcessGroup().getProcessGroupId())) {
                createWay = CreateWayEnum.TEMPLATE_BATCH.getType();
            } else {
                createWay = CreateWayEnum.TEMPLATE.getType();
            }
        } else {
            if (null != response.getProcessGroup() && StringUtils.isNotEmpty(response.getProcessGroup().getProcessGroupId())) {
                createWay = CreateWayEnum.NORMAL_BATCH.getType();
            } else {
                createWay = CreateWayEnum.NORMAL.getType();
            }
        }

        ContractProcessSaveParam param = new ContractProcessSaveParam();
        param.setProcessId(response.getProcessId());
        param.setSignMode(response.getSignMode());
        if (StringUtils.isNotBlank(response.getDedicatedCloudId())) {
            param.setDedicatedCloudId(response.getDedicatedCloudId());
        }
        // 设置流程业务类型
        Integer processBizType = response.getProcessConfig().getProcessBizType();
        if (ProcessBusinessType.RESTART.getBusinessType().equals(processBizType)) {
            processBizType = ProcessBusinessType.NORMAL.getBusinessType();
        }
        param.setProcessBizType(processBizType);
        // 设置流程发起方式
        param.setProcessStartType(response.getProcessConfig().getProcessStartType());
        // 设置流程来源
        param.setProcessFrom(response.getProcessConfig().getProcessFrom());
        param.setNonStandard(response.isNonStandard());
        param.setProcessSceneType(response.getProcessSceneType());
        // 获取子流程类型列表
        Set<Integer> subProcessTypes =
                subProcessList.stream().map(i -> i.getSubProcessType()).collect(Collectors.toSet());
        if (subProcessTypes.contains(SubProcessTypeEnum.SIGN.getType())) {
            param.setProcessType(ProcessTypeEnum.SIGNING.getType());
        } else if (subProcessTypes.contains(SubProcessTypeEnum.CONTRACT_APPROVAL.getType())) {
            param.setProcessType(ProcessTypeEnum.CONTRACT_APPROVAL.getType());
        } else if (subProcessTypes.contains(SubProcessTypeEnum.COOPERATION.getType())) {
            param.setProcessType(ProcessTypeEnum.WRITING.getType());
        } else if (subProcessTypes.contains(SubProcessTypeEnum.OFFLINE.getType())) {
            param.setProcessType(ProcessTypeEnum.OFFLINE_PROCESS.getType());
        }

        // 设置流程隐藏标识及管理员隐藏标识
        param.setIsShow(true);
        if (CreateWayEnum.AUTHORIZE_SEAL.getType().equals(createWay)) {
            param.setIsShow(Boolean.FALSE);
        }

        param.setProcessHidden(false);
        param.setAppId(response.getAppId());
        param.setTitle(StringEscapeUtils.unescapeJava(response.getProcessTitle()));


        param.setProcessCreateTime(response.getProcessCreateTime());

//        // todo processUpdateTime
//        param.setProcessUpdateTime(DateUtil.dateToLong(response.getProcessUpdateTime()));

        param.setContractExpireTime(response.getContractEndTime());
        param.setSignExpireTime(response.getProcessEndTime());

        // 完成时间，完成状态的才有
        if (ProcessStatusEnum.DONE.getStatus() == response.getProcessStatus()) {
            // 获取主流程中存储的流程完成时间
            Long processCompleteTime = response.getProcessConfig().getProcessCompleteTime();
            if (null != processCompleteTime) {
                param.setSignCompleteTime(DateUtil.long2Date(processCompleteTime));
            }
            // 如果存在签署流程， 优先获取签署流程的结束时间
            Optional<ProcessDetailBean.SubProcess> signListOptional = subProcessList.stream()
                    .filter(t -> t.getSubProcessType().equals(SubProcessTypeEnum.SIGN.getType()))
                    .findFirst();
            if (signListOptional.isPresent()) {
                String flowId = signListOptional.get().getSubProcessId();
                param.setSignCompleteTime(getSignCompleteTime(flowId));
            }
        }

        // 发起端信息
        param.setOperateClientType(response.getInitiateClient());

        param.setProcessStatus(response.getProcessStatus());
        param.setProcessDesc(ProcessStatusEnum.parse(response.getProcessStatus()).getStatusDesc());
        // 设置流程终止原因
        if (StringUtils.isNotBlank(response.getTerminateReason())) {
            param.setProcessDesc(response.getTerminateReason());
        }
        param.setCreateWay(createWay);

        param.setSignType(
                (response.isHashSign() ? DocSourceEnum.HASH : DocSourceEnum.PDF).getCode());
        param.setRescindRemarks(paramBuilder.generateRescindRemarks(processId));

        // 自定义配置
        param.setCustomizeConfig(buildProcessConfig(processId));

        // 设置发起人信息
        ProcessAccount initiator = paramBuilder.buildSearchProcessAccount(P_INITIATOR, response.getInitiator());
        paramBuilder.fillInitiatorDept(response.getProcessId(), response.getProcessConfig(), initiator);
        param.setInitiator(ProcessDataCollectConverter.account2Param(initiator));

        // 追加抄送方
        if (CollectionUtils.isNotEmpty(response.getCcs())) {
            List<ProcessAccount> ccList = new ArrayList<>();
            for (ProcessDetailBean.ProcessAccount cc : response.getCcs()) {
                ccList.add(paramBuilder.buildSearchProcessAccount(P_CC, cc));
            }
            param.setCc(ProcessDataCollectConverter.account2ParamList(ccList));
        }

        // 设置流程组信息
        if (null != response.getProcessGroup()) {
            param.setProcessGroup(ProcessDataCollectConverter.processGroup2Param(response.getProcessGroup()));
        }

        // 附件和文件
        ContractProcessFileInfoParam fileInfoParam =
                ProcessDataCollectConverter.file2FileInfoParam(response.getContracts(), response.getAttachments());
        param.setFileInfo(fileInfoParam);

        // 模板
        param.setTemplateInfo(ProcessDataCollectConverter.template2Param(paramBuilder.buildTemplateInfo(response.getProcessId())));
        param.setCustomTags(response.getCustomTag());

        // 判断下
        List<ContractProcessCooperationTaskParam> cooperationTaskList = null;
        List<ContractProcessSignTaskParam> signTaskDTOS = null;

        //1.只有填  2.只有签  3.有签有填
        SubProcessDataCombinationType subProcessDataCombinationType = getDataCombinationType(subProcessList);

        String signFlowId = null;
        String cooperationFlowId = null;


        if (SubProcessDataCombinationType.NO_DATA == subProcessDataCombinationType) {
            log.info(ProcessDataCollectSupport.LOG_PREFIX + " task no data processId : {}", processId);
            // 这种是api发起没有签也没有填写的
        } else if (SubProcessDataCombinationType.WRITE_ONLY == subProcessDataCombinationType
                || SubProcessDataCombinationType.WRITE_AND_APPROVAL == subProcessDataCombinationType) {
            // 只有填的数据
            ProcessDetailBean.SubProcess subProcess = subProcessList.get(0);

            // 填写任务
            cooperationTaskList = buildProcessWriteTaskList(response, subProcess);

            // 还未真正创建签署任务，进行构造
            signTaskDTOS = subProcess.getParticipants().stream().filter(item ->
                    Arrays.stream(item.getCooperatorRole().split(",")).anyMatch(role ->
                            CooperationerRoleEnum.SIGNER.getRole().equals(Integer.valueOf(role)))
            ).map(item -> {
                ContractProcessSignTaskParam taskInfo = new ContractProcessSignTaskParam();
                taskInfo.setHidden(false);
                // 合同审批场景下，签署不能看到流程， hidden属性设置成true
                if (ProcessStatusEnum.APPROVAL.getStatus() == response.getProcessStatus()) {
                    taskInfo.setHidden(true);
                }
                // 设置任务类型
                taskInfo.setTaskType(TaskTypeEnum.SIGN.getType());
                // 设置任务状态, 等待签署
                taskInfo.setStatus(TaskStatusEnum.SIGN_AWAIT.getStatus());
                // 设置签署人、操作人信息
                fillSignTaskExecutorAndOperator(item, taskInfo);
                return taskInfo;
            }).collect(Collectors.toList());

            cooperationFlowId = subProcess.getSubProcessId();

        } else {
            // 设置流程任务信息， 签署任务 + 填写任务
            for (ProcessDetailBean.SubProcess subProcess : response.getSubProcessList()) {
                Integer subProcessType = subProcess.getSubProcessType();
                if (SubProcessTypeEnum.COOPERATION.getType() == subProcessType) {
                    cooperationTaskList = buildProcessWriteTaskList(response, subProcess);
                    cooperationFlowId = subProcess.getSubProcessId();
                    continue;
                }
                if (SubProcessTypeEnum.SIGN.getType() == subProcessType) {
                    signTaskDTOS = buildProcessSignTaskList(response.getProcessStatus(), subProcess);
                    signFlowId = subProcess.getSubProcessId();
                    continue;
                }
                if (SubProcessTypeEnum.OFFLINE.getType() == subProcessType) {
                    signTaskDTOS = buildOfflineProcessSignTasks(subProcess);
                    continue;
                }
            }
        }

        param.setCooperationFlowId(cooperationFlowId);
        param.setSignFlowId(signFlowId);

        param.setCooperationTasks(cooperationTaskList);
        param.setSignTasks(signTaskDTOS);
        //兜底也加入填充合同编号
        fillContractNo(param.getFileInfo(), processId);
        param.setFileAuth(processFileAuthService.existFileAuth(processId));
        param.setHideInitiator(Boolean.TRUE.equals(response.getProcessConfig().getHideInitiatorName()));
        return param;
    }

    /**
     * 组装线下导入的流程对应的签署任务数据
     * @param subProcess
     * @return
     */
    private List<ContractProcessSignTaskParam> buildOfflineProcessSignTasks(
            ProcessDetailBean.SubProcess subProcess) {
        if (SubProcessTypeEnum.OFFLINE.getType() != subProcess.getSubProcessType()) {
            return Lists.newArrayList();
        }
        return subProcess.getParticipants().stream()
                .map(
                        item -> {
                            ContractProcessSignTaskParam taskInfo =
                                    new ContractProcessSignTaskParam();
                            taskInfo.setHidden(false);
                            // 线下流程默认无序， 签署顺序全部设置为1
                            taskInfo.setOrder(1);
                            // 设置任务类型
                            taskInfo.setTaskType(TaskTypeEnum.SIGN.getType());
                            // 设置任务状态, 等待签署
                            taskInfo.setStatus(TaskStatusEnum.SIGN_DONE.getStatus());
                            // 设置签署人、操作人信息
                            fillSignTaskExecutorAndOperator(item, taskInfo);
                            return taskInfo;
                        })
                .collect(Collectors.toList());
    }

    private List<ContractProcessSignTaskParam> buildProcessSignTaskList(Integer processStatus,
                                                                      ProcessDetailBean.SubProcess subProcess) {

        List<ContractProcessSignTaskParam> taskInfoList = Lists.newArrayList();
        for (ProcessDetailBean.SubProcessParticipant account : subProcess.getParticipants()) {
            if (null == account.getTaskInfo()) {
                continue;
            }
            ProcessDetailBean.TaskBaseInfo accountTaskInfo = account.getTaskInfo();

            ContractProcessSignTaskParam taskInfo = new ContractProcessSignTaskParam();
            // 设置任务基本信息
            taskInfo.setTaskId(accountTaskInfo.getTaskId());
            taskInfo.setFlowId(subProcess.getSubProcessId());
            taskInfo.setOrder(accountTaskInfo.getOrder());
            taskInfo.setHidden(false);
            taskInfo.setParticipantStartType(account.getParticipantStartType());
            // 合同审批场景下，签署不能看到流程， hidden属性设置成true
            if (ProcessStatusEnum.APPROVAL.getStatus() == processStatus) {
                taskInfo.setHidden(true);
            }

            taskInfo.setSource(accountTaskInfo.getSource());

            //操作时间
            if (accountTaskInfo.getOperateTime() != null) {
                taskInfo.setOperateTime(accountTaskInfo.getOperateTime().getTime());
            }

            // 设置任务类型
            taskInfo.setTaskType(TaskTypeEnum.SIGN.getType());
            // 设置任务状态
            taskInfo.setStatus(ProcessUtils.convertSignTaskSearchStatus(accountTaskInfo.getStatus()));
            //任务模式
            taskInfo.setTaskModel(accountTaskInfo.getTaskModel());
            //是否为任务执行人
            taskInfo.setExecutorOwner(accountTaskInfo.getExecutorOwner());

            // 设置签署人、操作人信息
            fillSignTaskExecutorAndOperator(account, taskInfo);

            taskInfoList.add(taskInfo);
        }

        Optional<ContractProcessSignTaskParam> doneSignTaskOptional = taskInfoList.stream()
                .filter(signTask -> TaskStatusEnum.SIGN_DONE.getStatus().equals(signTask.getStatus())).findFirst();
        if (doneSignTaskOptional.isPresent()) {
            // 部分需要填充印章信息
            fillSealInfo(taskInfoList, subProcess.getSubProcessId());
        }
        return taskInfoList;
    }

    /**
     * 填充签署人及操作人信息
     * @param account
     * @param taskInfo
     */
    private void fillSignTaskExecutorAndOperator(ProcessDetailBean.SubProcessParticipant account, ContractProcessSignTaskParam taskInfo) {
        // 设置签署人、操作人信息
        if (CollectionUtils.isNotEmpty(account.getExecutors())) {
            ProcessAccount signerExecute = paramBuilder.buildSearchProcessAccount(P_SIGNER, account.getExecutors().get(0));
            taskInfo.setExecute(ProcessDataCollectConverter.account2Param(signerExecute));
        }
        if (null != account.getOperator()) {
            ProcessAccount signerOperator = paramBuilder.buildSearchProcessAccount(P_OPERATOR, account.getOperator());
            taskInfo.setOperator(ProcessDataCollectConverter.account2Param(signerOperator));
        }
    }


    public List<ContractProcessSignTaskParam> buildProcessAllSignTaskList(String processId, Integer processStatus) {

        SubProcessDO subProcessDO = baseProcessService.getSubProcess(processId, SubProcessTypeEnum.SIGN);
        if (null == subProcessDO) {
            throw new BizContractManagerException(SUB_PROCESS_NOT_EXIST);
        }

        String signFlowId = subProcessDO.getSubProcessId();
        SignInfoOutput signInfoOutput = signClient.querySignInfo(signFlowId);
        if (signInfoOutput == null) {
            return null;
        }

        ProcessDetailBean.SubProcess subProcess = new ProcessDetailBean.SubProcess();
        subProcess.setSubProcessId(signFlowId);
        subProcess.setSubProcessType(SubProcessTypeEnum.SIGN.getType());
        subProcess.setParticipants(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(signInfoOutput.getSignInfoList())) {
            for (SignInfo signInfo : signInfoOutput.getSignInfoList()) {
                subProcess.getParticipants().add(processDetailHandler.buildSignFlowParticipants(signInfo, signFlowId));
            }
        }

        List<ContractProcessSignTaskParam> taskInfoList = buildProcessSignTaskList(processStatus, subProcess);

//        // 有一个签署完成的任务, 就去匹配。为什么不用 processStatus状态
//        Optional<ContractProcessSignTaskParam> doneSignTaskOptional = taskInfoList.stream()
//                .filter(signTask -> TaskStatusEnum.SIGN_DONE.getStatus().equals(signTask.getStatus())).findFirst();
//        if (doneSignTaskOptional.isPresent()) {
//            // 部分需要填充印章信息
//            fillSealInfo(taskInfoList, subProcess.getSubProcessId());
//        }
        return taskInfoList;
    }

    public ContractProcessSignTaskDTO buildUpdateSignTask(SignChangeMsgEntity entity) {
        QueryTaskInfoInput queryTaskInfoInput = new QueryTaskInfoInput();
        queryTaskInfoInput.setFlowId(entity.getFlowId());
        queryTaskInfoInput.setSignerAccountId(entity.getSignerAccountId());
        queryTaskInfoInput.setSignerAuthorizedAccountId(entity.getSignerAuthorizedAccountId());
        queryTaskInfoInput.setOrder(entity.getOrder());
        QueryTaskInfoOutput queryTaskInfoOutput = signClient.queryTaskInfo(queryTaskInfoInput);
        if (queryTaskInfoOutput == null) {
            throw new BizContractManagerException(SUB_PROCESS_NOT_EXIST);
        }
        ContractProcessSignTaskDTO param =  buildUpdateTaskInfo(entity, queryTaskInfoOutput);
        return param;
    }

    private ContractProcessSignTaskDTO buildUpdateTaskInfo(SignChangeMsgEntity entity, QueryTaskInfoOutput taskInfoOutput) {

        String taskId = TaskIdUtil.generateTaskId(entity.getFlowId(),
                entity.getSignerAccountId(),
                entity.getSignerAuthorizedAccountId(),
                entity.getOrder());

        Integer status = ProcessUtils.convertSignTaskSearchStatus(taskInfoOutput.getStatus());
        String flowId = entity.getFlowId();

        ContractProcessSignTaskDTO param = new ContractProcessSignTaskDTO();
        param.setFlowId(flowId);
        param.setTaskId(taskId);
        param.setTaskType(TaskTypeEnum.SIGN.getType());
        param.setOrder(entity.getOrder());
        param.setStatus(status);
        param.setSource(taskInfoOutput.getSource());
        param.setTaskModel(TaskInfoConverter.signTaskTypeToTaskModel(taskInfoOutput.getSignTaskType()));
        param.setExecutorOwner(taskInfoOutput.getExecuted());
        param.setParticipantStartType(taskInfoOutput.getParticipantStartType());
        if (taskInfoOutput.getUpdateTime() != 0L) {
            param.setOperateTime(taskInfoOutput.getUpdateTime());
        }
        param.setHidden(false);
        // 设置任务签署人信息
        AccountBean personExecutor = convertToAccountBean(taskInfoOutput.getAccountIds());
        AccountBean subjectExecutor = convertToAccountBean(taskInfoOutput.getAuthorizedAccountIds());
        ProcessAccount executor = paramBuilder.buildSearchProcessAccount(P_SIGNER, personExecutor, subjectExecutor);


        AccountBean personOperator = convertToAccountBean(taskInfoOutput.getOperatorAccountIds());
        AccountBean subjectOperator = convertToAccountBean(taskInfoOutput.getOperatorAuthorizedAccountIds());
        ProcessAccount operator = paramBuilder.buildSearchProcessAccount(P_OPERATOR, personOperator, subjectOperator);

        param.setExecute(executor);
        param.setOperator(operator);

        // 这些状态需要补充印章信息
        if (TaskStatusEnum.SIGN_DONE.getStatus().equals(status)) {
            ContractProcessSignTaskParam fillSealSignTask = ProcessDataCollectConverter.signTaskParam2Model(param);
            fillSealInfo(Lists.newArrayList(fillSealSignTask), flowId);
            param.setSealInfo(ProcessDataCollectConverter.sealInfoParam2DTO(fillSealSignTask.getSealInfo()));
        }

        return param;
    }

    /**
     * 需改这个方法 注意看 buildUpdateCooperationTask 这个方法有没有相同字段需要修改
     */
    private List<ContractProcessCooperationTaskParam> buildProcessWriteTaskList(
            ProcessDetailBean response, ProcessDetailBean.SubProcess subProcess) {
        List<ContractProcessCooperationTaskParam> taskInfoList = Lists.newArrayList();
        for (ProcessDetailBean.SubProcessParticipant account : subProcess.getParticipants()) {
            if (null == account.getTaskInfo()) {
                continue;
            }
            ProcessDetailBean.TaskBaseInfo accountTaskInfo = account.getTaskInfo();

            ContractProcessCooperationTaskParam taskInfo = new ContractProcessCooperationTaskParam();
            // 设置任务基本信息
            taskInfo.setTaskId(accountTaskInfo.getTaskId());
            taskInfo.setFlowId(subProcess.getSubProcessId());
            taskInfo.setOrder(accountTaskInfo.getOrder());
            taskInfo.setHidden(false);
            taskInfo.setParticipantStartType(account.getParticipantStartType());

            // 填写流程任务信息设置
            // 设置任务类型
            taskInfo.setTaskType(TaskTypeEnum.WRITE.getType());
            // 设置任务状态
            taskInfo.setStatus(ProcessUtils.convertCooperationTaskSearchStatus(accountTaskInfo.getStatus()));
            // 设置填写人信息
            if (CollectionUtils.isNotEmpty(account.getExecutors())) {
                ProcessAccount executor = paramBuilder.buildSearchProcessAccount(P_WRITTER, account.getExecutors().get(0));
                taskInfo.setExecute(ProcessDataCollectConverter.account2Param(executor));
            }
            taskInfo.setSource(accountTaskInfo.getSource());
            if (null != accountTaskInfo.getOperateTime()) {
                taskInfo.setOperateTime(accountTaskInfo.getOperateTime().getTime());
            }
            taskInfoList.add(taskInfo);

        }
        return taskInfoList;
    }

    /**
     * 需改这个方法 注意看 buildProcessWriteTaskList 这个方法有没有相同字段需要修改
     */
    public ContractProcessCooperationTaskParam buildUpdateWriteTask(String processId, String taskId) {
        if (StringUtils.isEmpty(processId) || StringUtils.isEmpty(taskId)) {
            log.warn("updateTaskInfo to es buildUpdateCooperationTask fail, processId:{}, taskId:{}", processId, taskId);
            return null;
        }
        SubProcessDO subProcess =
                baseProcessService.getSubProcess(processId, SubProcessTypeEnum.COOPERATION);
        if (null == subProcess) {
            throw new BizContractManagerException(SUB_PROCESS_NOT_EXIST);
        }
        GetCoooperatorWithTaskInput input = new GetCoooperatorWithTaskInput();
        input.setCooperationId(subProcess.getSubProcessId());
        input.setTaskIds(Lists.newArrayList(taskId));
        GetCooperatorWithTaskResult cooperatorsList = docCooperationClient.getCooperatorsWithTask(input);
        if (CollectionUtils.isEmpty(cooperatorsList.getCooperators())) {
            return null;
        }
        CooperatorInfoWithTask info = cooperatorsList.getCooperators().get(0);
        if (null == info.getTaskInfo()) {
            return null;
        }
        ContractProcessCooperationTaskParam param = new ContractProcessCooperationTaskParam();
        // 设置任务基本信息
        param.setTaskId(info.getTaskInfo().getTaskId());
        param.setTaskType(TaskTypeEnum.WRITE.getType());
        param.setFlowId(subProcess.getSubProcessId());
        param.setOrder(info.getTaskInfo().getTaskOrder());
        param.setStatus(ProcessUtils.convertCooperationTaskSearchStatus(info.getTaskInfo().getTaskStatus()));
        param.setSource(info.getTaskInfo().getSource());
        param.setHidden(false);
        if (null != info.getTaskInfo().getOperateTime()) {
            param.setOperateTime(info.getTaskInfo().getOperateTime().getTime());
        }
        // 设置任务填写人信息
        AccountBean person = convertToAccountBean(info.getAccount());
        AccountBean subject = convertToAccountBean(info.getSubject());
        ProcessAccount executor = paramBuilder.buildSearchProcessAccount(P_WRITTER, person, subject);
        param.setExecute(ProcessDataCollectConverter.account2Param(executor));
        if(info.getSignConfig() != null){
            param.setParticipantStartType(info.getSignConfig().getParticipantStartType());
        }
        return param;
    }

    public ContractProcessUpdateParam buildUpdateInfoParam(String processId, String source) {
        ProcessDO process = baseProcessService.getProcess(processId);
        if (null == process) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }
        SubProcessDO subProcess = baseProcessService.getCurrentSubProcess(processId);

        ContractProcessUpdateParam param = new ContractProcessUpdateParam();
        param.setProcessId(process.getProcessId());
        param.setContractExpireTime(process.getContractEndTime());
        param.setSignExpireTime(process.getProcessEndTime());
        // 设置流程组信息
        if (StringUtils.isNotBlank(process.getProcessGroupId())) {
            // 查询流程组信息
            ContractProcessGroupDO processGroupDO =
                    contractProcessGroupDAO.queryByProcessGroupId(process.getProcessGroupId());
            if (Objects.nonNull(processGroupDO)) {
                ContractProcessGroupParam groupParam = new ContractProcessGroupParam();
                groupParam.setGroupId(processGroupDO.getProcessGroupId());
                groupParam.setGroupName(processGroupDO.getProcessGroupName());
                groupParam.setGroupType(processGroupDO.getProcessGroupType());
                groupParam.setGroupStatus(GroupStatusEnum.ENABLE.getStatus());
                param.setProcessGroup(groupParam);
            }
        }
        param.setProcessStatus(process.getStatus());
        param.setProcessDesc(ProcessStatusEnum.parse(process.getStatus()).getStatusDesc());

        if (process.getStatus().equals(ProcessStatusEnum.DONE.getStatus())) {
            if (SubProcessTypeEnum.SIGN.getType() == subProcess.getSubProcessType()) {
                String flowId = subProcess.getSubProcessId();
                param.setSignCompleteTime(getSignCompleteTime(flowId));
            }
        }

        // 设置解约原因
        param.setRescindRemarks(paramBuilder.generateRescindRemarks(processId));

        // 设置流程终止原因
        ProcessConfigDO processConfig = baseProcessService.queryProcessConfig(processId);
        if (null != processConfig && StringUtils.isNotBlank(processConfig.getTerminateReason())) {
            param.setProcessDesc(processConfig.getTerminateReason());
        }
        if (SubProcessTypeEnum.SIGN.getType() == subProcess.getSubProcessType()) {
            param.setProcessType(ProcessTypeEnum.SIGNING.getType());
        } else if (SubProcessTypeEnum.CONTRACT_APPROVAL.getType() == subProcess.getSubProcessType()) {
            param.setProcessType(ProcessTypeEnum.CONTRACT_APPROVAL.getType());
        } else if (SubProcessTypeEnum.COOPERATION.getType() == subProcess.getSubProcessType()) {
            param.setProcessType(ProcessTypeEnum.WRITING.getType());
        } else if (SubProcessTypeEnum.OFFLINE.getType() == subProcess.getSubProcessType()) {
            param.setProcessType(ProcessTypeEnum.OFFLINE_PROCESS.getType());
        }
        if (ProcessDataCollectBizSceneConstants.PROCESS_INFO_CHANGE.equals(source)) {
            // 线下导入的流程
            if (ProcessConfigConverter.checkIsOfflineProcess(processConfig)) {
                // 签署人更新
                ProcessDetailBean processDetail = processDetailHandler.queryProcessDetail(processId);
                // 线下导入的流程只有一个子流程， 默认直接取第一个子流程即可
                if (CollectionUtils.isNotEmpty(processDetail.getSubProcessList())) {
                    param.setSignTasks(
                            buildOfflineProcessSignTasks(processDetail.getSubProcessList().get(0)));
                }
                // 文件合同类型配置更新
                param.setCustomizeConfig(buildProcessConfig(processId));
            }
            // 合同编号更新
            ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);
            ContractProcessFileInfoParam fileInfoParam = mapperFactory.getMapperFacade().map(contractProcessDTO.getFileInfo(), ContractProcessFileInfoParam.class);
            fileInfoParam = fillContractNo(fileInfoParam, processId);
            param.setFileInfo(fileInfoParam);

            boolean existFileAuth = processFileAuthService.existFileAuth(processId);
            param.setFileAuth(existFileAuth);
        }
        return param;
    }

    public ContractProcessFileInfoParam fillContractNo(ContractProcessFileInfoParam fileInfoParam, String processId) {
        // 填充合同编号
        List<ContractFileBizRelationDO> relationDOList = contractFileBizRelationService.listByProcessId(processId);
        if (CollectionUtils.isNotEmpty(relationDOList)) {
            List<ContractProcessFileParam> contractFiles = LambdaUtil.toList(relationDOList, this::buildFileBean);
            fileInfoParam.setContractFiles(contractFiles);
            return fileInfoParam;
        }
        // 如果合同编号关联关系为空， 清空文件合同编号
        if (fileInfoParam != null && CollectionUtils.isNotEmpty(fileInfoParam.getContractFiles())) {
            fileInfoParam.getContractFiles().forEach(i -> i.setContractNo(""));
        }
        return fileInfoParam;
    }

    /**
     * 组装文件基本信息
     *
     * @param fileInfo
     * @return
     */
    private ContractProcessFileParam buildFileBean(ContractFileBizRelationDO fileInfo) {
        ContractProcessFileParam fileBO = new ContractProcessFileParam();
        fileBO.setFileId(fileInfo.getFileId());
        fileBO.setFileName(fileInfo.getFileName());
        fileBO.setContractNo(fileInfo.getContractNo());
        return fileBO;
    }

    public String getProcessByApprovalId(Long approvalId) {
        ApprovalInfoOutput approvalInfoOutput = approvalClient.getApprovalInfo(approvalId);
        if (approvalInfoOutput == null) {
            return null;
        }
        String flowId = null;
        if (approvalInfoOutput.getApprovalInfo() != null) {
            flowId = approvalInfoOutput.getApprovalInfo().getBizId();
        }
        if (StringUtils.isBlank(flowId)) {
            return null;
        }
        //合同审批消息比cm入库和异步化消息先发送，需要重试
        SubProcessDO subProcessDO = subProcessDAO.getByIdxSubprocessId(flowId);
        if (subProcessDO == null) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.APPROVAL_QUERY_PROCESS_ERROR);
        }
        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(subProcessDO.getProcessId());
        if (contractProcessDTO == null) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.APPROVAL_QUERY_PROCESS_ERROR);
        }
        return subProcessDO.getProcessId();
    }

    /** 获取审批详情 */
    public String getProcessByApprovalCode(String approvalCode) {
        ApprovalSyncDataDetailDTO detail =
                processApprovalClient.getApprovalSyncDataDetail(approvalCode);
        if (Objects.isNull(detail)) {
            return null;
        }

        return detail.getBizId();
    }

    public ContractProcessUpdateParam buildNewProcessApproval(Long approvalId) {
        ApprovalInfoOutput approvalInfoOutput = approvalClient.getApprovalInfo(approvalId);
        if (approvalInfoOutput == null) {
            return null;
        }
        ApprovalGroupModel approvalGroupModel = approvalInfoOutput.getApprovalGroup();
        ContractApprovalGroupParam contractApprovalGroupParam = null;
        if (approvalGroupModel != null) {
            contractApprovalGroupParam = new ContractApprovalGroupParam();
            contractApprovalGroupParam.setApprovalGroupId(approvalGroupModel.getApprovalUuid());
            contractApprovalGroupParam.setApprovalGroupName(approvalGroupModel.getName());
            contractApprovalGroupParam.setApprovalGroupStatus(approvalGroupModel.getApprovalStatus());
        }
        ApprovalInfoModel approvalInfoModel = approvalInfoOutput.getApprovalInfo();
        ProcessApprovalParam processApprovalParam = new ProcessApprovalParam();
        if (approvalInfoModel != null) {
            processApprovalParam.setApprovalId(approvalInfoModel.getApprovalId().toString());
            processApprovalParam.setApprovalCreateTime(approvalInfoModel.getCreateTime());
            processApprovalParam.setApprovalCompleteTime(approvalInfoModel.getApprovalEndTime());
            processApprovalParam.setApprovalStatus(approvalInfoModel.getApprovalStatus());
            processApprovalParam.setApprovalTaskInfo(ProcessDataCollectConverter.ApprovalAccountTaskInfoModel2ParamList(approvalInfoModel.getTaskInfos()));
            //特殊处理，大于当前审批节点并且状态未审批中，更改为等待审批
            for (ProcessApprovalTaskInfoParam taskInfoParam : processApprovalParam.getApprovalTaskInfo()) {
                if (approvalInfoModel.getCurrentTaskId() != null && Long.parseLong(taskInfoParam.getTaskId()) > approvalInfoModel.getCurrentTaskId() && taskInfoParam.getTaskStatus().equals(ApprovalStatusEnum.APPROVALING.getValue())) {
                    taskInfoParam.setTaskStatus(ApprovalStatusEnum.WAIT_START.getValue());
                }
            }
        }

        ContractProcessUpdateParam contractProcessUpdateParam = new ContractProcessUpdateParam();
        contractProcessUpdateParam.setApprovalGroup(contractApprovalGroupParam);
        contractProcessUpdateParam.setProcessApproval(processApprovalParam);
        return contractProcessUpdateParam;
    }

    /** 构建更新es合同审批数据参数，新合同审批数据 */
    public ContractProcessUpdateParam buildNewProcessApproval(String approvalCode, String bizGroupId) {
        if (StringUtils.isAllBlank(approvalCode, bizGroupId)) {
            return null;
        }
        ApprovalSyncDataDetailDTO detail = processApprovalClient.getApprovalSyncDataDetail(approvalCode);
        ApprovalGroupDTO approvalGroup = processApprovalClient.getApprovalGroup(bizGroupId);
        if (Objects.isNull(detail) && Objects.isNull(approvalGroup)) {
            return null;
        }

        // 更新参数
        ContractProcessUpdateParam updateParam = new ContractProcessUpdateParam();

        // 加入审批流程信息
        if (Objects.nonNull(detail)) {
            ProcessApprovalParam processApprovalParam = new ProcessApprovalParam();
            processApprovalParam.setApprovalId(detail.getApprovalId().toString());
            processApprovalParam.setApprovalStatus(detail.getApprovalStatus());
            processApprovalParam.setApprovalCreateTime(detail.getCreateTime());
            processApprovalParam.setApprovalCompleteTime(detail.getFinishTime());
            if (CollectionUtils.isNotEmpty(detail.getApprovalTasks())) {
                processApprovalParam.setApprovalTaskInfo(
                        detail.getApprovalTasks().stream()
                                .map(this::buildTaskInfo)
                                .collect(Collectors.toList()));
            }
            updateParam.setProcessApproval(processApprovalParam);
        }

        // 加入审批流程组信息
        if (Objects.nonNull(approvalGroup)) {
            ContractApprovalGroupParam contractApprovalGroupParam = new ContractApprovalGroupParam();
            boolean closed = Objects.equals(approvalGroup.getFinishCount(), approvalGroup.getTotalCount());
            contractApprovalGroupParam.setApprovalGroupId(approvalGroup.getBizGroupId());
            contractApprovalGroupParam.setApprovalGroupName(approvalGroup.getBizGroupName());
            contractApprovalGroupParam.setApprovalGroupStatus(closed ? 0 : 1);
            updateParam.setApprovalGroup(contractApprovalGroupParam);
        }

        return updateParam;
    }

    private ProcessApprovalTaskInfoParam buildTaskInfo(ApprovalSyncDataTaskDTO task) {
        ProcessApprovalTaskInfoParam taskInfoParam = new ProcessApprovalTaskInfoParam();
        taskInfoParam.setTaskId(task.getTaskId());
        taskInfoParam.setTaskStatus(task.getTaskStatus());
        taskInfoParam.setOperatorTime(task.getFinishTime());
        taskInfoParam.setPerson(buildSimpleAccount(task.getAssignee()));
        if (ApprovalTemplateNodeTypeEnum.CARBON_COPY.getCode().equals(task.getNodeType())) {
            taskInfoParam.setTaskType(TaskTypeEnum.APPROVE_CC.getType());
        }
        if (CollectionUtils.isNotEmpty(task.getCandidates())) {
            List<ProcessAccountSimpleParam> simpleCandidates =
                    task.getCandidates().stream()
                            .map(this::buildSimpleAccount)
                            .collect(Collectors.toList());
            taskInfoParam.setCandidate(simpleCandidates);
        }

        return taskInfoParam;
    }

    private ProcessAccountSimpleParam buildSimpleAccount(ApprovalAccountDTO candidate) {
        if (Objects.isNull(candidate)) {
            return null;
        }

        ProcessAccountSimpleParam simpleAccount = new ProcessAccountSimpleParam();
        BeanUtils.copyProperties(candidate, simpleAccount);
        return simpleAccount;
    }

    public SealApprovalParam buildSealApproval(String approvalId) {
        QueryApprovalTaskOutput output = approvalClient.queryApprovalTask(approvalId);
        if (output == null) {
            return null;
        }
        List<SealInfoParam> seal = new ArrayList<>();
        List<String> sealIdList = new ArrayList<>();
        sealIdList.add(output.getSealId());
        List<SealDetailDTO> sealList = sealService.findBySealIdList(sealIdList);
        if (CollectionUtils.isNotEmpty(sealList)) {
            SealDetailDTO sealDetail = sealList.get(0);
            SealInfoParam sealInfoParam = new SealInfoParam();
            sealInfoParam.setSealId(sealDetail.getSealId());
            sealInfoParam.setSealName(sealDetail.getSealName());
            seal.add(sealInfoParam);
        }
        SealApprovalParam sealApproval = new SealApprovalParam();
        sealApproval.setApprovalId(output.getApprovalId());
        sealApproval.setApprovalCreateTime(new Date(output.getCreateTime()));
        sealApproval.setApprovalCompleteTime(output.getFinishTime() == null ? null : new Date(output.getFinishTime()));
        sealApproval.setApprovalStatus(output.getApprovalResult());
        sealApproval.setApprovalDesc(output.getApprovalRemark());
        sealApproval.setSeal(seal);
        ProcessAccountSimpleParam person = new ProcessAccountSimpleParam();
        person.setGid(output.getInitiatorAccountGid());
        person.setName(output.getInitiatorAccountName());
        sealApproval.setInitiatorPerson(person);
        ProcessAccountSimpleParam subject = new ProcessAccountSimpleParam();
        try {
            UserAccountDetail userAccountDetail = userCenterService.getFatUserAccountDetailByOid(output.getOrgOid());
            subject.setName(userAccountDetail.getAccountName());
        } catch (Exception e) {
            log.warn("buildSealApproval getSubjectAccount error:{}, approvalId:{}, oid:{}", e, approvalId, output.getOrgOid());
        }
        subject.setGid(output.getOrgGid());
        sealApproval.setSubject(subject);
        sealApproval.setApprovalTaskInfo(ProcessDataCollectConverter.approvalTask2ParamList(output.getTaskInfoList(), output.getCreateTime()));
        return sealApproval;
    }

    /**
     * 组装新用印审批数据
     * @param approvalCode
     * @return
     */
    public SealApprovalParam buildNewSealApproval(String approvalCode) {
        ApprovalSyncDataDetailDTO detail = processApprovalClient.getApprovalSyncDataDetail(approvalCode);
        if (detail == null) {
            return null;
        }
        List<SealInfoParam> seal = new ArrayList<>();
        if (ApprovalTemplateConditionTypeEnum.SEAL.getCode().equals(detail.getConditionType())) {
            String sealId = detail.getConditionValue();
            SealInfoParam sealInfoParam = new SealInfoParam();
            sealInfoParam.setSealId(sealId);
            List<SealDetailDTO> sealList = sealService.findBySealIdList(Lists.newArrayList(sealId));
            if (CollectionUtils.isNotEmpty(sealList)) {
                sealInfoParam.setSealName(sealList.get(0).getSealName());
            }
            seal.add(sealInfoParam);
        }
        SealApprovalParam sealApproval = new SealApprovalParam();
        sealApproval.setApprovalId(detail.getApprovalCode());
        sealApproval.setApprovalCreateTime(detail.getCreateTime());
        sealApproval.setApprovalCompleteTime(detail.getFinishTime() == null ? null : detail.getFinishTime());
        sealApproval.setApprovalStatus(detail.getApprovalStatus());
        sealApproval.setApprovalDesc(detail.getRemark());
        sealApproval.setSeal(seal);
        ProcessAccountSimpleParam person = new ProcessAccountSimpleParam();
        person.setGid(detail.getInitiatorGid());
        person.setName(detail.getInitiatorName());
        sealApproval.setInitiatorPerson(person);
        ProcessAccountSimpleParam subject = new ProcessAccountSimpleParam();
        try {
            UserAccountDetail userAccountDetail = userCenterService.getFatUserAccountDetailByOid(detail.getSubjectOid());
            subject.setName(userAccountDetail.getAccountName());
        } catch (Exception e) {
            log.warn("buildSealApproval getSubjectAccount error:{}, approvalCode:{}, oid:{}", e, approvalCode, detail.getSubjectOid());
        }
        subject.setGid(detail.getSubjectGid());
        sealApproval.setSubject(subject);
        sealApproval.setApprovalTaskInfo(ProcessDataCollectConverter.approvalTask2ParamList(detail.getApprovalTasks()));
        return sealApproval;
    }

    public List<ContractProcessCustomizeConfigParam> buildProcessConfig(String processId) {
        List<ContractProcessCustomizeConfigParam> customizeConfigList = new ArrayList<>();
        List<ProcessCustomizeConfigDO> processCustomizeConfigDOS =
                baseProcessService.queryCustomizeConfigs(processId);
        if (CollectionUtils.isEmpty(processCustomizeConfigDOS)) {
            return null;
        }
        for (ProcessCustomizeConfigDO processCustomizeConfig : processCustomizeConfigDOS) {
            ContractProcessCustomizeConfigParam customizeConfig = new ContractProcessCustomizeConfigParam();
            customizeConfig.setTenantOid(processCustomizeConfig.getSubjectOid());
            customizeConfig.setTenantGid(processCustomizeConfig.getSubjectGid());
            ProcessCustomizeConfigBean customizeConfigBean =
                    JSONObject.parseObject(processCustomizeConfig.getConfigInfo(), ProcessCustomizeConfigBean.class);
            if (customizeConfigBean != null) {
                customizeConfig.setSecret(customizeConfigBean.getSecretType());
                customizeConfig.setRenewable(customizeConfigBean.getRenewable());
                customizeConfig.setHidden(customizeConfigBean.getHidden());
                customizeConfig.setFileContractCategories(buildFileContractCategoryParams(customizeConfigBean));
            }
            customizeConfigList.add(customizeConfig);
        }
        return customizeConfigList;
    }

    /**
     * 组装文件合同类型
     * @param customizeConfigBean
     * @return
     */
    private List<FileContractCategoryParam> buildFileContractCategoryParams(
            ProcessCustomizeConfigBean customizeConfigBean) {
        List<FileContractCategoryParam> contractCategoryParams = Lists.newArrayList();
        if (CollectionUtils.isEmpty(customizeConfigBean.getFileContractCategories())) {
            return contractCategoryParams;
        }
        // 获取所有的合同类型id列表
        List<String> categoryIds =
                customizeConfigBean.getFileContractCategories().stream()
                        .map(i -> i.getCategoryId())
                        .collect(Collectors.toList());
        // 查询合同类型名称MAP
        Map<String, String> categoryNameMap =
                contractAnalysisClient.batchQueryContractCategoryNameMap(categoryIds);
        // 组装文件合同类型
        for (ProcessFileContractCategory contractCategory :
                customizeConfigBean.getFileContractCategories()) {
            FileContractCategoryParam categoryParam = new FileContractCategoryParam();
            categoryParam.setCategoryId(contractCategory.getCategoryId());
            categoryParam.setCategoryName(categoryNameMap.get(contractCategory.getCategoryId()));
            categoryParam.setFileId(contractCategory.getFileId());
            categoryParam.setOriginFileIds(contractCategory.getOriginFileIds());
            contractCategoryParams.add(categoryParam);
        }
        return contractCategoryParams;
    }

    public ContractProcessSaveParam buildTempGroupInfoParam(String groupId, Integer source) {
        ContractProcessGroupBean processGroupDO =
                processGroupService.queryProcessGroup(groupId);
        if (null == processGroupDO) {
            return null;
        }

        ContractProcessSaveParam param = new ContractProcessSaveParam();
        param.setProcessId(genGroupProcessId(groupId));
        param.setIsShow(true);
        param.setAppId(processGroupDO.getAppId());
        param.setProcessCreateTime(new Date(processGroupDO.getCreateTime()));
//        index.setProcessUpdateTime(processGroupDO.getGmtModified().getTime());
        param.setTitle(processGroupDO.getProcessGroupName());
        param.setOperateClientType(source);
        param.setProcessHidden(false);
        param.setCreateWay(CreateWayEnum.NORMAL.getType());
        param.setSignType(0);
        param.setProcessStatus(com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum.DRAFT.getStatus());
        param.setProcessType(ProcessTypeEnum.TEMP_GROUP.getType());
        param.setSignMode(processGroupDO.getSignMode());
        if (StringUtils.isNotBlank(processGroupDO.getDedicatedCloudId())) {
            param.setDedicatedCloudId(processGroupDO.getDedicatedCloudId());
        }

        UserAccountDetail subjectAccount = userCenterService.getUserAccountDetailByOid(processGroupDO.getOwnerAccountId());
        UserAccountDetail personAccount = userCenterService.getUserAccountDetailByOid(processGroupDO.getCreatorAccountId());
        AccountBean person = convertToAccountBean(personAccount);
        AccountBean subject = convertToAccountBean(subjectAccount);
        ProcessAccount initiator = paramBuilder.buildSearchProcessAccount(P_INITIATOR, person, subject);
        param.setInitiator(ProcessDataCollectConverter.account2Param(initiator));

        Integer processGroupType = processGroupDO.getProcessGroupType();

        ContractProcessGroupParam contractProcessGroupParam = new ContractProcessGroupParam();
        contractProcessGroupParam.setGroupId(processGroupDO.getProcessGroupId());
        contractProcessGroupParam.setGroupName(processGroupDO.getProcessGroupName());
        contractProcessGroupParam.setGroupType(processGroupType);
        contractProcessGroupParam.setGroupStatus(GroupStatusEnum.ENABLE.getStatus());
        if (GroupTypeEnum.isScanGroup(processGroupType)) {
            ShareSignTaskDO shareSignTask =
                    shareSignTaskDAO.queryByShareBizId(groupId, ShareSignTypeEnum.SINGLE_PARTICIPANT_SHARE.getType());
            if (null != shareSignTask) {
                contractProcessGroupParam.setGroupStatus(shareSignTask.getStatus());
            }
        }

        param.setProcessGroup(contractProcessGroupParam);

        // 从saas-search 里 copy 过来的
        ContractProcessCustomizeConfigParam customizeConfig = new ContractProcessCustomizeConfigParam();
        customizeConfig.setTenantOid(subject.getOid());
        customizeConfig.setTenantGid(subject.getGid());
        customizeConfig.setSecret(ProcessSecretEnum.NONE_SECRET.getType());
        param.setCustomizeConfig(Lists.newArrayList(customizeConfig));

        return param;
    }


    public void fillSealInfo(List<ContractProcessSignTaskParam> signTaskList,  String signFlowId) {

        if (CollectionUtils.isEmpty(signTaskList) || StringUtils.isBlank(signFlowId)) {
            return;
        }
        QueryFlowNodeFieldResult queryFlowNodeFieldResult = flowQueryClient.queryFlowNodeField(signFlowId);
        if (CollectionUtils.isEmpty(queryFlowNodeFieldResult.getFlowNodeFieldBeanList())) {
            return ;
        }

        //临时手绘章
        List<FlowNodeFieldBean> tempSealList = new ArrayList<>();
        //过滤sealId为空的
        List<FlowNodeFieldBean> filterNodeFieldList = new ArrayList<>();
        for (FlowNodeFieldBean flowNodeFieldBean : queryFlowNodeFieldResult.getFlowNodeFieldBeanList()) {
            Map<String, String> extendField = flowNodeFieldBean.getExtendFieldBean().getExtendField();
            String json = extendField.get(ServiceKeyEnum.PAAS.getName());
            JSONObject flowExtFieldBean = JSONObject.parseObject(json, JSONObject.class);
            Object signFieldCustomStatus = flowExtFieldBean.get("signfieldCustomStatus");
            if (signFieldCustomStatus != null && Integer.valueOf(signFieldCustomStatus.toString()).equals(5)) {
                continue;
            }
            if (StringUtils.isEmpty(flowNodeFieldBean.getSealId())) {
                if (StringUtils.isNotEmpty(flowNodeFieldBean.getSealFilekey())) {
                    //手绘章没有sealId,临时把sealFileKey当作sealId,用于后面get到这个nodeField
                    flowNodeFieldBean.setSealId(flowNodeFieldBean.getSealFilekey());
                    tempSealList.add(flowNodeFieldBean);
                }
                continue;
            }
            filterNodeFieldList.add(flowNodeFieldBean);
        }
        //普通印章和临时手绘章都没有则返回
        if (CollectionUtils.isEmpty(filterNodeFieldList) && CollectionUtils.isEmpty(tempSealList)) {
            return ;
        }
        //存在普通印章则去查询印章信息
        Map<String, SealDetailDTO> sealDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(filterNodeFieldList)) {
            List<String> sealIdList = filterNodeFieldList.stream().map(FlowNodeFieldBean::getSealId)
                    .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
            List<SealDetailDTO> sealList = sealService.findBySealIdList(sealIdList);
            if (CollectionUtils.isEmpty(sealList)) {
                if (CollectionUtils.isEmpty(tempSealList)) {
                    return;
                }
            } else {
                sealDetailMap = sealList.stream()
                        .collect(Collectors.toMap(SealDetailDTO::getSealId, Function.identity(), (k1, k2) -> k2));
            }
        }

        filterNodeFieldList.addAll(tempSealList);
        for (ContractProcessSignTaskParam taskInfo : signTaskList) {
            Map<String, SealInfo> repeatMap = new HashMap<>();
            for (FlowNodeFieldBean flowNodeFieldBean : filterNodeFieldList) {
                if (taskInfo.getExecute() == null ||
                        taskInfo.getExecute().getPerson() == null ||
                        taskInfo.getExecute().getTenant() == null) {
                    continue;
                }

                ProcessAccountParam execute = taskInfo.getExecute();
                //
                boolean matchGid1 = execute != null && execute.getPerson() != null && execute.getTenant() != null &&
                        Objects.equals(execute.getPerson().getOid(), getOid(flowNodeFieldBean.getTaskExecutorAccoutIdList())) &&
                        Objects.equals(execute.getTenant().getOid(), getOid(flowNodeFieldBean.getTaskSubjectAccountIdList()));

                if (matchGid1 && taskInfo.getOrder().equals(flowNodeFieldBean.getRank())
                        && StringUtils.isNotEmpty(flowNodeFieldBean.getSealId())) {
                    if (repeatMap.get(flowNodeFieldBean.getSealId()) != null) {
                        continue;
                    }
                    SealDetailDTO sealDetail = sealDetailMap.get(flowNodeFieldBean.getSealId());
                    SealInfo sealInfo = new SealInfo();
                    //临时手绘章 或者印章已删除的
                    if (sealDetail == null) {
                        //临时印章没有sealId, 按fileKey当sealId入库
                        sealInfo.setSealId(flowNodeFieldBean.getSealId());
                        sealInfo.setBizType(SealBizTypeDesc.COMMON.getCode());
                    } else {
                        sealInfo.setSealId(sealDetail.getSealId());
                        sealInfo.setSealName(sealDetail.getSealName());
                        sealInfo.setBizType(sealDetail.getSealBizType());
                    }
                    repeatMap.put(flowNodeFieldBean.getSealId(), sealInfo);
                }
            }
            taskInfo.setSealInfo(ProcessDataCollectConverter.sealInfo2Param(new ArrayList<>(repeatMap.values())));
        }
    }

    private String getOid(List<AccountIdBean> accountIdBeanList) {
        if (CollectionUtils.isEmpty(accountIdBeanList)) {
            return null;
        }
        for (AccountIdBean accountIdBean : accountIdBeanList) {
            if (accountIdBean.getAccountType().equals(AccountIdTypeEnum.OID.getType())) {
                return accountIdBean.getAccountId();
            }
        }
        return null;
    }

    public UpdateAccountParam processChangeAccount(ProcessAccountBindMsgEntity entity) {
        if (StringUtils.isEmpty(entity.getProcessId()) ||
                StringUtils.isEmpty(entity.getAccountId()) ||
                StringUtils.isEmpty(entity.getAccountIdType()) ||
                entity.getUpdatedAccount() == null) {
            return null;
        }
        UserAccountDetail newAccount = entity.getUpdatedAccount();

        Account updateAccount = new Account();

        updateAccount.setGid(newAccount.getAccountGid());
        updateAccount.setOid(newAccount.getAccountOid());
        updateAccount.setName(newAccount.getAccountName());
        updateAccount.setMobile(newAccount.getAccountMobile());
        updateAccount.setEmail(newAccount.getAccountEmail());
        updateAccount.setOrgan(newAccount.isOrganize());
        updateAccount.setDeleted(newAccount.isDeleted());

        // 组装更新账号信息参数
        UpdateAccountParam param = new UpdateAccountParam();
        param.setAccountId(entity.getAccountId());
        param.setAccountIdType(entity.getAccountIdType());
        param.setUpdatedAccount(updateAccount);
        param.setProcessId(entity.getProcessId());
        param.setChangeAccountContract(entity.getChangeAccountContract());

        return param;
    }


    public String genGroupProcessId(String groupId) {
        return "temp-" + groupId;
    }

    private Date getSignCompleteTime(String flowId) {
        SignInfoOutput signInfoOutput = signClient.querySignInfo(flowId);
        if (signInfoOutput != null) {
            return new Date(signInfoOutput.getFlowEndTime());
        }
        return null;
    }


    private SubProcessDataCombinationType getDataCombinationType(List<ProcessDetailBean.SubProcess> subProcessList) {
        boolean offline = false;
        boolean hasSign = false;
        boolean hasWrite = false;
        boolean hasApproval = false;
        for (ProcessDetailBean.SubProcess subProcess : subProcessList) {

            Integer subProcessType = subProcess.getSubProcessType();
            if (subProcessType.equals(SubProcessTypeEnum.OFFLINE.getType())) {
                offline = true;
                continue;
            }

            if (subProcessType.equals(SubProcessTypeEnum.SIGN.getType())) {
                hasSign = true;
                continue;
            }
            if (subProcessType.equals(SubProcessTypeEnum.COOPERATION.getType())) {
                hasWrite = true;
                continue;
            }
            if (subProcessType.equals(SubProcessTypeEnum.CONTRACT_APPROVAL.getType())) {
                hasApproval = true;
                continue;
            }
        }
        if (offline) {
            return SubProcessDataCombinationType.OFFLINE;
        }
        if (hasSign && hasWrite && hasApproval) {
            return SubProcessDataCombinationType.WRITE_AND_APPROVAL_AND_SIGN;
        }
        if (hasSign && hasWrite) {
            return SubProcessDataCombinationType.WRITE_AND_SIGN;
        }
        if (hasSign && hasApproval) {
            return SubProcessDataCombinationType.APPROVAL_AND_SIGN;
        }
        if (hasWrite && hasApproval) {
            return SubProcessDataCombinationType.WRITE_AND_APPROVAL;
        }
        if (hasSign) {
            return SubProcessDataCombinationType.SIGN_ONLY;
        }
        if (hasApproval) {
            return SubProcessDataCombinationType.APPROVAL_ONLY;
        }
        if (hasWrite) {
            return SubProcessDataCombinationType.WRITE_ONLY;
        }
        return SubProcessDataCombinationType.NO_DATA;
    }

    private enum SubProcessDataCombinationType {
        // 线下合同
        OFFLINE,
        // 只有签署
        SIGN_ONLY,
        // 只有填写
        WRITE_ONLY,
        // 只有合同审批
        APPROVAL_ONLY,
        // 签署和填写都有
        WRITE_AND_SIGN,
        // 签署和合同审批都有
        APPROVAL_AND_SIGN,
        // 填写和合同审批都有
        WRITE_AND_APPROVAL,
        // 填写、签署和合同审批都有
        WRITE_AND_APPROVAL_AND_SIGN,

        NO_DATA;

    }

}

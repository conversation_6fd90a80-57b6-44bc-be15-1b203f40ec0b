package com.timevale.contractmanager.core.service.offlinecontract.bean.output;

import com.timevale.contractmanager.common.service.bean.offlinecontract.OfflineContractExtractConfig;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 查询线下合同导入记录基本信息效应数据
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class OfflineContractRecordInfoOutputDTO extends ToString {
    /** 主体oid */
    private String subjectOid;
    /** 主体gid */
    private String subjectGid;
    /** 导入记录id */
    private String recordId;
    /** 归档菜单id */
    private String menuId;
    /** 归档菜单名称 */
    private String menuName;
    /** 归档菜单路径 */
    private String menuPath;
    /** 导入方式 */
    private String importWay;
    /** 导入时间 */
    private Long importTime;
    /** 合同信息提取方式 */
    private String extractWay;
    /** 合同数量 */
    private long contractSize;
    /** 导入成功数量 */
    private long successSize;
    /** 导入失败数量 */
    private long failedSize;
    /** 合同信息提取配置 */
    private OfflineContractExtractConfig extractConfig;
    /** 状态 */
    private String status;
}

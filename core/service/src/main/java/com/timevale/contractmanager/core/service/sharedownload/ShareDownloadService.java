package com.timevale.contractmanager.core.service.sharedownload;

import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadPreviewBO;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadShareInfoBO;
import com.timevale.contractmanager.core.model.dto.response.ProcessSignInfoResponse;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.ShareDownloadShareProcessInfoResponse;
import com.timevale.contractmanager.core.model.dto.sharedownload.FlowDocumentCacheDTO;

/**
 * <AUTHOR>
 * @since 2022/2/10
 */
public interface ShareDownloadService {
    /**
     * 获取分享下载信息
     *
     * @param processId 合同id
     * @param operatorOid 操作人id
     * @return
     */
    ShareDownloadShareInfoBO getShareDownloadInfo(String processId, String operatorOid,String operatingSubjectOid);

    /**
     * 获取合同文件信息
     *
     * @param shareDownloadId
     * @param operatorOid
     * @param sign
     * @return
     */
    ShareDownloadShareProcessInfoResponse getShareDownloadProcessFiles(
            String shareDownloadId, String operatorOid, String sign);

    /**
     * 获取合同文件下载地址
     *
     * @param shareDownloadId
     * @param operatorOid
     * @param sign
     * @param fileKey
     * @return
     */
    FlowDocumentCacheDTO getShareDownloadProcessFileUrl(
            String shareDownloadId, String operatorOid, String sign, String fileKey);

    /**
     * 合同文件预览
     * @param shareDownloadId
     * @param operatorOid
     * @param sign
     * @param previewBO
     * @return
     */
    ShareDownloadPreviewBO.PdfPageImagesBO shareDownloadProcessFilePreview(
            String shareDownloadId,
            String operatorOid,
            String sign,
            ShareDownloadPreviewBO previewBO);

    /**
     * 获取合同签署信息
     *
     * @param operatorOid 当前操作人id
     * @param subjectId 当前操作人主体id
     * @param processId 主流程
     * @return ProcessDetailWechatResponse
     */
    ProcessSignInfoResponse processSignInfo(String operatorOid, String subjectId, String processId);

    /**
     * 获取合同文件转pdf信息
     *
     * @param operatorOid 当前操作人id
     * @param subjectId 当前操作人主体id
     * @param processId 主流程
     * @return ShareDownloadShareFilePreviewResponse
     */
    ShareDownloadPreviewBO.PdfPageImagesBO processViewImage(
            String operatorOid, String subjectId, String processId, ShareDownloadPreviewBO previewBO);
}

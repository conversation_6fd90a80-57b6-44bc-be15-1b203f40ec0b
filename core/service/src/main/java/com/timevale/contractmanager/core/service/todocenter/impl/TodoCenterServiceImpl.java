package com.timevale.contractmanager.core.service.todocenter.impl;

import com.google.common.collect.Lists;
import com.timevale.contractapproval.facade.dto.ApprovalAggregateDTO;
import com.timevale.contractapproval.facade.enums.ApprovalQueryTypeEnum;
import com.timevale.contractapproval.facade.input.ApprovalPageInput;
import com.timevale.contractapproval.facade.input.SubjectApprovalCountInput;
import com.timevale.contractapproval.facade.output.PageApprovalFlowOutput;
import com.timevale.contractapproval.facade.output.SubjectApprovalCountOutput;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.bean.ProcessNodeAccount;
import com.timevale.contractmanager.common.service.bean.process.ContractFileBean;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.ApprovalInstanceClient;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.common.service.integration.client.InfoCollectClient;
import com.timevale.contractmanager.common.service.integration.client.LowcodeClient;
import com.timevale.contractmanager.core.model.dto.request.TodoProcessRequest;
import com.timevale.contractmanager.core.model.dto.response.todocenter.*;
import com.timevale.contractmanager.core.model.enums.TodoTypeEnum;
import com.timevale.contractmanager.core.service.fulfillment.ContractFulfillmentRecordService;
import com.timevale.contractmanager.core.service.other.UserCenterCachedService;
import com.timevale.contractmanager.core.service.todocenter.TodoCenterService;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.signflow.search.docSearchService.api.DocSearchApi;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.AccountIdInfo;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.enums.DocQueryEnum;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import com.timevale.signflow.search.docSearchService.enums.TimeRangeTypeEnum;
import com.timevale.signflow.search.docSearchService.param.DocQueryParam;
import com.timevale.signflow.search.docSearchService.param.HasWaitMeParam;
import com.timevale.signflow.search.docSearchService.param.QueryStatisticsParam;
import com.timevale.signflow.search.docSearchService.param.QueryWaitMeParam;
import com.timevale.signflow.search.docSearchService.param.TotalWaitMeParam;
import com.timevale.signflow.search.docSearchService.result.*;
import com.timevale.signflow.search.service.enums.ProcessInfoFilterFieldEnum;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.Collator;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.RELATE_PROCESS_TODO_TYPE_NOT_SUPPORT;
import static com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum.SIGNING;
import static com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum.WRITING;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;

/**
 * <AUTHOR>
 * @since 2021-11-24 14:53
 */
@Slf4j
@Service
public class TodoCenterServiceImpl implements TodoCenterService {
    // 全部主体
    private static final String SUBJECT_ALL_LABEL = "全部";
    private static final String SUBJECT_ALL_ID = "ALL";
    // 默认统计数量
    private static final Long COUNT_DEFAULT = 0L;
    // 无名称企业默认文案
    private static final String UNNAMED_SUBJECT = "未命名企业";
    // 个人空间
    private static final String PERSONAL_SUBJECT_LABEL = "个人空间";

    @Autowired private UserCenterCachedService userCenterCachedService;
    @Autowired private DocSearchApi docSearchApi;
    @Autowired private ContractFulfillmentRecordService fulfillmentRecordService;
    @Autowired private MapperFactory mapperFactory;
    @Autowired private EsClient esClient;
    @Autowired private ApprovalInstanceClient approvalInstanceClient;
    @Autowired
    private InfoCollectClient infoCollectClient;

    @Override
    public TypeTodoHaveResponse have(String tenantOid, String operatorOid) {
        TypeTodoHaveResponse response = new TypeTodoHaveResponse();
        response.setHave(doHaveTodo(tenantOid, operatorOid));
        return response;
    }

    private boolean doHaveTodo(String tenantOid, String operatorOid) {
        String operatorGid = userCenterCachedService.getAccountGid(operatorOid);

        HasWaitMeParam hasWaitMeParam = new HasWaitMeParam();
        HasWaitMeParam.Account account = new HasWaitMeParam.Account();
        account.setOid(operatorOid);
        account.setGid(operatorGid);
        hasWaitMeParam.setAccount(account);
        hasWaitMeParam.setInSignValidity(true);
        hasWaitMeParam.setTypes(Lists.newArrayList(
                TodoTypeEnum.getBizType(TodoTypeEnum.WAIT_SIGN.getType()),
                TodoTypeEnum.getBizType(TodoTypeEnum.WAIT_FILL.getType())));

        // <type, 是否有待办>
        Map<Integer, Boolean> haveMap = esClient.hasWaitMe(hasWaitMeParam);
        if (MapUtils.isNotEmpty(haveMap)) {
            boolean haveTodo = haveMap.values().stream().anyMatch(Boolean.TRUE::equals);
            if (haveTodo) {
                // 有待办直接返回
                return true;
            }
        }
        // 待我审批数量
        PageApprovalFlowOutput lowCodeOutput =
                Optional.ofNullable(operatorGid)
                        .filter(StringUtils::isNotBlank)
                        .map(x -> approvalInstanceClient.listApproval(buildApprovingInput(x,null)))
                        .orElse(new PageApprovalFlowOutput(emptyList(), 0));
        return lowCodeOutput.getTotal() > 0;
    }

    @Override
    public TypeTodoTotalResponse count(String userOid, String subjectOid, Boolean queryApprovalTotalCount, Boolean queryCurrentSubject) {
        String userGid = userCenterCachedService.getAccountGid(userOid);
        String subjectGid = userCenterCachedService.getAccountGid(subjectOid);
        // 合同待办统计数据 Map<processStatus, count>
        Map<Integer, Long> processCountMap = mGetProcessCount(userOid, userGid,subjectOid,subjectGid,queryCurrentSubject);
        // 1. 待我签署
        TypeTodoCountDTO signCount =
                new TypeTodoCountDTO(
                        TodoTypeEnum.WAIT_SIGN.getLabel(),
                        TodoTypeEnum.WAIT_SIGN.getType(),
                        processCountMap.getOrDefault(SIGNING.getStatus(), COUNT_DEFAULT));

        // 2. 待我填写
        TypeTodoCountDTO fillCount =
                new TypeTodoCountDTO(
                        TodoTypeEnum.WAIT_FILL.getLabel(),
                        TodoTypeEnum.WAIT_FILL.getType(),
                        processCountMap.getOrDefault(WRITING.getStatus(), COUNT_DEFAULT));

        // 3. 待我审批
        PageApprovalFlowOutput lowCodeOutput =
                Optional.ofNullable(userGid)
                        .filter(StringUtils::isNotBlank)
                        .map(x -> approvalInstanceClient.listApproval(buildApprovingInput(x,queryCurrentSubject ? subjectGid : null)))
                        .orElse(new PageApprovalFlowOutput(emptyList(), 0));
        TypeTodoCountDTO approvalCount =
                buildApprovalCountDTO(lowCodeOutput, queryApprovalTotalCount);

        StatisticsSubjectResult statisticsSubjectResult = 
                getWaitTaStatisticsSubjectResult(userOid, userGid, false,subjectOid,subjectGid,queryCurrentSubject);
        // 待他人操作
        TypeTodoCountDTO waitTaCount =
                new TypeTodoCountDTO(
                        TodoTypeEnum.WAIT_TA.getLabel(),
                        TodoTypeEnum.WAIT_TA.getType(),
                        statisticsSubjectResult.getTotal());


        // 4. 统计结果构建
        List<TypeTodoCountDTO> todos = Lists.newArrayList(signCount, fillCount, approvalCount, waitTaCount);

        // 履约提醒
        TypeTodoCountDTO fulfillmentCount = fulfillmentRecordService.querySubjectFulfillmentCount(userOid, userGid,subjectGid,queryCurrentSubject);
        todos.add(fulfillmentCount);

        // 待处理表单
        TypeTodoCountDTO waitForm = new TypeTodoCountDTO();
        waitForm.setCount(infoCollectClient.waitDealTaskCount(userOid));
        waitForm.setLabel(TodoTypeEnum.WAIT_FORM.getLabel());
        waitForm.setType(TodoTypeEnum.WAIT_FORM.getType());
        todos.add(waitForm);


        return new TypeTodoTotalResponse(todos);
    }

    private StatisticsSubjectResult getWaitTaStatisticsSubjectResult(String userOid, String userGid, boolean querySubjectCount,
                                                                     String subjectOid, String subjectGid, Boolean queryCurrentSubject) {
        AccountIdInfo person = new AccountIdInfo();
        person.setOid(userOid);
        person.setGid(userGid);
        QueryStatisticsParam statisticsParam = new QueryStatisticsParam();
        statisticsParam.setAccount(person);
        if (queryCurrentSubject) {
            // 查询当前企业条件
            AccountIdInfo subject = new AccountIdInfo();
            subject.setOid(subjectOid);
            subject.setGid(subjectGid);
            statisticsParam.setSubject(subject);
        }
        statisticsParam.setQuerySubjectCount(querySubjectCount);
        statisticsParam.setType(DocQueryEnum.KS_WAIT_TA.getType());

        return docSearchApi.statisticsSubjectByType(statisticsParam);
    }

    @Override
    public SubjectsTodoTotalResponse aggregateBySubject(String userOid, Integer type) {
        String userGid = null;
        // 1. 获取待办聚合统计列表
        List<SubjectTodoCountDTOHolder> subjectCounts;
        if (TodoTypeEnum.WAIT_APPROVAL_NEW.getType().equals(type)) {
            // 新版审批
            userGid = userCenterCachedService.getAccountGid(userOid);
            subjectCounts = aggregateNewApproval(userGid);
        } else if (TodoTypeEnum.isProcess(type)) {
            // 合同
            userGid = userCenterCachedService.getAccountGid(userOid);
            if (TodoTypeEnum.WAIT_TA.getType().equals(type)) {
                userGid = userCenterCachedService.getAccountGid(userOid);
                StatisticsSubjectResult statisticsSubjectResult = 
                        getWaitTaStatisticsSubjectResult(userOid, userGid, true,null,null,false);
                subjectCounts =
                        statisticsSubjectResult.getSubjectCount().stream()
                                .filter(Objects::nonNull)
                                .filter(subjectCount -> Objects.nonNull(subjectCount.getCount()))
                                .map(
                                        subjectCount ->
                                                new SubjectTodoCountDTOHolder(
                                                        subjectCount.getSubjectName(),
                                                        subjectCount.getSubjectGid(),
                                                        subjectCount.getSubjectOid(),
                                                        subjectCount.getCount()))
                                .collect(Collectors.toList());
            } else {
                subjectCounts = aggregateProcess(userOid, userGid, type);
            }
        } else if (TodoTypeEnum.FULFILLMENT.getType().equals(type)){
            userGid = userCenterCachedService.getAccountGid(userOid);
            subjectCounts = fulfillmentRecordService.aggregateFulfillment(userOid, userGid);
        } else {
            // 其他类型不支持(旧版审批不展示聚合统计)
            throw new BizContractManagerException(RELATE_PROCESS_TODO_TYPE_NOT_SUPPORT, type);
        }

        // 2. 填充主体名称
        if (CollectionUtils.isEmpty(subjectCounts)) {
            return new SubjectsTodoTotalResponse(Collections.emptyList());
        }
        fillSubjectName(subjectCounts);

        // 3. 处理返回值对象
        SubjectsTodoTotalResponse response = formatResponse(subjectCounts, userGid, type);

        return response;
    }

    @Override
    public ProcessTodoListResponse listProcess(String userOid, TodoProcessRequest request) {
        Integer type = request.getType();
        // 非支持的待办状态直接阻断
        if (!TodoTypeEnum.isProcess(type)) {
            throw new BizContractManagerException(RELATE_PROCESS_TODO_TYPE_NOT_SUPPORT, type);
        }

        String userGid = userCenterCachedService.getAccountGid(userOid);
        String subjectGid = userCenterCachedService.getFatUserAccountGid(request.getSubjectId());
        Integer processStatus = TodoTypeEnum.getBizType(type);

        Account subject = new Account();
        subject.setOid(request.getSubjectId());
        subject.setGid(subjectGid);
        Account person = new Account();
        person.setOid(userOid);
        person.setGid(userGid);
        if(TodoTypeEnum.WAIT_TA.getType().equals(request.getType())){
            DocQueryParam build = DocQueryParam
                    .builder()
                    .person(person)
                    .subject(subject)
                    .docQueryType(DocQueryEnum.KS_WAIT_TA.getType())
                    .fuzzyMatching(request.getFuzzyMatching())
                    .processStatusList(Lists.newArrayList(ProcessStatusEnum.APPROVE.getStatus(), ProcessStatusEnum.WRITING.getStatus(), ProcessStatusEnum.SIGNING.getStatus()))
                    .contractNo(request.getContractNo())
                    .timeRangeType(TimeRangeTypeEnum.PROCESS_START_TIME.getType())
                    .beginTimeInMillSec(request.getBeginTimeInMillSec())
                    .endTimeInMillSec(request.getEndTimeInMillSec())
                    .build();
            build.setPageNum(request.getPageNum());
            build.setPageSize(request.getPageSize());
            build.setExcludeFields(Lists.newArrayList(ProcessInfoFilterFieldEnum.FORM.getField(), ProcessInfoFilterFieldEnum.GROUPING_INFO.getField(),
                    ProcessInfoFilterFieldEnum.PROCESS_APPROVAL.getField(), ProcessInfoFilterFieldEnum.SEAL_APPROVAL.getField()));
            DocQueryResult docQueryResult = esClient.query(build);
            if (docQueryResult == null || CollectionUtils.isEmpty(docQueryResult.getProcessInfoList())) {
                return new ProcessTodoListResponse(Collections.emptyList(), COUNT_DEFAULT);
            }

            // 结果列表
            List<ProcessTodoInfoDTO> processInfoList =
                    docQueryResult.getProcessInfoList().stream()
                            .filter(Objects::nonNull)
                            .map(this::buildProcessTodoInfoDTO)
                            .collect(Collectors.toList());

            return new ProcessTodoListResponse(processInfoList, docQueryResult.getTotal());
        }

        QueryWaitMeParam param = new QueryWaitMeParam();
        // 必传参数
        param.setTypes(singletonList(processStatus));
        param.setAccount(new QueryWaitMeParam.Account(userOid, userGid));
        param.setSubject(new QueryWaitMeParam.Account(request.getSubjectId(), subjectGid));
        param.setOnlyStandard(true);
        param.setPageNum(request.getPageNum());
        param.setPageSize(request.getPageSize());
        param.setInSignValidity(true);
        // 非必传参数
        param.setTimeRangeStart(request.getBeginTimeInMillSec());
        param.setTimeRangeEnd(request.getEndTimeInMillSec());
        param.setMatching(request.getFuzzyMatching());
        param.setContractNo(request.getContractNo());
        param.setExcludeFields(ProcessInfoFilterFieldEnum.getNonMainExcludeFields());
        // 查询列表
        PageQueryResult<ProcessInfoSimplifyResult> pageQueryResult =
                docSearchApi.pageQueryWaitMe(param);
        if (pageQueryResult == null || CollectionUtils.isEmpty(pageQueryResult.getList())) {
            return new ProcessTodoListResponse(Collections.emptyList(), COUNT_DEFAULT);
        }

        // 结果列表
        List<ProcessTodoInfoDTO> processInfoList =
                pageQueryResult.getList().stream()
                        .filter(Objects::nonNull)
                        .map(this::buildProcessTodoInfoDTO)
                        .collect(Collectors.toList());

        return new ProcessTodoListResponse(processInfoList, pageQueryResult.getTotal());
    }

    /**
     * 构建待我审批统计对象
     *
     * @param lowCodeOutput
     * @return
     */
    private TypeTodoCountDTO buildApprovalCountDTO(
            PageApprovalFlowOutput lowCodeOutput, Boolean queryApprovalTotalCount) {
        Long allCount =
                Optional.ofNullable(lowCodeOutput)
                        .map(PageApprovalFlowOutput::getTotal)
                        .map(Integer::longValue)
                        .orElse(COUNT_DEFAULT);
        // 用印+合同
        TypeTodoCountDTO allApprovalCount =
                new TypeTodoCountDTO(
                        TodoTypeEnum.WAIT_APPROVAL_ALL.getLabel(),
                        TodoTypeEnum.WAIT_APPROVAL_ALL.getType(),
                        allCount);

        if (Boolean.TRUE.equals(queryApprovalTotalCount)) {
            return allApprovalCount;
        }

        // 待我审批
        return new TypeTodoCountDTO(
                TodoTypeEnum.WAIT_APPROVAL.getLabel(),
                TodoTypeEnum.WAIT_APPROVAL.getType(),
                Lists.newArrayList(allApprovalCount));
    }

    /**
     * 获取合同待办数量
     *
     * @param userOid
     * @param userGid
     * @see ProcessStatusEnum
     * @return Map<processStatus, count>, processStatus参阅:ProcessStatusEnum.getType()
     */
    private Map<Integer, Long> mGetProcessCount(String userOid, String userGid, String subjectOid, String subjectGid, Boolean queryCurrentSubject) {
        TotalWaitMeParam param = new TotalWaitMeParam();
        param.setAccount(new TotalWaitMeParam.Account(userOid, userGid));
        if (queryCurrentSubject) {
            // 查询当前企业条件
            param.setSubject(new TotalWaitMeParam.Account(subjectOid, subjectGid));
        }
        param.setInSignValidity(true);
        param.setTypes(
                Lists.newArrayList(
                        TodoTypeEnum.getBizType(TodoTypeEnum.WAIT_SIGN.getType()),
                        TodoTypeEnum.getBizType(TodoTypeEnum.WAIT_FILL.getType())));

        TotalWaitWeResult totalWaitWeResult = docSearchApi.totalWaitMeTask(param);

        Map<Integer, Long> processCountMap =
                Optional.ofNullable(totalWaitWeResult)
                        .map(TotalWaitWeResult::getResult)
                        .orElse(Collections.emptyMap());
        return processCountMap;
    }

    /**
     * 构建待办合同流程信息
     *
     * @param processInfoSimplify
     * @return
     */
    private ProcessTodoInfoDTO buildProcessTodoInfoDTO(
            ProcessInfoSimplifyResult processInfoSimplify) {
        // 返回值构建
        ProcessTodoInfoDTO processTodoInfoDTO = new ProcessTodoInfoDTO();
        processTodoInfoDTO.setProcessId(processInfoSimplify.getProcessId());
        processTodoInfoDTO.setTitle(processInfoSimplify.getTitle());
        processTodoInfoDTO.setProcessCreateTime(processInfoSimplify.getProcessCreateTime());
        processTodoInfoDTO.setProcessType(processInfoSimplify.getProcessType());
        processTodoInfoDTO.setSignMode(processInfoSimplify.getSignMode());
        processTodoInfoDTO.setDedicatedCloudId(processInfoSimplify.getDedicatedCloudId());
        processTodoInfoDTO.setInitiatorAccount(
                buildProcessNodeAccount(processInfoSimplify.getInitiatorAccount()));
        processTodoInfoDTO.setParticipantAccountList(
                processInfoSimplify.getParticipantAccountList().stream()
                        .map(this::buildProcessNodeAccount)
                        .collect(Collectors.toList()));
        processTodoInfoDTO.getParticipantAccountList().stream().filter(ProcessNodeAccount::isCurrentOperator).map(ProcessNodeAccount::isTransfer).filter(transfer -> transfer).findAny().ifPresent(processTodoInfoDTO::setTransfer);
        if (CollectionUtils.isNotEmpty(processInfoSimplify.getContractFiles())) {
            processTodoInfoDTO.setContractFiles(mapperFactory.getMapperFacade().mapAsList(processInfoSimplify.getContractFiles(), ContractFileBean.class));
        }
        return processTodoInfoDTO;
    }

    private ProcessTodoInfoDTO buildProcessTodoInfoDTO(
            ProcessInfoResult  processInfoResult) {
        // 返回值构建
        ProcessTodoInfoDTO processTodoInfoDTO = new ProcessTodoInfoDTO();
        processTodoInfoDTO.setProcessId(processInfoResult.getProcessId());
        processTodoInfoDTO.setTitle(processInfoResult.getTitle());
        processTodoInfoDTO.setProcessCreateTime(processInfoResult.getProcessCreateTime());
        processTodoInfoDTO.setProcessType(processInfoResult.getProcessType());
        processTodoInfoDTO.setSignMode(processInfoResult.getSignMode());
        processTodoInfoDTO.setDedicatedCloudId(processInfoResult.getDedicatedCloudId());
        processTodoInfoDTO.setProcessStatus(processInfoResult.getProcessStatus());
        processTodoInfoDTO.setInitiatorAccount(
                buildProcessNodeAccount(processInfoResult.getInitiatorAccount()));
        processTodoInfoDTO.setParticipantAccountList(
                processInfoResult.getParticipantAccountList().stream()
                        .map(this::buildProcessNodeAccount)
                        .collect(Collectors.toList()));
        processTodoInfoDTO.getParticipantAccountList().stream().filter(ProcessNodeAccount::isCurrentOperator).map(ProcessNodeAccount::isTransfer).filter(transfer -> transfer).findAny().ifPresent(processTodoInfoDTO::setTransfer);
        if (CollectionUtils.isNotEmpty(processInfoResult.getContractFiles())) {
            processTodoInfoDTO.setContractFiles(mapperFactory.getMapperFacade().mapAsList(processInfoResult.getContractFiles(), ContractFileBean.class));
        }
        processTodoInfoDTO.setCurrentOperatorList(mapperFactory.getMapperFacade().mapAsList(processInfoResult.getCurrentOperatorList(), com.timevale.contractmanager.common.service.bean.ProcessAccount.class));
        return processTodoInfoDTO;
    }

    /**
     * 构建参与人信息
     *
     * @param processAccount
     * @return
     */
    private ProcessNodeAccount buildProcessNodeAccount(ProcessAccount processAccount) {
        // 用户信息
        AccountBean person =
                mapperFactory.getMapperFacade().map(processAccount.getPerson(), AccountBean.class);
        // 主体信息
        AccountBean subject =
                mapperFactory.getMapperFacade().map(processAccount.getSubject(), AccountBean.class);

        ProcessNodeAccount processNodeAccount =
                new ProcessNodeAccount(person, subject, BooleanUtils.toBoolean(processAccount.getCurrentUser()), BooleanUtils.toBoolean(processAccount.getTransfer()));

        return processNodeAccount;
    }

    /**
     * 企业维度聚合统计待办数量
     *
     * @param userOid
     * @param userGid
     * @param type TodoTypeEnum.getType()
     * @see TodoTypeEnum
     * @return
     */
    private List<SubjectTodoCountDTOHolder> aggregateProcess(
            String userOid, String userGid, Integer type) {

        TotalWaitMeParam param = new TotalWaitMeParam();
        param.setAccount(new TotalWaitMeParam.Account(userOid, userGid));
        param.setTypes(singletonList(TodoTypeEnum.getBizType(type)));
        param.setInSignValidity(true);
        // 查询合同聚合统计
        StatisticsSubjectResult statisticsSubjectResult =
                docSearchApi.statisticsSubjectWaitMe(param);

        if (statisticsSubjectResult == null
                || CollectionUtils.isEmpty(statisticsSubjectResult.getSubjectCount())) {
            return Collections.emptyList();
        }

        List<SubjectCountResult> subjectCounts = statisticsSubjectResult.getSubjectCount();
        // 获取主体gid-oid映射，Map<subjectGid, subjectOid>
        Map<String, String> realNameGidOidMap = mappingRealNameGidAndOid(subjectCounts);

        // 获取主体gid-实名组织名称映射，Map<subjectGid, subjectName>
        Map<String, String> subjectGidNameMap =
                mappingSubjectGidAndName(subjectCounts, realNameGidOidMap);

        // 结果转换
        List<SubjectTodoCountDTOHolder> list =
                subjectCounts.stream()
                        .filter(Objects::nonNull)
                        .filter(subjectCount -> Objects.nonNull(subjectCount.getCount()))
                        .map(
                                subjectCount ->
                                        new SubjectTodoCountDTOHolder(
                                                subjectGidNameMap.get(subjectCount.getSubjectGid()),
                                                subjectCount.getSubjectGid(),
                                                realNameGidOidMap.get(subjectCount.getSubjectGid()),
                                                subjectCount.getCount()))
                        .collect(Collectors.toList());

        return list;
    }

    /**
     * 获取企业和名称的映射
     *
     * @param subjectCountResults
     * @param realNameGidOidMap
     * @return
     */
    private Map<String, String> mappingSubjectGidAndName(
            List<SubjectCountResult> subjectCountResults, Map<String, String> realNameGidOidMap) {

        Map<String, String> subjectGidNameMap = new HashMap<>();
        for (SubjectCountResult subjectResult : subjectCountResults) {
            // 实名组织oid
            String realNameOid = realNameGidOidMap.get(subjectResult.getSubjectGid());

            // 获取实名组织名称
            Optional<String> nameOptional =
                    subjectResult.getSubjectCount().stream()
                            .filter(subject -> Objects.equals(subject.getSubjectOid(), realNameOid))
                            .map(SubjectCountResult::getSubjectName)
                            .filter(StringUtils::isNotBlank)
                            .findFirst();

            // 如果实名组织名称为空，尝试获取第一个非空企业的名称
            String subjectName =
                    nameOptional.orElse(
                            subjectResult.getSubjectCount().stream()
                                    .map(SubjectCountResult::getSubjectName)
                                    .filter(StringUtils::isNotBlank)
                                    .findFirst()
                                    .orElse(null));
            subjectGidNameMap.put(subjectResult.getSubjectGid(), subjectName);
        }

        return subjectGidNameMap;
    }

    /**
     * 获取实名组织gid-oid映射
     *
     * @param subjectCount
     * @return
     */
    private Map<String, String> mappingRealNameGidAndOid(List<SubjectCountResult> subjectCount) {
        // 批量获取实名组织
        Set<String> gidSet =
                subjectCount.stream()
                        .map(SubjectCountResult::getSubjectGid)
                        .collect(Collectors.toSet());
        Map<String, String> gidOidMap = userCenterCachedService.mGetRealNameOid(gidSet);

        // 获取实名组织oid
        Function<SubjectCountResult, String> getRealNameOidOrAnyOne =
                subjectOidCount ->
                        Optional.ofNullable(gidOidMap.get(subjectOidCount.getSubjectGid()))
                                .orElse(subjectOidCount.getSubjectCount().get(0).getSubjectOid());
        // 获取gid对应的oid
        Map<String, String> subjectGidOidMap =
                subjectCount.stream()
                        .collect(
                                Collectors.toMap(
                                        SubjectCountResult::getSubjectGid,
                                        getRealNameOidOrAnyOne,
                                        (a, b) -> a));

        return subjectGidOidMap;
    }

    /**
     * 主体维度聚合统计新版审批
     *
     * @param userGid
     * @return
     */
    private List<SubjectTodoCountDTOHolder> aggregateNewApproval(String userGid) {
        if (StringUtils.isBlank(userGid)) {
            return Collections.emptyList();
        }
        // 获取审批聚合统计数据
        SubjectApprovalCountInput input = new SubjectApprovalCountInput();
        input.setAccountGid(userGid);
        SubjectApprovalCountOutput output = approvalInstanceClient.countApprovalBySubject(input);
        List<ApprovalAggregateDTO> counts = output.getAggregates();

        if (CollectionUtils.isEmpty(counts)) {
            log.info("todo approval list is empty, userGid:{}", userGid);
            return Collections.emptyList();
        }

        // 类型转换
        return counts.stream()
                .filter(Objects::nonNull)
                .filter(subjectApprovalCount -> Objects.nonNull(subjectApprovalCount.getCount()))
                .map(
                        subjectApprovalCount ->
                                new SubjectTodoCountDTOHolder(
                                        null,
                                        null,
                                        subjectApprovalCount.getSubjectOid(),
                                        subjectApprovalCount.getCount().longValue()))
                .collect(Collectors.toList());
    }

    /**
     * 格式化主体聚合统计列表
     *
     * @param subjects
     * @param currentUserGid
     * @param type TodoTypeEnum.getType()
     * @see TodoTypeEnum
     * @return
     */
    private SubjectsTodoTotalResponse formatResponse(
            List<SubjectTodoCountDTOHolder> subjects, String currentUserGid, Integer type) {
        // 按照名称排序
        List<SubjectTodoCountDTOHolder> sortedTodoCountList = sortedSubjectByName(subjects);

        // 合同流程要把”个人空间“放到最前面
        if (TodoTypeEnum.isProcess(type)) {
            for (int i = 0; i < sortedTodoCountList.size(); i++) {
                SubjectTodoCountDTOHolder theSubject = sortedTodoCountList.get(i);
                // gid和当前用户gid相同的就是个人空间
                if (StringUtils.equals(theSubject.getSubjectGid(), currentUserGid)) {
                    theSubject.setSubjectName(PERSONAL_SUBJECT_LABEL);
                    sortedTodoCountList.add(0, sortedTodoCountList.remove(i));
                }
            }
        }

        // 构建返回值对象, 这里计算了total
        List<SubjectTodoCountDTO> todoCountDTOList =
                sortedTodoCountList.stream()
                        .map(SubjectTodoCountDTOHolder::get)
                        .collect(Collectors.toList());
        SubjectsTodoTotalResponse response = new SubjectsTodoTotalResponse(todoCountDTOList);

        // 新版审批流程要展示"全部"
        if (TodoTypeEnum.WAIT_APPROVAL_NEW.getType().equals(type)) {
            SubjectTodoCountDTO totalCountTab =
                    new SubjectTodoCountDTO(SUBJECT_ALL_LABEL, SUBJECT_ALL_ID, response.getTotal());
            response.getSubjects().add(0, totalCountTab);
        } else if (TodoTypeEnum.FULFILLMENT.getType().equals(type)){
            SubjectTodoCountDTO totalCountTab =
                    new SubjectTodoCountDTO(SUBJECT_ALL_LABEL, SUBJECT_ALL_ID, subjects.stream().mapToLong(SubjectTodoCountDTOHolder::getCount).sum());
            response.getSubjects().add(0, totalCountTab);
        }
        return response;
    }

    /**
     * 对主体进行排序，按照名字首字母顺序排序
     *
     * @param subjects
     * @return
     */
    private List<SubjectTodoCountDTOHolder> sortedSubjectByName(
            List<SubjectTodoCountDTOHolder> subjects) {
        // 没有名称的企业给一个默认名称
        Consumer<SubjectTodoCountDTOHolder> renameUnnamedSubject =
                subject ->
                        subject.setSubjectName(
                                Optional.ofNullable(subject)
                                        .map(SubjectTodoCountDTOHolder::getSubjectName)
                                        .filter(StringUtils::isNotBlank)
                                        .orElse(UNNAMED_SUBJECT));

        // 按照主体名称的字典序排序，企业名称为空，填充默认名称
        List<SubjectTodoCountDTOHolder> sortedTodoCountList =
                subjects.stream()
                        .peek(renameUnnamedSubject)
                        .sorted(
                                Comparator.comparing(
                                        SubjectTodoCountDTOHolder::getSubjectName,
                                        Collator.getInstance(Locale.CHINESE)))
                        .collect(Collectors.toList());
        return sortedTodoCountList;
    }

    /**
     * 填充主体名称
     *
     * @param subjects
     */
    private void fillSubjectName(List<SubjectTodoCountDTOHolder> subjects) {
        Set<String> subjectOidSet =
                subjects.stream()
                        .filter(subject -> StringUtils.isBlank(subject.getSubjectName()))
                        .map(SubjectTodoCountDTOHolder::getSubjectOid)
                        .collect(Collectors.toSet());
        // 如果主体已经都有名字了，直接返回
        if (CollectionUtils.isEmpty(subjectOidSet)) {
            return;
        }

        // 批量获取主体名称，Map<subjectId, subjectName>
        Map<String, String> subjectNameMap = userCenterCachedService.mGetAccountName(subjectOidSet);

        // 填充主体名称
        for (SubjectTodoCountDTOHolder subject : subjects) {
            String subjectName = subjectNameMap.get(subject.getSubjectOid());
            if (StringUtils.isBlank(subjectName)) {
                log.warn("subject name not found, subjectId: {}", subject.getSubjectOid());
                continue;
            }
            subject.setSubjectName(subjectNameMap.get(subject.getSubjectOid()));
        }
    }

    private ApprovalPageInput buildApprovingInput(String userGid,String subjectGid) {
        ApprovalPageInput input = new ApprovalPageInput();
        input.setPageNum(1);
        input.setPageSize(5);
        input.setQueryType(ApprovalQueryTypeEnum.I_PENDING.getType());
        input.setOperatorGid(userGid);
        input.setOrgGid(subjectGid);

        return input;
    }
}

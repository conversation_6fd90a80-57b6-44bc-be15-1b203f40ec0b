package com.timevale.contractmanager.core.service.opponent;

import com.timevale.contractmanager.core.model.dto.request.opponent.detection.*;
import com.timevale.contractmanager.core.model.dto.response.opponent.detection.*;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.mq.model.opponent.OpponentDetectionExportMsg;


import java.util.List;
import java.util.Optional;

/**
 * @Author:jianyang
 * @since 2021-08-11 11:12
 */
public interface OpponentDetectionService {
	/**
	 * 保存相对方检测设置
	 * @param tenantOid
	 * @param request
	 */
	void saveSetting(String tenantOid, OpponentDetectionSaveSettingRequest request);


	/**
	 * 获取相对方检测设置
	 * @param tenantOid
	 * @return
	 */
	OpponentDetectionSettingResponse getSetting(String tenantOid);

	/**
	 * 开始批量检测
	 * @param tenantOid
	 * @param request
	 * @param operatorOid
	 * @return
	 */
	Integer batchDetection(String tenantOid, OpponentDetectioBatchRequest request, String operatorOid);


	/**
	 * 获取taskinfo
	 * @param tenantOid
	 * @param taskType
	 * @return
	 */
	OpponentDetectioInitDataResponse getOpponentDetectionTaskInfo(String tenantOid, Integer taskType);

	/**
	 * 获取检测任务列表
	 * @param pageSize
	 * @param pageNum
	 * @param tenantOid
	 * @param startDate
	 * @param endTime
	 * @return
	 */
	OpponentDetectioTaskListResponse getDetectionTasks(Integer pageSize,
													   Integer pageNum,
													   String tenantOid,
													   Long startDate,
													   Long endTime);

	/**
	 * 获取任务状态
	 * @param tenantOid
	 * @param detectionTaskId
	 * @return
	 */
	OpponentDetectioTaskResponse getTaskStatus(String tenantOid, String detectionTaskId);

	/**
	 * 检测问题列表
	 * @param tenantOid
	 * @param request
	 * @return
	 */
	OpponentDetectioTaskReportListResponse getDetectionReport(String tenantOid, OpponentDetectionReportRequest request);

	/**
	 * 导出检测任务结果
	 * @param tenantAccount
	 * @param operatorAccount
	 * @param taskId
	 * @param orgName
	 * @param riskLevels
	 * @param problemLevels
	 * @return
	 */
	OpponentDetectionTaskReportExportResponse exportTaskReports(UserAccountDetail tenantAccount,
																UserAccount operatorAccount,
																String taskId,
																String orgName,
																List<Integer> riskLevels,
																List<Integer> problemLevels,
																Boolean genAsyncTask,
																Long startDate,
																Long endDate) throws Exception;

	/**
	 * 任务中心异步导出相对方检测结果
	 * @param exportMsg
	 */
	void asyncExportTaskReports(OpponentDetectionExportMsg exportMsg) throws Exception;

	/**
	 * 停止检测任务
	 * @param tenantOid
	 * @param detectionTaskId
	 */
	void stopTask(String tenantOid, String detectionTaskId);


	/**
	 * 获取工商信息
	 * @param orgName
	 * @param tenantOid
	 * @return
	 */
	OpponentEnterpriseInfoResponse getEnterpriseData( String orgName, String tenantOid, String socialCreditNo);
}

package com.timevale.contractmanager.core.service.offlinecontract.bean.input;

import com.timevale.contractmanager.common.service.bean.offlinecontract.OfflineContract;
import com.timevale.contractmanager.common.service.bean.offlinecontract.OfflineContractExtractConfig;
import com.timevale.mandarin.common.result.ToString;

import lombok.Data;

import java.util.List;

/**
 * 保存线下合同导入记录
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class SaveOfflineContractRecordDTO extends ToString {

    /** 用户oid */
    private String accountOid;
    /** 用户gid */
    private String accountGid;
    /** 用户姓名 */
    private String accountName;
    /** 主体oid */
    private String subjectOid;
    /** 主体gid */
    private String subjectGid;
    /** 主体名称 */
    private String subjectName;
    /** 归档菜单id */
    private String menuId;
    /** 导入方式 */
    private String importWay;
    /** 合同信息提取方式 */
    private String extractWay;
    /** 导入端 */
    private String clientId;
    /** 线下合同列表 */
    private List<OfflineContract> contracts;
    /** 合同信息提取配置 */
    private OfflineContractExtractConfig extractConfig;

    /** 上传文件后关联的合同 */
    private String masterProcessId;
    
    /** 专属云项目id */
    private String dedicatedCloudId;
}

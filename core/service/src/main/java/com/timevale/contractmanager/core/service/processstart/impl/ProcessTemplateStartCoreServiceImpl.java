package com.timevale.contractmanager.core.service.processstart.impl;

import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.enums.ProcessStartMode;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.processstart.ProcessStartCoreService;
import com.timevale.contractmanager.core.service.processstart.impl.base.ProcessStartCoreBaseService;
import com.timevale.dayu.sdk.annotation.AuditLogAnnotation;
import com.timevale.doccooperation.service.enums.FlowTypeEnum;
import com.timevale.doccooperation.service.input.flow.BuildTemplateOnlySignDataInput;
import com.timevale.doccooperation.service.input.flow.StartCooperationFlowInput;
import com.timevale.doccooperation.service.input.flow.StartFlowBaseInput;
import com.timevale.doccooperation.service.result.CooperationFilledSignInfoResult;
import com.timevale.doccooperation.service.result.FlowTemplateStructResult;
import com.timevale.doccooperation.service.result.flow.BuildTemplateOnlySignDataResult;
import com.timevale.doccooperation.service.result.flow.StartFlowResult;
import com.timevale.docmanager.service.model.StructComponent;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 模板发起业务类
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Slf4j
@Service
public class ProcessTemplateStartCoreServiceImpl extends ProcessStartCoreBaseService
        implements ProcessStartCoreService {

    @Override
    public ProcessStartMode startMode() {
        return ProcessStartMode.TEMPLATE_START;
    }

    @AuditLogAnnotation(
            firstModule = AuditLogConstant.SpEL.PROCESS_START_FIRST_MODULE,
            secondaryModule = AuditLogConstant.SpEL.PROCESS_START_SECONDARY_MODULE,
            appid = "#request.appId",
            enterpriseSpaceUnique1 = "#request.tenantAccount.accountOid",
            resourceEntSpaceUnique = "#request.tenantAccount.accountOid",
            userUnique1 = "#request.initiatorAccount.accountOid",
            resourceName = "#request.processName",
            condition = "{{#_result != null}}",
            result = "{{#_result != null && T(com.timevale.mandarin.base.util.StringUtils).isNotBlank(#_result.processId) ? " + AuditLogConstant.RESULT + "}}",
            detailTactics = "1",
            postHandle = "auditLogProcessStartHandle")
    @Override
    public ProcessStartResult start(ProcessStartCoreRequest request) {
        // 发起必要参数校验
        validParam(request);
        //判断走同步还是异步
        CoreStartFlowAction action = request.isAsyncStart() ?
                this::doAsyncStart :
                this::doStart;
        // 发起流程
        return startFlow(request, action);
    }

    private ProcessStartResult doStart(String processId, ProcessStartCoreRequest startRequest) {
        try {
            // 构建创建模板发起流程input
            StartCooperationFlowInput startCooperationFlowInput =
                    processStartInputBuilder.buildStartCooperationFlowInput(processId, startRequest);
            // 或签方用户重复校验
            startCooperationFlowInput.getCooperationers().stream()
                    .map(StartFlowBaseInput.FlowParticipant::obtainAccounts)
                    .forEach(this::checkDuplicateParticipantAccounts);

            // 判断是否使用新版发起方式
            if (!startRequest.needFill()) {
                // 获取直接发起签署数据
                BuildTemplateOnlySignDataInput directStartSignInput = new BuildTemplateOnlySignDataInput();
                directStartSignInput.setStartInput(startCooperationFlowInput);
                directStartSignInput.setBatchStartCheck(false);
                BuildTemplateOnlySignDataResult startSignResult =
                        cooperationService.buildTemplateOnlySignData(directStartSignInput);
                // 如果是指定位置直接发起签署场景， 清空临时流程模板id
                if (ProcessStartScene.DIRECT_START.getScene() == startRequest.getStartScene()) {
                    startSignResult.getSignInfo().setFlowTemplateId(null);
                }
                // 发起合同审批或签署流程
                return startApprovalOrSignFlow(
                        startSignResult.getSignInfo(), buildUrlExtraParam(startRequest));
            }
            // 处理填写人通知方式
            processStartParticipantHandler.handleCooperatorNoticeTypes(startRequest, startCooperationFlowInput);
            // 创建填写子流程
            StartFlowResult result =
                    cooperationService.commonStartCooperationFlow(startCooperationFlowInput);
            // 如果当前子流程为填写流程且状态为直接发起签署， 则触发发起签署逻辑
            if (FlowTypeEnum.COOPERATION.getType().equals(result.getFlowType()) && result.isSkipFillStartSign()) {
                // 获取填写完成后的签署信息
                CooperationFilledSignInfoResult filledSignInfo =
                        cooperationService.getCooperationFilledSignInfo(result.getFlowId());
                // 发起签署
                return startApprovalOrSignFlow(
                        filledSignInfo.getSignInfo(), buildUrlExtraParam(startRequest));
            }
            return buildStartResponse(processId, result);
        } catch (Exception e) {
            // 如果是发起时指定了processId入参，则流程创建失败时删除该流程
            if (StringUtils.isNotBlank(processId)) {
                processStartHelper.deleteProcess(processId);
            }
            throw e;
        }
    }

    private ProcessStartResult doAsyncStart(String processId, ProcessStartCoreRequest startRequest) {
        try {
            // 构建创建模板发起流程input
            StartCooperationFlowInput startCooperationFlowInput =
                    processStartInputBuilder.buildStartCooperationFlowInput(processId, startRequest);
            if (startRequest.needFill()) {
                // 处理填写人通知方式
                processStartParticipantHandler.handleCooperatorNoticeTypes(startRequest, startCooperationFlowInput);
            }
            // 补充预填数据的模板ID
            complementTemplateIdInPreFillContent(startCooperationFlowInput);
            // 异步创建子流程
            StartFlowResult cooperationResult = cooperationService.asyncStartCooperationFlow(startCooperationFlowInput, startRequest.isEpaasTemplateTag());
            // 缓存异步发起操作用户
            String operatorId = startRequest.getOperatorAccount().getAccountOid();
            CacheUtil.degradeSet(CacheUtil.getProcessAsyncStartCacheKey(processId, operatorId), operatorId, 1, TimeUnit.HOURS);
            return buildStartResponse(processId, cooperationResult);
        } catch (Exception e) {
            // 如果是发起时指定了processId入参，则流程创建失败时删除该流程
            if (StringUtils.isNotBlank(processId)) {
                processStartHelper.deleteProcess(processId);
            }
            throw e;
        }
    }

    /**
     * 补充预填数据的模板ID
     * @param startCooperationFlowInput
     */
    private void complementTemplateIdInPreFillContent(StartCooperationFlowInput startCooperationFlowInput) {
        if(CollectionUtils.isEmpty(startCooperationFlowInput.getPreFillContents())){
            //如果没有预填数据，直接结束
            return;
        }
        UserAccount tenantAccount = new UserAccount();
        tenantAccount.setAppId(startCooperationFlowInput.getAppId());
        tenantAccount.setAccountOid(startCooperationFlowInput.getSubjectOid());
        tenantAccount.setAccountGid(startCooperationFlowInput.getSubjectGid());

        FlowTemplateStructResult flowTemplateStructs = flowTemplateService.getFlowTemplateStructs(startCooperationFlowInput.getFlowTemplateId(), tenantAccount);
        Map<String, String> structIdAndTemplateIdMap = flowTemplateStructs.getStructComponents().stream().collect(Collectors.toMap(StructComponent::getId, StructComponent::getTemplateId));

        startCooperationFlowInput.getPreFillContents().forEach(preFillContent ->
                preFillContent.setTemplateId(structIdAndTemplateIdMap.get(preFillContent.getStructId()))
        );
    }
}

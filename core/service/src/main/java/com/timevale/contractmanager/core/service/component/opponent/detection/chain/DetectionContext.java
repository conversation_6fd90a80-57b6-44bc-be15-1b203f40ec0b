package com.timevale.contractmanager.core.service.component.opponent.detection.chain;

import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.DetectionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-08-17 11:50
 */
@Component
@Slf4j
public class DetectionContext {

	@Autowired
	private List<DetectionHandler> detectionHandlers;

	public List<OpponentDetectionChainResultBO> execute(List<Integer> items, DetectionChainBO chainBO, List<OpponentDetectionChainResultBO> chainResults){
		for (DetectionHandler detectionHandler : detectionHandlers){
			if(!detectionHandler.filter(items)){
				continue;
			}
			detectionHandler.handler(chainBO, chainResults);
		}
		return chainResults;
	}
}

package com.timevale.contractmanager.core.service.transfer.impl;

import static com.timevale.contractmanager.common.service.enums.ProcessTransferSceneEnum.RETIRE_TRANSFER;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.USER_TRANSFER_TASK_EXIST;
import static com.timevale.contractmanager.common.utils.config.Constants.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.core.model.bo.transfer.ProcessTransferNoticeBO;
import com.timevale.contractmanager.core.model.bo.transfer.SingleTransferResultBO;
import com.timevale.contractmanager.core.model.bo.transfer.TransferUserListBO;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.transfer.TransferResultDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.mq.transfer.AccountProcessTransferProducer;
import com.timevale.contractmanager.core.service.mq.transfer.UniProcessTransferProducer;
import com.timevale.contractmanager.core.service.mq.transfer.UserProcessTransferTaskProducer;
import com.timevale.contractmanager.core.service.mq.transfer.msg.AccountProcessTransferMsg;
import com.timevale.contractmanager.core.service.mq.transfer.msg.UniProcessTransferMsg;
import com.timevale.contractmanager.core.service.mq.transfer.msg.UserProcessTransferTaskMsg;
import com.timevale.contractmanager.core.service.transfer.TransferAbstractBizService;
import com.timevale.contractmanager.core.service.transfer.TransferBizService;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.contractmanager.core.service.util.TransferUtils;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.*;
import com.timevale.saas.common.manage.common.service.enums.TaskStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.TaskTypeEnum;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskAddInput;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskUpdateInput;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskAddBean;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskBizInfo;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.result.DocQueryCountResult;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/11/25
 */
@Slf4j
@Service
public class TransferProcessBizServiceImpl extends TransferAbstractBizService
        implements TransferBizService {
    @Autowired private EsClient esClient;
    @Autowired private SaasCommonClient saasCommonClient;
    @Autowired private AccountProcessTransferProducer transferProducer;
    @Autowired private UniProcessTransferProducer uniProcessTransferProducer;
    @Autowired private UserProcessTransferTaskProducer userProcessTransferTaskProducer;
    @Autowired private LockFactory lockFactory;

    @Override
    public Boolean isSupport(Integer transferScene) {
        return null != transferScene && TransferSceneEnum.PROCESS.getCode() == transferScene;
    }

    @Override
    public TransferResultDTO transfer(TransferBizContext transferBizContext) {
        boolean isProcessTransfer = transferBizContext.isPartTransfer();
        TransferBizContext.TransferUserInfo transferUserInfo =
                transferBizContext.getTransferUserInfo();
        // 全量转交校验频率： 目前是一天只能一次
        if (!isProcessTransfer) {
            checkUserIsTransferred(
                    transferUserInfo.getTenantAccount().getAccountOid(),
                    transferUserInfo.getTransferAccount().stream()
                            .map(UserAccountDetail::getAccountOid)
                            .distinct()
                            .collect(Collectors.toList()));
        }
        // 创建任务中心任务
        boolean goToTaskCenter = false;
        try {
            goToTaskCenter = createTransferTask(transferBizContext);
            cacheProcessTransferNotifyData(
                    transferBizContext.getTaskId(), transferBizContext.getTransferUserInfo(),transferBizContext.isSystemTransfer());
        } catch (Exception e) {
            log.error("create transfer task or notify  error", e);
        }
        if (isProcessTransfer) {
            // 处理批量合同转交
            processBatchProcessTransfer(
                    transferBizContext.getTaskId(),
                    transferBizContext.getTransferProcessListInfo(),
                    transferUserInfo);
            addProcessTransferNotifyCount(
                    transferBizContext.getTaskId(),
                    transferBizContext
                            .getTransferProcessListInfo()
                            .getTransferProcessList()
                            .size());
        } else {
            // 人维度转交
            processBatchUserTransfer(transferBizContext.getTaskId(), transferUserInfo);
            setUserIsTransferredCache(
                    transferUserInfo.getTenantAccount().getAccountOid(),
                    transferUserInfo.getTransferAccount());
        }
        return new TransferResultDTO(goToTaskCenter);
    }

    @Override
    public Map<String, Long> transferCount(
            UserAccountDetail tenantAccount, List<Account> userAccounts) {
        if (null == tenantAccount || CollectionUtils.isEmpty(userAccounts)) {
            return Maps.newHashMap();
        }
        // es搜索用户的流程数量
        List<DocQueryCountResult> docQueryCountResults =
                esClient.batchTransferCountUserProcess(
                        tenantAccount.buildAccount(), userAccounts, null, null);
        return CollectionUtils.isEmpty(docQueryCountResults)
                ? Maps.newHashMap()
                : docQueryCountResults.stream()
                        .filter(
                                query ->
                                        query.getPerson() != null
                                                && StringUtils.isNotBlank(
                                                        query.getPerson().getOid()))
                        .collect(
                                Collectors.toMap(
                                        query -> query.getPerson().getOid(),
                                        DocQueryCountResult::getTotal,
                                        (k1, k2) -> k1));
    }

    @Override
    public OrgDeptListResponse transferUserList(TransferUserListBO transferUserListBO) {
        return super.getUserListDefault(transferUserListBO);
    }

    @Override
    public Integer transferToUserCount(String tenantId, String accountOid) {
        List<Object> counts =
                TedisUtil.hash()
                        .multiGet(
                                USER_TRANSFER_COUNT_PREFIX + accountOid,
                                Lists.newArrayList(TRANSFER_ALL_PROCESS_COUNT_PREFIX + tenantId));
        if (CollectionUtils.isEmpty(counts)) {
            // todo 为什么返回null
            return 0;
        }
        return NumberUtils.toInt(counts.get(0));
    }

    @Override
    public Long addTransferToUserCount(String tenantId, String accountOid) {
        try {
            String key = USER_TRANSFER_COUNT_PREFIX + accountOid;
            TedisUtil.expire(key, 60, TimeUnit.DAYS);
            return TedisUtil.hash()
                    .increment(key, TRANSFER_ALL_PROCESS_COUNT_PREFIX + tenantId, 1L);
        } catch (Exception e) {
            log.warn(
                    "process addTransferToUserCount failed tenantId:{},accountOid:{}",
                    tenantId,
                    accountOid,
                    e);
        }
        return 0L;
    }

    @Override
    public void singleTransferResultCallback(SingleTransferResultBO transferResultBO) {
        if (null == transferResultBO
                || StringUtils.isBlank(transferResultBO.getTaskId())
                || StringUtils.isBlank(transferResultBO.getTransferUser())) {
            return;
        }
        String taskId = transferResultBO.getTaskId();
        String transferUserOid = transferResultBO.getTransferUser();
        String transferTaskHashKey = CacheUtil.userTransferHashKey(taskId);
        // 获取转交任务缓存信息
        Map<Object, Object> taskCacheMap = TedisUtil.hash().entries(transferTaskHashKey);
        if (CollectionUtils.isEmpty(taskCacheMap)) {
            return;
        }
        Object userProcessCount = TedisUtil.hash().get(transferTaskHashKey, transferUserOid);
        if (null == userProcessCount) {
            // 当前用户无转交任务
            return;
        }
        if (transferResultBO.isForceCloseTask()) {
            // 强制关闭task数据直接设置为0
            TedisUtil.hash()
                    .put(
                            transferTaskHashKey,
                            transferUserOid,
                            StringUtils.isBlank(transferResultBO.getFailMessage())
                                    ? 0
                                    : transferResultBO.getFailMessage());
            // 强制获取数据
            taskCacheMap = TedisUtil.hash().entries(transferTaskHashKey);
            userProcessCount = TedisUtil.hash().get(transferTaskHashKey, transferUserOid);
        }
        if (Objects.equals(userProcessCount, Constants.USER_TASK_ADD)) {
            log.info(
                    "当前用户任务数量不正确不做处理 taskId:{} transferUserOid:{} userProcessCount:{}",
                    taskId,
                    transferUserOid,
                    userProcessCount);
            return;
        }
        if (userProcessCount instanceof Integer) {
            TedisUtil.hash().increment(transferTaskHashKey, transferUserOid, -1);
            taskCacheMap = TedisUtil.hash().entries(transferTaskHashKey);
            if (CollectionUtils.isEmpty(taskCacheMap)) {
                return;
            }
        }
        Object transferUserCount = taskCacheMap.get(transferUserOid);
        if (!isCacheTaskFinish(transferUserCount)) {
            log.info(
                    "当前转交用户任务未完成 无需更新任务中心 taskId:{} transferUserOid:{} transferUserCount:{}",
                    taskId,
                    transferUserOid,
                    transferUserCount);
            // 当前转交用户任务未完成，直接返回，无需更新任务中心
            return;
        }
        finishTransferTask(taskId, taskCacheMap, transferResultBO.getTenantId(), transferUserOid);
    }

    /** 校验用户当天是否已经做过全量转交 */
    private void checkUserIsTransferred(String tenantId, List<String> transferAccountOidList) {
        try {
            String prefix = Constants.CHECK_USER_IS_TRANSFERRED_PREFIX + tenantId + ":";
            List<Object> userTransferred =
                    TedisUtil.mGet(
                            transferAccountOidList.stream()
                                    .map(oid -> prefix + oid)
                                    .collect(Collectors.toList())
                                    .toArray(new String[transferAccountOidList.size()]));
            if (userTransferred != null && userTransferred.stream().anyMatch(Objects::nonNull)) {
                throw new BizContractManagerException(USER_TRANSFER_TASK_EXIST);
            }
        } catch (BizContractManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error(
                    "checkUserIsTransferred error tenantId {} transferAccountOidList {}",
                    tenantId,
                    JSON.toJSONString(transferAccountOidList),
                    e);
        }
    }

    /**
     * 创建转交任务
     *
     * @param transferBizContext 转交上下文信息
     */
    public boolean createTransferTask(TransferBizContext transferBizContext) {
        TransferBizContext.TransferUserInfo transferUserInfo =
                transferBizContext.getTransferUserInfo();
        String taskId = transferBizContext.getTaskId();
        TransferBizContext.TransferProcessListInfo transferProcessList =
                transferBizContext.getTransferProcessListInfo();
        List<UserAccountDetail> transferUsers = transferUserInfo.getTransferAccount();
        // 任务数
        long taskTotal = 0;
        // 转交任务的用户名称
        Set<String> transferUserNames = new HashSet<>();
        // 任务处理的缓存数据
        Map<String, Object> taskCacheMap = Maps.newHashMap();
        // 设置缓存数据及计算任务数
        if (CollectionUtils.isNotEmpty(transferUsers)) {
            transferUsers.forEach(
                    user -> {
                        taskCacheMap.put(user.getAccountOid(), Constants.USER_TASK_ADD);
                        transferUserNames.add(
                                StringUtils.isBlank(user.getAccountName())
                                        ? "-"
                                        : user.getAccountName());
                    });
            taskTotal += transferUsers.size();
        }
        if (transferProcessList != null) {
            taskCacheMap.put(
                    transferProcessList.getTransferAccountOid(),
                    transferProcessList.getTransferProcessList().size());
            transferUserNames.add(
                    transferUserInfo.getTransferProcessAccount() == null
                                    || StringUtils.isBlank(
                                            transferUserInfo
                                                    .getTransferProcessAccount()
                                                    .getAccountName())
                            ? "-"
                            : transferUserInfo.getTransferProcessAccount().getAccountName());
            taskTotal += 1;
        }

        if (taskTotal == 0) {
            return false;
        }
        // 原来逻辑
        if (MapUtils.isNotEmpty(taskCacheMap)) {
            // 添加转交任务用户的缓存
            TedisUtil.expire(CacheUtil.userTransferHashKey(taskId), 1, TimeUnit.DAYS);
            TedisUtil.hash().putAll(CacheUtil.userTransferHashKey(taskId), taskCacheMap);
        }
        if (TransferUtils.isSystemTaskId(taskId)) {
            // 系统任务不创建任务中心任务
            return false;
        }
        generateTaskCenterTask(taskId, taskTotal, transferUserNames, transferUserInfo);
        log.info("转交任务创建完成，bizId {} total {}", taskId, taskTotal);
        return true;
    }

    private void generateTaskCenterTask(
            String taskId,
            long taskTotal,
            Set<String> transferUserNames,
            TransferBizContext.TransferUserInfo transferUserInfo) {
        UserAccountDetail operator = transferUserInfo.getOperatorAccount();
        UserAccountDetail tenantAccount = transferUserInfo.getTenantAccount();
        SaasTaskAddInput taskAddInput = new SaasTaskAddInput();
        List<TaskAddBean> taskAddBeans = new ArrayList<>();
        TaskAddBean taskAddBean = new TaskAddBean();
        taskAddBeans.add(taskAddBean);
        taskAddBean.setType(TaskTypeEnum.PROCESS_TRANSFER.getType());
        taskAddBean.setBizId(taskId);
        taskAddBean.setName(
                parseTransferTaskName(transferUserNames, transferUserInfo.getTransferToAccount()));
        TaskBizInfo taskBizInfo = new TaskBizInfo();
        taskBizInfo.setOrganizationId(tenantAccount.getAccountOid());
        taskAddBean.setBizInfo(taskBizInfo);
        taskAddBean.setTotal(taskTotal);
        taskAddBean.setAccountOid(operator.getAccountOid());
        taskAddBean.setAccountGid(operator.getAccountGid());
        taskAddInput.setTasks(taskAddBeans);
        saasCommonClient.addTasks(taskAddInput);
    }

    /** 设置用户维度转交任务名称 */
    private String parseTransferTaskName(
            Set<String> transferUserNames, UserAccountDetail transferToAccount) {
        int total = CollectionUtils.isEmpty(transferUserNames) ? 0 : transferUserNames.size();
        String transferUserName =
                total < 1
                        ? "_"
                        : (total > 3
                                ? Joiner.on(",")
                                                .join(
                                                        Lists.newArrayList(transferUserNames)
                                                                .subList(0, 3))
                                        + "等"
                                : Joiner.on(",").join(transferUserNames));
        return String.format(
                TRANSFER_TASK_NAME,
                transferUserName,
                total,
                transferToAccount != null ? transferToAccount.getAccountName() : "-");
    }

    /**
     * 缓存合同转交的通知数据
     *
     * @param transferUserInfo 转交相关用户账户信息
     */
    private void cacheProcessTransferNotifyData(
            String taskId, TransferBizContext.TransferUserInfo transferUserInfo,boolean isSystemTransfer) {
        ProcessTransferNoticeBO noticeBO =
                ProcessTransferNoticeBO.builder()
                        .transferToAccount(transferUserInfo.getTransferToAccount())
                        .operatorAccount(transferUserInfo.getOperatorAccount())
                        .tenantAccount(transferUserInfo.getTenantAccount())
                        .isSystemTransfer(isSystemTransfer)
                        .build();
        TedisUtil.set(
                PROCESS_TRANSFER_NOTIFY_DATA_PREFIX + taskId,
                JSON.toJSONString(noticeBO),
                1,
                TimeUnit.DAYS);
    }

    /**
     * 处理批量用户转交
     *
     * @param transferUserInfo 转交相关账户信息
     */
    private void processBatchUserTransfer(
            String taskId, TransferBizContext.TransferUserInfo transferUserInfo) {
        List<AccountProcessTransferMsg> accountProcessTransferMsgs =
                generateUserTransferMsgs(taskId, transferUserInfo);
        if (CollectionUtils.isEmpty(accountProcessTransferMsgs)) {
            return;
        }
        accountProcessTransferMsgs.forEach(
                msg -> transferProducer.sendMessage(JsonUtils.obj2json(msg)));
    }

    /**
     * 生成批量转交用户的消息体
     *
     * @param transferUserInfo 转交相关账户信息
     * @return 转交处理的消息体，转交用户不存在则返回null
     */
    private List<AccountProcessTransferMsg> generateUserTransferMsgs(
            String taskId, TransferBizContext.TransferUserInfo transferUserInfo) {
        if (CollectionUtils.isEmpty(transferUserInfo.getTransferAccount())) {
            return null;
        }
        return transferUserInfo.getTransferAccount().stream()
                .map(
                        account -> {
                            AccountProcessTransferMsg transferMsg =
                                    new AccountProcessTransferMsg(
                                            transferUserInfo.getOperatorAccount().getAccountOid(),
                                            transferUserInfo.getTenantAccount().getAccountOid(),
                                            transferUserInfo.getTenantAccount().getAccountUid(),
                                            transferUserInfo.getTenantAccount().getAccountGid(),
                                            transferUserInfo.getTenantAccount().getAccountName(),
                                            transferUserInfo.getTenantAccount().isOrganize()
                                                    ? SubjectTypeEnum.ORG.getType()
                                                    : SubjectTypeEnum.PERSON.getType(),
                                            account.getAccountOid(),
                                            account.getAccountUid(),
                                            account.getAccountGid(),
                                            account.obtainAccountName(),
                                            account.account(),
                                            transferUserInfo.getTransferToAccount().getAccountOid(),
                                            transferUserInfo.getTransferToAccount().getAccountUid(),
                                            transferUserInfo.getTransferToAccount().getAccountGid(),
                                            transferUserInfo
                                                    .getTransferToAccount()
                                                    .obtainAccountName(),
                                            transferUserInfo.getTransferToAccount().account());
                            transferMsg.setUniqueId(UUIDUtil.genUUID());
                            transferMsg.setTransferScene(RETIRE_TRANSFER.getScene());
                            transferMsg.setTaskId(taskId);

                            return transferMsg;
                        })
                .collect(Collectors.toList());
    }

    /** 设置校验用户当天是否已经做过全量转交的缓存 */
    private void setUserIsTransferredCache(
            String tenantId, List<UserAccountDetail> transferAccountOidList) {
        if (CollectionUtils.isEmpty(transferAccountOidList)) {
            return;
        }
        List<String> transferOidList =
                transferAccountOidList.stream()
                        .map(UserAccountDetail::getAccountOid)
                        .collect(Collectors.toList());
        try {
            String prefix = Constants.CHECK_USER_IS_TRANSFERRED_PREFIX + tenantId + ":";

            transferOidList.forEach(oid -> TedisUtil.set(prefix + oid, 1, 6, TimeUnit.HOURS));
        } catch (Exception e) {
            log.error(
                    "setUserIsTransferredCache error tenantId {} transferAccountOidList {}",
                    tenantId,
                    JSON.toJSONString(transferOidList),
                    e);
        }
    }

    /**
     * 处理批量流程转交
     *
     * @param
     */
    private void processBatchProcessTransfer(
            String taskId,
            TransferBizContext.TransferProcessListInfo transferProcessList,
            TransferBizContext.TransferUserInfo TransferUserInfo) {
        transferProcessList
                .getTransferProcessList()
                .forEach(
                        processId -> {
                            uniProcessTransferProducer.sendMessage(
                                    JSONObject.toJSONString(
                                            generateUniProcessTransferMsg(
                                                    taskId, processId, TransferUserInfo)));
                        });
    }

    private UniProcessTransferMsg generateUniProcessTransferMsg(
            String taskId, String processId, TransferBizContext.TransferUserInfo transferUserInfo) {
        UniProcessTransferMsg transferMsg = new UniProcessTransferMsg();
        transferMsg.setTaskId(taskId);
        transferMsg.setProcessId(processId);
        transferMsg.setOperatorId(transferUserInfo.getOperatorAccount().getAccountOid());
        transferMsg.setTenantId(transferUserInfo.getTenantAccount().getAccountOid());
        transferMsg.setTenantUid(transferUserInfo.getTenantAccount().getAccountUid());
        transferMsg.setTenantGid(transferUserInfo.getTenantAccount().getAccountGid());
        transferMsg.setTenantName(transferUserInfo.getTenantAccount().getAccountName());
        transferMsg.setTenantType(
                transferUserInfo.getTenantAccount().isOrganize()
                        ? SubjectTypeEnum.ORG.getType()
                        : SubjectTypeEnum.PERSON.getType());
        transferMsg.setOriginalAccountId(
                transferUserInfo.getTransferProcessAccount().getAccountOid());
        transferMsg.setOriginalAccountUid(
                transferUserInfo.getTransferProcessAccount().getAccountUid());
        transferMsg.setOriginalAccountGid(
                transferUserInfo.getTransferProcessAccount().getAccountGid());
        transferMsg.setOriginalAccountName(
                transferUserInfo.getTransferProcessAccount().getAccountName());
        transferMsg.setOriginalAccount(transferUserInfo.getTransferProcessAccount().account());
        transferMsg.setTransferToAccountId(transferUserInfo.getTransferToAccount().getAccountOid());
        transferMsg.setTransferToAccountUid(
                transferUserInfo.getTransferToAccount().getAccountUid());
        transferMsg.setTransferToAccountGid(
                transferUserInfo.getTransferToAccount().getAccountGid());
        transferMsg.setTransferToAccountName(
                transferUserInfo.getTransferToAccount().getAccountName());
        transferMsg.setTransferToAccount(transferUserInfo.getTransferToAccount().account());
        return transferMsg;
    }

    /**
     * 添加合同转交的通知次数
     *
     * @param taskId 任务id
     * @param count 总数
     */
    private void addProcessTransferNotifyCount(String taskId, long count) {
        TedisUtil.expire(PROCESS_TRANSFER_NOTIFY_TOTAL_PREFIX + taskId, 1, TimeUnit.DAYS);
        TedisUtil.string().increment(PROCESS_TRANSFER_NOTIFY_TOTAL_PREFIX + taskId, count);
    }

    /**
     * 缓存任务是否完成： 当前转交人正在被转交的数量是否小于等于0
     *
     * @param count
     * @return
     */
    private boolean isCacheTaskFinish(Object count) {
        // 任务进行中
        if (count instanceof Integer) {
            // 判断任务是否结束
            return (int) count < 1;
        }
        // 任务失败
        return !Objects.equals(count, Constants.USER_TASK_ADD);
    }

    /**
     * 任务是否失败
     *
     * @param count
     * @return
     */
    private boolean isCacheTaskError(Object count) {
        return !(count instanceof Integer) && !Objects.equals(count, Constants.USER_TASK_ADD);
    }

    /**
     * 关闭转交任务
     *
     * @param taskId 任务id
     * @param taskCacheMap 转交任务缓存信息
     * @param tenantId 转交主体信息
     * @param transferUserOid 转交用户信息
     */
    private void finishTransferTask(
            String taskId,
            Map<Object, Object> taskCacheMap,
            String tenantId,
            String transferUserOid) {
        // 完成数量
        long taskDong = 0L;
        long errorDong = 0L;
        Object reason = null;

        for (Object p : taskCacheMap.values()) {
            if (isCacheTaskFinish(p)) {
                taskDong++;
            }
            if (isCacheTaskError(p)) {
                errorDong++;
                reason = p;
            }
        }
        // 任务是否已经终态
        TaskStatusEnum finalStatus = null;
        // 任务完成
        if ((long) taskCacheMap.size() == taskDong) {
            finalStatus =
                    errorDong == 0L
                            ? TaskStatusEnum.FINISH
                            : taskDong == errorDong
                                    ? TaskStatusEnum.FAILED
                                    : TaskStatusEnum.PARTIAL_FAILED;
        }
        //非系统转交需要更新任务中心任务
        if(!TransferUtils.isSystemTaskId(taskId)){
            SaasTaskUpdateInput saasInput = new SaasTaskUpdateInput();
            saasInput.setBizId(taskId);
            saasInput.setType(TaskTypeEnum.PROCESS_TRANSFER.getType());
            saasInput.setDone(taskDong);
            saasInput.setStatus(finalStatus == null ? null : finalStatus.getType());
            saasInput.setReason(reason != null ? String.valueOf(reason) : null);

            log.info(
                    "转交任务更新 bizId {} transferUserOid {} taskDong {} finalStatus {}",
                    taskId,
                    transferUserOid,
                    taskDong,
                    finalStatus);
            saasCommonClient.updateTask(saasInput); 
        }
        
        if (finalStatus == TaskStatusEnum.FINISH) {
            doAfterTransferFinish(taskId, tenantId, transferUserOid);
        }
    }

    /** 任务中心任务完成后执行的后续操作 1 发送用户任务转交完成消息 2 被转交人收到转交通知信息 */
    private void doAfterTransferFinish(String taskId, String tenantId, String transferUserOid) {
        Elock lock = null;
        try {
            lock = lockFactory.getLock(TRANSFER_TASK_CACHE_FINISH_LOCK_PREFIX + taskId);
            boolean isLock = lock.tryLock(2, TimeUnit.MILLISECONDS);
            boolean isKeyExist = TedisUtil.tedis().hasKey(CacheUtil.userTransferHashKey(taskId));
            log.info(
                    "doAfterTransferFinish taskId {} isLock {} isKeyExist {}",
                    taskId,
                    isLock,
                    isKeyExist);
            if (!isLock || !isKeyExist) {
                return;
            }
            try {
                String templateName = TransferUtils.isSystemTaskId(taskId) ? TransferSceneEnum.SYSTEM_PROCESS.getNotifyTemplateName() : TransferSceneEnum.PROCESS.getNotifyTemplateName();
                userProcessTransferTaskProducer.sendMessage(
                        JSON.toJSONString(
                                new UserProcessTransferTaskMsg(
                                        taskId,
                                        tenantId,
                                        transferUserOid,
                                        TaskStatusEnum.FINISH.getType())));
                sendProcessTransferNotify(
                        getTransferNoticeBOFromCache(PROCESS_TRANSFER_NOTIFY_DATA_PREFIX + taskId),
                        getProcessTransferNotifyCount(taskId),
                        templateName);
                // 通知完成了删除缓存，避免重复操作
                TedisUtil.delete(CacheUtil.userTransferHashKey(taskId));
            } catch (Exception e) {
                log.error("doAfterFinish error", e);
            }
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取合同转交的通知次数
     *
     * @param taskId
     * @return
     */
    private long getProcessTransferNotifyCount(String taskId) {
        Object count = TedisUtil.get(PROCESS_TRANSFER_NOTIFY_TOTAL_PREFIX + taskId);
        return NumberUtils.toLong(count);
    }
}

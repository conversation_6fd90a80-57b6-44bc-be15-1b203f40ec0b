package com.timevale.contractmanager.core.service.sharesign;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignProcessDO;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignTaskDO;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignUrlDO;
import com.timevale.contractmanager.common.dal.dao.sharesign.ShareSignProcessDAO;
import com.timevale.contractmanager.common.dal.dao.sharesign.ShareSignTaskDAO;
import com.timevale.contractmanager.common.dal.dao.sharesign.ShareSignUrlDAO;
import com.timevale.contractmanager.common.dal.query.shareSign.ShareSignTaskListParam;
import com.timevale.contractmanager.common.service.api.ContractProcessGroupService;
import com.timevale.contractmanager.common.service.enums.GroupTypeEnum;
import com.timevale.contractmanager.common.service.enums.sharesign.ShareSignProcessStatusEnum;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.model.CreateProcessGroupModel;
import com.timevale.contractmanager.common.service.result.CreateProcessGroupResult;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.dto.request.process.SaveShareSignInnerRequest;
import com.timevale.contractmanager.core.model.dto.response.sharesign.ShareSignQrCodeBean;
import com.timevale.contractmanager.core.service.configs.CommonBizConfig;
import com.timevale.contractmanager.core.service.sharesign.bean.ShareSignParticipantAccount;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.model.input.GenQrCodeInput;
import com.timevale.saas.common.manage.common.service.model.output.GenQrCodeOutput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-02-03
 */
@Service
public class ShareSignBaseServiceImpl implements ShareSignBaseService {

    @Autowired ShareSignTaskDAO shareSignTaskDAO;

    @Autowired ShareSignUrlDAO shareSignUrlDAO;

    @Autowired ShareSignProcessDAO shareSignProcessDAO;

    @Autowired SaasCommonClient saasCommonClient;
    @Autowired
    private ContractProcessGroupService groupService;

    @Deprecated
    @Override
    public void insertTask(ShareSignTaskDO entity) {
        shareSignTaskDAO.insert(entity);
    }

    @Override
    public void updateTaskStatus(String shareSignTaskId, Integer status) {
        shareSignTaskDAO.updateStatus(shareSignTaskId, status);
    }

    @Override
    public void updateByTaskId(ShareSignTaskDO updateData) {
        shareSignTaskDAO.updateByTaskId(updateData);
    }

    @Override
    public boolean upgradeTaskNum(String shareSignTaskId) {
        return shareSignTaskDAO.upgradeNum(shareSignTaskId) > 0;
    }

    @Override
    public boolean degradeTaskNum(String shareSignTaskId) {
        return shareSignTaskDAO.degradeNum(shareSignTaskId) > 0;
    }

    @Override
    public boolean upgradeTaskNumOccupied(String shareSignTaskId) {
        return shareSignTaskDAO.upgradeNumOccupied(shareSignTaskId) > 0;
    }

    @Override
    public boolean degradeTaskNumOccupied(String shareSignTaskId) {
        return shareSignTaskDAO.degradeNumOccupied(shareSignTaskId) > 0;
    }

    @Override
    public void upgradeTaskDone(String shareSignTaskId) {
        shareSignTaskDAO.upgradeDone(shareSignTaskId);
    }

    @Override
    public ShareSignTaskDO queryTaskById(String shareSignTaskId) {
        return shareSignTaskDAO.getByTaskId(shareSignTaskId);
    }

    @Override
    public List<ShareSignTaskDO> queryTasksByBizIds(List<String> shareBizIds, Integer shareType) {
        return shareSignTaskDAO.queryByShareBizIds(shareBizIds, shareType);
    }

    @Override
    public boolean checkIsShareTemplateId(String shareTemplateId) {
        return shareSignTaskDAO.countByShareTemplateId(shareTemplateId) > 0;
    }

    @Deprecated
    @Override
    public void insertTaskUrl(ShareSignUrlDO entity) {
        shareSignUrlDAO.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveShareSignTaskAndUrl(SaveShareSignInnerRequest request) {
        String appId = request.getAppId();
        ShareSignTaskDO taskDO = request.getTaskDO();
        List<ShareSignUrlDO> urlDOS = request.getUrlDOS();
        GroupTypeEnum groupType = request.getGroupType();
        String signMode = request.getSignMode();
        String dedicatedCloudId = request.getDedicatedCloudId();;
        // 创建流程组
        CreateProcessGroupModel contractProcessGroupDO = new CreateProcessGroupModel();
        contractProcessGroupDO.setProcessGroupName(taskDO.getTaskName());
        contractProcessGroupDO.setCreatorAccountId(taskDO.getAccountOid());
        contractProcessGroupDO.setProcessGroupAppId(appId);
        contractProcessGroupDO.setOwnerAccountId(taskDO.getSubjectOid());
        contractProcessGroupDO.setProcessGroupType(groupType.getType());
        contractProcessGroupDO.setSignMode(signMode);
        contractProcessGroupDO.setDedicatedCloudId(dedicatedCloudId);
        CreateProcessGroupResult result = groupService.createProcessGroup(contractProcessGroupDO);
        // 扫码任务设置流程组id作为业务id
        taskDO.setShareBizId(result.getProcessGroupId());
        // 保存扫码任务
        shareSignTaskDAO.insert(taskDO);
        // 保存扫码任务地址
        shareSignUrlDAO.batchInsert(urlDOS);
    }

    @Override
    public void updateTaskUrl(ShareSignUrlDO entity) {
        shareSignUrlDAO.update(entity);
    }

    @Override
    public List<ShareSignUrlDO> queryTaskUrls(String shareSignTaskId, String participantId) {
        if (StringUtils.isBlank(participantId)) {
            return shareSignUrlDAO.getByShareSignTaskId(shareSignTaskId);
        }
        return shareSignUrlDAO.getByParticipantId(shareSignTaskId, participantId);
    }

    @Override
    public ShareSignUrlDO queryTaskUrlById(String shareSignTaskUrlId) {
        if (StringUtils.isBlank(shareSignTaskUrlId)) {
            return null;
        }
        return shareSignUrlDAO.getByUuid(shareSignTaskUrlId);
    }

    @Override
    public void addTaskProcess(
            String shareSignTaskId,
            String participantId,
            String processId,
            ShareSignParticipantAccount signAccount,
            ShareSignProcessStatusEnum status) {
        // 保存任务流程记录
        ShareSignProcessDO entity = new ShareSignProcessDO();
        entity.setProcessId(processId);
        entity.setParticipantId(participantId);
        entity.setParticipantType(signAccount.getParticipantType());
        entity.setShareSignTaskId(shareSignTaskId);
        entity.setAccount(signAccount.getAccount());
        entity.setAccountOid(signAccount.getAccountOid());
        entity.setAccountGid(signAccount.getAccountGid());
        entity.setSubjectOid(signAccount.getSubjectId());
        entity.setSubjectName(signAccount.getSubjectName());
        entity.setDeleted(BooleanUtils.toInteger(false));
        entity.setStatus(status.getType());
        shareSignProcessDAO.insert(entity);
    }

    @Override
    public List<ShareSignProcessDO> queryByAccountGid(String shareSignTaskId, String signerGid, int limit) {
        return shareSignProcessDAO.queryByShareSignTaskIdAndGid(shareSignTaskId, signerGid, limit);
    }

    @Override
    public int countByAccountGid(String shareSignTaskId, String signerGid) {
        return shareSignProcessDAO.countByShareSignTaskIdAndGid(shareSignTaskId, signerGid);
    }

    @Override
    public int countByAccountGidWithPerson(String shareSignTaskId, String signerGid) {
        return shareSignProcessDAO.countByShareSignTaskIdAndGidWithPerson(shareSignTaskId, signerGid);
    }

    @Override
    public List<ShareSignProcessDO> queryByProcessId(String processId) {
        return shareSignProcessDAO.queryByProcessId(processId);
    }

    @Override
    public Map<String, ShareSignProcessDO> queryByProcessIds(List<String> processIds) {
        if (CollectionUtils.isEmpty(processIds)) {
            return Collections.emptyMap();
        }

        List<ShareSignProcessDO> shareSignProcesses =
                shareSignProcessDAO.queryByProcessIds(processIds);
        if (CollectionUtils.isEmpty(shareSignProcesses)) {
            return Collections.emptyMap();
        }

        return shareSignProcesses.stream()
                .sorted(Comparator.comparing(ShareSignProcessDO::getId))
                .collect(
                        Collectors.toMap(
                                ShareSignProcessDO::getProcessId,
                                Function.identity(),
                                (a, b) -> b));
    }

    @Override
    public int deleteByProcessId(String processId) {
        return shareSignProcessDAO.deleteByProcessId(processId);
    }

    @Override
    public ShareSignQrCodeBean genShareSignQrCode(String shareSignTaskId, String participantId) {
        // 生成二维码
        GenQrCodeInput input = new GenQrCodeInput();
        input.setQrCodeSize(CommonBizConfig.SHARE_SIGN_TASK_QRCODE_SIZE);
        input.setFullWidth(CommonBizConfig.SHARE_SIGN_TASK_QRCODE_SIZE);
        input.setFullHeight(CommonBizConfig.SHARE_SIGN_TASK_QRCODE_SIZE);
        input.setNeedLogo(Boolean.TRUE.equals(CommonBizConfig.SHARE_SIGN_TASK_QRCODE_NEED_LOGO));

        String shareScanId = "";
        String shareUrl = String.format(CommonBizConfig.SHARE_SIGN_TASK_URL, shareSignTaskId, participantId);
        // 如果存在新的扫码签二维码地址， 优先获取新的， 否则依旧使用老的地址
        if (useNewShareSignTaskUrl()) {
            shareScanId = UUIDUtil.genUUID();
            shareUrl = String.format(CommonBizConfig.SHARE_SIGN_TASK_NEW_URL, shareScanId);
        }
        input.setQrCodeContent(shareUrl);
        input.setQrCodeFileName("扫码签二维码.png");
        GenQrCodeOutput qrCodeOutput = saasCommonClient.genQrCode(input);

        ShareSignQrCodeBean qrCodeBean = new ShareSignQrCodeBean();
        qrCodeBean.setShareUrl(shareUrl);
        qrCodeBean.setShareScanId(shareScanId);
        qrCodeBean.setQrCodeFileKey(qrCodeOutput.getQrCodeFileKey());
        qrCodeBean.setQrCodeUrl(qrCodeOutput.getQrCodeUrl());
        return qrCodeBean;
    }

    @Override
    public void refreshShareSignQrCode(ShareSignUrlDO shareSignUrl) {
        // 如果当前地址存在uuid，说明已使用新扫码地址， 跳过
        if (StringUtils.isNotBlank(shareSignUrl.getUuid())) {
            return;
        }
        // 如果当前地址不存在uuid, 且系统没有配置新扫码地址，无需刷新，跳过
        if (!useNewShareSignTaskUrl()) {
            return;
        }

        // 生成二维码
        ShareSignQrCodeBean qrCodeBean =
                genShareSignQrCode(
                        shareSignUrl.getShareSignTaskId(), shareSignUrl.getParticipantId());
        // 保存二维码记录
        shareSignUrl.setUuid(qrCodeBean.getShareScanId());
        shareSignUrl.setShareUrl(qrCodeBean.getShareUrl());
        shareSignUrl.setShareQrcodeFilekey(qrCodeBean.getQrCodeFileKey());
        updateTaskUrl(shareSignUrl);
    }

    @Override
    public int countTaskByAccountGid(String accountGid, int status, Integer shareType) {
        return shareSignTaskDAO.countTaskByAccountGid(accountGid, status, shareType);
    }

    @Override
    public List<Map<String, Object>> countByStatus(String accountGid, String subjectGid) {
        return shareSignTaskDAO.countByStatus(accountGid, subjectGid);
    }

    @Override
    public Page<ShareSignTaskDO> getListByPage(ShareSignTaskListParam input) {
        return PageHelper.startPage(input.getPageNum(), input.getPageSize())
                .doSelectPage(() -> shareSignTaskDAO.getList(input));
    }

    @Override
    public void updateStatusByProcessId(String processId, Integer status) {
        shareSignProcessDAO.updateStatusByProcessId(processId, status);
    }

    /**
     * 判断是否使用新的扫码地址
     * @return
     */
    private boolean useNewShareSignTaskUrl() {
        return StringUtils.isNotBlank(CommonBizConfig.SHARE_SIGN_TASK_NEW_URL);
    }
}

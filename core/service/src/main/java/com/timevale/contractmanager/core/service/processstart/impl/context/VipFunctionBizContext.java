package com.timevale.contractmanager.core.service.processstart.impl.context;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.constant.FunctionLimitConstant;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import com.timevale.saas.common.manage.common.service.model.output.bean.VipFunction;

import java.util.Map;

import static com.timevale.saas.common.manage.common.service.exception.ResultEnum.*;

/**
 * 会员功能上下文对象
 *
 * <AUTHOR>
 * @since 2022-09-22
 */
public class VipFunctionBizContext {

    /** 会员功能Map, Map<功能标识，功能信息> */
    private Map<String, VipFunction> vipFunctionMap;

    public VipFunctionBizContext(Map<String, VipFunction> vipFunctionMap) {
        this.vipFunctionMap = vipFunctionMap;
    }

    /**
     * 校验会员功能是否支持
     *
     * @param functionCode
     * @return
     */
    public boolean checkFunctionValid(String functionCode, boolean throwEx) {
        // 获取会员功能信息
        VipFunction function = queryFunction(functionCode, throwEx);
        // 校验会员功能是否可用
        return isFunctionValid(function, throwEx);
    }

    public String getTrialStatus(String functionCode, boolean throwEx) {
        // 获取会员功能信息
        VipFunction function = queryFunction(functionCode, throwEx);
        // 校验会员功能是否可用
        return function != null ? function.getTrialStatus() : null;
    }

    /**
     * 校验会员功能是否支持
     *
     * @param functionCode
     * @return
     */
    public boolean checkFunctionLimit(String functionCode, int limit, boolean throwEx) {
        // 获取会员功能信息
        VipFunction function = queryFunction(functionCode, throwEx);
        // 如果会员功能不存在或者不可用， 则表示不支持当前功能
        boolean valid = isFunctionValid(function, throwEx);
        // 如果会员功能可用， 则校验是否达到批量上限
        if (valid) {
            valid = !isFunctionLimited(function, limit, throwEx);
        }
        return valid;
    }

    /**
     * 获取指定会员功能信息
     *
     * @param functionCode
     * @param throwEx
     * @return
     */
    private VipFunction queryFunction(String functionCode, boolean throwEx) {
        // 会员功能标识为空或者会员功能标识不存在，默认校验不通过
        if (StringUtils.isBlank(functionCode)
                || CollectionUtils.isEmpty(vipFunctionMap)
                || null == vipFunctionMap.get(functionCode)) {
            if (throwEx) {
                throw new SaasCommonBizException(ACCOUNT_VIP_FUNCTION_NOT_EXIST);
            }
            return null;
        }
        return vipFunctionMap.get(functionCode);
    }

    /**
     * 校验会员功能是否可用
     *
     * @param throwEx
     * @param function
     * @return
     */
    private boolean isFunctionValid(VipFunction function, boolean throwEx) {
        // 如果会员功能不存在或者不可用， 则表示不支持当前功能
        boolean valid = null != function && function.isEnable();
        // 如果会员功能不支持，根据入参控制是否抛出异常
        if (throwEx && !valid) {
            throw new SaasCommonBizException(ACCOUNT_VIP_FUNCTION_INVALID, function.getName());
        }
        return valid;
    }

    /**
     * 判断是否达到批量上限
     *
     * @param function
     * @param limit
     * @param throwEx
     * @return
     */
    private boolean isFunctionLimited(VipFunction function, int limit, boolean throwEx) {
        // 获取会员功能批量上限
        Integer funcLimit = parseFunctionLimit(function, FunctionLimitConstant.MAX_BATCH_COUNT);
        // 如果上限不为空且当前值 > 上限值， 则表示到达上限
        boolean limited = null != funcLimit && funcLimit < limit;
        // 如果已达到上限，根据入参控制是否抛出异常
        if (throwEx && limited) {
            throw new SaasCommonBizException(
                    ACCOUNT_VIP_FUNCTION_BATCH_LIMIT, function.getName(), funcLimit);
        }
        return limited;
    }

    /**
     * 解析会员功能批量限制
     *
     * @param function
     * @param limitKey
     * @return
     */
    private Integer parseFunctionLimit(VipFunction function, String limitKey) {
        if (null == function || CollectionUtils.isEmpty(function.getLimit())) {
            return null;
        }
        Object funcLimit = function.getLimit().get(limitKey);
        if (null != funcLimit && StringUtils.isNumeric(funcLimit.toString())) {
            return Integer.valueOf(funcLimit.toString());
        }
        return null;
    }
}

package com.timevale.contractmanager.core.service.offlinecontract.bean.output;

import com.timevale.contractmanager.common.service.bean.offlinecontract.OfflineContract;
import com.timevale.contractmanager.common.service.bean.offlinecontract.OfflineContractExtractConfig;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 查询线下合同单条合同信息响应数据
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Data
public class OfflineContractOutputDTO extends ToString {

    /** 导入人oid */
    private String importerOid;
    /** 导入人gid */
    private String importerGid;
    /** 主体oid */
    private String subjectOid;
    /** 主体gid */
    private String subjectGid;
    /** 状态 */
    private String status;
    /** 合同流程id */
    private String processId;
    /** 归档菜单id */
    private String menuId;
    /** 合同信息提取方式 */
    private String extractWay;
    /** 导入端 */
    private String importClient;
    /** 合同信息提取配置 */
    private OfflineContractExtractConfig extractConfig;
    /** 线下合同合同信息列表 */
    private OfflineContract contract;
}

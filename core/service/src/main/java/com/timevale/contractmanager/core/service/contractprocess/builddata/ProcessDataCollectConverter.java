package com.timevale.contractmanager.core.service.contractprocess.builddata;

import com.timevale.account.flow.service.model.approval.model.ApprovalAccountTaskInfoModel;
import com.timevale.account.flow.service.model.approval.model.ApprovalTaskModel;
import com.timevale.account.flow.service.model.approval.model.UserGidNameModel;
import com.timevale.contractanalysis.facade.api.bo.FormOriginalDataResult;
import com.timevale.contractapproval.facade.dto.ApprovalAccountDTO;
import com.timevale.contractapproval.facade.dto.ApprovalSyncDataTaskDTO;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateNodeTypeEnum;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.bean.FileBean;
import com.timevale.contractmanager.core.service.process.handler.bean.ProcessDetailBean;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.*;
import com.timevale.signflow.search.docSearchService.enums.TaskTypeEnum;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdsResult;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessCooperationTaskDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessFileDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessFileInfoDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessFormDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessGroupingDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessTempParticipantDTO;
import com.timevale.signflow.search.service.model.contractprocess.ProcessAccountSimpleDTO;
import com.timevale.signflow.search.service.model.contractprocess.ProcessApprovalDTO;
import com.timevale.signflow.search.service.model.contractprocess.SealApprovalDTO;
import com.timevale.signflow.search.service.model.contractprocess.SealApprovalTaskInfoDTO;
import com.timevale.signflow.search.service.model.contractprocess.SealInfoDTO;
import com.timevale.signflow.search.service.request.datacollect.AccountParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessCompareRecordParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessCooperationTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessFileInfoParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessFileParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessFormParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessGroupParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessGroupingParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSignTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessTempParticipantParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessTemplateParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessAccountParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessAccountSimpleParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessApprovalParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessApprovalTaskInfoParam;
import com.timevale.signflow.search.service.request.datacollect.SealApprovalParam;
import com.timevale.signflow.search.service.request.datacollect.SealApprovalTaskInfoParam;
import com.timevale.signflow.search.service.request.datacollect.SealInfoParam;
import com.timevale.signflow.search.service.result.v2.ProcessArchiveQueryResult;

import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/5/12
 */
public class ProcessDataCollectConverter {


    public static ContractProcessTemplateParam template2Param(TemplateInfo templateInfo) {
        if (null == templateInfo) {
            return null;
        }
        ContractProcessTemplateParam param = new ContractProcessTemplateParam();
        param.setTemplateId(templateInfo.getTemplateId());
        param.setOriginTemplateId(templateInfo.getOriginTemplateId());
        param.setTemplateName(templateInfo.getTemplateName());
        param.setOriginTemplateName(templateInfo.getOriginTemplateName());
        param.setTemplateType(templateInfo.getTemplateType());
        return param;
    }


    public static ContractProcessFileInfoParam file2FileInfoParam(List<? extends FileBean> contracts, List<FileBean> attachments) {
        ContractProcessFileInfoParam contractProcessFileInfoModel = new ContractProcessFileInfoParam();
        //文件
        contractProcessFileInfoModel.setContractFiles(file2FileParam(contracts));
        //附件
        contractProcessFileInfoModel.setAttachmentFiles(file2FileParam(attachments));
        return contractProcessFileInfoModel;
    }

    public static List<ContractProcessFileParam> file2FileParam(List<? extends FileBean> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(i -> {

            ContractProcessFileParam fileDTO = new ContractProcessFileParam();
            fileDTO.setFileName(i.getFileName());
            fileDTO.setFileId(i.getFileId());
            fileDTO.setContractNo(i.getContractNo());
            return fileDTO;

        }).collect(Collectors.toList());

    }


    public static ContractProcessFileInfoParam fileDTO2FileInfoParam(ContractProcessFileInfoDTO fileInfoDTO) {
        ContractProcessFileInfoParam contractProcessFileInfoModel = new ContractProcessFileInfoParam();
        //文件
        contractProcessFileInfoModel.setContractFiles(fileDTO2FileParam(fileInfoDTO.getContractFiles()));
        //附件
        contractProcessFileInfoModel.setAttachmentFiles(fileDTO2FileParam(fileInfoDTO.getAttachmentFiles()));
        return contractProcessFileInfoModel;
    }

    public static List<ContractProcessFileParam> fileDTO2FileParam(List<ContractProcessFileDTO> list) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(i -> {
            ContractProcessFileParam fileDTO = new ContractProcessFileParam();
            fileDTO.setFileId(i.getFileId());
            fileDTO.setContractNo(i.getContractNo());
            fileDTO.setFileName(i.getFileName());
            return fileDTO;
        }).collect(Collectors.toList());
    }


    public static ContractProcessGroupParam processGroup2Param(ProcessDetailBean.ProcessGroup processGroup) {
        if (null == processGroup) {
            return null;
        }
        ContractProcessGroupParam groupParam = new ContractProcessGroupParam();
        groupParam.setGroupId(processGroup.getProcessGroupId());
        groupParam.setGroupName(processGroup.getProcessGroupName());
        groupParam.setGroupType(processGroup.getProcessGroupType());
        groupParam.setGroupStatus(processGroup.getProcessGroupStatus());
        return groupParam;
    }

    public static List<ProcessAccountParam> account2ParamList(List<ProcessAccount> list) {

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(ProcessDataCollectConverter::account2Param).collect(Collectors.toList());
    }


    public static ProcessAccountParam account2Param(ProcessAccount model) {
        if (null == model) {
            return null;
        }
        ProcessAccountParam processAccount = new ProcessAccountParam();
        processAccount.setType(model.getType());
        processAccount.setHidden(model.getHidden());
        processAccount.setCurrentUser(model.getCurrentUser());
        processAccount.setDeptInfo(model.getDeptInfo());
        processAccount.setPerson(accountModel2Account(model.getPerson()));
        processAccount.setTenant(accountModel2Account(model.getSubject()));
        processAccount.setTransfer(model.getTransfer());
        return processAccount;
    }

    public static AccountParam accountModel2Account(Account accountModel) {
        if (null == accountModel) {
            return null;
        }
        AccountParam account = new AccountParam();
        account.setGid(accountModel.getGid());
        account.setOid(accountModel.getOid());
        account.setName(accountModel.getName());
        account.setMobile(accountModel.getMobile());
        account.setEmail(accountModel.getEmail());
        account.setOrgan(accountModel.getOrgan());
        account.setNickname(accountModel.getNickname());
        account.setLicense(accountModel.getLicense());
        account.setLicenseType(accountModel.getLicenseType());
        // 为空就至为false
        account.setDeleted(Optional.ofNullable(accountModel.getDeleted()).orElse(Boolean.FALSE));
        return account;
    }

    public static AccountParam account2Param(AccountBean accountModel) {
        if (null == accountModel) {
            return null;
        }
        AccountParam account = new AccountParam();
        account.setGid(accountModel.getGid());
        account.setOid(accountModel.getOid());
        account.setName(accountModel.getName());
        account.setMobile(accountModel.getMobile());
        account.setEmail(accountModel.getEmail());
        account.setOrgan(accountModel.getOrgan());
        account.setNickname(accountModel.getNickname());
        account.setDeleted(accountModel.getDeleted());
        account.setLicense(accountModel.getLicense());
        account.setLicenseType(accountModel.getLicenseType());
        return account;
    }

    public static Account accountBean2Account(AccountBean accountBean){
        if(null==accountBean){
            return null;
        }
        Account account=new Account();
        account.setGid(accountBean.getGid());
        account.setOid(accountBean.getOid());
        account.setRid(accountBean.getRid());
        account.setUid(accountBean.getUid());
        account.setName(accountBean.getName());
        account.setMobile(accountBean.getMobile());
        account.setEmail(accountBean.getEmail());
        account.setOrgan(accountBean.getOrgan());
        account.setNickname(accountBean.getNickname());
        account.setDeleted(accountBean.getDeleted());
        account.setLicense(accountBean.getLicense());
        account.setLicenseType(accountBean.getLicenseType());
        return account;
    }

    public static List<SealInfoParam> sealInfoDTO2Param(List<SealInfoDTO> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return new ArrayList<>();
        }
        return paramList.stream().map(item -> {
            SealInfoParam sealInfoModel = new SealInfoParam();
            sealInfoModel.setSealId(item.getSealId());
            sealInfoModel.setSealName(item.getSealName());
            sealInfoModel.setBizType(item.getBizType());
            return sealInfoModel;
        }).collect(Collectors.toList());
    }

    public static List<SealInfoParam> sealInfo2Param(List<SealInfo> paramList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(paramList)) {
            return new ArrayList<>();
        }
        return paramList.stream().map(item -> {
            SealInfoParam sealInfoModel = new SealInfoParam();
            sealInfoModel.setSealId(item.getSealId());
            sealInfoModel.setSealName(item.getSealName());
            sealInfoModel.setBizType(item.getBizType());
            return sealInfoModel;
        }).collect(Collectors.toList());
    }

    public static List<SealInfoDTO> sealInfoParam2DTO(List<SealInfoParam> paramList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(paramList)) {
            return new ArrayList<>();
        }
        return paramList.stream().map(item -> {
            SealInfoDTO sealInfoModel = new SealInfoDTO();
            sealInfoModel.setSealId(item.getSealId());
            sealInfoModel.setSealName(item.getSealName());
            sealInfoModel.setBizType(item.getBizType());
            return sealInfoModel;
        }).collect(Collectors.toList());
    }

    public static List<SealApprovalParam> sealApprovalDTO2ParamList(List<SealApprovalDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList();
        }
        List<SealApprovalParam> sealApprovalParamList = new ArrayList();
        for (SealApprovalDTO sealApprovalDTO : dtoList) {
            sealApprovalParamList.add(sealApprovalDTO2Param(sealApprovalDTO));
        }
        return sealApprovalParamList;
    }

    public static ProcessApprovalParam processApprovalParam2DTO(ProcessApprovalDTO processApprovalDTO) {
        if (null == processApprovalDTO) {
            return null;
        }
        ProcessApprovalParam processApprovalParam = new ProcessApprovalParam();
        processApprovalParam.setApprovalId(processApprovalDTO.getApprovalId());
        processApprovalParam.setApprovalStatus(processApprovalDTO.getApprovalStatus());
        processApprovalParam.setApprovalCreateTime(processApprovalDTO.getApprovalCreateTime());
        processApprovalParam.setApprovalCompleteTime(processApprovalDTO.getApprovalCompleteTime());
        if (CollectionUtils.isNotEmpty(processApprovalDTO.getApprovalTaskInfo())) {
            List<ProcessApprovalTaskInfoParam> approvalTaskInfo = processApprovalDTO.getApprovalTaskInfo()
                    .stream()
                    .map(elm -> {
                        ProcessApprovalTaskInfoParam processApprovalTaskInfoParam = new ProcessApprovalTaskInfoParam();
                        processApprovalTaskInfoParam.setTaskId(elm.getTaskId());
                        processApprovalTaskInfoParam.setTaskStatus(elm.getTaskStatus());
                        processApprovalTaskInfoParam.setOperatorTime(elm.getOperatorTime());
                        processApprovalTaskInfoParam.setPerson(accountSimpleDTO2Param(elm.getPerson()));
                        processApprovalTaskInfoParam.setHidden(elm.getHidden());
                        return processApprovalTaskInfoParam;
                    }).collect(Collectors.toList());
            processApprovalParam.setApprovalTaskInfo(approvalTaskInfo);
        }
        return processApprovalParam;
    }

    public static SealApprovalParam sealApprovalDTO2Param(SealApprovalDTO dto) {
        if (null == dto) {
            return null;
        }
        SealApprovalParam param = new SealApprovalParam();
        param.setApprovalId(dto.getApprovalId());
        param.setApprovalStatus(dto.getApprovalStatus());
        param.setApprovalCreateTime(dto.getApprovalCreateTime());
        param.setApprovalCompleteTime(dto.getApprovalCompleteTime());
        param.setApprovalDesc(dto.getApprovalDesc());
        param.setSeal(sealDTO2ParamList(dto.getSeal()));
        param.setApprovalTaskInfo(sealApprovalTaskInfoDTO2ParamList(dto.getApprovalTaskInfo()));
        param.setInitiatorPerson(accountSimpleDTO2Param(dto.getInitiatorPerson()));
        param.setSubject(accountSimpleDTO2Param(dto.getSubject()));
        return param;
    }

    public static List<SealInfoParam> sealDTO2ParamList(List<SealInfoDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList();
        }
        List<SealInfoParam> sealInfoParams = new ArrayList<>();
        for (SealInfoDTO sealInfoDTO : dtoList) {
            SealInfoParam sealInfoParam = new SealInfoParam();
            sealInfoParam.setSealId(sealInfoDTO.getSealId());
            sealInfoParam.setSealName(sealInfoDTO.getSealName());
            sealInfoParams.add(sealInfoParam);
        }
        return sealInfoParams;
    }

    public static List<SealApprovalTaskInfoParam> sealApprovalTaskInfoDTO2ParamList(List<SealApprovalTaskInfoDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList();
        }
        List<SealApprovalTaskInfoParam> sealApprovalTaskInfoParamList = new ArrayList<>();
        for (SealApprovalTaskInfoDTO dto : dtoList) {
            sealApprovalTaskInfoParamList.add(sealApprovalTaskInfoDTO2Param(dto));
        }
        return sealApprovalTaskInfoParamList;
    }

    public static SealApprovalTaskInfoParam sealApprovalTaskInfoDTO2Param(SealApprovalTaskInfoDTO dto) {
        if (null == dto) {
            return null;
        }
        SealApprovalTaskInfoParam infoParam = new SealApprovalTaskInfoParam();
        infoParam.setTaskId(dto.getTaskId());
        infoParam.setTaskStatus(dto.getTaskStatus());
        infoParam.setPerson(accountSimpleDTO2ParamList(dto.getPerson()));
        infoParam.setActualPerson(accountSimpleDTO2Param(dto.getActualPerson()));
        infoParam.setOperatorTime(dto.getOperatorTime());
        infoParam.setHiddenPersonGid(dto.getHiddenPersonGid());
        return infoParam;
    }

    public static List<ProcessAccountSimpleParam> accountSimpleDTO2ParamList(List<ProcessAccountSimpleDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList();
        }
        List<ProcessAccountSimpleParam> simpleParamList = new ArrayList<>();
        for (ProcessAccountSimpleDTO dto : dtoList) {
            simpleParamList.add(accountSimpleDTO2Param(dto));
        }
        return simpleParamList;
    }

    public static ProcessAccountSimpleParam accountSimpleDTO2Param(ProcessAccountSimpleDTO dto) {
        if (null == dto) {
            return null;
        }
        ProcessAccountSimpleParam simpleParam = new ProcessAccountSimpleParam();
        simpleParam.setGid(dto.getGid());
        simpleParam.setName(dto.getName());
        return simpleParam;
    }

    public static List<ContractProcessSignTaskParam> signTaskDTO2ParamList(List<ContractProcessSignTaskDTO> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return new ArrayList();
        }
        List<ContractProcessSignTaskParam> contractProcessSignTaskModelList = new ArrayList();
        for (ContractProcessSignTaskDTO contractProcessSignTaskDTO : paramList) {
            contractProcessSignTaskModelList.add(signTaskParam2Model(contractProcessSignTaskDTO));
        }
        return contractProcessSignTaskModelList;
    }


    public static ContractProcessSignTaskParam signTaskParam2Model(ContractProcessSignTaskDTO dto) {
        if (null == dto) {
            return null;
        }
        ContractProcessSignTaskParam param = new ContractProcessSignTaskParam();
        param.setSealInfo(sealInfoDTO2Param(dto.getSealInfo()));
        param.setTaskId(dto.getTaskId());
        param.setFlowId(dto.getFlowId());
        param.setTaskType(dto.getTaskType());
        param.setHidden(dto.getHidden());
        param.setStatus(dto.getStatus());
        param.setExecute(processAccountParam2Mode(dto.getExecute()));
        param.setOperator(processAccountParam2Mode(dto.getOperator()));
        param.setSource(dto.getSource());
        param.setOrder(dto.getOrder());
        param.setOperateTime(dto.getOperateTime());
        param.setTransfer(dto.getTransfer());
        param.setTaskModel(dto.getTaskModel());
        param.setExecutorOwner(dto.getExecutorOwner());
        param.setParticipantStartType(dto.getParticipantStartType());
        return param;
    }


    public static ContractProcessCooperationTaskParam cooperationTaskDTO2Param(ContractProcessCooperationTaskDTO dto) {
        if (null == dto) {
            return null;
        }
        ContractProcessCooperationTaskParam param = new ContractProcessCooperationTaskParam();
        param.setTaskId(dto.getTaskId());
        param.setFlowId(dto.getFlowId());
        param.setTaskType(dto.getTaskType());
        param.setHidden(dto.getHidden());
        param.setStatus(dto.getStatus());
        param.setExecute(processAccountParam2Mode(dto.getExecute()));
        param.setSource(dto.getSource());
        param.setOrder(dto.getOrder());
        param.setOperateTime(dto.getOperateTime());
        param.setTransfer(dto.getTransfer());
        param.setParticipantStartType(dto.getParticipantStartType());
        return param;
    }


    public static List<ContractProcessCooperationTaskParam> cooperationTask2ParamList(List<ContractProcessCooperationTaskDTO> paramList) {
        if (null == paramList) {
            return null;
        }
        if (CollectionUtils.isEmpty(paramList)) {
            return new ArrayList<>();
        }
        List<ContractProcessCooperationTaskParam> contractProcessCooperationTaskModelList = new ArrayList<>();
        for (ContractProcessCooperationTaskDTO contractProcessCooperationTaskDTO : paramList) {
            contractProcessCooperationTaskModelList.add(cooperationTaskDTO2Param(contractProcessCooperationTaskDTO));
        }
        return contractProcessCooperationTaskModelList;
    }


    private static ProcessAccountParam processAccountParam2Mode(ProcessAccount param) {
        if (param == null) {
            return null;
        }
        ProcessAccountParam processAccountModel = new ProcessAccountParam();
        processAccountModel.setPerson(account2Param(param.getPerson()));
        processAccountModel.setTenant(account2Param(param.getSubject()));
        processAccountModel.setType(param.getType());
        processAccountModel.setHidden(param.getHidden());
        processAccountModel.setCurrentUser(param.getCurrentUser());
        processAccountModel.setDeptInfo(param.getDeptInfo());
        return processAccountModel;
    }


    public static AccountParam account2Param(Account account) {
        if (null == account) {
            return null;
        }
        AccountParam accountModel = new AccountParam();
        accountModel.setGid(account.getGid());
        accountModel.setOid(account.getOid());
        accountModel.setName(account.getName());
        accountModel.setMobile(account.getMobile());
        accountModel.setEmail(account.getEmail());
        accountModel.setOrgan(account.getOrgan());
        accountModel.setNickname(account.getNickname());
        accountModel.setDeleted(Optional.ofNullable(account.getDeleted()).orElse(false));
        accountModel.setLicenseType(account.getLicenseType());
        accountModel.setLicense(account.getLicense());
        return accountModel;
    }


    public static ContractProcessFormParam form2FormParam(FormOriginalDataResult formDataExtendListResult,
                                                          String formId, String tenantGid) {

        ContractProcessFormParam param = new ContractProcessFormParam();
        param.setFormId(formId);
        param.setTenantGid(tenantGid);
        Map<String, Object> dataMap = formDataExtendListResult.getOriginalData();
        param.setData(dataMap);
        param.setExtractTime(Optional.ofNullable(formDataExtendListResult.getModifiedTime()).map(Date::getTime).orElse(null));
        return param;
    }

    public static List<ContractProcessFormParam> formDTO2FormParam(List<ContractProcessFormDTO> dtoList) {
        if (null == dtoList) {
            return null;
        }

        return dtoList.stream().map(item -> {
            ContractProcessFormParam param = new ContractProcessFormParam();
            param.setData(item.getData());
            param.setFormId(item.getFormId());
            param.setTenantGid(item.getTenantGid());
            param.setExtractTime(item.getExtractTime());
            return param;
        }).collect(Collectors.toList());
    }


    public static List<ContractProcessGroupingParam> processGroupingDTO2ParamList(List<ContractProcessGroupingDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }

        return dtoList.stream().map(elm -> processGroupingDTO2Param(elm)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ContractProcessGroupingParam processGroupingDTO2Param(ContractProcessGroupingDTO elm) {
        if (null == elm) {
            return null;
        }
        ContractProcessGroupingParam param = new ContractProcessGroupingParam();
        param.setMenuIdList(elm.getMenuIdList());
        param.setContractNo(elm.getContractNo());
        param.setSubjectId(elm.getSubjectId());
        param.setArchiverPerson(account2Param(elm.getArchiverPerson()));
        param.setArchiveType(elm.getArchiveType());
        param.setArchiveTime(elm.getArchiveTime());
        return param;
    }

    public static List<ProcessApprovalTaskInfoParam> ApprovalAccountTaskInfoModel2ParamList(List<ApprovalAccountTaskInfoModel> taskInfos) {
        if (CollectionUtils.isEmpty(taskInfos)) {
            return new ArrayList<>();
        }
        List<ProcessApprovalTaskInfoParam> processApprovalTaskInfoParamList = new ArrayList<>();
        for (ApprovalAccountTaskInfoModel approvalAccountTaskInfoModel : taskInfos) {
            processApprovalTaskInfoParamList.add(ApprovalAccountTaskInfoModel2Param(approvalAccountTaskInfoModel));
        }
        return processApprovalTaskInfoParamList;
    }

    public static ProcessApprovalTaskInfoParam ApprovalAccountTaskInfoModel2Param(ApprovalAccountTaskInfoModel taskInfo) {
        if (taskInfo == null) {
            return new ProcessApprovalTaskInfoParam();
        }
        ProcessApprovalTaskInfoParam processApprovalTaskInfoParam = new ProcessApprovalTaskInfoParam();
        processApprovalTaskInfoParam.setTaskId(taskInfo.getTaskId().toString());
        processApprovalTaskInfoParam.setTaskStatus(taskInfo.getTaskStatus());
        processApprovalTaskInfoParam.setOperatorTime(taskInfo.getModifyTime());
        ProcessAccountSimpleParam simpleParam = new ProcessAccountSimpleParam();
        simpleParam.setGid(taskInfo.getApprovalGid());
        simpleParam.setName(taskInfo.getApprovalAccountName());
        processApprovalTaskInfoParam.setPerson(simpleParam);
        processApprovalTaskInfoParam.setCandidate(Collections.singletonList(simpleParam));
        return processApprovalTaskInfoParam;
    }

    public static List<SealApprovalTaskInfoParam> approvalTask2ParamList(List<ApprovalTaskModel> outputList, Long createTime) {
        if (CollectionUtils.isEmpty(outputList)) {
            return new ArrayList<>();
        }
        List<SealApprovalTaskInfoParam> sealApprovalTaskInfoParamList = new ArrayList<>();
        for (ApprovalTaskModel output : outputList) {
            sealApprovalTaskInfoParamList.add(approvalTask2Param(output, createTime));
        }
        return sealApprovalTaskInfoParamList;
    }

    public static SealApprovalTaskInfoParam approvalTask2Param(ApprovalTaskModel taskModel, Long createTime) {
        if (taskModel == null) {
            return null;
        }
        SealApprovalTaskInfoParam param = new SealApprovalTaskInfoParam();
        param.setTaskId(taskModel.getTaskId());
        param.setTaskStatus(taskModel.getTaskStatus());
        param.setOperatorTime(taskModel.getHandleTime() == null ? new Date(createTime) : new Date(taskModel.getHandleTime()));
        param.setPerson(candidateUser2ParamList(taskModel.getCandidateUsers()));
        param.setActualPerson(candidateUser2Param(taskModel.getAssignee()));
        param.setRecord(record2ParamList(taskModel.getCandidateUsers()));
        if (ApprovalTemplateNodeTypeEnum.CARBON_COPY.getCode().equals(taskModel.getTaskNodeType())) {
            param.setTaskType(TaskTypeEnum.APPROVE_CC.getType());
        }
        return param;
    }

    public static List<ContractProcessCompareRecordParam> record2ParamList(List<UserGidNameModel> candidateUsers) {
        if (CollectionUtils.isEmpty(candidateUsers)) {
            return new ArrayList<>();
        }
        List<ContractProcessCompareRecordParam> recordParamList = new ArrayList<>();
        for (UserGidNameModel candidateUser : candidateUsers) {
            ContractProcessCompareRecordParam recordParam = new ContractProcessCompareRecordParam();
            recordParam.setGid(candidateUser.getGid());
            recordParam.setName(candidateUser.getName());
            recordParamList.add(recordParam);
        }
        return recordParamList;
    }

    public static List<ProcessAccountSimpleParam> candidateUser2ParamList(List<UserGidNameModel> candidateUsers) {
        if (CollectionUtils.isEmpty(candidateUsers)) {
            return new ArrayList<>();
        }
        List<ProcessAccountSimpleParam> simpleParamList = new ArrayList<>();
        for (UserGidNameModel candidateUser : candidateUsers) {
            simpleParamList.add(candidateUser2Param(candidateUser));
        }
        return simpleParamList;
    }

    public static ProcessAccountSimpleParam candidateUser2Param(UserGidNameModel candidateUser) {
        if (candidateUser == null) {
            return null;
        }
        ProcessAccountSimpleParam simpleParam = new ProcessAccountSimpleParam();
        simpleParam.setGid(candidateUser.getGid());
        simpleParam.setName(candidateUser.getName());
        return simpleParam;
    }

    public static List<SealApprovalTaskInfoParam> approvalTask2ParamList(List<ApprovalSyncDataTaskDTO> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return new ArrayList<>();
        }
        List<SealApprovalTaskInfoParam> sealApprovalTaskInfoParamList = new ArrayList<>();
        for (ApprovalSyncDataTaskDTO taskModel : taskList) {
            SealApprovalTaskInfoParam param = new SealApprovalTaskInfoParam();
            param.setTaskId(taskModel.getTaskId());
            param.setTaskStatus(taskModel.getTaskStatus());
            param.setOperatorTime(taskModel.getFinishTime() == null ? taskModel.getCreateTime() : taskModel.getFinishTime());
            param.setPerson(Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(taskModel.getCandidates())) {
                taskModel.getCandidates().forEach(i -> param.getPerson().add(approvalAccount2Param(i)));
            }
            param.setActualPerson(approvalAccount2Param(taskModel.getAssignee()));
            param.setRecord(approvalAccount2ParamList(taskModel.getCandidates()));
            if (ApprovalTemplateNodeTypeEnum.CARBON_COPY.getCode().equals(taskModel.getNodeType())) {
                param.setTaskType(TaskTypeEnum.APPROVE_CC.getType());
            }
            sealApprovalTaskInfoParamList.add(param);
        }
        return sealApprovalTaskInfoParamList;
    }

    public static List<ContractProcessCompareRecordParam> approvalAccount2ParamList(List<ApprovalAccountDTO> approvalAccounts) {
        if (CollectionUtils.isEmpty(approvalAccounts)) {
            return new ArrayList<>();
        }
        List<ContractProcessCompareRecordParam> recordParamList = new ArrayList<>();
        for (ApprovalAccountDTO candidateUser : approvalAccounts) {
            ContractProcessCompareRecordParam recordParam = new ContractProcessCompareRecordParam();
            recordParam.setGid(candidateUser.getGid());
            recordParam.setName(candidateUser.getName());
            recordParamList.add(recordParam);
        }
        return recordParamList;
    }

    public static ProcessAccountSimpleParam approvalAccount2Param(ApprovalAccountDTO approvalAccount) {
        if (approvalAccount == null) {
            return null;
        }
        ProcessAccountSimpleParam simpleParam = new ProcessAccountSimpleParam();
        simpleParam.setGid(approvalAccount.getGid());
        simpleParam.setName(approvalAccount.getName());
        return simpleParam;
    }

    public static List<ContractProcessTempParticipantParam> participantDTO2Param(List<ContractProcessTempParticipantDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(dto -> {
            ContractProcessTempParticipantParam param = new ContractProcessTempParticipantParam();
            param.setPerson(accountModel2Account(dto.getPerson()));
            param.setSubject(accountModel2Account(dto.getSubject()));
            param.setTransfer(dto.getTransfer());
            param.setNonStandardHidden(dto.getNonStandardHidden());
            return param;
        }).collect(Collectors.toList());

    }

    /**
     * 归档流程查询结果对象 -> 流程列表对象
     *
     * @param result 归档流程查询结果
     * @return 流程列表
     */
    public static QueryByProcessIdsResult processArchiveQueryResult2QueryByProcessIdsResult(
            ProcessArchiveQueryResult result) {
        QueryByProcessIdsResult response = new QueryByProcessIdsResult();
        if (Objects.isNull(result)) {
            return response;
        }
        // 参数转换
        List<ProcessInfoTotalInfo> collect =
                result.getProcessArchiveInfos().stream()
                        .map(
                                process -> {
                                    ProcessInfoTotalInfo processInfo = new ProcessInfoTotalInfo();
                                    BeanUtils.copyProperties(process, processInfo);
                                    List<ProcessGrouping> groupInfo = Lists.newArrayList();
                                    // 设置ai动态返回出参
                                    if (process.getCurrentSubjectAiData() != null) {
                                        SubjectAiData subjectAiData = new SubjectAiData();
                                        subjectAiData.setData(process.getCurrentSubjectAiData());
                                        subjectAiData.setExtractTime(process.getAiExtractTime());
                                        processInfo.setCurrentSubjectAiData(
                                                Collections.singletonList(subjectAiData));
                                    }
                                    ProcessGrouping processGrouping = new ProcessGrouping();
                                    if (process.getGrouping() != null) {
                                        processGrouping.setContractNo(
                                                process.getGrouping().getContractNo());
                                        if (process.getGrouping().getArchiverPerson() != null) {
                                            processGrouping.setArchiverPerson(
                                                    process.getGrouping().getArchiverPerson());
                                        }
                                        if (process.getGrouping().getArchiveTime() != 0) {
                                            processGrouping.setArchiveTime(
                                                    process.getGrouping().getArchiveTime());
                                        }
                                        if (StringUtils.isNotBlank(
                                                process.getGrouping().getArchiveType())) {
                                            processGrouping.setArchiveType(
                                                    process.getGrouping().getArchiveType());
                                        }
                                        processGrouping.setMenuIdList(
                                                process.getGrouping().getMenuIdList());
                                    }
                                    processInfo.setTemplateInfo(process.getTemplateInfo());
                                    processInfo.setInitiator(process.getInitiator());
                                    processInfo.setTaskInfo(process.getTaskInfo());
                                    groupInfo.add(processGrouping);
                                    processInfo.setGroupingInfo(groupInfo);
                                    return processInfo;
                                })
                        .collect(Collectors.toList());
        response.setProcessInfoTotalInfo(collect);
        response.setTotal(result.getTotal());
        response.setScrollId(result.getScrollId());
        return response;
    }
}

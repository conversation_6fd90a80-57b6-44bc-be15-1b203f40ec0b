package com.timevale.contractmanager.core.service.opponent.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDTO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.service.enums.opponent.AuthorizeTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentRiskLevelEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.FileSystemClient;
import com.timevale.contractmanager.common.service.integration.client.OpponentSearchClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.model.BatchExportKeyModel;
import com.timevale.contractmanager.common.utils.StringValidUtil;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.opponent.OrganizationInfoBO;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentExportRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationListRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentExportResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.enums.OpponentEntityCreditCodeTypeEnum;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.configs.CommonBizConfig;
import com.timevale.contractmanager.core.service.enums.OpponentBusinessTagEnum;
import com.timevale.contractmanager.core.service.mq.model.opponent.OpponentExportMsg;
import com.timevale.contractmanager.core.service.mq.producer.OpponentMqProducer;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityExportService;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.dayu.sdk.annotation.AuditLogAnnotation;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.mandarin.base.enums.BaseResultCodeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.enums.TaskStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.TaskTypeEnum;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskAddInput;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskUpdateInput;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskAddBean;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskBizInfo;
import com.timevale.signflow.search.service.model.v2.OpponentEntityQueryModel;
import com.timevale.signflow.search.service.result.v2.OpponentEntityQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * OpponentEntityExportServiceImpl
 *
 * <AUTHOR>
 * @since 2021/8/11 5:30 下午
 */
@Slf4j
@Service
public class OpponentEntityExportServiceImpl implements OpponentEntityExportService {

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private OpponentEntityDAO opponentEntityDAO;

    @Autowired
    private OpponentEntityService opponentEntityService;

    @Autowired
    private OpponentMqProducer opponentMqProducer;

    @Autowired
    private FileSystemClient fileSystemClient;

    @Autowired
    private OpponentSearchClient opponentSearchClient;

    @Autowired
    private SaasCommonClient saasCommonClient;

    @Autowired
    private SystemConfig systemConfig;

    private static final String[][] individualHeaderRow = { { "姓名" }, { "所在企业" }, { "手机号/邮箱" }, { "认证状态" }, { "备注" }, { "是否在黑名单" } };

    private static final String[][] organizationHeaderRow = { { "企业名称" }, { "企业证件代码类型" }, { "企业证件号码" }, { "法人姓名" }, { "认证状态" }, { "备注" }, { "人员" }, { "是否在黑名单" } };

    @AuditLogAnnotation(
            enterpriseSpaceUnique1 = "#tenantOid",
            resourceEntSpaceUnique = "#tenantOid",
            userUnique1 = "#operatorOid",
            result = "\"成功\"",
            resourceId = "{{#_result == null ? \"-\" : #_result.exportTaskId}}",
            detailTactics = "1",
            condition = "{{#_result != null && #_result.exportTaskId != null}}",
            postHandle = "auditLogOpponentExportExcelHandle")
    @Override
    public OpponentExportResponse createExportTask(OpponentExportRequest request, String tenantOid, String operatorOid) {
        if (OpponentEntityTypeEnum.getByType(request.getExportType()) == null) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_TYPE_ERROR);
        }
        UserAccount tenant = userCenterService.getUserAccountDetailByOid(tenantOid);
        UserAccount operator = userCenterService.getUserAccountBaseByOid(operatorOid);
        long total = getExportTotal(request, tenant, operatorOid);
        if (total == 0) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_EXPORT_NOT_EXIST_DATA);
        }
        if (total > systemConfig.getOpponentMaxExportSize()) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_EXPORT_MAX_SIZE_ERROR, systemConfig.getOpponentMaxExportSize());
        }
        String uniCode = "opponent_" + UUIDUtil.genUUID();
        addTask(tenantOid, operator, total, tenant, uniCode, request.getExportType());
        OpponentExportMsg input = new OpponentExportMsg();
        BeanUtils.copyProperties(request, input);
        input.setUniCode(uniCode);
        input.setOperatorOid(operatorOid);
        input.setTenantOid(tenantOid);
        opponentMqProducer.sendMessage(JsonUtils.obj2json(input), OpponentBusinessTagEnum.EXPORT.getType());
        LogRecordContext.putVariable(AuditLogConstant.Field.TOTAL_SIZE, total);
        return new OpponentExportResponse(uniCode, total);
    }

    @Override
    public void doExportTask(OpponentExportMsg msg) {
        if (OpponentEntityTypeEnum.getByType(msg.getExportType()) == null) {
            return;
        }
        boolean success = false;
        String message = null;
        String fileKey = null;
        try {
            fileKey = exportAndGetFileKey(msg);
            if (StringUtils.isNotEmpty(fileKey)) {
                success = true;
            } else {
                message = "导出文件生成失败";
            }
        } catch (Exception e) {
            message = BaseResultCodeEnum.SYSTEM_ERROR.getMessage();
            if (e instanceof IOException) {
                message = "数据写入excel异常";
            }
            log.error("handle export opponent message fail", e);
        } finally {
            /** 更新任务状态 */
            if (StringUtils.isNotBlank(msg.getUniCode())) {
                SaasTaskUpdateInput saasInput = new SaasTaskUpdateInput();
                saasInput.setBizId(msg.getUniCode());
                if (OpponentEntityTypeEnum.ORGANIZATION.getType() == msg.getExportType()) {
                    saasInput.setType(TaskTypeEnum.EXPORT_OPPONENT_ORGANIZATION.getType());
                } else {
                    saasInput.setType(TaskTypeEnum.EXPORT_OPPONENT_INDIVIDUAL.getType());
                }
                saasInput.setStatus(success ? TaskStatusEnum.FINISH.getType() : TaskStatusEnum.FAILED.getType());
                if (success) {
                    BatchExportKeyModel result = new BatchExportKeyModel();
                    result.setExportFileKey(fileKey);
                    saasInput.setResult(JsonUtils.obj2json(result));
                } else {
                    saasInput.setReason(message);
                }
                saasCommonClient.updateTask(saasInput);
            }
        }
    }

    private String exportAndGetFileKey(OpponentExportMsg msg) throws IOException {
        int pageSize = 100;
        int pageNum = 1;
        String scrollId = null;
        String fileKey;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String filePath = "/tmp/" + msg.getTenantOid() + "_" + simpleDateFormat.format(new Date()) + ".xlsx";
        File file = new File(filePath);
        FileOutputStream fileOutputStream = new FileOutputStream(filePath);
        ExcelWriter writer = EasyExcelFactory.getWriter(fileOutputStream);
        Sheet sheet = new Sheet(1, 1);
        sheet.setSheetName("sheet");
        sheet.setAutoWidth(Boolean.TRUE);
        long done = 0;
        long tempCount = 0;
        try {
            if (OpponentEntityTypeEnum.ORGANIZATION.getType() == msg.getExportType()) {
                sheet.setHead(JSON.parseObject(JSON.toJSONString(organizationHeaderRow), new TypeReference<List<List<String>>>() {
                }));
            } else {
                sheet.setHead(JSON.parseObject(JSON.toJSONString(individualHeaderRow), new TypeReference<List<List<String>>>() {
                }));
            }
            while (true) {
                List<List<Object>> fieldListObject = new ArrayList<>();
                if (OpponentEntityTypeEnum.ORGANIZATION.getType() == msg.getExportType()) {
                    OpponentOrganizationListResponse opponentOrganizationListResponse = queryOrgList(msg, pageSize, pageNum, scrollId);
                    if (opponentOrganizationListResponse == null) {
                        break;
                    }
                    scrollId = opponentOrganizationListResponse.getScrollId();
                    done += opponentOrganizationListResponse.getOrganizations().size();
                    tempCount += opponentOrganizationListResponse.getOrganizations().size();
                    for (OpponentOrganizationResponse opponentOrganizationResponse : opponentOrganizationListResponse.getOrganizations()) {
                        OpponentEntityCreditCodeTypeEnum byCode = OpponentEntityCreditCodeTypeEnum.getByCode(opponentOrganizationResponse.getCreditCodeType());
                        Object[] dataRow = new Object[] { opponentOrganizationResponse.getOrganizationName(),
                                byCode == null ? "" : byCode.getName(),
                                opponentOrganizationResponse.getSocialCreditCode(),
                                opponentOrganizationResponse.getLegalPersonName(),
                                AuthorizeTypeEnum.getDescByType(opponentOrganizationResponse.getAuthorizeType()),
                                StringValidUtil.replaceOpponentEntityDesc(opponentOrganizationResponse.getDesc()),
                                opponentOrganizationResponse.getMemberCount(),
                                OpponentRiskLevelEnum.BLACKLIST.getType() == opponentOrganizationResponse.getRiskLevel() ? "是" : "否" };
                        fieldListObject.add(Arrays.asList(dataRow));
                    }
                } else {
                    OpponentIndividualListResponse opponentIndividualListResponse = queryIndividualList(msg, pageSize, pageNum, scrollId);
                    if (opponentIndividualListResponse == null) {
                        break;
                    }
                    scrollId = opponentIndividualListResponse.getScrollId();
                    done += opponentIndividualListResponse.getIndividuals().size();
                    tempCount += opponentIndividualListResponse.getIndividuals().size();
                    for (OpponentIndividualResponse individualResponse : opponentIndividualListResponse.getIndividuals()) {
                        if(CollectionUtils.isNotEmpty(individualResponse.getOrganizationList())){
                            for (OrganizationInfoBO info : individualResponse.getOrganizationList()){
                                Object[] dataRow = new Object[] { individualResponse.getIndividualName(), info.getOrganizationName(), individualResponse.getContact(),
                                        AuthorizeTypeEnum.getDescByType(individualResponse.getAuthorizeType()), StringValidUtil.replaceOpponentEntityDesc(individualResponse.getDesc()),
                                        OpponentRiskLevelEnum.BLACKLIST.getType() == individualResponse.getRiskLevel() ? "是" : "否" };
                                fieldListObject.add(Arrays.asList(dataRow));
                            }
                            continue;
                        }
                        Object[] dataRow = new Object[] { individualResponse.getIndividualName(), "", individualResponse.getContact(),
                                AuthorizeTypeEnum.getDescByType(individualResponse.getAuthorizeType()), individualResponse.getDesc(),
                                OpponentRiskLevelEnum.BLACKLIST.getType() == individualResponse.getRiskLevel() ? "是" : "否" };
                        fieldListObject.add(Arrays.asList(dataRow));
                    }
                }
                pageNum++;
                writer.write1(fieldListObject, sheet);
                //每处理500条更新一次进度
                if (tempCount >= 500) {
                    updateTaskProcess(msg.getUniCode(), done, msg.getExportType());
                    tempCount = 0;
                }
            }
            writer.finish();
            fileOutputStream.close();
            fileKey = fileSystemClient.uploadFile(FileUtils.readFileToByteArray(file), filePath.replace("/tmp/", ""));
        } finally {
            if (file.exists()) {
                file.deleteOnExit();
            }
        }
        return fileKey;
    }

    private OpponentIndividualListResponse queryIndividualList(OpponentExportMsg msg, int pageSize, int pageNum, String scrollId) {
        OpponentIndividualListRequest request = new OpponentIndividualListRequest();
        request.setPageSize(pageSize);
        request.setPageNum(pageNum);
        request.setAuthorizeType(msg.getAuthorizeType());
        request.setRiskLevel(msg.getRiskLevel());
        request.setFuzzyDesc(msg.getFuzzyDesc());
        request.setFuzzyIndividualName(msg.getName());
        request.setFuzzyEntityUniqueId(msg.getFuzzyEntityUniqueId());
        request.setAttachedEntityType(msg.getAttachedEntityType());
        request.setScrollId(scrollId);
        request.setUseScroll(true);
        OpponentIndividualListResponse opponentIndividualListResponse = opponentEntityService.listIndividuals(msg.getOperatorOid(), msg.getTenantOid(), msg.getOrganizationId(), request, true);
        if (CollectionUtils.isEmpty(opponentIndividualListResponse.getIndividuals())) {
            return null;
        }
        return opponentIndividualListResponse;
    }

    private OpponentOrganizationListResponse queryOrgList(OpponentExportMsg msg, int pageSize, int pageNum, String scrollId) {
        OpponentOrganizationListRequest request = new OpponentOrganizationListRequest();
        request.setPageSize(pageSize);
        request.setPageNum(pageNum);
        request.setAuthorizeType(msg.getAuthorizeType());
        request.setRiskLevel(msg.getRiskLevel());
        request.setFuzzyDesc(msg.getFuzzyDesc());
        request.setFuzzyOrganizationName(msg.getName());
        request.setScrollId(scrollId);
        request.setUseScroll(true);
        OpponentOrganizationListResponse opponentOrganizationListResponse = opponentEntityService.listOrganizations(msg.getOperatorOid(), msg.getTenantOid(), request, true);
        if (CollectionUtils.isEmpty(opponentOrganizationListResponse.getOrganizations())) {
            return null;
        }
        return opponentOrganizationListResponse;
    }

    private void updateTaskProcess(String taskBizId, Long done, Integer exportType) {
        /** 更新任务状态 */
        if (StringUtils.isNotBlank(taskBizId)) {
            try {
                SaasTaskUpdateInput saasInput = new SaasTaskUpdateInput();
                saasInput.setBizId(taskBizId);
                if (OpponentEntityTypeEnum.ORGANIZATION.getType() == exportType) {
                    saasInput.setType(TaskTypeEnum.EXPORT_OPPONENT_ORGANIZATION.getType());
                } else {
                    saasInput.setType(TaskTypeEnum.EXPORT_OPPONENT_INDIVIDUAL.getType());
                }
                saasInput.setDone(done);
                saasCommonClient.updateTask(saasInput);
            } catch (Exception e) {
                log.warn("updateOpponentTask failed!", e);
            }
        }
    }

    private void addTask(String tenantOid, UserAccount operator, long total, UserAccount tenant, String uniCode, Integer exportType) {
        SaasTaskAddInput taskAddInput = new SaasTaskAddInput();
        List<TaskAddBean> taskAddBeans = new ArrayList<>();
        TaskAddBean taskAddBean = new TaskAddBean();
        String taskName = tenant.getAccountName() + "-";
        if (OpponentEntityTypeEnum.ORGANIZATION.getType() == exportType) {
            taskAddBean.setType(TaskTypeEnum.EXPORT_OPPONENT_ORGANIZATION.getType());
            taskName += TaskTypeEnum.EXPORT_OPPONENT_ORGANIZATION.getDesc();
        } else {
            taskAddBean.setType(TaskTypeEnum.EXPORT_OPPONENT_INDIVIDUAL.getType());
            taskName += TaskTypeEnum.EXPORT_OPPONENT_INDIVIDUAL.getDesc();
        }
        taskAddBean.setBizId(uniCode);
        taskAddBean.setBizId(uniCode);
        taskAddBean.setName(taskName);
        TaskBizInfo taskBizInfo = new TaskBizInfo();
        taskBizInfo.setOrganizationId(tenantOid);
        taskAddBean.setBizInfo(taskBizInfo);
        taskAddBean.setTotal(total);
        taskAddBean.setAccountOid(operator.getAccountOid());
        taskAddBean.setAccountGid(operator.getAccountGid());
        taskAddBeans.add(taskAddBean);

        taskAddInput.setTasks(taskAddBeans);
        saasCommonClient.addTasks(taskAddInput);
    }

    private long getExportTotal(OpponentExportRequest request, UserAccount tenant, String operatorOid) {
        OpponentEntityDO organization = null;
        long total;
        if (StringUtils.isNotBlank(request.getOrganizationId())) {
            organization = opponentEntityDAO.getByUuid(request.getOrganizationId());
        }
        if (CommonBizConfig.OPPONENT_QUERY_SWITCH) {
            OpponentEntityQueryModel opponentEntityQueryModel = new OpponentEntityQueryModel();
            opponentEntityQueryModel.setGid(tenant.getAccountGid());
            opponentEntityQueryModel.setEntityType(request.getExportType());
            opponentEntityQueryModel.setRiskLevel(request.getRiskLevel());
            if (request.getAuthorizeType() != null && AuthorizeTypeEnum.ACCEPT.getType() == request.getAuthorizeType()){
                opponentEntityQueryModel.setAuthorizeTypeList(AuthorizeTypeEnum.getAllAcceptTypes());
            }else {
                opponentEntityQueryModel.setAuthorizeType(request.getAuthorizeType());
            }
            opponentEntityQueryModel.setFuzzyDesc(request.getFuzzyDesc());
            opponentEntityQueryModel.setEntityUniqueId(request.getFuzzyEntityUniqueId());
            opponentEntityQueryModel.setEntityName(request.getName());
            opponentEntityQueryModel.setAttachedEntityType(request.getAttachedEntityType());
            opponentEntityQueryModel.setSocialCreditCode(request.getCreditCode());
            opponentEntityQueryModel.setLegalPersonName(request.getLegalPersonName());
            opponentEntityQueryModel.setPageSize(20);
            opponentEntityQueryModel.setPageNum(1);
            if (organization != null) {
                opponentEntityQueryModel.setAttachedEntityId(organization.getId());
            }
            opponentEntityService.populateOpponentMultiBizQuery(tenant.getAccountOid(), tenant.getAccountGid(),
                    operatorOid, opponentEntityQueryModel);
            OpponentEntityQueryResult opponentEntityQueryResult = opponentSearchClient.pageQuery(opponentEntityQueryModel);
            total = opponentEntityQueryResult.getTotal();
        } else {
            // 构造查询参数
            OpponentEntityDTO dto = new OpponentEntityDTO(tenant.getAccountGid(), request.getExportType(), request.getRiskLevel(), null, null,
                    request.getAuthorizeType(), request.getFuzzyDesc(), request.getFuzzyEntityUniqueId(), request.getAttachedEntityType());
            dto.setEntityName(request.getName());
            // 如果传入企业相对方实体,则查询相关信息
            if (organization != null) {
                dto.setAttachedEntityId(organization.getId());
            }
            total = opponentEntityDAO.countOpponentEntity(dto);
        }
        return total;
    }

}

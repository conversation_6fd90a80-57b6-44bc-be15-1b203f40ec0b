package com.timevale.contractmanager.core.service.component.autoarchive;

import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.contractmanager.common.dal.bean.autoarchive.AutoArchiveOperatorConfigDO;
import com.timevale.contractmanager.common.dal.bean.autoarchive.AutoArchiveOperatorDO;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.dto.autoarchive.AutoArchiveTreeDTO;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author:jianyang
 * @since 2021-05-08 17:19
 */
@Component
@Slf4j
public class AutoArchiveTreeComponent {

	public static final String AUTOARCHIVE = "AUTOARCHIVECAHE";
	@Autowired
	private LockFactory lockFactory;

	/**
	 * 分布式锁更新规则树
	 * @param tenantGid
	 * @param ruleId
	 * @param operatorDOS
	 */
	public void cacheRuleTree(String tenantGid,String ruleId,List<AutoArchiveOperatorDO> operatorDOS){
		log.info("insertAutoArchiveRuleWithLock :{}", ruleId+tenantGid);
		Elock lock = lockFactory.getLock(AUTOARCHIVE + ruleId + tenantGid);
		boolean isAcquired = lock.tryLock(1, TimeUnit.SECONDS);
		log.info(
				"autoArchiveRuleWithLock isAcquired lock :ruleId :{} tenantGid :{}  true/false :{}",ruleId,tenantGid,
				isAcquired);
		if(isAcquired){
			try{
				//构造树
				genRuleTree(tenantGid,operatorDOS,ruleId);
			}catch (Exception e){
				log.error("gen rule tree error:{}",e.getMessage());
				throw new BizContractManagerException(BizContractManagerResultCodeEnum.UPDATE_RULE_OR_GEN_TREE_ERROR.getCode(),
						BizContractManagerResultCodeEnum.INSERT_RULE_OR_GEN_TREE_ERROR.getMessage());
			}finally{
				log.info("autoArchiveRuleWithLock finally unlock :ruleId :{}  tenantGid :{} ",
						ruleId,tenantGid);
				lock.unlock();
			}
		}else {
			log.warn("等待后, 仍未获取到锁！ruleId :{}  tenantGid :{}", ruleId,tenantGid);
		}
	}

	/**
	 * 缓存或更新redis中的规则树
	 * @param tenantGid
	 * @param operatorDOS
	 * @param ruleId
	 * @return
	 */
	public Boolean genRuleTree(String tenantGid, List<AutoArchiveOperatorDO> operatorDOS,String ruleId){
		List<AutoArchiveTreeDTO> treeDTOS = new ArrayList<>();
		operatorDOS.stream().filter(x -> StringUtils.isBlank(x.getParentOperatorId())).forEach(x ->{
			AutoArchiveTreeDTO autoArchiveTreeDTO = new AutoArchiveTreeDTO();
			autoArchiveTreeDTO.setFieldId(x.getFieldId());
			autoArchiveTreeDTO.setFielType(x.getFieldType());
			autoArchiveTreeDTO.setOperatorType(x.getOperatorType());
			autoArchiveTreeDTO.setMatchType(x.getMatchType());
			autoArchiveTreeDTO.setChildOperators(converChild(operatorDOS,x.getOperatorId()));
			treeDTOS.add(autoArchiveTreeDTO);
		});
		if(!treeDTOS.isEmpty()){
			String key = CacheUtil.getAutoArchiveRuleKey(tenantGid,ruleId);

			if(TedisUtil.get(key) != null){
				TedisUtil.delete(key);
			}
			TedisUtil.set(key,JsonUtils.obj2json(treeDTOS),1, TimeUnit.DAYS);
			return true;
		}
		return false;
	}

	/**
	 * 构造子类
	 * @param operatorDOS
	 * @return
	 */
	public List<AutoArchiveTreeDTO> converChild(List<AutoArchiveOperatorDO> operatorDOS,String parentOperatorId){
		List<AutoArchiveTreeDTO> treeDTOSChild = new ArrayList<>();
		operatorDOS.stream()
				.filter(x -> StringUtils.isNotBlank(x.getParentOperatorId()) && x.getParentOperatorId().equals(parentOperatorId))
				.forEach(x ->{
					AutoArchiveTreeDTO child = new AutoArchiveTreeDTO();
					child.setOperatorType(x.getOperatorType());
					child.setConditionParams(x.getConditionParams());
					treeDTOSChild.add(child);
				});
		return treeDTOSChild;
	}
}

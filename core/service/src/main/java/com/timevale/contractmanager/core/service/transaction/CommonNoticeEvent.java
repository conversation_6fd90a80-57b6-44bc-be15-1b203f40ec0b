package com.timevale.contractmanager.core.service.transaction;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class CommonNoticeEvent extends ApplicationEvent {

    /** 消息内容 */
    private String msgBody;
    /** 通知类型 {@link CommonNoticeType} */
    private CommonNoticeType noticeType;

    public CommonNoticeEvent(Object source, String msgBody, CommonNoticeType noticeType) {
        super(source);
        this.msgBody = msgBody;
        this.noticeType = noticeType;
    }
}

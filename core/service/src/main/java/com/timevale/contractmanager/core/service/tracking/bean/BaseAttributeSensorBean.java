package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @Author:jianyang
 * @since 2022-03-16 10:44
 */
@Data
public class BaseAttributeSensorBean extends ToString {

	//http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=140147923&zToken=f716e757-2b7b-4df4-a5e5-ee869e8ffe2a
	public String entityType;
	public String entityName;
	public String authorizedOid;
	public String authorizedGid;
	public String operatorOid;
	public String operatorGid;
	public String userRole;
	public Boolean isFirstTime;
	public String platformType;
	public String module;
	public String vipVersion;
	public Integer operatorEntityNum;
	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData.put(ENTITY_TYPE, sensorString(entityType));
		sensorData.put(ENTITY_NAME, sensorString(entityName));
		sensorData.put(AUTHORIZED_OID, sensorString(authorizedOid));
		sensorData.put(AUTHORIZED_GID, sensorString(authorizedGid));
		sensorData.put(OPERATOR_OID, sensorString(operatorOid));
		sensorData.put(OPERATOR_GID, sensorString(operatorGid));
		sensorData.put(USER_ROLE, sensorString(userRole));
		sensorData.put(IS_FIRST_TIME, sensorString(isFirstTime));
		sensorData.put(PLATFORM_TYPE, sensorString(platformType));
		sensorData.put(MODULE, sensorString(module));
		sensorData.put(VIP_VERSION, sensorString(vipVersion));
		sensorData.put(OPERATOR_ENTITY_NUM, sensorString(operatorEntityNum));
		return sensorData;
	}
}

package com.timevale.contractmanager.core.service.authrelation.impl;

import com.timevale.contractmanager.common.service.integration.client.AuthRelationInnerClient;
import com.timevale.contractmanager.core.service.authrelation.AuthRelationBizService;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by tianlei on 2022/2/21
 */
@Slf4j
@Service
public class AuthRelationBizServiceImpl implements AuthRelationBizService {

    @Autowired
    private AuthRelationInnerClient authRelationInnerClient;

    @Override
    public boolean aboutAuthRelationProcess(String processId, String tenantGid) {
        AuthRelationLogDTO authRelationLogDTO = authRelationInnerClient.getAuthRelationLogByProcessId(processId);
        if(authRelationLogDTO == null){
            return false;
        }
        if(authRelationLogDTO.getParentTenantGid().equals(tenantGid) || authRelationLogDTO.getChildTenantGid().equals(tenantGid)
                || tenantGid.equals(authRelationLogDTO.getAuthTenantGid())){
            return true;
        }
        if(CollectionUtils.isEmpty(authRelationLogDTO.getHierarchyGidSnapshot())){
            return false;
        }
        return authRelationLogDTO.getHierarchyGidSnapshot().contains(tenantGid);
    }

}

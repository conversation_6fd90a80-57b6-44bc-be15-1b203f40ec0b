package com.timevale.contractmanager.core.service.opponent.impl;

import com.alibaba.fastjson.JSONObject;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityRelationDAO;
import com.timevale.contractmanager.common.service.integration.client.ResourceBelongClient;
import com.timevale.contractmanager.core.service.mq.model.SyncEsMsg;
import com.timevale.contractmanager.core.service.mq.model.opponent.OpponentEntityEsInfo;
import com.timevale.contractmanager.core.service.mq.producer.opponent.OpponentEntitySyncEsProducer;
import com.timevale.contractmanager.core.service.opponent.OpponentSyncEsService;
import com.timevale.saas.common.manage.common.service.enums.resourcebelong.ResourceBelongTypeEnum;
import com.timevale.saas.common.manage.common.service.model.output.resourcebelong.ResourceBelongDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/5 14:38
 */
@Service
public class OpponentSyncEsServiceImpl implements OpponentSyncEsService {

    @Autowired
    private OpponentEntityDAO opponentEntityDAO;
    @Autowired
    private OpponentEntitySyncEsProducer opponentEntitySyncEsProducer;
    @Autowired
    private OpponentEntityRelationDAO opponentEntityRelationDAO;
    @Autowired
    private ResourceBelongClient resourceBelongClient;

    @Override
    public void syncEsByResourceBelongChange(String opponentEntityUuid) {
        if (StringUtils.isBlank(opponentEntityUuid)) {
            return;
        }
        OpponentEntityDO opponentEntityDO = opponentEntityDAO.getByUuid(opponentEntityUuid);
        if (null == opponentEntityDO) {
            return;
        }
        doBeginSync(SyncEsMsg.UPSERT_ACTION, opponentEntityDO.getId(), opponentEntityDO);
    }

    @Override
    public void beginSyncEs(String action, Long opponentEntityId) {
        if (null == opponentEntityId) {
            return;
        }
        // 查询最新的数据 封装es对象
        OpponentEntityDO entity = opponentEntityDAO.getById(opponentEntityId);
        doBeginSync(action, opponentEntityId, entity);
    }

    private void doBeginSync(String action, Long opponentEntityId, OpponentEntityDO entity) {
        SyncEsMsg<OpponentEntityEsInfo> syncEsMsg = new SyncEsMsg<>();
        syncEsMsg.setAction(action);
        syncEsMsg.setDocId(opponentEntityId.toString());
        if (entity == null) {
            // 发送消息同步es
            syncEsMsg.setAction(SyncEsMsg.UPSERT_DELETE);
            opponentEntitySyncEsProducer.sendMsg(JSONObject.toJSONString(syncEsMsg));
            return;
        }
        OpponentEntityEsInfo info = new OpponentEntityEsInfo();
        info.setId(entity.getId());
        info.setUuid(entity.getUuid());
        info.setTenant_oid(entity.getTenantOid());
        info.setTenant_gid(entity.getTenantGid());
        info.setAuthorize_type(entity.getAuthorizeType());
        info.setCreate_by_oid(entity.getCreateByOid());
        info.setCreate_by_gid(entity.getCreateByGid());
        info.setCreate_time(entity.getCreateTime().getTime());
        info.setModify_by_oid(entity.getModifyByOid());
        info.setModify_by_gid(entity.getModifyByGid());
        info.setModified_time(entity.getModifiedTime().getTime());
        info.setRisk_level(entity.getRiskLevel());
        info.setCredit_code(entity.getSocialCreditCode());
        info.setCredit_code_type(entity.getCreditCodeType());
        info.setRepresentative_name(entity.getLegalRepresentativeName());
        info.setEntity_name(entity.getEntityName());
        info.setEntity_unique_id(entity.getEntityUniqueId());
        info.setEntity_type(entity.getEntityType());
        info.setDeleted(entity.getDeleted());
        info.setEntity_oid(entity.getEntityOid());
        info.setEntity_gid(entity.getEntityGid());
        info.setDetection_org_id(entity.getDetectionOrgId());
        info.setDescription(entity.getDescription());
        info.setCreate_process_id(entity.getCreateProcessId());
        info.setAttached_entity_id(entity.getAttachedEntityId());

        opponentEntityRelationDAO.queryOrgIdByPersonId(entity.getId());
        List<Long> ids = opponentEntityRelationDAO.queryOrgIdByPersonId(entity.getId());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<OpponentEntityDO> orgList = opponentEntityDAO.getByIdList(ids);
            if (CollectionUtils.isNotEmpty(orgList)) {
                List<OpponentEntityEsInfo.OrganizationInfo> orgInfoList =
                        orgList.stream()
                                .map(
                                        p -> {
                                            OpponentEntityEsInfo.OrganizationInfo orgInfo =
                                                    new OpponentEntityEsInfo
                                                            .OrganizationInfo();
                                            orgInfo.setId(p.getId());
                                            orgInfo.setUuid(p.getUuid());
                                            orgInfo.setOid(p.getEntityOid());
                                            orgInfo.setOrg_name(p.getEntityName());
                                            return orgInfo;
                                        })
                                .collect(Collectors.toList());
                info.setOrganization_info_list(orgInfoList);
            }
        }
        // 设置资源归属
        populateResourceBelong(info);
        syncEsMsg.setContent(info);
        // 发送消息同步es
        opponentEntitySyncEsProducer.sendMsg(JSONObject.toJSONString(syncEsMsg));
    }

    private void populateResourceBelong(OpponentEntityEsInfo info) {
        List<ResourceBelongDTO> resourceBelong =
                resourceBelongClient.getResourceBelong(info.getUuid(), ResourceBelongTypeEnum.OPPONENT.getCode());
        List<String> allBelongDept = resourceBelong.stream().map(ResourceBelongDTO::getDeptPath)
                .flatMap(List::stream).distinct().collect(Collectors.toList());
        info.setDeptPath(allBelongDept);
    }

}

package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.APP_ID;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.FLOW_ID;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.FLOW_TYPE;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.PROCESS_ID;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.TRANSFER_TYPE;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.TRANSFER_WAY;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * <AUTHOR>
 * @since 2021-03-25
 */
@Data
public class ProcessTransferSensorBean extends ToString {

    /** 流程id */
    private String processId;

    /** 签署流程id */
    private String flowId;

    /** 签署流程id */
    private String flowType;

    /** 应用id */
    private String appId;

    /** 转交类型 */
    private String transferType;

    /** 转交模式 */
    private String transferWay;

    public Map<String, Object> sensorData() {
        Map<String, Object> sensorData = Maps.newHashMap();
        sensorData.put(PROCESS_ID,  sensorString(processId));
        sensorData.put(FLOW_ID, sensorString(flowId));
        sensorData.put(FLOW_TYPE, sensorString(flowType));
        sensorData.put(APP_ID, sensorString(appId));
        sensorData.put(TRANSFER_TYPE, sensorString(transferType));
        sensorData.put(TRANSFER_WAY, sensorString(transferWay));
        return sensorData;
    }
}

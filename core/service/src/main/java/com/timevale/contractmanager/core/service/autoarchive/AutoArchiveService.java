package com.timevale.contractmanager.core.service.autoarchive;

import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveRuleModel;
import com.timevale.contractmanager.common.service.result.autoarchive.ProcessStatusResult;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveOperatorRequest;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveUpdateOperatorRequest;
import com.timevale.contractmanager.core.model.dto.response.autoArchive.AutoArchiveMenuResponse;
import com.timevale.contractmanager.core.model.dto.response.autoArchive.AutoArchiveRuleResponse;
import com.timevale.contractmanager.core.model.dto.user.SimpleUserAccountInfoResponse;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-05-06 19:31
 */
public interface AutoArchiveService {

	/**
	 *
	 * @param tenantOid
	 * @param operatorRequest
	 * @param operatorId
	 */
	void createAutoArchiveOperator(String tenantOid, AutoArchiveOperatorRequest operatorRequest,String operatorId);


	/**
	 *
	 * @param ruleId
	 * @param tenantId
	 * @param status
	 * @return
	 */
	String updateRuleStatus(String ruleId,String tenantId, String operatorOid, Integer status, boolean checkEditPermission);

	void updateRuleStatus(String ruleId, Integer status);


	/**
	 *
	 * @param tenantOid
	 * @param operatorId
	 * @return
	 */
	AutoArchiveMenuResponse list(String tenantOid, String operatorId);

	/**
	 *
	 * @param ruleId
	 * @param tenantOid
	 * @return
	 */
	AutoArchiveRuleResponse getAutoArchiveRule(String ruleId, String tenantOid);

	/**
	 *
	 * @param ruleId
	 * @param updateOperatorRequest
	 * @param tenantId
	 * @param operatorId
	 */
	void updateRule(String ruleId, AutoArchiveUpdateOperatorRequest updateOperatorRequest,String tenantId,String operatorId, boolean checkEditPermission);


	/**
	 *
	 * @param accountOids
	 * @return
	 */
	List<SimpleUserAccountInfoResponse> getAccountDetail(List<String> accountOids);

	/**
	 * 解绑分类
	 * @param menuId
	 * @param tenantOid
	 */
	void unBindRule(String menuId,String tenantOid);

	/**
	 * 为老的未关联过台账的分类新增规则
	 * @param tenantOid
	 * @param operatorRequest
	 * @param operatorId
	 * @return 结果code，0 成功 其他标识需要前端特殊处理
	 */
	Integer bindRule(String tenantOid, AutoArchiveOperatorRequest operatorRequest,String operatorId);

	/**
	 * 重新运行规则
	 * @param tenantId
	 * @param ruleId
	 */
	void runRule(String tenantId, String ruleId);

	/**
	 * 置顶/取消置顶
	 * @param ruleId
	 * @param tenantId
	 * @param operatorId
	 */
	void updateTopTime(String ruleId, String tenantId, String operatorId);

	List<AutoArchiveRuleModel> listRuleByTenantIdAndStatus(String tenantGid, List<Integer> status);

	void addArchiveNum(String ruleId, Integer num);

	String getRuleIdByMenuId(String menuId, String tenantId);

	/**
	 * 解绑归档规则
	 * @param menuId
	 * @param tenantId
	 * @param operatorId
	 */
	void unbindRuleMenu(String menuId, String tenantId, String operatorId);

	/**
	 * 判断历史是否存在过归档条件
	 * @param tenantGid
	 * @return
	 */
	boolean existArchiveRule(String tenantGid);

	ProcessStatusResult getOptionalStatusList(String operatorId);
}

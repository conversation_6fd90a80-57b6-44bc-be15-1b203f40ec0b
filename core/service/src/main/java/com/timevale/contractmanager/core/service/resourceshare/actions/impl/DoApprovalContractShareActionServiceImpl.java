package com.timevale.contractmanager.core.service.resourceshare.actions.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.account.flow.service.constant.GlobalConstant;
import com.timevale.account.flow.service.enums.ApprovalApplyBizTypeEnum;
import com.timevale.account.flow.service.enums.ApprovalStatusEnum;
import com.timevale.account.flow.service.enums.ClientTypeEnum;
import com.timevale.account.flow.service.model.output.ApprovalApplyDetailOutput;
import com.timevale.account.flow.service.model.output.ApprovalUrlOutput;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.enums.SubProcessTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.ApprovalClient;
import com.timevale.contractmanager.common.service.integration.client.ShortUrlClient;
import com.timevale.contractmanager.core.service.contractapproval.ContractApprovalService;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalFlowInfo;
import com.timevale.contractmanager.core.service.contractapproval.param.GetContractApprovalUrlDTO;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.resourceshare.actions.DoResourceShareActionService;
import com.timevale.contractmanager.core.service.util.UrlUtil;
import com.timevale.esign.compontent.simple.encrypt.SimpleCipher;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.enums.share.ResourceTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.share.ShareOperateTypeEnum;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceUrlActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoProcessParticipantAuthRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceShareActionResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceUrlActionResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoProcessParticipantAuthResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoResourceShareActionResponseDTO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Objects;

/**
 * 获取合同审批
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/17 11:09
 */
@Log4j2
@Service
public class DoApprovalContractShareActionServiceImpl implements DoResourceShareActionService {

    private static final String RESOURCE_NAME = "《%s》";

    @Autowired private BaseProcessService baseProcessService;
    @Autowired private ApprovalClient approvalClient;
    @Autowired private ShortUrlClient shortUrlClient;
    @Autowired private ContractApprovalService contractApprovalService;


    @Override
    public String getResourceType() {
        return ResourceTypeEnum.APPROVAL_CONTRACT_FLOW.name();
    }

    @Override
    public DoGetResourceShareActionResponseDTO doGetResourceShareAction(DoGetResourceShareActionRequestDTO request) {
      return null;
    }

    @Override
    public DoGetResourceUrlActionResponseDTO doGetResourceUrlAction(DoGetResourceUrlActionRequestDTO request) {
        log.info(" recive get resource url,request {} ", JSON.toJSONString(request));
        // 获取合同审批子流程
        SubProcessDO subProcess = checkAndGetSubProcess(request.getResourceId(), request.getShareOperateType());
        //获取合同审批链接
        String url = checkAndGetContractApprovalUrl(subProcess, request);

        String resourceUrl = url.concat("&resourceShareId=")
                        .concat(request.getResourceShareId());
        DoGetResourceUrlActionResponseDTO responseDTO = new DoGetResourceUrlActionResponseDTO();
        responseDTO.setResourceUrl(resourceUrl);
        return responseDTO;
    }

    @Override
    public DoProcessParticipantAuthResponseDTO doProcessParticipantAuth(DoProcessParticipantAuthRequestDTO requestDTO) {
        return null;
    }

    @Override
    public DoResourceShareActionResponseDTO doResourceShareAction(DoResourceShareActionRequestDTO request) {
        // 获取合同审批子流程
        SubProcessDO subProcess = checkAndGetSubProcess(request.getResourceId(), request.getShareOperateType());
        // 获取合同审批基本信息
        ApprovalFlowInfo output = this.checkAndGetContractApproval(subProcess, request.getAccountId());
        return DoResourceShareActionResponseDTO.builder()
                .initiatorAccountId(output.getInitiatorOid())
                .initiatorSubjectId(output.getSubjectOid())
                .resourceName(String.format(RESOURCE_NAME, output.getApprovalFlowName()))
                .build();
    }

    /**
     * 查询合同审批详情信息-包含校验
     * @param subProcess
     * @param accountId
     * @return
     */
    private ApprovalFlowInfo checkAndGetContractApproval(SubProcessDO subProcess, String accountId) {
        // 如果当前子流程为合同审批子流程， 查询新合同审批基本信息
        if (SubProcessTypeEnum.CONTRACT_APPROVAL.getType() == subProcess.getSubProcessType()) {
            return contractApprovalService.queryApprovalInfoByApprovalId(subProcess.getSubProcessId());
        }
        //查询合同审批流程
        ApprovalApplyDetailOutput output = approvalClient.getApprovalDetailByBizId(subProcess.getSubProcessId(), ApprovalApplyBizTypeEnum.SIGN.getCode().toString(), accountId);
        if (Objects.isNull(output)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.CONTRACT_APPROVAL_NOT_EXIST);
        }
        if (!ApprovalStatusEnum.APPROVALING.getValue().equals(output.getApprovalOutput().getStatus())) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.CONTRACT_APPROVAL_SHARE_RUSH_STATUS_DISABLE);
        }

        ApprovalFlowInfo approvalFlowInfo = new ApprovalFlowInfo();
        approvalFlowInfo.setApprovalFlowId(String.valueOf(output.getApprovalOutput().getApprovalId()));
        approvalFlowInfo.setApprovalFlowName(output.getApprovalOutput().getName());
        approvalFlowInfo.setApprovalFlowStatus(output.getApprovalOutput().getStatus());
        approvalFlowInfo.setSubjectOid(output.getApprovalOutput().getApplyOrgId());
        approvalFlowInfo.setInitiatorOid(output.getApprovalOutput().getApplyAccountId());
        approvalFlowInfo.setInitiatorGid(output.getApprovalOutput().getApplyAccountGid());
        approvalFlowInfo.setBizId(subProcess.getProcessId());
        return approvalFlowInfo;
    }

    /**
     * 获取合同审批所在的子流程
     *
     * @param processId
     * @param shareOperateType
     * @return
     */
    private SubProcessDO checkAndGetSubProcess(String processId, String shareOperateType) {
        // 查询流程信息
        ProcessDO process = baseProcessService.getProcess(processId, null);
        if (Objects.isNull(process)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST);
        }

        // 校验状态
        if (ShareOperateTypeEnum.NORMAL_SHARE.name().equals(shareOperateType)) {
            if (ProcessStatusEnum.APPROVAL.getStatus() != process.getStatus()) {
                // 不在合同审批中，直接报错
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.CONTRACT_APPROVAL_SHARE_RUSH_STATUS_DISABLE);
            }
        } else {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_SHARE_TYPE_NOT_SUPPORT);
        }
        // 查询合同审批中对应的当前子流程
        SubProcessDO subProcess = baseProcessService.getCurrentSubProcess(processId);

        if (Objects.isNull(subProcess)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.SUB_PROCESS_NOT_EXIST);
        }
        return subProcess;
    }

    /**
     * 查询合同审批地址
     *
     * @param subProcess
     * @param request
     * @return
     */
    private String checkAndGetContractApprovalUrl(
            SubProcessDO subProcess, DoGetResourceUrlActionRequestDTO request) {
        ClientTypeEnum clientType = ClientTypeEnum.PC;
        if (request.getPlatform() == 4) {
            clientType = ClientTypeEnum.H5;
        }
        // 获取合同审批基本信息
        ApprovalFlowInfo approvalFlowInfo =
                this.checkAndGetContractApproval(subProcess, request.getAccountId());
        // 新合同审批地址
        if (SubProcessTypeEnum.CONTRACT_APPROVAL.getType() == subProcess.getSubProcessType()) {
            GetContractApprovalUrlDTO param = new GetContractApprovalUrlDTO();
            param.setApprovalFlowId(approvalFlowInfo.getApprovalFlowId());
            param.setToken(request.getToken());
            param.setH5Url(ClientTypeEnum.H5.equals(clientType));
            param.setAccountId(request.getAccountId());
            param.setMenuId(request.getMenuId());
            param.setResourceShareId(request.getResourceShareId());
            return contractApprovalService.getApprovalUrl(param);
        }

        // 老合同审批地址
        long approvalId = Long.parseLong(approvalFlowInfo.getApprovalFlowId());
        ApprovalUrlOutput approvalUrlOutput =
                approvalClient.getClientApprovalUrl(approvalId, request.getAccountId(), clientType.getType());

        String url = approvalUrlOutput.getUrl();
        if (ClientTypeEnum.H5.equals(clientType)) {
            StringBuilder contextBuilder = new StringBuilder();
            if (StringUtils.isNotEmpty(approvalFlowInfo.getSubjectOid())) {
                contextBuilder.append("tenantId=").append(approvalFlowInfo.getSubjectOid());
            }
            if (StringUtils.isNotEmpty(request.getAccountId())) {
                contextBuilder.append("&operatorId=").append(request.getAccountId());
                contextBuilder.append("&accountId=").append(request.getAccountId());
            }
            if (StringUtils.isNotEmpty(request.getToken())) {
                contextBuilder.append("&token=").append(request.getToken());
            }
            if (contextBuilder.length() > 0) {
                // context长度>0，拼接context
                String encodedString =
                        SimpleCipher.INSTANCE.encode(
                                "AES", contextBuilder.toString(), GlobalConstant.DEFAULT_CODE);
                encodedString = encodedString.replaceAll("\\n", "").replaceAll("\\r", "");
                try {
                    encodedString = URLEncoder.encode(encodedString, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    throw new BizContractManagerException(
                            BizContractManagerResultCodeEnum.CONTRACT_APPROVAL_GET_URL_ERROR, "审批链接参数转换错误");
                }
                String shortContext = shortUrlClient.convertShortCode(encodedString);
                url = UrlUtil.appendParam(url, "context", shortContext);
            }
        }
        return url;
    }


}

package com.timevale.contractmanager.core.service.contractprocess.fix;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.HbaseProcessDataAsyncCollectException;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessFixTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.ProcessIdMsgEntity;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.api.contractprocess.ContractProcessQueryRpcService;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSignTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Created by tianlei on 2022/7/14
 */
@Slf4j
@Service
public class FixSealInfoProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter configCenter;
    @Autowired
    private LockService lockService;
    @Autowired
    private ContractProcessQueryRpcService processQueryClient;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient processWriteClient;


    @Override
    public Route route() {
        return Route.of(configCenter.fixProcessTopicName(), ProcessFixTagEnum.FIX_SEAL_INFO.getCode());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessIdMsgEntity entity = JsonUtils.json2pojo(data, ProcessIdMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        fixSealInfo(collectContext.getProcessId());
    }

    /**
     * 修复印章信息
     */
    public void fixSealInfo(String processId) {
        if (StringUtils.isBlank(processId)) {
            return;
        }
        Lock lock = lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(processId));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                doFixSealInfo(processId);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }

    private void doFixSealInfo(String processId) {
        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);
        if (null == contractProcessDTO) {
            return;
        }
        List<ContractProcessSignTaskDTO> oldSignTaskDTOList = contractProcessDTO.getSignTasks();
        if (CollectionUtils.isEmpty(oldSignTaskDTOList)) {
            return;
        }
        log.info("begin fix seal info {} ", processId);

        List<ContractProcessSignTaskParam> signTaskParamList =
                ProcessDataCollectConverter.signTaskDTO2ParamList(oldSignTaskDTOList);

        // 删除印章信息
        signTaskParamList.forEach(elm -> elm.setSealInfo(null));

        Optional<String> flowIdOptional = signTaskParamList.stream().filter(elm -> StringUtils.isNotBlank(elm.getFlowId()))
                .map(ContractProcessSignTaskParam::getFlowId).findFirst();
        if (!flowIdOptional.isPresent()) {
            return;
        }

        processDataBuilder.fillSealInfo(signTaskParamList, flowIdOptional.get());
        ContractProcessUpdateParam input = new ContractProcessUpdateParam();
        input.setBizScene(ProcessDataCollectBizSceneConstants.FIX_SEAL_INFO);
        input.setProcessId(processId);
        input.setSignTasks(signTaskParamList);
        processWriteClient.updateByProcessId(input);
    }

}

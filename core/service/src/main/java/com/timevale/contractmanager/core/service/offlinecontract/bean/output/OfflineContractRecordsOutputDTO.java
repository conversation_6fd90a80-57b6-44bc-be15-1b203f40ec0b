package com.timevale.contractmanager.core.service.offlinecontract.bean.output;

import com.timevale.contractmanager.common.service.bean.offlinecontract.OfflineContractRecordListBean;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 查询线下合同导入记录列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class OfflineContractRecordsOutputDTO extends ToString {

    /** 总数 */
    private long total;

    /** 线下合同导入记录列表 */
    private List<OfflineContractRecordListBean> records;
}

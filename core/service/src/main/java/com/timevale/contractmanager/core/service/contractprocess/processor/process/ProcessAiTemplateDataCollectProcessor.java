package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessAiTemplateMsgEntity;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessTemplateParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by tianlei on 2022/6/6
 */
@Slf4j
@Component
public class ProcessAiTemplateDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessReadClient contractProcessReadClient;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processTopicName(), ProcessChangeTagEnum.PROCESS_AI_TEMPLATE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessAiTemplateMsgEntity entity = JsonUtils.json2pojo(data, ProcessAiTemplateMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        ProcessAiTemplateMsgEntity entity = (ProcessAiTemplateMsgEntity) collectContext.getData();


        ContractProcessDTO contractProcessDTO = contractProcessReadClient.getByProcessId(processId);
        if (null != contractProcessDTO &&
                null != contractProcessDTO.getTemplateInfo() &&
                null != contractProcessDTO.getTemplateInfo().getTemplateId()) {
            log.info(LOG_PREFIX + "template exist processId : {}", processId);
            return;
        }

        ContractProcessTemplateParam contractProcessTemplateParam=new ContractProcessTemplateParam();
        contractProcessTemplateParam.setTemplateId(entity.getTemplateId());
        contractProcessTemplateParam.setTemplateName(entity.getTemplateName());
        contractProcessTemplateParam.setTemplateType(entity.getTemplateType());
        ContractProcessUpdateParam param = new ContractProcessUpdateParam();
        param.setProcessId(processId);
        param.setTemplateInfo(contractProcessTemplateParam);
        param.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_AI_TEMPLATE);
        contractProcessWriteClient.updateByProcessId(param);

    }



}

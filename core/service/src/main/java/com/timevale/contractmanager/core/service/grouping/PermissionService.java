package com.timevale.contractmanager.core.service.grouping;

import com.timevale.contractmanager.common.service.enums.grouping.FilePermissionEnum;
import com.timevale.contractmanager.common.service.enums.grouping.MenuPermissionEnum;
import com.timevale.contractmanager.common.service.result.grouping.PermissionInfo;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.grouping.impl.menu.BuildMenuAuthorizeTreeResultDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 权限相关服务
 *
 * @author: xuanzhu
 * @since: 2019-09-09 09:52
 */
public interface PermissionService {

    /**
     * 获取角色下的所有操作权限，取并集
     * @param roleList 角色列表
     * @return 角色下的对应操作权限
     *  key:    roleId
     *  value:  权限集合
     */
    Map<String, Set<PermissionInfo>> getPermissionMapByRoleList(List<String> roleList);

    /**
     * 获取对应资源下的具体操作权限
     *
     * <p>从用户中心获取
     *
     * @param resource 资源名称
     * @param tenantId
     * @param operatorOid
     * @return
     */
    List<PermissionInfo> getPermissionListBySource(
            String resource, String tenantId, String operatorOid);

    /**
     * 获取用户对资源的操作权限
     *
     * <p>从用户中心获取
     *
     * @param resource 资源名称
     * @param tenantId 空间Oid
     * @param operatorOid 用户oid
     * @return 权限值如：DOWNLOAD,UPDATE 等
     */
    List<String> getPermissionList(String resource, String tenantId, String operatorOid);

    List<String> getPermissionList(String resource, Collection<String> privilegeKeys, String tenantId, String operatorOid);

    /**
     * 批量获取用户对资源的操作权限，Map<source, Set<permission>>
     * @param resources
     * @param tenantId
     * @param operatorOid
     * @return
     */
    Map<String, Set<String>> getPermissionListMap(
            List<String> resources, String tenantId, String operatorOid);

    /**
     * 获取用户是否为企业法人
     * @param tenantId
     * @param operatorOid
     * @return
     */
    boolean checkLegalPerson(String tenantId, String operatorOid);

    /**
     * 判断当前操作用户是否为企业管理员或法人代表(包括个人空间用户)
     *
     * @param tenantId 企业空间Oid
     * @param operatorOid 当前操作人Oid
     * @return true-是，false-否
     */
    boolean checkAdmin(String tenantId, String operatorOid);

    /**
     * 判断用户是否有管理员权限
     *
     * @param tenantId 企业空间Oid
     * @param operatorOid 当前操作人账号信息
     * @return true-是，false-否
     */
    boolean checkAdminPermission(String tenantId, String operatorOid);

    /**
     * 判断当前操作人是否具有归档的操作权限
     *
     * <p>从用户中心读取配置
     *
     * <p>用途：归档按钮的权限判断
     *
     * <p>1.归档时权限校验
     *
     * @param tenantId 空间Oid
     * @param operatorOid 当前操作人oid
     * @return true-是，false-否
     */
    boolean checkGroupingPermission(String tenantId, String operatorOid);

    /**
     * 判断在某个menu下面是否有权限，权限分成两种：分类操作权限（分类管理员或者企业账号管理员或者在企业控制台分配了企业合同-归档合同管理权限的成员）
     * @param menuId
     * @param operatorOid
     * @param tenantOid
     * @param requiredFilePermissionEnum
     * @param requiredMenuPermissionEnum
     * @param checkMenuPermission requiredFilePermissionEnum非空时为false，否则为true
     * @return
     */
    void checkMenuOperatorPermission(String menuId,
                                        String operatorOid,
                                        String operatorGid,
                                        String tenantOid,
                                        String tenantGid,
                                        FilePermissionEnum requiredFilePermissionEnum,
                                        MenuPermissionEnum requiredMenuPermissionEnum,
                                        Boolean checkMenuPermission);

    /**
     * 判断操作人在某个menu下面是否有分类操作权限（创建分类、移动分类、设置分类权限）
     * @param menuId
     * @param operatorOid
     * @param operatorGid
     * @param tenantOid
     * @param tenantGid
     * @param requiredMenuPermissionEnum
     */
    void checkOperatorHasMenuPermission(String menuId,
                                        String operatorOid,
                                        String operatorGid,
                                        String tenantOid,
                                        String tenantGid,
                                        MenuPermissionEnum requiredMenuPermissionEnum);

    /**
     * 判断操作人是否有menuId代表的分类下所有合同的操作权限（查看、下载、导出等）
     * @param menuId
     * @param operatorOid
     * @param operatorGid
     * @param tenantOid
     * @param tenantGid
     * @param requiredFilePermissionEnum
     */
    void checkOperatorHasProcessPermission(String menuId,
                                           String operatorOid,
                                           String operatorGid,
                                           String tenantOid,
                                           String tenantGid,
                                           FilePermissionEnum requiredFilePermissionEnum
                                           );

    /**
     * 全局权限校验 - 由用户中心的权限配置决定, 根据oid匹配
     *
     * <p>适用场景：
     *
     * <p>1.企业管理员 或 法人代表
     *
     * <p>2.在用户中心配置了【已归档文件管理】权限的
     *
     * <p>3.在用户中心配置了【企业文件管理】权限的
     *
     * @param tenantId 空间Oid
     * @param operatorOid 当前操作人
     * @param resource 资源名称
     * @param permissionName 操作权限名如：DOWNLOAD
     * @return
     */
    boolean checkGlobalPermission(
            String tenantId, String operatorOid, String resource, String permissionName);


    /**
     * 判断用户拥有指定权限列表中的哪几个权限 - 由用户中心的权限配置决定, 根据oid匹配
     *
     * <p>适用场景： * *
     *
     * <p>1.企业管理员 或 法人代表 * *
     *
     * <p>2.在用户中心配置了【已归档文件管理】权限的 * *
     *
     * <p>3.在用户中心配置了【企业文件管理】权限的
     *
     * @param tenantId 空间Oid
     * @param operatorOid 当前操作人
     * @param resource 资源名称
     * @param permissionNames 操作权限名列表，如：DOWNLOAD，QUERY
     * @return
     */
    List<String> queryGlobalPermission(
            String tenantId, String operatorOid, String resource, List<String> permissionNames);

    /**
     * 全局权限校验 - 由用户中心的权限配置决定, 根据gid匹配
     *
     * <p>适用场景：
     *
     * <p>1.企业管理员 或 法人代表
     *
     * <p>2.在用户中心配置了【已归档文件管理】权限的
     *
     * <p>3.在用户中心配置了【企业文件管理】权限的
     *
     * @param tenantId 空间Oid
     * @param operatorOid 当前操作人
     * @param resource 资源名称
     * @param permissionName 操作权限名如：DOWNLOAD
     * @return
     */
    boolean checkGlobalUserPermission(
            String tenantId, String operatorOid, String resource, String permissionName);

    /**
     * 获取一个人在一个主体下菜单的所有权限
     *
     * @param operatorOid 当前操作人
     * @param tenantOid 空间Oid
     * @param menuId 菜单id
     * @return
     */
    Set<String> getMenuAllPermissions(String operatorOid, String tenantOid, String menuId, String operatorGid, String tenantGid);

    /**
     * 判断一个流程在对应菜单下是否具有相关操作权限
     *
     * @param processIdList 流程id集合
     * @param operatorAccount 当前操作人
     * @param tenantAccount 操作空间
     * @param menuId 菜单id
     * @param permission 需要校验的操作权限
     * @return key -> processId，value ->是否具备权限，true-是，false-否
     */
    Map<String, Boolean> checkProcessPermissions(
            List<String> processIdList,
            UserAccount operatorAccount,
            UserAccount tenantAccount,
            String menuId,
            String permission,
            boolean checkSecret);

    /**
     * 校验保密流程查看权限
     * @param operatorAccount
     * @param tenantAccount
     * @param allPrePermissionResultMap
     * @return
     */
    Map<String, Boolean> checkMenuSecretPermission(
            String permission,
            UserAccount operatorAccount,
            UserAccount tenantAccount,
            Map<String, Boolean> allPrePermissionResultMap);

    /**
     * 构造授权树新逻辑
     */
    BuildMenuAuthorizeTreeResultDTO buildRebirthMenuAuthorizeTree(String operatorOid, String operatorGid,
                                                                  String tenantOid, String tenantGid);


    /**
     * 构造授权树新逻辑, 缓存有值有限从缓存里获取
     */
    BuildMenuAuthorizeTreeResultDTO buildRebirthMenuAuthorizeTreeFromCache(String operatorOid, String operatorGid,
                                                                           String tenantOid, String tenantGid);

    /**
     * 删除权限树缓存数据
     */
    void clearMenuTreePermissionCache(String tenantGid, String operatorGid);

    /**
     * 判断是否菜单拥有者
     * @param tenantId
     * @param menuId
     * @return
     */
    boolean checkMenuOwner(String tenantId, String menuId);

    /**
     * 根据权限判断用户是否为企业员工
     * @param operatorOid
     * @param tenantOid
     * @return
     */
    boolean isOrgMember(String operatorOid,String tenantOid);

}

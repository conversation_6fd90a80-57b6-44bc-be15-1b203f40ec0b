package com.timevale.contractmanager.core.service.contractprocess;

import lombok.Getter;

/**
 * Created by tianlei on 2022/5/10
 */
@Getter
public class Route {

    private String topic;
    private String tag;

    public Route(String topic, String tag) {
        this.topic = topic;
        this.tag = tag;
    }
    public static Route of(String topic, String tag) {
        return new Route(topic, tag);
    }
}

package com.timevale.contractmanager.core.service.tracking.service;

import com.alibaba.fastjson.JSON;
import com.timevale.contractanalysis.facade.api.bo.FormResult;
import com.timevale.contractmanager.common.dal.bean.grouping.MenuDO;
import com.timevale.contractmanager.common.dal.dao.grouping.MenuDAO;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveOperator;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveOperatorRequest;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveUpdateOperatorRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.menu.CreateMenuRequest;
import com.timevale.contractmanager.core.service.tracking.enums.TrackingFieldEnum;
import com.timevale.contractmanager.core.service.tracking.service.base.BaseTrackingService;
import com.timevale.contractmanager.core.service.util.MenuUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.tracking.bean.TrackingCollectBean;
import com.timevale.saas.tracking.service.custom.ICustomTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.consts.TrackingKeyConstant.*;
import static com.timevale.contractmanager.core.service.tracking.consts.TrackingServiceConstant.MENU_SAVE_TRACKING;
import static com.timevale.contractmanager.core.service.tracking.enums.TrackingFieldEnum.*;

/**
 * <AUTHOR>
 * @since 2023-12-14 10:55
 */
@Slf4j
@Component(MENU_SAVE_TRACKING)
public class MenuSaveTrackingService extends BaseTrackingService implements ICustomTrackingService {

    @Autowired private MenuDAO menuDAO;
    @Autowired private ContractAnalysisClient contractAnalysisClient;
    @Autowired private SaasCommonClient saasCommonClient;

    @Override
    public String distinctId() {
        return getAuthorizedOid();
    }

    @Override
    public String trackingName() {
        return MENU_SAVE_TRACKING;
    }

    @Override
    public List<TrackingFieldEnum> trackingFields() {
        return Lists.newArrayList(SET_TYPE,FOLDER_NAME,FATHER_FOLDER_NAME,
                FOLDER_CLASS,IS_RELATED_STANDING_BOOK_RULE,STANDING_BOOK_NAME,
                IS_AUTO_CLASSIFY,CLASSIFY_CONDITION,IS_UPDATE_HISTORY,FOLDER_DEPT,
                VIP_VERSION,IS_FIRST_TIME,USER_ROLE,PLATFORM_TYPE);
    }

    @Override
    public Map<String, Object> buildTrackingFieldData(TrackingCollectBean trackingBean) {
        String trackingKey = trackingBean.getTrackingKey();
        Map<String, Object> stringObjectMap = super.trackingFieldData();
        Object trackingData = trackingBean.getTrackingData();
        // 分类名称
        String menuName = "";
        // 父分类id
        String parentMenuId = null;
        // 绑定formId
        String bindingFormId = null;
        // condition
        List<AutoArchiveOperator> conditions = null;
        if (MENU_SAVE.equals(trackingKey)) {
            CreateMenuRequest request = (CreateMenuRequest) trackingData;
            stringObjectMap.put(SET_TYPE.getKey(), "新建");
            stringObjectMap.put(IS_RELATED_STANDING_BOOK_RULE.getKey(), false);
            stringObjectMap.put(STANDING_BOOK_NAME.getKey(), "");
            stringObjectMap.put(IS_AUTO_CLASSIFY.getKey(), false);
            stringObjectMap.put(CLASSIFY_CONDITION.getKey(), "");
            stringObjectMap.put(IS_UPDATE_HISTORY.getKey(), false);
            menuName = request.getName();
            parentMenuId = request.getParentMenuId();
        }
        if (CREATE_AUTO_ARCHIVE_SAVE_MENU.equals(trackingKey)) {
            AutoArchiveOperatorRequest request = (AutoArchiveOperatorRequest) trackingData;
            stringObjectMap.put(SET_TYPE.getKey(), "新建");
            stringObjectMap.put(IS_AUTO_CLASSIFY.getKey(), true);
            stringObjectMap.put(IS_UPDATE_HISTORY.getKey(), request.getStatus() == 0);
            menuName = request.getName();
            parentMenuId = request.getParentMenuId();
            bindingFormId = request.getBindingFormId();
            conditions = request.getConditions();
        }
        if (UPDATE_RULE_SAVE_MENU.equals(trackingKey)) {
            AutoArchiveUpdateOperatorRequest request = (AutoArchiveUpdateOperatorRequest) trackingData;
            stringObjectMap.put(SET_TYPE.getKey(), "修改");
            stringObjectMap.put(IS_AUTO_CLASSIFY.getKey(), true);
            stringObjectMap.put(IS_UPDATE_HISTORY.getKey(), request.getStatus() == 0);
            menuName = request.getName();
            parentMenuId = request.getParentMenuId();
            bindingFormId = request.getBindingFormId();
            conditions = request.getConditions();
        }
        // 公共的
        stringObjectMap.put(FOLDER_NAME.getKey(), menuName);
        stringObjectMap.put(FOLDER_CLASS.getKey(), "");
        String level = StringUtils.isNotBlank(trackingBean.getTenantId()) ? saasCommonClient.queryAccountVipLevel(trackingBean.getTenantId(), "") + "" : "";
        stringObjectMap.put(VIP_VERSION.getKey(), level);
        stringObjectMap.put(IS_FIRST_TIME.getKey(), true);
        stringObjectMap.put(USER_ROLE.getKey(), "");
        stringObjectMap.put(PLATFORM_TYPE.getKey(), "标准签WEB");
        if (StringUtils.isNotBlank(parentMenuId)) {
            MenuDO parentMenu = menuDAO.getByMenuId(parentMenuId);
            if (parentMenu != null) {
                stringObjectMap.put(FATHER_FOLDER_NAME.getKey(), parentMenu.getName());
                stringObjectMap.put(FOLDER_DEPT.getKey(), MenuUtils.getMenuDept(parentMenu.getPath()) + 1);
            }
        }
        // 台账更新/插入
        if (StringUtils.isNotBlank(bindingFormId)) {
            boolean relatedStandingBookRule = false;
            String standingBookName = "";
            if (StringUtils.isNotBlank(bindingFormId)) {
                relatedStandingBookRule = true;
                FormResult formResult = contractAnalysisClient.getForm(trackingBean.getTenantId(), bindingFormId);
                standingBookName = formResult.getFormName();
            }
            stringObjectMap.put(IS_RELATED_STANDING_BOOK_RULE.getKey(), relatedStandingBookRule);
            stringObjectMap.put(STANDING_BOOK_NAME.getKey(), standingBookName);
        }
        if (CollectionUtils.isNotEmpty(conditions)) {
            stringObjectMap.put(CLASSIFY_CONDITION.getKey(), JSON.toJSONString(conditions));
        }
        return stringObjectMap;
    }


}

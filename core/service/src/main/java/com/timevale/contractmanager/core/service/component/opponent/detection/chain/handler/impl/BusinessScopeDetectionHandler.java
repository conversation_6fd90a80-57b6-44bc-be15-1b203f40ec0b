package com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionProblemEnum;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionSwitchEnum;
import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.DetectionHandler;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author:jianyang
 * @since 2021-08-19 17:41
 */
@Component
@Slf4j
public class BusinessScopeDetectionHandler implements DetectionHandler {

	@Autowired
	private SensorService sensorService;

	public static final Integer DETECTION_NO = 5;
	public static final String DESC = "经营范围没有您要求的:%s";
	@Override
	public List<OpponentDetectionChainResultBO> handler(DetectionChainBO chainBO, List<OpponentDetectionChainResultBO> chainResults) {
		log.info("开始相对方检测(经营范围检测) taskId:{} taskType:{} tenantGid:{} orgName:{}",
				chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
		if(chainBO.getDetail() == null){
			return chainResults;
		}

		if(chainBO.getBusinessScopeDetection() == OpponentDetectionSwitchEnum.CLOSE.getType()
				|| StringUtils.isBlank(chainBO.getBusinessScope())){
			log.info("相对方检测(经营范围检测)未开启经营范围检测或经营范围关键词为空 taskId:{} taskType:{} tenantGid:{} orgName:{}",
					chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
			return chainResults;
		}

		List<String> businessScopeList = JSON.parseArray(chainBO.getBusinessScope(), String.class);
		List<String> misMatching = Lists.newArrayList();
		String scope = chainBO.getDetail().getBusinessScope();
		for(String x : businessScopeList){
			if(!scope.contains(x)){
				x = String.format("%s%s%s","“",x,"”");
				misMatching.add(x);
			}
		}
		String misBusinessScope = misMatching.stream().collect(Collectors.joining("、"));
		if(CollectionUtils.isNotEmpty(misMatching)){
			OpponentDetectionChainResultBO chainResultBO = new OpponentDetectionChainResultBO();
			chainResultBO.setOrgName(chainResultBO.getOrgName());
			chainResultBO.setProblemDesc(String.format(DESC,misBusinessScope));
			chainResultBO.setRiskLevel(OpponentDetectionProblemEnum.BUSINESS_SCOPE_NOT_MATCHING.getProblemRiskLevel());
			chainResultBO.setProblemNo(OpponentDetectionProblemEnum.BUSINESS_SCOPE_NOT_MATCHING.getProblemNo());
			chainResultBO.setProblemNo(DETECTION_NO);
			chainResultBO.setSuggestDesc(OpponentDetectionProblemEnum.BUSINESS_SCOPE_NOT_MATCHING.getSuggestDesc());
			chainResults.add(chainResultBO);
			log.info("相对方检测(经营范围检测) 未查询到的关键词:{} taskId:{} taskType:{} tenantGid:{} orgName:{}",
					misBusinessScope, chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
			sensorService.opponentDetectionProblemReport(
					chainBO.getTenantGid(), chainBO.getOrgName(),
					OpponentDetectionProblemEnum.BUSINESS_SCOPE_NOT_MATCHING.getProblemNo());
		}
		return chainResults;
	}

	@Override
	public boolean filter(List<Integer> items) {
		if(items.contains(DETECTION_NO)){
			return true;
		}
		return false;
	}
}

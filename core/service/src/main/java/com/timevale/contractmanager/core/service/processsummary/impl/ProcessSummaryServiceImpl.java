package com.timevale.contractmanager.core.service.processsummary.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.clmc.facade.api.enums.ContractFileTypeEnum;
import com.timevale.clmc.facade.api.enums.ExtractFieldTypeEnum;
import com.timevale.clmc.facade.api.enums.JobPriorityEnum;
import com.timevale.clmc.facade.api.model.bean.SummaryKeyword;
import com.timevale.clmc.facade.api.model.input.RpcAsyncExtractContractV2Input;
import com.timevale.clmc.facade.api.model.input.RpcAsyncSummarizeContractV2Input;
import com.timevale.clmc.facade.api.model.output.RpcAsyncExtractContractOutput;
import com.timevale.clmc.facade.api.model.output.RpcAsyncSummarizeContractOutput;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.GetContractCategoryDetailResultDTO;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.bean.PromptKeywordDTO;
import com.timevale.contractanalysis.facade.api.request.contractcategory.bean.ExtractField;
import com.timevale.contractmanager.common.dal.bean.ProcessSummaryBaseDO;
import com.timevale.contractmanager.common.dal.bean.ProcessSummaryDO;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.dal.dao.ProcessSummaryBaseDAO;
import com.timevale.contractmanager.common.dal.dao.ProcessSummaryDAO;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.enums.ProcessFromEnum;
import com.timevale.contractmanager.common.service.enums.ProcessSummaryDataTypeEnum;
import com.timevale.contractmanager.common.service.enums.ProcessSummaryOverAllJobStatusEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.ClmCapabilityClient;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.processsummary.ProcessSummaryDetailBO;
import com.timevale.contractmanager.core.model.bo.processsummary.ProcessSummaryKeyInfoBO;
import com.timevale.contractmanager.core.model.bo.processsummary.UpdateProcessSummaryBO;
import com.timevale.contractmanager.core.service.bo.ClmCExtendInfo;
import com.timevale.contractmanager.core.service.enums.ClmcJobResultStatusEnum;
import com.timevale.contractmanager.core.service.enums.ProcessSummaryJobStatusEnum;
import com.timevale.contractmanager.core.service.enums.YesNoEnum;
import com.timevale.contractmanager.core.service.flow.FlowOperationFactory;
import com.timevale.contractmanager.core.service.flow.bean.param.QueryFlowFilesDTO;
import com.timevale.contractmanager.core.service.flow.bean.result.QueryFlowFilesResult;
import com.timevale.contractmanager.core.service.mq.consumer.clmcapability.ClmJobResultTagConstants;
import com.timevale.contractmanager.core.service.mq.model.AiReviseDataMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.AiReviseDataProducer;
import com.timevale.contractmanager.core.service.other.ClmCapabilityService;
import com.timevale.contractmanager.core.service.other.DocManagerService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.processsummary.ProcessSummaryService;
import com.timevale.docmanager.service.result.DocInfoResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.ObjectUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.model.input.AccountVipFunctionsInput;
import com.timevale.saas.common.manage.common.service.model.output.AccountVipFunctionsOutput;
import com.timevale.saas.common.manage.common.service.model.output.bean.VipFunction;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessCustomizeConfigDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessFileDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessFileInfoDTO;
import com.timevale.signflow.search.service.model.contractprocess.FileContractCategoryDTO;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合同摘要服务接口实现
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Service
@Slf4j
public class ProcessSummaryServiceImpl implements ProcessSummaryService {

    private static final String SUMMARIZE_CONTRACT_LOCK_KEY = "summarize_Contract_lock_key:%s";
    private static final String REFRESH_SUMMARIZE_CONTRACT_LOCK_KEY =
            "refresh_summarize_Contract_lock_key:%s";
    private static final String EXTEND_DATA_KEY_CATEGORY_ID = "category_id";
    private static final String EXTEND_DATA_KEY_CATEGORY_NAME = "category_name";
    private static final String EXTEND_DATA_KEY_TEMP_CATEGORY_ID = "temp_category_id";
    private static final String EXTEND_DATA_KEY_TEMP_CATEGORY_NAME = "temp_category_name";
    private static final String AI_BIZ_SOURCE = "process_summary";
    private static final String AI_BIZ_SOURCE_NAME = "合同摘要";
    private static final String FUNC_TYPE_DESC_SUMMARY = "合同概要";
    private static final String FUNC_TYPE_DESC_EXTRACT = "关键信息抽取";
    
    // 免费摘要会员功能标识，在limit里。形式为key：free value：true/false
    private static final String CONTRACT_SUMMARY_FREE_FLAG = "free";

    @Autowired private ContractProcessReadClient contractProcessReadClient;
    @Autowired private SaasCommonClient saasCommonClient;
    @Autowired private ContractAnalysisClient contractAnalysisClient;
    @Autowired private ProcessSummaryDAO processSummaryDAO;
    @Autowired private ProcessSummaryBaseDAO processSummaryBaseDAO;
    @Autowired private DocManagerService docManagerService;
    @Autowired private ClmCapabilityService clmCapabilityService;
    @Autowired private ClmCapabilityClient clmCapabilityClient;
    @Autowired private TransactionTemplate transactionTemplate;
    @Autowired private BaseProcessService baseProcessService;
    @Autowired private FlowOperationFactory flowOperationFactory;
    @Autowired private SystemConfig systemConfig;
    @Autowired private AiReviseDataProducer aiReviseDataProducer;
    @Autowired private LockFactory lockFactory;

    @Override
    public void createProcessSummary(
            String processId, String fileId, String subjectOid, String subjectGid) {
        FileTypeInfo fileTypeInfo = packageFileTypeInfo(processId, fileId, subjectOid, subjectGid);
        Boolean freeFlag = isContractSummaryFree(subjectOid);
        if (!freeFlag) {
            //摘要和抽取共两次调用大模型动作
            checkBalance(subjectGid, fileTypeInfo.getFileId(), 2);
        }
        createContractSummary(fileTypeInfo, subjectOid, freeFlag);
    }

    @Override
    public void createProcessAllFileSummary(
            String processId, String subjectOid, String subjectGid) {
        ContractProcessDTO processDTO = contractProcessReadClient.getByProcessId(processId);
        summarizeProcess(processDTO, subjectOid, subjectGid);
    }

    @Override
    public ProcessSummaryDetailBO queryProcessSummary(
            String processId, String fileId, String subjectGid) {
        // 先看自己空间下是否有数据
        List<ProcessSummaryDO> processSummaryDOS =
                processSummaryDAO.listByProcessIdFileIdAndGid(processId, fileId, subjectGid);
        Map<String, ProcessSummaryBaseDO> dataTypeMap = Maps.newHashMap();
        if (!ListUtils.isEmpty(processSummaryDOS)) {
            dataTypeMap.putAll(
                    processSummaryDOS.stream()
                            .map(this::do2BaseDO)
                            .collect(
                                    Collectors.toMap(
                                            ProcessSummaryBaseDO::getDataType,
                                            Function.identity(),
                                            (v1, v2) -> v1)));
        }
        // 有两条数据表示概要和关键信息都是自己的
        if (dataTypeMap.size() >= 2) {
            return dataTypeMap2DetailBO(dataTypeMap);
        }

        // 从基础数据补全
        List<ProcessSummaryBaseDO> processSummaryBaseDOS =
                processSummaryBaseDAO.listByProcessIdAndFileId(processId, fileId);
        Map<String, ProcessSummaryBaseDO> baseMap =
                processSummaryBaseDOS.stream()
                        .collect(
                                Collectors.toMap(
                                        ProcessSummaryBaseDO::getDataType,
                                        Function.identity(),
                                        (v1, v2) -> v1));
        baseMap.forEach((k, v) -> dataTypeMap.putIfAbsent(k, v));
        return dataTypeMap2DetailBO(dataTypeMap);
    }

    @Override
    public void refreshProcessSummary(
            String processId,
            String fileId,
            String subjectOid,
            String subjectGid,
            ProcessSummaryDataTypeEnum dataType) {
        FileTypeInfo fileTypeInfo = packageFileTypeInfo(processId, fileId, subjectOid, subjectGid);
        checkBalance(subjectGid, fileTypeInfo.getFileId(), 1);
        // 先确认基数据是否存在
        ProcessSummaryBaseDO baseDO =
                processSummaryBaseDAO.findByProcessIdAndFileIdAndDataType(
                        fileTypeInfo.getProcessId(), fileTypeInfo.getFileId(), dataType.getCode());
        if (baseDO == null || !new Integer(YesNoEnum.Y.code()).equals(baseDO.getLastJobSuccess())) {
            createJob(dataType, fileTypeInfo, subjectOid, isContractSummaryFree(subjectOid));
        } else {
            refreshJob(subjectOid, subjectGid, dataType, fileTypeInfo);
        }
    }

    @Override
    public ProcessSummaryOverAllJobStatusEnum queryProcessSummaryOverAllStatus(
            String processId, String subjectGid) {
        // 获取文件列表
        ContractProcessDTO processDTO = contractProcessReadClient.getByProcessId(processId);
        List<ContractProcessFileDTO> fileDTOList =
                Optional.ofNullable(processDTO)
                        .map(ContractProcessDTO::getFileInfo)
                        .map(ContractProcessFileInfoDTO::getContractFiles)
                        .orElse(null);
        if (ListUtils.isEmpty(fileDTOList)) {
            return ProcessSummaryOverAllJobStatusEnum.NOT_CREATED;
        }

        List<String> fileIds = getFileIds(processId);

        // 查询自己空间的数据
        List<ProcessSummaryDO> processSummaryDOS =
                processSummaryDAO.listByProcessIdAndGid(processId, subjectGid);
        List<ProcessSummaryBaseDO> selfDOs =
                processSummaryDOS.stream().map(this::do2BaseDO).collect(Collectors.toList());
        // 查询基础数据列表
        List<ProcessSummaryBaseDO> baseDOS = processSummaryBaseDAO.listByProcessId(processId);

        // 分析整体状态
        return analysisJobStatus(selfDOs, baseDOS, fileIds);
    }

    @Override
    public void updateProcessSummary(UpdateProcessSummaryBO bo) {
        // 更新摘要
        String summaryAiJobId =
                updateProcessSummaryData(
                        bo.getProcessId(),
                        bo.getFileId(),
                        bo.getSubjectId(),
                        bo.getSubjectGid(),
                        ProcessSummaryDataTypeEnum.SUMMARY,
                        bo.getSummary());
        // 更新关键信息
        String keyInfoAiJobId =
                updateProcessSummaryData(
                        bo.getProcessId(),
                        bo.getFileId(),
                        bo.getSubjectId(),
                        bo.getSubjectGid(),
                        ProcessSummaryDataTypeEnum.KEY_INFO,
                        bo.getKeyInfo());
        // 发送ai结果修正消息
        aiReviseDataProducer.sendMessage(
                new AiReviseDataMsgEntity(summaryAiJobId, bo.getSummary()));
        aiReviseDataProducer.sendMessage(
                new AiReviseDataMsgEntity(keyInfoAiJobId, bo.getKeyInfo()));
    }

    @Override
    public boolean updateProcessSummaryResultWithCreate(Long id, String jobStatus, Object result) {
        // 查询
        ProcessSummaryBaseDO summaryBaseDO = processSummaryBaseDAO.getById(id);
        if (summaryBaseDO == null) {
            return false;
        }

        // 装换数据
        String dataStr;
        Integer success;
        if (ClmcJobResultStatusEnum.ERROR.getCode().equals(jobStatus)) {
            dataStr = "";
            success = YesNoEnum.N.code();
        } else {
            dataStr = parseResult(result, summaryBaseDO);
            success = YesNoEnum.Y.code();
        }

        // 入库
        processSummaryBaseDAO.updateDataAndAiJobStatusById(
                summaryBaseDO.getId(),
                dataStr,
                ProcessSummaryJobStatusEnum.DONE.getCode(),
                success);
        return true;
    }

    private String parseResult(Object result, ProcessSummaryBaseDO summaryBaseDO) {
        if (ProcessSummaryDataTypeEnum.SUMMARY.getCode().equals(summaryBaseDO.getDataType())) {
            return result == null ? "" : result.toString();
        }
        if (result == null) {
            return "[]";
        } else {
            JSONObject jsonObject;
            if (result instanceof JSONObject) {
                jsonObject = (JSONObject) result;
            } else {
                jsonObject = JSON.parseObject(JSON.toJSONString(result), Feature.OrderedField);
            }

            List<ProcessSummaryKeyInfoBO> keyInfoBOS = aiResultMap2KeyInfoBO(jsonObject);
            return JSON.toJSONString(keyInfoBOS);
        }
    }

    @Override
    public boolean updateProcessSummaryResultWithRefresh(Long id, String jobStatus, Object result) {
        // 查询
        ProcessSummaryDO summaryDO = processSummaryDAO.getById(id);
        if (summaryDO == null) {
            return false;
        }
        ProcessSummaryBaseDO summaryBaseDO = do2BaseDO(summaryDO);

        // 转换数据
        String dataStr;
        Integer success;
        if (ClmcJobResultStatusEnum.ERROR.getCode().equals(jobStatus)) {
            dataStr = "";
            success = YesNoEnum.N.code();
        } else {
            dataStr = parseResult(result, summaryBaseDO);
            success = YesNoEnum.Y.code();
        }

        // 入库
        if (YesNoEnum.Y.code() == success) {
            processSummaryDAO.updateDataAndAiJobStatusById(
                    summaryBaseDO.getId(),
                    dataStr,
                    ProcessSummaryJobStatusEnum.DONE.getCode(),
                    success,
                    updateRefresExtendData(summaryBaseDO.getExtendData()));
        } else {
            processSummaryDAO.updateLastJobSuccessById(id, success, ProcessSummaryJobStatusEnum.DONE.getCode());
        }
        return true;
    }

    private void checkBalance(String tenantGid, String fileId, int aiActionNumber) {
        DocInfoResult docInfoResult = docManagerService.getDocByFileId(fileId);
        int pageSize = docInfoResult.getTotalPageSize() == null ? 1: docInfoResult.getTotalPageSize();
        //摘要和关键信息需要调用两次大模型
        boolean enough = clmCapabilityClient.queryAIBalanceEnough(tenantGid, pageSize * aiActionNumber);
        if (!enough) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_SUMMARY_BALANCE);
        }
    }

    private List<ProcessSummaryKeyInfoBO> aiResultMap2KeyInfoBO(Map<String, Object> aiResultMap) {
        List<ProcessSummaryKeyInfoBO> keyInfoBOS = Lists.newArrayList();
        Set<String> keys = aiResultMap.keySet();
        int seq = 1;
        for (String key : keys) {
            Object value = aiResultMap.get(key);
            ProcessSummaryKeyInfoBO keyInfoBO = new ProcessSummaryKeyInfoBO();
            keyInfoBO.setSeq(seq++);
            keyInfoBO.setName(key);
            keyInfoBO.setValue(value);
            keyInfoBO.setAiValue(value);
            keyInfoBOS.add(keyInfoBO);
        }
        return keyInfoBOS;
    }

    private String updateProcessSummaryData(
            String processId,
            String fileId,
            String subjectOid,
            String subjectGid,
            ProcessSummaryDataTypeEnum dataType,
            Object data) {
        // 先查一下基数据
        ProcessSummaryBaseDO baseDO =
                processSummaryBaseDAO.findByProcessIdAndFileIdAndDataType(
                        processId, fileId, dataType.getCode());
        if (baseDO == null || !new Integer(YesNoEnum.Y.code()).equals(baseDO.getLastJobSuccess())) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_SUMMARY_NOT_EXIST);
        }

        // 更新自己空间的数据
        Elock lock =
                lockFactory.getLock(
                        getRefreshSummarizeContractLockKey(
                                subjectGid, processId, fileId, dataType.getCode()));
        if (!lock.tryLock(1, TimeUnit.SECONDS)) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.SERVICE_BUSY);
        }
        try {
            ProcessSummaryDO summaryDO =
                    processSummaryDAO.getByProcessIdFileIdSubjectGIdDataType(
                            processId, fileId, subjectGid, dataType.getCode());
            if (summaryDO == null) {
                // 不存在创建
                summaryDO = new ProcessSummaryDO();
                summaryDO.setProcessId(processId);
                summaryDO.setFileId(fileId);
                summaryDO.setSubjectOid(subjectOid);
                summaryDO.setSubjectGid(subjectGid);
                summaryDO.setAiJobId(baseDO.getAiJobId());
                summaryDO.setAiJobStatus(ProcessSummaryJobStatusEnum.DONE.getCode());
                summaryDO.setLastJobSuccess(YesNoEnum.Y.code());
                summaryDO.setDataType(dataType.getCode());
                summaryDO.setExtendData(baseDO.getExtendData());
                String dataStr = convertData(data, dataType, baseDO.getData());
                summaryDO.setData(dataStr);
                processSummaryDAO.insert(summaryDO);
                return summaryDO.getAiJobId();
            }
            // 如果在运行中，直接报错
            if (ProcessSummaryJobStatusEnum.RUNNING.getCode().equals(summaryDO.getAiJobStatus())) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SUMMARY_RUNNING);
            }

            String dataStr = convertData(data, dataType, summaryDO.getData());
            processSummaryDAO.updateJobDataById(summaryDO.getId(), dataStr, YesNoEnum.Y.code());
            return summaryDO.getAiJobId();
        } finally {
            lock.unlock();
        }
    }

    private String convertData(
            Object data, ProcessSummaryDataTypeEnum dataType, String originDataStr) {
        // 概要
        if (dataType == ProcessSummaryDataTypeEnum.SUMMARY) {
            return data.toString();
        }

        // 关键信息
        List<ProcessSummaryKeyInfoBO> originkeyInfoBOS = convertKeyInfo(originDataStr);
        List<ProcessSummaryKeyInfoBO> newKeyInfoBOs = (List<ProcessSummaryKeyInfoBO>) data;
        Map<Integer, Object> originMap =
                originkeyInfoBOS.stream()
                        .collect(
                                Collectors.toMap(
                                        ProcessSummaryKeyInfoBO::getSeq,
                                        ProcessSummaryKeyInfoBO::getAiValue,
                                        (v1, v2) -> v1));
        for (int i = newKeyInfoBOs.size() - 1; i >= 0; i--) {
            ProcessSummaryKeyInfoBO keyInfoBO = newKeyInfoBOs.get(i);
            if (originMap.containsKey(keyInfoBO.getSeq())) {
                keyInfoBO.setAiValue(originMap.get(keyInfoBO.getSeq()));
            } else {
                newKeyInfoBOs.remove(i);
            }
        }

        return JSON.toJSONString(newKeyInfoBOs);
    }

    private ProcessSummaryDetailBO dataTypeMap2DetailBO(
            Map<String, ProcessSummaryBaseDO> dataTypeMap) {
        ProcessSummaryDetailBO detailBO = new ProcessSummaryDetailBO();
        if (MapUtils.isEmpty(dataTypeMap)) {
            detailBO.setStatus(ProcessSummaryJobStatusEnum.NOT_CREATED.getCode());
            return detailBO;
        }

        String status = ProcessSummaryJobStatusEnum.DONE.getCode();
        ProcessSummaryBaseDO summaryBaseDO =
                dataTypeMap.get(ProcessSummaryDataTypeEnum.SUMMARY.getCode());
        detailBO.setSummaryRefreshCount(getRefreshCount(summaryBaseDO));
        if (summaryBaseDO == null
                || ProcessSummaryJobStatusEnum.RUNNING
                        .getCode()
                        .equals(summaryBaseDO.getAiJobStatus())) {
            status = ProcessSummaryJobStatusEnum.RUNNING.getCode();
        } else {
            detailBO.setSummary(summaryBaseDO.getData());
            detailBO.setSummaryLastJobSuccess(
                    YesNoEnum.from(summaryBaseDO.getLastJobSuccess()).bool());
        }
        ProcessSummaryBaseDO keyInfoBaseDO =
                dataTypeMap.get(ProcessSummaryDataTypeEnum.KEY_INFO.getCode());
        detailBO.setKeyInfoRefreshCount(getRefreshCount(keyInfoBaseDO));
        if (keyInfoBaseDO == null
                || ProcessSummaryJobStatusEnum.RUNNING
                        .getCode()
                        .equals(keyInfoBaseDO.getAiJobStatus())) {
            status = ProcessSummaryJobStatusEnum.RUNNING.getCode();
        } else {
            detailBO.setKeyInfo(convertKeyInfo(keyInfoBaseDO.getData()));
            detailBO.setKeyInfoLastJobSuccess(
                    YesNoEnum.from(keyInfoBaseDO.getLastJobSuccess()).bool());
        }
        detailBO.setStatus(status);
        dataInputCategoryInfo(summaryBaseDO, keyInfoBaseDO, detailBO);
        return detailBO;
    }

    private int getRefreshCount(ProcessSummaryBaseDO summaryBaseDO) {
        if (summaryBaseDO == null) {
            return systemConfig.getProcessSummaryMaxRetry();
        }

        return Math.max(
                systemConfig.getProcessSummaryMaxRetry() - summaryBaseDO.getRetryTimes(), 0);
    }

    private void dataInputCategoryInfo(
            ProcessSummaryBaseDO summaryDO,
            ProcessSummaryBaseDO keyInfoDO,
            ProcessSummaryDetailBO detailBO) {
        if (StringUtils.isBlank(summaryDO.getExtendData())
                && StringUtils.isBlank(keyInfoDO.getExtendData())) {
            return;
        }
        String extendDataStr;
        ;
        if (StringUtils.isNotBlank(summaryDO.getExtendData())
                && StringUtils.isNotBlank(keyInfoDO.getExtendData())) {
            extendDataStr =
                    summaryDO.getModifyTime().after(keyInfoDO.getModifyTime())
                            ? summaryDO.getExtendData()
                            : keyInfoDO.getExtendData();
        } else {
            extendDataStr =
                    StringUtils.isNotBlank(summaryDO.getExtendData())
                            ? summaryDO.getExtendData()
                            : keyInfoDO.getExtendData();
        }
        JSONObject extendData = JSON.parseObject(extendDataStr);
        detailBO.setCategoryId(extendData.getString(EXTEND_DATA_KEY_CATEGORY_ID));
        detailBO.setCategoryName(extendData.getString(EXTEND_DATA_KEY_CATEGORY_NAME));
    }

    private List<ProcessSummaryKeyInfoBO> convertKeyInfo(String keyInfo) {
        return StringUtils.isNotBlank(keyInfo)
                ? JSON.parseArray(keyInfo, ProcessSummaryKeyInfoBO.class)
                : Lists.newArrayList();
    }

    private ProcessSummaryBaseDO do2BaseDO(ProcessSummaryDO processSummaryDO) {
        ProcessSummaryBaseDO processSummaryBaseDO = new ProcessSummaryBaseDO();
        processSummaryBaseDO.setId(processSummaryDO.getId());
        processSummaryBaseDO.setProcessId(processSummaryDO.getProcessId());
        processSummaryBaseDO.setFileId(processSummaryDO.getFileId());
        processSummaryBaseDO.setAiJobId(processSummaryDO.getAiJobId());
        processSummaryBaseDO.setAiJobStatus(processSummaryDO.getAiJobStatus());
        processSummaryBaseDO.setDataType(processSummaryDO.getDataType());
        processSummaryBaseDO.setData(processSummaryDO.getData());
        processSummaryBaseDO.setRetryTimes(processSummaryDO.getRetryTimes());
        processSummaryBaseDO.setCreateTime(processSummaryDO.getCreateTime());
        processSummaryBaseDO.setModifyTime(processSummaryDO.getModifyTime());
        processSummaryBaseDO.setLastJobSuccess(processSummaryDO.getLastJobSuccess());
        processSummaryBaseDO.setExtendData(processSummaryDO.getExtendData());
        return processSummaryBaseDO;
    }

    private ProcessSummaryOverAllJobStatusEnum analysisJobStatus(
            List<ProcessSummaryBaseDO> selfDOs,
            List<ProcessSummaryBaseDO> baseDOS,
            List<String> fileIds) {
        if (ListUtils.isEmpty(selfDOs) && ListUtils.isEmpty(baseDOS)) {
            return ProcessSummaryOverAllJobStatusEnum.NOT_CREATED;
        }
        // 按fileId:dataType装换为map
        Map<String, Map<String, ProcessSummaryBaseDO>> selfGroupMap = groupBaseDO(selfDOs);
        Map<String, Map<String, ProcessSummaryBaseDO>> baseGroupMap = groupBaseDO(baseDOS);
        AtomicInteger runningCount = new AtomicInteger(0);
        AtomicInteger doneCount = new AtomicInteger(0);
        AtomicInteger notCreatedCount = new AtomicInteger(0);
        for (String fileId : fileIds) {
            Map<String, ProcessSummaryBaseDO> selfDataTypeMap = selfGroupMap.get(fileId);
            Map<String, ProcessSummaryBaseDO> baseDataTypeMap = baseGroupMap.get(fileId);
            if (selfDataTypeMap == null && baseDataTypeMap == null) {
                continue;
            }

            Map<String, ProcessSummaryBaseDO> dataTypeMap =
                    Optional.ofNullable(selfDataTypeMap).orElse(baseDataTypeMap);
            ProcessSummaryBaseDO summaryDO =
                    dataTypeMap.getOrDefault(
                            ProcessSummaryDataTypeEnum.SUMMARY.getCode(),
                            baseDataTypeMap.get(ProcessSummaryDataTypeEnum.SUMMARY.getCode()));
            countBaseDO(runningCount, doneCount, notCreatedCount, summaryDO);
            ProcessSummaryBaseDO keyInfoDO =
                    dataTypeMap.getOrDefault(
                            ProcessSummaryDataTypeEnum.KEY_INFO.getCode(),
                            baseDataTypeMap.get(ProcessSummaryDataTypeEnum.KEY_INFO.getCode()));
            countBaseDO(runningCount, doneCount, notCreatedCount, keyInfoDO);
        }
        if ((notCreatedCount.get() > 0 || runningCount.get() > 0) && doneCount.get() > 0) {
            return ProcessSummaryOverAllJobStatusEnum.PARTLY_DONE;
        }
        if ((notCreatedCount.get() == 0 && runningCount.get() == 0) && doneCount.get() > 0) {
            return ProcessSummaryOverAllJobStatusEnum.ALL_DONE;
        }
        if (doneCount.get() == 0) {
            return ProcessSummaryOverAllJobStatusEnum.RUNNING;
        }
        return ProcessSummaryOverAllJobStatusEnum.NOT_CREATED;
    }

    private void countBaseDO(
            AtomicInteger runningCount,
            AtomicInteger doneCount,
            AtomicInteger notCreatedCount,
            ProcessSummaryBaseDO summaryBaseDO) {
        if (summaryBaseDO == null) {
            notCreatedCount.incrementAndGet();
            return;
        }
        if (ProcessSummaryJobStatusEnum.RUNNING.getCode().equals(summaryBaseDO.getAiJobStatus())) {
            runningCount.incrementAndGet();
        } else {
            doneCount.incrementAndGet();
        }
    }

    private Map<String, Map<String, ProcessSummaryBaseDO>> groupBaseDO(
            List<ProcessSummaryBaseDO> baseDOS) {
        Map<String, Map<String, ProcessSummaryBaseDO>> groupMap = Maps.newHashMap();
        for (ProcessSummaryBaseDO baseDO : baseDOS) {
            Map<String, ProcessSummaryBaseDO> dataTypeMap = groupMap.get(baseDO.getFileId());
            if (dataTypeMap == null) {
                dataTypeMap = Maps.newHashMap();
                groupMap.put(baseDO.getFileId(), dataTypeMap);
            }
            dataTypeMap.put(baseDO.getDataType(), baseDO);
        }
        return groupMap;
    }

    private FileTypeInfo packageFileTypeInfo(
            String processId, String fileId, String subjectOid, String subjectGid) {
        // 查询文件信息
        FileTypeInfo fileTypeInfo = new FileTypeInfo(processId, fileId);
        DocInfoResult docInfoResult = docManagerService.getDocByFileId(fileId);
        if (docInfoResult == null || StringUtils.isBlank(docInfoResult.getFileKey())) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_FILE_NOT_EXIST);
        }
        // 文件是否在合同下
        checkProcessHasFileId(processId, fileId);
        // 获取合同信息
        ContractProcessDTO processDTO = contractProcessReadClient.getByProcessId(processId);
        // 是否为线下文件
        if (ProcessFromEnum.OFFLINE.getType().equals(processDTO.getProcessFrom())) {
            fileTypeInfo.setOnlineFile(false);
        }
        // 获取合同分类id
        String categoryId = getCategoryId(processDTO, fileId, subjectOid, subjectGid);
        fileTypeInfo.setCategoryId(categoryId);
        // 查询分类信息
        if (StringUtils.isNotBlank(fileTypeInfo.getCategoryId())) {
            dataInputCategoryInfo(fileTypeInfo, fileTypeInfo.getCategoryId());
        }
        return fileTypeInfo;
    }

    private void summarizeProcess(
            ContractProcessDTO processDTO, String subjectOid, String subjectGid) {

        // 读取文件列表和文件类型id
        List<FileTypeInfo> fileTypeInfos = getFileTypeInfos(processDTO, subjectOid, subjectGid);

        // 读取提取字段和关键信息
        buildFieldInfo(fileTypeInfos);

        // 根据文件列表生成任务
        fileTypeInfos.forEach(fileTypeInfo -> createContractSummary(fileTypeInfo, subjectOid,isContractSummaryFree(subjectOid)));
    }

    private void dataInputCategoryInfo(FileTypeInfo fileTypeInfo, String categoryId) {
        GetContractCategoryDetailResultDTO resultDTO =
                contractAnalysisClient.getContractCategoryDetailWithFields(categoryId);
        if (resultDTO == null) {
            return;
        }
        fileTypeInfo.setPromptKeyword(resultDTO.getPromptKeywords());
        fileTypeInfo.setExtractFields(resultDTO.getExtractFields());
        fileTypeInfo.setCategoryName(resultDTO.getCategoryName());
    }

    private String getCategoryId(
            ContractProcessDTO processDTO, String fileId, String subjectOid, String subjectGid) {
        if (ListUtils.isEmpty(processDTO.getCustomizeConfig())) {
            return null;
        }
        return processDTO.getCustomizeConfig().stream()
                .filter(
                        configDTO ->
                                ObjectUtils.equals(subjectGid, configDTO.getTenantGid())
                                        || ObjectUtils.equals(subjectOid, configDTO.getTenantOid()))
                .findFirst()
                .map(ContractProcessCustomizeConfigDTO::getFileContractCategories)
                .flatMap(
                        categories ->
                                categories.stream()
                                        .filter(
                                                categoryDTO ->
                                                        ObjectUtils.equals(
                                                                fileId, categoryDTO.getFileId()))
                                        .findFirst())
                .map(FileContractCategoryDTO::getCategoryId)
                .orElse(null);
    }

    private void checkProcessHasFileId(String processId, String fileId) {
        List<String> fileIds = getFileIds(processId);
        if (ListUtils.isEmpty(fileIds) || !fileIds.contains(fileId)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_FILE_NOT_EXIST);
        }
    }

    private void createContractSummary(FileTypeInfo fileTypeInfo, String subjectId,Boolean freeFlag) {
        // 调用摘要生成接口并记录任务信息
        createJob(ProcessSummaryDataTypeEnum.SUMMARY, fileTypeInfo, subjectId,freeFlag);
        // 调用抽取接口并记录任务信息
        createJob(ProcessSummaryDataTypeEnum.KEY_INFO, fileTypeInfo, subjectId,freeFlag);
    }

    private void refreshJob(
            String oid,
            String gid,
            ProcessSummaryDataTypeEnum dataType,
            FileTypeInfo fileTypeInfo) {
        Elock elock =
                lockFactory.getLock(
                        getRefreshSummarizeContractLockKey(
                                gid,
                                fileTypeInfo.getProcessId(),
                                fileTypeInfo.getFileId(),
                                dataType.getCode()));
        if (!elock.tryLock(1, TimeUnit.SECONDS)) {
            return;
        }
        try {
            ProcessSummaryDO summaryDO =
                    processSummaryDAO.getByProcessIdFileIdSubjectGIdDataType(
                            fileTypeInfo.getProcessId(),
                            fileTypeInfo.getFileId(),
                            gid,
                            dataType.getCode());
            // 运行中不再调用
            if (summaryDO != null
                    && ProcessSummaryJobStatusEnum.RUNNING
                            .getCode()
                            .equals(summaryDO.getAiJobStatus())) {
                log.info(
                        "process summary is running, skip refresh, processId:{}, fileId:{}, gid:{},"
                                + " dataType:{}",
                        fileTypeInfo.getProcessId(),
                        fileTypeInfo.getFileId(),
                        gid,
                        dataType.getCode());
                return;
            }

            String jId =
                    transactionTemplate.execute(
                            action ->
                                    callRefreshJobAndSaveDB(
                                            oid, gid, dataType, fileTypeInfo, summaryDO));

            log.info(
                    "refresh process summary, processId:{}, fileId:{}, gid:{}, dataType:{},"
                            + " jobId:{}",
                    fileTypeInfo.getProcessId(),
                    fileTypeInfo.getFileId(),
                    gid,
                    dataType.getCode(),
                    jId);
        } finally {
            elock.unlock();
        }
    }

    private String callRefreshJobAndSaveDB(
            String oid,
            String gid,
            ProcessSummaryDataTypeEnum dataType,
            FileTypeInfo fileTypeInfo,
            ProcessSummaryDO summaryDO) {
        ProcessSummaryDO newDO = summaryDO;
        boolean first = false;
        if (newDO == null) {
            first = true;
            newDO = new ProcessSummaryDO();
            newDO.setProcessId(fileTypeInfo.getProcessId());
            newDO.setFileId(fileTypeInfo.getFileId());
            newDO.setSubjectGid(gid);
            newDO.setSubjectOid(oid);
            newDO.setLastJobSuccess(YesNoEnum.Y.code());
            newDO.setDataType(dataType.getCode());
            newDO.setExtendData(genExtendData(fileTypeInfo));
            processSummaryDAO.insert(newDO);
        }

        String functionTypeDesc = ProcessSummaryDataTypeEnum.SUMMARY == dataType ? FUNC_TYPE_DESC_SUMMARY : FUNC_TYPE_DESC_EXTRACT;
        ClmCExtendInfo sensorData = new ClmCExtendInfo(fileTypeInfo.getProcessId(), oid, AI_BIZ_SOURCE, AI_BIZ_SOURCE_NAME, functionTypeDesc, isContractSummaryFree(oid));
        // 调用cc创建任务接口
        String jobId =
                callJob(
                        dataType,
                        fileTypeInfo,
                        newDO.getId().toString(),
                        ClmJobResultTagConstants.PROCESS_SUMMARY_NOTIFY_REFRESH_TAG,
                        true,
                        sensorData);

        // 更新任务信息
        newDO.setAiJobStatus(ProcessSummaryJobStatusEnum.RUNNING.getCode());
        if (first) {
            newDO.setAiJobId(jobId);
        } else {
            newDO.setExtendData(updateExtendData(fileTypeInfo, newDO.getExtendData(), true));
        }
        processSummaryDAO.updateAiJobInfoById(newDO);
        return jobId;
    }

    private String callJob(
            ProcessSummaryDataTypeEnum dataType,
            FileTypeInfo fileTypeInfo,
            String id,
            String notifyTag,
            boolean refresh,
            ClmCExtendInfo sensorData) {
        String jobId;
        if (dataType == ProcessSummaryDataTypeEnum.SUMMARY) {
            jobId = callSummary(fileTypeInfo, refresh, notifyTag, id, sensorData);
        } else {
            jobId = callExtract(fileTypeInfo, refresh, notifyTag, id, sensorData);
        }
        return jobId;
    }

    private String callSummary(
            FileTypeInfo fileTypeInfo, boolean refresh, String notifyTag, String bizId, ClmCExtendInfo sensorData) {
        RpcAsyncSummarizeContractV2Input input = new RpcAsyncSummarizeContractV2Input();
        input.setRefresh(refresh);
        List<SummaryKeyword> keywords = Lists.newArrayList();
        if (!ListUtils.isEmpty(fileTypeInfo.getPromptKeyword())) {
            List<SummaryKeyword> words =
                    fileTypeInfo.getPromptKeyword().stream()
                            .map(
                                    promptKeywordDTO -> {
                                        SummaryKeyword summaryKeyword = new SummaryKeyword();
                                        summaryKeyword.setWord(promptKeywordDTO.getKeyword());
                                        return summaryKeyword;
                                    })
                            .collect(Collectors.toList());
            keywords.addAll(words);
        }
        input.setKeywords(keywords);
        input.setFileType(
                fileTypeInfo.isOnlineFile()
                        ? ContractFileTypeEnum.ONLINE_PDF.getType()
                        : ContractFileTypeEnum.OFFLINE_PDF.getType());
        input.setNotifyTag(notifyTag);
        input.setBizId(bizId);
        input.setFileId(fileTypeInfo.getFileId());
        RpcAsyncSummarizeContractOutput output = clmCapabilityService.asyncSummarizeContract(input, sensorData);
        return output.getJobId();
    }

    private String callExtract(
            FileTypeInfo fileTypeInfo, boolean refresh, String notifyTag, String bizId, ClmCExtendInfo clmCExtendInfo) {
        RpcAsyncExtractContractV2Input input = new RpcAsyncExtractContractV2Input();
        input.setFields(convertExtractFields(fileTypeInfo.extractFields));
        input.setFileIds(Lists.newArrayList(fileTypeInfo.getFileId()));
        input.setRefresh(refresh);
        input.setFileType(
                fileTypeInfo.isOnlineFile()
                        ? ContractFileTypeEnum.ONLINE_PDF.getType()
                        : ContractFileTypeEnum.OFFLINE_PDF.getType());
        input.setPriority(JobPriorityEnum.HIGH.getCode());
        input.setNotifyTag(notifyTag);
        input.setBizId(bizId);
        RpcAsyncExtractContractOutput output = clmCapabilityService.asyncExtractContract(input, clmCExtendInfo);
        return output.getJobId();
    }

    private List<com.timevale.clmc.facade.api.model.bean.ExtractField> convertExtractFields(
            List<ExtractField> extractFields) {
        if (ListUtils.isEmpty(extractFields)) {
            return Lists.newArrayList();
        }
        List<com.timevale.clmc.facade.api.model.bean.ExtractField> result = new ArrayList<>();
        for (ExtractField extractField : extractFields) {
            com.timevale.clmc.facade.api.model.bean.ExtractField field =
                    new com.timevale.clmc.facade.api.model.bean.ExtractField();
            field.setFieldKey(extractField.getFieldName());
            // 摘要不严格控制值类型，全部使用字符串
            field.setFieldType(ExtractFieldTypeEnum.STRING.getType());
            result.add(field);
        }
        return result;
    }

    private void createJob(ProcessSummaryDataTypeEnum dataType, FileTypeInfo fileTypeInfo, String subjectId,Boolean freeFlag) {
        Elock elock =
                lockFactory.getLock(
                        getSummarizeContractLockKey(
                                fileTypeInfo.getProcessId(),
                                fileTypeInfo.getFileId(),
                                dataType.getCode()));
        if (!elock.tryLock(1, TimeUnit.SECONDS)) {
            return;
        }
        try {
            // 先看任务是否存在
            ProcessSummaryBaseDO summaryDO =
                    processSummaryBaseDAO.findByProcessIdAndFileIdAndDataType(
                            fileTypeInfo.processId, fileTypeInfo.getFileId(), dataType.getCode());
            if (summaryDO != null
                    && new Integer(YesNoEnum.Y.code()).equals(summaryDO.getLastJobSuccess())) {
                log.info(
                        "任务已存在, processId:{}, fileId:{}, dataType:{}, jobId:{}",
                        fileTypeInfo.getProcessId(),
                        fileTypeInfo.getFileId(),
                        dataType.getCode(),
                        summaryDO.getAiJobId());
                return;
            }

            String functionTypeDesc = ProcessSummaryDataTypeEnum.SUMMARY == dataType ? FUNC_TYPE_DESC_SUMMARY : FUNC_TYPE_DESC_EXTRACT;
            ClmCExtendInfo sensorData = new ClmCExtendInfo(fileTypeInfo.getProcessId(), subjectId, AI_BIZ_SOURCE, AI_BIZ_SOURCE_NAME, functionTypeDesc,freeFlag);
            ProcessSummaryBaseDO summaryBaseDO =
                    transactionTemplate.execute(
                            action -> callCreateJobAndSaveDB(dataType, fileTypeInfo, summaryDO, sensorData));

            log.info(
                    "创建任务, processId:{}, fileId:{}, dataType:{}, jobId:{}",
                    fileTypeInfo.getProcessId(),
                    fileTypeInfo.getFileId(),
                    dataType.getCode(),
                    summaryBaseDO.getAiJobId());
        } finally {
            elock.unlock();
        }
    }

    private ProcessSummaryBaseDO callCreateJobAndSaveDB(
            ProcessSummaryDataTypeEnum dataType,
            FileTypeInfo fileTypeInfo,
            ProcessSummaryBaseDO summaryDO,
            ClmCExtendInfo sensorData) {
        ProcessSummaryBaseDO baseDO = summaryDO;
        if (baseDO == null) {
            // 创建任务记录
            baseDO = new ProcessSummaryBaseDO();
            baseDO.setProcessId(fileTypeInfo.getProcessId());
            baseDO.setFileId(fileTypeInfo.getFileId());
            baseDO.setDataType(dataType.getCode());
            baseDO.setLastJobSuccess(YesNoEnum.Y.code());
            baseDO.setExtendData(genExtendData(fileTypeInfo));
            processSummaryBaseDAO.insert(baseDO);
        }

        // 调用cc创建任务接口
        String jobId =
                callJob(
                        dataType,
                        fileTypeInfo,
                        baseDO.getId().toString(),
                        ClmJobResultTagConstants.PROCESS_SUMMARY_NOTIFY_TAG,
                        false,
                        sensorData);
        baseDO.setAiJobId(jobId);
        baseDO.setAiJobStatus(ProcessSummaryJobStatusEnum.RUNNING.getCode());

        // 更新任务信息
        processSummaryBaseDAO.updateJobInfoById(
                baseDO.getId(),
                baseDO.getAiJobId(),
                baseDO.getAiJobStatus(),
                updateExtendData(fileTypeInfo, baseDO.getExtendData(), false));
        return baseDO;
    }

    private String genExtendData(FileTypeInfo fileTypeInfo) {
        Map<String, Object> extendData = Maps.newHashMap();
        extendData.put(EXTEND_DATA_KEY_CATEGORY_ID, fileTypeInfo.getCategoryId());
        extendData.put(EXTEND_DATA_KEY_CATEGORY_ID, fileTypeInfo.getCategoryName());
        return JSON.toJSONString(extendData);
    }

    private String updateExtendData(FileTypeInfo fileTypeInfo, String extendDataStr, boolean temp) {
        Map<String, Object> extendDataMap;
        if (StringUtils.isBlank(extendDataStr)) {
            extendDataMap = Maps.newHashMap();
        } else {
            extendDataMap =
                    JSON.parseObject(extendDataStr, new TypeReference<Map<String, Object>>() {});
        }
        if (temp) {
            extendDataMap.put(EXTEND_DATA_KEY_TEMP_CATEGORY_ID, fileTypeInfo.getCategoryId());
            extendDataMap.put(EXTEND_DATA_KEY_TEMP_CATEGORY_NAME, fileTypeInfo.getCategoryName());
        } else {
            extendDataMap.put(EXTEND_DATA_KEY_CATEGORY_ID, fileTypeInfo.getCategoryId());
            extendDataMap.put(EXTEND_DATA_KEY_CATEGORY_NAME, fileTypeInfo.getCategoryName());
        }
        return JSON.toJSONString(extendDataMap);
    }

    private String updateRefresExtendData(String extendDataStr) {
        if (StringUtils.isBlank(extendDataStr)) {
            return "";
        }
        Map<String, Object> extendDataMap =
                JSON.parseObject(extendDataStr, new TypeReference<Map<String, Object>>() {});
        String tempCategoryId =
                (String) extendDataMap.getOrDefault(EXTEND_DATA_KEY_TEMP_CATEGORY_ID, "");
        String tempCategoryName =
                (String) extendDataMap.getOrDefault(EXTEND_DATA_KEY_TEMP_CATEGORY_NAME, "");
        extendDataMap.put(EXTEND_DATA_KEY_CATEGORY_ID, tempCategoryId);
        extendDataMap.put(EXTEND_DATA_KEY_CATEGORY_NAME, tempCategoryName);
        return JSON.toJSONString(extendDataMap);
    }

    private String getSummarizeContractLockKey(String processId, String fileId, String dataType) {
        return String.format(
                SUMMARIZE_CONTRACT_LOCK_KEY, processId + "_" + fileId + "_" + dataType);
    }

    private String getRefreshSummarizeContractLockKey(
            String gid, String processId, String fileId, String dataType) {
        return String.format(
                REFRESH_SUMMARIZE_CONTRACT_LOCK_KEY,
                gid + "_" + processId + "_" + fileId + "_" + dataType);
    }

    private void buildFieldInfo(List<FileTypeInfo> fileTypeInfos) {
        List<FileTypeInfo> categoryFiles =
                fileTypeInfos.stream()
                        .filter(
                                fileTypeInfo ->
                                        StringUtils.isNotBlank(fileTypeInfo.getCategoryId()))
                        .collect(Collectors.toList());
        if (ListUtils.isEmpty(categoryFiles)) {
            return;
        }
        List<String> categoryIds =
                categoryFiles.stream()
                        .map(FileTypeInfo::getCategoryId)
                        .distinct()
                        .collect(Collectors.toList());
        List<GetContractCategoryDetailResultDTO> categoryDetails =
                contractAnalysisClient.batchQueryContractCategoriesWithFields(categoryIds);
        if (ListUtils.isEmpty(categoryDetails)) {
            return;
        }
        // 根据categoryId转换为map, 并写入字段信息
        Map<String, GetContractCategoryDetailResultDTO> categoryDetailMap =
                categoryDetails.stream()
                        .collect(
                                Collectors.toMap(
                                        GetContractCategoryDetailResultDTO::getCategoryId,
                                        Function.identity(),
                                        (v1, v2) -> v1));
        categoryFiles.forEach(
                fileTypeInfo -> {
                    String categoryId = fileTypeInfo.getCategoryId();
                    GetContractCategoryDetailResultDTO categoryDetail =
                            categoryDetailMap.get(categoryId);
                    if (categoryDetail != null) {
                        fileTypeInfo.setExtractFields(categoryDetail.getExtractFields());
                        fileTypeInfo.setPromptKeyword(categoryDetail.getPromptKeywords());
                        fileTypeInfo.setCategoryName(categoryDetail.getCategoryName());
                    }
                });
    }

    private List<FileTypeInfo> getFileTypeInfos(
            ContractProcessDTO processDTO, String oid, String gid) {
        List<String> fileIds = getFileIds(processDTO.getProcessId());
        List<FileTypeInfo> fileTypeInfos =
                fileIds.stream()
                        .map(fileId -> new FileTypeInfo(processDTO.getProcessId(), fileId))
                        .collect(Collectors.toList());

        // 获取分类id
        List<FileContractCategoryDTO> categoryDTOS =
                processDTO.getCustomizeConfig().stream()
                        .filter(
                                config ->
                                        oidOrGidEq(
                                                oid,
                                                gid,
                                                config.getTenantOid(),
                                                config.getTenantGid()))
                        .findFirst()
                        .map(ContractProcessCustomizeConfigDTO::getFileContractCategories)
                        .orElse(null);
        if (ListUtils.isEmpty(categoryDTOS)) {
            return fileTypeInfos;
        }
        // 根据fileId装换为map
        Map<String, FileContractCategoryDTO> fileContractCategoryDTOMap =
                categoryDTOS.stream()
                        .collect(
                                Collectors.toMap(
                                        FileContractCategoryDTO::getFileId,
                                        Function.identity(),
                                        (v1, v2) -> v1));
        fileTypeInfos.forEach(
                fileInfo -> {
                    FileContractCategoryDTO fileContractCategoryDTO =
                            fileContractCategoryDTOMap.get(fileInfo.getFileId());
                    if (fileContractCategoryDTO != null) {
                        fileInfo.setCategoryId(fileContractCategoryDTO.getCategoryId());
                    }
                });
        return fileTypeInfos;
    }

    private List<String> getFileIds(String processId) {
        // 获取当前子流程
        SubProcessDO currentSubProcess = baseProcessService.getCurrentSubProcess(processId);
        // 查询子流程文件列表
        QueryFlowFilesDTO flowFilesDTO = new QueryFlowFilesDTO();
        flowFilesDTO.setFlowId(currentSubProcess.getSubProcessId());
        flowFilesDTO.setProcessId(currentSubProcess.getProcessId());
        QueryFlowFilesResult filesResult =
                flowOperationFactory.getService(currentSubProcess).queryFlowFiles(flowFilesDTO);
        if (filesResult == null || filesResult.getFlowFiles() == null) {
            return Lists.newArrayList();
        }
        return filesResult.getFlowFiles().stream()
                .filter(file -> !file.isAttachment())
                .map(QueryFlowFilesResult.FlowFile::getFileId)
                .distinct()
                .collect(Collectors.toList());
    }

    private boolean oidOrGidEq(String oid1, String gid1, String oid2, String gid2) {
        return StringUtils.equals(oid1, oid2) || StringUtils.equals(gid1, gid2);
    }

    @Data
    private static class FileTypeInfo {
        // 合同id
        private String processId;
        // 文件id
        private String fileId;
        // 是否是线上文件
        private boolean onlineFile = true;
        // 分类id
        private String categoryId;
        // 分类名称
        private String categoryName;
        // 关键信息
        private List<PromptKeywordDTO> promptKeyword;
        // 提取字段
        private List<ExtractField> extractFields;

        public FileTypeInfo(String processId, String fileId) {
            this.processId = processId;
            this.fileId = fileId;
        }
    }


    private Boolean isContractSummaryFree(String subjectOid) {
        return Optional.ofNullable(saasCommonClient.queryAccountFunctions(buildContractSummaryVipFunctionsInput(subjectOid)))
                .map(AccountVipFunctionsOutput::getFunctions)
                .filter(CollectionUtils::isNotEmpty)
                .map(functions -> functions.get(0))
                .map(VipFunction::getLimit)
                .filter(MapUtils::isNotEmpty)
                .map(limit -> Boolean.TRUE.equals(limit.get(CONTRACT_SUMMARY_FREE_FLAG)))
                .orElse(Boolean.FALSE);
    }
    
    private AccountVipFunctionsInput buildContractSummaryVipFunctionsInput(String subjectOid) {
        AccountVipFunctionsInput input = new AccountVipFunctionsInput();
        input.setAccountId(subjectOid);
        input.setFunctionCodes(Collections.singletonList(FunctionCodeConstants.CONTRACT_SUMMARY));
        return input;
    }
    
}

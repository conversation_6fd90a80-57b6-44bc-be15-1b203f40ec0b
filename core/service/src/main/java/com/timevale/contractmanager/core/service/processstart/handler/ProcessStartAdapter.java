package com.timevale.contractmanager.core.service.processstart.handler;

import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.ProcessStartBO;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.request.sharesign.ShareSignTaskStartRequest;
import com.timevale.contractmanager.core.model.enums.ProcessStartMode;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.doccooperation.service.enums.XuanYuanBizSceneEnum;
import com.timevale.mandarin.base.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class ProcessStartAdapter {
    /**
     * 组装新的发起参数
     *
     * @param request
     * @return
     */
    public static ProcessStartBizRequest buildProcessStartBizRequest(
            ProcessStartBO<? extends FileBO> request) {
        ProcessStartBizRequest bizRequest = new ProcessStartBizRequest();
        bizRequest.setAccountId(request.getInitiatorAccountId());
        bizRequest.setTenantId(RequestContextExtUtils.getTenantId());
        bizRequest.setRefFlowTemplateId(request.getRefFlowTemplateId());
        bizRequest.setOriginProcessId(request.getOriginProcessId());
        bizRequest.setOriginFileIds(request.getOriginFileIds());
        bizRequest.setSecretType(request.getSecretType());
        bizRequest.setAllowRescind(request.getAllowRescind());
        bizRequest.setVisibleAccounts(request.getVisibleAccounts());
        bizRequest.setAppId(RequestContextExtUtils.getAppId());
        bizRequest.setCorpId(RequestContextExtUtils.getDingCorpId());
        // 需要将发起场景带入到facade参数中， 后续模板模式发起内部会用到
        bizRequest.setStartScene(request.getScene());
        // 初始化流程发起模式， 默认基于流程发起场景设值
        // ProcessStartScene.DIRECT_START --> ProcessStartMode.DIRECT_START
        // ProcessStartScene.TEMPLATE_START --> ProcessStartMode.TEMPLATE_START
        bizRequest.setStartMode(
                ProcessStartScene.DIRECT_START.getScene() == request.getScene()
                        ? ProcessStartMode.DIRECT_START.getMode()
                        : ProcessStartMode.TEMPLATE_START.getMode());
        bizRequest.setProcessGroupId(request.getProcessGroupId());
        bizRequest.setBizGroupId(request.getBizGroupId());
        bizRequest.setProcessName(request.getTaskName());
        bizRequest.setProcessComment(request.getTaskComment());
        bizRequest.setParticipants(request.getParticipants());
        bizRequest.setCcs(request.getCcs());
        bizRequest.setSignMode(request.getSignMode());
        bizRequest.setDedicatedCloudId(request.getDedicatedCloudId());
        if (CollectionUtils.isNotEmpty(request.getFiles())) {
            bizRequest.setContracts(
                    request.getFiles().stream()
                            .filter(i -> ProcessFileType.isContract(i.getFileType()))
                            .collect(Collectors.toList()));
            bizRequest.setAttachments(
                    request.getFiles().stream()
                            .filter(i -> ProcessFileType.isAttachment(i.getFileType()))
                            .collect(Collectors.toList()));
        }
        bizRequest.setFlowTemplateId(request.getFlowTemplateId());
        bizRequest.setOriginFlowTemplateId(request.getOriginFlowTemplateId());
        bizRequest.setApproveTemplateId(request.getApproveTemplateId());
        bizRequest.setSignPlatform(request.getPlatform());
        bizRequest.setSignEndTime(request.getSignEndTime());
        bizRequest.setSignValidityConfig(request.getSignValidityConfig());
        bizRequest.setFileEndTime(request.getFileEndTime());
        bizRequest.setFileValidityConfig(request.getFileValidityConfig());
        bizRequest.setRedirectUrl(request.getRedirectUrl());
        bizRequest.setSignedNoticeUrl(request.getSignedNoticeUrl());
        bizRequest.setProcessNotifyUrl(request.getProcessNotifyUrl());
        bizRequest.setNoticeType(request.getNoticeType());
        bizRequest.setSkipFill(request.getSkipFill());
        bizRequest.setSkipFillType(request.getSkipFillType());
        bizRequest.setToken(request.getToken());
        bizRequest.setClientId(RequestContextExtUtils.getClientId());
        bizRequest.setAppName(RequestContextExtUtils.getAppName());
        bizRequest.setBizScene(XuanYuanBizSceneEnum.XUANYUAN.getFrom());
        if (null != request.getBusinessType()) {
            bizRequest.setBusinessType(request.getBusinessType());
            bizRequest.setBusinessRemark(request.getBusinessRemark());
        }
        bizRequest.setStartType(request.getStartType());
        bizRequest.setBatchDropSeal(request.getBatchDropSeal());
        bizRequest.setSkipStartValid(request.getSkipStartValid());
        // 设置指定的签署流程id 及 合同流程id
        // 直接发起指定位置场景使用，保存模板的时候前置生成签署流程id和合同流程id
        bizRequest.setAssignFlowId(request.getFlowId());
        bizRequest.setAssignProcessId(request.getProcessId());
        bizRequest.setRelationProcessIds(request.getRelationProcessIds());
        bizRequest.setResetProcess(request.isResetProcess());
        bizRequest.setThirdApprovalInstanceId(request.getThirdApprovalInstanceId());
        bizRequest.setInitiatorDeptId(request.getInitiatorDeptId());
        bizRequest.setUseWatermarkTemplateId(request.getUseWatermarkTemplateId());
        bizRequest.setUseWatermarkTemplateSnapshotId(request.getUseWatermarkTemplateSnapshotId());
        bizRequest.setEpaasTemplateTag(request.isEpaasTag());
        bizRequest.setStartDataSource(request.getDataSource());
        bizRequest.setRemarks(request.getRemarks());
        bizRequest.setProcessRemarks(request.getProcessRemarks());
        bizRequest.setNeedSealAuthCheck(request.isNeedSealAuthCheck());
        //fda
        bizRequest.setFdaSignatureConfig(request.getFdaSignatureConfig());
        LogRecordContext.putVariable(AuditLogConstant.Field.ORIGIN_PROCESS_ID, request.getOriginProcessId());
        return bizRequest;
    }

    public static ShareSignTaskStartRequest buildShareSignTaskStartRequest(
            ProcessStartBO<? extends FileBO> request) {
        ShareSignTaskStartRequest shareSignTaskStartRequest = new ShareSignTaskStartRequest();
        shareSignTaskStartRequest.setOperatorOid(request.getInitiatorAccountId());
        shareSignTaskStartRequest.setSubjectOid(RequestContextExtUtils.getTenantId());
        shareSignTaskStartRequest.setRefFlowTemplateId(request.getRefFlowTemplateId());
        shareSignTaskStartRequest.setOriginProcessId(request.getOriginProcessId());
        shareSignTaskStartRequest.setOriginFileIds(request.getOriginFileIds());
        shareSignTaskStartRequest.setSecretType(request.getSecretType());
        shareSignTaskStartRequest.setAllowRescind(request.getAllowRescind());
        shareSignTaskStartRequest.setVisibleAccounts(request.getVisibleAccounts());
        shareSignTaskStartRequest.setScene(request.getScene());
        shareSignTaskStartRequest.setProcessGroupId(request.getProcessGroupId());
        shareSignTaskStartRequest.setBizGroupId(request.getBizGroupId());
        shareSignTaskStartRequest.setTaskName(request.getTaskName());
        shareSignTaskStartRequest.setTaskComment(request.getTaskComment());
        if (CollectionUtils.isNotEmpty(request.getParticipants())) {
            request.getParticipants().forEach(item -> {
                if (CollectionUtils.isEmpty(item.getInstances())) {
                    item.setInstances(Collections.emptyList());
                }
            });
            shareSignTaskStartRequest.setParticipants(request.getParticipants());
        }
        shareSignTaskStartRequest.setCcs(request.getCcs());
        shareSignTaskStartRequest.setSignMode(request.getSignMode());
        shareSignTaskStartRequest.setDedicatedCloudId(request.getDedicatedCloudId());
        if (CollectionUtils.isNotEmpty(request.getFiles())) {
            shareSignTaskStartRequest.setFiles((List<FileBO>) request.getFiles());
        }
        shareSignTaskStartRequest.setFlowTemplateId(request.getFlowTemplateId());
        shareSignTaskStartRequest.setOriginFlowTemplateId(request.getOriginFlowTemplateId());
        shareSignTaskStartRequest.setApproveTemplateId(request.getApproveTemplateId());
        shareSignTaskStartRequest.setSignEndTime(request.getSignEndTime());
        shareSignTaskStartRequest.setSignValidityConfig(request.getSignValidityConfig());
        shareSignTaskStartRequest.setFileEndTime(request.getFileEndTime());
        shareSignTaskStartRequest.setFileValidityConfig(request.getFileValidityConfig());
        shareSignTaskStartRequest.setRedirectUrl(request.getRedirectUrl());
        shareSignTaskStartRequest.setSignedNoticeUrl(request.getSignedNoticeUrl());
        shareSignTaskStartRequest.setProcessNotifyUrl(request.getProcessNotifyUrl());
        shareSignTaskStartRequest.setNoticeType(request.getNoticeType());
        shareSignTaskStartRequest.setSkipFill(request.getSkipFill());
        shareSignTaskStartRequest.setSkipFillType(request.getSkipFillType());
        shareSignTaskStartRequest.setToken(request.getToken());
        shareSignTaskStartRequest.setClientId(RequestContextExtUtils.getClientId());
        shareSignTaskStartRequest.setAppName(RequestContextExtUtils.getAppName());
        if (null != request.getBusinessType()) {
            shareSignTaskStartRequest.setBusinessType(request.getBusinessType());
            shareSignTaskStartRequest.setBusinessRemark(request.getBusinessRemark());
        }
        shareSignTaskStartRequest.setStartType(request.getStartType());
        shareSignTaskStartRequest.setBatchDropSeal(request.getBatchDropSeal());
        shareSignTaskStartRequest.setSkipStartValid(request.getSkipStartValid());
        shareSignTaskStartRequest.setRelationProcessIds(request.getRelationProcessIds());
        shareSignTaskStartRequest.setResetProcess(request.isResetProcess());
        shareSignTaskStartRequest.setThirdApprovalInstanceId(request.getThirdApprovalInstanceId());
        shareSignTaskStartRequest.setInitiatorDeptId(request.getInitiatorDeptId());
        shareSignTaskStartRequest.setUseWatermarkTemplateId(request.getUseWatermarkTemplateId());
        shareSignTaskStartRequest.setUseWatermarkTemplateSnapshotId(request.getUseWatermarkTemplateSnapshotId());
        shareSignTaskStartRequest.setEpaasTag(request.isEpaasTag());
        shareSignTaskStartRequest.setInitiatorAccountId(request.getInitiatorAccountId());
        shareSignTaskStartRequest.setProcessRemarks(request.getProcessRemarks());
        shareSignTaskStartRequest.setRemarks(request.getRemarks());
        return shareSignTaskStartRequest;
    }
}

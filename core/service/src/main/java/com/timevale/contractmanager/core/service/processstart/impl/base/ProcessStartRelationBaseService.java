package com.timevale.contractmanager.core.service.processstart.impl.base;

import com.timevale.contractmanager.common.service.enums.RelationTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.common.service.enums.ProcessBusinessType;
import com.timevale.contractmanager.core.service.grouping.CacheService;
import com.timevale.contractmanager.core.service.mq.producer.ProcessRelationProducer;
import com.timevale.contractmanager.core.service.process.handler.ProcessRelationHandler;
import com.timevale.contractmanager.core.service.process.handler.bean.SaveProcessRelationBean;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;

@Slf4j
@Service
public class ProcessStartRelationBaseService {
    /** 解约/续签流程分布式锁过期时长 */
    protected static final int DEFAULT_CACHE_TIME = 30;

    @Autowired protected CacheService cacheService;
    @Autowired protected ProcessRelationHandler processRelationHandler;
    @Autowired ProcessRelationProducer relationProducer;
    /**
     * 发起流程
     *
     * @param request
     * @param startFlowAction
     * @return
     */
    protected ProcessStartResult startRelationProcess(
            ProcessStartBizRequest request, RelationStartFlowAction startFlowAction) {
        ProcessBusinessType businessType = ProcessBusinessType.from(request.getBusinessType());
        // 获取关联类型
        Integer relationType = businessType.getRelationType();
        // 如果关联类型为空，表示非关联流程发起, 直接触发发起逻辑
        if (null == relationType) {
            return startFlowAction.doRelation();
        }

        // 关联流程校验参数合法性
        checkRelationProcessValid(request);

        String originProcessId = request.getOriginProcessId();
        // 判断是否需要加锁
        boolean needRelateLock = needRelateLock(originProcessId, businessType);
        String lockKey = String.format(Constants.RELATION_PROCESS_KEY_PREFIX, originProcessId);
        // 如果需要加锁， 判断是否有其他线程已发起
        if (needRelateLock && !cacheService.setNx(lockKey, DEFAULT_CACHE_TIME, TimeUnit.SECONDS)) {
            throw new BizContractManagerException(RELATE_PROCESS_ALREADY_STARTED);
        }
        try {
            ProcessStartResult startResult = startFlowAction.doRelation();

            SaveProcessRelationBean relationBean = new SaveProcessRelationBean();
            relationBean.setFileIds(request.getOriginFileIds());
            relationBean.setProcessId(startResult.getProcessId());
            relationBean.setOriginProcessId(originProcessId);
            relationBean.setRemark(request.getBusinessRemark());
            // 保存关联关系
            processRelationHandler.saveRelationProcessList(relationType, relationBean);
            //发送流程关联通知
            relationProducer.sendMessage(startResult.getProcessId(), originProcessId, startResult.getFlowId(), relationType);
            return startResult;
        } finally {
            // 加锁情况下，释放锁
            if (needRelateLock) {
                // 删除分布式锁， 分布式锁的保存位置为ProcessStartHelper, 发起时设置。
                TedisUtil.delete(lockKey);
            }
        }
    }

    /**
     * 关联流程校验参数合法性
     *
     * @param startRequest 发起请求对象
     */
    public void checkRelationProcessValid(ProcessStartBizRequest startRequest) {

        ProcessBusinessType businessType = ProcessBusinessType.from(startRequest.getBusinessType());
        String originProcessId = startRequest.getOriginProcessId();
        List<String> originFileIds = startRequest.getOriginFileIds();
        List<ParticipantBO> participantList = startRequest.getParticipants();

        // 如果当前业务类型未指定关联类型，跳过
        if (null == businessType.getRelationType()) {
            return;
        }

        RelationTypeEnum relationType = RelationTypeEnum.from(businessType.getRelationType());

        // 校验文件是否可以发起关联
        processRelationHandler.checkFileCanRelate(
                originProcessId, originFileIds, relationType.getType());

        // 校验签署方是否一致
        processRelationHandler.checkProcessParticipant(
                relationType, originProcessId, participantList);

        // 校验通过后，自定义处理参与方参数
        processRelationHandler.customizeHandleParticipant(relationType, startRequest);
        //校验合同与生成文件的关系
        processRelationHandler.checkRelationGenerateFile(relationType,startRequest.getOriginProcessId(),startRequest.getContracts());
    }

    /**
     * 校验是否需要进行关联校验加锁
     *
     * @param originProcessId
     * @param businessType
     * @return
     */
    private boolean needRelateLock(String originProcessId, ProcessBusinessType businessType) {
        return StringUtils.isNotBlank(originProcessId) && null != businessType.getRelationType();
    }

    public interface RelationStartFlowAction {
        ProcessStartResult doRelation();
    }
}

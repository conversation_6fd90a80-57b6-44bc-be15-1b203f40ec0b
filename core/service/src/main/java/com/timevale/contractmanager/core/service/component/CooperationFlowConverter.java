package com.timevale.contractmanager.core.service.component;

import static com.timevale.doccooperation.service.enums.ParticipantModeEnum.NORMAL;
import static com.timevale.doccooperation.service.enums.ParticipantModeEnum.OR_SIGN;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.ValidityConfigBean;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.bo.AttachmentConfigBO;
import com.timevale.contractmanager.core.model.bo.FileDetailBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.contractmanager.core.model.dto.cooperation.CooperationParticipant;
import com.timevale.contractmanager.core.model.dto.cooperation.ParticipantInstance;
import com.timevale.contractmanager.core.model.dto.response.ProcessBackFillResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartDetailResponse;
import com.timevale.doccooperation.service.enums.CooperationerRoleEnum;
import com.timevale.doccooperation.service.enums.DocTemplateFileStatusEnum;
import com.timevale.doccooperation.service.enums.DocTemplateFromEnum;
import com.timevale.doccooperation.service.model.CopyTo;
import com.timevale.doccooperation.service.model.FlowInfo;
import com.timevale.doccooperation.service.model.FlowTemplateConfig;
import com.timevale.doccooperation.service.result.CooperationBackfillInfoResult;
import com.timevale.doccooperation.service.result.CooperationDetailResult;
import com.timevale.doccooperation.service.util.LambdaUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.ListUtils;

import lombok.extern.slf4j.Slf4j;

import ma.glasnost.orika.CustomMapper;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.MappingContext;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

/**
 * 发起填写流程模型转换器
 *
 * <AUTHOR>
 * @since 2020/1/17
 */
@Slf4j
@Component
public class CooperationFlowConverter {

    @Autowired FlowTemplateModelConverter flowTemplateModelConverter;

    @Autowired MapperFactory mapperFactory;

    @PostConstruct
    public void init() {
        mapperFactory
                .classMap(FlowInfo.class, ProcessStartDetailResponse.class)
                .field("redirectUrl", "redirectUrl")
                .field("signValidity", "signEndTime")
                .field("fileValidity", "fileEndTime")
                .field("signedNoticeUrl", "signedNoticeUrl")
                .field("processNoticeUrl", "processNotifyUrl")
                .field("approveTemplateId", "approveTemplateId")
                .field("flowId", "flowId")
                .field("processId", "processId")
                .register();

        mapperFactory
                .classMap(CooperationDetailResult.TemplateDetail.class, FileDetailBO.class)
                .field("templateName", "fileName")
                .field("templateId", "fileId")
                .customize(
                        new CustomMapper<CooperationDetailResult.TemplateDetail, FileDetailBO>() {
                            @Override
                            public void mapAtoB(
                                    CooperationDetailResult.TemplateDetail templateDetail,
                                    FileDetailBO fileDetail,
                                    MappingContext context) {
                                fileDetail.setFileType(ProcessFileType.CONTRACT_FILE.getType());
                                //如果强行设置为2会导致重新发起有问题
                                fileDetail.setFrom(Objects.isNull(templateDetail.getFrom()) ? DocTemplateFromEnum.CONTRACT_FILE.getFrom() : templateDetail.getFrom());
                                fileDetail.setFileStatus(
                                        DocTemplateFileStatusEnum.UPLOADED.getStatus());
                            }
                        })
                .byDefault()
                .register();

        mapperFactory
                .classMap(CooperationDetailResult.CooperationerDetail.class, ParticipantBO.class)
                .field("cooperationerId", "participantId")
                .field("cooperationerLabel", "participantLabel")
                .field("cooperationerType", "type")
                .field("signOrder", "signOrder")
                .field("order", "fillOrder")
                .field("cooperationerSubjectType", "participantSubjectType")
                .customize(
                        new CustomMapper<
                                CooperationDetailResult.CooperationerDetail, ParticipantBO>() {
                            @Override
                            public void mapAtoB(
                                    CooperationDetailResult.CooperationerDetail cooperationerDetail,
                                    ParticipantBO participantBO,
                                    MappingContext context) {
                                if (CollectionUtils.isNotEmpty(
                                        cooperationerDetail.getAttachmentConfigs())) {
                                    participantBO.setAttachmentConfigs(
                                            cooperationerDetail.getAttachmentConfigs().stream()
                                                    .map(
                                                            p -> {
                                                                AttachmentConfigBO config =
                                                                        new AttachmentConfigBO();
                                                                BeanUtils.copyProperties(p, config);
                                                                return config;
                                                            })
                                                    .collect(Collectors.toList()));
                                }
                                //如果不需要通知， 设置通知方式为空数组
                                if (!cooperationerDetail.isNeedNotice()) {
                                    participantBO.setNoticeTypes(Lists.newArrayList());
                                }
                            }
                        })
                .byDefault()
                .register();

        mapperFactory
                .classMap(CooperationDetailResult.AccountDetail.class, ParticipantInstanceBO.class)
                .field("accountId", "accountOid")
                .field("nickname", "accountNick")
                .byDefault()
                .register();
    }

    public CooperationParticipant boToCooperationParticipant(ParticipantBO participantBO) {
        return mapperFactory.getMapperFacade().map(participantBO, CooperationParticipant.class);
    }

    public ParticipantInstance boToParticipantInstance(
            ParticipantInstanceBO participantInstanceBO) {
        return mapperFactory
                .getMapperFacade()
                .map(participantInstanceBO, ParticipantInstance.class);
    }

    /**
     * 填写流程信息回填转换
     *
     * @param cooperationBackfillInfoResult 填写流程对象
     * @return 结果
     */
    public ProcessBackFillResponse convertToBackFillResponse(
            CooperationBackfillInfoResult cooperationBackfillInfoResult) {
        ProcessBackFillResponse response;

        FlowInfo flowInfo = cooperationBackfillInfoResult.getFlowInfo();
        if (flowInfo == null) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.FLOW_QUERY_FAIL);
        }

        // 处理流程信息
        response = mapperFactory.getMapperFacade().map(flowInfo, ProcessBackFillResponse.class);
        response.setFlowTemplateId(cooperationBackfillInfoResult.getFlowTemplateId());
        response.setSchemaType(cooperationBackfillInfoResult.getSchemaType());
        response.setInitiatorAccountId(cooperationBackfillInfoResult.getInitiatorAccountId());
        response.setTaskName(cooperationBackfillInfoResult.getCooperationName());
        response.setEpaasTag(cooperationBackfillInfoResult.isEppasTemplateTag());

        if (null == response.getSignValidityConfig()) {
            response.setSignValidityConfig(ValidityConfigBean.defaultConfig());
        }
        if (null == response.getFileValidityConfig()) {
            response.setFileValidityConfig(ValidityConfigBean.defaultConfig());
        }

        // 处理抄送人信息
        List<CopyTo> copyUserList = flowInfo.getCopyTos();
        response.setCcs(flowTemplateModelConverter.copyToToCc(copyUserList));

        // 处理模板中的文件
        List<CooperationDetailResult.TemplateDetail> templateDetailList =
                cooperationBackfillInfoResult.getTemplates();
        List<FileDetailBO> fileDetailList = new ArrayList<>();
        if (!ListUtils.isEmpty(templateDetailList)) {
            fileDetailList =
                    mapperFactory
                            .getMapperFacade()
                            .mapAsList(templateDetailList, FileDetailBO.class);
        }

        // 处理附件
        List<FlowInfo.Attachment> attachmentList = flowInfo.getAttachments();
        if (!ListUtils.isEmpty(attachmentList)) {
            for (FlowInfo.Attachment attachment : attachmentList) {
                FileDetailBO fileDetail = new FileDetailBO();
                fileDetail.setFileType(ProcessFileType.ATTACHMENT_FILE.getType());
                fileDetail.setFileStatus(DocTemplateFileStatusEnum.UPLOADED.getStatus());
                fileDetail.setFrom(DocTemplateFromEnum.CONTRACT_FILE.getFrom());
                fileDetail.setFileId(attachment.getFileId());
                fileDetail.setFileName(attachment.getFileName());
                fileDetailList.add(fileDetail);
            }
        }
        response.setFiles(fileDetailList);

        // 处理参与方
        List<CooperationDetailResult.CooperationerDetail> cooperationerDetailList =
                cooperationBackfillInfoResult.getCooperationers();
        if (!ListUtils.isEmpty(cooperationerDetailList)) {
            List<ParticipantBO> participantList =
                    mapperFactory
                            .getMapperFacade()
                            .mapAsList(cooperationerDetailList, ParticipantBO.class);

            // 设置预填信息
            List<CooperationBackfillInfoResult.FillContent> fillContentList =
                    cooperationBackfillInfoResult.getFillContentList();
            Map<String, Object> fillContentMap = null;
            if (!ListUtils.isEmpty(fillContentList)) {
                fillContentMap =
                        fillContentList.stream()
                                .collect(
                                        Collectors.toMap(
                                                CooperationBackfillInfoResult.FillContent
                                                        ::getStructId,
                                                CooperationBackfillInfoResult.FillContent
                                                        ::getFillContent));
            }

            // Map<cooperationerId, CooperationerDetail>
            Map<String, CooperationDetailResult.CooperationerDetail> cooperationerDetailMap =
                    LambdaUtil.toMap(
                            cooperationerDetailList,
                            CooperationDetailResult.CooperationerDetail::getCooperationerId,
                            Function.identity());

            // 设置参与方实例
            for (int i = 0; i < participantList.size(); i++) {
                ParticipantBO participant = participantList.get(i);
                // 处理多个参与方实例数据
                CooperationDetailResult.CooperationerDetail cooperationerDetail =
                        cooperationerDetailMap.get(participant.getParticipantId());
                if (null == cooperationerDetail || CollectionUtils.isEmpty(cooperationerDetail.getAccounts())) {
                    continue;
                }
                participant.setInstances(mapperFactory.getMapperFacade()
                        .mapAsList(cooperationerDetail.getAccounts(), ParticipantInstanceBO.class));
                // 设置参与模式
                boolean isOrSign = participant.getInstances().size() > 1;
                participant.setParticipantMode(isOrSign ? OR_SIGN.getMode() : NORMAL.getMode());

                // 填写和签署都支持预填
                if (CooperationerRoleEnum.isHas(
                                participant.getRole(), CooperationerRoleEnum.FORMULATER)
                        || CooperationerRoleEnum.isHas(
                                participant.getRole(), CooperationerRoleEnum.APPROVALER)) {
                    participant.getInstances().get(0).setPreFillValues(fillContentMap);
                }
                // 企业类型覆盖
                participant.setParticipantSubjectType(participant.getInstances().get(0).getSubjectType());

            }

            response.setParticipants(participantList);
        }

        FlowTemplateConfig templateConfig = cooperationBackfillInfoResult.getFlowTemplateConfig();
        if (templateConfig != null) {
            response.setDedicatedCloudId(templateConfig.getDedicatedCloudId());
            response.setSignMode(templateConfig.getSignMode());
        }

        return response;
    }
}

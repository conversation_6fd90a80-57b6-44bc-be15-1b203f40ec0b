package com.timevale.contractmanager.core.service.processstart.impl;

import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.enums.ProcessStartMode;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.processstart.ProcessStartCoreService;
import com.timevale.contractmanager.core.service.processstart.impl.base.ProcessStartCoreBaseService;
import com.timevale.dayu.sdk.annotation.AuditLogAnnotation;
import com.timevale.doccooperation.service.input.flow.StartFlowBaseInput;
import com.timevale.doccooperation.service.input.flow.StartSignFlowInput;
import com.timevale.mandarin.base.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 * 直接发起业务类
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Slf4j
@Service
public class ProcessRedirectStartCoreServiceImpl extends ProcessStartCoreBaseService
        implements ProcessStartCoreService {

    @Override
    public ProcessStartMode startMode() {
        return ProcessStartMode.DIRECT_START;
    }

    /**
     * 发起流程， start方法调用之前， 需先调用checkParamValidity， 这块逻辑在ProcessStartHandler内部封装
     *
     * @param request
     * @return
     */
    @AuditLogAnnotation(
            firstModule = AuditLogConstant.SpEL.PROCESS_START_FIRST_MODULE,
            secondaryModule = AuditLogConstant.SpEL.PROCESS_START_SECONDARY_MODULE,
            appid = "#request.appId",
            enterpriseSpaceUnique1 = "#request.tenantAccount.accountOid",
            resourceEntSpaceUnique = "#request.tenantAccount.accountOid",
            userUnique1 = "#request.initiatorAccount.accountOid",
            resourceName = "#request.processName",
            condition = "{{#_result != null}}",
            result =
                    "{{#_result != null && T(com.timevale.mandarin.base.util.StringUtils).isNotBlank(#_result.processId) ? " + AuditLogConstant.RESULT + "}}",
            detailTactics = "1",
            postHandle = "auditLogProcessStartHandle")
    @Override
    public ProcessStartResult start(ProcessStartCoreRequest request) {
        // 发起必要参数校验
        validParam(request);
        // 发起流程
        return startFlow(request, (processId, coreRequest) -> doStart(processId, coreRequest));
    }

    /**
     * 触发底层创建流程
     *
     * @param startRequest
     * @return
     */
    private ProcessStartResult doStart(String processId, ProcessStartCoreRequest startRequest) {
        try {
            // 构建创建签署流程input
            StartSignFlowInput input =
                    processStartInputBuilder.buildBaseStartSignFlowInput(processId, startRequest);
            // 或签方账号重复校验
            input.getSigners().stream()
                    .map(StartFlowBaseInput.FlowParticipant::obtainAccounts)
                    .forEach(this::checkDuplicateParticipantAccounts);

            // 发起合同审批或签署流程
            return startApprovalOrSignFlow(input, buildUrlExtraParam(startRequest));
        } catch (Exception e) {
            // 如果是发起时指定了processId入参，则流程创建失败时删除该流程
            if (StringUtils.isNotBlank(processId)) {
                processStartHelper.deleteProcess(processId);
            }
            throw e;
        }
    }
}

package com.timevale.contractmanager.core.service.transaction;

import lombok.Getter;

/**
 * 通知事件类型
 *
 * <AUTHOR>
 * @since 2023-09-14
 */

@Getter
public enum CommonNoticeType {
    PROCESS_FILE_CONTRACT_CATEGORY_CHANGE("PROCESS_FILE_CONTRACT_CATEGORY_CHANGE", "流程文件合同类型变更"),
    ;

    /** 通知类型 */
    private String type;
    /** 通知类型描述 */
    private String desc;

    CommonNoticeType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}

package com.timevale.contractmanager.core.service.contractapproval.builder;

import com.google.common.collect.Maps;
import com.timevale.contractapproval.facade.dto.biz.contractprocess.*;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessStartSignParam;
import com.timevale.doccooperation.service.model.SignArea;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.tool.ValidateUtil;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同审批流程业务数据对象组装类
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
public class BizContractProcessDTOBuilder {

    /**
     * 组装合同审批流程业务数据对象
     *
     * @param param
     * @return
     */
    public static BizContractProcessDTO buildBizContractProcessDTO(ProcessStartSignParam param) {
        BizContractProcessDTO contractProcessDTO = new BizContractProcessDTO();
        contractProcessDTO.setProcessId(param.getProcessId());
        contractProcessDTO.setProcessTitle(param.getFlowName());
        contractProcessDTO.setAppId(param.getAppId());

        contractProcessDTO.setInitiator(buildContractProcessAccountDTO(param.getInitiator()));

        // 文件签署区映射关系Map
        Map<String, List<BizSignFieldDTO>> fileSignFieldMap = Maps.newHashMap();
        // 签署人
        if (CollectionUtils.isNotEmpty(param.getSigners())) {
            contractProcessDTO.setTasks(
                    param.getSigners().stream()
                            .peek(i -> appendFileBizSignFieldDTOMap(i, fileSignFieldMap))
                            .map(i -> buildBizContractProcessTaskDTO(i))
                            .collect(Collectors.toList()));
        }
        // 合同文件
        if (CollectionUtils.isNotEmpty(param.getDocuments())) {
            contractProcessDTO.setFiles(
                    param.getDocuments().stream()
                            .map(i -> buildBizContractProcessFileDTO(i, fileSignFieldMap))
                            .collect(Collectors.toList()));
        }
        // 附属文件
        if (CollectionUtils.isNotEmpty(param.getAttachments())) {
            contractProcessDTO.setAttachmentFiles(
                    param.getAttachments().stream()
                            .map(i -> buildBizContractProcessFileDTO(i))
                            .collect(Collectors.toList()));
        }
        return contractProcessDTO;
    }

    /**
     * 追加文件签署区到Map中
     * @param flowSigner
     * @param fileSignFieldMap
     */
    private static void appendFileBizSignFieldDTOMap(
            ProcessStartSignParam.FlowSigner flowSigner,
            Map<String, List<BizSignFieldDTO>> fileSignFieldMap) {
        if (CollectionUtils.isEmpty(flowSigner.getSignAreas())) {
            return;
        }
        for (SignArea signArea : flowSigner.getSignAreas()) {
            String fileId = signArea.getFileId();
            // 组装签署区信息
            BizSignFieldDTO signFieldDTO = buildBizSignFieldDTO(flowSigner, signArea);
            List<BizSignFieldDTO> signFieldDTOList = fileSignFieldMap.getOrDefault(fileId, Lists.newArrayList());
            signFieldDTOList.add(signFieldDTO);
            fileSignFieldMap.put(fileId, signFieldDTOList);
        }
    }

    /**
     * 组装签署区信息
     * @param flowSigner
     * @param signArea
     * @return
     */
    private static BizSignFieldDTO buildBizSignFieldDTO(
            ProcessStartSignParam.FlowSigner flowSigner, SignArea signArea) {
        BizSignFieldDTO signFieldDTO = new BizSignFieldDTO();
        signFieldDTO.setSubjectName(flowSigner.getSubjectName());
        signFieldDTO.setQiFeng(signArea.isQiFeng());
        if (null != signArea.getSignPos()) {
            signFieldDTO.setWidth(signArea.getSignPos().getWidth());
            signFieldDTO.setHeight(signArea.getSignPos().getHeight());
            signFieldDTO.setX(signArea.getSignPos().getX());
            signFieldDTO.setY(signArea.getSignPos().getY());
        }
        if (signArea.isQiFeng()) {
            signFieldDTO.setPage(signArea.getPage());
        } else if (null != signArea.getSignPos()) {
            signFieldDTO.setPage(String.valueOf(signArea.getSignPos().getPage()));
        }
        return signFieldDTO;
    }

    /**
     * 组装任务信息
     *
     * @param signer
     * @return
     */
    private static BizContractProcessTaskDTO buildBizContractProcessTaskDTO(
            ProcessStartSignParam.FlowSigner signer) {

        BizContractProcessTaskDTO taskDTO = new BizContractProcessTaskDTO();

        BizContractProcessTaskDTO.Operator operator = new BizContractProcessTaskDTO.Operator();
        operator.setPerson(buildBizContractAccountPersonDTO(signer));
        operator.setSubject(buildBizContractAccountSubjectDTO(signer));

        taskDTO.setExecute(operator);

        return taskDTO;
    }

    /**
     * 组装流程用户信息
     *
     * @param flowAccount
     * @return
     */
    private static BizContractProcessAccountDTO buildContractProcessAccountDTO(
            ProcessStartSignParam.FlowAccount flowAccount) {
        if (null == flowAccount) {
            return null;
        }
        BizContractProcessAccountDTO initiator = new BizContractProcessAccountDTO();
        initiator.setPerson(buildBizContractAccountPersonDTO(flowAccount));
        initiator.setSubject(buildBizContractAccountSubjectDTO(flowAccount));
        return initiator;
    }

    /**
     * 基于FlowAccount组装主体账号信息
     *
     * @param flowAccount
     * @return
     */
    private static BizContractAccountDTO buildBizContractAccountSubjectDTO(
            ProcessStartSignParam.FlowAccount flowAccount) {
        BizContractAccountDTO subject = new BizContractAccountDTO();
        subject.setGid(flowAccount.getSubjectGid());
        subject.setOid(flowAccount.getSubjectOid());
        subject.setName(flowAccount.getSubjectName());
        return subject;
    }

    /**
     * 基于FlowAccount组装个人账号信息
     *
     * @param flowAccount
     * @return
     */
    private static BizContractAccountDTO buildBizContractAccountPersonDTO(
            ProcessStartSignParam.FlowAccount flowAccount) {
        BizContractAccountDTO person = new BizContractAccountDTO();
        person.setGid(flowAccount.getAccountGid());
        person.setOid(flowAccount.getAccountOid());
        person.setName(flowAccount.getAccountName());
        if (ValidateUtil.mobileValid(flowAccount.getAccount())) {
            person.setMobile(flowAccount.getAccount());
        } else {
            person.setEmail(flowAccount.getAccount());
        }
        person.setNickname(flowAccount.getAccountNickname());
        return person;
    }

    /**
     * 组装文件信息
     *
     * @param flowFile
     * @return
     */
    private static BizContractProcessFileDTO buildBizContractProcessFileDTO(
            ProcessStartSignParam.FlowFile flowFile, Map<String, List<BizSignFieldDTO>> fileSignFieldMap) {
        BizContractProcessFileDTO processFileDTO = buildBizContractProcessFileDTO(flowFile);
        processFileDTO.setSignFields(fileSignFieldMap.get(processFileDTO.getFileId()));
        return processFileDTO;
    }

    /**
     * 组装文件信息
     *
     * @param flowFile
     * @return
     */
    private static BizContractProcessFileDTO buildBizContractProcessFileDTO(
            ProcessStartSignParam.FlowFile flowFile) {
        BizContractProcessFileDTO processFileDTO = new BizContractProcessFileDTO();
        processFileDTO.setFileName(flowFile.getFileName());
        processFileDTO.setFileId(flowFile.getFileId());
        processFileDTO.setFileKey(flowFile.getFileKey());
        processFileDTO.setWatermarkId(flowFile.getWatermarkId());
        return processFileDTO;
    }
}

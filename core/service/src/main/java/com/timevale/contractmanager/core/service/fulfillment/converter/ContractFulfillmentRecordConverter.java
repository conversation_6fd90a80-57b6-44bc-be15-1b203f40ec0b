package com.timevale.contractmanager.core.service.fulfillment.converter;

import com.timevale.contractmanager.common.dal.bean.fulfillment.ContractFulfillmentRecordDO;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentRecordStatusEnum;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordUpdateModel;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.mandarin.base.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ContractFulfillmentRuleConverter
 *
 * <AUTHOR>
 * @since 2023/10/12 11:42 上午
 */
public class ContractFulfillmentRecordConverter {

    public static List<ContractFulfillmentRecordDO> convertSaveRecordDOList(List<ContractFulfillmentRecordModel> recordModelList, UserAccount userAccountTenant){

        if(CollectionUtils.isEmpty(recordModelList)){
            return new ArrayList<>();
        }

        List<ContractFulfillmentRecordDO> recordDOList = new ArrayList<>();
        for(ContractFulfillmentRecordModel model : recordModelList){
            ContractFulfillmentRecordDO recordDO = new ContractFulfillmentRecordDO();
            recordDO.setRecordId(UUIDUtil.genUUID());
            recordDO.setProcessId(model.getProcessId());
            recordDO.setRuleId(model.getRuleId());
            recordDO.setStatus(FulfillmentRecordStatusEnum.UNMARKED.getStatus());
            recordDO.setTenantOid(userAccountTenant.getAccountOid());
            recordDO.setTenantGid(userAccountTenant.getAccountGid());
            recordDO.setNoticeOid(model.getNoticeOid());
            recordDO.setNoticeGid(model.getNoticeGid());
            recordDO.setTitle(model.getTitle());
            recordDO.setType(model.getType());
            recordDO.setTypeName(model.getTypeName());
            recordDO.setNoticeTime(model.getNoticeTime());
            recordDOList.add(recordDO);
        }
        return recordDOList;
    }

    public static List<ContractFulfillmentRecordDO> convertUpdateRecordDOList(List<ContractFulfillmentRecordUpdateModel> recordModelList){

        if(CollectionUtils.isEmpty(recordModelList)){
            return new ArrayList<>();
        }

        List<ContractFulfillmentRecordDO> recordDOList = new ArrayList<>();
        for(ContractFulfillmentRecordUpdateModel model : recordModelList){
            ContractFulfillmentRecordDO recordDO = new ContractFulfillmentRecordDO();
            recordDO.setRecordId(model.getRecordId());
            recordDO.setNoticeOid(model.getNoticeOid());
            recordDO.setNoticeGid(model.getNoticeGid());
            recordDO.setType(model.getType());
            recordDO.setTypeName(model.getTypeName());
            recordDOList.add(recordDO);
        }
        return recordDOList;
    }
}

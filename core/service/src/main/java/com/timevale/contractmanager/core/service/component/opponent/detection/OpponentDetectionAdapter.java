package com.timevale.contractmanager.core.service.component.opponent.detection;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionOrgDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionProblemInfoDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionSettingDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionTaskDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionOrgDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionProblemInfoDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionSettingDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionTaskDAO;
import com.timevale.contractmanager.common.service.bean.Preference;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.constant.FunctionLimitConstant;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentRiskLevelEnum;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionOrgStatusEnum;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionSwitchEnum;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionTaskStatusEnum;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionTaskTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.common.service.integration.client.impl.SaasCommonClientImpl;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionResultAggregateBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionDeptBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionPushObjectBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEnterpriseInformationBO;
import com.timevale.contractmanager.core.model.dto.request.QueryPreferenceRequest;
import com.timevale.contractmanager.core.model.dto.response.PreferenceResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.component.opponent.detection.chain.DetectionContext;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.contractmanager.core.service.mq.msg.OpponentDetectionMsg;
import com.timevale.contractmanager.core.service.opponent.EnterpriseInformationService;
import com.timevale.contractmanager.core.service.opponent.OpponentConfigCenter;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.saas.common.manage.common.service.model.output.VipFunctionQueryOutput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author:jianyang
 * @since 2021-08-16 18:05
 */
@Slf4j
@Component
public class OpponentDetectionAdapter {

	@Autowired
	private SaasCommonClientImpl saasCommonClient;

	@Autowired
	private OpponentDetectionSettingDAO settingDAO;

	@Autowired
	private OpponentDetectionTaskDAO taskDAO;

	@Autowired
	private OpponentDetectionAsyncSendMsg sendMsg;

	@Autowired
	private OpponentDetectionProblemInfoDAO problemInfoDAO;

	@Autowired
	private OpponentDetectionOrgDAO detectionOrgDAO;
	
	@Autowired
	private DetectionContext detectionContext;

	@Autowired
	private UserCenterService userCenterService;

	@Autowired
	private OpponentEntityDAO entityDAO;

	@Autowired
	private OpponentDetectionNoticeComponent detectionNotice;

	@Autowired
	private OpponentEntityDAO opponentEntityDAO;
	@Autowired
	private TransactionTemplate transactionTemplate;
	@Autowired
	private OpponentConfigCenter configCenter;
	@Autowired
	private EnterpriseInformationService enterpriseInformationService;
	@Autowired
	private PreferencesService preferencesService;


	/**
	 * 检测任务生成 初始化相关数据
	 *
	 * @param opponentEntityDOS
	 * @param userAccount
	 * @param taskType
	 * @param createByOid
	 * @param total
	 * @param taskId
	 * @param existSetting
	 * @return
	 */
	public String detectionTask(List<OpponentEntityDO> opponentEntityDOS,
								UserAccount userAccount,
								Integer taskType,
								String createByOid,
								Integer total, String taskId, OpponentDetectionSettingDO existSetting) {
		/**初始化任务 */
		try {
			if (StringUtils.isBlank(taskId)) {
				taskId = initTask(userAccount, total, existSetting, taskType, createByOid);
			}
		} catch (DuplicateKeyException e) {
			log.info("相对方检测任务重复新增 tenantGid:{}", userAccount.getAccountGid());
			taskId = userAccount.getAccountGid();
		}
		/** 异步发送mq */
		sendMsg.sendMsg(opponentEntityDOS, taskType, taskId, userAccount.getAccountGid());
		return taskId;
	}


	/**
	 * 批量检测任务开始
	 *
	 * @param userAccount
	 * @param startTime
	 * @param endTime
	 * @param createByOid
	 */
	public void batchDetectionTaskStart(UserAccount userAccount,
										Date startTime,
										Date endTime,
										String createByOid) {
		/** 获取国内企业相对方数据总数 */
		Long total = 0L;
		total = opponentEntityDAO.countDomesticDetectionEntity(
				userAccount.getAccountGid(),
				OpponentEntityTypeEnum.ORGANIZATION.getType(),
				OpponentRiskLevelEnum.WHITELIST.getType(), DeletedEnum.NO.code(),
				startTime, endTime);

		if (total == 0) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_ORG_NOTHING.getCode());
		}
		/** 获取相对方设置,为null则进行初始化 **/
		OpponentDetectionSettingDO existSetting = getSetting(userAccount.getAccountGid());
		try {
			existSetting = addDetectionSetting(existSetting, userAccount);
		} catch (DuplicateKeyException e) {
			log.info("相对方检测设置重复新增 tenantGid:{}", userAccount.getAccountGid());
			existSetting = getSetting(userAccount.getAccountGid());
		}

		/** 单次检测上限校验 */
		Integer singleLimit = getCountSingle(userAccount.getAccountOid());
		if (total > singleLimit) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_OVER_SINGLE_LIMIT.getCode());
		}

		/**同时只允许一个运行中的任务*/
		OpponentDetectionTaskDO taskDO = taskDAO.getByTaskStatus(
				userAccount.getAccountGid(),
				OpponentDetectionTaskStatusEnum.RUNING.getType(),
				OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType());

		if (taskDO != null) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TASK_IS_RUNING.getCode());
		}

		/** 数据小于1000直接获取*/
		if (total < 1000) {
			// 获取国内企业相对方，国外企业不需要检测
			List<OpponentEntityDO> opponentEntityDOS = opponentEntityDAO.getDomesticDetectionEntity(
					userAccount.getAccountGid(), OpponentEntityTypeEnum.ORGANIZATION.getType(),
					OpponentRiskLevelEnum.WHITELIST.getType(), DeletedEnum.NO.code(),
					startTime, endTime);

			detectionTask(opponentEntityDOS, userAccount, OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType(),
					createByOid, opponentEntityDOS.size(), null, existSetting);
		} else {
			/** 数据大于1000进行分页获取 */
			Integer offset = 0;
			Integer size = 1000;
			boolean loop = true;
			String taskId = "";
			do {
				// 分页获取国内企业相对方
				List<OpponentEntityDO> opponentEntityDOS = opponentEntityDAO.getDomesticDetectionEntityByPage(
						userAccount.getAccountGid(), OpponentEntityTypeEnum.ORGANIZATION.getType(),
						OpponentRiskLevelEnum.WHITELIST.getType(), DeletedEnum.NO.code(),
						startTime, endTime, offset, size);
				if (CollectionUtils.isNotEmpty(opponentEntityDOS)) {
					taskId = detectionTask(opponentEntityDOS, userAccount, OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType(),
							createByOid, total.intValue(), taskId, existSetting);
					offset += size;
				} else {
					loop = false;
				}

			} while (loop);
		}
	}


	/**
	 * 新增检测开始
	 *
	 * @param opponentEntityDOS
	 * @param userAccount
	 * @param total
	 */
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
	public String singleDetectionTaskStart(List<OpponentEntityDO> opponentEntityDOS, UserAccount userAccount, Integer total) {
		/** 校验会员版本是否符合 */
		if (!checkDetectionFunction(userAccount.getAccountOid())) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}
		/** 获取相对方设置,为null则进行初始化 **/
		OpponentDetectionSettingDO existSetting = getSetting(userAccount.getAccountGid());
		try {
			existSetting = addDetectionSetting(existSetting, userAccount);
		} catch (DuplicateKeyException e) {
			log.info("相对方检测设置重复新增 tenantGid:{}", userAccount.getAccountGid());
			existSetting = getSetting(userAccount.getAccountGid());
		}

		/*未开启新增检测或未设置相对方检测设置 则直接返回*/
		if (existSetting == null || existSetting.getRealTimeDetection().equals(OpponentDetectionSwitchEnum.CLOSE.getType())) {
			return null;
		}

		/** 单次检测上限校验 */
		Integer singleLimit = getCountSingle(userAccount.getAccountOid());
		if (total > singleLimit) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_OVER_SINGLE_LIMIT.getCode());
		}


		return detectionTask(opponentEntityDOS, userAccount, OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType(),
				null, opponentEntityDOS.size(), null, existSetting);
	}

	public boolean consumerDetection(OpponentDetectionMsg msg) {
		String tenantGid = msg.getTenantGid();
		String taskId = msg.getTaskId();
		Integer taskType = msg.getTaskType();
		String orgName = msg.getOrgName();
		/** 获取任务信息 */
		OpponentDetectionTaskDO taskDO = taskDAO.getTaskByTaskId(tenantGid, taskId);
		long startTime = System.currentTimeMillis();
		try {
			if (taskDO == null) {
				log.info("相对方检测 未查询到检测任务  tenantGid:{} taskId:{} taskType:{} orgName:{}",
						tenantGid, taskId, taskType, orgName);
				return false;
			}
			detectionHandler(taskId, tenantGid, orgName, msg, taskDO, startTime, taskType);
			return true;
		} catch (Exception e) {
			log.info("相对方检测失败 tenantGid:{} taskId:{} taskType:{} orgName:{} errMsg:{}",
					tenantGid, taskId, taskType, orgName, e.getMessage());
			saveDetectionOrg(taskId, tenantGid, null,
					OpponentDetectionOrgStatusEnum.UNCOMPLETED.getType(),
					startTime, msg.getUniCode());
			return true;
		}
	}

	public void detectionHandler(String taskId,
								 String tenantGid,
								 String orgName,
								 OpponentDetectionMsg msg,
								 OpponentDetectionTaskDO taskDO,
								 long startTime,
								 Integer taskType) {

		/**幂等,通过OpponentDetectionOrgDO 唯一code来实现*/
		OpponentDetectionOrgDO orgDO = detectionOrgDAO.getOrgByUniCode(msg.getUniCode(), taskId, tenantGid);
		if (orgDO != null) {
			return;
		}

		/** 执行检测链,前置校验 */
		DetectionResultAggregateBO aggregateBO = detectionNotContinue(tenantGid, taskDO, orgName, msg.getSocialCreditCode());
		/**插入企业数据状态为未检测*/
		if (aggregateBO == null || aggregateBO.getExistSetting() == null) {
			saveDetectionOrg(taskId, tenantGid, null,
					OpponentDetectionOrgStatusEnum.UNCOMPLETED.getType(),
					startTime, msg.getUniCode());
			log.info("相对方检测未进行检测 tenantGid:{} taskId:{} taskType:{} orgName:{}",
					tenantGid, taskId, taskType, orgName);
			return;
		}
		/** 执行检测链 */
		DetectionChainBO chainBO = new DetectionChainBO();
		chainBO.setDetail(aggregateBO.getDetail());
		chainBO.setOrgName(msg.getOrgName());
		chainBO.setTenantGid(tenantGid);
		chainBO.setSocialCreditCode(msg.getSocialCreditCode());
		chainBO.setTaskId(taskId);
		chainBO.setEntity(aggregateBO.getEntity());
		chainBO.setBusinessScope(aggregateBO.getExistSetting().getBusinessScope());
		if (taskDO.getTaskType() == OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType()) {
			chainBO.setBusinessScopeDetection(aggregateBO.getExistSetting().getBusinessScopeDetection());
		} else {
			chainBO.setBusinessScopeDetection(taskDO.getBusinessScope() == null
					? OpponentDetectionSwitchEnum.CLOSE.getType()
					: OpponentDetectionSwitchEnum.OPEN.getType());
		}
		String detectionItems = aggregateBO.getExistSetting().getDetectionItems();
		List<Integer> items = JSON.parseArray(detectionItems, Integer.class);
		List<OpponentDetectionChainResultBO> chainResults = Lists.newArrayList();
		detectionContext.execute(items, chainBO, chainResults);
		/**保存问题信息 */
		if (CollectionUtils.isEmpty(chainResults)) {
			saveDetectionOrg(
					taskId,
					tenantGid,
					aggregateBO,
					OpponentDetectionOrgStatusEnum.COMPLETE_NORMAL.getType(),
					startTime,
					msg.getUniCode());
		} else {
			saveDetectionData(tenantGid, taskId, taskType, orgName,
					chainResults, aggregateBO, startTime, msg.getUniCode());
		}
	}


	/**
	 * 保存 检测企业数据
	 *
	 * @param taskId
	 * @param tenantGid
	 * @param aggregateBO
	 * @param detectionOrgStatus
	 * @param startTime
	 * @param uniCode
	 */
	public void saveDetectionOrg(String taskId, String tenantGid,
								 DetectionResultAggregateBO aggregateBO,
								 Integer detectionOrgStatus,
								 long startTime, String uniCode) {

		Boolean result = transactionTemplate.execute(action -> {
			try {
				doSaveDetectionOrg(taskId, tenantGid, aggregateBO, detectionOrgStatus, startTime, uniCode);
				return true;
			} catch (Exception e) {
				action.setRollbackOnly();
				log.error(configCenter.logPrefix() + "saveDetectionOrg", e);
				return false;
			}
		});
		AssertX.isTrue(Boolean.TRUE.equals(result), "相对方检测无结果更新失败");
	}

	private void doSaveDetectionOrg(String taskId, String tenantGid,
								 DetectionResultAggregateBO aggregateBO,
								 Integer detectionOrgStatus,
								 long startTime, String uniCode) {
		/** 生成检测企业数据*/
		long endTime = System.currentTimeMillis();
		OpponentDetectionOrgDO orgDO = OpponentDetectionOrgDO
				.builder()
				.detectionOrgId(UUIDUtil.genUUID())
				.detectionTaskId(taskId)
				.problemMapping(null)
				.tenantGid(tenantGid)
				.uniCode(uniCode)
				.opponentEntityId(aggregateBO != null ? aggregateBO.getEntity().getUuid() : null)
				.riskLevelMapping(null)
				.elapsedTime(endTime - startTime)
				.detectionStatus(detectionOrgStatus)
				.build();
		detectionOrgDAO.insert(orgDO);
		//由于表数据较大 暂时先不更新等dba字段新增完毕
		if (aggregateBO != null && detectionOrgStatus.equals(OpponentDetectionOrgStatusEnum.COMPLETE_NORMAL.getType())) {
			/**更新 检测企业uuid 进 opponent_entity*/
			entityDAO.updateDetectionOrgId(orgDO.getDetectionOrgId(), aggregateBO.getEntity().getId());
		}
	}

	/**
	 * 插入检测配置
	 *
	 * @param existSetting
	 * @param userAccount
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public OpponentDetectionSettingDO addDetectionSetting(OpponentDetectionSettingDO existSetting, UserAccount userAccount) {
		Integer total = 0;
		if (existSetting == null) {
			existSetting = new OpponentDetectionSettingDO();
			total = getVipInfo(userAccount.getAccountOid(), RequestContextExtUtils.getClientId());
			OpponentDetectionSettingDO settingDO = OpponentDetectionSettingDO.builder()
					.usedDetectionNum(0)
					.detectionNum(total)
					.tenantGid(userAccount.getAccountGid())
					.realTimeDetection(OpponentDetectionSwitchEnum.CLOSE.getType())
					.immediatelyPush(OpponentDetectionSwitchEnum.CLOSE.getType())
					.usedDetectionNum(0)
					.autoSaveOpponent(OpponentDetectionSwitchEnum.OPEN.getType())
					.push(OpponentDetectionSwitchEnum.CLOSE.getType())
					.businessScopeDetection(OpponentDetectionSwitchEnum.CLOSE.getType())
					.build();
			settingDAO.insert(settingDO);
			BeanUtils.copyProperties(settingDO, existSetting);
			return existSetting;
		}
		return existSetting;
	}

	/**
	 * 校验检测是否继续进行
	 *
	 * @param tenantGid
	 * @param taskDO
	 * @param orgName
	 * @return
	 */
	public DetectionResultAggregateBO detectionNotContinue(String tenantGid,
														   OpponentDetectionTaskDO taskDO,
														   String orgName, String socialCreditCode) {
		DetectionResultAggregateBO aggregateBO = new DetectionResultAggregateBO();

		String taskId = taskDO.getDetectionTaskId();
		Integer taskType = taskDO.getTaskType();
		if (taskDO.getTaskStatus() == OpponentDetectionTaskStatusEnum.COMPLETE.getType()) {
			log.info("相对方检测 任务已完成  tenantGid:{} taskId:{} taskType:{} orgName:{}",
					tenantGid, taskId, taskType, orgName);
			return aggregateBO;
		}
		/**
		 * 批量任务查询到停止标识,停止任务
		 */
		if (taskType == OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType()
				&& OpponentDetectionTaskStatusEnum.STOP.getType() == taskDO.getTaskStatus()) {
			log.info("相对方检测 用户主动停止批量检测任务  tenantGid:{} taskId:{} taskType:{} ",
					tenantGid, taskId, taskType);
			return aggregateBO;
		}

		/**未配置相对方设置 */
		OpponentDetectionSettingDO existSetting = getSetting(tenantGid);
		if (existSetting == null) {
			log.info("相对方检测 未查询到配置项  tenantGid:{} taskId:{} taskType:{} orgName:{}",
					tenantGid, taskId, taskType, orgName);
			return aggregateBO;
		}

		/**未开启实时检测 不进行新增检测*/
		if (taskType.equals(OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType()) &&
				existSetting.getRealTimeDetection().equals(OpponentDetectionSwitchEnum.CLOSE.getType())) {
			log.info("相对方检测 未开启实时检测  tenantGid:{} taskId:{} taskType:{} orgName:{}",
					tenantGid, taskId, taskType, orgName);
			return aggregateBO;
		}

		//获取待检测企业信息和租户信息
		UserAccount userAccountTenant = userCenterService.getUserAccountDetailByGid(tenantGid);
		OpponentEntityDO entity = entityDAO.getByEntityUniqueId(orgName,
				tenantGid, OpponentEntityTypeEnum.ORGANIZATION.getType());

		if (entity == null || entity.getDeleted() == DeletedEnum.YES.code()) {
			log.info("相对方检测 待检测企业在相对方表中删除  tenantGid:{} taskId:{} taskType:{} orgName:{}",
					tenantGid, taskId, taskType, orgName);
			return aggregateBO;
		}

		/** 检测企业是自身跳过 */
		if (Objects.equals(userAccountTenant.getAccountName(), orgName)) {
			log.info("相对方检测 待检测企业是自身不进行检测  tenantGid:{} taskId:{} taskType:{} orgName:{}",
					tenantGid, taskId, taskType, orgName);
			return aggregateBO;
		}

		/**在黑名单中*/
		if (Objects.equals(entity.getRiskLevel(), OpponentRiskLevelEnum.BLACKLIST.getType())) {
			log.info("相对方检测 企业被加入黑名单不进行检测  tenantGid:{} taskId:{} taskType:{} orgName:{}",
					tenantGid, taskId, taskType, orgName);
			return aggregateBO;
		}

		OpponentEnterpriseInformationBO infoResult = enterpriseInformationService.getEnterpriseInformation(orgName);

		if (!infoResult.isSuccess()) {
			log.info("相对方检测获取工商信息失败 tenantGid:{}  待检测企业:{}  taskId:{} taskType:{}",
					tenantGid, orgName, taskId, taskType);
			return aggregateBO;
		}

		if (infoResult.getDetail() == null) {
			log.info("相对方检测获取工商信息为空 tenantGid:{}  待检测企业:{}  taskId:{} taskType:{}",
					tenantGid, orgName, taskId, taskType);
		}
		aggregateBO.setDetail(infoResult.getDetail());
		aggregateBO.setExistSetting(existSetting);
		aggregateBO.setEntity(entity);
		/** 批量任务经营范围检测关键词来源于快照 */
		if (taskDO.getTaskType() == OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType()) {
			existSetting.setBusinessScope(taskDO.getBusinessScope());
			aggregateBO.setExistSetting(existSetting);
		}
		return aggregateBO;
	}


	/**
	 * 保存相对方检测结果
	 *
	 * @param tenantGid
	 * @param taskId
	 * @param taskType
	 * @param orgName
	 * @param chainResults
	 * @param aggregateBO
	 * @param startTime
	 * @return
	 */
	public void saveDetectionData(String tenantGid, String taskId, Integer taskType, String orgName,
								  List<OpponentDetectionChainResultBO> chainResults,
								  DetectionResultAggregateBO aggregateBO,
								  long startTime, String uniCode) {

		Boolean result = transactionTemplate.execute(action -> {
			try {
				doSaveDetectionData(tenantGid, taskId, taskType, orgName, chainResults, aggregateBO, startTime, uniCode);
				return true;
			} catch (Exception e) {
				action.setRollbackOnly();
				log.error(configCenter.logPrefix() + "saveDetectionData", e);
				return false;
			}
		});
		AssertX.isTrue(Boolean.TRUE.equals(result), "相对方检测数据更新失败");
	}

	private void doSaveDetectionData(String tenantGid, String taskId, Integer taskType, String orgName,
								  List<OpponentDetectionChainResultBO> chainResults,
								  DetectionResultAggregateBO aggregateBO,
								  long startTime, String uniCode) {
		OpponentDetectionTaskDO taskDO = taskDAO.getTaskByTaskId(tenantGid, taskId);
		OpponentDetectionSettingDO existSetting = getSetting(tenantGid);


		/** 生成检测企业数据*/
		OpponentDetectionOrgDO orgDO = OpponentDetectionOrgDO
				.builder()
				.detectionOrgId(UUIDUtil.genUUID())
				.detectionTaskId(taskId)
				.problemMapping(calculateProblemMapping(chainResults))
				.tenantGid(tenantGid)
				.uniCode(uniCode)
				.opponentEntityId(aggregateBO.getEntity().getUuid())
				.riskLevelMapping(calculateRiskLevelMapping(chainResults))
				.detectionStatus(OpponentDetectionOrgStatusEnum.COMPLETE_ABNORMAL.getType())
				.elapsedTime(System.currentTimeMillis() - startTime)
				.build();
		detectionOrgDAO.insert(orgDO);

		/** 生成企业问题数据*/
		List<OpponentDetectionProblemInfoDO> problemInfoDOList = Lists.newArrayList();
		chainResults.forEach(x -> {
			OpponentDetectionProblemInfoDO problemInfoDO = OpponentDetectionProblemInfoDO
					.builder()
					.detectionOrgId(orgDO.getDetectionOrgId())
					.problemDesc(x.getProblemDesc())
					.problemNo(x.getProblemNo())
					.riskLevel(x.getRiskLevel())
					.tenantGid(tenantGid)
					.detectionTaskId(taskId)
					.suggestDesc(x.getSuggestDesc())
					.suggestDesc(x.getSuggestDesc())
					.build();
			problemInfoDOList.add(problemInfoDO);
		});
		problemInfoDAO.batchInsert(problemInfoDOList);

		/**更新 检测企业uuid 进 opponent_entity*/
		entityDAO.updateDetectionOrgId(orgDO.getDetectionOrgId(), aggregateBO.getEntity().getId());
		/**新增检测开启了实时检测推送*/
		if (taskType == OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType()
				&& existSetting.getPush() == OpponentDetectionSwitchEnum.OPEN.getType()
				&& existSetting.getImmediatelyPush() == OpponentDetectionSwitchEnum.OPEN.getType()) {

			detectionNotice.singleDetectionNotice(tenantGid, orgName, getNoticeOid(existSetting), chainResults);
		}
	}

	/**
	 * 查询配置
	 *
	 * @param tenantGid
	 * @return
	 */
	public OpponentDetectionSettingDO getSetting(String tenantGid) {
		OpponentDetectionSettingDO opponentettingDO = settingDAO.getByTenantGid(tenantGid, DeletedEnum.NO.code());
		
		if (opponentettingDO == null) {
			return opponentettingDO;
		}
		
		QueryPreferenceRequest preferenceRequest = new QueryPreferenceRequest();
		preferenceRequest.setOrgGid(tenantGid);
		preferenceRequest.setPreferenceKeys(Collections.singletonList(ProcessPreferenceEnum.AUTO_SAVE_OPPONENT.getKey()));

		/**用户配置了不添加相对方则跳过*/
		PreferenceResponse preferenceResponse = preferencesService.queryByCondition(preferenceRequest);

		boolean isAutoSaveOpponentDisabled = Optional.ofNullable(preferenceResponse)
				.map(PreferenceResponse::getPreferences)
				.filter(preferences -> !preferences.isEmpty())
				.map(preferences -> preferences.get(0))
				.map(Preference::getPreferenceValue)
				.map(Boolean::parseBoolean)
				.orElse(true);
		opponentettingDO.setAutoSaveOpponent(isAutoSaveOpponentDisabled ?
				OpponentDetectionSwitchEnum.OPEN.getType() : OpponentDetectionSwitchEnum.CLOSE.getType());
		return opponentettingDO;
	}

	/**
	 * 查询会员版本是否支持相对方检测
	 *
	 * @param tenantOid
	 * @return
	 */
	public boolean checkDetectionFunction(String tenantOid) {
		return saasCommonClient.checkFunctionValid(tenantOid, FunctionCodeConstants.OPPONENTDETECTION, false);
	}

	/**
	 * 获取会员版本对应的检测次数
	 *
	 * @param tenantOid
	 * @return
	 */
	public Integer getVipInfo(String tenantOid, String clientId) {
		Integer maxDetectionCount = 0;
        VipFunctionQueryOutput output =
                saasCommonClient.queryVipFunctionInfo(
                        tenantOid, FunctionCodeConstants.OPPONENTDETECTIONNUM, clientId);
		Map<String, Object> limit = output.getLimit();
		if (!(Objects.isNull(limit) || Objects.equals(limit.size(), 0))) {
			//根据会员版本获取检测数量
			maxDetectionCount = (Integer) output.getLimit().get(FunctionLimitConstant.MAX_USE_COUNT);
		}
		return maxDetectionCount;
	}

	/**
	 * 获取单次检测上限
	 *
	 * @param tenantOid
	 * @return
	 */
	public Integer getCountSingle(String tenantOid) {
		Integer maxDetectionCount = 0;
        VipFunctionQueryOutput output =
                saasCommonClient.queryVipFunctionInfo(
                        tenantOid,
                        FunctionCodeConstants.OPPONENTDETECTIONNUM,
                        RequestContextExtUtils.getClientId());
		Map<String, Object> limit = output.getLimit();
		if (!(Objects.isNull(limit) || Objects.equals(limit.size(), 1))) {
			//根据会员版本获取单次检测数量
			maxDetectionCount = (Integer) output.getLimit().get(FunctionLimitConstant.MAX_DETECTION_COUNT);
		}
		return maxDetectionCount;
	}

	/**
	 * 初始化任务并返回任务id
	 *
	 * @param userAccount
	 * @param total
	 * @param existSetting
	 * @param taskType
	 * @param createByOid
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String initTask(UserAccount userAccount,
						   Integer total,
						   OpponentDetectionSettingDO existSetting,
						   Integer taskType,
						   String createByOid) {
		/** 初始化批量检测任务信息 */
		String taskId = null;
		if (taskType.equals(OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType())) {
			OpponentDetectionTaskDO taskDO = OpponentDetectionTaskDO
					.builder()
					.detectionTaskId(UUIDUtil.genUUID())
					.detectionTotality(total)
					.detectionQuantityCompletion(0)
					.businessScope(existSetting != null
							&& existSetting.getBusinessScopeDetection() == OpponentDetectionSwitchEnum.OPEN.getType()
							&& StringUtils.isNotBlank(existSetting.getBusinessScope()) ?
							existSetting.getBusinessScope() : null)
					.tenantGid(userAccount.getAccountGid())
					.taskStatus(OpponentDetectionTaskStatusEnum.RUNING.getType())
					.taskType(taskType)
					.createByOid(createByOid)
					.build();
			taskDAO.insert(taskDO);
			taskId = taskDO.getDetectionTaskId();
		}

		/** 获取新增检测taskId,新增检测任务不存在则添加 */
		if (taskType.equals(OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType())
				&& existSetting != null
				&& existSetting.getRealTimeDetection().equals(OpponentDetectionSwitchEnum.OPEN.getType())) {
			String tenantGid = userAccount.getAccountGid();
			OpponentDetectionTaskDO taskDO = taskDAO.getTaskByTaskId(tenantGid, tenantGid);
			if (taskDO == null) {
				OpponentDetectionTaskDO taskInit = OpponentDetectionTaskDO
						.builder()
						.tenantGid(tenantGid)
						.taskType(OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType())
						.taskStatus(OpponentDetectionTaskStatusEnum.RUNING.getType())
						.detectionTaskId(tenantGid)
						.detectionQuantityCompletion(0)
						.detectionTotality(0)
						.reportProblemNum(0L)
						.ventureBusinessNum(0)
						.build();
				taskDAO.insert(taskInit);
			}
			taskId = userAccount.getAccountGid();
		}
		return taskId;
	}

	/**
	 * 计算问题类型映射值
	 *
	 * @param chainResults
	 * @return
	 */
	public int calculateProblemMapping(List<OpponentDetectionChainResultBO> chainResults) {
		int mapping = 0;
		for (OpponentDetectionChainResultBO chainResult : chainResults) {
			mapping += (int) Math.pow(2, chainResult.getProblemNo() - 1);
		}
		return mapping;
	}

	/**
	 * 计算风险等级映射值
	 *
	 * @param chainResults
	 * @return
	 */
	public int calculateRiskLevelMapping(List<OpponentDetectionChainResultBO> chainResults) {
		List<Integer> result = chainResults.stream()
				.map(OpponentDetectionChainResultBO::getRiskLevel).collect(Collectors.toList());
		List<Integer> disResult = result.stream().distinct().collect(Collectors.toList());
		int mapping = 0;
		for (Integer x : disResult) {
			mapping += (int) Math.pow(2, (x / 10 - 1));
		}
		return mapping;
	}


	/**
	 * 获取任务更新信息
	 */
	public void totalOrgNum() {
		/**获取进行中的任务*/
		List<OpponentDetectionTaskDO> taskDOS = taskDAO.getTaskListByType(OpponentDetectionTaskStatusEnum.RUNING.getType());
		if (CollectionUtils.isEmpty(taskDOS)) {
			return;
		}

		for (OpponentDetectionTaskDO taskDO : taskDOS) {
			try {
				updateTaskData(taskDO);
			} catch (Exception e) {
				log.info("任务信息更新失败 taskId:{}", taskDO.getDetectionTaskId(), e);
			}
		}
	}

	/**
	 * 发送前一天的检测结果
	 */
	public void sendDetectionResult() {
		long size = 50;
		long offset = 0;
		boolean stop = true;

		/** 分批发送*/
		do {
			List<OpponentDetectionSettingDO> list = settingDAO.getNeedSendList(
					offset,
					size,
					DeletedEnum.NO.code(),
					OpponentDetectionSwitchEnum.OPEN.getType());
			if (CollectionUtils.isNotEmpty(list)) {
				for (OpponentDetectionSettingDO settingDO : list){
					try {
						sendNotice(settingDO);
					}catch (Exception e){
						log.info("相对方检测定时推送检测结果失败 tenantGid:{} errMsg:{}", settingDO.getTenantGid(), e.getMessage());
					}
				}
				offset += list.size();
			} else {
				stop = false;
			}
		} while (stop);
	}

	/**
	 * 发送通知
	 * @param settingDO
	 */
	public void sendNotice(OpponentDetectionSettingDO settingDO) {
		int detectionOrgNormal = detectionOrgDAO.sumOrgByDetectionStatus(
				settingDO.getTenantGid(),
				DateUtils.getDayBegin(DateUtils.getBeforeDate()),
				DateUtils.getDayEnd(DateUtils.getBeforeDate()),
				OpponentDetectionOrgStatusEnum.COMPLETE_NORMAL.getType(),
				settingDO.getTenantGid());
		int detectionOrgAbnormal = detectionOrgDAO.sumOrgByDetectionStatus(
				settingDO.getTenantGid(),
				DateUtils.getDayBegin(DateUtils.getBeforeDate()),
				DateUtils.getDayEnd(DateUtils.getBeforeDate()),
				OpponentDetectionOrgStatusEnum.COMPLETE_ABNORMAL.getType(),
				settingDO.getTenantGid());

		long reportProblemNum = problemInfoDAO.countProblemByTaskId(
				settingDO.getTenantGid(),
				DateUtils.getDayBegin(DateUtils.getBeforeDate()),
				DateUtils.getDayEnd(DateUtils.getBeforeDate()));
		int total = detectionOrgNormal + detectionOrgAbnormal;
		/** 检测数量不为0才发送*/
		if(total != 0){
			detectionNotice.batchDetectionNotice(
					settingDO.getTenantGid(),
					total,
					detectionOrgAbnormal,
					reportProblemNum,
					getNoticeOid(settingDO),
					settingDO.getTenantGid(),
					OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType());
		}else {
			log.info("相对方检测定时推送检测结果为零不推送 tenantGid:{} ", settingDO.getTenantGid());
		}
	}

	/**
	 * 获取通知人oid
	 *
	 * @param settingDO
	 */
	public List<String> getNoticeOid(OpponentDetectionSettingDO settingDO) {
		List<String> sendOid = new ArrayList<>();
		UserAccount userAccount = userCenterService.getUserAccountDetailByGid(settingDO.getTenantGid());
		if (settingDO.getPushObject() != null) {
			OpponentDetectionPushObjectBO pushObjectBO = JSON.parseObject(settingDO.getPushObject(), OpponentDetectionPushObjectBO.class);
			if (CollectionUtils.isNotEmpty(pushObjectBO.getDepartment())) {
				for (OpponentDetectionDeptBO detectionDeptBO : pushObjectBO.getDepartment()) {
					sendOid.addAll(userCenterService.getDeptMembers(userAccount.getAccountOid(), detectionDeptBO.getDeptId()));
				}
			}

			if (CollectionUtils.isNotEmpty(pushObjectBO.getPerson())) {
				pushObjectBO.getPerson().forEach(x -> {
					sendOid.add(x.getMemberId());
				});
			}
		}
		return sendOid;
	}

	public void updateTaskData(OpponentDetectionTaskDO taskDO) {

		Boolean success = transactionTemplate.execute(action -> {
			try {
				doUpdateTaskData(taskDO);
				return true;
			} catch (Exception e) {
				log.error("opponent updateTaskData  taskId : {}", taskDO.getId(), e);
				action.setRollbackOnly();
				return false;
			}
		});

		if (!Boolean.TRUE.equals(success)) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.SERVICE_BIZ_ERROR.getCode(),
					"updateTaskData failure");
		}
	}


	/**
	 * 更新任务信息
	 *
	 * @param taskDO
	 */
	private void doUpdateTaskData(OpponentDetectionTaskDO taskDO) {
		/**统计当月总的有效检测次数*/
		Integer detectionNUm = detectionOrgDAO.sumOrgByTenantGid(
				taskDO.getTenantGid(),
				DateUtils.getMonthFirstDay(new Date()),
				DateUtils.getMonthLastDay(new Date()));
		OpponentDetectionSettingDO settingDO = getSetting(taskDO.getTenantGid());
		if (null == settingDO) {
			return;
		}
		if (settingDO.getDetectionNum() >= detectionNUm) {
			settingDO.setUsedDetectionNum(detectionNUm);
			settingDAO.update(settingDO);
		}else {
			/**检测次数用完不更新*/
			return;
		}

		/**统计发现问题数量*/
		long problemNum = problemInfoDAO.countProblemByTaskId(taskDO.getDetectionTaskId(), null, null);
		/**统计检测数量*/
		List<OpponentDetectionOrgDO> orgDOList = detectionOrgDAO.countOrgs(taskDO.getDetectionTaskId());
		if (CollectionUtils.isEmpty(orgDOList)) {
			return;
		}
		Integer detectionNum = orgDOList.stream()
				.filter(x -> x.getDetectionStatus() != null)
				.mapToInt(OpponentDetectionOrgDO::getOrgNum).sum();
		/**新增检测统计检测企业数量*/
		if (taskDO.getTaskType().equals(OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType())) {
			detectionNum = orgDOList.stream()
					.filter(x -> x.getDetectionStatus() != null && !x.getDetectionStatus().equals(OpponentDetectionOrgStatusEnum.UNCOMPLETED.getType()))
					.mapToInt(OpponentDetectionOrgDO::getOrgNum).sum();
		}
		/**统计问题企业数量*/
		Integer ventureBusinessNum = orgDOList.stream()
				.filter(x -> x.getDetectionStatus() != null && x.getDetectionStatus().equals(OpponentDetectionOrgStatusEnum.COMPLETE_ABNORMAL.getType()))
				.mapToInt(OpponentDetectionOrgDO::getOrgNum).sum();

		if (taskDO.getTaskType().equals(OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType())
				&& detectionNum.equals(taskDO.getDetectionTotality())) {
			taskDO.setTaskStatus(OpponentDetectionTaskStatusEnum.COMPLETE.getType());
		}
		taskDO.setVentureBusinessNum(ventureBusinessNum);
		taskDO.setReportProblemNum(problemNum);
		taskDO.setDetectionQuantityCompletion(detectionNum);
		taskDAO.update(taskDO);
		/**批量检测任务完成发送通知*/
		if(taskDO.getTaskType().equals(OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType())
				&& detectionNum.equals(taskDO.getDetectionTotality())
				&& StringUtils.isNotBlank(taskDO.getCreateByOid())
				&& ventureBusinessNum != 0){
			detectionNotice.batchDetectionNotice(
					taskDO.getTenantGid(),
					detectionNUm,
					ventureBusinessNum,
					problemNum,
					Collections.singletonList(taskDO.getCreateByOid()),
					taskDO.getDetectionTaskId(),
					taskDO.getTaskType());
		}
	}
}

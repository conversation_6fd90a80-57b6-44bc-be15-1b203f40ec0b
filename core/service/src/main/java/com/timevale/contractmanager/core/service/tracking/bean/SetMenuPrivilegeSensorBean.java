package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;

/**
 * @Author:jianyang
 * @since 2022-02-24 14:26
 */
@Data
public class SetMenuPrivilegeSensorBean extends BaseAttributeSensorBean {
	private String setType;
	private String folderName;
	private String fatherFolderName;
	private String folderClass;
	private Integer numberOfViewAbleDepartment;
	private Integer numberOfViewAbleStaff;
	private Integer numberOfDownloadAbleDepartment;
	private Integer numberOfDownloadAbleStaff;
	private Integer numberOfEditAbleDepartment;
	private Integer numberOfDditAbleStaff;

	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData = super.sensorData();
		sensorData.put(SET_TYPE, sensorString(setType));
		sensorData.put(FOLDER_NAME, sensorString(folderName));
		sensorData.put(FATHER_FOLDER_NAME, sensorString(fatherFolderName));
		sensorData.put(FOLDER_CLASS, sensorString(folderClass));
		sensorData.put(NUMBER_OF_VIEW_ABLE_DEPARTMENT, sensorString(numberOfViewAbleDepartment));
		sensorData.put(NUMBER_OF_VIEW_ABLE_STAFF, sensorString(numberOfViewAbleStaff));
		sensorData.put(NUMBER_OF_DOWNLOAD_ABLE_DEPARTMENT, sensorString(numberOfDownloadAbleDepartment));
		sensorData.put(NUMBER_OF_DOWNLOAD_ABLE_STAFF, sensorString(numberOfDownloadAbleStaff));
		sensorData.put(NUMBER_OF_EDIT_ABLE_DEPARTMENT, sensorString(numberOfEditAbleDepartment));
		sensorData.put(NUMBER_OF_EDIT_ABLE_STAFF, sensorString(numberOfDditAbleStaff));
		return sensorData;
	}
}

package com.timevale.contractmanager.core.service.grouping;

/**
 * 台账服务类
 *
 * @author: xuanzhu
 * @since: 2019-10-30 15:09
 */
public interface StandingBookService {

    /**
     * 获取企业空间下默认台账规则id
     * @param tenantId 空间Oid
     * @return 企业空间下默认台账规则id
     */
    public String getDefaultRuleId(String tenantId);


    /**
     * 设置AI解析合同的状态
     *
     * <p>使用场景：
     *
     * <p>1.用于判断后台AI是否对该企业下的合同解析完毕，便于台账规则的修改
     *
     * @param tenantId
     */
    public void setAiStatus(String tenantId);
}

package com.timevale.contractmanager.core.service.watermark.impl;

import com.timevale.contractmanager.common.service.enums.WatermarkBizTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.service.other.DocManagerService;
import com.timevale.contractmanager.core.service.watermark.WatermarkStrategy;
import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkModel;
import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkResult;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootModel;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootResult;
import com.timevale.docmanager.service.input.watermark.AddWatermarkInput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkContentTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkTypeEnum;
import com.timevale.saas.common.manage.common.service.model.bean.watermark.WatermarkTemplate;
import com.timevale.saas.common.manage.common.service.model.input.watermark.BatchGenerateWatermarkSnapShootInput;
import com.timevale.saas.common.manage.common.service.model.input.watermark.WatermarkTemplateDetailInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.IntFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: contract-manager-project
 * @Description: 文件水印--合同编号水印策略处理器
 * @date Date : 2023年04月23日 14:46
 */
@Slf4j
@Service
public class DocContractNumDocWatermarkStrategy implements WatermarkStrategy {

    @Autowired SaasCommonClient saasCommonClient;

    @Qualifier("watermarkAsyncResultExecutor")
    @Autowired
    AsyncTaskExecutor watermarkAsyncResultExecutor;

    @Autowired DocManagerService docManagerService;



    @Override
    public String getType() {
        return WatermarkStrategy.assembleServiceId(WatermarkTypeEnum.DOC_WATERMARK, WatermarkContentTypeEnum.CONTRACT_NUM);
    }

    @Override
    public GenerateWatermarkSnapShootResult generateWatermarkSnapShoot(GenerateWatermarkSnapShootModel model) {
        GenerateWatermarkSnapShootResult result = new GenerateWatermarkSnapShootResult();
        if(StringUtils.isBlank(model.getWatermarkId()) || CollectionUtils.isEmpty(model.getFiles())){
            return result;
        }

        BatchGenerateWatermarkSnapShootInput batchInput = new BatchGenerateWatermarkSnapShootInput();
        batchInput.setBizType(WatermarkBizTypeEnum.CONTRACT_PROCESS.getBizType());
        batchInput.setBizId(model.getProcessId());
        batchInput.setOid(model.getTenantId());
        batchInput.setOperatorOid(model.getOperatorId());
        AtomicBoolean hasContractNO = new AtomicBoolean(false);
        List<BatchGenerateWatermarkSnapShootInput.BizWatermarkSnapShootData> watermarkSnapShootDataList = model.getFiles().stream().map(fileBO -> {
            BatchGenerateWatermarkSnapShootInput.BizWatermarkSnapShootData watermarkSnapShootData = new BatchGenerateWatermarkSnapShootInput.BizWatermarkSnapShootData();
            watermarkSnapShootData.setUniqueId(fileBO.getFileId());
            String contractNo = fileBO.getContractNo();
            if(StringUtils.isNotBlank(contractNo) && !hasContractNO.get()){
                //是否有合同编号
                hasContractNO.set(true);
            }
            watermarkSnapShootData.setBizContent(contractNo);
            watermarkSnapShootData.setWatermarkTemplateId(model.getWatermarkId());
            watermarkSnapShootData.setFileId(fileBO.getFileId());
            return watermarkSnapShootData;
        }).collect(Collectors.toList());
        if(!hasContractNO.get()){
            //没有合同编号,直接返回
            return result;
        }

        batchInput.setWatermarkSnapShootDataList(watermarkSnapShootDataList);
        Map<String, String> fileIdAndWatermarkSSIdMap = saasCommonClient.batchGenerateWatermarkSnapShoot(batchInput);

        result.setFileIdAndWatermarkSSIdMap(fileIdAndWatermarkSSIdMap);
        return result;
    }

    @Override
    public AddWatermarkResult addWatermark(AddWatermarkModel model) {
        AddWatermarkResult result = new AddWatermarkResult();
        result.setScreenWatermark(false);
        // 批量异步添加水印
        CompletableFuture<Map.Entry<String, String>>[] completableFutures =
                model.getFiles().stream()
                        .map(
                                fileBO ->
                                        CompletableFuture.supplyAsync(
                                                new SupplierWrapper<Map.Entry<String, String>>(
                                                        () -> {
                                                            String watermarkFileId = addWatermarkToFile(
                                                                    fileBO.getFileId(),
                                                                    fileBO.getWatermarkId(),
                                                                    model.getTenantId(),
                                                                    model.getOperatorId());
                                                            return new AbstractMap.SimpleEntry<>(fileBO.getFileId(), watermarkFileId);
                                                        }),
                                                watermarkAsyncResultExecutor))
                        .toArray((IntFunction<CompletableFuture<Map.Entry<String, String>>[]>)CompletableFuture[]::new);

        CompletableFuture.allOf(completableFutures).join();
        Map<String, String> originalAndWatermarkFileIdMap = Stream.of(completableFutures)
                .map(CompletableFuture::join)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        result.setOriginalAndWatermarkFileIdMap(originalAndWatermarkFileIdMap);
        return result;

    }

    /**
     * 单文件添加水印
     *
     * @param fileId
     * @param watermarkId
     * @return 新的文件ID
     */
    private String addWatermarkToFile(String fileId, String watermarkId, String tenantOid, String operatorId) {
        WatermarkTemplateDetailInput input = new WatermarkTemplateDetailInput();
        input.setWatermarkId(watermarkId);
        input.setOid(tenantOid);
        input.setOperatorOid(operatorId);
        WatermarkTemplate watermarkTemplateDetail =
                saasCommonClient.getWatermarkTemplateDetail(input);
        if (watermarkTemplateDetail == null || watermarkTemplateDetail.getAttribute() == null) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.WATERMARK_IS_NULL);
        }
        AddWatermarkInput addWatermarkInput = convertWatermarkTemplate2Input(fileId, watermarkTemplateDetail);
        addWatermarkInput.setOperatorId(operatorId);
        return docManagerService.addWaterMarkToFile(addWatermarkInput);
    }
}

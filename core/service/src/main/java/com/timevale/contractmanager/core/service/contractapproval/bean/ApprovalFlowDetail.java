package com.timevale.contractmanager.core.service.contractapproval.bean;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 审批节点信息
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@ApiModel("审批流程详情")
public class ApprovalFlowDetail extends ApprovalFlowInfo {

    /** 业务流程id */
    private String bizId;

    /** 审批流程状态 */
    private Integer approvalFlowStatus;

    /** 当前审批节点 */
    private ApprovalNode currentNode;

    /** 抄送节点 */
    private List<ApprovalNode> ccNodes;

    /** 全部审批节点 */
    private List<ApprovalNode> taskNodes;

}

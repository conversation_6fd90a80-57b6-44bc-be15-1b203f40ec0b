package com.timevale.contractmanager.core.service.sharesign.bean;

import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021-08-06
 */
@Data
public class ShareSignParticipantAccount extends ToString {

    private String account;

    private String accountOid;

    private String accountGid;

    private String accountUid;

    private String accountName;

    private String subjectName;

    private String subjectId;

    private Integer participantType;

    public boolean sameAccount(String oid, String gid) {
        if (StringUtils.isNoneBlank(accountOid, oid) && StringUtils.equals(accountOid, oid)) {
            return true;
        }
        return StringUtils.isNoneBlank(accountGid, gid) && StringUtils.equals(accountGid, gid);
    }
    public static ShareSignParticipantAccount valueOf(UserAccountDetail userAccount) {
        ShareSignParticipantAccount signAccount = new ShareSignParticipantAccount();
        signAccount.setAccount(userAccount.account());
        signAccount.setAccountOid(userAccount.getAccountOid());
        signAccount.setAccountGid(userAccount.getAccountGid());
        signAccount.setAccountUid(userAccount.getAccountUid());
        signAccount.setAccountName(userAccount.getAccountName());
        return signAccount;
    }
}

package com.timevale.contractmanager.core.service.opponent;

import com.timevale.contractmanager.common.service.model.opponent.BatchQueryRiskLevelByProcessIdsModel;
import com.timevale.contractmanager.common.service.model.opponent.QueryRiskLevelByEntityModel;
import com.timevale.contractmanager.common.service.model.opponent.QueryRiskLevelByOidOrGidModel;
import com.timevale.contractmanager.common.service.model.opponent.data.TenantData;
import com.timevale.contractmanager.common.service.result.opponent.OpponentRiskLevelByProcessIdsResult;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentBatchGetBlackListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.ResetEnterpriseViewRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBatchGetBlackListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBlackListOrgCodeRespone;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBlackListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentEnterpriseViewResponse;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-03-04 21:27
 */
public interface OpponentBlackListService {
	/**
	 * rpc调用
	 * @param processId
	 * @param tenantsDatas
	 */
	void queryBlackListByProcessId(String processId,String flowId, List<TenantData> tenantsDatas);

	/**
	 * rpc调用
	 * @param model
	 */
	void queryBlackListNoAccountByEntity(QueryRiskLevelByEntityModel model, boolean checkFunction);

	void queryRiskLevelByOidOrGid(QueryRiskLevelByOidOrGidModel queryRiskLevelByOidOrGidModel);

	OpponentRiskLevelByProcessIdsResult batchQueryRiskLevelByProcessIds(BatchQueryRiskLevelByProcessIdsModel model);

	/**
	 * 获取黑名单
	 * @param processId
	 * @param processType
	 * @param accountId
	 * @return
	 */
	OpponentBlackListResponse getBlackListByProcess(String processId,Integer processType,String accountId);

	OpponentBlackListResponse getBlackListForSign(String processId,Integer processType,String authorizerIds);
	/**
	 * 更新实体的风险等级
	 * @param tenantOid
	 * @param uuids
	 * @param riskLevel
	 */
	Integer updateEntityRiskLevel(String tenantOid,String operatorOid,String uuids,Integer riskLevel);

	/**
	 * 批量更新实体的风险等级
	 * @param tenantOid
	 * @param uuids
	 * @param riskLevel
	 */
	Integer batchUpdateEntityRiskLevel(String tenantOid,String operatorOid, List<String> uuids, Integer riskLevel);

	/**
	 * 查询企业的风险等级
	 * @param tenantOid
	 * @param orgName
	 * @return
	 */
	Boolean getOrgRiskLevel(String tenantOid,String orgName);

	/**
	 * 查询个人的风险等级
	 * @param tenantOid
	 * @param contact
	 * @return
	 */
	Boolean getIndividualRiskLevel(String tenantOid,String contact);

	/**
	 * 获取供应商访问token和编码
	 * @param orgName
	 * @return
	 */
	OpponentBlackListOrgCodeRespone getTokenAndOrgCode(String orgName, String tenantOid);

	/**
	 *
	 * @param opponentBatchGetBlackListRequest
	 * @param tenantOid
	 * @return
	 */
	List<OpponentBatchGetBlackListResponse> batchGetBlackList(OpponentBatchGetBlackListRequest opponentBatchGetBlackListRequest, String tenantOid);

	OpponentEnterpriseViewResponse getNumberOfEnterpriseView(String tenantOid);

	void resetEnterpriseView(ResetEnterpriseViewRequest resetEnterpriseViewRequest);

}

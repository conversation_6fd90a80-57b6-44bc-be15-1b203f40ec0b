package com.timevale.contractmanager.core.service.infocollect;

import com.timevale.contractmanager.core.model.dto.request.infocollect.InfoCollectStartRequest;
import com.timevale.contractmanager.core.model.dto.response.infocollect.InfoCollectStartResponse;
import com.timevale.footstone.base.model.response.BaseResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * InfoCollectFormDataService
 *
 * <AUTHOR>
 * @since 2022/9/21 2:47 下午
 */
public interface InfoCollectFormDataService {

    /**
     * 获取发起凭证
     */
    InfoCollectStartResponse start(String tenantOid, String operatorOid, InfoCollectStartRequest request);

}

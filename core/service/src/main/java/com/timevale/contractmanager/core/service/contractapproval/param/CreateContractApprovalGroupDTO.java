package com.timevale.contractmanager.core.service.contractapproval.param;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 创建合同审批组请求参数
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Data
public class CreateContractApprovalGroupDTO extends ToString {

    /** 用户oid */
    private String accountId;
    /** 用户gid */
    private String accountGid;
    /** 用户姓名 */
    private String accountName;
    /** 主体oid */
    private String subjectId;
    /** 主体gid */
    private String subjectGid;
    /** 业务组id */
    private String bizGroupId;
    /** 业务组名称 */
    private String bizGroupName;
    /** 总数 */
    private Integer totalCount;
}

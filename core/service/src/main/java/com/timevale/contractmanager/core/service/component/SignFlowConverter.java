package com.timevale.contractmanager.core.service.component;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.ValidityConfigBean;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.core.model.bo.*;
import com.timevale.contractmanager.core.model.dto.response.ProcessBackFillResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.common.service.enums.ParticipantSubjectType;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.saas.common.enums.SignModeEnum;
import com.timevale.contractmanager.core.service.enums.SealTypeEnum;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.BizUtils;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.doccooperation.service.enums.*;
import com.timevale.doccooperation.service.util.LambdaUtil;
import com.timevale.footstone.rpc.enums.AuthorizerTypeEnum;
import com.timevale.footstone.rpc.enums.SignerRoleEnum;
import com.timevale.footstone.rpc.model.flowmodel.bean.RecipientDTO;
import com.timevale.footstone.rpc.model.flowmodel.bean.SignerDto;
import com.timevale.footstone.rpc.model.flowmodel.bean.SignerOperator;
import com.timevale.footstone.rpc.result.flowresult.bean.*;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.tool.ValidateUtil;
import ma.glasnost.orika.CustomMapper;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.MappingContext;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

import static com.timevale.footstone.rpc.enums.SignTaskTypeEnum.OR_SIGN;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;

/**
 * 签署流程模型转换器
 *
 * <AUTHOR>
 * @since 2022/4/20
 */
@Component
public class SignFlowConverter {

    /** 签署流程扩展属性-流程模板id */
    private static final String TEMPLATE_ID = "templateId";

    @Autowired MapperFactory mapperFactory;

    @Autowired UserCenterService userCenterService;

    @PostConstruct
    public void init() {
        mapperFactory
                .classMap(UserBO.class, RecipientDTO.class)
                .field("accountOid", "recipientAccountId")
                .field("accountName", "recipientName")
                .field("accountRealName", "recipientRealName")
                .field("subjectId", "recipientIdentityAccountId")
                .field("subjectName", "recipientIdentityAccountName")
                .field("subjectType", "recipientIdentityAccountType")
                .field("subjectRealName", "recipientIdentityRealName")
                .field("accountNick", "recipientNickname")
                .field("account", "recipientAccount")
                .customize(
                        new CustomMapper<UserBO, RecipientDTO>() {
                            @Override
                            public void mapBtoA(
                                    RecipientDTO recipientDTO,
                                    UserBO userBO,
                                    MappingContext context) {
                                String recipientAccount = recipientDTO.getRecipientAccount();
                                if (StringUtils.isNotBlank(recipientAccount)
                                        && !ValidateUtil.mobileValid(recipientAccount)
                                        && !ValidateUtil.emailValid(recipientAccount)) {
                                    userBO.setAccount(null);
                                }
                            }
                        })
                .byDefault()
                .register();

        mapperFactory
                .classMap(SignerDto.class, ParticipantBO.class)
                .field("signerAccount", "participantId")
                .field("signerRoleLabel", "participantLabel")
                .field("signOrder", "signOrder")
                .field("signerAuthorizedAccountType", "participantSubjectType")
                .customize(
                        new CustomMapper<SignerDto, ParticipantBO>() {
                            @Override
                            public void mapAtoB(
                                    SignerDto signerDto,
                                    ParticipantBO participantBO,
                                    MappingContext context) {
                                participantBO.setType(CooperationerTypeEnum.USER.getType());
                                participantBO.setRole(CooperationerRoleEnum.SIGNER.getRole().toString());
                                participantBO.setRoleSet(CooperationerRoleSetEnum.INITIATOR_DESIGNATE.getType());
                                participantBO.setSignRequirements(parseSignRequirements(signerDto));
                                participantBO.setSealType(parseSealTypes(signerDto));
                                participantBO.setWillTypes(parseWillTypes(signerDto));
                            }
                        })
                .byDefault()
                .register();

        mapperFactory
                .classMap(SignerDto.class, ParticipantInstanceBO.class)
                .field("signerAccountId", "accountOid")
                .field("signerAuthorizedAccountName", "subjectRealName")
                .field("signerNickname", "accountNick")
                .field("signerName", "accountName")
                .field("signerAccount", "account")
                .field("signerAuthorizedAccountType", "subjectType")
                .field("signerAuthorizedAccountRealName", "subjectRealName")
                .field("signerRealName", "accountRealName")
                .field("signerAuthorizedAccountId", "subjectId")
                .field("signerAuthorizedAccountName", "subjectName")
                .byDefault()
                .register();
    }

    /**
     * 转换签署人的签署要求
     *
     * @param signerDto
     * @return
     */
    private String parseSignRequirements(SignerDto signerDto) {
        // 企业主体的签署要求
        if (AuthorizerTypeEnum.ORGANIZE.getType().equals(signerDto.getSignerAuthorizedAccountType())) {
            return StringUtils.join(BizUtils.parseSignRequirements(signerDto), ",");
        }
        // 个人主体默认无签署要求
        return "";
    }

    /**
     * 转换签署人的签章类型
     *
     * @param signerDto
     * @return
     * @see SealTypeEnum  返回对应的sealType
     */
    private String parseSealTypes(SignerDto signerDto) {

        String sealType = signerDto.getSealType();
        String handDrawnWay = signerDto.getHandDrawnWay();
        if (AuthorizerTypeEnum.ORGANIZE.getType().equals(signerDto.getSignerAuthorizedAccountType()) &&
                IdsUtil.getIdList(signerDto.getSignerSignRoles()).contains(SignerRoleEnum.ORGAN_AGENT.getType()) &&
                CollectionUtils.isNotEmpty(signerDto.getSignerRoleDetails())) {
            // 参与方如果有多个签署区，签署只会随机取一个签署区的 sealType 和  handDrawnWay， 所以要把这个参数进行补充
            // 企业包含经办人签
            for (SignerDto.SignerRoleDetail signerRoleDetail : signerDto.getSignerRoleDetails()) {
                if (SignerRoleEnum.ORGAN_AGENT.getType().equals(signerRoleDetail.getSignerSignRole())) {
                    sealType = signerRoleDetail.getSealType();
                    handDrawnWay = signerRoleDetail.getHandDrawnWay();
                }
            }
        }

        // todo tl-bug 既然sealType为空为什么要有默认值呢，企业非经办人签不需要sealType, 是不是应该个人的时候或者经办人签署才这样判断
        Set<String> sealTypes = BizUtils.parseSealType(sealType);
        // 企业主体  经办人签这里要改掉 这里有问题，以前是按企业签署方不会有   企业的saas发起参数如果 经办人签 sealType是 0，1，2  发起之后backFill 只有 "0,1" 了
//        if (AuthorizerTypeEnum.ORGANIZE.getType().equals(signerDto.getSignerAuthorizedAccountType())) {
//            return Joiner.on(",").join(sealTypes);
//        }
        // 个人主体     ai + 普通  0,1      只有ai    只有普通
        if (sealTypes.contains(SealTypeEnum.HAND_DRAW.getHandDrawnWay())
                && StringUtils.isNotBlank(handDrawnWay)) {
            // 移除手绘印章类型
            sealTypes.remove(SealTypeEnum.HAND_DRAW.getHandDrawnWay());
            // 根据手绘方式返回实际支持的手绘印章类型
            sealTypes.addAll(BizUtils.parseHandDrawnWay(handDrawnWay));
        }
        return Joiner.on(",").join(sealTypes);
    }

    /**
     * 转换签署人的意愿方式
     *
     * @param signerDto
     * @return
     */
    private List<String> parseWillTypes(SignerDto signerDto) {
        List<String> willTypes = signerDto.getWillTypes();
        // 如果意愿方式为空， 直接返回
        if (CollectionUtils.isEmpty(willTypes)) {
            return willTypes;
        }
        return willTypes.stream()
                .map(willType -> checkFaceWillType(willType) ? WillTypeEnum.FACE.getType() : willType)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 转换签署人的通知方式
     *
     * @param signerDto
     * @return
     */
    private List<Integer> parseNoticeTypes(SignerDto signerDto) {
        List<String> noticeTypes = signerDto.getNoticeTypes();
        // 如果意愿方式为空， 直接返回
        if (null == noticeTypes) {
            return null;
        }
        return noticeTypes.stream()
                .map(noticeType -> Integer.valueOf(noticeType))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 判断是否人脸认证, 排除智能视频认证
     *
     * @param willType
     * @return
     */
    private boolean checkFaceWillType(String willType) {
        return willType.startsWith("FACE_") && !WillTypeEnum.VIDEO.getType().equals(willType);
    }

    /**
     * 签署流程信息回填转换
     *
     * @param signFlowDetail 签署流程对象
     * @return 结果
     */
    public ProcessBackFillResponse convertToBackFillResponse(SignFlowDetailOutput signFlowDetail) {
        ProcessBackFillResponse response = new ProcessBackFillResponse();

        // 封装任务名称
        response.setTaskName(signFlowDetail.getBusinessScene());

        // 封装流程文件列表
        response.setFiles(convertBackFillFiles(signFlowDetail));

        // 封装参与人
        response.setParticipants(convertBackFillParticipants(signFlowDetail));

        // 封装抄送人
        List<RecipientDTO> recipientList = signFlowDetail.getRecipients();
        if (!ListUtils.isEmpty(recipientList)) {
            response.setCcs(mapperFactory.getMapperFacade().mapAsList(recipientList, UserBO.class));
        }

        // 封装流程过期时间
        response.setSignEndTime(signFlowDetail.getSignValidity());
        // 封装合同到期时间
        response.setFileEndTime(signFlowDetail.getContractValidity());
        response.setSignValidityConfig(ValidityConfigBean.defaultConfig());
        response.setFileValidityConfig(ValidityConfigBean.defaultConfig());
        // 设置流程模板id
        response.setFlowTemplateId(parseFlowTemplateId(signFlowDetail.getExtend()));

        if (null != signFlowDetail.getConfigInfo()) {
            response.setSignMode(signFlowDetail.getConfigInfo().getSignMode());
            response.setDedicatedCloudId(getDedicateCloudId(signFlowDetail.getExtend()));
        }

        return response;
    }

    private String getDedicateCloudId(Map<String, String> dataMap) {
        if (CollectionUtils.isEmpty(dataMap)) {
            return null;
        }
        return dataMap.get(Constants.DEDICATED_CLOUD_ID_KEY);
    }

    /**
     * 组装签署流程文件回填信息
     * @param signFlowDetail
     * @return
     */
    private List<FileDetailBO> convertBackFillFiles(SignFlowDetailOutput signFlowDetail) {
        // 签署流程回填文件列表
        List<FileDetailBO> fileDetailList = new ArrayList<>();
        // 初始化签署文件列表
        List<DocumentResBean> contractList = Lists.newArrayList();
        // 追加只读文件
        if (CollectionUtils.isNotEmpty(signFlowDetail.getCommonDocs())) {
            contractList.addAll(signFlowDetail.getCommonDocs());
        }
        // 追加已签文件
        if (CollectionUtils.isNotEmpty(signFlowDetail.getSignedDocs())) {
            contractList.addAll(signFlowDetail.getSignedDocs());
        }
        // 追加未签文件
        if (CollectionUtils.isNotEmpty(signFlowDetail.getSignDocs())) {
            contractList.addAll(signFlowDetail.getSignDocs());
        }
        // 处理签署文件列表
        if (CollectionUtils.isNotEmpty(contractList)) {
            for (DocumentResBean contract : contractList) {
                FileDetailBO fileDetail = new FileDetailBO();
                fileDetail.setFileType(ProcessFileType.CONTRACT_FILE.getType());
                fileDetail.setFileStatus(DocTemplateFileStatusEnum.UPLOADED.getStatus());
                fileDetail.setFrom(DocTemplateFromEnum.CONTRACT_FILE.getFrom());
                fileDetail.setFileId(contract.getFileId());
                fileDetail.setFileName(contract.getFileName());
                fileDetailList.add(fileDetail);
            }
        }
        // 处理附件列表
        List<AttachmentResBeanDTO> attachmentList = signFlowDetail.getAttachments();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            for (AttachmentResBeanDTO attachment : attachmentList) {
                FileDetailBO fileDetail = new FileDetailBO();
                fileDetail.setFileType(ProcessFileType.ATTACHMENT_FILE.getType());
                fileDetail.setFileStatus(DocTemplateFileStatusEnum.UPLOADED.getStatus());
                fileDetail.setFrom(DocTemplateFromEnum.CONTRACT_FILE.getFrom());
                fileDetail.setFileId(attachment.getFileId());
                fileDetail.setFileName(attachment.getAttachmentName());
                fileDetailList.add(fileDetail);
            }
        }
        return fileDetailList;
    }

    /**
     * 转换签署方回填信息
     *
     * @param signFlowDetail
     * @return
     */
    private List<ParticipantBO> convertBackFillParticipants(SignFlowDetailOutput signFlowDetail) {
        List<SignerDto> signerList = signFlowDetail.getSigners();
        if (ListUtils.isEmpty(signerList)) {
            return Lists.newArrayList();
        }

        // Map<oid, 用户信息>，所有签署人/操作人账号信息
        Map<String, UserAccountDetail> signerAccountMap = querySignerAccountMap(signerList);
        // Map<signOrder, 签署区>
        Map<Integer, SignFieldSimpleDTO> signFieldMap =
                getOrSignFieldMap(signFlowDetail.getSignDocs(), signFlowDetail.getSignedDocs());
        // 是否或签
        boolean isOrSign =
                signerList.stream().anyMatch(x -> OR_SIGN.getType().equals(x.getSignTaskType()));

        Map<Object, ParticipantBO> participantMap = new HashMap<>();
        // 遍历构建合同流程的各个参与方，并填充参与方下的实例
        for (int i = 0; i < signerList.size(); i++) {
            SignerDto signer = signerList.get(i);
            Object signerKey = signerKey(signer, isOrSign);
            // 参与方
            ParticipantBO participant =
                    participantMap.getOrDefault(signerKey, getParticipant(signFlowDetail, signer, i, signFieldMap));
            // 参与方实例
            ParticipantInstanceBO instance = getParticipantInstance(signFlowDetail, signer, signerAccountMap);
            participant.getInstances().add(instance);
            participant.setParticipantStartType(signer.getParticipantStartType());
            participant.setParticipantStartValue(signer.getParticipantStartValue());
            // 记录这个参与方，下一个签署人如果属于同一个参与方，直接把实例放到这个参与方下
            participantMap.putIfAbsent(signerKey, participant);
        }
        return participantMap.values().stream()
                .sorted(Comparator.comparing(ParticipantBO::getParticipantLabel))
                .collect(Collectors.toList());
    }

    /**
     * 查询签署人/操作人账号信息Map
     *
     * @param signerList
     * @return
     */
    public Map<String, UserAccountDetail> querySignerAccountMap(List<SignerDto> signerList) {
        // 获取所有签署人及操作人id列表
        Set<String> signerAccountIds = Sets.newHashSet();
        signerList.forEach(
                i -> {
                    // 签署人id
                    signerAccountIds.add(i.getSignerAccountId());
                    // 操作人id
                    List<SignerOperator> signerOperators = i.getSignerOperators();
                    if (CollectionUtils.isNotEmpty(signerOperators)) {
                        signerAccountIds.addAll(
                                LambdaUtil.toSet(signerOperators, SignerOperator::getOperatorId));
                    }
                });
        // 根据id列表查询账号信息
        return userCenterService.queryFatAccountMapByAccountIds(signerAccountIds);
    }

    public Map<String, UserAccountDetail> querySignerAccountAndSubjectMap(List<SignerDto> signerList) {
        // 获取所有签署人及操作人id列表
        Set<String> signerAccountIds = Sets.newHashSet();
        signerList.forEach(
                i -> {
                    // 签署人id
                    signerAccountIds.add(i.getSignerAccountId());
                    // 签署人主体id
                    signerAccountIds.add(i.getSignerAuthorizedAccountId());
                    List<SignerOperator> signerOperators = i.getSignerOperators();
                    if (CollectionUtils.isNotEmpty(signerOperators)) {
                        // 操作人id
                        signerAccountIds.addAll(
                                LambdaUtil.toSet(signerOperators, SignerOperator::getOperatorId));
                        // 操作主体id
                        signerAccountIds.addAll(
                                LambdaUtil.toSet(signerOperators, SignerOperator::getOperatorAuthorizerId));
                    }
                });
        // 根据id列表查询账号信息
        return userCenterService.queryFatAccountMapByAccountIds(signerAccountIds);
    }

    public String buildSignerKey(String subjectId, String accountId, Integer order) {
        return subjectId + "-" + accountId + "-" + order;
    }


    public Object signerKey(SignerDto signer, boolean isOrSign) {
        if (isOrSign) {
            return signer.getSignOrder();
        } else {
            return StringUtils.joinWith(
                    "-",
                    signer.getSignerAuthorizedAccountId(),
                    signer.getSignerAccountId(),
                    signer.getSignOrder());
        }
    }

    /**
     * 解析流程模板id
     * @param extend
     * @return
     */
    public String parseFlowTemplateId(Map<String, String> extend) {
        return MapUtils.isEmpty(extend) ? null : extend.get(TEMPLATE_ID);
    }

    /**
     * 获取参与方
     *
     * @param signer 签署人
     * @param participantNo 参与方序号
     * @param signFieldMap 签署区域Map<signOrder, signField>，用来回填指定印章
     * @return 参与方对象
     */
    private ParticipantBO getParticipant(
            SignFlowDetailOutput signFlowDetail,
            SignerDto signer, int participantNo, Map<Integer, SignFieldSimpleDTO> signFieldMap) {
        ParticipantBO participant =
                mapperFactory.getMapperFacade().map(signer, ParticipantBO.class);
        participant.setParticipantLabel(
                defaultIfBlank(signer.getSignerRoleLabel(), "参与方" + (participantNo + 1)));
        participant.setForceReadTime(signer.getForcedReadingTime());
        participant.setForceReadEnd(signer.isForceRead());
        participant.setAttachmentConfigs(buildAttachmentConfigs(signer.getAttachmentConfigs()));
        participant.setAuthWay(signer.getAuthWay());
        participant.setNeedWill(Boolean.TRUE.equals(signer.getRequiredWilling()));
        participant.setNoticeTypes(parseNoticeTypes(signer));
        participant.setAccessToken(signer.getAccessToken());
        participant.setInstances(new ArrayList<>());
        if (OR_SIGN.getType().equals(signer.getSignTaskType())) {
            participant.setParticipantMode(ParticipantModeEnum.OR_SIGN.getMode());
        }

        // 指定印章
        populateAssignedSeal(participant, signFieldMap.get(signer.getSignOrder()));

        // 企业主体情况, 合并签署要求
        if (AuthorizerTypeEnum.isOrgan(signer.getSignerAuthorizedAccountType())) {
            Set<String> signRequirements = IdsUtil.getIdSet(participant.getSignRequirements());
            signRequirements.addAll(BizUtils.parseSignRequirements(signer));
            participant.setSignRequirements(StringUtils.join(signRequirements, ","));
        }


        // 企业经办人特殊处理，这个场景已经没了，老数据的企业经办人当成个人处理
        if (AuthorizerTypeEnum.isOrgan(signer.getSignerAuthorizedAccountType())) {
            // 企业经办人签场景， 强转为个人主体
//            if (SignerRoleEnum.ORGAN_AGENT.getType().equals(signer.getSignerSignRoles()) &&
//                    !SignModeEnum.GLOBAL.getCode().equals(getSignMode(signFlowDetail))) {
//                participant.setParticipantSubjectType(ParticipantSubjectType.PSN.getType());
//            }
            // todo tl-bug 兜底异常逻辑，但是这个异常逻辑是因为前面传参错误造成的
            // 判断签署要求中是否包含经办人， 如果不包含,不存在个人签章场景，移除印章类型属性值
            if (!signer.getSignerSignRoles().contains(SignerRoleEnum.ORGAN_AGENT.getType())) {
                participant.setSealType("");
            }
        }

        return participant;
    }

    /**
     * 填充指定印章信息
     *
     * @param participant 参与方
     * @param signField 签署区信息，主要是用指定印章的信息
     */
    private void populateAssignedSeal(ParticipantBO participant, SignFieldSimpleDTO signField) {
        if (signField == null) {
            return;
        }
        StartSignSpecifySealTypeEnum signSealType;
        String signSeal;

        if (signField.getAssignedSeal()) {
            // 指定具体印章
            signSealType = StartSignSpecifySealTypeEnum.SEAL_ID;
            signSeal = signField.getSealId();
        } else if (StringUtils.isNotBlank(signField.getSealBizTypes())) {
            // 指定印章类型
            signSealType = StartSignSpecifySealTypeEnum.BIZ_TYPE;
            signSeal = signField.getSealBizTypes();
        } else {
            // 不指定
            signSealType = StartSignSpecifySealTypeEnum.NONE;
            signSeal = "";
        }

        participant.setSignSealType(signSealType.getType());
        participant.setSignSeal(signSeal);
    }

    /** 获取每个参与方的任意一个签署区(这里是指定印章时使用，为了获取每个方指定的印章) */
    private Map<Integer, SignFieldSimpleDTO> getOrSignFieldMap(
            List<DocumentForSignWithSignFieldResBean> signDocs,
            List<DocumentForSignResBean> signedDocs) {
        // Map<signOrder, 签署区信息>
        Map<Integer, SignFieldSimpleDTO> signFileMap = new HashMap<>();

        // 获取每个文件的签署区，每个方保留一个签署区(因为指定印章场景一个方所有签署区的印章或印章类型都是一样的，所以任意留一个签署区就行了)
        // 这里想要的实际是每个方的指定印章信息，但是指定印章信息没有封装成对象，所以只能返回整个签署区信息
        signDocs.stream()
                .map(DocumentForSignWithSignFieldResBean::getSignfields)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .forEach(signField -> signFileMap.putIfAbsent(signField.getOrder(), signField));

        signedDocs.stream()
                .map(DocumentForSignResBean::getSignfields)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .forEach(signField -> signFileMap.putIfAbsent(signField.getOrder(), signField));

        return signFileMap;
    }

    /** 构建参与方附件配置信息 */
    private List<AttachmentConfigBO> buildAttachmentConfigs(
            List<SignerDto.AttachmentConfig> attachmentConfigs) {
        if (CollectionUtils.isEmpty(attachmentConfigs)) {
            return Collections.emptyList();
        }

        return attachmentConfigs.stream()
                .map(
                        x ->
                                AttachmentConfigBO.builder()
                                        .name(x.getAttachmentName())
                                        .type(x.isVerify() ? x.getVerificationType() : null)
                                        .checkInfo(x.isCheckSignerInfo())
                                        .multiple(x.isMultiple())
                                        .required(x.isRequired())
                                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 构建参与方实例信息
     *
     * @param signer 签署人信息
     * @param signerAccountMap 签署人账号信息
     * @return
     */
    private ParticipantInstanceBO getParticipantInstance(SignFlowDetailOutput signFlowDetail,
            SignerDto signer, Map<String, UserAccountDetail> signerAccountMap) {
        // 使用了框架的转换 getMapperFacade().map ，逻辑在 public void init() 方法里，比较隐蔽
        ParticipantInstanceBO instance =
                mapperFactory.getMapperFacade().map(signer, ParticipantInstanceBO.class);
        instance.setActualSigner(signer.getExecuted());
        instance.setAccountLicense(signer.getSignerLicense());
        instance.setAccountLicenseType(signer.getSignerLicenseType());
        // 获取签署人账号，补充姓名及手机号、邮箱信息
        UserAccountDetail person = signerAccountMap.get(signer.getSignerAccountId());
        if (null != person && person.isDeleted()) {
            instance.setAccountOid(null);
            instance.setAccountGid(null);
            instance.setAccountUid(null);
        }
        if (null != person && StringUtils.isBlank(instance.getAccountName())) {
            instance.setAccountName(person.getAccountName());
        }
        // 如果用户有最新的登录账号或联系账号，优先以最新数据为准
        if (null != person && StringUtils.isNotBlank(person.obtainAccount())) {
            instance.setAccount(person.obtainAccount());
        }

        // 企业主体
        if (AuthorizerTypeEnum.isOrgan(signer.getSignerAuthorizedAccountType())) {
            // 企业经办人签场景， 强转为个人主体
//            if (SignerRoleEnum.ORGAN_AGENT.getType().equals(signer.getSignerSignRoles()) &&
//                    !SignModeEnum.GLOBAL.getCode().equals(getSignMode(signFlowDetail))) {
//                instance.setSubjectType(SubjectTypeEnum.PERSON.getType());
//                instance.setSubjectId(instance.getAccountOid());
//                instance.setSubjectGid(instance.getAccountGid());
//                instance.setSubjectUid(instance.getAccountUid());
//                instance.setSubjectName(instance.getAccountName());
//            } else if (CollectionUtils.isNotEmpty(signer.getSignerOperators())) {
//                // 如果签署人已经签署完成， 回填时优先使用操作主体账号信息
//                SignerOperator signerOperator = signer.getSignerOperators().get(0);
//                instance.setSubjectId(signerOperator.getOperatorAuthorizerId());
//            }
            if (CollectionUtils.isNotEmpty(signer.getSignerOperators())) {
                // 如果签署人已经签署完成， 回填时优先使用操作主体账号信息
                SignerOperator signerOperator = signer.getSignerOperators().get(0);
                instance.setSubjectId(signerOperator.getOperatorAuthorizerId());
            }
            return instance;
        }

        // 个人主体
        instance.setSubjectId(instance.getAccountOid());
        instance.setSubjectGid(instance.getAccountGid());
        instance.setSubjectUid(instance.getAccountUid());
        instance.setSubjectName(instance.getAccountName());
        // 兜底处理， 补偿姓名及账号
        if (StringUtils.isAnyBlank(instance.getAccount(), instance.getAccountName())
                && CollectionUtils.isNotEmpty(signer.getSignerOperators())) {
            SignerOperator signerOperator = signer.getSignerOperators().get(0);
            UserAccountDetail operator = signerAccountMap.get(signerOperator.getOperatorId());
            if (null != operator && StringUtils.isBlank(instance.getAccountName())) {
                instance.setAccountName(operator.getAccountName());
            }
            if (null != operator && StringUtils.isBlank(instance.getAccount())) {
                instance.setAccount(operator.obtainAccount());
            }
        }

        return instance;
    }
}

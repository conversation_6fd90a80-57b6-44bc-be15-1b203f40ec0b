package com.timevale.contractmanager.core.service.opponent.impl;

import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEnterpriseBasicInfoDetailBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentSimpleInfoListBO;
import com.timevale.contractmanager.core.model.dto.response.opponent.detection.OpponentEnterpriseInfoResponse;
import com.timevale.contractmanager.core.service.opponent.EnterpriseInformationService;
import com.timevale.contractmanager.core.service.opponent.OrganizationQueryService;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: huifeng
 * @since: 2021-12-28 16:19
 **/
@Service
@Slf4j
public class OrganizationQueryServiceImpl implements OrganizationQueryService {
    

    @Autowired
    private EnterpriseInformationService enterpriseInformationService;

    @Override
    public Optional<OpponentEnterpriseInfoResponse> getSimpleInfoByOrgName(String orgName) {
        OpponentSimpleInfoListBO simpleInfoListResult = enterpriseInformationService.enterpriseSimpleInfo(orgName);
        if(!simpleInfoListResult.isSuccess()) {
            return Optional.empty();
        }

        if (CollectionUtils.isEmpty(simpleInfoListResult.getItems())) {
            return Optional.empty();
        }

        List<OpponentEnterpriseBasicInfoDetailBO> target = simpleInfoListResult.getItems().stream().filter(i->i.getName().equals(orgName)).collect(Collectors.toList());
        if (target.isEmpty()) {
            return Optional.empty();
        }

        /**对于个体户重名情况，不提示统一社会信用代码，让用户手动输入 */
        if (target.size()>1) {
            return Optional.empty();
        }

        OpponentEnterpriseInfoResponse response = new OpponentEnterpriseInfoResponse();
        OpponentEnterpriseBasicInfoDetailBO source = target.get(0);
        response.setName(source.getName());
        response.setLegalPersonName(source.getLegalPersonName());
        response.setBusinessStartDate(source.getBusinessStartDate());
        response.setCreditCode(source.getCreditCode());
        return Optional.of(response);
    }

    @Override
    public Optional<OpponentEnterpriseInfoResponse> getSimpleInfoByCreditCode(String socialCreditCode) {
        OpponentSimpleInfoListBO simpleInfoListResult = enterpriseInformationService.enterpriseSimpleInfo(socialCreditCode);
        if(!simpleInfoListResult.isSuccess()) {
            return Optional.empty();
        }

        if (CollectionUtils.isEmpty(simpleInfoListResult.getItems())) {
            return Optional.empty();
        }

        OpponentEnterpriseInfoResponse response = new OpponentEnterpriseInfoResponse();
        OpponentEnterpriseBasicInfoDetailBO source = simpleInfoListResult.getItems().get(0);
        response.setName(source.getName());
        response.setLegalPersonName(source.getLegalPersonName());
        response.setBusinessStartDate(source.getBusinessStartDate());
        response.setCreditCode(source.getCreditCode());
        return Optional.of(response);
    }
}

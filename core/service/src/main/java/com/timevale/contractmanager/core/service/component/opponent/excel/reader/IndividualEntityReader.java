package com.timevale.contractmanager.core.service.component.opponent.excel.reader;

import com.timevale.contractmanager.core.model.bo.opponent.ExcelInfoBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEntityExcelReadBO;

import java.util.LinkedHashMap;

/**
 * 个人相对方实体读取
 *
 * <AUTHOR>
 * @since 2021-02-03 16:32
 */
public class IndividualEntityReader implements OpponentEntityReader {

    /** 表格列数 */
    private final int HEAD_COUNT = 4;

    @Override
    public OpponentEntityExcelReadBO read(LinkedHashMap<Integer, Object> data) {
        OpponentEntityExcelReadBO excelReadBO = new OpponentEntityExcelReadBO();
        ExcelInfoBO excelInfo = new ExcelInfoBO();
        // 读取每列的值
        for (int i = 0; i < HEAD_COUNT; i++) {
            Object object = data.get(i);
            // 非空校验
            if (null == object) {
                // 为空时添加空字符串保证列的完整性
                excelReadBO.getData().add("");
                continue;
            }
            excelReadBO.getData().add(object);
            switch (i) {
                case 0:
                    // 姓名
                    excelInfo.setUserName(object.toString().trim());
                    break;
                case 1:
                    // 手机号/邮箱
                    excelInfo.setEntityUniqueId(object.toString().trim());
                    break;
                case 2:
                    // 所在企业
                    excelInfo.setOrganizationName(object.toString().trim());
                    break;
                case 3:
                    // 备注
                    excelInfo.setDescription(object.toString());
                default:
                    // 不在处理范围内
                    break;
            }
        }
        excelReadBO.setEntity(excelInfo);
        return excelReadBO;
    }
}

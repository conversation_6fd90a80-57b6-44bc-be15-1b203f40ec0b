package com.timevale.contractmanager.core.service.contractprocess.processor.sign;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.*;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.SignChangeTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.ProcessApprovalEntity;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.docSearchService.enums.TaskTypeEnum;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessCooperationTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSignTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/5/24
 */
@Slf4j
@Component
public class SignContractApprovalAgreeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient processWriteClient;
    @Autowired
    private ContractProcessReadClient processQueryClient;
    @Autowired
    private LockService lockService;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.signTopicName(), SignChangeTagEnum.CONTRACT_APPROVAL_AGREE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessApprovalEntity entity =
                JsonUtils.json2pojo(data, ProcessApprovalEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        Lock lock =
                lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                doProcess(collectContext);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }


    private void doProcess(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();

        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);
        ProcessApprovalEntity signChangeMsgEntity = (ProcessApprovalEntity) collectContext.getData();


        boolean hidden = Boolean.TRUE.equals(signChangeMsgEntity.getHidden());


        ContractProcessUpdateParam updateParam = new ContractProcessUpdateParam();
        updateParam.setProcessId(processId);

        if (TaskTypeEnum.WRITE.getType().equals(signChangeMsgEntity.getTaskType())) {

            if (CollectionUtils.isNotEmpty(contractProcessDTO.getCooperationTasks())) {
                List<ContractProcessCooperationTaskParam> updateList =
                        ProcessDataCollectConverter.cooperationTask2ParamList(contractProcessDTO.getCooperationTasks())
                                .stream().peek(item -> {
                                    item.setHidden(hidden);
                                }).collect(Collectors.toList());
                updateParam.setCooperationTasks(updateList);
                updateParam.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_CONTRACT_APPROVAL_AGREE);
                processWriteClient.updateByProcessId(updateParam);
            }
        } else if (TaskTypeEnum.SIGN.getType().equals(signChangeMsgEntity.getTaskType())){

            if (CollectionUtils.isNotEmpty(contractProcessDTO.getSignTasks())) {
                List<ContractProcessSignTaskParam> updateList =
                        ProcessDataCollectConverter.signTaskDTO2ParamList(contractProcessDTO.getSignTasks())
                                .stream().peek(item -> {
                                    item.setHidden(hidden);
                                }).collect(Collectors.toList());
                updateParam.setSignTasks(updateList);
                updateParam.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_CONTRACT_APPROVAL_AGREE);
                processWriteClient.updateByProcessId(updateParam);
            }
        } else {
            log.info(ProcessDataCollectSupport.LOG_PREFIX + " contract approval unknow taskType");
        }

    }
}

package com.timevale.contractmanager.core.service.contractprocess.processor.approval;

import com.google.common.collect.Maps;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.HbaseProcessDataAsyncCollectException;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.approval.bean.ApprovalDoProcessParam;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ProcessApprovalTaskInfoDTO;
import com.timevale.signflow.search.service.model.contractprocess.SealApprovalDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessApprovalTaskInfoParam;
import com.timevale.signflow.search.service.request.datacollect.SealApprovalParam;
import com.timevale.signflow.search.service.request.datacollect.SealApprovalTaskInfoParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ApprovalComponent
 *
 * <AUTHOR>
 * @since 2024-01-13
 */
@Slf4j
@Component
public class ApprovalComponent {

    @Autowired private LockService lockService;
    @Autowired private ProcessDataBuilder processDataBuilder;
    @Autowired private ContractProcessReadClient processQueryClient;
    @Autowired private ContractProcessReadClient contractProcessReadClient;
    @Autowired private ContractProcessWriteClient contractProcessWriteClient;

    /**
     * 处理审批数据
     * @param collectContext
     * @param param
     */
    public void doProcess(ProcessDataCollectContext collectContext, ApprovalDoProcessParam param) {
        // 判断标准平台审批是否已发起， 未发起无需处理
        if (!param.isStandardPlatformStarted()) {
            log.info(ProcessDataCollectSupport.LOG_PREFIX + "standardApproval is false, skip");
            return;
        }
        // 合同审批
        if (ApprovalTypeEnum.CONTRACT.getCode().equalsIgnoreCase(param.getApprovalType())) {
            ContractProcessUpdateParam updateParam =
                    processDataBuilder.buildNewProcessApproval(
                            param.getApprovalCode(), param.getBizGroupId());
            doContractProcess(collectContext, updateParam);
            return;
        }
        // 用印审批
        Lock lock = lockService.getLock(ProcessDataCollectSupport.sealTaskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                SealApprovalParam approvalParam = processDataBuilder.buildNewSealApproval(param.getApprovalCode());
                doSealProcess(collectContext, approvalParam);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }

    /**
     * 处理合同审批
     * @param collectContext
     * @param param
     */
    public void doContractProcess(ProcessDataCollectContext collectContext, ContractProcessUpdateParam param) {
        if (null == param) {
            return;
        }
        String processId = collectContext.getProcessId();
        Map<String, Boolean> taskHiddenMap = new HashMap<>();
        ContractProcessDTO contractProcessDTO = contractProcessReadClient.getByProcessId(processId);
        if (null != contractProcessDTO.getProcessApproval() &&
                CollectionUtils.isNotEmpty(contractProcessDTO.getProcessApproval().getApprovalTaskInfo())) {
            taskHiddenMap = contractProcessDTO.getProcessApproval().getApprovalTaskInfo().stream()
                    .collect(Collectors.toMap(ProcessApprovalTaskInfoDTO::getTaskId,
                            elm -> Boolean.TRUE.equals(elm.getHidden()), (v1, v2) -> v2));
        }

        // 已存在删除的 不能更新没了
        if (MapUtils.isNotEmpty(taskHiddenMap) &&
                null != param.getProcessApproval() &&
                CollectionUtils.isNotEmpty(param.getProcessApproval().getApprovalTaskInfo())) {
            for (ProcessApprovalTaskInfoParam task : param.getProcessApproval().getApprovalTaskInfo()) {
                Boolean hidden;
                if ((hidden = taskHiddenMap.get(task.getTaskId())) != null) {
                    task.setHidden(hidden);
                }
            }
        }

        param.setProcessId(processId);
        param.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_APPROVAL_CHANGE);
        contractProcessWriteClient.updateByProcessId(param);
    }

    /**
     * 处理用印审批
     * @param collectContext
     */
    public void doSealProcess(ProcessDataCollectContext collectContext, SealApprovalParam sealApproval) {
        String processId = collectContext.getProcessId();
        if (sealApproval == null) {
            log.info(ProcessDataCollectSupport.LOG_PREFIX + "sealApproval is null skip, msg:{}", JsonUtils.obj2json(collectContext.getData()));
            return;
        }
        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);


        //  准备回填已删除的， 防止已删除人数据更新丢失
        Map<String, Map<String, List<String>>> approvalIdTaskIdHiddenPersonMap =
                buildApprovalIdTaskIdHiddenPersonMap(contractProcessDTO);

        List<SealApprovalDTO> sealApprovalDTOList = contractProcessDTO.getSealApproval();
        List<SealApprovalParam> sealApprovalParamList = ProcessDataCollectConverter.sealApprovalDTO2ParamList(sealApprovalDTOList);
        boolean exist = false;
        for (int i = 0; i < sealApprovalParamList.size(); i++) {
            SealApprovalParam param = sealApprovalParamList.get(i);
            if (StringUtils.isNotEmpty(param.getApprovalId()) && param.getApprovalId().equals(sealApproval.getApprovalId())) {
                sealApprovalParamList.set(i, sealApproval);
                exist = true;
                // 回填已删除的
                Map<String, List<String>> taskIdHiddenPersonGidMap = approvalIdTaskIdHiddenPersonMap.get(param.getApprovalId());
                if (MapUtils.isNotEmpty(taskIdHiddenPersonGidMap) &&
                        CollectionUtils.isNotEmpty(sealApproval.getApprovalTaskInfo())) {
                    for (SealApprovalTaskInfoParam task : sealApproval.getApprovalTaskInfo()) {
                        task.setHiddenPersonGid(taskIdHiddenPersonGidMap.get(task.getTaskId()));
                    }
                }
            }
        }

        if (!exist) {
            sealApprovalParamList.add(sealApproval);
        }
        ContractProcessUpdateParam param = new ContractProcessUpdateParam();
        param.setProcessId(processId);
        param.setSealApproval(sealApprovalParamList);
        param.setBizScene(ProcessDataCollectBizSceneConstants.SEAL_APPROVAL_CHANGE);
        contractProcessWriteClient.updateByProcessId(param);
    }

    /**
     * 获取审批流程中任务已删除人信息列表
     * @param contractProcessDTO
     * @return
     */
    private Map<String, Map<String, List<String>>> buildApprovalIdTaskIdHiddenPersonMap(ContractProcessDTO contractProcessDTO) {
        if (CollectionUtils.isEmpty(contractProcessDTO.getSealApproval())) {
            return Maps.newHashMap();
        }
        Map<String, Map<String, List<String>>> approvalIdTaskIdHiddenPersonMap = new HashMap<>();
        contractProcessDTO.getSealApproval().stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getApprovalTaskInfo()))
                .forEach(i ->
                        i.getApprovalTaskInfo().stream()
                            .filter(task -> CollectionUtils.isNotEmpty(task.getHiddenPersonGid()))
                            .forEach(task -> approvalIdTaskIdHiddenPersonMap.computeIfAbsent(i.getApprovalId(), key -> new HashMap<>())
                                    .put(task.getTaskId(), task.getHiddenPersonGid())));
        return approvalIdTaskIdHiddenPersonMap;
    }
}

package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;

/**
 * @Author:jiany<PERSON>
 * @since 2022-02-24 14:13
 */
@Data
public class MoveMenuSensorBean extends BaseAttributeSensorBean {
	private String folderName;
	private Long numberOfProcess;
	private long returnTime;
	private String processingResult;

	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData = super.sensorData();
		sensorData.put(FOLDER_NAME, sensorString(folderName));
		sensorData.put(NUMBER_OF_PROCESS, sensorString(numberOfProcess));
		sensorData.put(RETURN_TIME, sensorString(returnTime));
		sensorData.put(PROCESSING_RESULT, sensorString(processingResult));
		return sensorData;
	}
}

package com.timevale.contractmanager.core.service.resourceshare.actions;

import com.timevale.saas.common.manage.spi.extender.DoGetResourceShareActionPluginExtender;
import com.timevale.saas.common.manage.spi.extender.DoGetResourceUrlActionPluginExtender;
import com.timevale.saas.common.manage.spi.extender.DoProcessParticipantAuthPluginExtender;
import com.timevale.saas.common.manage.spi.extender.DoResourceShareActionPluginExtender;

/**
 * 当前服务对于资源分享的插件的实现
 *
 * <AUTHOR>
 * @since 2020/12/8
 */
public interface DoResourceShareActionService
        extends DoResourceShareActionPluginExtender,
                DoGetResourceShareActionPluginExtender,
                DoGetResourceUrlActionPluginExtender,
                DoProcessParticipantAuthPluginExtender {

    String getResourceType();
}

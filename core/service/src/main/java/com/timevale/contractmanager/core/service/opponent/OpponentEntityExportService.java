package com.timevale.contractmanager.core.service.opponent;

import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentExportRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentExportResponse;
import com.timevale.contractmanager.core.service.mq.model.opponent.OpponentExportMsg;

/**
 * OpponentEntityExportService
 *
 * <AUTHOR>
 * @since 2021/8/11 5:30 下午
 */
public interface OpponentEntityExportService {

    /**
     * 相对方导出
     *
     * @param request request
     * @return taskId
     */
    OpponentExportResponse createExportTask(OpponentExportRequest request, String tenantOid, String operatorOid);

    /**
     * 执行导出任务
     *
     * @param msg
     */
    void doExportTask(OpponentExportMsg msg) throws Exception;
}

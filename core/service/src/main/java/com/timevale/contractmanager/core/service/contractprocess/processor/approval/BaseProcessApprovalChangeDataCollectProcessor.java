package com.timevale.contractmanager.core.service.contractprocess.processor.approval;

import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.mq.model.ProcessApprovalChangeMsgEntity;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ProcessApprovalDataCollectProcessor
 *
 * <AUTHOR>
 * @since 2022/8/9 2:36 下午
 */
@Component
public abstract class BaseProcessApprovalChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ApprovalComponent approvalComponent;

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessApprovalChangeMsgEntity entity =
                JsonUtils.json2pojo(data, ProcessApprovalChangeMsgEntity.class);
        //消息体内拿不到processId, 通过别的渠道拿processId
        String processId = processDataBuilder.getProcessByApprovalId(entity.getApprovalId());
        return new DataAnalysisResult(processId, entity);
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        ProcessApprovalChangeMsgEntity entity = (ProcessApprovalChangeMsgEntity) collectContext.getData();
        ContractProcessUpdateParam param = buildUpdateParam(entity);
        approvalComponent.doContractProcess(collectContext, param);
    }

    public abstract ContractProcessUpdateParam buildUpdateParam(ProcessApprovalChangeMsgEntity entity);
}

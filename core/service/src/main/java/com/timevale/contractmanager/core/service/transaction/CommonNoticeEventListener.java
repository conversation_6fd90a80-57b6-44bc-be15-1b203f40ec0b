package com.timevale.contractmanager.core.service.transaction;

import com.timevale.contractmanager.core.service.mq.producer.ProcessContractCategoryProducer;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
public class CommonNoticeEventListener {

    @Autowired ProcessContractCategoryProducer processContractCategoryProducer;

    @TransactionalEventListener(fallbackExecution = true, phase = TransactionPhase.AFTER_COMMIT)
    public void handleCommonNoticeEvent(CommonNoticeEvent event) {
        if (null == event.getNoticeType() || StringUtils.isBlank(event.getMsgBody())) {
            return;
        }
        switch (event.getNoticeType()) {
            case PROCESS_FILE_CONTRACT_CATEGORY_CHANGE:
                processContractCategoryProducer.sendMessage(event.getMsgBody());
                break;
            default:
                break;
        }
    }
}

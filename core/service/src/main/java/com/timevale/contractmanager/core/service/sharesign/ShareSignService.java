package com.timevale.contractmanager.core.service.sharesign;

import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.core.model.dto.request.sharesign.*;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResponse;
import com.timevale.contractmanager.core.model.dto.response.sharesign.*;

/**
 * <AUTHOR>
 * @since 2021-02-03
 */
public interface ShareSignService {

    /**
     * 获取扫码签详情信息
     *
     * @param shareSignTaskId 扫码签id
     * @param token           用户授权token APP4.0后必传
     * @param withProgress
     * @return 扫码签任务信息
     */
    ShareSignTaskInfoResponse getTaskInfo(String shareSignTaskId, String token, Boolean withProgress);

    ShareSignScanInfoResponse getTaskInfoByUrlId(String shareSignTaskUrlId);

    ShareSignTaskUrlResponse getTaskUrl(String shareSignTaskId, String participantId, String accountId);

    ShareSignTaskListResponse getTaskList(ShareSignTaskListRequest request);

    /**
     * 获取扫码签任务流程列表
     *
     * @return
     */
    ShareSignProcessListResponse getProcessList(QueryTaskProcessListRequest request);

    void updateTaskStatus(ShareSignTaskStatusRequest request);

    /**
     * 删除扫码签任务
     */
    void deleteTask(DeleteShareSignTaskRequest request);

    /**
     * 更新任务信息
     *
     * @param request   任务信息
     * @param accountId 当前操作人oid
     * @param subjectId 当前操作企业
     */
    void updateTaskInfo(ShareSignTaskInfoRequest request, String accountId, String subjectId);

    /**
     * 发起扫码签
     */
    ShareSignTaskUrlResponse startTask(ShareSignTaskStartRequest request);

    /**
     * 参与扫码签
     */
    ProcessStartResponse startProcess(ShareSignProcessStartRequest request);

    ShareSignTaskManageUrlResponse getTaskManageUrl(ShareSignTaskManageUrlRequest request);

    /**
     * 结束扫码流程发起
     *
     * @param processId
     * @param success
     */
    void completeShareSignProcessInitiate(String processId, boolean success);

    /**
     * 处理扫码任务
     *
     * @param process
     */
    void handleShareSignTask(ProcessDO process);

  /**
   * 是否支持扫码签
   *
   * @param flowTemplateId 合同模板id
   * @return 是否支持扫码签 true or false
   */
  ShareSignSupportScanResponse supportScan(String flowTemplateId);

    /**
     * 获取进行中的任务数量
     * @return 数量
     */
    ShareSignInProcessResponse inProcessNum();

    ShareSignTaskCountResponse countTask(String accountOid, String subjectOid);

    /**
     * 根据模板信息开始任务
     * @param request 请求参数
     * @return 任务开时后响应值
     */
    ShareSignTaskUrlResponse startTaskByFlowTemplate(ShareSignTaskStartFlowTemplateRequest request);


    /**
     * 扫码任务中列表信息
     * @param request
     * @return 扫码签列表
     */
    ShareSignTaskListResponse inProcessList(ShareSignTaskInProcessListRequest request);

    /**
     * 扫码详情页计数数据
     * @param shareSignTaskId 任务id
     * @param subjectId 空间主体id
     * @return 计数详情只
     */
    ShareSignTaskDetailCountResponse taskDetailCount(String shareSignTaskId,String subjectId);

    /**
     * 查询用户在任务的加入信息
     * @param taskId
     * @param accountId
     * @param subjectName
     * @return
     */
    ShareSignTaskJoinInfoResponse taskJoinInfo(String taskId, String accountId, String subjectName);

    /**
     * 查询用户已扫码加入的流程
     *
     * @param request 扫码签所需参数
     * @return 用户已扫码的前一个流程
     */
    ProcessStartResponse queryJoinedTask(QueryJoinProcessRequest request);
}

package com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.impl;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.contractmanager.common.service.enums.opponent.AuthorizeTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionProblemEnum;
import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.DetectionHandler;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Author:jianyang
 * @since 2021-08-17 11:34
 */
@Component
@Slf4j
public class RealNameStatusDetectionHandler implements DetectionHandler {

	@Autowired
	private SensorService sensorService;

	public static final Integer DETECTION_NO = 1;

	public static final String DESC = "该企业未完成在e签宝的实名认证";

	/**
	 * 实名检测
	 * @param chainBO
	 * @param chainResults
	 * @return
	 */
	@Override
	public List<OpponentDetectionChainResultBO> handler(DetectionChainBO chainBO,List<OpponentDetectionChainResultBO> chainResults) {
		log.info("开始相对方检测(实名检测) taskId:{} taskType:{} tenantGid:{} orgName:{}",
				chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
		if(Objects.equals(AuthorizeTypeEnum.INIT.getType(),chainBO.getEntity().getAuthorizeType())
				|| Objects.equals(AuthorizeTypeEnum.AUTHENTICATING.getType(),chainBO.getEntity().getAuthorizeType())){
			OpponentDetectionChainResultBO chainResultBO = new OpponentDetectionChainResultBO();
			chainResultBO.setOrgName(chainResultBO.getOrgName());
			chainResultBO.setProblemDesc(DESC);
			chainResultBO.setRiskLevel(OpponentDetectionProblemEnum.INIT.getProblemRiskLevel());
			chainResultBO.setProblemNo(OpponentDetectionProblemEnum.INIT.getProblemNo());
			chainResultBO.setProblemNo(DETECTION_NO);
			chainResultBO.setSuggestDesc(OpponentDetectionProblemEnum.INIT.getSuggestDesc());
			chainResults.add(chainResultBO);
			log.info("相对方检测企业未实名 taskId:{} taskType:{} tenantGid:{} orgName:{}",
					chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
			sensorService.opponentDetectionProblemReport(
					chainBO.getTenantGid(), chainBO.getOrgName(),
					OpponentDetectionProblemEnum.INIT.getProblemNo());
		}
		return chainResults;
	}

	@Override
	public boolean filter(List<Integer> items) {
		if(items.contains(DETECTION_NO)){
			return true;
		}
		return false;
	}
}

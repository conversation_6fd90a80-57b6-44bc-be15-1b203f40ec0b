package com.timevale.contractmanager.core.service.grouping;

import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.doccooperation.service.result.FlowTemplateStructResult;

import java.util.List;
import java.util.Set;

/**
 * 模板服务
 *
 * <AUTHOR>
 * @since 2020-09-02 16:27
 **/
public interface TemplateService {

    /**
     * 获取模板文件中的指定类型控件
     *
     * @param tenantAccount 企业信息
     * @param appId appId
     * @param flowTemplateId 模板id
     * @param selectedStructTypeSet 控件id，传空则查所有控件 {@link
     *     com.timevale.docmanager.service.enums.StructComponentTypeEnum}
     * @return 流程模板对象
     */
    FlowTemplateStructResult getSelectedStructList(
            UserAccount tenantAccount,
            String appId,
            String flowTemplateId,
            Set<Integer> selectedStructTypeSet);

    /**
     * 判断参与方签署方式是否使用AI手绘
     *
     * @param participants 参与方
     * @return 是否使用
     */
    boolean isAIDrawUsed(List<ParticipantBO> participants);

    /**
     * 获取模板文件中是否使用高级控件(多行文本、选择、图片)
     *
     * @param tenantAccount  企业信息
     * @param appId          appId
     * @param flowTemplateId 模板id
     * @param clientId
     * @return 是否使用
     */
    boolean isAdvancedComponentUsed(UserAccount tenantAccount,
                                    String appId,
                                    String flowTemplateId,
                                    String clientId);
}

package com.timevale.contractmanager.core.service.dedicatedcloud.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.enums.SourceEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.AuthRelationClient;
import com.timevale.contractmanager.common.service.integration.client.DedicatedCloudClient;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.core.model.dto.request.QueryPreferenceRequest;
import com.timevale.contractmanager.core.model.dto.response.PreferenceResponse;
import com.timevale.contractmanager.core.model.enums.CloudTypeEnum;
import com.timevale.contractmanager.core.service.common.LimitOperateService;
import com.timevale.contractmanager.core.service.dedicatedcloud.DedicatedCloudService;
import com.timevale.contractmanager.core.service.enums.YesNoEnum;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.constant.AuthRelationShareConfigKeyConstant;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.GetAuthRelationShareConfigOutput;
import com.timevale.saas.integration.service.model.output.model.QueryDedicatedProjectOutput;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/2/5 14:11
 */
@Service
public class DedicatedCloudServiceImpl implements DedicatedCloudService {

    private static final String CAN_OPERATE_CLIENT = "dedicatedCloud.canOperateClient";

    @Autowired
    private DedicatedCloudClient dedicatedCloudClient;
    @Autowired
    private AuthRelationClient authRelationClient;
    @Autowired
    private PreferencesService preferencesService;
    @Autowired
    private LimitOperateService limitOperateService;


    @Override
    public QueryDedicatedProjectOutput availableDedicatedProject(String subjectGid) {

        QueryDedicatedProjectOutput dedicatedProject =
                dedicatedCloudClient.getOnlyOneDedicatedProject(subjectGid);
        if (dedicatedProject != null && YesNoEnum.Y.code() == dedicatedProject.getStatus()) {
            return dedicatedProject;
        }
        // 没有有效的关联企业
        GetAuthRelationShareConfigOutput getAuthRelationShareConfigOutput =
                authRelationClient.getAuthRelationShareConfig(subjectGid, AuthRelationShareConfigKeyConstant.SHARE_DEDICATED_CLOUD);
        if (!getAuthRelationShareConfigOutput.getEffective()) {
            return null;
        }

        // 授权了共享专属云
        QueryDedicatedProjectOutput parentDedicatedProject =
                dedicatedCloudClient.getOnlyOneDedicatedProject(getAuthRelationShareConfigOutput.getParentTenantGid());
        if (parentDedicatedProject != null && YesNoEnum.Y.code() == parentDedicatedProject.getStatus()) {
            return parentDedicatedProject;
        }
        return null;
    }

    @Override
    public boolean canUseDedicatedProject(String subjectGid, String dedicatedCloudId) {
        QueryDedicatedProjectOutput dedicatedProject = availableDedicatedProject(subjectGid);
        return dedicatedProject != null && Objects.equals(dedicatedProject.getDedicatedCloudId(), dedicatedCloudId);
    }

    @Override
    public boolean canUseDedicatedCloudByDataAndPreference(String subjectOid, String subjectGid, String dedicatedCloudId) {
        if (StringUtils.isBlank(dedicatedCloudId)) {
            return false;
        }
        List<String> dedicateCloudTypes = preferenceDedicatedType(subjectOid, subjectGid);
        if (!dedicateCloudTypes.contains(CloudTypeEnum.DEDICATED_CLOUD.getCode())) {
            return false;
        }
        return canUseDedicatedProject(subjectGid, dedicatedCloudId);
    }

    @Override
    public void limitClientOperate(String client, String appName) {
        if (limitOperateService.canOperate(CAN_OPERATE_CLIENT, client, appName)) {
            return;
        }
        throw new BizContractManagerException(BizContractManagerResultCodeEnum.SERVICE_BIZ_ERROR.getCode(),
                "专属云不支持在该端操作");
    }




    @Override
    public List<String> preferenceDedicatedType(String subjectOid, String subjectGid) {
        QueryPreferenceRequest preferenceRequest = new QueryPreferenceRequest();
        preferenceRequest.setOrgId(subjectOid);
//        preferenceRequest.setOrgGid(subjectGid);
        preferenceRequest.setPreferenceKeys(Arrays.asList(ProcessPreferenceEnum.DEDICATED_CLOUD.getKey()));
        PreferenceResponse preferenceResponse = preferencesService.queryByCondition(preferenceRequest);
        if (null == preferenceResponse || CollectionUtils.isEmpty(preferenceResponse.getPreferences())) {
            return null;
        }
        return IdsUtil.getIdList(preferenceResponse.getPreferences().get(0).getPreferenceValue());
    }
}

package com.timevale.contractmanager.core.service.contractprocess.processor.approval;

import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.HbaseProcessDataAsyncCollectException;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.SealApprovalChangeMsgEntity;
import com.timevale.mandarin.base.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * SealApprovalFillDataCollectProcessor
 *
 * <AUTHOR>
 * @since 2022/8/11 2:36 下午
 */
@Component
public class SealApprovalFillDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;

    @Autowired
    private SealApprovalComponent sealApprovalComponent;

    @Autowired
    private LockService lockService;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.getSealApprovalFillTopicName(), null);
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        SealApprovalChangeMsgEntity entity =
                JsonUtils.json2pojo(data, SealApprovalChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getBizId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return false;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        Lock lock = lockService.getLock(ProcessDataCollectSupport.sealTaskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                sealApprovalComponent.doProcess(collectContext);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }
}

package com.timevale.contractmanager.core.service.opponent;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.service.enums.opponent.AuthorizeTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.OpponentSearchClient;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.bean.v2.OpponentEntityInfo;
import com.timevale.signflow.search.service.request.opponent.OpponentFixDataQueryParam;
import com.timevale.signflow.search.service.result.v2.OpponentEntityQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;

/**
 * Created by tianlei on 2022/8/8
 */
@Slf4j
@Service
public class OpponentDevelopService {

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";


    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private OpponentConfigCenter configCenter;
    @Autowired
    private OpponentSearchClient opponentSearchClient;
    @Autowired
    private OpponentEntityDAO opponentEntityDAO;

    // 修复某个主体的数据
    public void fixData(String tenantGid,
                        Integer entityType,
                        Integer size,
                        String minCreateTimeStr,
                        String maxCreateTimeStr) {
        AssertX.isTrue(StringUtils.isNotBlank(minCreateTimeStr), "minCreateTimeStr 必传");
        AssertX.isTrue(StringUtils.isNotBlank(maxCreateTimeStr), "maxCreateTimeStr 必传");
        long start = System.currentTimeMillis();
        log.info(configCenter.logPrefix() + "start fix data: {} entityType: {} size:{} " +
                "minCreateTimeStr : {} maxCreateTimeStr : {}", tenantGid, entityType, size, minCreateTimeStr, maxCreateTimeStr);
        Date minCreateTime = null;
        Date maxCreateTime = null;
        try {
            minCreateTime = DateUtils.parseDate(minCreateTimeStr, DATE_FORMAT);
            maxCreateTime = DateUtils.parseDate(maxCreateTimeStr, DATE_FORMAT);
        } catch (ParseException pe) {
            throw new BaseBizRuntimeException(BizContractManagerResultCodeEnum.SERVICE_BIZ_ERROR.getCode(), "时间解析错误");
        }

        OpponentFixDataQueryParam param = new OpponentFixDataQueryParam();
        param.setUseScroll(true);
        param.setEntityType(entityType);
        param.setTenantGid(tenantGid);
        param.setSize(size);
        param.setMinCreateTime(minCreateTime.getTime());
        param.setMaxCreateTime(maxCreateTime.getTime());

        for (int i = 1;;i++) {
            OpponentEntityQueryResult result = opponentSearchClient.opponentFixData(param);
            if (null == result || CollectionUtils.isEmpty(result.getList())) {
                break;
            }
            log.info(configCenter.logPrefix() + "query data index : {} lastCreate: {}", i,
                    result.getList().get(result.getList().size() - 1).getCreateTime());
            result.getList().forEach(elm -> {
                try {
                    this.fixOneData(elm);
                }catch (BizContractManagerException e){
                    if (!e.getCode().equals(BizContractManagerResultCodeEnum.USER_ACCOUNT_NOT_EXIST.getCode())){
                        log.info(configCenter.logPrefix() + "fix data failure tenantOid : {}  ", elm.getTenantOid(), e);
                    }
                } catch (Exception e) {
                    log.info(configCenter.logPrefix() + "fix data failure tenantOid : {}  ", elm.getTenantOid(), e);
                }
            });
            param.setScrollId(result.getScrollId());
        }

        log.info(configCenter.logPrefix() + "finish fix data : {} entityType: {} size:{} " +
                "minCreateTimeStr : {} maxCreateTimeStr : {}, 耗时: {}m", tenantGid, entityType, size, minCreateTimeStr,
                maxCreateTimeStr, (System.currentTimeMillis() - start)/1000/60);
    }


    private void fixOneData(OpponentEntityInfo opponentEntity) {
        String oid = opponentEntity.getEntityOid();
        if (StringUtils.isBlank(oid)) {
            return;
        }
        UserAccount account = userCenterService.getUserAccountDetailByOid(oid);
        if (null != account &&
                StringUtils.isNotBlank(account.getAccountGid()) &&
                AuthorizeTypeEnum.INIT.getType() == opponentEntity.getAuthorizeType()) {
            OpponentEntityDO updateData = new OpponentEntityDO();
            updateData.setId(opponentEntity.getId());
            updateData.setEntityGid(account.getAccountGid());
            updateData.setAuthorizeType(AuthorizeTypeEnum.SUPPOSE_ACCEPT.getType());
            log.info(configCenter.logPrefix() + "fix data tenantOid : {}  update: {}",
                    opponentEntity.getTenantOid(), JSON.toJSONString(updateData));
            opponentEntityDAO.updateById(updateData);
        }
    }

}

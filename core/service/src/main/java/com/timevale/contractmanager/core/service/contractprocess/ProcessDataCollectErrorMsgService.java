//package com.timevale.contractmanager.core.service.contractprocess;
//
//import com.google.common.collect.Lists;
//import com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO;
//import com.timevale.contractmanager.common.dal.dao.ProcessDataCollectErrorMsgDAO;
//import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
//import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
//import com.timevale.mandarin.base.util.CollectionUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * Created by tianlei on 2022/6/23
// */
//@Slf4j
//@Component
//public class ProcessDataCollectErrorMsgService {
//
//    private static final List<String> SKIP_ERROR_CODE =
//            Lists.newArrayList(BizContractManagerResultCodeEnum.USER_ACCOUNT_NOT_EXIST.getCode());
//
//    @Autowired
//    private ProcessDataCollector processDataCollector;
//    @Autowired
//    private ProcessDataCollectErrorMsgDAO processDataCollectErrorMsgDAO;
//
//    public void start() {
//        Long minId = null;
//        int size = 100;
//        for (int i = 0; i < 100; i++) {
//
//            List<ProcessDataCollectErrorMsgDO> list = processDataCollectErrorMsgDAO.listSearchAfter(minId, size);
//            if (CollectionUtils.isEmpty(list)) {
//                break;
//            }
//
//            for (ProcessDataCollectErrorMsgDO errorMsg : list) {
//                // 处理一条数据
//                if (processOne(errorMsg)) {
//                    // todo 删除消息
//                }
//            }
//            minId = list.get(list.size() - 1).getId();
//        }
//    }
//
//    private boolean processOne(ProcessDataCollectErrorMsgDO errorMsg) {
//
//        try {
//            processDataCollector.retryCollect(errorMsg);
//            return true;
//        } catch (BizContractManagerException bizE) {
//            if (SKIP_ERROR_CODE.contains(bizE)) {
//                return true;
//            } else {
//                // todo log
//                return false;
//            }
//        } catch (Exception e) {
//            // todo log
//            return false;
//        }
//    }
//}

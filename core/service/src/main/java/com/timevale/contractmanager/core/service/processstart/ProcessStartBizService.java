package com.timevale.contractmanager.core.service.processstart;

import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContext;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-08-20
 */
public interface ProcessStartBizService {

    /**
     * 发起模式， 直接发起或者模版发起
     *
     * @return
     */
    ProcessStartScene startScene();

    /**
     * 可支持的发起类型
     *
     * @return
     */
    List<ProcessStartType> supportStartTypes();
    /**
     * 发起流程
     *
     * @param startRequest
     * @return
     */
    ProcessStartResult start(ProcessStartContext processStartContext, ProcessStartBizRequest startRequest);
}

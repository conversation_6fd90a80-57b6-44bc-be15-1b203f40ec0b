package com.timevale.contractmanager.core.service.component.opponent.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.FileSystemClient;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEntityExcelBO;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.OPPONENT_PARSE_BATCH_EXCEL_FAIL;

/**
 * 相对方表格解析小助手
 *
 * <AUTHOR>
 * @since 2021-02-02 11:12
 */
@Slf4j
@Component
public class OpponentEntityExcelHelper {

    /** 表头行数 */
    private final int HEAD_ROW_NUMBER = 1;

    private static final String EXTENSION_NAME = ".xlsx";

    @Autowired private FileSystemClient fileSystemClient;

    /**
     * 读取批量表格
     *
     * @param entityType 表格类型
     * @param fileKey 表格fileKey
     * @return 读取结果
     */
    public OpponentEntityExcelBO readExcel(String fileKey, OpponentEntityTypeEnum entityType) {
        byte[] bytes = fileSystemClient.downloadFile(fileKey);
        try {
            // 读取excel
            ExcelReaderSheetBuilder builder =
                    EasyExcelFactory.read(new ByteArrayInputStream(bytes)).sheet();
            // 监听解析表格事件获取表格数据
            ReadExcelAnalysisEventListener listener =
                    new ReadExcelAnalysisEventListener(entityType);
            builder.registerReadListener(listener);
            // 设置表头行数
            builder.headRowNumber(HEAD_ROW_NUMBER).doRead();

            OpponentEntityExcelBO opponentEntityExcelBO = new OpponentEntityExcelBO();
            opponentEntityExcelBO.setData(listener.getDataList());
            opponentEntityExcelBO.setList(listener.getList());
            return opponentEntityExcelBO;
        } catch (Exception e) {
            log.warn("readExcel error", e);
            throw new BizContractManagerException(OPPONENT_PARSE_BATCH_EXCEL_FAIL, "解析异常，请尝试下载最新导入模板重试");
        }
    }

    /**
     * 构建错误信息表格
     *
     * @param headers 表格头
     * @param errorMap 解析错误行数及错误信息
     * @param data 表格数据信息
     * @param fileName 表格名称
     * @return 表格下载地址
     */
    public String buildErrorExcel(
            List<List<String>> headers,
            Map<Integer, String> errorMap,
            List<List<Object>> data,
            String fileName) {
        try {
            // 创建个临时文件,上传完后删除掉
            File tempFile =
                    File.createTempFile(
                            "batch_" + RequestContextExtUtils.getTenantId(), EXTENSION_NAME);
            // 写入表格数据
            EasyExcel.write(tempFile)
                    .head(headers)
                    .sheet("Sheet1")
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .doWrite(buildErrorData(errorMap, data));
            // 上传文件
            byte[] bytes = FileUtils.readFileToByteArray(tempFile);
            // 返回文件下载地址
            return fileSystemClient.uploadFile(bytes, fileName + EXTENSION_NAME);
        } catch (IOException e) {
            log.error("buildErrorExcel error", e);
            throw new BizContractManagerException(OPPONENT_PARSE_BATCH_EXCEL_FAIL, "解析异常");
        }
    }

    /**
     * 获取失败原因及失败数据
     *
     * @param errorMap 失败数据及原因
     * @param data 表格数据
     * @return 失败数据
     */
    private List<List<Object>> buildErrorData(
            Map<Integer, String> errorMap, List<List<Object>> data) {
        List<List<Object>> dataList = new ArrayList<>();
        Set<Map.Entry<Integer, String>> entrySet = errorMap.entrySet();
        for (Map.Entry<Integer, String> entry : entrySet) {
            // 获取失败的数据
            List<Object> errorData = data.get(entry.getKey());
            // 添加失败原因
            errorData.add(entry.getValue());
            // 添加至需要导出的数据中
            dataList.add(errorData);
        }
        return dataList;
    }
}

package com.timevale.contractmanager.core.service.tracking;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.constant.SystemConstant;
import com.timevale.contractmanager.core.service.tracking.bean.ProcessStartSensorBean;
import com.timevale.contractmanager.core.service.tracking.enums.TrackingEventEnum;
import com.timevale.saas.tracking.mq.mapper.TrackingKeyDataTypeMapper;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局埋点事件及埋点数据类型的映射关系
 *
 * <AUTHOR>
 * @since 2022-12-09
 */
@Component
public class GlobalTrackingKeyDataTypeMapper implements TrackingKeyDataTypeMapper {
    @Override
    public Map<String, Class> buildTrackingKeyDataTypeMap() {
        Map<String, Class> dataTypeMap = new HashMap();
        dataTypeMap.put(SystemConstant.TRACKING_KEY_AI_MARK, Integer.class);
        Map<String, Class> collect = Lists.newArrayList(TrackingEventEnum.values()).stream()
                .collect(Collectors.toMap(TrackingEventEnum::getKey, TrackingEventEnum::getBeanType));
        dataTypeMap.putAll(collect);
        return dataTypeMap;
    }
}

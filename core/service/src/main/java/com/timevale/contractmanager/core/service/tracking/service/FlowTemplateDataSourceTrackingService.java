package com.timevale.contractmanager.core.service.tracking.service;

import com.timevale.contractmanager.core.service.tracking.consts.TrackingKeyConstant;
import com.timevale.contractmanager.core.service.tracking.enums.TrackingEventEnum;
import com.timevale.contractmanager.core.service.tracking.enums.TrackingFieldEnum;
import com.timevale.contractmanager.core.service.tracking.service.base.BaseTrackingService;
import com.timevale.saas.tracking.bean.TrackingCollectBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/9/13 15:16
 */
@Slf4j
@Component
public class FlowTemplateDataSourceTrackingService extends BaseTrackingService {

    // 模版设置数据源自动发起成功
    private static final String FLOW_TEMPLATE_DATA_SOURCE_AUTO_START_SUCCESS = "moban_zidongfaqi";

    // 模版设置数据源自动发起失败
    private static final String FLOW_TEMPLATE_DATA_SOURCE_AUTO_START_FAILURE = "moban_zidongfaqi_error";

    // 模版设置数据源自手动发起成功
    private static final String FLOW_TEMPLATE_DATA_SOURCE_START_SUCCESS = "daiban_daiwofaqi";

    @Override
    public String distinctId() {
        return "";
    }

    @Override
    public List<TrackingFieldEnum> trackingFields() {
        return Collections.emptyList();
    }

    @Override
    public Map<String, Object> buildTrackingFieldData(TrackingCollectBean trackingBean) {
        return Collections.emptyMap();
    }

    //自动发起结果
    public void autoStartResult(String subjectOid,
                                int successCount,
                                int failureCount) {
        try {
            doAutoStartResult(subjectOid, successCount, failureCount);
        } catch (Exception e) {
            log.warn("flowTemplate dataSource auto start", e);
        }
    }

    public void doAutoStartResult(String subjectOid,
                                  int successCount,
                                  int failureCount) {
        TrackingCollectBean trackingBean = new TrackingCollectBean();
        trackingBean.setTenantId(subjectOid);
        initBaseTrackingData(trackingBean);
        Map<String, Object> data = trackingFieldData();
        if (successCount != 0) {
            doTracking(subjectOid, FLOW_TEMPLATE_DATA_SOURCE_AUTO_START_SUCCESS, data);
        }
        if (failureCount != 0) {
            doTracking(subjectOid, FLOW_TEMPLATE_DATA_SOURCE_AUTO_START_FAILURE, data);
        }
    }


    public void startSuccess(String subjectOid) {
        try {
            doStartSuccess(subjectOid);
        } catch (Exception e) {
            log.warn("flowTemplate dataSource start", e);
        }
    }

    private void doStartSuccess(String subjectOid) {
        TrackingCollectBean trackingBean = new TrackingCollectBean();
        trackingBean.setTenantId(subjectOid);
        initBaseTrackingData(trackingBean);
        Map<String, Object> data = trackingFieldData();
        doTracking(subjectOid, FLOW_TEMPLATE_DATA_SOURCE_START_SUCCESS, data);
    }


}

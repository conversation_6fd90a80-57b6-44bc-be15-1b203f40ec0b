package com.timevale.contractmanager.core.service.relationcontract;

import com.timevale.contractmanager.core.model.dto.request.relationcontract.RelationContractChooseProcessRequest;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractChooseProcessVO;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractProcessVO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * Created by tianlei on 2022/7/5
 */
public interface RelationContractService {

    // 关联合同选择列表
    Pair<List<RelationContractChooseProcessVO>, Long> chooseProcessList(RelationContractChooseProcessRequest request);

    List<RelationContractProcessVO> list(String tenantOid,
                                         String operatorOid,
                                         String processId,
                                         Integer entranceType,
                                         String menuId);

    List<RelationContractProcessVO> processInfo(String tenantOid, List<String> processIds);


    Integer relationCount(String tenantOid, String processId);

}

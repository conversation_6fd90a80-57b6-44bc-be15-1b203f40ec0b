package com.timevale.contractmanager.core.service.component;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.ProcessConfigDO;
import com.timevale.contractmanager.common.dal.bean.ProcessCustomizeConfigDO;
import com.timevale.contractmanager.common.service.bean.ProcessConfigBean;
import com.timevale.contractmanager.common.service.bean.ProcessCustomizeConfigBean;
import com.timevale.contractmanager.common.service.bean.ValidityConfigBean;
import com.timevale.contractmanager.common.service.bean.process.ProcessFileContractCategory;
import com.timevale.contractmanager.common.service.enums.ProcessFromEnum;
import com.timevale.doccooperation.service.model.ValidityConfig;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;

import java.util.List;

/**
 * 合同流程配置数据转换器
 *
 * <AUTHOR>
 * @since 2021/06/03
 */
public class ProcessConfigConverter {

    public static ValidityConfig convertValidityConfig(ValidityConfigBean configBean, boolean endOfDay) {
        ValidityConfig validityConfig = new ValidityConfig();
        validityConfig.setEndOfDay(endOfDay);

        if (null != configBean) {
            validityConfig.setDurationYear(configBean.getDurationYear());
            validityConfig.setDurationMonth(configBean.getDurationMonth());
            validityConfig.setDurationDay(configBean.getDurationDay());
            if (null != configBean.getValidityType()) {
                validityConfig.setValidityType(configBean.getValidityType());
            }
        }
        return validityConfig;
    }

    public static ValidityConfigBean convertValidityConfig(ValidityConfig validityConfig) {
        ValidityConfigBean configBean = ValidityConfigBean.defaultConfig();
        if (null != validityConfig) {
            configBean.setDurationYear(validityConfig.getDurationYear());
            configBean.setDurationMonth(validityConfig.getDurationMonth());
            configBean.setDurationDay(validityConfig.getDurationDay());
            if (null != validityConfig.getValidityType()) {
                configBean.setValidityType(validityConfig.getValidityType());
            }
        }
        return configBean;
    }

    public static ProcessConfigBean convertProcessConfig(String configInfo, boolean create) {
        if (StringUtils.isBlank(configInfo)) {
            return create ? new ProcessConfigBean() : null;
        }
        return JSONObject.parseObject(configInfo, ProcessConfigBean.class);
    }

    public static ProcessConfigBean convertProcessConfig(ProcessConfigDO configDO, boolean create) {
        if (null == configDO) {
            return create ? new ProcessConfigBean() : null;
        }
        return convertProcessConfig(configDO.getConfigInfo(), create);
    }

    public static ProcessCustomizeConfigBean convertCustomizeConfig(String config, boolean create) {
        if (StringUtils.isBlank(config)) {
            return create ? new ProcessCustomizeConfigBean() : null;
        }
        return JSONObject.parseObject(config, ProcessCustomizeConfigBean.class);
    }

    /**
     * 校验是否线下导入的流程
     * @param configDO
     * @return
     */
    public static boolean checkIsOfflineProcess(ProcessConfigDO configDO) {
        ProcessConfigBean processConfigBean = convertProcessConfig(configDO, true);
        return ProcessFromEnum.isOffline(processConfigBean.getProcessFrom());
    }

    /**
     * 获取合同类型配置信息
     *
     * @param configDO
     * @return
     */
    public static List<ProcessFileContractCategory> convertFileContractCategories(
            ProcessCustomizeConfigDO configDO) {
        if (null == configDO) {
            return Lists.newArrayList();
        }
        ProcessCustomizeConfigBean customizeConfigBean =
                convertCustomizeConfig(configDO.getConfigInfo(), false);
        if (null == customizeConfigBean
                || CollectionUtils.isEmpty(customizeConfigBean.getFileContractCategories())) {
            return Lists.newArrayList();
        }
        return customizeConfigBean.getFileContractCategories();
    }
}

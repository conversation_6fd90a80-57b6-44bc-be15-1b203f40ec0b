package com.timevale.contractmanager.core.service.component.autoarchive;

import com.timevale.contractmanager.common.dal.bean.grouping.GroupingInfoDO;
import com.timevale.contractmanager.common.dal.bean.grouping.MenuDO;
import com.timevale.contractmanager.common.dal.dao.grouping.GroupingInfoDAO;
import com.timevale.contractmanager.common.dal.dao.grouping.MenuDAO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.component.grouping.GroupingFileComponent;
import com.timevale.contractmanager.core.service.grouping.GroupingFileService;
import com.timevale.contractmanager.core.service.mq.model.GroupingFileInfo;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.IdGeneratorUtils;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.ProcessInfoTotalInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author:jianyang
 * @since 2021-05-09 13:47
 */
@Component
@Slf4j
public class AutoArchiveOperationComponent {

	@Autowired private GroupingFileService groupingFileService;
	@Autowired private UserCenterService userCenterService;
	@Autowired private GroupingInfoDAO groupingInfoDAO;
	@Autowired private GroupingFileComponent groupingFileComponent;
	@Autowired private MenuDAO menuDAO;


	/**
	 * 归档
	 * @param ProcessInfoTotalInfos
	 * @param menuId
	 * @param tenantOid
	 */
	public List<GroupingInfoDO> executeArchive(List<ProcessInfoTotalInfo> ProcessInfoTotalInfos, String menuId, String tenantOid){
		//获取租户信息
		UserAccount account = userCenterService.getUserAccountBaseByOid(tenantOid);
		if(account == null || StringUtils.isBlank(account.getAccountGid())){
			return new ArrayList<>();
		}

		List<GroupingInfoDO> groupingInfoDOs = new ArrayList<>();
		List<GroupingFileInfo.FileInfo> groupingFileInfos = new ArrayList<>();
		List<String> keys = new ArrayList<>();

		//特殊处理，归档所属企业的Oid拿创建菜单时候的企业Oid
		MenuDO menuDO = menuDAO.getByMenuId(menuId);
		if(menuDO == null){
			return new ArrayList<>();
		}
		if(account.getAccountGid().equals(menuDO.getGid())){
			log.info("executeArchive menuTenantOid:{}, accountTenantOid:{}, tenantGid:{}", menuDO.getOid(), account.getAccountOid(), account.getAccountGid());
			tenantOid = menuDO.getOid();
		}

		for(ProcessInfoTotalInfo x: ProcessInfoTotalInfos) {
			String processId = x.getProcessId();
			// 获取合同编号
			String contractNo = getContractNo(processId, tenantOid);
			String key = CacheUtil.getContractNoRepeatKey(processId, menuId, tenantOid);
			// 如果菜单下已经有该归档合同,则不进行处理
			if (isRepeat(key, processId, menuId, account.getAccountOid())) {
				log.info("auto archive process isRepeat processId:{},menuId:{}", processId, menuId);
				continue;
			}
			TedisUtil.set(key, true, 15, TimeUnit.MINUTES);
			keys.add(key);
			GroupingInfoDO groupingInfoDO = new GroupingInfoDO();
			groupingInfoDO.setContractNo(contractNo);
			groupingInfoDO.setProcessId(processId);
			groupingInfoDO.setSubjectOid(tenantOid);
			groupingInfoDO.setSubjectGid(account.getAccountGid());
			groupingInfoDO.setOperatorOid(account.getAccountOid());
			groupingInfoDO.setOperatorGid(account.getAccountGid());
			groupingInfoDO.setMenuId(menuId);
			groupingInfoDOs.add(groupingInfoDO);
			GroupingFileInfo.FileInfo fileInfo = new GroupingFileInfo.FileInfo();
			fileInfo.setContractNo(contractNo);
			fileInfo.setProcessId(processId);
			groupingFileInfos.add(fileInfo);
		}

		if(!groupingInfoDOs.isEmpty()){
			groupingInfoDAO.insertBatch(groupingInfoDOs);
		}
		// 流程归档至es中
		if(!groupingFileInfos.isEmpty()){
			try {
				groupingFileComponent.batchEsArchive(
						menuId, tenantOid, groupingFileInfos);
			} catch (Exception e) {
				log.error("台账2.0-2021流程归档至es异常: menuId:{},tenantOid:{},msg:{}",
						menuId,tenantOid,e);
				// es归档异常清空缓存
				keys.stream().forEach(x ->{
					TedisUtil.delete(x);
				});
				throw e;
			}
		}
		return groupingInfoDOs;
	}

	/**
	 * 根据主体和流程获取合同编号
	 *
	 * @param processId 流程id
	 * @param tenantId 主体id
	 * @return 合同编号
	 */
	private String getContractNo(String processId, String tenantId) {
		String key = CacheUtil.getContractNoKey(processId, tenantId);
		String contractNo = TedisUtil.get(key);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(contractNo)) {
			return contractNo;
		}
		List<GroupingInfoDO> groupingInfoDOList = groupingFileService.getGroupingInfoList(processId, tenantId);
		// 获取已有的合同编号,如果没有则重新生成
		if (ListUtils.isEmpty(groupingInfoDOList)) {
			contractNo = String.valueOf(IdGeneratorUtils.getId());
		} else {
			contractNo = groupingInfoDOList.get(0).getContractNo();
		}
		TedisUtil.set(key, contractNo, 15, TimeUnit.MINUTES);
		return contractNo;
	}

	/**
	 * 判断流程在菜单下是否重复
	 *
	 * @param processId 流程id
	 * @param tenantId 空间id
	 * @param menuId 菜单id
	 * @return true 重复 false 没有重复
	 */
	private Boolean isRepeat(String key, String processId, String menuId, String tenantId) {
		Boolean isRepeat = TedisUtil.get(key);
		if (null != isRepeat) {
			return isRepeat;
		}
		isRepeat = false;
		List<GroupingInfoDO> tempList =
				groupingInfoDAO.queryByIdxProcessMenuOid(processId, menuId, tenantId);
		if (!ListUtils.isEmpty(tempList)) {
			isRepeat = true;
		}
		return isRepeat;
	}
}

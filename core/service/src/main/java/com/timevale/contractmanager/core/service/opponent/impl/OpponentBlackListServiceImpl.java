package com.timevale.contractmanager.core.service.opponent.impl;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentOrgInfoOrder;
import com.timevale.contractmanager.common.dal.dao.SubProcessDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentOrgInfoOrderDAO;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.constant.FunctionLimitConstant;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentCheckInfoOrderTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentRiskLevelEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.common.service.integration.client.impl.SaasCommonClientImpl;
import com.timevale.contractmanager.common.service.model.opponent.BatchQueryRiskLevelByProcessIdsModel;
import com.timevale.contractmanager.common.service.model.opponent.QueryRiskLevelByEntityModel;
import com.timevale.contractmanager.common.service.model.opponent.QueryRiskLevelByOidOrGidModel;
import com.timevale.contractmanager.common.service.model.opponent.data.BatchQueryRiskLevelData;
import com.timevale.contractmanager.common.service.model.opponent.data.TenantData;
import com.timevale.contractmanager.common.service.result.opponent.OpponentRiskLevel;
import com.timevale.contractmanager.common.service.result.opponent.OpponentRiskLevelByProcessIdsResult;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentBatchGetBlackListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.ResetEnterpriseViewRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBatchGetBlackListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBlackListOrgCodeRespone;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBlackListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentEnterpriseViewResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.auditlog.AuditLogRecordService;
import com.timevale.contractmanager.core.service.component.opponent.baseability.OpponentBlackListBaseAbility;
import com.timevale.contractmanager.core.service.configs.CommonBizConfig;
import com.timevale.contractmanager.core.service.dto.opponent.OpponentAccountInfoBO;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.contractmanager.core.service.opponent.OpponentBlackListService;
import com.timevale.contractmanager.core.service.opponent.adapter.OpponentSupplierAdapter;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import com.timevale.saas.common.manage.common.service.model.output.VipFunctionQueryOutput;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.AccountBase;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.bean.ProcessInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.bean.TaskInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.enums.DocQueryEnum;
import com.timevale.signflow.search.docSearchService.enums.ProcessTypeEnum;
import com.timevale.signflow.search.docSearchService.param.DocQueryParam;
import com.timevale.signflow.search.docSearchService.result.DocQueryResult;
import com.timevale.signflow.search.docSearchService.result.ProcessAccountInfosResult;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdResult;
import com.timevale.signflow.search.service.model.v2.ProcessIdsModel;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:jianyang
 * @since 2021-03-04 21:27
 */
@Service
@Slf4j
public class OpponentBlackListServiceImpl implements OpponentBlackListService {

	@Autowired
	private OpponentEntityDAO opponentEntityDAO;
	@Autowired
	private EsClient esClient;
	@Autowired
	private UserCenterService userCenterService;
	@Autowired
	private SaasCommonClientImpl saasCommonClient;
	@Autowired
	private SubProcessDAO subProcessDAO;
	@Autowired
	private OpponentOrgInfoOrderDAO opponentCheckInfoDAO;
	@Autowired
	private OpponentBlackListBaseAbility opponentBlackListBaseAbility;
	@Autowired
	private OpponentSupplierAdapter opponentSupplierAdapter;
	@Autowired
	private AuditLogRecordService auditLogRecordService;


	@Override
	public void queryBlackListByProcessId(String processId,String flowId,List<TenantData> tenantsDatas) {
		/** add by 灰风@******** 增加降级开关*/
		Boolean blacklistSwitchOn = CommonBizConfig.getOpponentBlacklistSwitchOn();
		if(blacklistSwitchOn != null && !blacklistSwitchOn) {
			log.info("查询是否命中黑名单降级中");
			return;
		}

		if(StringUtils.isBlank(processId)){
			SubProcessDO subProcessDO = subProcessDAO.getByIdxSubprocessId(flowId);
			if(!Objects.isNull(subProcessDO)){
				processId = subProcessDO.getProcessId();
			}
		}
		QueryByProcessIdResult queryByProcessIdResult = esClient.queryById(processId);
		if(queryByProcessIdResult == null || queryByProcessIdResult.getProcessInfoTotalInfo() == null){
			return;
		}
		ProcessInfoTotalInfo processInfoTotalInfo = queryByProcessIdResult.getProcessInfoTotalInfo();
		//单租户场景
		if(tenantsDatas.size() == 1){
			String tenantOid = tenantsDatas.get(0).getTenantOid();
			OpponentBlackListResponse opponentBlackListResponse = new OpponentBlackListResponse();
			opponentBlackListResponse = getBlackListByProcessIdExecute(tenantOid,opponentBlackListResponse,processInfoTotalInfo);
			Boolean result = !(Objects.isNull(opponentBlackListResponse.getCc())) && (!opponentBlackListResponse.getParticipant().isEmpty()
					|| !opponentBlackListResponse.getCc().isEmpty() || !opponentBlackListResponse.getInitiator().isEmpty());
			if(result){
				throw new BizContractManagerException(
						BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getCode(),
						BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getMessage());
			}
			return;
		}

		//多租户场景
		if(tenantsDatas.size() > 1){
			//待优化
			for (TenantData tenantsData : tenantsDatas){
				String tenantOid = tenantsData.getTenantOid();
				OpponentBlackListResponse opponentBlackListResponse = new OpponentBlackListResponse();
				opponentBlackListResponse = getBlackListByProcessIdExecute(tenantOid,opponentBlackListResponse,processInfoTotalInfo);
				Boolean result = !(Objects.isNull(opponentBlackListResponse.getCc())) && (!opponentBlackListResponse.getParticipant().isEmpty()
						|| !opponentBlackListResponse.getCc().isEmpty() || !opponentBlackListResponse.getInitiator().isEmpty());
				if(result){
					throw new BizContractManagerException(
							BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getCode(),
							BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getMessage());
				}
			}
		}
	}


	@Override
	public void queryBlackListNoAccountByEntity(QueryRiskLevelByEntityModel model, boolean checkFunction) {
		/** add by 灰风@******** 增加降级开关*/
		Boolean blacklistSwitchOn = CommonBizConfig.getOpponentBlacklistSwitchOn();
		if(blacklistSwitchOn != null && !blacklistSwitchOn) {
			log.info("查询是否命中黑名单降级中");
			return;
		}
		String tenantOid = model.getTenantOid();
		String tenantGid = model.getTenantGid();
		// 如果租户gid为空， 根据租户id查询租户gid
        if (StringUtils.isBlank(tenantGid)) {
            // 租户企业的信息
            UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
            tenantGid = userAccountTenant.getAccountGid();
		}

		if(StringUtils.isBlank(tenantGid)){
			return ;
		}
		// 查询是否有黑名单功能
		if(checkFunction && !checkBlackListFunction(tenantOid)){
			return ;
		}

		List<String> orgNames = model.getEntityDatas().stream()
				.filter(x -> Objects.equals(x.getEntityType(),OpponentEntityTypeEnum.ORGANIZATION.getType()))
				.map(x ->x.getUniqueId()).collect(Collectors.toList());

		List<String> individual = model.getEntityDatas().stream()
				.filter(x ->Objects.equals(x.getEntityType(),OpponentEntityTypeEnum.INDIVIDUAL.getType()))
				.map(x ->x.getUniqueId()).collect(Collectors.toList());

		//企业
		if(!orgNames.isEmpty()){
			List<OpponentEntityDO> opponentEntityDOOrgs = opponentBlackListBaseAbility.listBatchTenantBlackListByEntityUniqueIds(tenantGid,orgNames,OpponentEntityTypeEnum.ORGANIZATION.getType());
			if(!opponentEntityDOOrgs.isEmpty()){
				throw new BizContractManagerException(
						BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getCode(),
						BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getMessage());
			}
		}

		//个人
		if(!individual.isEmpty()){
			List<OpponentEntityDO> opponentEntityDOIndividuals = opponentBlackListBaseAbility.listBatchTenantBlackListByEntityUniqueIds(tenantGid,individual,OpponentEntityTypeEnum.INDIVIDUAL.getType());
			if(!opponentEntityDOIndividuals.isEmpty()){
				throw new BizContractManagerException(
						BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getCode(),
						BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getMessage());
			}
		}

	}

	@Override
	public void queryRiskLevelByOidOrGid(QueryRiskLevelByOidOrGidModel model) {
		/** add by 灰风@******** 增加降级开关*/
		Boolean blacklistSwitchOn = CommonBizConfig.getOpponentBlacklistSwitchOn();
		if(blacklistSwitchOn != null && !blacklistSwitchOn) {
			log.info("查询是否命中黑名单降级中");
			return;
		}
		String tenantOid = model.getTenantOid();

		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid)){
			return ;
		}
		//查询是否有黑名单功能
		if(!checkBlackListFunction(tenantOid)){
			return ;
		}

		List<String> entityOid = Lists.newArrayList();
		List<String> entityGid = Lists.newArrayList();

		model.getEntityDatas().stream().forEach(x ->{
			if(!Objects.isNull(x.getEntityGid())){
				entityGid.add(x.getEntityGid());
			}else {
				entityOid.add(x.getEntityOid());
			}
		});
		List<OpponentEntityDO> opponentEntityDOIndividuals =Lists.newArrayList();
		if(entityOid != null && !entityOid.isEmpty()){
			List<OpponentEntityDO> opponentEntityDOByOid = opponentEntityDAO.getTenantBlackListByEntityOids(tenantGid,entityOid);
			opponentEntityDOIndividuals.addAll(opponentEntityDOByOid);
		}
		if(entityGid != null && !entityGid.isEmpty()){
			opponentEntityDOIndividuals.addAll(opponentBlackListBaseAbility.listBatchTenantBlackListByGid(tenantGid,entityGid));
		}

		if(!opponentEntityDOIndividuals.isEmpty()){
			throw new BizContractManagerException(
					BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_ENTITY_BLACK_LIST.getMessage());
		}
	}

	@Override
	public OpponentRiskLevelByProcessIdsResult batchQueryRiskLevelByProcessIds(BatchQueryRiskLevelByProcessIdsModel model) {
		OpponentRiskLevelByProcessIdsResult result = new OpponentRiskLevelByProcessIdsResult();
		/** add by 灰风@******** 增加降级开关*/
		Boolean blacklistSwitchOn = CommonBizConfig.getOpponentBlacklistSwitchOn();
		if(blacklistSwitchOn != null && !blacklistSwitchOn) {
			log.info("查询是否命中黑名单降级中");
			return buildMockRiskResult(model, result);
		}

		String tenantGid = model.getBatchQueryRiskLevelDatas().get(0).getTenantDatas().get(0).getTenantGid();
		/** 获取所有的流程信息**/
		List<String> processIds = model.getBatchQueryRiskLevelDatas().stream()
				.map(BatchQueryRiskLevelData::getProcessId).collect(Collectors.toList());
		ProcessIdsModel processIdsModel = new ProcessIdsModel();
		processIdsModel.setProcessIds(processIds);
		ProcessAccountInfosResult processAccountInfosResult =
				esClient.queryAccountInfoByProcessIds(processIdsModel);
		if(processAccountInfosResult == null || processAccountInfosResult.getProcessAccountInfos().isEmpty()) {
			//es异步化写入后，可能会导致es写入未完成，这里查询不到的情况，非必须场景，查不到直接返回成功
			//			throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ID_BLANK.getCode(),
			//					BizContractManagerResultCodeEnum.PROCESS_ID_BLANK.getMessage());
			return buildMockRiskResult(model, result);
		}


		/**获取黑名单信息**/
		OpponentAccountInfoBO opponentAccountInfoBO = opponentBlackListBaseAbility.batchConvertToAccountInfos(processAccountInfosResult.getProcessAccountInfos());
		List<OpponentEntityDO> opponentEntityDOS =
				opponentBlackListBaseAbility.getBlackListInfoByGid(tenantGid,opponentAccountInfoBO.getEntityOid(),opponentAccountInfoBO.getEntityGid());
		List<OpponentRiskLevel> opponentRiskLevels = new ArrayList<>();
		if(opponentEntityDOS.isEmpty()){
			for(BatchQueryRiskLevelData riskLevelData : model.getBatchQueryRiskLevelDatas()){
				OpponentRiskLevel opponentRiskLevel = new OpponentRiskLevel();
				opponentRiskLevel.setRiskLevel(false);
				opponentRiskLevel.setProcessId(riskLevelData.getProcessId());
				opponentRiskLevel.setFlowId(riskLevelData.getFlowId());
				opponentRiskLevels.add(opponentRiskLevel);
			}
		}else {
			List<String> blackListTenantOid = opponentEntityDOS.stream().map(OpponentEntityDO::getTenantOid).collect(Collectors.toList());
			model.getBatchQueryRiskLevelDatas().forEach(x ->{
				OpponentRiskLevel opponentRiskLevel = new OpponentRiskLevel();
				opponentRiskLevel.setRiskLevel(false);
				opponentRiskLevel.setProcessId(x.getProcessId());
				opponentRiskLevel.setFlowId(x.getFlowId());

				for(TenantData tenantData : x.getTenantDatas()){
					//查询是否有黑名单功能
					if(!checkBlackListFunction(tenantData.getTenantOid())){
						continue;
					}
					if(blackListTenantOid.contains(tenantData.getTenantOid())){
						opponentRiskLevel.setRiskLevel(true);
						break;
					}
				}
				opponentRiskLevels.add(opponentRiskLevel);
			});
		}

		result.setOpponentRiskLevels(opponentRiskLevels);
		return result;
	}

	private OpponentRiskLevelByProcessIdsResult buildMockRiskResult(BatchQueryRiskLevelByProcessIdsModel model, OpponentRiskLevelByProcessIdsResult result) {
		List<OpponentRiskLevel> opponentRiskLevels = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(model.getBatchQueryRiskLevelDatas())) {
			for (BatchQueryRiskLevelData item : model.getBatchQueryRiskLevelDatas()) {
				OpponentRiskLevel riskLevel = new OpponentRiskLevel();
				riskLevel.setProcessId(item.getProcessId());
				riskLevel.setFlowId(item.getFlowId());
				riskLevel.setRiskLevel(false);
				opponentRiskLevels.add(riskLevel);
			}
		}
		result.setOpponentRiskLevels(opponentRiskLevels);
		return result;
	}

	@Override
	public OpponentBlackListResponse getBlackListByProcess(String processId, Integer processType, String accountId) {
		OpponentBlackListResponse opponentBlackListResponse = new OpponentBlackListResponse();
		ProcessInfoTotalInfo processInfoTotalInfo = getProcessTotalInfo(processId, processType);

		if (Objects.isNull(processInfoTotalInfo)) {
			return opponentBlackListResponse;
		}
		UserAccountDetail userAccountPerson = userCenterService.getUserAccountDetailByOid(accountId);
		if(!userAccountPerson.getAccountRealNamed()){
			userAccountPerson.setAccountGid(userAccountPerson.getAccountOid());
		}

		//获取用户所有的企业列表
		List<UserAccountDetail> userAccountDetails = userCenterService.queryAccountOrgs(accountId);
		if(userAccountDetails.isEmpty()){
			return opponentBlackListResponse;
		}
		Map<String,String> map = new HashMap<>();
		for (UserAccountDetail userAccountDetail : userAccountDetails){
			map.put(userAccountDetail.getAccountOid(),userAccountDetail.getAccountGid());
		}
		//流程中的校验方主体
		List<UserAccountDetail> userAccountDetailProcesss = Lists.newArrayList();

		List<AccountBase> participants = processInfoTotalInfo.getParticipant();
		List<TaskInfoTotalInfo> taskInfos = processInfoTotalInfo.getTaskInfo();
		List<ProcessAccount> cc = processInfoTotalInfo.getCc();
		ProcessAccount initiator = processInfoTotalInfo.getInitiator();
		//获取流程中的所有企业
		if(!Objects.isNull(participants) && !participants.isEmpty()){
			for (AccountBase accountBase : participants){
				if(!accountBase.getSubject().getOrgan()){
					continue;
				}
				//当前操作人的主体是流程参与方
				String gid = map.get(accountBase.getSubject().getOid());
				if(StringUtils.isNotBlank(gid) && Objects.equals(gid,accountBase.getSubject().getGid())){
					UserAccountDetail userAccountDetail = new UserAccountDetail();
					userAccountDetail.setAccountOid(accountBase.getSubject().getOid());
					userAccountDetail.setAccountGid(accountBase.getSubject().getGid());
					userAccountDetailProcesss.add(userAccountDetail);
				}else {
					//当前操作人是流程参与方
					if(Objects.equals(accountId,accountBase.getPerson().getOid())){
						UserAccountDetail userAccountDetail = new UserAccountDetail();
						userAccountDetail.setAccountOid(accountBase.getSubject().getOid());
						userAccountDetail.setAccountGid(accountBase.getSubject().getGid());
						userAccountDetailProcesss.add(userAccountDetail);
					}
				}
			}
		}else {
			for (TaskInfoTotalInfo taskInfoTotalInfo : taskInfos){
				if(!taskInfoTotalInfo.getOperator().getSubject().getOrgan()){
					continue;
				}
				String gid = map.get(taskInfoTotalInfo.getOperator().getSubject().getOid());
				if(StringUtils.isNotBlank(gid) && Objects.equals(gid,taskInfoTotalInfo.getOperator().getSubject().getGid())){
					UserAccountDetail userAccountDetail = new UserAccountDetail();
					userAccountDetail.setAccountOid(taskInfoTotalInfo.getOperator().getSubject().getOid());
					userAccountDetail.setAccountGid(taskInfoTotalInfo.getOperator().getSubject().getGid());
					userAccountDetailProcesss.add(userAccountDetail);
				}else {
					if(Objects.equals(accountId,taskInfoTotalInfo.getOperator().getPerson().getOid())){
						UserAccountDetail userAccountDetail = new UserAccountDetail();
						userAccountDetail.setAccountOid(taskInfoTotalInfo.getOperator().getSubject().getOid());
						userAccountDetail.setAccountGid(taskInfoTotalInfo.getOperator().getSubject().getGid());
						userAccountDetailProcesss.add(userAccountDetail);
					}
				}
			}
		}

		if(!Objects.isNull(cc) && !cc.isEmpty()){
			for (ProcessAccount processAccount : cc){
				if(!processAccount.getSubject().getOrgan()){
					continue;
				}
				String gid = map.get(processAccount.getSubject().getOid());
				if(StringUtils.isNotBlank(gid) && Objects.equals(gid,processAccount.getSubject().getGid())){
					UserAccountDetail userAccountDetail = new UserAccountDetail();
					userAccountDetail.setAccountOid(processAccount.getSubject().getOid());
					userAccountDetail.setAccountGid(processAccount.getSubject().getGid());
					userAccountDetailProcesss.add(userAccountDetail);
				}else {
					if(Objects.equals(accountId,processAccount.getPerson().getOid())){
						UserAccountDetail userAccountDetail = new UserAccountDetail();
						userAccountDetail.setAccountOid(processAccount.getSubject().getOid());
						userAccountDetail.setAccountGid(processAccount.getSubject().getGid());
						userAccountDetailProcesss.add(userAccountDetail);
					}
				}
			}
		}

		if(!Objects.isNull(initiator)){
			if(initiator.getSubject().getOrgan()){
				String gid = map.get(initiator.getSubject().getOid());
				if(StringUtils.isNotBlank(gid) && Objects.equals(gid,initiator.getSubject().getGid())){
					UserAccountDetail userAccountDetail = new UserAccountDetail();
					userAccountDetail.setAccountOid(initiator.getSubject().getOid());
					userAccountDetail.setAccountGid(initiator.getSubject().getGid());
					userAccountDetailProcesss.add(userAccountDetail);
				}else {
					if(Objects.equals(initiator.getPerson().getOid(),accountId)){
						UserAccountDetail userAccountDetail = new UserAccountDetail();
						userAccountDetail.setAccountOid(initiator.getSubject().getOid());
						userAccountDetail.setAccountGid(initiator.getSubject().getGid());
						userAccountDetailProcesss.add(userAccountDetail);
					}
				}
			}
		}
		List<OpponentEntityDO> ccEntity = Lists.newArrayList();
		List<OpponentEntityDO> initiatorEntity = Lists.newArrayList();
		List<OpponentEntityDO> participantEntity = Lists.newArrayList();
		for (UserAccountDetail userAccountDetail : userAccountDetailProcesss){
			OpponentBlackListResponse result = getBlackListByProcessIdExecute(userAccountDetail.getAccountOid(),opponentBlackListResponse,processInfoTotalInfo);
			if(Objects.isNull(result.getInitiator())){
				continue;
			}
			initiatorEntity.addAll(result.getInitiator());
			ccEntity.addAll(result.getCc());
			participantEntity.addAll(result.getParticipant());
		}
		//去重
		ccEntity = Lists.newArrayList(ccEntity.stream().collect(Collectors.toMap(OpponentEntityDO::getEntityName,x->x,(k1,k2)->k1)).values());
		initiatorEntity = Lists.newArrayList(initiatorEntity.stream().collect(Collectors.toMap(OpponentEntityDO::getEntityName,x->x,(k1,k2)->k1)).values());
		participantEntity = Lists.newArrayList(participantEntity.stream().collect(Collectors.toMap(OpponentEntityDO::getEntityName,x->x,(k1,k2)->k1)).values());
		opponentBlackListResponse.setInitiator(initiatorEntity);
		opponentBlackListResponse.setParticipant(participantEntity);
		opponentBlackListResponse.setCc(ccEntity);
		return opponentBlackListResponse;
	}

	@Override
	public OpponentBlackListResponse getBlackListForSign(String processId, Integer processType, String authorizerIds) {
		OpponentBlackListResponse opponentBlackListResponse = new OpponentBlackListResponse();
		ProcessInfoTotalInfo processInfoTotalInfo = getProcessTotalInfo(processId,processType);

		if(Objects.isNull(processInfoTotalInfo)){
			return opponentBlackListResponse;
		}
		if(StringUtils.isBlank(authorizerIds)){
			return opponentBlackListResponse;
		}
		List<String> signEntitys = Arrays.asList(authorizerIds.split(","));

		List<OpponentEntityDO> ccEntity = Lists.newArrayList();
		List<OpponentEntityDO> initiatorEntity = Lists.newArrayList();
		List<OpponentEntityDO> participantEntity = Lists.newArrayList();
		for (String signEntity : signEntitys){
			//签署使用$$来区分是gid还是oid
			if(signEntity.contains("$$")){
				signEntity = signEntity.replaceAll("\\$","");
				signEntity = signEntity.replaceAll("GID","");
				UserAccountDetail userAccountDetail = userCenterService.getUserAccountDetailByGid(signEntity);
				signEntity = userAccountDetail.getAccountOid();
			}
			OpponentBlackListResponse result = getBlackListByProcessIdExecute(signEntity,opponentBlackListResponse,processInfoTotalInfo);
			if(Objects.isNull(result.getInitiator())){
				continue;
			}
			initiatorEntity.addAll(result.getInitiator());
			ccEntity.addAll(result.getCc());
			participantEntity.addAll(result.getParticipant());
		}

		//去重
		ccEntity = Lists.newArrayList(ccEntity.stream().collect(Collectors.toMap(OpponentEntityDO::getEntityName,x->x,(k1,k2)->k1)).values());
		initiatorEntity = Lists.newArrayList(initiatorEntity.stream().collect(Collectors.toMap(OpponentEntityDO::getEntityName,x->x,(k1,k2)->k1)).values());
		participantEntity = Lists.newArrayList(participantEntity.stream().collect(Collectors.toMap(OpponentEntityDO::getEntityName,x->x,(k1,k2)->k1)).values());
		opponentBlackListResponse.setInitiator(initiatorEntity);
		opponentBlackListResponse.setParticipant(participantEntity);
		opponentBlackListResponse.setCc(ccEntity);
		return opponentBlackListResponse;
	}

	/**
	 * 获取ProcessInfoTotalInfo
	 * @param processId
	 * @param processType
	 * @return
	 */
	public ProcessInfoTotalInfo getProcessTotalInfo(String processId,Integer processType){
		if(Objects.equals(processType, ProcessTypeEnum.SIGNING.getType())){
			SubProcessDO subProcessDO = subProcessDAO.getByIdxSubprocessId(processId);
			if(!Objects.isNull(subProcessDO)){
				processId = subProcessDO.getProcessId();
			}
		}
		QueryByProcessIdResult queryByProcessIdResult = esClient.queryById(processId);

		if(queryByProcessIdResult ==null || queryByProcessIdResult.getProcessInfoTotalInfo() == null){
			return null;
		}

		ProcessInfoTotalInfo processInfoTotalInfo = queryByProcessIdResult.getProcessInfoTotalInfo();
		return processInfoTotalInfo;
	}


	/**
	 *
	 * @param tenantOid
	 * @param opponentBlackListResponse
	 * @param processInfoTotalInfo
	 * @return
	 */
	public OpponentBlackListResponse getBlackListByProcessIdExecute(String tenantOid,OpponentBlackListResponse opponentBlackListResponse,ProcessInfoTotalInfo processInfoTotalInfo){
		/** add by 灰风@******** 增加降级开关*/
		Boolean blacklistSwitchOn = CommonBizConfig.getOpponentBlacklistSwitchOn();
		if(blacklistSwitchOn != null && !blacklistSwitchOn) {
			log.info("查询是否命中黑名单降级中");
			return opponentBlackListResponse;
		}

		//租户企业的信息
		UserAccountDetail userAccountTenant = userCenterService.getFatUserAccountDetailByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid) || userAccountTenant.isDeleted()){
			return opponentBlackListResponse;
		}
		//查询是否有黑名单功能
		if(!checkBlackListFunction(tenantOid)){
			return opponentBlackListResponse;
		}

		//参与方
		List<String> entityOidsPartic = new ArrayList<>();
		List<String> entityGidsPartic = new ArrayList<>();
		if(!Objects.isNull(processInfoTotalInfo.getParticipant()) && ! processInfoTotalInfo.getParticipant().isEmpty()){
			processInfoTotalInfo.getParticipant().stream()
					.forEach(x ->{
						if(Objects.isNull(x.getPerson().getGid())){
							entityOidsPartic.add(x.getPerson().getOid());
						}else {
							entityGidsPartic.add(x.getPerson().getGid());
						}
						if(x.getSubject().getOrgan() && !Objects.equals(x.getSubject().getOid(),tenantOid)){
							if(Objects.isNull(x.getSubject().getGid())){
								entityOidsPartic.add(x.getSubject().getOid());
							}else {
								entityGidsPartic.add(x.getSubject().getGid());
							}
						}
						if(!x.getSubject().getOrgan() && !Objects.equals(x.getSubject().getOid(),x.getPerson().getOid())){
							if(Objects.isNull(x.getSubject().getGid())){
								entityOidsPartic.add(x.getSubject().getOid());
							}else {
								entityGidsPartic.add(x.getSubject().getGid());
							}
						}
					});
		}else {
			processInfoTotalInfo.getTaskInfo().stream()
					.forEach(x ->{
						if(Objects.isNull(x.getExecute().getPerson().getGid())){
							entityOidsPartic.add(x.getExecute().getPerson().getOid());
						}else {
							entityGidsPartic.add(x.getExecute().getPerson().getGid());
						}
						if(x.getExecute().getSubject().getOrgan() && !Objects.equals(x.getExecute().getSubject().getOid(),tenantOid)){
							if(Objects.isNull(x.getExecute().getSubject().getGid())){
								entityOidsPartic.add(x.getExecute().getSubject().getOid());
							}else {
								entityGidsPartic.add(x.getExecute().getSubject().getGid());
							}
						}
						if(!x.getExecute().getSubject().getOrgan() && !Objects.equals(x.getExecute().getSubject().getOid(),x.getExecute().getPerson().getOid())){
							if(Objects.isNull(x.getExecute().getSubject().getGid())){
								entityOidsPartic.add(x.getExecute().getSubject().getOid());
							}else {
								entityGidsPartic.add(x.getExecute().getSubject().getGid());
							}
						}
					});
		}

		entityGidsPartic.stream().distinct().collect(Collectors.toList());
		entityOidsPartic.stream().distinct().collect(Collectors.toList());

		List<OpponentEntityDO> opponentEntityDOSPart = Lists.newArrayList();

		if(entityOidsPartic != null && !entityOidsPartic.isEmpty()){
			opponentEntityDOSPart.addAll(opponentEntityDAO.getTenantBlackListByEntityOids(tenantGid,entityOidsPartic));
		}

		if(entityGidsPartic != null && !entityGidsPartic.isEmpty()){
			opponentEntityDOSPart.addAll(opponentBlackListBaseAbility.listBatchTenantBlackListByGid(tenantGid,entityGidsPartic));
		}
		//抄送方获取黑名单
		List<String> entityOidsCC = new ArrayList<>();
		List<String> entityGidsCC = new ArrayList<>();

		if(!Objects.isNull(processInfoTotalInfo.getCc()) && !processInfoTotalInfo.getCc().isEmpty()){
			processInfoTotalInfo.getCc().stream()
					.forEach(x ->{
						if(Objects.isNull(x.getPerson().getGid())){
							entityOidsCC.add(x.getPerson().getOid());
						}else {
							entityGidsCC.add(x.getPerson().getGid());
						}
						if(x.getSubject().getOrgan() && !Objects.equals(x.getSubject().getOid(),tenantOid)){
							if(Objects.isNull(x.getSubject().getGid())){
								entityOidsCC.add(x.getSubject().getOid());
							}else {
								entityGidsCC.add(x.getSubject().getGid());
							}
						}
						if(!x.getSubject().getOrgan() && !Objects.equals(x.getSubject().getOid(),x.getPerson().getOid())){
							if(Objects.isNull(x.getSubject().getGid())){
								entityOidsPartic.add(x.getSubject().getOid());
							}else {
								entityGidsPartic.add(x.getSubject().getGid());
							}
						}
					});
		}

		entityGidsCC.stream().distinct().collect(Collectors.toList());
		entityOidsCC.stream().distinct().collect(Collectors.toList());
		List<OpponentEntityDO> opponentEntityDOSCC = Lists.newArrayList();
		if(entityOidsCC != null && !entityOidsCC.isEmpty()){
			opponentEntityDOSCC.addAll(opponentEntityDAO.getTenantBlackListByEntityOids(tenantGid,entityOidsCC));
		}
		if(entityGidsCC != null && !entityGidsCC.isEmpty()){
			opponentEntityDOSCC.addAll(opponentBlackListBaseAbility.listBatchTenantBlackListByGid(tenantGid,entityGidsCC));
		}
		//发起方
		List<String> entityOidsInitiator = new ArrayList<>();
		List<String> entityGidsInitiator = new ArrayList<>();

		ProcessAccount initiator = processInfoTotalInfo.getInitiator();
		if(Objects.isNull(initiator.getPerson().getGid())){
			entityOidsInitiator.add(initiator.getPerson().getOid());
		}else {
			entityGidsInitiator.add(initiator.getPerson().getGid());
		}
		if(initiator.getSubject().getOrgan() && !Objects.equals(initiator.getSubject().getOid(),tenantOid)){
			if(Objects.isNull(initiator.getSubject().getGid())){
				entityOidsInitiator.add(initiator.getSubject().getOid());
			}else {
				entityGidsInitiator.add(initiator.getSubject().getGid());
			}
		}
		if(!initiator.getSubject().getOrgan() && !Objects.equals(initiator.getSubject().getOid(),initiator.getPerson().getOid())){
			if(Objects.isNull(initiator.getSubject().getGid())){
				entityOidsInitiator.add(initiator.getSubject().getOid());
			}else {
				entityGidsInitiator.add(initiator.getSubject().getGid());
			}
		}
		List<OpponentEntityDO> opponentEntityDOSInitiator = Lists.newArrayList();
		if(entityGidsInitiator != null && !entityGidsInitiator.isEmpty()){
			opponentEntityDOSInitiator = opponentBlackListBaseAbility.listBatchTenantBlackListByGid(tenantGid,entityGidsInitiator);
		}

		if(entityOidsInitiator != null && !entityOidsInitiator.isEmpty()){
			opponentEntityDOSInitiator.addAll(opponentEntityDAO.getTenantBlackListByEntityOids(tenantGid,entityOidsInitiator));
		}
		opponentBlackListResponse.setParticipant(opponentEntityDOSPart);
		opponentBlackListResponse.setCc(opponentEntityDOSCC);
		opponentBlackListResponse.setInitiator(opponentEntityDOSInitiator);
		return opponentBlackListResponse;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Integer updateEntityRiskLevel(String tenantOid,String operatorOid,String uuid,Integer riskLevel) {
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_VIP_RISK_LEVEL.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_ENTITY_VIP_RISK_LEVEL.getMessage());
		}

		//查询是否有黑名单功能
		if(Objects.equals(riskLevel,OpponentRiskLevelEnum.BLACKLIST.getType())){
			if(!checkBlackListFunction(tenantOid)){
				throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_VIP_RISK_LEVEL.getCode(),
						BizContractManagerResultCodeEnum.OPPONENT_ENTITY_VIP_RISK_LEVEL.getMessage());
			}
		}

        // 获取相对方实体
        OpponentEntityDO opponentEntityDO = opponentEntityDAO.getByUuid(uuid);
        if (Objects.isNull(opponentEntityDO)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.OPPONENT_ENTITY_NOT_EXIST.getCode(),
                    BizContractManagerResultCodeEnum.OPPONENT_ENTITY_NOT_EXIST.getMessage());
        }
		// 非自己空间的相对方无法加入自己的黑名单
        if (!Objects.equals(opponentEntityDO.getTenantOid(), tenantOid)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.OPPONENT_BLACK_LIST_CHECK_ORG_ERROR.getCode(),
                    BizContractManagerResultCodeEnum.OPPONENT_BLACK_LIST_CHECK_ORG_ERROR.getMessage());
        }

		//禁止将自身加入黑名单
		if(Objects.equals(tenantOid,opponentEntityDO.getEntityOid()) && Objects.equals(riskLevel,OpponentRiskLevelEnum.BLACKLIST.getType())){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_ADD_SELf_ERROR.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_ENTITY_ADD_SELf_ERROR.getMessage());
		}

		Integer processingContractCount = 0;
		opponentEntityDO.setModifiedTime(new Date());
		opponentEntityDO.setRiskLevel(riskLevel);
		opponentEntityDAO.updateEntityInfo(opponentEntityDO);

		//返回进行中的相关合同数量
		UserAccount userAccount = userCenterService.getUserAccountBaseByOid(operatorOid);
		UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		Account subject = tenant.buildAccount();
		Account person = userAccount.buildAccount();

		processingContractCount = getProcessingContractCount(person,subject,opponentEntityDO);

		auditLogRecordService.recordOpponentBlackChange(tenantOid, operatorOid, Collections.singletonList(opponentEntityDO), riskLevel);
		return processingContractCount;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Integer batchUpdateEntityRiskLevel(String tenantOid,String operatorOid, List<String> uuids, Integer riskLevel) {
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_VIP_RISK_LEVEL.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_ENTITY_VIP_RISK_LEVEL.getMessage());
		}
		//查询是否有黑名单功能
		if(Objects.equals(riskLevel,OpponentRiskLevelEnum.BLACKLIST.getType())){
			if(!checkBlackListFunction(tenantOid)){
				throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_VIP_RISK_LEVEL.getCode(),
						BizContractManagerResultCodeEnum.OPPONENT_ENTITY_VIP_RISK_LEVEL.getMessage());
			}
		}

		//批量获取相对方实体
		List<OpponentEntityDO> opponentEntityDOS = opponentEntityDAO.getIdsByUuid(uuids);

		Integer processingContractCount = 0;

		if(!Objects.isNull(opponentEntityDOS) && !opponentEntityDOS.isEmpty()){

			//禁止将自身加入黑名单
			for(OpponentEntityDO opponentEntityDO : opponentEntityDOS){
				if(Objects.equals(opponentEntityDO.getEntityGid(),tenantGid) && Objects.equals(riskLevel,OpponentRiskLevelEnum.BLACKLIST.getType())){
					throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_ADD_SELf_ERROR.getCode(),
							BizContractManagerResultCodeEnum.OPPONENT_ENTITY_ADD_SELf_ERROR.getMessage());
				}
			}
			List<String> result = opponentEntityDOS.stream().map(x -> x.getUuid()).collect(Collectors.toList());
      		opponentEntityDAO.batchUpdateRiskLevel(result, riskLevel);

		}

		auditLogRecordService.recordOpponentBlackChange(tenantOid, operatorOid, opponentEntityDOS, riskLevel);
		return processingContractCount;
	}

	@Override
	public Boolean getOrgRiskLevel(String tenantOid, String orgName) {
		/** add by 灰风@******** 增加降级开关*/
		Boolean blacklistSwitchOn = CommonBizConfig.getOpponentBlacklistSwitchOn();
		if(blacklistSwitchOn != null && !blacklistSwitchOn) {
			log.info("查询是否命中黑名单降级中");
			return false;
		}

		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid)){
			return false;
		}
		//查询是否有黑名单功能
		if(!checkBlackListFunction(tenantOid)){
			return false;
		}

    	List<OpponentEntityDO> opponentEntityDOS =
        opponentEntityDAO.getRiskLevelByEntityUniqueId(
            orgName, tenantGid);

		if(CollectionUtils.isEmpty(opponentEntityDOS)){
			return false;
		}
		
		for (OpponentEntityDO opponentEntityDO : opponentEntityDOS){
			if(Objects.equals(opponentEntityDO.getDeleted(), DeletedEnum.YES)){
				return false;
			}
		}
		OpponentEntityDO opponentEntityDO = opponentEntityDOS.get(0);
		if(Objects.equals(opponentEntityDO.getRiskLevel(), OpponentRiskLevelEnum.BLACKLIST.getType())){
			return true;
		}
		return false;
	}

	@Override
	public Boolean getIndividualRiskLevel(String tenantOid, String contact) {
		/** add by 灰风@******** 增加降级开关*/
		Boolean blacklistSwitchOn = CommonBizConfig.getOpponentBlacklistSwitchOn();
		if(blacklistSwitchOn != null && !blacklistSwitchOn) {
			log.info("查询是否命中黑名单降级中");
			return false;
		}

		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid)){
			return false;
		}
		//查询是否有黑名单功能
		if(!checkBlackListFunction(tenantOid)){
			return false;
		}

    List<OpponentEntityDO> opponentEntityDOS =
        opponentEntityDAO.getRiskLevelByEntityUniqueId(
            contact, tenantGid);

		if(CollectionUtils.isEmpty(opponentEntityDOS)){
			return false;
		}

		for (OpponentEntityDO opponentEntityDO : opponentEntityDOS){
			if(Objects.equals(opponentEntityDO.getDeleted(), DeletedEnum.YES)){
				return false;
			}
		}

		OpponentEntityDO opponentEntityDO = opponentEntityDOS.get(0);
		if(Objects.equals(opponentEntityDO.getRiskLevel(), OpponentRiskLevelEnum.BLACKLIST.getType())){
			return true;
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public OpponentBlackListOrgCodeRespone getTokenAndOrgCode(String orgName, String tenantOid) {
		OpponentBlackListOrgCodeRespone opponentBlackListOrgCodeRespone = new OpponentBlackListOrgCodeRespone();
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		// 根据企业名称获取账号信息,可能为空
		UserAccount userAccount = userCenterService.getOrgInfoByName(orgName, null);
		if (StringUtils.isNotBlank(userAccount.getAccountOid())) {
			UserAccountDetail userAccountDetail =
					userCenterService.getUserAccountDetailByOid(userAccount.getAccountOid());
			userAccount.setAccountOid(userAccountDetail.getAccountOid());
			userAccount.setAccountGid(userAccountDetail.getAccountGid());
		}

		if(!checkOgrInfoFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_CHECK_ORG_INFO.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_ENTITY_CHECK_ORG_INFO.getMessage());
		}


		return opponentSupplierAdapter.getQiXinBaoToken(orgName);
	}

	@Override
	public List<OpponentBatchGetBlackListResponse> batchGetBlackList(OpponentBatchGetBlackListRequest opponentBatchGetBlackListRequest, String tenantOid) {
		List<OpponentBatchGetBlackListResponse> opponentBatchGetBlackListResponses = Lists.newArrayList();
		/** add by 灰风@******** 增加降级开关*/
		Boolean blacklistSwitchOn = CommonBizConfig.getOpponentBlacklistSwitchOn();
		if(blacklistSwitchOn != null && !blacklistSwitchOn) {
			log.info("批量查询是否命中黑名单降级中");
			return opponentBatchGetBlackListResponses;
		}

		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid)){
			return opponentBatchGetBlackListResponses;
		}
		//查询是否有黑名单功能
		if(!checkBlackListFunction(tenantOid)){
			return opponentBatchGetBlackListResponses;
		}

		List<String> orgNames = opponentBatchGetBlackListRequest.getEntityUniqueIds().stream()
				.filter(x -> Objects.equals(x.getEntityType(),OpponentEntityTypeEnum.ORGANIZATION.getType()))
				.map(x ->x.getUniqueId()).collect(Collectors.toList());

		List<String> individual = opponentBatchGetBlackListRequest.getEntityUniqueIds().stream()
				.filter(x ->Objects.equals(x.getEntityType(),OpponentEntityTypeEnum.INDIVIDUAL.getType()))
				.map(x ->x.getUniqueId()).collect(Collectors.toList());

		//企业
		List<OpponentEntityDO> opponentEntityDOOrgs = Lists.newArrayList();
		if(!orgNames.isEmpty()){
			opponentEntityDOOrgs = opponentBlackListBaseAbility.listBatchTenantBlackListByEntityUniqueIds(tenantGid,orgNames,OpponentEntityTypeEnum.ORGANIZATION.getType());
		}

		//个人
		List<OpponentEntityDO> opponentEntityDOIndividuals = Lists.newArrayList();
		if(!individual.isEmpty()){
			opponentEntityDOIndividuals = opponentBlackListBaseAbility.listBatchTenantBlackListByEntityUniqueIds(tenantGid,individual,OpponentEntityTypeEnum.INDIVIDUAL.getType());
		}
		List<OpponentBatchGetBlackListResponse> result = Lists.newArrayList();

		if(!opponentEntityDOOrgs.isEmpty()){
			result = opponentEntityDOOrgs.stream().map(x ->{
				OpponentBatchGetBlackListResponse response = OpponentBatchGetBlackListResponse
						.builder().build();
				response.setEntityName(x.getEntityUniqueId());
				response.setEntityType(x.getEntityType());
				return response;
			}).collect(Collectors.toList());
			opponentBatchGetBlackListResponses.addAll(result);
		}

		if(!opponentEntityDOIndividuals.isEmpty()){
			result = opponentEntityDOIndividuals.stream().map(x ->{
				OpponentBatchGetBlackListResponse response = OpponentBatchGetBlackListResponse
						.builder().build();
				response.setEntityName(x.getEntityUniqueId());
				response.setEntityType(x.getEntityType());
				return response;
			}).collect(Collectors.toList());
			opponentBatchGetBlackListResponses.addAll(result);
		}
		return opponentBatchGetBlackListResponses;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public OpponentEnterpriseViewResponse getNumberOfEnterpriseView(String tenantOid) {
		OpponentEnterpriseViewResponse opponentEnterpriseViewResponse = new OpponentEnterpriseViewResponse();
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid)){
			return opponentEnterpriseViewResponse;
		}
		Integer vipNum = getCheckInfoNum(tenantOid, RequestContextExtUtils.getClientId());
		OpponentOrgInfoOrder opponentCheckInfoInit = opponentCheckInfoDAO.queryInitByTenantGid(tenantGid);
		if(Objects.isNull(opponentCheckInfoInit)){
			OpponentOrgInfoOrder opponentCheckInfoSave = OpponentOrgInfoOrder.builder()
					.checkInfoNum(vipNum)
					.usedCheckInfoNum(0)
					.deleted(DeletedEnum.NO.code())
					.orderType(OpponentCheckInfoOrderTypeEnum.INIT.getType())
					.tenantOid(tenantOid)
					.tenantGid(tenantGid)
					.expiratTime(new Date(200,0,0))
					.build();

			opponentCheckInfoDAO.insert(opponentCheckInfoSave);
		}

		//失效所有的过期订单
		List<OpponentOrgInfoOrder> opponentCheckOrgInfoExprira = opponentCheckInfoDAO.queryExpiraByTenantGid(tenantGid);
		if(!opponentCheckOrgInfoExprira.isEmpty()){
			List<Long> ids = opponentCheckOrgInfoExprira.stream().map(x-> x.getId()).collect(Collectors.toList());
			opponentCheckInfoDAO.batchUpdate(ids);
		}
		//失效赠送订单中次数用完的
		List<OpponentOrgInfoOrder> opponentCheckOrgInfoFinish = opponentCheckInfoDAO.queryFinishedByTenantGid(tenantGid);
		if(!opponentCheckOrgInfoFinish.isEmpty()){
			List<Long> ids = opponentCheckOrgInfoFinish.stream().map(x-> x.getId()).collect(Collectors.toList());
			opponentCheckInfoDAO.batchUpdate(ids);
		}

		List<OpponentOrgInfoOrder> opponentCheckInfos = opponentCheckInfoDAO.queryByTenantGid(tenantGid);
		Integer totalNum = 0;
		Integer usedNum = 0;
		for (OpponentOrgInfoOrder opponentCheckInfo: opponentCheckInfos){
			totalNum += opponentCheckInfo.getCheckInfoNum();
			usedNum += opponentCheckInfo.getUsedCheckInfoNum();
		}
		opponentEnterpriseViewResponse.setResidueDegree(totalNum-usedNum);
		return opponentEnterpriseViewResponse;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void resetEnterpriseView(ResetEnterpriseViewRequest resetEnterpriseViewRequest) {
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(resetEnterpriseViewRequest.getTenantOid());
		String tenantGid = userAccountTenant.getAccountGid();

		if(StringUtils.isBlank(tenantGid)){
			throw new BizContractManagerException(
					BizContractManagerResultCodeEnum.OPPONENT_ERROR_PRESENT_NUM.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_ERROR_PRESENT_NUM.getMessage());
		}

		OpponentOrgInfoOrder opponentCheckInfoSave = OpponentOrgInfoOrder.builder()
				.checkInfoNum(resetEnterpriseViewRequest.getNum())
				.usedCheckInfoNum(0)
				.deleted(DeletedEnum.NO.code())
				.orderType(OpponentCheckInfoOrderTypeEnum.PRESENT.getType())
				.tenantOid(userAccountTenant.getAccountOid())
				.tenantGid(userAccountTenant.getAccountGid())
				.expiratTime(resetEnterpriseViewRequest.getExpiratTime())
				.build();

		opponentCheckInfoDAO.insert(opponentCheckInfoSave);
	}

	/**
	 * 查询是否支持会员黑名单功能
	 * @param tenantOid
	 * @return
	 */
	public boolean checkBlackListFunction(String tenantOid){
		return saasCommonClient.checkFunctionValid(tenantOid, FunctionCodeConstants.RISK_LEVEL,false);
	}

	/**
	 * 查询是否支持企业全貌功能
	 * @param tenantOid
	 * @return
	 */
	public boolean checkOgrInfoFunction(String tenantOid){
		try {
			saasCommonClient.checkFunctionValid(tenantOid, FunctionCodeConstants.CHECK_ORG_INFO,true);
		}catch (SaasCommonBizException e){
			return false;
		}
		return true;
	}

	/**
	 * 获取进行中合同数量
	 *
	 * @param subject 当前企业主体
	 * @param person 当前操作人
	 * @param opponentEntity 相对方实体
	 * @return 相关合同数量
	 */
	private Integer getProcessingContractCount(Account person, Account subject, OpponentEntityDO opponentEntity) {
		// 相对方实体为空,直接返回0
		if (StringUtils.isBlank(opponentEntity.getEntityOid())) {
			return 0;
		}
		Account opponentEntityAccount = new Account();
		opponentEntityAccount.setOid(opponentEntity.getEntityOid());
		opponentEntityAccount.setGid(opponentEntity.getEntityGid());
		opponentEntityAccount.setOrgan(
				OpponentEntityTypeEnum.ORGANIZATION.getType() == opponentEntity.getEntityType());
		List<Integer> processStatus = Lists.newArrayList();
		processStatus.add(1);
		processStatus.add(2);
		processStatus.add(3);
		// 获取流程信息
		DocQueryParam param =
				DocQueryParam.builder()
						.docQueryType(DocQueryEnum.OPPONENT.getType())
						.person(person)
						.subject(subject)
						.processStatusList(processStatus)
						.opponentEntityAccount(opponentEntityAccount)
						.build();

		// 仅获取总数,传1即可
		param.setPageNum(1);
		param.setPageSize(1);
		// 调用es接口
		DocQueryResult result = esClient.query(param);
		// 类型转换,获取相关合同总数量
		return ((Long) result.getTotal()).intValue();
	}

	/**
	 * 获取企业全貌会员版本对应可查看的数量
	 * @param tenantOid
	 * @return
	 */
	public Integer getCheckInfoNum(String tenantOid, String clientId){
		//获取限制信息
		Integer maxCreateNum = 0;
        VipFunctionQueryOutput output =
                saasCommonClient.queryVipFunctionInfo(
                        tenantOid, FunctionCodeConstants.CHECK_ORG_INFO, clientId);
		Map<String, Object> limit = output.getLimit();
		if(!(Objects.isNull(limit) || Objects.equals(limit.size(),0))){
			//根据会员版本获取查看的数量
			maxCreateNum = (Integer) output.getLimit().get(FunctionLimitConstant.MAX_CHECK_INFO);
		}
		return maxCreateNum;
	}
}

package com.timevale.contractmanager.core.service.grouping;

import com.timevale.signflow.search.docSearchService.bean.AccountBase;
import com.timevale.signflow.search.docSearchService.bean.TaskInfoTotalInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-20 17:56
 */
public interface GroupingProcessHandler {
    /**
     * 添加参与方
     *
     * @param data
     * @param taskInfo
     * @param participant
     * @param processStatus
     * @param showSign
     * @param processFrom
     * @return
     */
    int addParticipants(
            List<Object> data,
            List<TaskInfoTotalInfo> taskInfo,
            List<AccountBase> participant,
            Integer processStatus,
            boolean showSign,
            String processFrom);
}

package com.timevale.contractmanager.core.service.processstart.bean;

import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.service.enums.ParticipantStartType;
import com.timevale.contractmanager.common.service.constant.SystemConstant;
import com.timevale.contractmanager.core.model.bo.fda.FDASignatureConfig;
import com.timevale.doccooperation.service.constans.ValidGroups;
import com.timevale.doccooperation.service.ding.CreateFlow;
import com.timevale.doccooperation.service.enums.DocTemplateFromEnum;
import com.timevale.doccooperation.service.exception.DocCooperationBizException;
import com.timevale.doccooperation.service.model.*;
import com.timevale.docmanager.service.aop.annotation.StringCheckValid;
import com.timevale.docmanager.service.enums.StringCheckTypeEnum;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.EnumCheck;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.assertj.core.util.Lists;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.timevale.doccooperation.service.exception.DocCooperationErrorEnum.PARAMS_ILLEGAL;

/**
 * 流程发起签署参数对象
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Data
public class ProcessStartSignParam extends ToString {

    /** 应用id */
    @NotBlank(message = "应用id不能为空")
    private String appId;

    /** 流程id, 发起签署前预生成 */
    @NotBlank(message = "流程id不能为空")
    private String flowId;

    @NotBlank(message = "flowName不能为空")
    @Length(max = 100, message = "flowName长度最高为100")
    private String flowName;

    /** 发起人 */
    @Valid
    @NotNull(message = "发起人不能为空")
    private FlowAccount initiator;

    /** 发起人所属部门id */
    private String initiatorDeptId;
    /** 合同审批模板条件类型 {@link com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum} */
    private Integer approvalTemplateConditionType;
    /** 合同审批模板条件值 */
    private String approvalTemplateConditionValue;

    /** 流程信息 */
    @Valid
    @NotNull(message = "流程信息不能为空")
    private FlowInfo flowInfo;

    /** 合同管理流程id，创建合同管理流程前置到cm中，当前服务应该根据此字段是否为空兼容新老版本 */
    @NotBlank(message = "合同流程id不能为空", groups = CreateFlow.class)
    private String processId;

    /** 合同管理子流程id */
    private String cooperationId;

    /** 流程模板ID */
    private String flowTemplateId;

    /** 批量流程组id，批量发起场景会用到 */
    private String processGroupId;

    /** 批量流程组名称，批量发起场景发起合同审批时会用到 */
    private String processGroupName;

    /** @see ClientEnum#getClientId() */
    private String source = ClientEnum.WEB.getClientId();

    /** 业务场景, {@link com.timevale.doccooperation.service.enums.XuanYuanBizSceneEnum} */
    private String bizScene;

    /** 发起透传参数 */
    private Map<String, String> startFlowExtendMap;

    /** 签署文件列表 */
    @NotEmpty(message = "签署文件列表不能为空")
    private List<FlowFile> documents;


    /**
     * 签署模式
     */
    private String signMode;

    /** 流程附件列表 */
    @Valid private List<FlowFile> attachments;

    /** 抄送人 */
    @Valid private List<FlowAccount> ccs;

    /** 参与方 */
    @NotEmpty(message = "参与方列表不能为空")
    private List<FlowSigner> signers;

    /**
     * 企业经办人签项目增加的兼容参数，经办人签项目上线后一段时间，可删除这个参数
     * 原企业参与方发起如果包含经办人，会拆分成个人参与方 + 企业参与方。
     * 经办人签项目后，移除了这个拆分逻辑
     * 上线时间 ********
     */
    private Boolean oldTemplateStartData;

    @ApiModelProperty(value = "通知方式，1-短信，2-邮件，空字符串不发送，空值不限")
    private String noticeType;

    /**
     * fda配置
     */
    private FDASignatureConfig fdaSignatureConfig;

    /** 签署方信息 */
    @Data
    public static class FlowSigner extends FlowParticipant {

        /** 签署方名称 */
        @NotBlank(message = "signerLabel不能为空")
        @Length(max = 20, message = "签署方名称最长20个字符")
        private String signerLabel;

        /** 签署顺序 */
        private Integer signOrder = 1;

        /** 签署区 */
        private List<SignArea> signAreas;

        /** 备注签署区 */
        private List<RemarkSignArea> remarkSignAreas;

        /** 日期签署区 */
        private List<DateSignArea> dateSignAreas;

        private String signSealSizeType;

        public void checkSignPosList() {
            if (CollectionUtils.isEmpty(signAreas)) {
                return;
            }
            for (SignArea signPosBean : signAreas) {
                // 校验签署区签署要求
                if (StringUtils.isNotBlank(signPosBean.getSignRequirements())
                        && !StringUtils.isNumeric(signPosBean.getSignRequirements())) {
                    throw new DocCooperationBizException(PARAMS_ILLEGAL, "签署区签署要求格式错误");
                }
                Pos signPos = signPosBean.getSignPos();
                if (null == signPos) {
                    throw new DocCooperationBizException(PARAMS_ILLEGAL, "签署区坐标信息不能为空");
                }
                // 骑缝章场景，页码格式示例：1-10
                if (signPosBean.isQiFeng()) {
                    // 页码空值校验, 不能为空或者-
                    if (StringUtils.isBlank(signPosBean.getPage())
                            || "-".equals(signPosBean.getPage())) {
                        throw new DocCooperationBizException(PARAMS_ILLEGAL, "骑缝签署区页码不能为空");
                    }
                    // 页码数值校验 (page为all是正常场景)
                    if (!StringUtils.equalsIgnoreCase("ALL", signPosBean.getPage())) {
                        String[] pageList = signPosBean.getPage().split("-");
                        for (String page : pageList) {
                            if (!StringUtils.isNumeric(page)) {
                                throw new DocCooperationBizException(PARAMS_ILLEGAL, "骑缝签署区页码格式错误");
                            }
                        }
                    }

                    // 坐标校验
                    if (null == signPos.getY()) {
                        throw new DocCooperationBizException(PARAMS_ILLEGAL, "骑缝签署区Y坐标不能为空");
                    }
                    continue;
                }
                // 非骑缝章场景
                if (null == signPos.getX() || null == signPos.getY() || null == signPos.getPage()) {
                    throw new DocCooperationBizException(PARAMS_ILLEGAL, "签署区坐标或页码不能为空");
                }

                // 如果无需显示签署日期， 跳过签署日期校验
                if (!signPosBean.isShowDate()) {
                    continue;
                }
                // 签署区时间格式不能为空
                if (StringUtils.isBlank(signPosBean.getDateFormat())) {
                    throw new DocCooperationBizException(PARAMS_ILLEGAL, "签署区时间格式不能为空");
                }
                // 如果需显示签署日期，签署日期信息为空时补充默认值,
                if (null == signPosBean.getSignDatePos()) {
                    signPosBean.setSignDatePos(new Pos());
                }
                Pos signDate = signPosBean.getSignDatePos();
                // 签署日期默认坐标计算
                if (null == signDate.getX()) {
                    float signDateX = signPos.getX() - 50;
                    signDate.setX(signDateX >= 0 ? signDateX : 0);
                }
                if (null == signDate.getY()) {
                    float signDateY = signPos.getY() - 50;
                    signDate.setY(signDateY >= 0 ? signDateY : 0);
                }
            }
        }
    }

    public String getSource() {
        if (ClientEnum.valueOfType(source) == null) {
            source = ClientEnum.WEB.getClientId();
        }
        return source;
    }

    /**
     * 获取通知方式
     * @return
     */
    public String getNoticeType() {
        if (null == noticeType) {
            return SystemConstant.DEFAULT_CONTRACT_NOTICE_WAYS;
        }
        return noticeType;
    }

    public void addExtension(String key, String value) {
        if (startFlowExtendMap == null) startFlowExtendMap = Maps.newHashMap();
        startFlowExtendMap.put(key, value);
    }

    public String getExtension(String key) {
        if (null == startFlowExtendMap || null == key) {
            return null;
        }
        return startFlowExtendMap.get(key);
    }

    /** 文件信息 */
    @Data
    public static class FlowFile extends ToString {
        /** 文件id */
        @NotBlank(message = "fileId不能为空")
        private String fileId;
        /** 文件fileKey */
        private String fileKey;
        /** 文件名称 */
        @NotBlank(message = "fileName不能为空")
        private String fileName;
        /** 标识文件id来自哪里，1-模板文件 2-合同文件 3-动态模板文件 请参考 {@link DocTemplateFromEnum } */
       @EnumCheck(
                message = "文件来源类型不合法",
                target = DocTemplateFromEnum.class,
                enumField = "from")
        private Integer from = DocTemplateFromEnum.CONTRACT_FILE.getFrom();
        /** 水印ID */
        private String watermarkId;
    }

    /** 流程用户信息 */
    @Data
    public static class FlowAccount extends ToString {
        /** 用户账号 */
        private String account;
        /** 用户账号姓名 */
        @StringCheckValid(
                message = "用户账号姓名:'%s'包含不支持的字符",
                types = {StringCheckTypeEnum.RARELY_USED, StringCheckTypeEnum.EMOJI},
                groups = ValidGroups.Process.StartWithFill.class)
        private String accountName;
        /** 用户账号昵称 */
        @StringCheckValid(
                message = "用户账号昵称:'%s'包含不支持的字符",
                types = {StringCheckTypeEnum.RARELY_USED, StringCheckTypeEnum.EMOJI},
                groups = ValidGroups.Process.StartWithFill.class)
        private String accountNickname;
        /** 用户oid */
        @NotBlank(message = "accountOid不能为空")
        private String accountOid;
        /** 用户gid */
        private String accountGid;
        /** 用户uid */
        private String accountUid;

        private String accountLicense;

        private String accountLicenseType;

        /** 主体名称 */
        @NotBlank(message = "主体名称不能为空")
        @StringCheckValid(
                message = "用户主体名称:'%s'包含不支持的字符",
                types = {StringCheckTypeEnum.RARELY_USED, StringCheckTypeEnum.EMOJI},
                groups = ValidGroups.Process.StartWithFill.class)
        private String subjectName;
        /** 主体oid */
        @NotBlank(message = "subjectOid不能为空")
        private String subjectOid;
        /** 主体gid */
        private String subjectGid;
        /** 主体uid */
        private String subjectUid;
        /** 主体类型 */
        @NotNull(message = "subjectType不能为空")
        private Integer subjectType;
        /** 参与人级别-通知方式类型 */
        private List<Integer> signNoticeTypes;

        public void setSubjectType(Integer subjectType) {
            this.subjectType = subjectType;
        }
    }

    /** 流程参与人信息 */
    @Data
    public static class FlowParticipant extends FlowAccount {
        /** 参与方实例， 支持多实例场景 */
        private List<FlowAccount> accounts;
        /** 参与方扩展字段 */
        private Ext ext;

        /** 获取参与人实例列表 */
        public List<FlowAccount> obtainAccounts() {
            if (CollectionUtils.isNotEmpty(accounts)) {
                return accounts;
            }
            if (null == accounts) {
                accounts = Lists.newArrayList();
            }
            // TODO 兼容历史调用场景
            FlowAccount flowAccount = new FlowAccount();
            flowAccount.setAccount(super.getAccount());
            flowAccount.setAccountName(super.getAccountName());
            flowAccount.setAccountNickname(super.getAccountNickname());
            flowAccount.setAccountLicense(super.getAccountLicense());
            flowAccount.setAccountLicenseType(super.getAccountLicenseType());
            flowAccount.setAccountOid(super.getAccountOid());
            flowAccount.setAccountGid(super.getAccountGid());
            flowAccount.setAccountUid(super.getAccountUid());
            flowAccount.setSubjectName(super.getSubjectName());
            flowAccount.setSubjectOid(super.getSubjectOid());
            flowAccount.setSubjectGid(super.getSubjectGid());
            flowAccount.setSubjectUid(super.getSubjectUid());
            flowAccount.setSubjectType(super.getSubjectType());
            accounts.add(flowAccount);
            return accounts;
        }

        @Data
        public static final class Ext extends ToString {
            /** 参与模式， NORMAL-普通，OR_SIGN-或签 {@link com.timevale.doccooperation.service.enums.ParticipantModeEnum} */
            private String participantMode;
            /** 印章类型 1-模板章 0-手绘章 */
            private String sealType;
            /** 签署要求，逗号分隔 1-企业章 2-经办人 3-法定代表人章 */
            private String signRequirements;
            /** 签署意愿方式类型 */
            private List<String> willTypes;
            /**
             * 是否需要意愿，默认需要
             */
            private Boolean needWill;
            /** 签署顺序 */
            private Integer signOrder;
            /**
             * 发起指定印章 {@link com.timevale.doccooperation.service.enums.StartSignSpecifySealTypeEnum}
             */
            private Integer signSealType;
            /** 印章类型/id * */
            private String signSeal;
            /**"是否支持跨企业授权印章：true 支持，false 不支持"*/
            private Boolean supportCrossSignSeal = true;
            /** 强制阅读到底 * */
            private Boolean forceReadEnd;
            /** 强制阅读时长 * */
            private Integer forceReadTime;
            /** 签署人附件配置 */
            private List<AttachmentConfig> attachmentConfigs;
            /** 签署声明标题 */
            private String signTipsTitle;
            /** 签署声明内容 */
            private String signTipsContent;

            /**
             * 认证方式
             */
            private String authWay;

            /**
             * authWay = ACCESS_TOKEN 存在改值
             */
            private String accessToken;
            /** 是否扫码参与方 */
            private Boolean shareParticipant;
            /** 参与方级别-通知方式类型 */
            private List<Integer> signNoticeTypes;

            private String participantStartType = ParticipantStartType.PARTICIPANT_ASSIGN_ACCOUNT.getType();

            private String participantStartValue;

            private Boolean forceLicense;
        }

        /** 获取签署人印章类型 */
        public String obtainSealType() {
            String sealType = "";
            if (null != ext && StringUtils.isNotBlank(ext.getSealType())) {
                sealType = ext.getSealType();
            }
            return sealType;
        }

        /** 获取签署人签署要求 */
        public String obtainSignRequirements() {
            String signRequirements = "";
            if (null != ext && StringUtils.isNotBlank(ext.getSignRequirements())) {
                signRequirements = ext.getSignRequirements();
            }
            return signRequirements;
        }
    }

    /** 流程基本信息 */
    @Data
    public static class FlowInfo extends ToString {
        /** 签署有效日期 */
        private Date signValidity;
        /** 签署有效期配置，有效期类型仅支持 0-无需设置，1-自发起日开始计算，3-指定到期日 */
        private ValidityConfig signValidityConfig;
        /** 文件到期日 */
        private Date fileValidity;
        /** 文件有效期配置，有效期类型仅支持 0-无需设置，1-自发起日开始计算，2-自签署完成日开始计算，3-指定到期日 */
        private ValidityConfig fileValidityConfig;
        /** 备注 */
        private String comment;
        /** 合同类型 */
        private String contractType;
        /** 印章类型,1手绘,2模板 */
        private String sealType;
        /** 重定向地址 */
        private String redirectUrl;
        /** 重定向延迟时间，单位：秒 */
        private Integer redirectDelay;
        /** 签署完成通知地址 */
        private String signedNoticeUrl;
        /** 流程状态变更通知地址 */
        private String processNoticeUrl;
        /** 签署流程扩展数据 */
        private String signFlowExtData;
        /** 合同审批流模板id */
        private String approveTemplateId;
        /** 合同流程id */
        private String processId;
        /** 三方业务流程id */
        private String thirdProcessId;
        /** 一键落章配置 */
        private Boolean batchDropSeal;
        /** 指定付费方账号 */
        private String payerAccountId;
        /**
         * 专属云id
         */
        private String dedicatedCloudId;
        /** 是否允许解约，默认允许 */
        private Boolean allowRescind;

        /** 兼容老数据， 获取签署有效期配置 */
        public ValidityConfig getSignValidityConfig() {
            if (null == signValidityConfig) {
                signValidityConfig = new ValidityConfig();
            }
            return signValidityConfig;
        }

        /** 兼容老数据， 获取文件有效期配置 */
        public ValidityConfig getFileValidityConfig() {
            if (null == fileValidityConfig) {
                fileValidityConfig = new ValidityConfig();
            }
            return fileValidityConfig;
        }

        /** 发起时计算签署到期时间 */
        public Long calculateSignValidity() {
            ValidityConfig validityConfig = getSignValidityConfig();
            return null == validityConfig
                    ? null
                    : validityConfig.calculateValidateTime(signValidity);
        }

        /** 发起时计算合同到期时间 */
        public Long calculateFileValidity() {
            ValidityConfig validityConfig = getFileValidityConfig();
            return null == validityConfig
                    ? null
                    : validityConfig.calculateValidateTime(fileValidity);
        }
    }
}

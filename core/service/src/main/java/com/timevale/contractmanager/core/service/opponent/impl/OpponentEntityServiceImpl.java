package com.timevale.contractmanager.core.service.opponent.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDTO;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityRelationDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityRelationDAO;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.constant.FunctionLimitConstant;
import com.timevale.contractmanager.common.service.enums.opponent.AuthorizeTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.common.service.integration.client.OpponentSearchClient;
import com.timevale.contractmanager.common.service.integration.client.ResourceBelongClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.opponent.OrganizationInfoBO;
import com.timevale.contractmanager.core.model.dto.process.BuildQueryAllHavePermissionProcessResultDTO;
import com.timevale.contractmanager.core.model.dto.process.BuildQueryMenuAllProcessResultDTO;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualCreateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualUpdateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationCreateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationListRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationUpdateRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBaseResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentIndividualResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationListResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentOrganizationResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.detection.OpponentEnterpriseInfoResponse;
import com.timevale.contractmanager.core.model.dto.user.OrgDeptDTO;
import com.timevale.contractmanager.core.model.dto.user.OrgDeptRichDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.OpponentEntityCreditCodeTypeEnum;
import com.timevale.contractmanager.core.service.auditlog.AuditLogRecordService;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.auditlog.handler.AuditLogHelper;
import com.timevale.contractmanager.core.service.component.opponent.OpponentEntityConverter;
import com.timevale.contractmanager.core.service.component.opponent.detection.OpponentDetectionAdapter;
import com.timevale.contractmanager.core.service.configs.CommonBizConfig;
import com.timevale.contractmanager.core.service.dto.account.AccountUpdateDTO;
import com.timevale.contractmanager.core.service.dto.opponent.OpponentEntityBO;
import com.timevale.contractmanager.core.service.enums.AccountContactTypeEnum;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.contractmanager.core.service.enums.OpponentBusinessTagEnum;
import com.timevale.contractmanager.core.service.grouping.PermissionService;
import com.timevale.contractmanager.core.service.mq.consumer.opponent.OpponentEntityAdapter;
import com.timevale.contractmanager.core.service.mq.consumer.opponent.OpponentEntitySyncGidNeedRetryException;
import com.timevale.contractmanager.core.service.mq.model.opponent.OpponentUpdateMsg;
import com.timevale.contractmanager.core.service.mq.producer.OpponentMqProducer;
import com.timevale.contractmanager.core.service.opponent.OpponentAddSourceEnum;
import com.timevale.contractmanager.core.service.opponent.OpponentConfigCenter;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityService;
import com.timevale.contractmanager.core.service.opponent.OrganizationQueryService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.ProcessUserHavePermissionDataService;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.contractmanager.core.service.util.Utils;
import com.timevale.dayu.sdk.annotation.AuditLogAnnotation;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.easun.service.model.account.output.DeptBaseInfo;
import com.timevale.framework.mq.client.producer.DelayMsgLevel;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.enums.resourcebelong.ResourceBelongTypeEnum;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongBatchDeleteInput;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongBatchSaveInput;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongSaveInput;
import com.timevale.saas.common.manage.common.service.model.output.VipFunctionQueryOutput;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.AccountBase;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.bean.ProcessInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.bean.TaskInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.param.OpponentProcessQueryParam;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdResult;
import com.timevale.signflow.search.service.model.v2.OpponentEntityGroupByOrgQueryModel;
import com.timevale.signflow.search.service.model.v2.OpponentEntityQueryModel;
import com.timevale.signflow.search.service.result.v2.OpponentEntityGroupByOrgQueryResult;
import com.timevale.signflow.search.service.result.v2.OpponentEntityQueryResult;

import org.assertj.core.util.Sets;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.OPPONENT_ENTITY_NOT_EXIST;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.OPPONENT_ENTITY_SOCIAL_CREDIT_CODE_INVALID;

/**
 * @Author:jianyang
 * @since 2021-01-26 10:54
 */
@Service
@Slf4j
public class OpponentEntityServiceImpl implements OpponentEntityService {

    private static final String MOCK_TENANT_QUERY = "opponent.queryEnterpriseFromThirdMock";
    
    private static final String CREDIT_CODE_REGULAR = "credit.code.regular";
    private static final String CREDIT_CODE_REGULAR_MAP = "{\"1\":\"^[0-9A-HJ-NPQRTUWXY]{2}\\\\d{6}[0-9A-HJ-NPQRTUWXY]{9}[0-9A-HJ-NPQRTUWXY]$\",\"2\":\"(^[\\\\dA-Za-z]{18}$)|(^\\\\d{15}$)\",\"3\":\"(^(?!91|92)([\\\\dA-Za-z]{18})$)|(^[\\\\dA-Za-z]{1,17}$)|(^[\\\\dA-Za-z]{19,20}$)\"}";

    @Autowired
    private BaseProcessService baseProcessService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private OpponentEntityDAO opponentEntityDAO;

    @Autowired
    private SaasCommonClient saasCommonClient;

    @Autowired
    private EsClient esClient;

    @Autowired
    private OpponentSearchClient opponentSearchClient;

    @Autowired
    private OpponentMqProducer opponentMqProducer;

    @Resource
    private SystemConfig systemConfig;

    @Autowired private OpponentDetectionAdapter detectionAdapter;

    @Autowired
    private OrganizationQueryService organizationQueryService;

    @Autowired
    private SensorService  sensorService;
    @Autowired
    private OpponentConfigCenter opponentConfigCenter;
    @Autowired
    private OpponentEntityAdapter opponentEntityAdapter;

    @Autowired OpponentEntityRelationDAO opponentEntityRelationDAO;
    @Autowired
    private ResourceBelongClient resourceBelongClient;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private ProcessUserHavePermissionDataService processUserHavePermissionDataService;
    @Autowired
    private AuditLogRecordService auditLogRecordService;

    @Override
    public OpponentBaseResponse createOrganization(String processId, String operatorOid, String tenantOid, OpponentOrganizationCreateRequest createRequest) {
        String creditCode = createRequest.getCreditCode();
        if (!Constants.DEFAULT_SOCIAL_CREDIT_CODE.equals(creditCode)) {
            // 获取正则表达式
            Map<String, Object> regularMap = JsonUtils.json2map(ConfigService.getAppConfig()
                    .getProperty(CREDIT_CODE_REGULAR, CREDIT_CODE_REGULAR_MAP));
            // 校验正则
            if (!creditCode.matches(regularMap.get(createRequest.getCreditCodeType().toString()).toString())) {
                throw new BizContractManagerException(OPPONENT_ENTITY_SOCIAL_CREDIT_CODE_INVALID, creditCode);
            }
        }
       return doCreateOrganization(processId, operatorOid, tenantOid, createRequest, true);
    }

    @Override
    public OpponentBaseResponse autoCreateOrganizationByMsg(String processId, String operatorOid, String tenantOid,
                                                             String organizationName) {
        AssertX.isTrue(StringUtils.isNotBlank(organizationName), "请传入主体名称");
        OpponentOrganizationCreateRequest createRequest  = new OpponentOrganizationCreateRequest();
        createRequest.setOrganizationName(organizationName);
        return doCreateOrganization(processId, operatorOid, tenantOid, createRequest, false);
    }

    private OpponentBaseResponse doCreateOrganization(String processId, String operatorOid, String tenantOid,
                                                      OpponentOrganizationCreateRequest createRequest, boolean checkParam) {
        // 若开启校验参数，则校验参数
        if (checkParam) {
            validOrganizationCreateRequest(createRequest);
        }

        // 租户企业的信息
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        // 操作人账号信息
        UserAccount userAccountOperator = userCenterService.getUserAccountBaseByOid(operatorOid);

        return createOrganization(processId, userAccountOperator, userAccountTenant, createRequest);
    }

    @Override
    public OpponentBaseResponse createIndividual(String processId, String operatorOid, String tenantOid, OpponentIndividualCreateRequest createRequest) {
        // 租户企业的信息
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        // 操作人账号信息
        UserAccount userAccountOperator = userCenterService.getUserAccountBaseByOid(operatorOid);

        List<OpponentEntityDO> opponentEntityList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(createRequest.getOrganizationIds())){
            opponentEntityList = opponentEntityDAO.getIdsByUuid(createRequest.getOrganizationIds());
        }
        return createIndividual(
                processId,
                userAccountOperator,
                userAccountTenant,
                createRequest,
                opponentEntityList);
    }

    @Override
    public OpponentBaseResponse createOrganization(String processId,
                                                   UserAccount userAccountOperator,
                                                   UserAccount userAccountTenant,
                                                   OpponentOrganizationCreateRequest createRequest) {
        OpponentBaseResponse response = new OpponentBaseResponse();
        //企业名称中的半角括号转换为全角
        String orgName = createRequest.getOrganizationName();
        orgName.replace("（", Utils.halfToFull("（"));
        orgName.replace("）",Utils.halfToFull("）"));
        createRequest.setOrganizationName(orgName);
        String operatorOid = userAccountOperator.getAccountOid();
        String tenantOid = userAccountTenant.getAccountOid();

        //数量是否超过额度上限
        checkCreateLimit(tenantOid, userAccountTenant.getAccountGid(),
                operatorOid, OpponentEntityTypeEnum.ORGANIZATION.getType());


        String socialCreditCode = createRequest.getCreditCode();
        String legalPersonName = createRequest.getLegalPersonName();
        Integer creditCodeType = createRequest.getCreditCodeType();
        // 证件代码为空且不为国外企业
        if (StringUtils.isBlank(socialCreditCode) && !OpponentEntityCreditCodeTypeEnum.OTHER.getCode().equals(creditCodeType)) {
            // 特定企业不查三方
            OpponentEnterpriseInfoResponse mockOpponentEnterpriseInfoResponse = mockQueryThird(orgName);
            if (null != mockOpponentEnterpriseInfoResponse) {
                socialCreditCode = mockOpponentEnterpriseInfoResponse.getCreditCode();
                legalPersonName = mockOpponentEnterpriseInfoResponse.getLegalPersonName();
            } else {
            // 非特定企业查三方
            Optional<OpponentEnterpriseInfoResponse> optionResp = organizationQueryService.getSimpleInfoByOrgName(orgName);
            if (optionResp.isPresent()) {
                OpponentEnterpriseInfoResponse simpleInfo = optionResp.get();
                socialCreditCode = simpleInfo.getCreditCode();
                legalPersonName = simpleInfo.getLegalPersonName();
            }else {
                throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_ORG_NOT_EXIST,
                                                                createRequest.getOrganizationName());
            }
            }
        } else if (!socialCreditCode.equals(Constants.DEFAULT_SOCIAL_CREDIT_CODE)
                && !OpponentEntityCreditCodeTypeEnum.OTHER.getCode().equals(creditCodeType)) {
            /** 部分企业没有企业证件代码，在启信宝查出来是 '-'，如果有企业证件代码且为非国外企业 */
            Optional<OpponentEnterpriseInfoResponse> optionResp = organizationQueryService.getSimpleInfoByCreditCode(socialCreditCode);
            if(optionResp.isPresent()) {
                OpponentEnterpriseInfoResponse simpleInfo = optionResp.get();
                if (StringUtils.isBlank(simpleInfo.getName()) || !simpleInfo.getName().equals(createRequest.getOrganizationName())) {
                    throw new BizContractManagerException(
                            BizContractManagerResultCodeEnum.OPPONENT_ENTITY_SOCIAL_CREDIT_CODE_ORG_NAME_NOT_MATCH,
                            socialCreditCode, createRequest.getOrganizationName());
                }
            }else {
                throw new BizContractManagerException(OPPONENT_ENTITY_SOCIAL_CREDIT_CODE_INVALID, socialCreditCode);
            }
        }

        if (StringUtils.isBlank(socialCreditCode)) {
            log.info("企业:{} 无统一社会信用代码,不添加相对方", createRequest.getOrganizationName());
            return response;
        }

        // 校验相对方企业是否存在
        OpponentEntityDO opponentEntityDO =
                opponentEntityDAO.getByEntityUniqueIdAndCreditCode(
                        orgName,
                        userAccountTenant.getAccountGid(),
                        socialCreditCode,
                        OpponentEntityTypeEnum.ORGANIZATION.getType());

        if (!Objects.isNull(opponentEntityDO) && Objects.equals(opponentEntityDO.getDeleted(), DeletedEnum.NO.code())) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ORG_ADDED);
        }

        // 根据企业名称获取账号信息,可能为空
        UserAccount userAccount = userCenterService.getOrgInfoByNameForAccpect(orgName);
        if (StringUtils.isNotBlank(userAccount.getAccountOid())) {
            UserAccountDetail userAccountDetail =
                    userCenterService.getUserAccountDetailByOid(userAccount.getAccountOid());
            userAccount.setRealNameStatus(
                    Objects.isNull(userAccountDetail)
                            ? AuthorizeTypeEnum.INIT.name()
                            : userAccountDetail.getRealNameStatus());
        }
        // 相对方企业不为空且已经被删除，更新
        if (!Objects.isNull(opponentEntityDO) && Objects.equals(opponentEntityDO.getDeleted(), DeletedEnum.YES.code())) {
            opponentEntityDO.setDescription(createRequest.getDesc());
            opponentEntityDO.setModifyByGid(userAccountOperator.getAccountGid());
            opponentEntityDO.setModifyByOid(userAccountOperator.getAccountOid());
            opponentEntityDO.setDeleted(DeletedEnum.NO.code());
            opponentEntityDO.setEntityOid(userAccount.getAccountOid());
            opponentEntityDO.setEntityGid(userAccount.getAccountGid());
            opponentEntityDO.setCreateTime(new Date());
            opponentEntityDO.setEntityName(createRequest.getOrganizationName());
            opponentEntityDO.setSocialCreditCode(socialCreditCode);
            opponentEntityDO.setCreditCodeType(creditCodeType);
            opponentEntityDO.setLegalRepresentativeName(legalPersonName);
            opponentEntityDO.setEntityType(OpponentEntityTypeEnum.ORGANIZATION.getType());
            opponentEntityDO.setCreateByGid(userAccountOperator.getAccountGid());
            opponentEntityDO.setCreateByOid(userAccountOperator.getAccountOid());
            Integer realNameStatus = compatibleConvertRealNameStatus(coverRealNameStatus(userAccount.getRealNameStatus()), userAccount.getAccountGid());
            opponentEntityDO.setAuthorizeType(realNameStatus);
            opponentEntityDAO.update(opponentEntityDO);

            response.setUuid(opponentEntityDO.getUuid());
            response.setId(opponentEntityDO.getId());

            /** 相对方检测 **/
            try {
                // 国外企业或者自身跳过检测
                if(opponentEntityDO.getCreditCodeType().equals(OpponentEntityCreditCodeTypeEnum.OTHER.getCode())
                        || (opponentEntityDO.getEntityGid() != null
                        && opponentEntityDO.getEntityGid().equals(userAccountTenant.getAccountGid()))) {
                    return response;
                }
                detectionAdapter.singleDetectionTaskStart(Collections.singletonList(opponentEntityDO),
                        userAccountTenant, 1);
            }catch (BizContractManagerException e){
                log.info("租户版本不符合或检测次数用完, tenantGid:{} ", userAccount.getAccountGid());
            }catch (Exception e){
                log.info("开启检测任务异常:{}",e.getMessage());
            }
            addOpponentResourceBelong(opponentEntityDO.getUuid(), tenantOid, userAccountTenant.getAccountGid(), operatorOid);
            return response;
        }

        // 保存相对方关系
        OpponentEntityDO opponentEntityDOAdd = buildOpponentEntity(
                OpponentEntityTypeEnum.ORGANIZATION.getType(),
                userAccountTenant.getAccountGid(),
                userAccountTenant.getAccountOid(),
                userAccount.getAccountOid(),
                userAccount.getAccountGid(),
                orgName,
                null,
                userAccountOperator.getAccountOid(),
                userAccountOperator.getAccountGid(),
                compatibleConvertRealNameStatus(coverRealNameStatus(userAccount.getRealNameStatus()), userAccount.getAccountGid()),
                createRequest.getDesc(), processId, socialCreditCode, legalPersonName, creditCodeType);

        opponentEntityDAO.insert(opponentEntityDOAdd);
        response.setUuid(opponentEntityDOAdd.getUuid());
        response.setId(opponentEntityDOAdd.getId());

        /** 相对方检测 **/
        try {
            // 国外企业或者自身跳过检测
            if(opponentEntityDO.getCreditCodeType().equals(OpponentEntityCreditCodeTypeEnum.OTHER.getCode())
                    || (opponentEntityDO.getEntityGid() != null
                    && opponentEntityDO.getEntityGid().equals(userAccountTenant.getAccountGid()))) {
                return response;
            }
            detectionAdapter.singleDetectionTaskStart(Collections.singletonList(opponentEntityDOAdd),
                    userAccountTenant, 1);
        }catch (BizContractManagerException e){
            log.info("租户版本不符合或检测次数用完, tenantGid:{} ", userAccount.getAccountGid());
        }catch (Exception e){
            log.info("开启检测任务异常:{}",e.getMessage());
        }
        addOpponentResourceBelong(opponentEntityDOAdd.getUuid(), tenantOid, userAccountTenant.getAccountGid(), operatorOid);
        return response;
    }

    /**
     * 添加相对方，资源归属
     * @param opponentEntityId   相对方id
     * @param subjectOid  主体
     * @param operatorAddPersonOid  谁添加的
     */
    private void addOpponentResourceBelong(String opponentEntityId, String subjectOid, String subjectGid,
                                           String operatorAddPersonOid) {
        try {
            doAddOpponentResourceBelong(opponentEntityId, subjectOid, subjectGid, operatorAddPersonOid);
        } catch (Exception e) {
            log.error("opponent add resource belong ", e);
        }
    }

    private void doAddOpponentResourceBelong(String opponentEntityId, String subjectOid, String subjectGid,
                                             String operatorAddPersonOid) {
        // 查询当前企业是否有业务空间
        boolean haveMultiBizZone = userCenterService.haveMultiBizZone(subjectOid);
        if (!haveMultiBizZone) {
            return;
        }
        List<OrgDeptDTO> deptInfosByMemberId = userCenterService.getDeptInfosByMemberId(subjectOid, operatorAddPersonOid);
        if (CollectionUtils.isEmpty(deptInfosByMemberId)) {
            return;
        }

        List<ResourceBelongSaveInput> saveInputList = new ArrayList<>();
        deptInfosByMemberId.forEach(elm -> {
            List<OrgDeptRichDTO> deptTreeByDeptId =
                    userCenterService.getDeptTreeByDeptId(subjectOid, elm.getDeptId(), false);
            // 获取部门id路径
            ResourceBelongSaveInput saveInput = new ResourceBelongSaveInput();
            saveInput.setResourceId(opponentEntityId);
            saveInput.setSubjectOid(subjectOid);
            saveInput.setSubjectGid(subjectGid);
            saveInput.setDeptPath(deptTreeByDeptId.stream()
                    .map(OrgDeptRichDTO::getIncDeptId)
                    .map(String::valueOf).collect(Collectors.toList()));
            saveInput.setDirectDeptId(deptTreeByDeptId.get(0).getIncDeptId().toString());
            saveInput.setReason("");
            saveInputList.add(saveInput);
        });

        ResourceBelongBatchSaveInput input = new ResourceBelongBatchSaveInput();
        input.setDataList(saveInputList);
        input.setResourceType(ResourceBelongTypeEnum.OPPONENT.getCode());
        input.setAddBeforeShouldDeleteOldData(true);
        resourceBelongClient.batchSaveResourceBelong(input);
    }

    @Override
    public OpponentBaseResponse createIndividual(String processId, UserAccount userAccountOperator, UserAccount userAccountTenant, OpponentIndividualCreateRequest createRequest, List<OpponentEntityDO> relationEntity) {
        OpponentBaseResponse response = new OpponentBaseResponse();

        String individualName = createRequest.getIndividualName();
        String contact = createRequest.getContact();
        String operatorOid = userAccountOperator.getAccountOid();
        String tenantOid = userAccountTenant.getAccountOid();

        // 获取有效的相对方 （删除存在的无效相对方）
        OpponentEntityDO opponentEntityDO =
                queryOpponentEntityDO(
                        contact,
                        userAccountTenant.getAccountGid(),
                        OpponentEntityTypeEnum.INDIVIDUAL);
        //重复新增 判断是否修改
        if (!Objects.isNull(opponentEntityDO)) {
            ((OpponentEntityServiceImpl) AopContext.currentProxy())
                    .modifyOnCreationIndividual(
                            opponentEntityDO, relationEntity, userAccountOperator);
            response.setId(opponentEntityDO.getId());
            response.setUuid(opponentEntityDO.getUuid());
            return response;
        }

        // 会员版本数量限制校验
        checkCreateLimit(
                tenantOid,
                userAccountTenant.getAccountGid(),
                operatorOid,
                OpponentEntityTypeEnum.INDIVIDUAL.getType());

        //查询个人账号是否存在
        UserAccount userAccount = userCenterService.getPsnInfoAndCreate(null, contact);

        //获取实名状态
        UserAccountDetail userAccountDetail = userCenterService.getUserAccountDetailByOid(userAccount.getAccountOid());

        // 判断实名状态,若该联系方式已实名,判断姓名是否一致
        if (userAccountDetail.getAccountRealNamed()
                && !Objects.equals(
                        userAccountDetail.getAccountName(), createRequest.getIndividualName())) {
            // 若用户传入姓名与实名信息不一致,则抛出异常
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.TWO_FACTOR_CHECK_FAILURE, "已实名联系方式与姓名不一致");
        }
        // 新增
        opponentEntityDO =
                buildOpponentEntity(
                        OpponentEntityTypeEnum.INDIVIDUAL.getType(),
                        userAccountTenant.getAccountGid(),
                        userAccountTenant.getAccountOid(),
                        userAccount.getAccountOid(),
                        userAccount.getAccountGid(),
                        individualName,
                        contact,
                        userAccountOperator.getAccountOid(),
                        userAccountOperator.getAccountGid(),
                        compatibleConvertRealNameStatus(coverRealNameStatus(userAccountDetail.getRealNameStatus()), userAccountDetail.getAccountGid()),
                        createRequest.getDesc(),
                        processId,"",null, null);
        ((OpponentEntityServiceImpl)AopContext.currentProxy()).saveIndividual(opponentEntityDO,relationEntity);
        addOpponentResourceBelong(opponentEntityDO.getUuid(), tenantOid, userAccountTenant.getAccountGid(), operatorOid);
        response.setUuid(opponentEntityDO.getUuid());
        response.setId(opponentEntityDO.getId());
        return response;
    }

    @Transactional
    public void saveIndividual(OpponentEntityDO entity, List<OpponentEntityDO> relationEntity) {
        opponentEntityDAO.insert(entity);
        if (CollectionUtils.isNotEmpty(relationEntity)) {
            Long personId = entity.getId();
            String personUuid = entity.getUuid();
            List<OpponentEntityRelationDO> relationList =
                    relationEntity.stream()
                            .map(
                                    p -> {
                                        OpponentEntityRelationDO relation =
                                                new OpponentEntityRelationDO();
                                        relation.setOrgId(p.getId());
                                        relation.setOrgUuid(p.getUuid());
                                        relation.setPersonId(personId);
                                        relation.setPersonUuid(personUuid);
                                        return relation;
                                    })
                            .collect(Collectors.toList());
            opponentEntityRelationDAO.insert(relationList);
        }
    }

    @Transactional
    public void modifyOnCreationIndividual(OpponentEntityDO entity,List<OpponentEntityDO> relationEntity,UserAccount userAccountOperator){
        // 判断是否修改关联企业 原id列表+新id列表 去重数量是否等于原id列表数量
        // 新增接口 只能添加企业 删除关联关系通过修改接口
        List<Long> ids = opponentEntityRelationDAO.queryOrgIdByPersonId(entity.getId());
        Long personId = entity.getId();
        String personUuid = entity.getUuid();
        List<OpponentEntityRelationDO> relationList =
                relationEntity.stream()
                        .filter(p -> !ids.contains(p.getId()))
                        .map(
                                p -> {
                                    OpponentEntityRelationDO relation =
                                            new OpponentEntityRelationDO();
                                    relation.setOrgId(p.getId());
                                    relation.setOrgUuid(p.getUuid());
                                    relation.setPersonId(personId);
                                    relation.setPersonUuid(personUuid);
                                    return relation;
                                })
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relationList)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.OPPONENT_INDIVIDUAL_ADDED);
        } else {
            OpponentEntityDO upEntity = new OpponentEntityDO();
            upEntity.setId(entity.getId());
            // 记录操作人
            upEntity.setModifyByGid(userAccountOperator.getAccountGid());
            upEntity.setModifyByOid(userAccountOperator.getAccountOid());
            upEntity.setModifiedTime(new Date());
            opponentEntityRelationDAO.insert(relationList);
            opponentEntityDAO.updateById(upEntity);
        }
    }

    private OpponentEntityDO queryOpponentEntityDO(String contact,String gid,OpponentEntityTypeEnum typeEnum){
        List<OpponentEntityDO> opponentEntityDOS =
                opponentEntityDAO.listByTenantGidEntityUniqueIdEntityType(
                        contact, gid, typeEnum.getType());
        if(CollectionUtils.isEmpty(opponentEntityDOS)){
            return null;
        }
        // ******** fix: 符合查询的有2条数据，isDeleted 分别为 0 和 1，单个查会报错。如果存在这种情况把那个 isDeleted = 1 的删除掉
        OpponentEntityDO entity = null;
        List<Long> deleteIds = new ArrayList<>();
        for (OpponentEntityDO e : opponentEntityDOS) {
            if (Objects.equals(DeletedEnum.NO.code(), e.getDeleted())) {
                entity = e;
            } else {
                deleteIds.add(e.getId());
            }
        }
        if(CollectionUtils.isNotEmpty(deleteIds)){
            opponentEntityDAO.batchDelete(deleteIds);
        }
        return entity;
    }
    /**
     * 查询是否超过会员版本限制
     *
     * @param tenantId
     * @param tenantGid
     * @param operatorOid
     * @param entityType
     */
    public void checkCreateLimit(String tenantId, String tenantGid, String operatorOid, Integer entityType) {
        checkCreateLimit(tenantId, tenantGid, operatorOid, entityType, 1, RequestContextExtUtils.getClientId());
    }

    @Override
    public void checkCreateLimit(String tenantId, String tenantGid, String operatorOid, Integer entityType, Integer count, String clientId) {
        //获取限制信息
        VipFunctionQueryOutput output = null;
        if (Objects.equals(entityType, OpponentEntityTypeEnum.ORGANIZATION.getType())) {
            output = saasCommonClient.queryVipFunctionInfo(tenantId, FunctionCodeConstants.CREATE_OPPONENT_ORGANIZATION, clientId);
        } else {
            output = saasCommonClient.queryVipFunctionInfo(tenantId, FunctionCodeConstants.CREATE_OPPONENT_INDIVIDUALSIN, clientId);
        }

        Map<String, Object> limit = output.getLimit();
        if (!(Objects.isNull(limit) || Objects.equals(limit.size(), 0))) {
            //根据会员版本获取可新增的数量
            Integer maxCreateNum = (Integer) output.getLimit().get(FunctionLimitConstant.MAX_CREATE_COUNT);
            //获取已经新增的实体数量
            if (!Objects.isNull(tenantGid)) {
                Integer existNums = getOpponentEntityNums(tenantGid, entityType);
                if (existNums + count > maxCreateNum) {
                    throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_CREATE_LIMIT);
                }
                Integer createOpponentMax = ConfigService.getAppConfig()
                        .getIntProperty("check.create.opponent.organization.max", 1000);
                if (existNums + count > createOpponentMax) {
                    throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_ENTITY_CREATE_MAX,
                            createOpponentMax);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateOpponentEntityRealNameStatus(String oid, String gid, String name, Integer authorizeType) {
        UserAccountDetail opponentOrgAccount = userCenterService.getFatUserAccountDetailByOid(oid);
        String opponentOrgName = opponentOrgAccount.getAccountName();
        String opponentSocialCreditCode = opponentOrgAccount.getSocialCreditCode();
        //企业统一社会信用代码为空不更新
        if (!opponentOrgAccount.isPerson() && StringUtils.isBlank(opponentSocialCreditCode)) {
            return;
        }

        List<OpponentEntityDO> entities = new ArrayList<>();
        if(!opponentOrgAccount.isPerson()){
            entities = opponentEntityDAO.queryAllOpponents(opponentOrgName,
                    OpponentEntityTypeEnum.ORGANIZATION.getType(),
                    opponentSocialCreditCode);
        }

        if(opponentOrgAccount.isPerson()){
            entities = opponentEntityDAO.queryPersonOpponents(opponentOrgAccount.getAccountOid(),
                    OpponentEntityTypeEnum.INDIVIDUAL.getType(),
                    DeletedEnum.NO.code());
        }
        //相对放实体为空不更新
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        //更新实名状态
        entities.stream().forEach(x -> {
            x.setEntityGid(gid);
            x.setEntityOid(oid);
            x.setAuthorizeType(compatibleConvertRealNameStatus(authorizeType, gid));
            x.setModifiedTime(new Date());
            opponentEntityDAO.update(x);
        });
    }

    @Override
    public void deleteOpponentIndividual(List<String> uuidList, List<Long> idList, Boolean hasDel) {
        if (CollectionUtils.isNotEmpty(uuidList)) {
            //相对方联系人是无上限的，这里拆分下到mq里去按批删除
            List<OpponentEntityDO> opponentEntityDOS = opponentEntityDAO.getByUuids(uuidList, DeletedEnum.YES.code());
            if (CollectionUtils.isEmpty(opponentEntityDOS)) {
                return;
            }
            List<Long> ids = opponentEntityDOS.stream().map(OpponentEntityDO::getId).collect(Collectors.toList());
            List<Long> individualIdList = opponentEntityRelationDAO.queryPersonByOrgIdList(ids);
            if(CollectionUtils.isEmpty(individualIdList)){
                return;
            }
            //删除关联关系 在分批更新（触发更新es）/删除相对方
            opponentEntityRelationDAO.deleteByOrgId(ids);
            List<List<Long>> partitionList = Lists.partition(individualIdList, systemConfig.getOpponentMaxPartitionSize());
            for (List<Long> partition : partitionList) {
                OpponentUpdateMsg opponentUpdateMsg = new OpponentUpdateMsg();
                opponentUpdateMsg.setIdList(partition);
                opponentUpdateMsg.setHasDel(hasDel);
                opponentMqProducer.sendMessage(JSONObject.toJSONString(opponentUpdateMsg), OpponentBusinessTagEnum.DELETE.getType());
            }
            return;
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            if(hasDel){
                //删除个人关联关系 在删除个人相对方信息
                opponentEntityRelationDAO.deleteByPersonId(idList);
                opponentEntityDAO.batchDeleteIndividualsByIdList(idList, DeletedEnum.YES.code());
            }else{
                //更新触发同步es
                opponentEntityDAO.batchDeleteIndividualsByIdList(idList,DeletedEnum.NO.code());
            }
        }
    }

    @Override
    public void batchSyncOpponentOrgans() {
        long maxId = 0L;

        OpponentEntityDTO source = new OpponentEntityDTO();
        source.setEntityType(OpponentEntityTypeEnum.ORGANIZATION.getType());
        source.setPageSize(50);
        source.setId(0L);
        long processCnt = 0L, deleteCnt =0L, updateCnt = 0L;
        log.info("batchSyncOpponentOrgans job started by manual, scroll entities with condition:{}", source);

        List<OpponentEntityDO> entityDOS = opponentEntityDAO.scrollOpponentEntity(source);
        while (CollectionUtils.isNotEmpty(entityDOS)) {
            for(OpponentEntityDO item : entityDOS) {
                if (item.getId()>maxId) {
                    maxId = item.getId();
                }

                if (StringUtils.isBlank(item.getSocialCreditCode())) {
                    processCnt ++;
                    Optional<OpponentEnterpriseInfoResponse> simpleInfo = Optional.empty();
                    try{
                        simpleInfo = organizationQueryService.getSimpleInfoByOrgName(item.getEntityName());
                    }catch (Exception e) {
                        if ( e instanceof BaseBizRuntimeException) {
                            BaseBizRuntimeException bizRuntimeException = (BaseBizRuntimeException)e;
                            log.warn("getSimpleInfoByOrgName by:{} of:{} failed with code:{} message:{}", item.getEntityName(),
                                                                            item,
                                                                            bizRuntimeException.getCode(),
                                                                            bizRuntimeException.getMessage());
                        } else {
                            log.error("getSimpleInfoByOrgName by:{} of:{} failed", item.getEntityName(), item, e);
                        }
                        continue;
                    }

                    if (simpleInfo.isPresent()) {
                        OpponentEntityDO existEntity = opponentEntityDAO.getByEntityUniqueIdAndCreditCode(item.getEntityUniqueId(),
                                item.getTenantGid(), simpleInfo.get().getCreditCode(), OpponentEntityTypeEnum.ORGANIZATION.getType());

                        if (existEntity!=null) {
                            log.info("organization: {} duplicate with entityId:{}, delete it", existEntity.toString(), item.getId());
                            opponentEntityDAO.delete(existEntity.getId());
                        }
                        OpponentEntityDO target = new OpponentEntityDO();
                        try{
                            target.setId(item.getId());
                            target.setSocialCreditCode(simpleInfo.get().getCreditCode());
                            target.setLegalRepresentativeName(simpleInfo.get().getLegalPersonName());
                            opponentEntityDAO.updateEntityInfo(target);
                            log.info("update entity:{} with value:{} ", target.getId(), target.toString());
                            updateCnt++;
                        }catch (Exception e) {
                            log.error("fail to update opponent entity:{}", target.toString(), e);
                        }
                    }else {
                        log.info("delete none exist organization: {}", item.toString());
                        opponentEntityDAO.delete(item.getId());
                        deleteCnt ++;
                    }
                }
            }

            source.setId(maxId);
            log.info("sync at:{}. processedCnt:{} deletedCnt:{} updateCnt:{}", maxId, processCnt, deleteCnt, updateCnt);
            entityDOS = opponentEntityDAO.scrollOpponentEntity(source);
        }
        log.info("sync finished. processedCnt:{} deletedCnt:{} updateCnt:{}", processCnt, deleteCnt, updateCnt);
    }

    @Override
    public void updateLegalInfo(String tenantOid, String name) {
        List<OpponentEntityDO> opponentEntityDOS =
                opponentEntityDAO.getOpponentEntityByEntityOid(tenantOid, DeletedEnum.NO.code());

        if(CollectionUtils.isNotEmpty(opponentEntityDOS)){
            opponentEntityDOS.forEach(x ->{
                x.setLegalRepresentativeName(name);
                x.setModifiedTime(new Date());
                opponentEntityDAO.update(x);
            });
        }
    }

    /**
     * 转换实名状态
     *
     * @param realNameStatus
     * @return
     */
    private Integer coverRealNameStatus(String realNameStatus) {
        // 默认未认证
        if (StringUtils.isBlank(realNameStatus)) {
            return AuthorizeTypeEnum.INIT.getType();
        }
        if (Objects.equals(realNameStatus, RealnameStatus.REJECT.name())) {
            return AuthorizeTypeEnum.INIT.getType();
        }

        if (Objects.equals(realNameStatus, RealnameStatus.COMMIT.name())) {
            return AuthorizeTypeEnum.AUTHENTICATING.getType();
        }

        if (Objects.equals(realNameStatus, RealnameStatus.DEPRECATE.name())) {
            return AuthorizeTypeEnum.INIT.getType();
        }
        return AuthorizeTypeEnum.valueOf(realNameStatus).getType();
    }

    private Integer compatibleConvertRealNameStatus(Integer status, String gid) {
        if (status == null) {
            return null;
        }

        if (AuthorizeTypeEnum.INIT.getType() == status && StringUtils.isNotBlank(gid)) {
            return AuthorizeTypeEnum.SUPPOSE_ACCEPT.getType();
        }

        return status;
    }

    /**
     * 统计已经新增的企业数量
     *
     * @param tenantGid
     * @param entityType
     * @return
     */
    private Integer getOpponentEntityNums(String tenantGid, Integer entityType) {
        OpponentEntityQueryModel opponentEntityQueryModel = new OpponentEntityQueryModel();
        opponentEntityQueryModel.setGid(tenantGid);
        opponentEntityQueryModel.setEntityType(entityType);
        opponentEntityQueryModel.setPageSize(100);
        opponentEntityQueryModel.setPageNum(1);
        OpponentEntityQueryResult opponentEntityQueryResult = opponentSearchClient.pageQuery(opponentEntityQueryModel);
        return Math.toIntExact(opponentEntityQueryResult.getTotal());
    }

    private Integer getOpponentEntityNums(String tenantOid, String tenantGid, String personOid, Integer entityType) {
        return getOpponentEntityNums(tenantOid,personOid,tenantGid, null, entityType);
    }

    private Integer getOpponentEntityNums(String tenantOid, String tenantGid, String personOid,Long attachedEntityId) {
        return getOpponentEntityNums(tenantOid,personOid,tenantGid, attachedEntityId, null);
    }

    private Integer getOpponentEntityNums(String tenantOid,String personOid,String tenantGid,Long attachedEntityId, Integer entityType) {
        OpponentEntityQueryModel opponentEntityQueryModel = new OpponentEntityQueryModel();
        opponentEntityQueryModel.setGid(tenantGid);
        if (entityType != null) {
            opponentEntityQueryModel.setEntityType(entityType);
        } else {
            opponentEntityQueryModel.setEntityType(OpponentEntityTypeEnum.INDIVIDUAL.getType());
        }
        opponentEntityQueryModel.setPageNum(1);
        opponentEntityQueryModel.setPageSize(1);
        if (attachedEntityId != null) {
            opponentEntityQueryModel.setAttachedEntityId(attachedEntityId);
        }
        populateOpponentMultiBizQuery(tenantOid, tenantGid, personOid, opponentEntityQueryModel);
        OpponentEntityQueryResult opponentEntityQueryResult =
                opponentSearchClient.pageQuery(opponentEntityQueryModel);
        return Math.toIntExact(opponentEntityQueryResult.getTotal());
    }

    @AuditLogAnnotation(
            enterpriseSpaceUnique1 = "#tenantOid",
            userUnique1 = "#operatorOid",
            resourceEntSpaceUnique = "#tenantOid",
            resourceName = "#request.organizationName",
            result = "\"成功\"",
            detailTactics = "1",
            condition = "{{T(com.timevale.mandarin.base.util.StringUtils).isNotBlank(#request.legalPersonName)}}",
            selfDefiningData = "{{T(com.google.common.collect.ImmutableMap).of(\"newLegalPersonName\", #request.legalPersonName)}}",
            postHandle = "auditLogOpponentUpdateHandle")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateOrganization(
            String operatorOid,
            String tenantOid,
            String organizationId,
            OpponentOrganizationUpdateRequest request) {
        AuditLogHelper.acceptHeaderFields();
        // 获取用户空间信息
        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);

        // 根据uuid获取相对方实体信息
        OpponentEntityDO entity = opponentEntityDAO.getByUuid(organizationId);
        // 数据不存在报错及越权校验
        if (null == entity
                || !entity.getTenantGid().equals(tenant.getAccountGid())
                || DeletedEnum.YES.code() == entity.getDeleted()) {
            throw new BizContractManagerException(OPPONENT_ENTITY_NOT_EXIST);
        }
        LogRecordContext.putVariable(AuditLogConstant.Field.OPPONENT_ID, entity.getUuid());
        LogRecordContext.putVariable(AuditLogConstant.Field.ORIGIN_LEGAL_PERSON_NAME, entity.getLegalRepresentativeName());
        // 获取用户信息
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(operatorOid);

        String orgName = request.getOrganizationName();
        String socialCreditCode = request.getCreditCode();
        Integer creditCodeType = request.getCreditCodeType();
        entity.setModifyByOid(userAccount.getAccountOid());
        entity.setModifyByGid(userAccount.getAccountGid());
        entity.setDescription(request.getDesc());
        // 判断企业名称是否被修改 如果不修改企业名称,则仅修改备注即可
        if (entity.getEntityName().equals(orgName)
                && socialCreditCode.equals(entity.getSocialCreditCode())
                && creditCodeType.equals(entity.getCreditCodeType())) {
            // 仅修改备注即可
            entity.setLegalRepresentativeName(request.getLegalPersonName());
            entity.setDescription(request.getDesc());
            opponentEntityDAO.updateEntityInfo(entity);
            return;
        }
        /** 已实名并且证件代码不为'-'的企业不允许进行修改*/
        if (!entity.getSocialCreditCode().equals(Constants.DEFAULT_SOCIAL_CREDIT_CODE) &&
                AuthorizeTypeEnum.ACCEPT.getType() == entity.getAuthorizeType()) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.OPPONENT_ENTITY_UPDATE);
        }

        String legalPersonName = request.getLegalPersonName();
        // 若企业证件代码不为'-'且为大陆企业（统一社会信用代码、工商注册号）
        if (!socialCreditCode.equals(Constants.DEFAULT_SOCIAL_CREDIT_CODE)
                && !OpponentEntityCreditCodeTypeEnum.OTHER.getCode().equals(creditCodeType)) {
            /** 部分企业没有证件代码，在启信宝查出来是 '-' */
            Optional<OpponentEnterpriseInfoResponse> optionResp =
                    organizationQueryService.getSimpleInfoByCreditCode(socialCreditCode);
            if (!optionResp.isPresent()) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.OPPONENT_ENTITY_SOCIAL_CREDIT_CODE_INVALID,
                        socialCreditCode);

            }
            OpponentEnterpriseInfoResponse simpleInfo = optionResp.get();
            if (StringUtils.isBlank(simpleInfo.getName())
                    || !simpleInfo.getName().equals(orgName)) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum
                                .OPPONENT_ENTITY_SOCIAL_CREDIT_CODE_ORG_NAME_NOT_MATCH,
                        socialCreditCode,
                        orgName);
            }
        }

        // 校验相对方企业是否存在
        OpponentEntityDO opponentEntityDO =
                opponentEntityDAO.getByEntityUniqueIdAndCreditCode(
                        orgName, tenant.getAccountGid(), socialCreditCode, OpponentEntityTypeEnum.ORGANIZATION.getType());

        // 该企业是否已有该企业记录
        boolean exist = false;
        // 非空校验
        if (null != opponentEntityDO) {
            // 如果该企业记录已存在但处于逻辑删除状态,则进行物理删除,并继承风险等级
            if (DeletedEnum.YES.code() == opponentEntityDO.getDeleted()) {
                entity.setRiskLevel(opponentEntityDO.getRiskLevel());
                // 物理删除
                opponentEntityDAO.delete(opponentEntityDO.getId());
            } else {
                // 该企业记录已存在
                exist = true;
            }
        }
        // 企业重名则抛出异常
        if (exist && !entity.getUuid().equals(opponentEntityDO.getUuid())) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.OPPONENT_ORG_ADDED);
        }

        // 获取用户中心企业信息
        UserAccount orgInfo = userCenterService.getOrgInfoByNameForAccpect(orgName);
        Integer realNameStatus = AuthorizeTypeEnum.INIT.getType();
        if (StringUtils.isNotBlank(orgInfo.getAccountOid())) {
            UserAccountDetail userAccountDetail =
                    userCenterService.getUserAccountDetailByOid(orgInfo.getAccountOid());
            if (userAccountDetail != null) {
                realNameStatus = compatibleConvertRealNameStatus(coverRealNameStatus(userAccountDetail.getRealNameStatus()), userAccountDetail.getAccountGid());
            }
        }
        entity.setAuthorizeType(realNameStatus);
        entity.setEntityOid(Optional.ofNullable(orgInfo.getAccountOid()).orElse(""));
        entity.setEntityGid((Optional.ofNullable(orgInfo.getAccountGid()).orElse("")));
        entity.setEntityName(request.getOrganizationName());
        entity.setEntityUniqueId(request.getOrganizationName());
        entity.setLegalRepresentativeName(legalPersonName);
        entity.setSocialCreditCode(socialCreditCode);
        entity.setCreditCodeType(creditCodeType);
        opponentEntityDAO.updateEntityInfo(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateIndividual(
            String operatorOid,
            String tenantOid,
            String individualId,
            OpponentIndividualUpdateRequest request) {
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(operatorOid);
        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);

        // 根据uuid获取相对方实体信息
        OpponentEntityDO entity = opponentEntityDAO.getByUuid(individualId);
        // 数据不存在报错及越权校验
        if (null == entity
                || !entity.getTenantGid().equals(tenant.getAccountGid())
                || DeletedEnum.YES.code() == entity.getDeleted()) {
            throw new BizContractManagerException(OPPONENT_ENTITY_NOT_EXIST);
        }

        String contact = request.getContact();
        entity.setModifyByOid(userAccount.getAccountOid());
        entity.setModifyByGid(userAccount.getAccountGid());
        entity.setModifiedTime(new Date());
        entity.setDescription(request.getDesc());
        // 校验个人是否已添加
        OpponentEntityDO opponentEntityDO =
                opponentEntityDAO.getByEntityUniqueId(
                        contact, tenant.getAccountGid(), OpponentEntityTypeEnum.INDIVIDUAL.getType());

        // 该企业下是否已经添加该相对方个人
        boolean exist = false;
        // 非空校验
        if (null != opponentEntityDO) {
            // 如果该记录已存在但处于逻辑删除状态,则进行物理删除,并继承风险等级
            if (DeletedEnum.YES.code() == opponentEntityDO.getDeleted()) {
                entity.setRiskLevel(opponentEntityDO.getRiskLevel());
                // 物理删除
                opponentEntityDAO.delete(opponentEntityDO.getId());
            } else {
                // 该企业记录已存在
                exist = true;
            }
        }
        // 已经添加则抛出异常
        if (exist && !entity.getUuid().equals(opponentEntityDO.getUuid())) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.OPPONENT_INDIVIDUAL_ADDED);
        }

        // 获取所属企业id
        List<OpponentEntityDO> orgList =new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getOrganizationIds())) {
            orgList = opponentEntityDAO.getIdsByUuid(request.getOrganizationIds());
        }

        // 如果联系方式及姓名未修改,则仅修改所属企业及备注即可
        if (!entity.getEntityUniqueId().equals(request.getContact())
                || !entity.getEntityName().equals(request.getIndividualName())) {
            // 根据联系方式查询个人账号是否存在,不存在则根据联系方式创建一个账号
            UserAccount psnInfo = userCenterService.getPsnInfoAndCreate(null, contact);

            // 获取实名状态
            UserAccountDetail userAccountDetail =
                    userCenterService.getUserAccountDetailByOid(psnInfo.getAccountOid());
            // 判断实名状态,若该联系方式已实名,判断姓名是否一致
            if (userAccountDetail.getAccountRealNamed()) {
                // 若用户传入姓名与实名信息不一致,则抛出异常
                if (!userAccountDetail.getAccountName().equals(request.getIndividualName())) {
                    throw new BizContractManagerException(
                            BizContractManagerResultCodeEnum.TWO_FACTOR_CHECK_FAILURE, "已实名联系方式与姓名不一致");
                }
                entity.setAuthorizeType(coverRealNameStatus(userAccountDetail.getRealNameStatus()));
            } else {
                entity.setAuthorizeType(AuthorizeTypeEnum.INIT.getType());
            }
            entity.setAuthorizeType(compatibleConvertRealNameStatus(entity.getAuthorizeType(), userAccountDetail.getAccountGid()));
            entity.setEntityOid(userAccountDetail.getAccountOid());
            entity.setEntityGid(Optional.ofNullable(userAccountDetail.getAccountGid()).orElse(""));
            entity.setEntityName(request.getIndividualName());
            entity.setEntityUniqueId(userAccountDetail.account());
        }

        updateOpponentRelation(orgList,entity);
        opponentEntityDAO.updateEntityInfo(entity);
    }

    @Override
    public OpponentOrganizationListResponse listOrganizations(
            String operatorOid, String tenantOid, OpponentOrganizationListRequest request, Boolean calculateFlag) {
        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        String gid = tenant.getAccountGid();
        List<OpponentEntityDO> list;
        long count;
        String scrollId = null;
        if (CommonBizConfig.OPPONENT_QUERY_SWITCH) {
            OpponentEntityQueryModel opponentEntityQueryModel = new OpponentEntityQueryModel();
            opponentEntityQueryModel.setGid(gid);
            opponentEntityQueryModel.setEntityType(OpponentEntityTypeEnum.ORGANIZATION.getType());
            opponentEntityQueryModel.setRiskLevel(request.getRiskLevel());
            if (request.getAuthorizeType() != null && AuthorizeTypeEnum.ACCEPT.getType() == request.getAuthorizeType()){
                opponentEntityQueryModel.setAuthorizeTypeList(AuthorizeTypeEnum.getAllAcceptTypes());
            }else {
                opponentEntityQueryModel.setAuthorizeType(request.getAuthorizeType());
            }
            opponentEntityQueryModel.setFuzzyDesc(request.getFuzzyDesc());
            opponentEntityQueryModel.setEntityName(request.getFuzzyOrganizationName());
            opponentEntityQueryModel.setSocialCreditCode(request.getCreditCode());
            opponentEntityQueryModel.setLegalPersonName(request.getLegalPersonName());
            opponentEntityQueryModel.setPageSize(request.getPageSize());
            opponentEntityQueryModel.setPageNum(request.getPageNum());
            opponentEntityQueryModel.setScrollId(request.getScrollId());
            opponentEntityQueryModel.setUseScroll(request.getUseScroll());
            populateOpponentMultiBizQuery(tenantOid,  tenant.getAccountGid(), operatorOid, opponentEntityQueryModel);
            OpponentEntityQueryResult opponentEntityQueryResult = opponentSearchClient.pageQuery(opponentEntityQueryModel);
            count = opponentEntityQueryResult.getTotal();
            if (count == 0) {
                return buildOrganizationEmptyResult(tenantOid, gid, operatorOid);
            }
            list = OpponentEntityConverter.toOpponentEntityDOList(opponentEntityQueryResult.getList());
            scrollId = opponentEntityQueryResult.getScrollId();
        } else {
            // 计算分页参数
            int offset = (request.getPageNum() - 1) * request.getPageSize();
            // 构造查询参数
            OpponentEntityDTO dto =
                    new OpponentEntityDTO(
                            gid,
                            OpponentEntityTypeEnum.ORGANIZATION.getType(),
                            request.getRiskLevel(),
                            offset,
                            request.getPageSize(),
                            request.getAuthorizeType(),
                            request.getFuzzyDesc(),
                            request.getFuzzyOrganizationName(),
                            null);
            count = opponentEntityDAO.countOpponentEntity(dto);
            if (count == 0) {
                return buildOrganizationEmptyResult(tenantOid, gid, operatorOid);
            }
            list = opponentEntityDAO.listOpponentEntity(dto);
        }

        if (CollectionUtils.isEmpty(list)) {
            return buildOrganizationEmptyResult(tenantOid, gid, operatorOid);
        }

        // 获取每个企业下的人数
        List<Long> idList = list.stream().map(OpponentEntityDO::getId).collect(Collectors.toList());
        Map<String, Long> memberCountMap = calculateFlag ? getMemberCountMap(gid, idList) : new HashMap<>();
        // 构造返回结果
        List<OpponentOrganizationResponse> organizations =
                list.parallelStream()
                        .map(
                                entity -> {
                                    OpponentOrganizationResponse organization =
                                            OpponentOrganizationResponse.builder().build();
                                    organization.setAuthorizeType(OpponentEntityConverter.convertShowAuthorizeType(entity.getAuthorizeType()));
                                    organization.setOrganizationId(entity.getUuid());
                                    organization.setOrganizationName(entity.getEntityName());
                                    organization.setSocialCreditCode(entity.getSocialCreditCode());
                                    organization.setCreditCodeType(entity.getCreditCodeType() == null ?
                                            OpponentEntityCreditCodeTypeEnum.SOCIAL_CREDIT_CODE.getCode() : entity.getCreditCodeType());
                                    organization.setLegalPersonName(entity.getLegalRepresentativeName());
                                    organization.setDesc(entity.getDescription());
                                    organization.setRiskLevel(entity.getRiskLevel());
                                    organization.setOrganizationAccountId(entity.getEntityOid());
                                    organization.setOrganizationAccountGid(entity.getEntityGid());
                                    // 从map中获取企业人员数量,没获取到则默认返回0
                                    organization.setMemberCount(calculateFlag ?
                                            memberCountMap.getOrDefault(entity.getId().toString(), 0L).intValue() : 0);
                                    // 获取相关合同数量
                                    organization.setProcessingContractCount(0);
                                    return organization;
                                })
                        .collect(Collectors.toList());

        return OpponentOrganizationListResponse.builder()
                .totalSize(count)
                .scrollId(scrollId)
                .organizations(organizations)
                .opponentEntityNums(
                        getOpponentEntityNums(tenantOid, gid, operatorOid, OpponentEntityTypeEnum.ORGANIZATION.getType()))
                .build();
    }

    private OpponentOrganizationListResponse buildOrganizationEmptyResult(String tenantOid, String gid, String personOid) {
        return OpponentOrganizationListResponse.builder()
                .totalSize(0L)
                .organizations(new ArrayList<>())
                .opponentEntityNums(
                        getOpponentEntityNums(tenantOid,
                                gid, personOid, OpponentEntityTypeEnum.ORGANIZATION.getType()))
                .build();
    }

    @Override
    public OpponentOrganizationResponse getOrganization(
            String operatorOid, String tenantOid, String organizationId) {
        // 获取用户信息
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(operatorOid);
        // 获取用户空间信息
        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        OpponentEntityDO entity = opponentEntityDAO.getByUuid(organizationId);
        // 数据不存在报错及越权校验
        if (null == entity
                || !entity.getTenantGid().equals(tenant.getAccountGid())
                || DeletedEnum.YES.code() == entity.getDeleted()) {
            throw new BizContractManagerException(OPPONENT_ENTITY_NOT_EXIST);
        }
        // 组装响应对象
        OpponentOrganizationResponse response = OpponentOrganizationResponse.builder().build();
        response.setOrganizationName(entity.getEntityName());
        response.setAuthorizeType(entity.getAuthorizeType());
        response.setDesc(entity.getDescription());
        response.setCreateProcessId(entity.getCreateProcessId());
        response.setCreateTime(entity.getCreateTime());
        response.setRiskLevel(entity.getRiskLevel());
        response.setSocialCreditCode(entity.getSocialCreditCode());
        response.setLegalPersonName(entity.getLegalRepresentativeName());
        response.setCreditCodeType(entity.getCreditCodeType());

        // 获取创建来源用户信息
        UserAccountDetail createUser =
                userCenterService.getFatUserAccountDetailByOid(entity.getCreateByOid());
        response.setCreateContact(createUser.account());

        // 获取流程名称
        if (StringUtils.isNotBlank(entity.getCreateProcessId())) {
            ProcessDO processDO = baseProcessService.getProcess(entity.getCreateProcessId());
            response.setCreateName(null != processDO ? processDO.getProcessTitle() : null);
        } else {
            response.setCreateName(userAccount.getAccountName());
        }

        // 如果没有创建流程,则创建名称为用户名称
        if (StringUtils.isBlank(response.getCreateName())) {
            response.setCreateName(createUser.getAccountName());
        }
        // 获取企业人员数量,没获取到则默认返回0
        Integer opponentEntityNums = getOpponentEntityNums(tenantOid,tenant.getAccountGid(),operatorOid,entity.getId());
        response.setMemberCount(null != opponentEntityNums ? opponentEntityNums : 0);
        // 获取相关合同数量
        response.setProcessingContractCount(
                getProcessingContractCount(
                        userAccount.buildAccount(), tenant.buildAccount(), entity));
        return response;
    }

    @Override
    public OpponentIndividualListResponse listIndividuals(
            String operatorOid,
            String tenantOid,
            String organizationId,
            OpponentIndividualListRequest request,
            Boolean calculateFlag) {
        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        String gid = tenant.getAccountGid();
        OpponentEntityDO organization = null;
        // 如果传入企业相对方实体,则查询相关信息
        if (StringUtils.isNotBlank(organizationId)) {
            organization = opponentEntityDAO.getByUuid(organizationId);
        }
        long count;
        String scrollId = null;
        OpponentEntityQueryModel opponentEntityQueryModel = new OpponentEntityQueryModel();
        opponentEntityQueryModel.setGid(gid);
        opponentEntityQueryModel.setEntityType(OpponentEntityTypeEnum.INDIVIDUAL.getType());
        opponentEntityQueryModel.setRiskLevel(request.getRiskLevel());
        if (request.getAuthorizeType() != null
                && AuthorizeTypeEnum.ACCEPT.getType() == request.getAuthorizeType()) {
            opponentEntityQueryModel.setAuthorizeTypeList(AuthorizeTypeEnum.getAllAcceptTypes());
        } else {
            opponentEntityQueryModel.setAuthorizeType(request.getAuthorizeType());
        }
        opponentEntityQueryModel.setFuzzyDesc(request.getFuzzyDesc());
        opponentEntityQueryModel.setEntityUniqueId(request.getFuzzyEntityUniqueId());
        opponentEntityQueryModel.setEntityName(request.getFuzzyIndividualName());
        opponentEntityQueryModel.setAttachedEntityType(request.getAttachedEntityType());
        opponentEntityQueryModel.setSocialCreditCode("");
        opponentEntityQueryModel.setPageSize(request.getPageSize());
        opponentEntityQueryModel.setPageNum(request.getPageNum());
        opponentEntityQueryModel.setScrollId(request.getScrollId());
        opponentEntityQueryModel.setUseScroll(request.getUseScroll());
        if (organization != null) {
            opponentEntityQueryModel.setAttachedEntityId(organization.getId());
        }
        populateOpponentMultiBizQuery(tenantOid, tenant.getAccountGid(), operatorOid, opponentEntityQueryModel);
        OpponentEntityQueryResult opponentEntityQueryResult =
                opponentSearchClient.pageQuery(opponentEntityQueryModel);
        count = opponentEntityQueryResult.getTotal();
        if (count == 0) {
            return buildIndividualEmptyResult(tenantOid, gid, operatorOid);
        }

        scrollId = opponentEntityQueryResult.getScrollId();

        if (CollectionUtils.isEmpty(opponentEntityQueryResult.getList())) {
            return buildIndividualEmptyResult(tenantOid, gid, operatorOid);
        }

        // 构造返回结果
        List<OpponentIndividualResponse> individuals =
                opponentEntityQueryResult.getList().stream()
                        .map(OpponentEntityConverter::toIndividualResponse)
                        .collect(Collectors.toList());
        sensorService.opponentIndividualListTracking(tenantOid, operatorOid);
        return OpponentIndividualListResponse.builder()
                .totalSize(count)
                .scrollId(scrollId)
                .individuals(individuals)
                .opponentEntityNums(
                        getOpponentEntityNums(tenantOid, gid, operatorOid, OpponentEntityTypeEnum.INDIVIDUAL.getType()))
                .build();
    }

    private OpponentIndividualListResponse buildIndividualEmptyResult(String tenantOid, String gid, String personOid) {
        return OpponentIndividualListResponse.builder()
                .totalSize(0L)
                .individuals(new ArrayList<>())
                .opponentEntityNums(
                        getOpponentEntityNums(tenantOid, gid, personOid, OpponentEntityTypeEnum.INDIVIDUAL.getType()))
                .build();
    }

    @Override
    public void populateOpponentMultiBizQuery(String tenantOid, String tenantGid,
                                              String personOid, OpponentEntityQueryModel query) {

        if (permissionService.checkLegalPerson(tenantOid, personOid)) {
            // 法人可以看全部想对方
            return;
        }
        // 查询企业是否有业务空间
        boolean have = userCenterService.haveMultiBizZone(tenantOid);
        if (!have) {
            return;
        }
        Map<String, List<DeptBaseInfo>> subjectOidDeptMap =
                userCenterService.allMultiBizDept(tenantOid);

        List<DeptBaseInfo> allDept = subjectOidDeptMap.get(tenantOid);
        if (CollectionUtils.isEmpty(allDept)) {
            return;
        }

        List<String> allDeptIds = allDept.stream().map(DeptBaseInfo::getAutoIncId)
                .map(String::valueOf)
                .collect(Collectors.toList());
        Map<String, List<DeptBaseInfo>> belongSubjectOidDeptMap =
                userCenterService.currentUserMultiBizDept(tenantOid, personOid);
        List<DeptBaseInfo> belongDept = Optional.ofNullable(belongSubjectOidDeptMap.get(tenantOid))
                .orElse(new ArrayList<>());

        List<String> belongDeptIds = belongDept.stream().map(DeptBaseInfo::getAutoIncId)
                .map(String::valueOf)
                .collect(Collectors.toList());
        query.setFilterByMultiBiz(true);
        query.setAllDept(allDeptIds);
        query.setBelongDept(belongDeptIds);
    }

    @Override
    public OpponentIndividualResponse getIndividuals(
            String operatorOid, String tenantOid, String individualId) {
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(operatorOid);
        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        OpponentEntityDO entity = opponentEntityDAO.getByUuid(individualId);
        // 数据不存在报错及越权校验
        if (null == entity
                || !entity.getTenantGid().equals(tenant.getAccountGid())
                || DeletedEnum.YES.code() == entity.getDeleted()) {
            throw new BizContractManagerException(OPPONENT_ENTITY_NOT_EXIST);
        }

        // 进行数据转换
        OpponentIndividualResponse individual =
                OpponentEntityConverter.toIndividualResponse(entity);

        // 获取所属企业信息
        List<OrganizationInfoBO> organizationInfoList = new ArrayList<>();
        List<Long> ids = opponentEntityRelationDAO.queryOrgIdByPersonId(entity.getId());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<OpponentEntityDO> orgList = opponentEntityDAO.getByIdList(ids);
            organizationInfoList =
                    orgList.stream()
                            .map(
                                    p -> {
                                        OrganizationInfoBO info = new OrganizationInfoBO();
                                        info.setOrganizationAccountId(p.getEntityOid());
                                        info.setOrganizationName(p.getEntityName());
                                        info.setOrganizationId(p.getUuid());
                                        info.setAuthorizeType(p.getAuthorizeType());
                                        return info;
                                    })
                            .collect(Collectors.toList());
        }
        individual.setOrganizationList(organizationInfoList);

        // 获取创建来源用户信息
        UserAccountDetail createUser =
                userCenterService.getFatUserAccountDetailByOid(entity.getCreateByOid());
        individual.setCreateContact(createUser.account());

        // 获取流程名称
        if (StringUtils.isNotBlank(individual.getCreateProcessId())) {
            ProcessDO processDO = baseProcessService.getProcess(individual.getCreateProcessId());
            individual.setCreateName(null != processDO ? processDO.getProcessTitle() : null);
        } else {
            individual.setCreateName(userAccount.getAccountName());
        }

        // 如果没有创建流程,则创建名称为用户名称
        if (StringUtils.isBlank(individual.getCreateName())) {
            individual.setCreateName(createUser.getAccountName());
        }

        // 相关合同数量
        individual.setProcessingContractCount(
                getProcessingContractCount(
                        userAccount.buildAccount(), tenant.buildAccount(), entity));
        return individual;
    }

    @AuditLogAnnotation(
            enterpriseSpaceUnique1 = "#tenantOid",
            resourceEntSpaceUnique = "#tenantOid",
            result = "\"成功\"",
            resourceId = "#uuid",
            detailTactics = "1",
            condition = "{{T(com.timevale.mandarin.base.util.StringUtils).isNotBlank(#uuid)}}",
            postHandle = "auditLogOpponentDeleteHandle")
    @Override
    public void deleteOpponentEntity(String uuid, boolean deleteIndividuals, String tenantOid) {
        AuditLogHelper.acceptHeaderFields();
        OpponentEntityDO opponentEntityDO = opponentEntityDAO.getByUuid(uuid);
        if(opponentEntityDO == null){
            return;
        }
        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        if (!Objects.equals(tenant.getAccountGid(), opponentEntityDO.getTenantGid())) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_TENANT_ID_NOT_ACCORDANCE);
        }
        opponentEntityDAO.updateDeleted(uuid, DeletedEnum.YES.code());

        // 多业务 删除资源归属
        ResourceBelongBatchDeleteInput input = new ResourceBelongBatchDeleteInput();
        input.setResourceBelongType(ResourceBelongTypeEnum.OPPONENT.getCode());
        input.setResourceIds(Arrays.asList(uuid));
        resourceBelongClient.deleteResourceBelong(input);

        //解除企业和个人的绑定关系,同时删除个人
        OpponentUpdateMsg opponentUpdateMsg = new OpponentUpdateMsg();
        opponentUpdateMsg.setUuidList(Collections.singletonList(opponentEntityDO.getUuid()));
        opponentUpdateMsg.setHasDel(deleteIndividuals);
        opponentMqProducer.sendDelayMessage(JSONObject.toJSONString(opponentUpdateMsg), OpponentBusinessTagEnum.DELETE.getType(), DelayMsgLevel.ONE);

        LogRecordContext.putVariable(AuditLogConstant.Field.OPPONENT_NAME, opponentEntityDO.getEntityName());
        LogRecordContext.putVariable(AuditLogConstant.Field.OPPONENT_OPERATOR_ID, RequestContextExtUtils.getOperatorId());
    }

    @Override
    public void batchDeleteOpponentEntity(List<String> uuids, boolean deleteIndividuals, String tenantOid) {
        AuditLogHelper.acceptHeaderFields();
        List<OpponentEntityDO> opponentEntityDOS = opponentEntityDAO.getIdsByUuid(uuids);
        if(CollectionUtils.isEmpty(opponentEntityDOS)){
            return;
        }
        OpponentEntityDO opponentEntityDO = opponentEntityDOS.get(0);

        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        if (!Objects.equals(tenant.getAccountGid(), opponentEntityDO.getTenantGid())) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_TENANT_ID_NOT_ACCORDANCE);
        }

        opponentEntityDAO.batchUpdateDeleted(uuids, DeletedEnum.YES.code());

        // 解除授权关系
        ResourceBelongBatchDeleteInput input = new ResourceBelongBatchDeleteInput();
        input.setResourceBelongType(ResourceBelongTypeEnum.OPPONENT.getCode());
        input.setResourceIds(uuids);
        resourceBelongClient.deleteResourceBelong(input);

        //解除企业和个人的绑定关系,联系人无上限，数据多时会比较慢，通过消息异步进行删除联系人, 延迟1秒，防止更新还没入库
        OpponentUpdateMsg opponentUpdateMsg = new OpponentUpdateMsg();
        opponentUpdateMsg.setUuidList(uuids);
        opponentUpdateMsg.setHasDel(deleteIndividuals);
        opponentMqProducer.sendDelayMessage(JSONObject.toJSONString(opponentUpdateMsg), OpponentBusinessTagEnum.DELETE.getType(), DelayMsgLevel.ONE);

        // 记录审计日志
        auditLogRecordService.recordDeleteBatchOpponent(opponentEntityDOS);
    }

    @Override
    public OpponentEntityBO getEntity(String uuid) {
        OpponentEntityDO entityDO = opponentEntityDAO.getByUuid(uuid);
        if (entityDO == null) {
            throw new BizContractManagerException(OPPONENT_ENTITY_NOT_EXIST);
        }

        OpponentEntityBO result = OpponentEntityBO.builder().entityOid(entityDO.getEntityOid()).
                entityGid(entityDO.getEntityGid()).entityType(OpponentEntityTypeEnum.getByType(entityDO.getEntityType()))
                .uuid(entityDO.getUuid())
                .tenantOid(entityDO.getTenantOid())
                .tenantGid(entityDO.getTenantGid())
                .build();
        return result;
    }

    @Override
    public void updateIndividualOid(String phone, String email, String entityOid) {
        if (StringUtils.isEmpty(phone) && StringUtils.isEmpty(email)) {
            return;
        }
        List<OpponentEntityDO> entityDOS = opponentEntityDAO.listByMultiEntityUniqueId(phone, email);
        if (CollectionUtils.isEmpty(entityDOS)) {
            return;
        }
        List<Long> batchUpdateEntityIds = entityDOS.stream().map(OpponentEntityDO::getId).collect(Collectors.toList());
        opponentEntityDAO.batchUpdateEntityOid(entityOid, batchUpdateEntityIds);
    }

    @Override
    public void renameOpponentOrganizations(String organizationOid, String organizationGid, String organizationName) {
        if (StringUtils.isBlank(organizationGid)) {
            return;
        }

        List<OpponentEntityDO> entities = opponentEntityDAO.listEntitiesByGid(organizationGid);

        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        for (OpponentEntityDO entity : entities) {

            if (StringUtils.isBlank(entity.getEntityOid()) || StringUtils.isBlank(entity.getEntityName())) {
                continue;
            }

            if (entity.getEntityOid().equals(organizationOid) && !entity.getEntityName().equals(organizationName)) {
                try {
                    OpponentEntityDO updateEntityNameDO = new OpponentEntityDO();
                    updateEntityNameDO.setId(entity.getId());
                    updateEntityNameDO.setEntityName(organizationName);
                    updateEntityNameDO.setEntityUniqueId(organizationName);
                    opponentEntityDAO.updateEntityName(updateEntityNameDO);
                } catch (Exception e) {
                    /** 如果A企业添加了B企业作为其相对方，然后B企业在esign变更企业名前A企业添加了B企业变更后的企业名B'作为其相对方
                     *  这时候B变更为B'会导致更新A的相对方企业B失败，唯一键冲突
                     * */
                    log.warn("rename organization failed tenantId:{} opponentEntityGid:{} entityName:{}",
                            entity.getTenantGid(), entity.getTenantOid(), organizationName);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void personAccountUpdateOnChange(String personAccountOid, String personAccountGid, AccountUpdateDTO accountUpdateDTO) {
        if (StringUtils.isBlank(personAccountGid)) {
            return;
        }

        List<OpponentEntityDO> entities = opponentEntityDAO.listByEntityUniqueIdAndType(
                                                                    accountUpdateDTO.getSourceAccount().getContact(),
                                                                    OpponentEntityTypeEnum.INDIVIDUAL.getType());

        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        log.info("personAccountUpdateOnChange:{} entities size:{}", accountUpdateDTO, entities.size());
        UserAccountDetail userAccountDetail = userCenterService.getUserAccountDetailByOid(personAccountOid);
        switch (accountUpdateDTO.getUpdateType()) {
            case UNBIND:
                String newContact = accountUpdateDTO.getSourceAccount().getContactType().equals(AccountContactTypeEnum.EMAIL)?
                        userAccountDetail.getAccountMobile():userAccountDetail.getAccountEmail();
                batchUpdateOpponentEntityContacts(entities, newContact);
                break;
            case CHANGE_CONTACT:
                batchUpdateOpponentEntityContacts(entities, accountUpdateDTO.getSourceAccount().getContact());
                break;
            default:
                break;
        }
    }

    private void batchUpdateOpponentEntityContacts(List<OpponentEntityDO> entities,  String targetContact) {
        if (StringUtils.isBlank(targetContact)) {
            return;
        }
        List<OpponentEntityDO> existEntities = opponentEntityDAO.listByEntityUniqueIdAndType(targetContact,  OpponentEntityTypeEnum.INDIVIDUAL.getType());
        Map<String, OpponentEntityDO> existEntityGidMap = existEntities.stream().filter(item -> StringUtils.isNoneBlank(item.getTenantGid()))
                .collect(Collectors.toMap(i->i.getTenantGid(), j->j));

        Set<Long> toUpdateUniqueEntityIdItems = Sets.newHashSet();
        Set<Long> toDeleteItems = Sets.newHashSet();

        entities.stream().filter(item->StringUtils.isNotBlank(item.getTenantGid())).forEach(entity->{
            if(existEntityGidMap.containsKey(entity.getTenantGid())) {
                toDeleteItems.add(entity.getId());
            }else {
                toUpdateUniqueEntityIdItems.add(entity.getId());
            }
        });

        if(CollectionUtils.isNotEmpty(toDeleteItems)){
            opponentEntityDAO.batchDelete(new ArrayList<>(toDeleteItems));

            String deletedIds = toDeleteItems.stream().map(item->item.toString()).collect(Collectors.joining(","));
            log.info("delete ids:{}",deletedIds);
        }

        if(CollectionUtils.isNotEmpty(toUpdateUniqueEntityIdItems)) {
            opponentEntityDAO.batchUpdateIndividualsUniqueEntityIdByIdList(new ArrayList<>(toUpdateUniqueEntityIdItems), targetContact);
            String updatedIds = toUpdateUniqueEntityIdItems.stream().map(item->item.toString()).collect(Collectors.joining(","));
            log.info("updated ids:{} to {}", updatedIds, targetContact);
        }
    }

    /**
     * 根据相对方实体id获取每个实体下的成员人数
     *
     * @param gid 企业gid
     * @param idList 实体id列表
     * @return 返回成员人数mao key为实体id,value为成员人数
     */
    private Map<String, Long> getMemberCountMap(String gid, List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new HashMap<>();
        }
        OpponentEntityGroupByOrgQueryModel model = new OpponentEntityGroupByOrgQueryModel();
        model.setGid(gid);
        model.setOrgIdList(idList);
        try {
            OpponentEntityGroupByOrgQueryResult result =
                    opponentSearchClient.opponentGroupByOrg(model);
            if (result == null || CollectionUtils.isEmpty(result.getCountMap())) {
                return new HashMap<>();
            }
            return result.getCountMap();
        } catch (Exception e) {
            log.warn("query es opponent group count error", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取相关合同数量
     *
     * @param subject 当前企业主体
     * @param person 当前操作人
     * @param opponentEntity 相对方实体
     * @return 相关合同数量
     */
    private Integer getProcessingContractCount(Account person, Account subject, OpponentEntityDO opponentEntity) {
        // 相对方实体为空,直接返回0
        if (StringUtils.isBlank(opponentEntity.getEntityOid())) {
            return 0;
        }
        Account opponentEntityAccount = new Account();
        opponentEntityAccount.setOid(opponentEntity.getEntityOid());
        opponentEntityAccount.setGid(opponentEntity.getEntityGid());
        opponentEntityAccount.setOrgan(
                OpponentEntityTypeEnum.ORGANIZATION.getType() == opponentEntity.getEntityType());
        // 相对方分离出来
        //build requestBody
        OpponentProcessQueryParam build = OpponentProcessQueryParam
                .builder()
                .person(person)
                .subject(subject)
                .opponentEntityAccount(opponentEntityAccount)
                .build();
        build.setPageNum(1);
        build.setPageSize(1);

        BuildQueryAllHavePermissionProcessResultDTO havePermissionQueryParamDTO =
                processUserHavePermissionDataService.buildCommonQuery(subject.getOid(), person.getOid());
        build.setSubSubjectQueryParam(havePermissionQueryParamDTO.getWaitingGroupQueryParam());
        BuildQueryMenuAllProcessResultDTO queryMenuAllProcessResultDTO =
                havePermissionQueryParamDTO.getQueryMenuAllProcessResultDTO();
        build.setNeedQueryPersonGrouping(queryMenuAllProcessResultDTO.getSearchPersonRelated());
        build.setMenuIdList(queryMenuAllProcessResultDTO.getMenuIds());
        return (int) esClient.opponentProcessQuery(build).getTotal();
    }

    @Override
    public void checkEntityLimit(String operatorOid, String tenantOid, Integer opponentEntityType) {
        checkCreateLimit(tenantOid, null, operatorOid, opponentEntityType);
    }

    /**
     * @param tenantGid
     * @param tenantOid
     * @param entityOid
     * @param entityGid
     * @param name
     * @param contact
     * @param operatorOid
     * @param operatorGid
     * @param authorizeType
     * @param desc
     * @param createProcessId
     * @return
     */
    public OpponentEntityDO buildOpponentEntity(Integer entityType,
                                                String tenantGid,
                                                String tenantOid,
                                                String entityOid,
                                                String entityGid,
                                                String name,
                                                String contact,
                                                String operatorOid,
                                                String operatorGid,
                                                Integer authorizeType,
                                                String desc,
                                                String createProcessId,
                                                String socialCreditNo,
                                                String legalRepresentativeName,
                                                Integer creditCodeType)
    {
        return OpponentEntityDO.builder()
                .tenantGid(tenantGid)
                .tenantOid(tenantOid)
                .createByGid(operatorGid)
                .createByOid(operatorOid)
                .entityType(entityType)
                .modifyByGid(operatorGid)
                .modifyByOid(operatorOid)
                .authorizeType(authorizeType)
                .entityOid(entityOid)
                .entityGid(entityGid)
                .entityUniqueId(Objects.isNull(contact) ? name : contact)
                .uuid(UUIDUtil.genUUID())
                .description(desc)
                .entityName(name)
                .createProcessId(createProcessId)
                .socialCreditCode(socialCreditNo)
                .legalRepresentativeName(legalRepresentativeName)
                .creditCodeType(creditCodeType)
                .build();
    }

    @Override
    public void validOrganizationCreateRequest(OpponentOrganizationCreateRequest createRequest) {

        String organizationName = createRequest.getOrganizationName();
        AssertX.isTrue(StringUtils.isNotBlank(organizationName) &&
                        organizationName.length() <= opponentConfigCenter.organizeNameMaxLength,
                "企业名称最大长度 <= " + opponentConfigCenter.organizeNameMaxLength);
        AssertX.isTrue(StringUtils.isNotBlank(organizationName) &&
                        organizationName.length() >= opponentConfigCenter.organizeNameMinLength,
                "企业名称最小长度 >= " + opponentConfigCenter.organizeNameMinLength);

        // other
        ValidationUtil.validateBean(createRequest);

    }

    @Override
    public void syncOpponentEntityGid(String oid) {

        // 观察一段时间没问题，这个开关去掉就好了
        if (!opponentConfigCenter.syncGidSwitch()) {
            return;
        }

        if (StringUtils.isBlank(oid)) {
            log.warn("opponent syncOpponentEntityGid oid is null");
            return;
        }

        List<OpponentEntityDO> opponentEntityDOS =
                opponentEntityDAO.listByEntityOidAndEntityType(oid, OpponentEntityTypeEnum.INDIVIDUAL.getType());
        if (CollectionUtils.isEmpty(opponentEntityDOS)) {
            return;
        }

        // 没有gid的 entity
        List<OpponentEntityDO> noGidOpponentEntity = opponentEntityDOS.stream()
                .filter(elm -> StringUtils.isBlank(elm.getEntityGid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noGidOpponentEntity)) {
            return;
        }

        UserAccount account = userCenterService.getUserAccountBaseByOid(oid);
        if (null == account || StringUtils.isBlank(account.getAccountGid())) {
            log.warn("opponent syncOpponentEntityGid oid : {} gid is null", oid);
            // 用户中心更新gid 是异步的，如果没有gid重试进行更新
            throw new OpponentEntitySyncGidNeedRetryException();
        }

        noGidOpponentEntity.forEach(opponentEntity -> {
            OpponentEntityDO opponentEntityDO = new OpponentEntityDO();
            opponentEntityDO.setId(opponentEntity.getId());
            opponentEntityDO.setEntityGid(account.getAccountGid());
            opponentEntityDO.setAuthorizeType(AuthorizeTypeEnum.SUPPOSE_ACCEPT.getType());
            log.info("opponent syncOpponentEntityGid update data : {}", JSON.toJSONString(opponentEntityDO));
            opponentEntityDAO.updateById(opponentEntityDO);
        });
    }

    @Override
    public boolean addOpponentByProcess(String processId) {
        log.info("opponent addOpponentByProcess processId : {}", processId);
        QueryByProcessIdResult queryByProcessIdResult = esClient.queryById(processId);
        if (queryByProcessIdResult == null || null == queryByProcessIdResult.getProcessInfoTotalInfo()) {
            log.info("processInfoTotalInfo is null processId: {}", processId);
            return Boolean.FALSE;
        }
        ProcessInfoTotalInfo processInfoTotalInfo = queryByProcessIdResult.getProcessInfoTotalInfo();
        //发起人账户信息
        ProcessAccount userAccountInitiator = processInfoTotalInfo.getInitiator();
        //发起主体账户信息
        Account accountSubject = userAccountInitiator.getSubject();
        //发起主体是企业才添加
        if (Boolean.TRUE.equals(accountSubject.getOrgan())) {
            //参与人
            List<TaskInfoTotalInfo> taskInfo = processInfoTotalInfo.getTaskInfo();
            List<AccountBase> participant = processInfoTotalInfo.getParticipant();

            if (CollectionUtils.isEmpty(participant)) {
                participant = new ArrayList<>();
                for (TaskInfoTotalInfo taskInfoTotalInfo : taskInfo) {
                    participant.add(taskInfoTotalInfo.getExecute());
                }
            }

            for (AccountBase accountBase : participant) {
                try {
                    Boolean result = opponentEntityAdapter.createOpponentEntity(OpponentAddSourceEnum.DEVELOP,
                            processId, userAccountInitiator, accountBase);
                    if (!result) {
                        return Boolean.FALSE;
                    }
                } catch (BizContractManagerException e) {
                    log.info("process start complete create opponent entity msg:{},租户: {},相对方{}",
                            e.getMessage(), JSON.toJSON(userAccountInitiator), JSON.toJSON(accountBase), e);
                }
            }
        }
        return Boolean.TRUE;
    }


    /**
     * 特定企业不查第三方，直接返回结果
     *
     *    以下三个字段
     *    name  杭州天谷信息科技有限公司
     *    creditCode  913301087458306077
     *    legalPersonName  金宏洲
     */
    private OpponentEnterpriseInfoResponse mockQueryThird(String orgName) {

        try {
            String mock = ConfigService.getAppConfig().getProperty(MOCK_TENANT_QUERY, null);
            if (StringUtils.isBlank(mock)) {
                return null;
            }

            List<OpponentEnterpriseInfoResponse> list = JSON.parseArray(mock, OpponentEnterpriseInfoResponse.class);
            Optional<OpponentEnterpriseInfoResponse> optional = list.stream()
                    .filter(elm -> Objects.equals(elm.getName(), orgName)).findFirst();

            return optional.orElse(null);
        } catch (Exception e) {
            log.info("opponent mockQueryThird", e);
            return null;
        }
    }

    private void updateOpponentRelation(
            List<OpponentEntityDO> orgList, OpponentEntityDO person) {
        opponentEntityRelationDAO.deleteByPersonId(Collections.singletonList(person.getId()));
        if(CollectionUtils.isEmpty(orgList)){
            return;
        }
        List<OpponentEntityRelationDO> addRelationList =
                orgList.stream()
                        .map(
                                p -> {
                                    OpponentEntityRelationDO relation =
                                            new OpponentEntityRelationDO();
                                    relation.setOrgId(p.getId());
                                    relation.setOrgUuid(p.getUuid());
                                    relation.setPersonId(person.getId());
                                    relation.setPersonUuid(person.getUuid());
                                    return relation;
                                })
                        .collect(Collectors.toList());
        opponentEntityRelationDAO.insert(addRelationList);
    }
}

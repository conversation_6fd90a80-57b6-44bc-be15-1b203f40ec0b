package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.contractmanager.common.service.enums.SubProcessTypeEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.HbaseProcessDataAsyncCollectException;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ContractProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ContractProcessChangeProducer;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSignTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/5/10
 */
@Slf4j
@Component
public class ProcessStatusChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessReadClient processQueryClient;
    @Autowired
    private ContractProcessChangeProducer contractProcessChangeProducer;

    @Autowired
    private BaseProcessService baseProcessService;
    
    @Autowired
    private LockFactory lockFactory;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processTopicName(), ProcessChangeTagEnum.PROCESS_STATUS_CHANGE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessChangeMsgEntity entity = JsonUtils.json2pojo(data, ProcessChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {

        String processId = collectContext.getProcessId();
        ContractProcessUpdateParam updateInput = processDataBuilder.buildUpdateInfoParam(processId, ProcessDataCollectBizSceneConstants.PROCESS_STATUS_CHANGE);
        log.info(LOG_PREFIX + " status change input : {}", JsonUtils.obj2json(updateInput));

        updateInput.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_STATUS_CHANGE);
        contractProcessWriteClient.updateByProcessId(updateInput);

        //特殊处理，更新节点拒签状态
        updateRejectTaskInfo(processId, updateInput);

        if (ProcessStatusEnum.DONE.getStatus().equals(updateInput.getProcessStatus())) {
            ProcessChangeMsgEntity processChangeMsgEntity = new ProcessChangeMsgEntity();
            processChangeMsgEntity.setProcessId(collectContext.getProcessId());
            contractProcessChangeProducer.sendMessageDelay(processChangeMsgEntity, ContractProcessChangeTagEnum.SIGN_FINISH.getTag());
            return;
        }

        List<Integer> endProcessStatus = com.timevale.contractmanager.common.service.enums.ProcessStatusEnum.optionalFinalStatusList().stream()
                .filter(item -> !com.timevale.contractmanager.common.service.enums.ProcessStatusEnum.DONE.equals(item))
                .map(com.timevale.contractmanager.common.service.enums.ProcessStatusEnum::getStatus).collect(Collectors.toList());

        if (endProcessStatus.contains(updateInput.getProcessStatus())){
            ProcessChangeMsgEntity processChangeMsgEntity = new ProcessChangeMsgEntity();
            processChangeMsgEntity.setProcessId(collectContext.getProcessId());
            contractProcessChangeProducer.sendMessageDelay(processChangeMsgEntity, ContractProcessChangeTagEnum.END.getTag());
        }

        // 620 statusChange 不在修改印章信息，改为 signTaskChange 里修改

        //        if (!processDataBuilder.shouldWriteSeal(updateInput.getProcessStatus())) {
        //            // 不需要写印章信息
        //            updateInput.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_STATUS_CHANGE);
        //            contractProcessWriteClient.updateByProcessId(updateInput);
        //            return;
        //        }

//        // 需要修改印章信息
//        Elock lock =
//                LockFactory.getReentrantLock(ProcessDataCollectSupport.taskInfoChangeLockKey(processId));
//        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
//            try {
//                ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);
//                List<ContractProcessSignTaskDTO> signTasks = contractProcessDTO.getSignTasks();
//                if (CollectionUtils.isEmpty(signTasks)) {
//                    return;
//                }
//                //
//                List<ContractProcessSignTaskParam> taskParamList =
//                        ProcessDataCollectConverter.signTaskDTO2ParamList(signTasks);
//                if (CollectionUtils.isEmpty(taskParamList)) {
//                    return;
//                }
//                //填充印章信息
//                processDataBuilder.fillSealInfo(taskParamList, taskParamList.get(0).getFlowId());
//
//                // 覆盖更新加进去
//                updateInput.setSignTasks(taskParamList);
//                updateInput.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_STATUS_CHANGE);
//                contractProcessWriteClient.updateByProcessId(updateInput);
//
        //            } finally {
        //                lock.unlock();
        //            }
        //        } else {
        //            // 抛异常重试
        //            throw new HbaseProcessDataAsyncCollectException();
        //        }

    }

    private void updateRejectTaskInfo(String processId, ContractProcessUpdateParam updateInput) {
        //特殊处理，记录节点拒签状态
        if (!updateInput.getProcessStatus().equals(ProcessStatusEnum.REJECT.getStatus())) {
            return;
        }

        boolean hasSign =  baseProcessService.hasSubProcess(processId, SubProcessTypeEnum.SIGN);
        if (!hasSign) {
            return;
        }

        Elock lock = lockFactory.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(processId));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);
                List<ContractProcessSignTaskDTO> signTasks = contractProcessDTO.getSignTasks();
                if (CollectionUtils.isEmpty(signTasks)) {
                    return;
                }
                List<ContractProcessSignTaskParam> taskParamList =
                        ProcessDataCollectConverter.signTaskDTO2ParamList(signTasks);
                if (CollectionUtils.isEmpty(taskParamList)) {
                    return;
                }
                List<ContractProcessSignTaskParam> newSignTaskDTOS =
                        processDataBuilder.buildProcessAllSignTaskList(processId, updateInput.getProcessStatus());
                if (CollectionUtils.isEmpty(newSignTaskDTOS)) {
                    return;
                }
                Map<String, ContractProcessSignTaskParam> taskIdMap = newSignTaskDTOS.stream().collect(Collectors.toMap(a -> a.getTaskId(), b -> b));
                for (ContractProcessSignTaskParam taskParam : taskParamList) {
                    ContractProcessSignTaskParam param = taskIdMap.get(taskParam.getTaskId());
                    if (param == null) {
                        continue;
                    }
                    taskParam.setStatus(param.getStatus());
                }

                // 覆盖更新
                updateInput.setSignTasks(taskParamList);
                updateInput.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_STATUS_CHANGE);
                contractProcessWriteClient.updateByProcessId(updateInput);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }
}

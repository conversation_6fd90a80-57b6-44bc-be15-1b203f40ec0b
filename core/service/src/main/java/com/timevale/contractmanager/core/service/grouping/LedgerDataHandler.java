package com.timevale.contractmanager.core.service.grouping;


import com.timevale.contractmanager.core.model.dto.ledger.CreateLedgerProcessInfoDataInputDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdsResult;

import java.util.List;
import java.util.Map;

/**
 * 台账数据处理器
 *
 * <AUTHOR>
 * @since 2023-10-20 16:25
 */
public interface LedgerDataHandler {
    /**
     * 滚动或分页查询台账表单
     *
     * @param formId 台账id
     * @param pageNum 当前页
     * @param pageSize 页大小
     * @param subject 空间信息
     * @param useScroll 使用滚动分页
     * @param scrollId 滚动分页id
     * @return 流程列表信息
     */
    QueryByProcessIdsResult scrollPageFormWithLedgerProcess(
            String formId,
            Integer pageNum,
            Integer pageSize,
            UserAccount subject,
            boolean useScroll,
            String scrollId);

    /**
     * 创建台账数据
     *
     * @param inputDTO 请求参数
     * @return 多行合同数据
     */
    List<List<Object>> createLedgerProcessInfoData(CreateLedgerProcessInfoDataInputDTO inputDTO);

    /**
     * 创建台账表头
     *
     * @param aiDataKeyList ai识别的表头
     * @param dataType 数据类型
     * @param menuAll 全部分类
     * @param wasParent 是否成为主企业
     * @return 创建好的台账表头
     */
    List<List<String>> createLedgerHead(Map<String, String> aiDataKeyList,
                                        Boolean dataType, Boolean menuAll, Boolean wasParent,
                                        Integer pCount);
}

package com.timevale.contractmanager.core.service.processstart.impl;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.core.service.component.FlowTemplateModelConverter;
import com.timevale.contractmanager.core.service.mq.model.CommonBatchProcessStartMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ProcessMqProducer;
import com.timevale.contractmanager.core.service.process.builder.SaasCommonTaskBuilder;
import com.timevale.contractmanager.core.service.processstart.ProcessStartBatchService;
import com.timevale.contractmanager.core.service.processstart.impl.base.ProcessStartCoreBaseService;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContextService;
import com.timevale.contractmanager.core.service.util.StructComponentUtil;
import com.timevale.doccooperation.service.enums.DocTemplateFromEnum;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.doccooperation.service.model.CooperationerStruct;
import com.timevale.doccooperation.service.model.DocTemplate;
import com.timevale.doccooperation.service.result.FlowTemplateStructResult;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.docmanager.service.enums.StructComponentTypeEnum;
import com.timevale.docmanager.service.model.SignAreaContextExt;
import com.timevale.docmanager.service.model.StructComponent;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskAddInput;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskAddBean;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskBizInfo;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM;
import static com.timevale.contractmanager.core.model.enums.ProcessExcelEnum.TASK_NAME;
import static com.timevale.contractmanager.core.service.mq.producer.ProducerWhatEnum.BATCH_COMMON_PROCESS_START;
import static com.timevale.doccooperation.service.model.CommonStartExtensionKeyConstants.SIGN_PRE_FLOW_ID;

/**
 * 批量发起业务类
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Slf4j
@Service
public class ProcessNormalStartBatchServiceImpl extends ProcessStartCoreBaseService
        implements ProcessStartBatchService {

    @Autowired ProcessMqProducer processMqProducer;
    @Autowired MapperFactory mapperFactory;
    @Autowired SaasCommonClient saasCommonClient;
    @Autowired SaasCommonTaskBuilder saasCommonTaskBuilder;
    @Autowired FlowTemplateModelConverter flowTemplateModelConverter;
    @Autowired ProcessStartContextService processStartContextService;

    @Override
    public List<ProcessStartType> startTypes() {
        return ProcessStartType.normalBatchTypes();
    }

    /**
     * 发起流程， start方法调用之前， 需先调用checkParamValidity， 这块逻辑在ProcessStartHandler内部封装
     *
     * @param request
     * @param
     * @return
     */
    @Override
    public ProcessStartResult start(ProcessStartCoreRequest request) {
        // 获取发起场景
        ProcessStartScene startScene = ProcessStartScene.getScene(request.getStartScene());
        // 拆分批量参与方和非批量参与方
        List<ParticipantBO> batchParticipants = Lists.newArrayList();
        Set<ParticipantBO> normalParticipantDOs = Sets.newHashSet();
        for (ParticipantBO participant : request.getParticipants()) {
            if (ParticipantModeEnum.isOrSign(participant.getParticipantMode())
                    || CollectionUtils.isEmpty(participant.getInstances())
                    || participant.getInstances().size() == 1) {
                normalParticipantDOs.add(participant);
                continue;
            }
            batchParticipants.add(participant);
        }
        // 如果批量参与方为空， 报错
        if (CollectionUtils.isEmpty(batchParticipants)) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "缺少批量参与方");
        }
        // 获取批量流程数
        int processSize = request.processCount();
        // 发起必要参数校验
        validParam(request, processSize);
        // 校验批量发起参数
        checkBatchStartParam(request, processSize);
        // 批量直接发起场景，不支持指定processId及flowId
        request.setAssignFlowId(null);
        request.setAssignProcessId(null);
        request.getStartSignExtendMap().put(SIGN_PRE_FLOW_ID, null);
        // 模板发起场景， 查询模板控件信息
        List<DocTemplate> docTemplates;
        FlowTemplateStructResult flowTemplateStructs;
        if (StringUtils.isNotBlank(request.getFlowTemplateId())) {
            String flowTemplateId = request.getFlowTemplateId();
            UserAccountDetail tenant = request.getTenantAccount();
            GetFlowTemplateResult flowTemplateResult = processStartContextService.getFlowTemplateResult(flowTemplateId, tenant);
            docTemplates = flowTemplateResult.getDocTemplates();
            flowTemplateStructs = processStartContextService.getFlowTemplateStructs(flowTemplateId, tenant);
        } else {
            docTemplates = flowTemplateModelConverter.fileToDocTemplate(request.getContracts());
            flowTemplateStructs = new FlowTemplateStructResult();
        }

        // 构建任务中心需要的入参
        List<String> uniCodeList = Lists.newArrayList();
        List<TaskAddBean> taskAddBeans = Lists.newArrayList();
        List<String> processStartMsgList = Lists.newArrayList();
        for (int i = 0; i < processSize; i++) {
            // 获取首个批量参与方实例信息， 用于后续获取任务名称
            ParticipantInstanceBO instanceBO = batchParticipants.get(0).getInstances().get(i);

            TaskAddBean taskAddBean =
                    saasCommonTaskBuilder.buildProcessTaskAddBean(
                            request.getProcessName(),
                            request.getInitiatorAccount().getAccountOid(),
                            instanceBO.getSubTaskName(),
                            startScene,
                            uniCodeList,
                            request.needFill());
            List<ParticipantBO> processParts;
            // 多对多批量发起场景优先标题，其次参与方信息， 最后控件信息
            if (batchParticipants.size() > 1) {
                // 任务中exportInfo追加Excel标题
                taskAddBean.getBizInfo().addExportInfo(0, TASK_NAME.getName(), taskAddBean.getName(), 2);
                // 任务中追加批量参与方信息，同时填充exportInfo
                processParts = buildBatchParticipantAndAppendToTask(i, batchParticipants, taskAddBean);
            } else {
                // 单方批量发起场景优先参与方信息， 其次标题，最后控件信息
                // 任务中追加批量参与方信息，同时填充exportInfo
                processParts = buildBatchParticipantAndAppendToTask(i, batchParticipants, taskAddBean);
                // 任务中exportInfo追加Excel标题
                taskAddBean.getBizInfo().addExportInfo(taskAddBean.getBizInfo().getExportInfos().size(), TASK_NAME.getName(), taskAddBean.getName(), 2);
            }
            // 设置下预填内容
            fillDocTemplateStructInfo(docTemplates, flowTemplateStructs, instanceBO.getPreFillValues(), taskAddBean);

            taskAddBeans.add(taskAddBean);

            // 构建发起基本参数，参与方中仅包含非批量参与方，由于需要埋点，构造业务发起数据
            ProcessStartCoreRequest batchBizRequest =
                    mapperFactory.getMapperFacade().map(request, ProcessStartCoreRequest.class);
            batchBizRequest.setParticipants(Lists.newArrayList(normalParticipantDOs));
            batchBizRequest.setProcessName(taskAddBean.getName());
            batchBizRequest.setProcessGroupName(request.getProcessName());
            batchBizRequest.setBizGroupId(request.getBizGroupId());

            // 构建发起流程消息结构体
            CommonBatchProcessStartMsgEntity msgEntity = new CommonBatchProcessStartMsgEntity();
            msgEntity.setRequest(batchBizRequest);
            msgEntity.setParticipants(processParts);
            msgEntity.setUniCode(taskAddBean.getBizId());
            // 批量发起场景一个instance代表的是一个流程，所以三方业务id在instance里
            msgEntity.getRequest().setBizProcessId(instanceBO.getSubTaskBizId());
            msgEntity.getRequest().setBizProcessType(instanceBO.getSubTaskBizType());
            processStartMsgList.add(JsonUtils.obj2json(msgEntity));
        }

        // 批量添加任务到任务中心
        SaasTaskAddInput taskAddInput = new SaasTaskAddInput();
        taskAddInput.setGroupId(request.getProcessGroupId());
        taskAddInput.setGroupName(request.getProcessName());
        taskAddInput.setTasks(taskAddBeans);
        saasCommonClient.addTasks(taskAddInput);

        // 发送流程发起消息
        processStartMsgList.forEach(
                i -> processMqProducer.sendMessage(BATCH_COMMON_PROCESS_START, i, null));

        return ProcessStartResult.builder()
                .processId("")
                .resultUrl(request.getRedirectUrl())
                .longResultUrl(request.getRedirectUrl())
                .flowId("")
                .groupId(request.getProcessGroupId())
                .build();
    }

    /**
     * 组装批量参与方信息并追加到任务中
     * @param index
     * @param batchParticipants
     * @param taskAddBean
     * @return
     */
    private List<ParticipantBO> buildBatchParticipantAndAppendToTask(
            int index, List<ParticipantBO> batchParticipants, TaskAddBean taskAddBean) {
        List<ParticipantBO> processParts = Lists.newArrayList();
        for (ParticipantBO participant : batchParticipants) {
            ParticipantInstanceBO participantInstance = participant.getInstances().get(index);
            if (null == participantInstance) {
                throw new BizContractManagerException(
                        PROCESS_ILLEGAL_PARAM, participant.getParticipantLabel() + "用户信息缺失");
            }
            // 设置参与人
            saasCommonTaskBuilder.putParticipant(participant, taskAddBean, participantInstance);
            // 构建批量参与方， 设置参与方实例为当前实例
            ParticipantBO copyParticipant = new ParticipantBO(participant);
            copyParticipant.setInstances(Arrays.asList(participantInstance));
            processParts.add(copyParticipant);
        }
        //构建动态的抄送方
        for (ParticipantBO participant : batchParticipants) {
            ParticipantInstanceBO participantInstance = participant.getInstances().get(index);
            if (CollectionUtils.isEmpty(participantInstance.getCcs())) {
                continue;
            }
            List<UserBO> ccUserBOS = participantInstance.getCcs();
            int ccIndex = 1;
            for (UserBO userBO : ccUserBOS) {
                saasCommonTaskBuilder.putCc(taskAddBean, userBO, ccIndex);
                ccIndex++;
            }
        }
        return processParts;
    }

    /**
     * 填充模板文件控件信息
     *
     * @param docTemplates
     * @param flowTemplateStructs
     * @param preFillValues
     * @param taskAddBean
     */
    private void fillDocTemplateStructInfo(
            List<DocTemplate> docTemplates,
            FlowTemplateStructResult flowTemplateStructs,
            Map<String, Object> preFillValues,
            TaskAddBean taskAddBean) {
        Map<String, List<StructComponent>> commonStructs = flowTemplateStructs.getCoomonStructs();
        for (DocTemplate docTemplate : docTemplates) {

            Comparator<StructComponent> orderCompare = getStructOrderCompare(docTemplate.getFrom());
            // 遍历协作方控件， 如果协作方下控件列表为空， 跳过
            if (CollectionUtils.isNotEmpty(flowTemplateStructs.getCooperationerStructs())) {
                // 遍历下协作方下的控件列表
                for (CooperationerStruct cooperationerStruct :
                        flowTemplateStructs.getCooperationerStructs()) {
                    if (CollectionUtils.isEmpty(cooperationerStruct.getStructs())) {
                        continue;
                    }
                    // 获取该文件下的控件列表
                    List<StructComponent> structs =
                            cooperationerStruct.getStructs().get(docTemplate.getTemplateId());
                    String label = cooperationerStruct.getCooperationerLabel();
                    fillTaskStructCell(structs, orderCompare, label, taskAddBean.getBizInfo(), preFillValues);
                }
            }
            // 遍历公共控件(公共控件在后)
            List<StructComponent> structs =
                    null == commonStructs ? null : commonStructs.get(docTemplate.getTemplateId());
            // 处理合同编号控件
            structs = StructComponentUtil.handleContractNumStruct(docTemplate, structs);
            if (CollectionUtils.isNotEmpty(structs)) {
                fillTaskStructCell(structs, orderCompare, "", taskAddBean.getBizInfo(), preFillValues);
            }
        }
    }

    /**
     * 按文件类型，构建文件上控件的排序规则
     * @param from
     * @return
     */
    private Comparator<StructComponent> getStructOrderCompare(Integer from) {
        //构建order排序规则
        if(Objects.equals(DocTemplateFromEnum.DYNAMIC_FILE.getFrom(), from)
                || Objects.equals(DocTemplateFromEnum.STATIC_EPAAS_DOC.getFrom(), from)
                || Objects.equals(DocTemplateFromEnum.DYNAMIC_EPAAS_DOC.getFrom(), from)){
            return Comparator.comparing(
                    (StructComponent struct) -> {
                        Map<String, Object> structExtMap = JsonUtils.json2map(struct.getContext().getExt());
                        Object order = structExtMap.getOrDefault("order", 0);
                        return (Integer)order;
                    });
        }

        //构建坐标排序规则
        return Comparator.comparing(
                        (StructComponent struct) -> struct.getContext().getPos().getPage())
                .thenComparing(
                        Comparator.comparing(
                                        (StructComponent struct) ->
                                                struct.getContext().getPos().getY())
                                .reversed())
                .thenComparing(struct -> struct.getContext().getPos().getX());
    }

    /**
     * 构建控件excel 单元格
     *
     * @param structs 要写入的控件列表
     * @param participantLabel 参与方名称
     */
    protected void fillTaskStructCell(
            List<StructComponent> structs,
            Comparator<StructComponent> orderCompare,
            String participantLabel,
            TaskBizInfo taskBizInfo,
            Map<String, Object> preFillValues) {

        if (CollectionUtils.isEmpty(structs)) {
            return;
        }

        participantLabel = StringUtils.isNotBlank(participantLabel) ? participantLabel : "不限定";

        // 按照从上到下 从左到右 排个序
        structs.sort(orderCompare);
        Set<String> structGroupKeys = Sets.newHashSet();
        for (int i = 1; i <= structs.size(); i++) {
            StructComponent struct = structs.get(i - 1);
            if (!StructComponentUtil.isForExcel(struct) || structGroupKeys.contains(struct.getGroupKey())) {
                // 跳过不支持Excel的控件或同一分组的控件
                continue;
            }
            // 如果有分组,记录一下
            if (StringUtils.isNotBlank(struct.getGroupKey())) {
                structGroupKeys.add(struct.getGroupKey());
            }
            // 如果存了有字数限制
            int fillLengthLimit = 0;
            StructComponent.Context context = struct.getContext();
            if (StringUtils.isNotBlank(context.getExt())) {
                SignAreaContextExt ext =
                        JsonUtils.json2pojo(context.getExt(), SignAreaContextExt.class);
                fillLengthLimit = ext.getFillLengthLimit();
            }
            String value = "";
            if (MapUtils.isNotEmpty(preFillValues) && null != preFillValues.get(struct.getId())) {
                value = preFillValues.get(struct.getId()).toString();
                //如果该控件时选择控件，还需要对控件的值进行预处理
                if(StructComponentTypeEnum.isSelect(struct.getType())){
                    //Excel仅支持单项选择
                    try {
                        value = context.getOptions().get(Integer.parseInt(value)).getLabel();
                    } catch (NumberFormatException e) {
                        log.warn("select struct deal fail, struct:{}, preFillValue:{} ", struct, value);
                    }
                }

            }
            String limitLabel = fillLengthLimit > 0 ? "(限" + fillLengthLimit + "字)" : "";
            String columnName = context.getLabel() + limitLabel + "-" + participantLabel;
            taskBizInfo.addExportInfo(taskBizInfo.getExportInfos().size(), columnName, value, 2);
        }
    }
}

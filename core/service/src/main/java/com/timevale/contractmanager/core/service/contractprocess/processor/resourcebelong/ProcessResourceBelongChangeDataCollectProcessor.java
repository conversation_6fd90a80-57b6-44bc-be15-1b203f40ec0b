package com.timevale.contractmanager.core.service.contractprocess.processor.resourcebelong;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.common.service.integration.client.ResourceBelongClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saas.common.manage.common.service.constant.ResourceBelongTopicConstant;
import com.timevale.saas.common.manage.common.service.enums.resourcebelong.ResourceBelongTypeEnum;
import com.timevale.saas.common.manage.common.service.message.resourcebelong.ResourceBelongChangeMessage;
import com.timevale.saas.common.manage.common.service.model.output.resourcebelong.ResourceBelongDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessResourceBelongParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants.PROCESS_BELONG;

/**
 * <AUTHOR>
 * @since 2023/7/4 16:33
 */
@Component
public class ProcessResourceBelongChangeDataCollectProcessor implements ProcessDataCollectProcessor {


    @Autowired
    private ResourceBelongClient resourceBelongClient;
    @Autowired
    private ContractProcessWriteClient writeClient;

    @Override
    public Route route() {
        return Route.of(ResourceBelongTopicConstant.TOPIC, ResourceBelongTopicConstant.DEFAULT_TAG);
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ResourceBelongChangeMessage entity =
                JsonUtils.json2pojo(data, ResourceBelongChangeMessage.class);
        return new DataAnalysisResult(entity.getResourceId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        List<ResourceBelongDTO> resourceBelong =
                resourceBelongClient.getResourceBelong(processId, ResourceBelongTypeEnum.PROCESS.getCode());
        if (CollectionUtils.isEmpty(resourceBelong)) {
            // 清空
            writeClient.clearBelong(processId);
            return;
        }
        Map<String, List<String>> subjectGidDirectsMap = resourceBelong.stream()
                .collect(Collectors.groupingBy(ResourceBelongDTO::getSubjectGid,
                        Collectors.collectingAndThen(Collectors.mapping(ResourceBelongDTO::getDeptPath, Collectors.toList()),
                                elm -> elm.stream().flatMap(List::stream).distinct().collect(Collectors.toList()))));
        // 构造数据保存到es
        List<ProcessResourceBelongParam> updateBelongParam = subjectGidDirectsMap.entrySet().stream().map(elm -> {
            ProcessResourceBelongParam param = new ProcessResourceBelongParam();
            param.setSubjectGid(elm.getKey());
            param.setDeptPath(elm.getValue());
            return param;
        }).collect(Collectors.toList());

        ContractProcessUpdateParam updateParam = new ContractProcessUpdateParam();
        updateParam.setProcessId(processId);
        updateParam.setBelong(updateBelongParam);
        updateParam.setBizScene(PROCESS_BELONG);
        writeClient.updateByProcessId(updateParam);
    }

}

package com.timevale.contractmanager.core.service.offlinecontract.bean.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 查询线下合同导入记录合同信息列表
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class QueryOfflineContractRecordContractsDTO extends ToString {

    @NotBlank(message = "主体gid不能为空")
    private String subjectGid;

    @NotBlank(message = "导入记录id不能为空")
    private String recordId;

    /** 导入状态列表 */
    private List<String> statusList;

    /** 是否返回提取信息 */
    private boolean withExtract;

    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @NotNull(message = "每页数据大小不能为空")
    private Integer pageSize;
}

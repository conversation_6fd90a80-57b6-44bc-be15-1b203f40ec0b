package com.timevale.contractmanager.core.service.tracking;

import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.core.model.dto.request.GenerateRescindDocRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.request.sharesign.ShareSignTaskStartRequest;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.process.bean.UniProcessTransferInput;
import com.timevale.contractmanager.common.service.bean.AccountBean;

import java.util.Map;
import java.util.Set;

public interface SensorService {

    void sensorsCollect(String distinctId, String event, Map<String, Object> data);

    /**
     * 添加流程开启埋点数据
     *
     * @param processId
     * @param request
     */
    void processStartTracking(String processId, ProcessStartCoreRequest request);

    /**
     * 添加流程结束埋点数据
     *
     * @param
     * @return
     */
    void processEndTracking(String accountId, ProcessDO process, Integer processStatus);

    /**
     * 添加流程转交埋点数据
     *
     * @param process
     * @param flowId
     * @param flowType
     * @param input
     */
    void processTransferTracking(
            ProcessDO process, String flowId, Integer flowType, UniProcessTransferInput input);

    /**
     * 添加扫码任务发起埋点数据
     *
     * @param request
     * @param subject
     */
    void shareSignTaskStartTracking(ShareSignTaskStartRequest request, UserAccount subject);


    /**
     * 模板合同提取信息数据埋点
     * @param tenantId
     * @param processId
     * @param orgName
     */
    void templatematchStandingBookTracking(String tenantId,String processId,String orgName);


    /**
     * 非模板合同提取信息数据埋点
     * @param tenantId
     * @param processId
     * @param orgName
     */
    void unTemplatematchStandingBookTracking(String tenantId,String processId,String orgName);

    /**
     * 自动归档提取信息数据埋点
     * @param tenantId
     * @param orgName
     * @param menuId
     */
    void autoArchiveTracking(String tenantId,String orgName,String menuId);

    /**
     * 全局直接发起开关埋点
     * @param tenantId
     * @param orgName
     * @param isOpen
     */
    void processDirectSwitchTracking(String tenantId,String orgName,boolean isOpen);

    /**
     * 相对方检测企业成功
     * @param tenantId
     * @param orgName
     */
    void opponentDetection(String tenantId, String orgName);

    /**
     * 相对方检测企业，发现问题
     * @param tenantId
     * @param orgName
     * @param problemType
     */
    void opponentDetectionProblemReport(String tenantId, String orgName, Integer problemType);


    /**
     * 经办合同列表埋点
     * @param menuName
     * @param status
     * @param isInitiateTime
     * @param isFinishTime
     * @param bizTypeList
     * @param processNum
     * @param elapsedTime
     */
    void processesListTracking(String tenantId, String menuName, Set<Integer> status, Boolean isInitiateTime,
                               Boolean isFinishTime, Set<Integer> bizTypeList, Long processNum, Long elapsedTime);

    /**
     * 删除合同
     * @param tenantId
     * @param menuName
     * @param processNum
     * @param elapsedTime
     * @param authenticationResult
     * @param authenticationFailureReason
     * @param processingResult
     */
    void processBatchDeleteTracking(String tenantId, String menuName, Long processNum, Long elapsedTime,
                                    String authenticationResult, String authenticationFailureReason, String processingResult);

    /**
     * 流程下载
     * @param tenantId
     * @param menuName
     * @param processNum
     * @param elapsedTime
     * @param processingResult
     * @param authenticationResult
     * @param authenticationFailureReason
     */
    void processDownloadContractTracking(String tenantId, String menuName, Long processNum, Long elapsedTime,
                                         String processingResult, String authenticationResult, String authenticationFailureReason);

    /**
     *修改到期日期埋点
     * @param tenantId
     * @param menuName
     * @param processNum
     * @param elapsedTime
     * @param processingResult
     * @param authenticationResult
     * @param authenticationFailureReason
     */
    void processUpdateContractValidityTracking(String tenantId, String menuName, Long processNum, Long elapsedTime,
                                               String processingResult, String authenticationResult, String authenticationFailureReason);

    /**
     * 修改续签埋点
     * @param tenantId
     * @param menuName
     * @param processNum
     * @param elapsedTime
     * @param processingResult
     * @param authenticationResult
     * @param authenticationFailureReason
     */
    void processUpdateRenewableTracking(String tenantId, String menuName, Long processNum, Long elapsedTime,
                                        String processingResult, String authenticationResult, String authenticationFailureReason);

    /**
     * 导出合同明细
     * @param tenantId
     * @param menuName
     * @param processNum
     * @param elapsedTime
     * @param processingResult
     * @param authenticationResult
     * @param authenticationFailureReason
     */
    void exportProcessTracking(String tenantId, String menuName, Long processNum, Long elapsedTime,
                               String processingResult, String authenticationResult, String authenticationFailureReason);
    /**
     * 企业合同列表埋点
     * @param tenantId
     * @param menuName
     * @param processNum
     * @param elapsedTime
     * @param processingResult
     * @param numOfField
     * @param field
     */
    void groupListTracking(String tenantId, String menuName, Long processNum, Long elapsedTime,
                           String processingResult, Integer numOfField, String field);

    /**
     * 设置分类权限埋点
     * @param tenantId
     * @param setType
     * @param folderName
     * @param fatherFolderName
     * @param folderClass
     * @param numberOfViewAbleDepartment
     * @param numberOfViewAbleStaff
     * @param numberOfEditAbleStaff
     * @param numberOfEditAbleDepartment
     * @param numberOfDownloadAbleStaff
     * @param numberOfDownloadAbleDepartment
     */
    void setMenuPrivilegeTracking(String tenantId, String setType, String folderName, String fatherFolderName,
                                  String folderClass, Integer numberOfViewAbleDepartment, Integer numberOfViewAbleStaff,
                                  Integer numberOfEditAbleStaff, Integer numberOfEditAbleDepartment,
                                  Integer numberOfDownloadAbleStaff, Integer numberOfDownloadAbleDepartment);

    /**
     * 移动分类埋点
     * @param tenantId
     * @param folderName
     * @param processNum
     * @param elapsedTime
     * @param processingResult
     */
    void moveMenuTracking(String tenantId, String folderName, Long processNum, Long elapsedTime, String processingResult);

    /**
     * 生成解约文件埋点
     * @param tenantId
     * @param request
     */
    void generateRescindFileTracking(String tenantId, GenerateRescindDocRequest request);

    /**
     * 从相对方选人埋点
     *
     * @param tenantId
     * @param operateId
     */
    void opponentIndividualListTracking(String tenantId, String operateId);

    void relationContractQuery(String tenantGid, String tenantName, String operatorGid, int relationContractNum);

    /**
     * 保存合同编号规则埋点
     *
     * @param tenantGid
     * @param prefix
     * @param timeType
     * @param tailType
     * @param tailNumber
     * @param initNumber
     * @param ruleName
     */
    void saveRuleTracking(String tenantGid, String prefix, Integer timeType, Integer tailType, Integer tailNumber, Integer initNumber, String ruleName);

    /**
     * 设置全局水印模板设置埋点
     *
     * @param tenantOid
     * @param globalWatermarksettingJson
     */
    void setGlobalWatermarkSettingSensor(String tenantOid, String globalWatermarksettingJson);


    /**
     * 设置自动转交埋点
     * @param tenantOid
     * @param value
     * @param event
     */
    void preferenceAutoTransferSensor(String tenantOid, String value, String event);

    /**
     * 生态组织签署完成埋点
     * @param processId 流程ID
     * @param tenant 租户信息
     * @param request 请求信息
     */
    void ecoOrgSigningCompleted(String processId, AccountBean tenant, ProcessStartCoreRequest request);
}

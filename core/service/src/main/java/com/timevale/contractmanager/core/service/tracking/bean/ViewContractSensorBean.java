package com.timevale.contractmanager.core.service.tracking.bean;

import com.timevale.contractmanager.core.model.dto.request.GetProcessUrlRequest;
import com.timevale.contractmanager.core.model.enums.SensorEnum;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * 查看合同埋点模版
 * <AUTHOR>
 *
 * @date 2022/2/22
 */
@Data
public class ViewContractSensorBean extends SensorBaseBean {

    private GetProcessUrlRequest request;

    @Override
    public SensorEventEnum sensorKey() {
        return SensorEventEnum.VIEW_CONTRACT_SEVER;
    }

    @Override
    public List<SensorEnum> sensorTemplate() {
        return Arrays.asList(
                SensorEnum.PROCESS_STATUS,
                SensorEnum.AUTHENTICATION_RESULT,
                SensorEnum.AUTHENTICATION_FAILURE_REASON);
    }

    @Override
    public void initData() {
        setOperatorOid(request.getAccountId());
        setAuthorizedOid(request.getSubjectId());
    }

    @Override
    public String getProcessId() {
        return request.getProcessId();
    }

    @Override
    public void setRequest(Object request) {
        this.request = (GetProcessUrlRequest) request;
    }

    public void setRequest(GetProcessUrlRequest request) {
        this.request = request;
    }
}

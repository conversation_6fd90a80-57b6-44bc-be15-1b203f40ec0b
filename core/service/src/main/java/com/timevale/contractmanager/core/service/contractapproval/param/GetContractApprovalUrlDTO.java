package com.timevale.contractmanager.core.service.contractapproval.param;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 获取合同审批详情地址请求参数
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
public class GetContractApprovalUrlDTO extends ToString {

    /** 合同审批流程id */
    private String approvalFlowId;
    /** 三方端对应的登录token */
    private String token;
    /** 是否获取h5详情地址， 默认返回pc地址 */
    private boolean h5Url;
    /** 重定向地址 */
    private String redirectUrl;
    /** 用户id */
    private String accountId;
    /** 主体id */
    private String subjectOid;
    /** 分类id */
    private String menuId;
    /** 资源分享id */
    private String resourceShareId;

}

package com.timevale.contractmanager.core.service.contractprocess;

import com.timevale.framework.mq.client.producer.Msg;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/5/11
 */
public class ProcessDataCollectSupport {




    private static final String INIT_DATA_LOCK = "pDataCollect:lock:initData:";
    private static final String TASK_UPDATE_LOCK = "pDataCollect:lock:updateTask:";

    private static final String SEAL_TASK_LOCK = "pDataCollect:lock:updateSealTask:";

    private static final String SHADOW_TOPIC_PREFIX = "SL_APT";

    public static final String LOG_PREFIX = "hbase async collect ";

    public static String initDataLockKey(String processId) {
        return INIT_DATA_LOCK + processId;
    }

    public static String taskInfoChangeLockKey(String processId) {
        return TASK_UPDATE_LOCK + processId;
    }

    public static String sealTaskInfoChangeLockKey(String processId) {
        return SEAL_TASK_LOCK + processId;
    }

    public static ProcessDataCollectMsg msgConvert(Msg msg, String topic) {
        return msgConvert(msg, topic, null);
    }

    public static ProcessDataCollectMsg msgConvert(Msg msg, String topic, String consumerGroup) {
        if (msg == null) {
            return null;
        }
        ProcessDataCollectMsg collectMsg = new ProcessDataCollectMsg();
        collectMsg.setMsg(new String(msg.getBody()));
        collectMsg.setMsgId(msg.getMsgId());
        collectMsg.setTag(msg.getTags());
        collectMsg.setTopic(topic);
        collectMsg.setReconsumeTimes(msg.getReconsumeTimes());
        collectMsg.setShadowTopic(msg.getTopic().startsWith(SHADOW_TOPIC_PREFIX));
        collectMsg.setConsumerGroup(consumerGroup);
        return collectMsg;
    }
}

package com.timevale.contractmanager.core.service.component.grouping;

import com.alibaba.fastjson.JSONObject;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.service.enums.SubProcessTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.core.model.dto.autoarchive.ArchiveTemplateDTO;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.ProcessTemplateService;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.TemplateInfo;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST;

/**
 * 模板匹配组件
 *
 * @author: jinhuan
 * @since: 2020-05-08 14:39
 */
@Slf4j
@Component
public class TemplateMatchComponent {

    private static final Integer ONLINE_TEMPLATE_TYPE = 1;

    @Autowired private EsClient esClient;
    @Autowired private BaseProcessService baseProcessService;
    @Autowired private ProcessTemplateService processTemplateService;

    /**
     * 获取流程匹配的流程模板信息,若未匹配上则返回null
     *
     * @param processId 流程id
     * @return 流程模板信息对象
     */
    public ArchiveTemplateDTO getProcessTemplate(String processId) {
        String cacheKey = CacheUtil.getProcessArchiveTemplateDTOCacheKey(processId);
        String cacheData = CacheUtil.degradeGet(cacheKey);
        if (StringUtils.isNotBlank(cacheData)) {
            return JSONObject.parseObject(cacheData, ArchiveTemplateDTO.class);
        }
        ArchiveTemplateDTO archiveTemplateDTO = getProcessArchiveTemplate(processId);
        CacheUtil.degradeSet(cacheKey, JSONObject.toJSONString(archiveTemplateDTO), 1, TimeUnit.DAYS);
        return archiveTemplateDTO;
    }

    /**
     * 获取流程匹配的流程模板id,若未匹配上则返回null
     *
     * @param processId 流程id
     * @return 流程模板id
     */
    private ArchiveTemplateDTO getProcessArchiveTemplate(String processId) {
        ArchiveTemplateDTO archiveTemplateDTO = new ArchiveTemplateDTO();
        // 获取合同流程对应的流程模板id
        String flowTemplateId = processTemplateService.getFlowTemplateId(processId);
        // 如果流程模板id不为空，则直接返回
        if (StringUtils.isNotBlank(flowTemplateId)) {
            archiveTemplateDTO.setTemplateId(flowTemplateId);
            archiveTemplateDTO.setTemplateType(ONLINE_TEMPLATE_TYPE);
            return archiveTemplateDTO;
        }
        SubProcessDO subProcessDO = baseProcessService.getPrevSubProcess(processId);
        if (Objects.isNull(subProcessDO)) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }
        // 如果为合同审批流程或签署流程且流程模板id为空, 则额外查询是否是AI模板
        if (SubProcessTypeEnum.CONTRACT_APPROVAL.getType() == subProcessDO.getSubProcessType()
                || SubProcessTypeEnum.SIGN.getType() == subProcessDO.getSubProcessType()) {
            // 查询是否是AI模板
            QueryByProcessIdResult queryByProcessIdResult = esClient.queryById(processId);
            if (queryByProcessIdResult == null || queryByProcessIdResult.getProcessInfoTotalInfo() == null) {
                return archiveTemplateDTO;
            }
            TemplateInfo templateInfo = queryByProcessIdResult.getProcessInfoTotalInfo().getTemplateInfo();
            if (templateInfo == null) {
                return archiveTemplateDTO;
            }
            log.info("getProcessTemplate matched, processId: {}, templateId: {}", processId, templateInfo.getTemplateId());
            archiveTemplateDTO.setTemplateId(templateInfo.getTemplateId());
            archiveTemplateDTO.setTemplateType(templateInfo.getTemplateType() == null ? ONLINE_TEMPLATE_TYPE : templateInfo.getTemplateType());
        }
        return archiveTemplateDTO;
    }
}

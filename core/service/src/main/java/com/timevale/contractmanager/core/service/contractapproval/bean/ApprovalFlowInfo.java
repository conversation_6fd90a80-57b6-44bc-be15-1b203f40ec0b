package com.timevale.contractmanager.core.service.contractapproval.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 审批节点信息
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@ApiModel("审批流程基本信息")
public class ApprovalFlowInfo extends ToString {

    /** 审批流程id */
    private String approvalFlowId;

    /** 审批流程名称 */
    private String approvalFlowName;

    /** 审批流程状态 */
    private Integer approvalFlowStatus;
    /**
     * 发起主体oid
     */
    private String subjectOid;
    /**
     * 发起主体gid
     */
    private String subjectGid;
    /**
     * 发起人oid
     */
    private String initiatorOid;
    /**
     * 发起人gid
     */
    private String initiatorGid;
    /**
     * 业务id
     */
    private String bizId;

}

package com.timevale.contractmanager.core.service.processstart.impl.context;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.privilege.service.enums.BuiltinPrivilege;
import com.timevale.privilege.service.enums.PrivilegeOperationRegister;

import java.util.Map;
import java.util.Set;

/**
 * 用户权限上下文对象
 *
 * <AUTHOR>
 * @since 2022-09-22
 */
public class UserPrivilegeBizContext {
    /** 用户权限MAP, Key为资源名称，Value为权限列表 */
    private Map<String, Set<String>> privilegeMap;

    public UserPrivilegeBizContext(Map<String, Set<String>> privilegeMap) {
        this.privilegeMap = privilegeMap;
    }

    private static final String ADMIN_ALL = PrivilegeOperationRegister.ALL;

    /**
     * 判断是否有指定资源的指定权限
     *
     * @param resource
     * @param operation
     * @return
     */
    public boolean hasPrivilege(String resource, String operation) {
        if (CollectionUtils.isEmpty(privilegeMap)) {
            return false;
        }
        // 如果权限中包含管理员， 则默认有权限
        if (privilegeMap.containsKey(BuiltinPrivilege.GOD)
                && privilegeMap.get(BuiltinPrivilege.GOD).contains(ADMIN_ALL)) {
            return true;
        }
        return privilegeMap.containsKey(resource) && privilegeMap.get(resource).contains(operation);
    }
}

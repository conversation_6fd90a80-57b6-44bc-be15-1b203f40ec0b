package com.timevale.contractmanager.core.service.comparator;

import com.timevale.signflow.search.docSearchService.bean.TaskInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.enums.TaskStatusEnum;

import java.lang.reflect.Field;
import java.util.Comparator;

/**
 * 流程参与人排序规则定义
 * @author: hei pao
 * @since: 2019-08-27 18:12
 **/
public class OrderComparator implements Comparator<Object> {

    public static final OrderComparator INSTANCE = new OrderComparator();

    @Override
    public int compare(Object o1, Object o2) {

        int statusValue1;
        int statusValue2;
        TaskInfoTotalInfo o11 = (TaskInfoTotalInfo) o1;
        TaskInfoTotalInfo o22 = (TaskInfoTotalInfo) o2;

        //先根据order正序排序
        Integer order1 = o11.getOrder();
        Integer order2 = o22.getOrder();
        if (order1 != null && order2 != null && !order1.equals(order2)){
            return order1 - order2;
        }

        //order无法排序时根据status倒序排序
        statusValue1 = specialStatusTransform(o11.getStatus());
        statusValue2 = specialStatusTransform(o22.getStatus());

        return statusValue2 - statusValue1;
    }

    private int specialStatusTransform(int status){
        if (status == TaskStatusEnum.APPROVE.getStatus() ||
            status == TaskStatusEnum.APPROVE_AWAIT.getStatus()) {
            return TaskStatusEnum.SIGNING.getStatus();
        }else {
            return status;
        }
    }
}

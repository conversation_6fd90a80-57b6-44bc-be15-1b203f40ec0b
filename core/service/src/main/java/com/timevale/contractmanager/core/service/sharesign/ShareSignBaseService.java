package com.timevale.contractmanager.core.service.sharesign;

import com.github.pagehelper.Page;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignProcessDO;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignTaskDO;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignUrlDO;
import com.timevale.contractmanager.common.dal.query.shareSign.ShareSignTaskListParam;
import com.timevale.contractmanager.common.service.enums.GroupTypeEnum;
import com.timevale.contractmanager.common.service.enums.sharesign.ShareSignProcessStatusEnum;
import com.timevale.contractmanager.core.model.dto.request.process.SaveShareSignInnerRequest;
import com.timevale.contractmanager.core.model.dto.response.sharesign.ShareSignQrCodeBean;
import com.timevale.contractmanager.core.service.sharesign.bean.ShareSignParticipantAccount;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-02-03
 */
public interface ShareSignBaseService {

    /** ------------------------------分享签任务相关接口---------------------------------- */
    @Deprecated
    void insertTask(ShareSignTaskDO entity);

    void updateTaskStatus(String shareSignTaskId, Integer status);

    void updateByTaskId(ShareSignTaskDO updateData);

    void upgradeTaskDone(String shareSignTaskId);

    boolean upgradeTaskNum(String shareSignTaskId);

    boolean degradeTaskNum(String shareSignTaskId);

    boolean upgradeTaskNumOccupied(String shareSignTaskId);

    boolean degradeTaskNumOccupied(String shareSignTaskId);

    ShareSignTaskDO queryTaskById(String shareSignTaskId);

    List<ShareSignTaskDO> queryTasksByBizIds(List<String> shareBizIds, Integer shareType);

    /**
     * 判断是否扫码任务关联的流程模板
     *
     * @param shareTemplateId
     * @return
     */
    boolean checkIsShareTemplateId(String shareTemplateId);

    /** ------------------------------分享签任务分享地址相关接口---------------------------------- */
    @Deprecated
    void insertTaskUrl(ShareSignUrlDO entity);

    /**
     * 保存扫码任务及二维码地址
     */
    void saveShareSignTaskAndUrl(SaveShareSignInnerRequest request);

    void updateTaskUrl(ShareSignUrlDO entity);

    List<ShareSignUrlDO> queryTaskUrls(String shareSignTaskId, String participantId);

    ShareSignUrlDO queryTaskUrlById(String shareSignTaskUrlId);

    /** ------------------------------分享签任务流程相关接口---------------------------------- */
    void addTaskProcess(
            String shareSignTaskId,
            String participantId,
            String processId,
            ShareSignParticipantAccount signAccount,
            ShareSignProcessStatusEnum status);

    /**
     * 查询扫码任务中用户参与的流程列表
     *
     * @param shareSignTaskId
     * @param signerGid
     * @param limit
     * @return
     */
    List<ShareSignProcessDO> queryByAccountGid(String shareSignTaskId, String signerGid, int limit);

    /**
     * 统计扫码任务中用户参与的流程数
     *
     * @param shareSignTaskId
     * @param signerGid
     * @return
     */
    int countByAccountGid(String shareSignTaskId, String signerGid);

    /**
     * 统计扫码任务中用户个人参与的流程数
     * @param shareSignTaskId
     * @param signerGid
     * @return
     */
    int countByAccountGidWithPerson(String shareSignTaskId, String signerGid);

    List<ShareSignProcessDO> queryByProcessId(String processId);
    Map<String, ShareSignProcessDO> queryByProcessIds(List<String> processIds);

    int deleteByProcessId(String processId);

    ShareSignQrCodeBean genShareSignQrCode(String shareSignTaskId, String participantId);

    /**
     * 刷新二维码， 兼容历史数据
     *
     * @param shareSignUrl
     */
    void refreshShareSignQrCode(ShareSignUrlDO shareSignUrl);

    /**
     * 根据accountId和任务status查询任务总数
     *
     * @param accountGid 用户accountGid
     * @param status 任务状态
     * @param shareType 任务类型
     * @return 任务总数
     */
    int countTaskByAccountGid(String accountGid, int status, Integer shareType);

    List<Map<String, Object>> countByStatus(String accountGid, String subjectGid);

    /**
     * 分页获取扫码签任务列表
     * @param input 请求参数
     * @return 扫码签列表
     */
    Page<ShareSignTaskDO> getListByPage(ShareSignTaskListParam input);

    /**
     * 更新扫码流程发起状态
     * @param processId
     * @param status
     */
    void updateStatusByProcessId(String processId, Integer status);
}

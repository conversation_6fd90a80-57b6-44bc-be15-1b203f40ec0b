package com.timevale.contractmanager.core.service.opponent.adapter;


import com.timevale.contractmanager.core.model.bo.opponent.OpponentOrgSummaryBO;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBlackListOrgCodeRespone;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.opponent.EnterpriseInformationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author:jianyang
 * @since 2022-04-19 15:13
 */
@Component
@Slf4j
public class OpponentSupplierAdapter {

	@Autowired
	private EnterpriseInformationService enterpriseInformationService;

	public OpponentBlackListOrgCodeRespone getQiXinBaoToken(String orgName){
		OpponentOrgSummaryBO enterpriseUrl = enterpriseInformationService.getEnterpriseUrl(orgName);
		OpponentBlackListOrgCodeRespone respone = new OpponentBlackListOrgCodeRespone();
		respone.setToken(enterpriseUrl.getToken());
		respone.setOrgCode(enterpriseUrl.getOrgCode());
		respone.setUrl(enterpriseUrl.getOrgSummaryUrl());
		return respone;
		
	}
}

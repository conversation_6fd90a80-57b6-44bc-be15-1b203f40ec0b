package com.timevale.contractmanager.core.service.tracking.bean;

import com.timevale.contractmanager.core.model.dto.request.ProcessSecretConfigUpdateRequest;
import com.timevale.contractmanager.core.model.enums.SensorEnum;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 修改保密性埋点模版
 * <AUTHOR>
 *
 * @date 2022/2/22
 */
@Data
public class EditSecrecySensorBean extends SensorBaseBean {
    private ProcessSecretConfigUpdateRequest request;

    @Override
    public SensorEventEnum sensorKey() {
        return SensorEventEnum.EDIT_SECRECY_SEVER;
    }

    @Override
    public List<SensorEnum> sensorTemplate() {
        return Arrays.asList(
                SensorEnum.NUMBER_OF_PROCESS,
                SensorEnum.AUTHENTICATION_FAILURE_REASON,
                SensorEnum.AUTHENTICATION_RESULT,
                SensorEnum.RETURN_TIME,
                SensorEnum.PROCESSING_RESULT,
                SensorEnum.IS_SECRECY,
                SensorEnum.NUMBER_OF_PEOPLE);
    }

    @Override
    public void initData() {
        getTempData().put(SensorEnum.NUMBER_OF_PROCESS.getKey(), request.getProcessIds().size());
        if(CollectionUtils.isNotEmpty(request.getVisibleAccounts())){
            getTempData().put(
                    SensorEnum.NUMBER_OF_PEOPLE.getKey(), request.getVisibleAccounts().size());
        }
        getTempData().put(
                SensorEnum.IS_SECRECY.getKey(), !Objects.equals(request.getSecretType(), 1));
    }

    @Override
    public void setRequest(Object request) {
        this.request = (ProcessSecretConfigUpdateRequest) request;
    }

    public void setRequest(ProcessSecretConfigUpdateRequest request) {
        this.request = request;
    }
}

package com.timevale.contractmanager.core.service.autoarchive;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;

/**
 * @Author:jianyang
 *
 * @since 2021-05-10 11:46
 */
public interface OperationConditionService {

    /**
     * 判断规则树叶子条件是否有效，如果无效，会把整棵规则树失效掉
     *
     * @param fieldId
     * @param tenantOid
     * @param tenantGid
     * @param ruleId
     * @param conditionParams
     * @return
     */
    Boolean checkCondition(
            String fieldId,
            String tenantOid,
            String tenantGid,
            String ruleId,
            String conditionParams);

    /**
     * 获取规则树叶子节点id
     *
     * @return
     */
    String getFieldId();

    BizContractManagerException getErrorException();
}

package com.timevale.contractmanager.core.service.contractapproval.param;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 催办合同审批请求参数
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
public class RemindContractApprovalDTO extends ToString {

    /** 合同审批流程id */
    private String approvalFlowId;
    /** 用户oid */
    private String accountId;
    /** 用户gid */
    private String accountGid;
    /** 主体oid */
    private String subjectId;
    /** 主体gid */
    private String subjectGid;
}

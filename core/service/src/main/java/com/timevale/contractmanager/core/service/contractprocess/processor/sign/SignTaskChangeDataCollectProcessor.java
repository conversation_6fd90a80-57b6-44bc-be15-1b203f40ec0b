package com.timevale.contractmanager.core.service.contractprocess.processor.sign;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.*;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.SignChangeTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.SignChangeMsgEntity;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Created by tianlei on 2022/5/11
 */
@Slf4j
@Component
public class SignTaskChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient processWriteClient;
    @Autowired
    private ContractProcessReadClient processQueryClient;
    @Autowired
    private LockService lockService;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.signTopicName(), SignChangeTagEnum.SIGN_TASK_CHANGE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        SignChangeMsgEntity entity =
                JsonUtils.json2pojo(data, SignChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        Lock lock = lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                doProcess(collectContext);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }


    private void doProcess(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        SignChangeMsgEntity signChangeMsgEntity = (SignChangeMsgEntity) collectContext.getData();

        if (StringUtils.isBlank(signChangeMsgEntity.getSignerAuthorizedAccountId()) || signChangeMsgEntity.getOrder() == null) {
            log.info(LOG_PREFIX + "msg data miss processId : {}", signChangeMsgEntity);
            return;
        }

        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);

        List<ContractProcessSignTaskDTO> oldSignTaskDTOList = contractProcessDTO.getSignTasks();
        if (CollectionUtils.isEmpty(oldSignTaskDTOList)) {
            // 消息乱序 该消息先与 sign_start 到
            log.info("signTasks is null skip");
            return;
        }

        // 看下是否是构造的假数据
        boolean falseData = StringUtils.isBlank(oldSignTaskDTOList.get(0).getTaskId());
        if (falseData) {
            // 说明是提前构造的假签署任务，消息先与 signStart 到了，丢弃等signStart来到在进行构造
            log.info("signTaskChange before signStart");
            return;
        }

        ContractProcessSignTaskDTO newSignTaskDTO = processDataBuilder.buildUpdateSignTask(signChangeMsgEntity);
        if (null == newSignTaskDTO) {
            log.info("signTasks build is null");
            return;
        }

        boolean findExistData = false;
        Integer existIndex = null;
        for (int i = 0; i < oldSignTaskDTOList.size(); i++) {
            if (Objects.equals(oldSignTaskDTOList.get(i).getTaskId(), newSignTaskDTO.getTaskId())) {
                // id相同记性替换
                findExistData = true;
                existIndex = i;
                break;
            }
        }

        if (findExistData) {
            // 保留转交
            newSignTaskDTO.setTransfer(oldSignTaskDTOList.get(existIndex).getTransfer());
            oldSignTaskDTOList.set(existIndex, newSignTaskDTO);

            ContractProcessUpdateParam input = new ContractProcessUpdateParam();
            input.setProcessId(processId);
            input.setSignTasks(ProcessDataCollectConverter.signTaskDTO2ParamList(oldSignTaskDTOList));
            input.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_TASK_CHANGE);
            processWriteClient.updateByProcessId(input);

        } else {
            // 应该不会有这种数据 这个消息处理的前提 是signStart 消息已经消费了，signStart 构建数据时会把签署的全部构建处理
            // 时候未通过的通过appendSign消息
            // 这里找不到也不新增，认为新增一定是通过 appendSign过来的。   转交的时候会出现 “新增的情况"
//            oldSignTaskDTOList.add(newSignTaskDTO);
        }
    }
}

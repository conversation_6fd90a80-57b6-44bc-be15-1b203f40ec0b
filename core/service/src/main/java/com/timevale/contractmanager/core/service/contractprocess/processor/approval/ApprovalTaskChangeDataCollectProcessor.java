package com.timevale.contractmanager.core.service.contractprocess.processor.approval;

import com.timevale.contractapproval.facade.enums.ApprovalGroupTypeEnum;
import com.timevale.contractapproval.facade.message.ApprovalTaskMessage;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.contractprocess.processor.approval.bean.ApprovalDoProcessParam;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ApprovalTaskChangeDataCollectProcessor
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Component
public class ApprovalTaskChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired private ApprovalComponent approvalComponent;
    @Autowired private ProcessDataCollectConfigCenter dataCollectConfigCenter;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.getApprovalTaskChangeTopicName(), null);
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ApprovalTaskMessage entity = JsonUtils.json2pojo(data, ApprovalTaskMessage.class);
        return new DataAnalysisResult(entity.getBizId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return false;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        ApprovalTaskMessage entity = (ApprovalTaskMessage) collectContext.getData();
        ApprovalDoProcessParam param = new ApprovalDoProcessParam();
        param.setApprovalCode(entity.getApprovalCode());
        param.setApprovalType(entity.getApprovalType());
        param.setStandardPlatformStarted(entity.isStandardPlatform());
        // 获取审批组信息
        ApprovalTaskMessage.ApprovalGroup approvalGroup = entity.getApprovalGroup();
        if (null != approvalGroup
                && ApprovalGroupTypeEnum.GROUP.getType().equals(approvalGroup.getGroupType())) {
            param.setBizGroupId(approvalGroup.getGroupId());
        }
        approvalComponent.doProcess(collectContext, param);
    }
}

package com.timevale.contractmanager.core.service.contractapproval;

import com.google.common.collect.Maps;
import com.timevale.contractapproval.facade.dto.*;
import com.timevale.contractapproval.facade.enums.ApprovalTaskStatusEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateNodeTypeEnum;
import com.timevale.contractapproval.facade.input.ApprovalTransferByAccountInput;
import com.timevale.contractapproval.facade.input.ApprovalTransferInput;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalFlowDetail;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalNode;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;

import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 合同审批转换类
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
public class ContractApprovalConverter {


    /**
     * 组装合同审批详情信息， 包含未流转到的审批节点信息
     * @param approvalDetail
     * @return
     */
    public static ApprovalFlowDetail convertApprovalFlowDetail(ApprovalDetailWithLogDTO approvalDetail) {
        // 解析审批节点
        Pair<List<ApprovalNode>, List<ApprovalNode>> approvalNodePair =
                convertApprovalLogNodes(approvalDetail.getApprovalLogs());

        ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
        approvalFlowDetail.setApprovalFlowId(approvalDetail.getApprovalCode());
        approvalFlowDetail.setBizId(approvalDetail.getBizId());
        approvalFlowDetail.setApprovalFlowStatus(approvalDetail.getApprovalStatus());
        approvalFlowDetail.setSubjectOid(approvalDetail.getSubjectOid());
        approvalFlowDetail.setSubjectGid(approvalDetail.getSubjectGid());
        approvalFlowDetail.setInitiatorOid(approvalDetail.getInitiatorOid());
        approvalFlowDetail.setInitiatorGid(approvalDetail.getSubjectGid());
        approvalFlowDetail.setTaskNodes(approvalNodePair.getLeft());
        approvalFlowDetail.setCcNodes(approvalNodePair.getRight());
        // 获取当前审批节点
        approvalFlowDetail.setCurrentNode(approvalNodePair.getLeft().stream()
                .filter(i -> i.getTasks().stream().anyMatch(j -> ApprovalTaskStatusEnum.APPROVALING.getCode().equals(j.getStatus())))
                .findFirst()
                .orElse(null));

        return approvalFlowDetail;
    }

    /**
     * 组装合同审批详情信息，只包含已流转到的审批节点信息
     * @param approvalDetail
     * @return
     */
    public static ApprovalFlowDetail convertApprovalFlowDetail(ApprovalDetailDTO approvalDetail) {
        // 解析审批节点
        Pair<List<ApprovalNode>, List<ApprovalNode>> approvalNodePair =
                convertApprovalNodes(approvalDetail.getApprovalNodeTasks());

        ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
        approvalFlowDetail.setApprovalFlowId(approvalDetail.getApprovalCode());
        approvalFlowDetail.setBizId(approvalDetail.getBizId());
        approvalFlowDetail.setApprovalFlowStatus(approvalDetail.getApprovalStatus());
        approvalFlowDetail.setSubjectOid(approvalDetail.getSubjectOid());
        approvalFlowDetail.setSubjectGid(approvalDetail.getSubjectGid());
        approvalFlowDetail.setInitiatorOid(approvalDetail.getInitiatorOid());
        approvalFlowDetail.setInitiatorGid(approvalDetail.getSubjectGid());
        approvalFlowDetail.setTaskNodes(approvalNodePair.getLeft());
        approvalFlowDetail.setCcNodes(approvalNodePair.getRight());
        // 获取当前审批节点
        ApprovalNodeTaskDTO currentNode = approvalDetail.getCurrentApprovalNodeTask();
        if (null != currentNode) {
            approvalFlowDetail.setCurrentNode(
                    approvalNodePair.getLeft().stream()
                            .filter(i -> StringUtils.equals(i.getNodeId(), currentNode.getNodeId()))
                            .findFirst()
                            .orElse(null));
        }
        return approvalFlowDetail;
    }

    /**
     * 解析审批节点，返回任务节点和抄送节点， Pair<任务节点, 抄送节点>
     *
     * @param nodeTaskDTOS
     * @return
     */
    private static Pair<List<ApprovalNode>, List<ApprovalNode>> convertApprovalNodes(
            List<ApprovalNodeTaskDTO> nodeTaskDTOS) {
        AtomicInteger order = new AtomicInteger(0);
        List<ApprovalNode> taskNodeList = Lists.newArrayList();
        List<ApprovalNode> ccNodeList = Lists.newArrayList();
        for (ApprovalNodeTaskDTO approvalNodeTask : nodeTaskDTOS) {
            // 获取节点类型
            String nodeType = approvalNodeTask.getNodeType();
            // 抄送节点
            if (ApprovalTemplateNodeTypeEnum.CARBON_COPY.getCode().equals(nodeType)) {
                ccNodeList.add(convertApprovalNode(order, approvalNodeTask));
                continue;
            }
            // 审批节点
            if (ApprovalTemplateNodeTypeEnum.OR_SIGN.getCode().equals(nodeType)
                    || ApprovalTemplateNodeTypeEnum.COUNTER_SIGN.getCode().equals(nodeType)) {
                taskNodeList.add(convertApprovalNode(order, approvalNodeTask));
            }
        }
        return Pair.of(taskNodeList, ccNodeList);
    }

    /**
     * 转换审批流中节点中的用户信息
     *
     * @param order
     * @param nodeTaskDTO
     * @return
     */
    private static ApprovalNode convertApprovalNode(AtomicInteger order, ApprovalNodeTaskDTO nodeTaskDTO) {
        ApprovalNode approvalNode = new ApprovalNode();
        approvalNode.setNodeId(nodeTaskDTO.getNodeId());
        approvalNode.setNodeType(nodeTaskDTO.getNodeType());
        approvalNode.setOrder(order.incrementAndGet());
        approvalNode.setTasks(Lists.newArrayList());

        for (ApprovalTaskDTO approvalTask : nodeTaskDTO.getApprovalTasks()) {
            // 组装审批任务信息
            ApprovalNode.ApprovalNodeTask nodeTask = new ApprovalNode.ApprovalNodeTask();
            nodeTask.setTaskId(approvalTask.getTaskId());
            nodeTask.setStatus(approvalTask.getTaskStatus());
            nodeTask.setUsers(buildApprovalTaskUsers(approvalTask));
            // 追加审批任务信息到审批节点中
            approvalNode.getTasks().add(nodeTask);
        }

        return approvalNode;
    }

    /**
     * 组装审批人列表
     *
     * @param approvalTask
     * @return
     */
    private static List<ApprovalNode.ApprovalTaskUser> buildApprovalTaskUsers(
            ApprovalTaskDTO approvalTask) {
        List<ApprovalNode.ApprovalTaskUser> taskUsers = Lists.newArrayList();
        // 获取任务状态
        Integer taskStatus = approvalTask.getTaskStatus();
        // 判断任务状态是否审批通过或审批拒绝
        boolean passOrRefuse =
                ApprovalTaskStatusEnum.APPROVAL_PASS.getCode().equals(taskStatus)
                        || ApprovalTaskStatusEnum.APPROVAL_REFUSE.getCode().equals(taskStatus);
        // 审批通过或审批拒绝情况下， 优先获取审批操作人信息
        if (passOrRefuse && null != approvalTask.getAssignee()) {
            taskUsers.add(convertApprovalTaskUser(approvalTask.getAssignee()));
            return taskUsers;
        }
        for (ApprovalAccountDTO candidate : approvalTask.getCandidates()) {
            taskUsers.add(convertApprovalTaskUser(candidate));
        }
        return taskUsers;
    }

    /**
     * 解析审批节点，返回任务节点和抄送节点， Pair<任务节点, 抄送节点>
     *
     * @param approvalLogDTOS
     * @return
     */
    public static Pair<List<ApprovalNode>, List<ApprovalNode>> convertApprovalLogNodes(
            List<ApprovalLogDTO> approvalLogDTOS) {
        AtomicInteger order = new AtomicInteger(0);
        List<ApprovalNode> taskNodeList = Lists.newArrayList();
        List<ApprovalNode> ccNodeList = Lists.newArrayList();
        for (ApprovalLogDTO approvalLog : approvalLogDTOS) {
            // 获取节点类型
            String nodeType = approvalLog.getNodeType();
            // 抄送节点
            if (ApprovalTemplateNodeTypeEnum.CARBON_COPY.getCode().equals(nodeType)) {
                ccNodeList.add(convertApprovalLogNode(order, approvalLog));
                continue;
            }
            // 审批节点
            if (ApprovalTemplateNodeTypeEnum.OR_SIGN.getCode().equals(nodeType)
                    || ApprovalTemplateNodeTypeEnum.COUNTER_SIGN.getCode().equals(nodeType)) {
                taskNodeList.add(convertApprovalLogNode(order, approvalLog));
            }
        }
        return Pair.of(taskNodeList, ccNodeList);
    }

    public static ApprovalTransferInput buildBaseInput(
            UserAccountDetail tenant,
            UserAccountDetail operator,
            UserAccountDetail targetAccount,
            UserAccountDetail sourceAccount) {
        ApprovalTransferInput input = new ApprovalTransferInput();
        input.setSubjectOid(tenant.getAccountOid());
        input.setSubjectGid(tenant.getAccountGid());
        input.setOperatorOid(operator.getAccountOid());
        input.setOperatorGid(operator.getAccountGid());
        input.setSourcePersonOid(sourceAccount.getAccountOid());
        input.setSourcePersonGid(sourceAccount.getAccountGid());
        input.setTargetPersonOid(targetAccount.getAccountOid());
        input.setTargetPersonGid(targetAccount.getAccountGid());
        input.setTargetPersonName(targetAccount.getAccountName());
        return input;
    }

    public static ApprovalTransferByAccountInput buildTransferByAccountInput(
            UserAccountDetail tenant,
            UserAccountDetail operator,
            UserAccountDetail targetAccount,
            UserAccountDetail sourceAccount) {
        ApprovalTransferByAccountInput input = new ApprovalTransferByAccountInput();
        input.setSubjectOid(tenant.getAccountOid());
        input.setSubjectGid(tenant.getAccountGid());
        input.setOperatorOid(operator.getAccountOid());
        input.setOperatorGid(operator.getAccountGid());
        input.setSourcePersonOid(sourceAccount.getAccountOid());
        input.setSourcePersonGid(sourceAccount.getAccountGid());
        input.setTargetPersonOid(targetAccount.getAccountOid());
        input.setTargetPersonGid(targetAccount.getAccountGid());
        input.setTargetPersonName(targetAccount.getAccountName());
        return input;
    }

    /**
     * 转换审批流中节点中的用户信息
     *
     * @param order
     * @param approvalLog
     * @return
     */
    private static ApprovalNode convertApprovalLogNode(AtomicInteger order, ApprovalLogDTO approvalLog) {
        ApprovalNode approvalNode = new ApprovalNode();
        approvalNode.setNodeId(approvalLog.getNodeId());
        approvalNode.setNodeType(approvalLog.getNodeType());
        approvalNode.setOrder(order.incrementAndGet());
        approvalNode.setTasks(Lists.newArrayList());
        // 或审
        if (ApprovalTemplateNodeTypeEnum.OR_SIGN.getCode().equals(approvalLog.getNodeType())) {
            // 组装审批任务信息
            ApprovalNode.ApprovalNodeTask nodeTask = new ApprovalNode.ApprovalNodeTask();
            nodeTask.setStatus(approvalLog.getNodeStatus());
            List<ApprovalNode.ApprovalTaskUser> taskUsers = Lists.newArrayList();
            for (ApprovalAccountDTO candidate : approvalLog.getCandidates()) {
                taskUsers.add(convertApprovalTaskUser(candidate));
            }
            nodeTask.setUsers(taskUsers);
            // 追加审批任务信息到审批节点中
            approvalNode.getTasks().add(nodeTask);
            return approvalNode;
        }
        // 会审，每个审批人对应一个审批任务
        Map<String, List<ApprovalPersonOperateLogDTO>> approverAssigneeLogMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(approvalLog.getAssigneeLogs())) {
            approverAssigneeLogMap.putAll(
                    approvalLog.getAssigneeLogs().stream()
                            .collect(Collectors.groupingBy(i -> i.getGid())));
        }
        for (ApprovalAccountDTO candidate : approvalLog.getCandidates()) {
            // 默认任务状态为节点状态
            Integer status = approvalLog.getNodeStatus();
            // 查询审批人操作日志
            List<ApprovalPersonOperateLogDTO> operateLogDTOS = approverAssigneeLogMap.get(candidate.getGid());
            // 如果操作日志不为空，获取审批人操作状态并移除日志，防止同一条日志重复使用
            if (CollectionUtils.isNotEmpty(operateLogDTOS)) {
                Iterator<ApprovalPersonOperateLogDTO> iterator = operateLogDTOS.iterator();
                status = iterator.next().getTaskStatus();
                iterator.remove();
            }
            // 组装审批任务信息
            ApprovalNode.ApprovalNodeTask nodeTask = new ApprovalNode.ApprovalNodeTask();
            nodeTask.setStatus(status);
            nodeTask.setUsers(Lists.newArrayList(convertApprovalTaskUser(candidate)));
            // 追加审批任务信息到审批节点中
            approvalNode.getTasks().add(nodeTask);
        }

        return approvalNode;
    }

    /**
     * 组装审批人信息
     *
     * @param candidate
     * @return
     */
    private static ApprovalNode.ApprovalTaskUser convertApprovalTaskUser(ApprovalAccountDTO candidate) {
        ApprovalNode.ApprovalTaskUser taskUser = new ApprovalNode.ApprovalTaskUser();
        taskUser.setAccountId(candidate.getOid());
        taskUser.setAccountGid(candidate.getGid());
        taskUser.setAccountName(candidate.getName());
        return taskUser;
    }
}

package com.timevale.contractmanager.core.service.processstart.factory;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.service.processstart.ProcessStartCoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_START_SIGN_FLOW_FAIL;

/**
 * <AUTHOR>
 * @since 2021-08-20
 */
@Slf4j
@Service
public class ProcessStartCoreServiceFactory {

    @Autowired List<ProcessStartCoreService> processStartCoreServiceList;

    public ProcessStartCoreService getService(ProcessStartCoreRequest request) {
        return getService(request.getStartMode());
    }

    public ProcessStartCoreService getService(Integer processStartMode) {
        log.info("factory-get-processStartCoreService");
        for (ProcessStartCoreService processStartCoreService : processStartCoreServiceList) {
            if (processStartCoreService.startMode().getMode() == processStartMode) {
                return processStartCoreService;
            }
        }
        throw new BizContractManagerException(PROCESS_START_SIGN_FLOW_FAIL, "暂不支持当前发起模式");
    }
}

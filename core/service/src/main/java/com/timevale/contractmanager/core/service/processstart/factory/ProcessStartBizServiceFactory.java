package com.timevale.contractmanager.core.service.processstart.factory;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.core.service.processstart.ProcessStartBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_START_SIGN_FLOW_FAIL;

/**
 * <AUTHOR>
 * @since 2021-11-09
 */
@Slf4j
@Service
public class ProcessStartBizServiceFactory {

    @Autowired List<ProcessStartBizService> processStartBizServiceList;

    public ProcessStartBizService getService(Integer scene, Integer type) {
        ProcessStartType startType = ProcessStartType.from(type);
        for (ProcessStartBizService processStartBizService : processStartBizServiceList) {
            if (processStartBizService.startScene().getScene() == scene
                    && processStartBizService.supportStartTypes().contains(startType)) {
                log.info("use processStartBizService, serviceName: {}", processStartBizService.getClass().getSimpleName());
                return processStartBizService;
            }
        }
        throw new BizContractManagerException(PROCESS_START_SIGN_FLOW_FAIL, "暂不支持当前发起场景");
    }
}

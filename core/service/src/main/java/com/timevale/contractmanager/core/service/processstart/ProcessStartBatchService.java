package com.timevale.contractmanager.core.service.processstart;

import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface ProcessStartBatchService {

    /**
     * 支持批量发起的发起类型
     *
     * @return
     */
    List<ProcessStartType> startTypes();
    /**
     * 发起流程
     *
     * @param startRequest
     * @return
     */
    ProcessStartResult start(ProcessStartCoreRequest startRequest);
}

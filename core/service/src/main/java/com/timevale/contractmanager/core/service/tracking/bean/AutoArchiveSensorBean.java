package com.timevale.contractmanager.core.service.tracking.bean;

import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.MENU_ID;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @Author:jiany<PERSON>
 * @since 2021-06-20 17:24
 */
@Data
public class AutoArchiveSensorBean extends BaseAttributeSensorBean {
	/**
	 *
	 */
	private String menuId;

	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = super.sensorData();
		sensorData.put(MENU_ID, sensorString(menuId));
		return sensorData;
	}
}

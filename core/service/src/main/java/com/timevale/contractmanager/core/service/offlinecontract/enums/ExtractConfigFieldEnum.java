package com.timevale.contractmanager.core.service.offlinecontract.enums;

import com.timevale.clmc.facade.api.enums.ExtractFieldTypeEnum;
import lombok.Getter;

/**
 * 合同信息提取配置字段枚举
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Getter
public enum ExtractConfigFieldEnum {
    CONTRACT_NO("contractNo", "合同编号", "合同编号", ExtractFieldTypeEnum.STRING, true),
    SIGNER_NAME("signerName", "签署方姓名", "姓名", ExtractFieldTypeEnum.STRING, true),
    SIGNER_ORGAN_NAME("signerOrgName", "签署方企业名称", "企业名称", ExtractFieldTypeEnum.STRING, true),
    CONTRACT_VALIDITY("contractValidity", "合同到期时间", "合同到期时间", ExtractFieldTypeEnum.DATE, true),
    ;

    ExtractConfigFieldEnum(
            String fieldKey,
            String fieldDesc,
            String fieldKeyword,
            ExtractFieldTypeEnum fieldType,
            boolean extract) {
        this.fieldKey = fieldKey;
        this.fieldDesc = fieldDesc;
        this.fieldKeyword = fieldKeyword;
        this.fieldType = fieldType;
        this.extract = extract;
    }

    /** 字段标识 */
    private String fieldKey;
    /** 字段描述 */
    private String fieldDesc;
    /** 字段关键字 */
    private String fieldKeyword;
    /** 字段值类型 */
    private ExtractFieldTypeEnum fieldType;
    /** 是否默认提取 */
    private boolean extract;

    public static ExtractConfigFieldEnum from(String fieldKey) {
        for (ExtractConfigFieldEnum value : values()) {
            if (value.getFieldKey().equals(fieldKey)) {
                return value;
            }
        }
        return null;
    }
}

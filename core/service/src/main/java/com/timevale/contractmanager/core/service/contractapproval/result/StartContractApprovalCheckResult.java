package com.timevale.contractmanager.core.service.contractapproval.result;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 发起合同审批前置模板校验响应数据
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Data
public class StartContractApprovalCheckResult extends ToString {

    /** 合同审批模板id */
    private String approvalTemplateId;
    /** 合同审批模板条件类型 {@link com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum} */
    private Integer approvalTemplateConditionType;
    /** 合同审批模板条件值 */
    private String approvalTemplateConditionValue;
    /** 是否旧版合同审批模板 */
    private boolean oldApprovalTemplate;
}

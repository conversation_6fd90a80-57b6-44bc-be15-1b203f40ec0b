package com.timevale.contractmanager.core.service.processstart;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.SignClient;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.core.service.other.DingSignService;
import com.timevale.contractmanager.core.service.processstart.impl.ProcessRedirectStartBizServiceImpl;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContext;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM;

/**
 * 钉签附件审批发起
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Service
public class ProcessDingAttachmentStartBizServiceImpl extends ProcessRedirectStartBizServiceImpl
        implements ProcessStartBizService {

    @Autowired SignClient signClient;
    @Autowired DingSignService dingSignService;

    @Override
    public ProcessStartScene startScene() {
        return ProcessStartScene.DIRECT_START;
    }

    @Override
    public List<ProcessStartType> supportStartTypes() {
        return Lists.newArrayList(ProcessStartType.DING_ATTACHMENT_APPROVAL_START);
    }

    @Override
    public ProcessStartResult start(ProcessStartContext processStartContext, ProcessStartBizRequest startRequest) {
        // 钉钉OA审批id
        String instanceId = notBlankAndGet(startRequest.getThirdApprovalInstanceId(), "钉钉OA审批id不能为空");
        // 个人空间不支持附件审批发起
        if (StringUtils.equals(startRequest.getAccountId(), startRequest.getTenantId())) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "个人空间不支持附件审批发起");
        }
        // 钉签corpId、isvAppId
        String corpId = notBlankAndGet(RequestContextExtUtils.getDingCorpId(), "钉钉企业corpId不能为空");
        String isvAppId = notBlankAndGet(RequestContextExtUtils.getIsvAppId(), "钉钉isvAppId不能为空");
        // 发起应用id
        String appId = notBlankAndGet(RequestContextExtUtils.getAppId(), "应用appId不能为空");
        // 文件id列表
        List<String> fileIds =
                startRequest.getContracts().stream()
                        .map(FileBO::getFileId)
                        .collect(Collectors.toList());
        // 校验是否可进行附件审批发起
        dingSignService.checkDingAttachmentStart(corpId, isvAppId, instanceId, fileIds);
        // 获取指定的流程id
        String flowId = startRequest.getAssignFlowId();
        // 如果未指定流程id， 则预生成流程id并绑定钉钉流程群组
        if (StringUtils.isBlank(flowId)) {
            // 预生成流程id
            flowId = signClient.generateFlowId();
            // 绑定钉钉流程群组
            dingSignService.bindDingGroupFlow(appId, instanceId, flowId, fileIds);
        }
        // 发起流程
        try {
            // 设置flowId
            startRequest.setAssignFlowId(flowId);
            // 附件审批发起场景无需校验直接发起权限
            startRequest.setSkipStartValid(true);
            // 发起流程
            return super.start(processStartContext, startRequest);
        } catch (Exception e) {
            // 解绑钉钉流程群组
            dingSignService.revokeDingGroupFlow(appId, instanceId, flowId);
            throw e;
        }
    }

    /**
     * 检验字符串参数不为空并返回， 如果参数为空，抛出指定message的参数异常
     *
     * @param value
     * @param message
     * @return
     */
    private String notBlankAndGet(String value, String message) {
        if (StringUtils.isNotBlank(value)) {
            return value;
        }
        throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, message);
    }
}

package com.timevale.contractmanager.core.service.autoarchive.archivestrategy;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.service.enums.autoarchive.OperationConditionStrategyEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.dto.autoarchive.ArchiveTemplateDTO;
import com.timevale.contractmanager.core.service.autoarchive.OperationConditionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author:jianyang
 *
 * @since 2021-05-10 16:55
 */
@Service
@Slf4j
public class ContractTemplateConditionServiceImpl extends AbstractOperationService
        implements OperationConditionService {

    @Override
    public Boolean checkCondition(
            String fieldId,
            String tenantOid,
            String tenantGid,
            String ruleId,
            String conditionParams) {
        List<ArchiveTemplateDTO> templateParams =
                JSON.parseArray(conditionParams, ArchiveTemplateDTO.class);
        if (templateParams == null || templateParams.isEmpty()) {
            return false;
        }
        return true;
    }

    @Override
    public String getFieldId() {
        return OperationConditionStrategyEnum.CONTRACT_TEMPLATE.getFieldId();
    }

    @Override
    public BizContractManagerException getErrorException() {
        // code为fieldId区分是那个条件失效
        return new BizContractManagerException(
                OperationConditionStrategyEnum.CONTRACT_TEMPLATE.getFieldId(),
                BizContractManagerResultCodeEnum.TEMPLATE_NOT_EXIST.getMessage());
    }
}

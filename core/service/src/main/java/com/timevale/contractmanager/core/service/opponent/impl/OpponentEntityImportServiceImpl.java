package com.timevale.contractmanager.core.service.opponent.impl;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.FORMAT_ERROR;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.FileSystemClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.bo.opponent.ExcelInfoBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEntityExcelBO;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentIndividualCreateRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.OpponentOrganizationCreateRequest;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentBaseResponse;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentEntityExcelResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.auditlog.handler.AuditLogHelper;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.component.opponent.OpponentEntityConverter;
import com.timevale.contractmanager.core.service.component.opponent.excel.OpponentEntityExcelHelper;
import com.timevale.contractmanager.core.service.dto.opponent.OpponentImportResultDTO;
import com.timevale.contractmanager.core.service.enums.OpponentBusinessTagEnum;
import com.timevale.contractmanager.core.service.mq.model.opponent.OpponentImportMsg;
import com.timevale.contractmanager.core.service.mq.producer.OpponentMqProducer;
import com.timevale.contractmanager.core.service.opponent.OpponentConfigCenter;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityImportService;
import com.timevale.contractmanager.core.service.opponent.OpponentEntityService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.contractmanager.core.service.util.Utils;
import com.timevale.dayu.sdk.annotation.AuditLogAnnotation;
import com.timevale.filesystem.common.service.result.GetFileInfoResult;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;

import com.timevale.saas.common.manage.common.service.enums.TaskStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.TaskTypeEnum;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskAddInput;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskUpdateInput;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskAddBean;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskBizInfo;
import com.timevale.saas.common.manage.common.service.model.output.SaasUnderwayTaskResultOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 批量导入service实现
 *
 * <AUTHOR>
 * @since 2021-02-01 17:23
 */
@Slf4j
@Service
public class OpponentEntityImportServiceImpl implements OpponentEntityImportService {

    @Value("${opponent.import.template.organization}")
    private String organizationTemplateFileKey;

    @Value("${opponent.import.template.individual}")
    private String individualTemplateFileKey;

    @Value("${opponent.import.count:500}")
    private Integer importOpponentCount;

    private List<List<String>> organizationHeaders;
    private List<List<String>> individualHeaders;
    private static final String ORG_ERROR_NAME = "批量导入相对方-企业-失败详情";
    private static final String IND_ERROR_NAME = "批量导入相对方-个人-失败详情";
    private static final Integer TASK_TYPE = TaskTypeEnum.OPPONENT_IMPORT.getType();

    @Autowired private FileSystemClient fileSystemClient;
    @Autowired private UserCenterService userCenterService;
    @Autowired private OpponentEntityExcelHelper opponentEntityExcelHelper;
    @Autowired private OpponentEntityService opponentEntityService;
    @Autowired private OpponentEntityDAO opponentEntityDAO;
    @Autowired
    private OpponentConfigCenter configCenter;
    @Autowired
    private SaasCommonClient saasCommonClient;
    @Autowired
    private OpponentMqProducer opponentMqProducer;

    @Override
    public String getDownloadUrl(OpponentEntityTypeEnum entityType) {
        String key = CacheUtil.getOpponentTemplateKey(entityType.getType());
        String downloadUrl = TedisUtil.get(key);
        // 缓存中有数据,则直接获取缓存数据`
        if (StringUtils.isNotBlank(downloadUrl)) {
            return downloadUrl;
        }

        // 根据类型获取模板下载地址
        if (OpponentEntityTypeEnum.ORGANIZATION == entityType) {
            downloadUrl = fileSystemClient.getDownloadUrl(organizationTemplateFileKey);
        } else {
            downloadUrl = fileSystemClient.getDownloadUrl(individualTemplateFileKey);
        }

        // 缓存
        TedisUtil.set(key, downloadUrl, 10, TimeUnit.MINUTES);

        return downloadUrl;
    }

    @AuditLogAnnotation(
            enterpriseSpaceUnique1 = "#tenantOid",
            userUnique1 = "#operatorId",
            resourceEntSpaceUnique = "#tenantOid",
            result = "#_result != null ? " + AuditLogConstant.RESULT,
            resourceId = "#fileKey",
            resourceName = "\"批量导入相对方\"",
            detailTactics = "1",
            selfDefiningData =
                    "{{T(com.google.common.collect.ImmutableMap).of(\"opponentCreateMethod\", \"批量导入\")}}",
            postHandle = "auditLogOpponentBatchImportHandle")
    @Override
    public OpponentEntityExcelResponse batchImport(
            String operatorOid,
            String tenantOid,
            String fileKey,
            OpponentEntityTypeEnum entityType) {
        AuditLogHelper.acceptHeaderFields();

        // todo tianlei 上线后改为true, 下个迭代删除
        if (ConfigService.getAppConfig().getBooleanProperty("opponent.import.old", true)) {
            // 兼容默认同步
            OpponentImportMsg importMsg = new OpponentImportMsg();
            importMsg.setFileKey(fileKey);
            importMsg.setTenantOid(tenantOid);
            importMsg.setOperatorOid(operatorOid);
            importMsg.setOpponentEntityType(entityType.getType());
            OpponentEntityExcelResponse response = doImport(importMsg);
            response.setErrorDataExcelDownloadUrl(fileSystemClient.getDownloadUrl(response.getFileKey(), false));
            return response;
        }
        
        // 1. 校验excel大小
        GetFileInfoResult fileInfo = fileSystemClient.getFileInfo(fileKey);
        int maxSizeM = configCenter.importExcelMaxSizeM();
        int excelBytesLimit = Utils.m2Byte(maxSizeM);
        // 如果文件大小超过限制，直接阻断
        AssertX.isTrue(fileInfo.getFileSize() <= excelBytesLimit, () -> {
            log.warn("opponent import excel size: {}", fileInfo.getFileSize());
            return String.format("导入excel大小不能超过 %dM", maxSizeM);
        });

        // 2. 校验任务重复
        SaasUnderwayTaskResultOutput checkExistResult =
                saasCommonClient.queryUnderwayTaskByAccountIdAndType(operatorOid, TASK_TYPE);
        AssertX.isTrue(null == checkExistResult || StringUtils.isBlank(checkExistResult.getBizId()), "请等待上一个导入任务结束");

        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(operatorOid);

        // 3. 创建新任务
        String bizId = UUIDUtil.genUUID();
        addImportTask(tenantOid, operatorOid, userAccount.getAccountGid(), bizId, "相对方批量导入");

        // 4. 发消息
        OpponentImportMsg importMsg = new OpponentImportMsg();
        importMsg.setFileKey(fileKey);
        importMsg.setBizId(bizId);
        importMsg.setTenantOid(tenantOid);
        importMsg.setOperatorOid(operatorOid);
        importMsg.setOpponentEntityType(entityType.getType());
        opponentMqProducer.sendMessage(JSONObject.toJSONString(importMsg), OpponentBusinessTagEnum.IMPORT.getType());
        return new OpponentEntityExcelResponse();
    }

    @Override
    public void asyncImportOpponent(OpponentImportMsg importMsg) {
        Integer status = null;
        String reason = null;
        OpponentEntityExcelResponse response = null;
        try {
            response = doImport(importMsg);
            status = StringUtils.isBlank(response.getFileKey()) ?
                    TaskStatusEnum.FINISH.getType() : TaskStatusEnum.FAILED.getType();
        } catch (Exception e) {
            log.info("opponent import failure req : {}", JSON.toJSONString(importMsg), e);
            status = TaskStatusEnum.FAILED.getType();
            if (e instanceof BizContractManagerException) {
                reason = e.getMessage();
            } else {
                reason = "导入失败请联系客服";
            }
        }
        SaasTaskUpdateInput saasInput = new SaasTaskUpdateInput();
        saasInput.setDone(1L);
        saasInput.setBizId(importMsg.getBizId());
        saasInput.setType(TASK_TYPE);
        saasInput.setStatus(status);
        saasInput.setReason(reason);
        if (null != response && StringUtils.isNotBlank(response.getFileKey())) {
            saasInput.setResult(JSON.toJSONString(new OpponentImportResultDTO(response.getFileKey())));
        }
        saasCommonClient.updateTask(saasInput);
    }
    
    private OpponentEntityExcelResponse doImport(OpponentImportMsg importMsg) {
        String tenantOid = importMsg.getTenantOid();
        String operatorOid = importMsg.getOperatorOid();
        String fileKey = importMsg.getFileKey();
        OpponentEntityTypeEnum entityType = OpponentEntityTypeEnum.getByType(importMsg.getOpponentEntityType());
        
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(operatorOid);
        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        // 读取表格内容
        OpponentEntityExcelBO excelBO = opponentEntityExcelHelper.readExcel(fileKey, entityType);

        // 获取导入数量
        int count = excelBO.getList().size();

        // 空表格直接返回
        if (count == 0) {
            return new OpponentEntityExcelResponse();
        }
        // 数量校验
        if (count > importOpponentCount) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.OPPONENT_PARSE_BATCH_EXCEL_COUNT_FAIL,
                    importOpponentCount.toString());
        }

        // 校验多版本数量
        opponentEntityService.checkCreateLimit(
                tenantOid, tenant.getAccountGid(), operatorOid, entityType.getType(), count - 1,
                RequestContextExtUtils.getClientId());
        // 判断是相对方企业导入或是相对方个人导入
        if (OpponentEntityTypeEnum.ORGANIZATION == entityType) {
            // 导入相对方企业
            return batchImportOrganization(userAccount, tenant, excelBO);
        }

        // 导入相对方个人
        return batchImportIndividuals(userAccount, tenant, excelBO);
    }

    public void addImportTask( String subjectId, String accountId, String accountGid, String bizId, String taskName) {
        SaasTaskAddInput taskAddInput = new SaasTaskAddInput();
        TaskAddBean taskAddBean = new TaskAddBean();
        taskAddBean.setAccountOid(accountId);
        taskAddBean.setAccountGid(accountGid);
        taskAddBean.setBizId(bizId);
        taskAddBean.setTotal(1L);
        taskAddBean.setType(TASK_TYPE);
        taskAddBean.setName(taskName);
        taskAddInput.setTasks(Lists.newArrayList(taskAddBean));
        TaskBizInfo taskBizInfo = new TaskBizInfo();
        taskBizInfo.setOrganizationId(subjectId);
        taskAddBean.setBizInfo(taskBizInfo);
        saasCommonClient.addTasks(taskAddInput);
    }

    /**
     * 批量导入相对方企业信息
     *
     * @param userAccount 当前操作人
     * @param tenant 企业空间id
     * @param excelBO 导入数据
     * @return 导入结果
     */
    private OpponentEntityExcelResponse batchImportOrganization(
            UserAccount userAccount, UserAccount tenant, OpponentEntityExcelBO excelBO) {

        List<ExcelInfoBO> list = excelBO.getList();
        // 失败索引
        Map<Integer, String> errorMap = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            ExcelInfoBO entity = list.get(i);
            // 转换对象为添加企业的入参对象
            OpponentOrganizationCreateRequest request =
                    OpponentEntityConverter.toOrganizationCreate(entity);
            try {
                // 校验入参
                opponentEntityService.validOrganizationCreateRequest(request);
                // 调用添加企业方法
                opponentEntityService.createOrganization(null, userAccount, tenant, request);
            }
            /*catch (BizContractManagerException e){
                //
            } */
            catch (Exception e) {
                // 记录失败信息
                errorMap.put(i, e.getMessage());
            }
        }
        return getExcelResponse(organizationHeaders, errorMap, excelBO.getData(), ORG_ERROR_NAME);
    }

    /**
     * 批量导入相对方个人数据
     *
     * @param userAccount 当前操作人
     * @param tenant 企业空间id
     * @param excelBO 导入数据
     * @return 导入结果
     */
    private OpponentEntityExcelResponse batchImportIndividuals(
            UserAccount userAccount, UserAccount tenant, OpponentEntityExcelBO excelBO) {
        List<ExcelInfoBO> list = excelBO.getList();
        // 失败索引
        Map<Integer, String> errorMap = new HashMap<>();
        // 企业名称map key为企业名称 value为该相对方企业的主键id
        Map<String, OpponentEntityDO> organizationNameMap = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            ExcelInfoBO entity = list.get(i);
            // 转换对象为添加个人的入参对象
            OpponentIndividualCreateRequest request =
                    OpponentEntityConverter.toIndividualCreate(entity);
            try {
                // 校验入参
                ValidationUtil.validateBean(request);
                String contact = request.getContact();
                // 校验手机号/邮箱格式
                if (!Utils.isEmail(contact) && !Utils.isTel(contact)) {
                    throw new BizContractManagerException(FORMAT_ERROR);
                }
                // 获取所属企业 企业不存在则创建相对方企业
                List<OpponentEntityDO> entityList = new ArrayList<>();
                OpponentEntityDO orgEntity=
                        getEntityIdByNameCreateOrg(
                                userAccount,
                                tenant,
                                organizationNameMap,
                                entity.getOrganizationName());
                if(orgEntity != null){
                    request.setOrganizationIds(Sets.newHashSet(orgEntity.getUuid()));
                    entityList.add(orgEntity);
                }
                // 调用添加个人方法
                opponentEntityService.createIndividual(
                        null, userAccount, tenant, request, entityList);
            } catch (Exception e) {
                // 记录失败信息
                errorMap.put(i, e.getMessage());
            }
        }
        return getExcelResponse(individualHeaders, errorMap, excelBO.getData(), IND_ERROR_NAME);
    }

    /**
     * 获取表格解析结果
     *
     * @param headers 表格头
     * @param errorMap 解析错误行数及错误信息
     * @param data 表格信息
     * @param fileName 导出表格名称
     * @return 表格结果
     */
    private OpponentEntityExcelResponse getExcelResponse(
            List<List<String>> headers,
            Map<Integer, String> errorMap,
            List<List<Object>> data,
            String fileName) {
        int errorCount = errorMap.size();
        OpponentEntityExcelResponse response = new OpponentEntityExcelResponse();
        response.setErrorCount(errorCount);
        response.setSuccessCount(data.size() - errorCount);
        // 全部导入成功,直接返回即可
        if (MapUtils.isEmpty(errorMap)) {
            return response;
        }
        // 有错误信息,则构建错误信息表格
        String download =
                opponentEntityExcelHelper.buildErrorExcel(headers, errorMap, data, fileName);
        response.setFileKey(download);
        return response;
    }

    /**
     * 根据名称获取相对方企业,如果不存在该相对方企业则创建
     *
     * @param userAccount 个人账号
     * @param tenant 企业账号
     * @param organizationNameMap 已经获取过的企业名称缓存
     * @param orgName 企业名称
     * @return 相对方企业主键id
     */
    private OpponentEntityDO getEntityIdByNameCreateOrg(
            UserAccount userAccount,
            UserAccount tenant,
            Map<String, OpponentEntityDO> organizationNameMap,
            String orgName) {
        // 判断该企业名称是否已经获取过,已经获取过则直接返回即可
        if (StringUtils.isBlank(orgName) || organizationNameMap.containsKey(orgName)) {
            return organizationNameMap.get(orgName);
        }
        OpponentEntityDO entity =
            opponentEntityDAO.getByEntityUniqueId(
                orgName, tenant.getAccountGid(), OpponentEntityTypeEnum.ORGANIZATION.getType());

        // 如果相对方企业已经存在,返回数据库数据
        if (null != entity) {
            organizationNameMap.put(orgName, entity);
            return entity;
        }
        // 创建相对方企业并返回主键id
        OpponentOrganizationCreateRequest request = new OpponentOrganizationCreateRequest();
        request.setOrganizationName(orgName);
        OpponentBaseResponse response =
                opponentEntityService.createOrganization(null, userAccount, tenant, request);
        OpponentEntityDO entityDO =  OpponentEntityDO.builder().id(response.getId()).uuid(response.getUuid()).build();
        organizationNameMap.put(orgName, entityDO);
        return entityDO;
    }

    @Value("${opponent.organization.headers}")
    public void setOrganizationHeaders(String header) {
        List<String> headers = JsonUtils.json2list(header, String.class);
        this.organizationHeaders =
                headers.stream().map(Collections::singletonList).collect(Collectors.toList());
    }

    @Value("${opponent.individual.headers}")
    public void setIndividualHeaders(String header) {
        List<String> headers = JsonUtils.json2list(header, String.class);
        this.individualHeaders =
                headers.stream().map(Collections::singletonList).collect(Collectors.toList());
    }
}

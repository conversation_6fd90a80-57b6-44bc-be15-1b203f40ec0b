package com.timevale.contractmanager.core.service.transfer;

import com.timevale.contractmanager.core.model.bo.transfer.SingleTransferResultBO;
import com.timevale.contractmanager.core.model.bo.transfer.TransferUserListBO;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.transfer.TransferResultDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.signflow.search.docSearchService.bean.Account;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/11/25
 */
public interface TransferBizService {
    /**
     * 根据转交场区分是否支持当前转交业务
     *
     * @param transferScene 转交场景值 {@link
     *     com.timevale.contractmanager.common.service.enums.TransferSceneEnum}
     * @return true or false
     */
    Boolean isSupport(Integer transferScene);

    /**
     * 转交接口 1 合同转交 2 用印转交
     *
     * @param transferBizContext 转交上下文
     */
    TransferResultDTO transfer(TransferBizContext transferBizContext);

    /**
     * 获取用户的转交数量
     *
     * @param userAccounts 用户账户信息
     * @param tenantAccount 主体账户信息
     * @return 用户可转交数 key: accountOid,value:总数
     */
    Map<String, Long> transferCount(UserAccountDetail tenantAccount, List<Account> userAccounts);

    /**
     * 可转交用户列表
     *
     * @param transferUserListBO 请求参数
     * @return 可转交用户列表
     */
    OrgDeptListResponse transferUserList(TransferUserListBO transferUserListBO);

    /**
     * 获取用户被转交总数
     *
     * @param tenantId 主体id
     * @param accountOid 用户oid
     * @return 返回用户被转交数
     */
    Integer transferToUserCount(String tenantId, String accountOid);

    /**
     * 设置用户被转交数： 发生异常可降级，不影响业务主流程
     *
     * @param tenantId 主体id
     * @param accountOid 用户oid
     * @return 返回转交的总数
     */
    Long addTransferToUserCount(String tenantId, String accountOid);

    /**
     * 单个转交任务结果回调
     *
     * @param transferResultBO 单个转交任务结束参数
     */
    void singleTransferResultCallback(SingleTransferResultBO transferResultBO);
}

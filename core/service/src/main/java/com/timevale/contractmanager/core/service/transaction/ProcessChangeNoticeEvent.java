package com.timevale.contractmanager.core.service.transaction;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Collection;

@Getter
public class ProcessChangeNoticeEvent extends ApplicationEvent {

    /** 流程id列表 */
    private Collection<String> processIds;
    /**
     * 变更类型
     *
     * @see com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum
     */
    private String changeType;
    /** 是否异步通知 */
    private boolean asyncNotice;
    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public ProcessChangeNoticeEvent(
            Object source, Collection<String> processIds, String changeType, boolean asyncNotice) {
        super(source);
        this.processIds = processIds;
        this.changeType = changeType;
        this.asyncNotice = asyncNotice;
    }
}

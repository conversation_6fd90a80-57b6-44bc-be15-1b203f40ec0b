package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.*;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.consumer.searchsync.ProcessSearchParamBuilder;
import com.timevale.contractmanager.core.service.mq.model.ProcessEsTransferNotifyMsgEntity;
import com.timevale.contractmanager.core.service.mq.model.ProcessTransferMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ProcessEsTransferNotifyProducer;
import com.timevale.contractmanager.core.service.util.TaskIdUtil;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.enums.ProcessTypeEnum;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessCooperationTaskDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.request.datacollect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/5/10
 */
@Slf4j
@Component
public class ProcessTransferDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessReadClient contractProcessQueryClient;
    @Autowired
    private ProcessSearchParamBuilder processSearchParamBuilder;
    @Autowired
    private LockService lockService;
    @Autowired
    private ProcessEsTransferNotifyProducer processEsTransferNotifyProducer;


    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processTopicName(), ProcessChangeTagEnum.PROCESS_TRANSFER.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessTransferMsgEntity entity = JsonUtils.json2pojo(data, ProcessTransferMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {

        String processId = collectContext.getProcessId();

        ProcessTransferMsgEntity entity = (ProcessTransferMsgEntity) collectContext.getData();

        AccountBean tenantAccount = entity.getTenantAccount();
        AccountBean oldPersonAccount = entity.getOriginalAccount();
        oldPersonAccount.setOrgan(false);
        AccountBean newPersonAccount = entity.getTransferAccount();
        newPersonAccount.setDeleted(Optional.ofNullable(newPersonAccount.getDeleted()).orElse(false));
        newPersonAccount.setOrgan(false);

        // 填写变更的taskId
        Set<Long> writeChangeTaskIds = new HashSet<>(Optional.ofNullable(entity.getTaskIds()).orElse(new ArrayList<>()));
        // 签署变更的taskId映射关系
        Map<String, String> signChangeTaskIdMap = Optional.ofNullable(entity.getTaskIdMapping()).orElse(Maps.newHashMap());

        ContractProcessDTO contractProcessDTO = contractProcessQueryClient.getByProcessId(processId);

        ContractProcessUpdateParam updateParam = new ContractProcessUpdateParam();
        updateParam.setProcessId(processId);

        // 发起人修改
        if (entity.isInitiatorTransferred()) {

            if ((Objects.equals(oldPersonAccount.getGid(), contractProcessDTO.getInitiator().getPerson().getGid()) ||
                    Objects.equals(oldPersonAccount.getOid(), contractProcessDTO.getInitiator().getPerson().getOid()))
                    &&
                    (Objects.equals(tenantAccount.getGid(), contractProcessDTO.getInitiator().getSubject().getGid()) ||
                    Objects.equals(tenantAccount.getOid(), contractProcessDTO.getInitiator().getSubject().getOid()))) {
                //
                ProcessAccount processAccount = contractProcessDTO.getInitiator();
                processAccount.setTransfer(true);
                processAccount.setPerson(ProcessDataCollectConverter.accountBean2Account(newPersonAccount));
                processSearchParamBuilder.fillInitiatorDept(processId, processAccount);
                ProcessAccountParam accountParam = ProcessDataCollectConverter.account2Param(processAccount);
                updateParam.setInitiator(accountParam);
                //
            }

            // 反查的原因因为有部门，转交后部门可能不同, 为什么可以不用考虑 hidden 可以隐藏的合同都是无效的合同，所以这里不用考虑
            // 这个走不通，收到消息时数据库发起人还未变更
//            ProcessAccountParam accountParam = processDataBuilder.getProcessInitiator(processId);
//            accountParam.setTransfer(true);
//            updateParam.setInitiator(accountParam);
        }

        // 抄送人修改
        if (entity.isCcTransferred()) {
            if (CollectionUtils.isNotEmpty(contractProcessDTO.getCc())) {
                List<ProcessAccountParam> cc = ProcessDataCollectConverter.account2ParamList(contractProcessDTO.getCc());
                for (ProcessAccountParam ccAccountParam : cc) {
                    if (oidOrGidEqual(ccAccountParam.getPerson(), oldPersonAccount) &&
                            oidOrGidEqual(ccAccountParam.getTenant(), tenantAccount)) {
                        ccAccountParam.setTransfer(true);
                        // 换成新的人
                        ccAccountParam.setPerson(ProcessDataCollectConverter.account2Param(newPersonAccount));
                    }
                }
                //可能出现a,b -> b,b的情况，需要过滤掉重复项
                AtomicBoolean has = new AtomicBoolean(true);
                cc = cc.stream().filter(accountParam ->  {
                    if (oidOrGidEqual(accountParam.getPerson(), newPersonAccount) &&
                            oidOrGidEqual(accountParam.getTenant(), newPersonAccount)) {
                        return has.getAndSet(false);
                    }
                    return true;
                }).collect(Collectors.toList());
                updateParam.setCc(cc);
            }
        }

        // 仅是发起人和抄送人变更，没有修改task
        // 先签后填的场景，没填完的时候发生转交 signTransferred = true 、 signChangeIdMap 为空, 因为签署任务还未生成
        // 只有签署的场景，转交               signTransferred = false、 signChangeIdMap 不为空
//        if (CollectionUtils.isEmpty(writeChangeTaskIds) &&
//                (MapUtils.isEmpty(signChangeIdMap) && !Boolean.TRUE.equals(entity.isSignTransferred()))) {
//            // 只有发起人或者抄送人变更
//            if (updateParam.getCc() != null ||  updateParam.getInitiator() != null) {
//                contractProcessWriteClient.updateByProcessId(updateParam);
//            }
//            return;
//        }


        Lock lock = lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                ContractProcessDTO contractProcessDTOLast = contractProcessQueryClient.getByProcessId(processId);

                // 填写变更
                if (CollectionUtils.isNotEmpty(writeChangeTaskIds)) {
                    List<ContractProcessCooperationTaskDTO> cooperationTasks = contractProcessDTOLast.getCooperationTasks();
                    List<ContractProcessCooperationTaskParam> cooperationTaskParams =
                            ProcessDataCollectConverter.cooperationTask2ParamList(cooperationTasks);
                    for (ContractProcessCooperationTaskParam taskParam : cooperationTaskParams) {
                        if (writeChangeTaskIds.contains(Long.valueOf(taskParam.getTaskId())) &&
                                // 判断人
                                oidOrGidEqual(taskParam.getExecute().getPerson(), oldPersonAccount) &&
                                oidOrGidEqual(taskParam.getExecute().getTenant(), tenantAccount)) {

                            taskParam.getExecute().setPerson(ProcessDataCollectConverter.account2Param(newPersonAccount));
                            taskParam.setTransfer(true);
                            updateParam.setCooperationTasks(cooperationTaskParams);
                        }
                    }
                }
                // 获取签署任务列表
                List<ContractProcessSignTaskParam> signTaskParamList =
                        Optional.ofNullable(ProcessDataCollectConverter.signTaskDTO2ParamList(contractProcessDTOLast.getSignTasks()))
                                .orElse(new ArrayList<>());
                // 更新签署任务， 签署人转交 或 非签署流程转交场景（比如填写流程、审批流程）都需要更新签署任务
                if (entity.isSignerTransferred() || !ProcessTypeEnum.SIGNING.getType().equals(contractProcessDTO.getProcessType())) {
                    for (ContractProcessSignTaskParam taskParam : signTaskParamList) {
                        // 执行转交
                        boolean transferred = transferSignTaskAccount(taskParam, tenantAccount, oldPersonAccount, newPersonAccount);
                        if (transferred && StringUtils.isNotBlank(taskParam.getTaskId())) {
                            // 原来的taskId 不为null,  说明这个是真实的任务，需要重新构建下 taskId. 为null. 在signStart的时候会构建taskId
                            String newTaskId = TaskIdUtil.generateTaskId(taskParam.getFlowId(),
                                    taskParam.getExecute().getPerson().getOid(),
                                    taskParam.getExecute().getTenant().getOid(),
                                    taskParam.getOrder());
                            taskParam.setTaskId(newTaskId);
                        }
                    }
                    updateParam.setSignTasks(signTaskParamList);
                }

                //fix 2022年6月份之前发起的合同，到现在还未填写完成的合同，转交导致es participant 字段里的人未转交问题
                if (CollectionUtils.isNotEmpty(contractProcessDTO.getParticipant())) {
                    List<ContractProcessTempParticipantParam> participantParams =
                            ProcessDataCollectConverter.participantDTO2Param(contractProcessDTO.getParticipant());
                    for (ContractProcessTempParticipantParam param : participantParams) {
                        if (oidOrGidEqual(param.getPerson(), oldPersonAccount) &&
                                oidOrGidEqual(param.getSubject(), tenantAccount)) {
                            param.setPerson(ProcessDataCollectConverter.account2Param(newPersonAccount));
                            param.setTransfer(true);
                            updateParam.setParticipant(participantParams);
                        }
                    }
                }

                update(updateParam);
                log.info(ProcessDataCollectSupport.LOG_PREFIX + "transfer success processId: {}", entity.getProcessId());
                //成功发送消息，用于转交回调，轩辕判断合同是否最终转交完成
                if (dataCollectConfigCenter.newTransferMsgSwitch()) {
                    if (StringUtils.isNotBlank(entity.getBizTaskId())) {
                        ProcessEsTransferNotifyMsgEntity msgEntity = new ProcessEsTransferNotifyMsgEntity(entity.getProcessId(),
                                entity.getBizTaskId(), entity.getOriginalAccount().getOid(), entity.getTenantAccount().getOid(), true, entity.getTransferToAccountId());
                        log.info(ProcessDataCollectSupport.LOG_PREFIX + "send transfer helpMsg processId: {}", entity.getProcessId());
                        processEsTransferNotifyProducer.sendMessage(msgEntity);
                    }
                }

            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }

    /**
     * 转交签署任务用户信息
     *
     * @param tenant
     * @param oldPerson
     * @param newPerson
     * @param taskParam
     */
    private boolean transferSignTaskAccount(
            ContractProcessSignTaskParam taskParam, AccountBean tenant, AccountBean oldPerson, AccountBean newPerson) {
        boolean transferred = false;
        // 判断签署人
        if (null != taskParam.getExecute()
                && oidOrGidEqual(taskParam.getExecute().getPerson(), oldPerson)
                && oidOrGidEqual(taskParam.getExecute().getTenant(), tenant)) {
            taskParam.getExecute().setPerson(ProcessDataCollectConverter.account2Param(newPerson));
            taskParam.setTransfer(true);
            transferred = true;
        }
        // 判断操作人
        if (null != taskParam.getOperator()
                && oidOrGidEqual(taskParam.getOperator().getPerson(), oldPerson)
                && oidOrGidEqual(taskParam.getOperator().getTenant(), tenant)) {
            taskParam.getOperator().setPerson(ProcessDataCollectConverter.account2Param(newPerson));
            taskParam.setTransfer(true);
            transferred = true;
        }
        return transferred;
    }

    private void update(ContractProcessUpdateParam updateParam) {
        if (null != updateParam.getInitiator() || null != updateParam.getCc() ||
                null != updateParam.getSignTasks() || null != updateParam.getCooperationTasks() ||
                null != updateParam.getParticipant()) {
            updateParam.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_TRANSFER);
            contractProcessWriteClient.updateByProcessId(updateParam);
        }
    }


    private boolean oidOrGidEqual(AccountParam accountParam, AccountBean newPersonAccount) {
        return Objects.equals(accountParam.getOid(), newPersonAccount.getOid()) ||
                Objects.equals(accountParam.getGid(), newPersonAccount.getGid());
    }


}

package com.timevale.contractmanager.core.service.fulfillment;

import com.timevale.contractmanager.common.service.bean.CountDetail;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchSaveModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchUpdateModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRecordBatchSaveResult;
import com.timevale.contractmanager.core.model.dto.response.ProcessHomeListResponse;
import com.timevale.contractmanager.core.model.dto.response.todocenter.SubjectTodoCountDTOHolder;
import com.timevale.contractmanager.core.model.dto.response.todocenter.TypeTodoCountDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;

import java.util.List;

/**
 * ContractFulfillmentRecordService
 *
 * <AUTHOR>
 * @since 2023/10/11 4:31 下午
 */
public interface ContractFulfillmentRecordService {

    ContractFulfillmentRecordBatchSaveResult batchSave(ContractFulfillmentRecordBatchSaveModel model);

    void batchDelete(List<String> recordIds);

    void batchUpdate(ContractFulfillmentRecordBatchUpdateModel model);

    void updateStatus(String tenantId, String accountId, String recordId, String status);

    void batchUpdateStatus(String tenantId, String accountId, List<String> recordIdList, String status);

    TypeTodoCountDTO queryFulfillmentCount(String userOid, String userGid);

    TypeTodoCountDTO querySubjectFulfillmentCount(String userOid, String userGid, String subjectGid, boolean queryCurrentSubject);

    List<SubjectTodoCountDTOHolder> aggregateFulfillment(
            String userOid, String userGid);

    ProcessHomeListResponse queryFulfillmentRecordList(UserAccount person, UserAccount subject, String type);

    List<CountDetail> queryFulfillmentCount(UserAccount person, UserAccount subject);
}

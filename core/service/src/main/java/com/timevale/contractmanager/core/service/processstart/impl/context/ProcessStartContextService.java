package com.timevale.contractmanager.core.service.processstart.impl.context;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.Preference;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.dto.request.QueryPreferenceRequest;
import com.timevale.contractmanager.core.model.dto.response.PreferenceResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.process.impl.ProcessCommonSupport;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.doccooperation.service.model.CooperationerStruct;
import com.timevale.doccooperation.service.result.FlowTemplateStructResult;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.doccooperation.service.util.LambdaUtil;
import com.timevale.docmanager.service.model.StructComponent;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.manage.common.service.model.input.AccountVipFunctionsInput;
import com.timevale.saas.common.manage.common.service.model.output.AccountVipFunctionsOutput;
import com.timevale.saas.common.manage.common.service.model.output.bean.VipFunction;
import com.timevale.tlcache.TLConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 流程发起上下文处理类
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Service
public class ProcessStartContextService {

    private static final List<String> FUNCTION_CODES = Lists.newArrayList(
            FunctionCodeConstant.AI_HAND_DRAW,
            FunctionCodeConstant.SAAS_WATERMARK,
            FunctionCodeConstants.SET_PROCESS_SECRET,
            FunctionCodeConstants.PARTICIPANTS_NUM,
            FunctionCodeConstant.INITIATE_CONTRACT_EXPIRE,
            FunctionCodeConstant.ORG_APPROVE_TEMPLATE_MANAGE,
            FunctionCodeConstants.RISK_LEVEL,
            FunctionCodeConstant.BATCH_INITIATE,
            FunctionCodeConstant.MULTIPART_BATCH_INITIATE,
            FunctionCodeConstant.SAAS_START_WILL_TYPE_SPECIFY,
            FunctionCodeConstant.TEMPLATE_ADVANCED_COMPONENT,
            FunctionCodeConstant.SAAS_START_SEAL_SPECIFY,
            FunctionCodeConstant.SAAS_START_FORCE_READ,
            FunctionCodeConstant.SIGN_REMARK,
            FunctionCodeConstant.SIGNER_ATTACHMENT,
            FunctionCodeConstants.ATTACHMENT_CONFIG_NUM,
            FunctionCodeConstants.ATTACHMENT_CONFIG_CHECK,
            FunctionCodeConstants.OR_SIGN,
            FunctionCodeConstants.SIGNING_STATEMENT,
            FunctionCodeConstants.SHARE_SCAN_SIGN);

    private static final List<String> PREFERENCE_KEYS = Lists.newArrayList(
            ProcessPreferenceEnum.PROCESS_WILL_TYPES.getKey(),
            ProcessPreferenceEnum.ALLOW_INTRA_SIGNATORY_CONTRACTS.getKey(),
            ProcessPreferenceEnum.CONTRACT_PERSON_SIGN_METHOD.getKey(),
            ProcessPreferenceEnum.FDA_SIGNATURE.getKey(),
            ProcessPreferenceEnum.PROCESS_SIGN_FILED_SIZE_TYPE.getKey());

    @Autowired SaasCommonClient saasCommonClient;
    @Autowired UserCenterService userCenterService;
    @Autowired FlowTemplateService flowTemplateService;
    @Autowired private PreferencesService preferencesService;
    @Autowired private ProcessCommonSupport processCommonSupport;

    /**
     * 初始化流程发起业务上下文
     */
    public ProcessStartContext initProcessStartContext(InitProcessStartContextRequest request) {

        String accountId = request.getAccountOid();
        String tenantId = request.getSubjectOid();
        String operatorId = StringUtils.defaultIfBlank(request.getOperatorOid(), accountId);
        String spaceId = StringUtils.isNotBlank(request.getSpaceOid()) ? request.getSpaceOid() : request.getSubjectOid();
        String clientId = request.getClientId();
        String appName = request.getAppName();
        String appId = request.getAppId();

        // 查询发起主体信息
        UserAccountDetail tenant = userCenterService.getUserAccountDetailByOid(tenantId);
        // 查询发起人信息
        UserAccountDetail account = userCenterService.getUserAccountDetailByOid(accountId);
        // 查询操作人信息
        UserAccountDetail operator =
                StringUtils.equals(accountId, operatorId) ? account : userCenterService.getUserAccountDetailByOid(operatorId);
        // 指定上下文中会用到的所有会员功能标识， 必须指定
        UserAccountDetail spaceTenant = tenant;
        if(!tenantId.equals(spaceId)){
            spaceTenant = userCenterService.getUserAccountDetailByOid(spaceId);
        }

        // 获取会员功能MAP
        Map<String, VipFunction> functionMap = queryVipFunctionMap(tenant.getAccountOid(), request.getFunctionCodes());

        // 企业合同偏好设置
        Map<String, String> preferenceData = preferenceData(tenant, request.getPreferenceKeys());

        // 初始化流程发起业务上下文
        ProcessStartContext context = new ProcessStartContext(account, tenant, spaceTenant, functionMap, preferenceData);
        context.setOperatorAccount(operator);
        context.setClientId(clientId);
        context.setAppName(appName);
        context.setAppId(appId);
        context.setStartBizRuleConfig(request.getStartBizRuleConfig());


        if (processCommonSupport.shouldHaveFlowTemplateId(request.getScene()) &&
                StringUtils.isNotBlank(request.getFlowTemplateId())) {
            context.setFlowTemplateDetail(getFlowTemplateResult(request.getFlowTemplateId(), tenantId));
        }
        return context;
    }

    private Map<String, String> preferenceData(UserAccountDetail tenant, List<String> preferenceKeys) {
        Map<String, String> preferenceData = new HashMap<>();
        if (tenant.isOrganize()) {
            Set<String> finalPreferenceKeys = new HashSet<>(PREFERENCE_KEYS);
            if (CollectionUtils.isNotEmpty(preferenceKeys)) {
                finalPreferenceKeys.addAll(preferenceKeys);
            }

            QueryPreferenceRequest preferenceRequest = new QueryPreferenceRequest();
            preferenceRequest.setOrgId(tenant.getAccountOid());
            preferenceRequest.setOrgGid(tenant.getAccountGid());
            preferenceRequest.setPreferenceKeys(new ArrayList<>(finalPreferenceKeys));
            PreferenceResponse preferenceResponse =
                    preferencesService.queryByCondition(preferenceRequest);
            for (Preference preference : preferenceResponse.getPreferences()) {
                preferenceData.put(preference.getPreferenceKey(), preference.getPreferenceValue());
            }
        }
        return preferenceData;
    }

    @Cacheable(
            value = TLConstants.TL_CACHE,
            key = "'processStart:getFlowTemplateDetail:'+ #flowTemplateId + ':' + #tenant.accountOid",
            cacheManager = TLConstants.TL_CACHE_MANAGER)
    public GetFlowTemplateResult getFlowTemplateResult(String flowTemplateId, UserAccount tenant) {
        return flowTemplateService.getFlowTemplateResult(flowTemplateId, tenant);
    }

    @Cacheable(
            value = TLConstants.TL_CACHE,
            key = "'processStart:getFlowTemplateDetail:'+ #flowTemplateId + ':' + #tenantId",
            cacheManager = TLConstants.TL_CACHE_MANAGER)
    public GetFlowTemplateResult getFlowTemplateResult(String flowTemplateId, String tenantId) {
        UserAccountDetail tenant = userCenterService.getUserAccountDetailByOid(tenantId);
        return flowTemplateService.getFlowTemplateResult(flowTemplateId, tenant);
    }

    @Cacheable(
            value = TLConstants.TL_CACHE,
            key = "'processStart:getFlowTemplateStructs:'+ #flowTemplateId + ':' + #tenant.accountOid",
            cacheManager = TLConstants.TL_CACHE_MANAGER)
    public FlowTemplateStructResult getFlowTemplateStructs(String flowTemplateId, UserAccount tenant) {
        return flowTemplateService.getFlowTemplateStructs(flowTemplateId, tenant);
    }

    /**
     * 解析获取流程模板所有控件列表
     *
     * @param structResult
     * @return
     */
    public List<StructComponent> parseStructComponents(FlowTemplateStructResult structResult) {
        List<StructComponent> structComponents = Lists.newArrayList();
        // 协作方控件
        List<CooperationerStruct> cooperationerStructs = structResult.getCooperationerStructs();
        if (CollectionUtils.isNotEmpty(cooperationerStructs)) {
            cooperationerStructs.stream()
                    .filter(s -> MapUtils.isNotEmpty(s.getStructs()))
                    .map(s -> s.getStructs().values())
                    .forEach(s -> s.forEach(struct -> structComponents.addAll(struct)));
        }
        // 公共控件
        Map<String, List<StructComponent>> commonStructs = structResult.getCoomonStructs();
        if (MapUtils.isNotEmpty(commonStructs)) {
            commonStructs.values().forEach(struct -> structComponents.addAll(struct));
        }
        return structComponents;
    }

    /**
     * 获取会员功能MAP
     *
     * @param tenantId
     * @param functionCodes
     * @return
     */
    protected Map<String, VipFunction> queryVipFunctionMap(
            String tenantId, List<String> functionCodes) {

        Set<String> finalFunctionCodes = new HashSet<>(FUNCTION_CODES);
        if (CollectionUtils.isNotEmpty(functionCodes)) {
            finalFunctionCodes.addAll(functionCodes);
        }

        // 查询租户指定会员功能列表
        AccountVipFunctionsInput input = new AccountVipFunctionsInput();
        input.setAccountId(tenantId);
        input.setFunctionCodes(new ArrayList<>(finalFunctionCodes));
        input.setClientId(RequestContextExtUtils.getClientId());
        AccountVipFunctionsOutput output = saasCommonClient.queryAccountFunctions(input);
        return LambdaUtil.toMap(output.getFunctions(), i -> i.getCode(), i -> i);
    }
}

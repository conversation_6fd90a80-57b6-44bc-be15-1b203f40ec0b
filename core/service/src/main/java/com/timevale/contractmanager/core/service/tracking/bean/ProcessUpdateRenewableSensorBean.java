package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @Author:jianyang
 * @since 2022-02-24 16:17
 */
@Data
public class ProcessUpdateRenewableSensorBean extends BaseAttributeSensorBean {
	private String listName;
	private Long numberOfProcess;
	private String authenticationResult;
	private String authenticationFailureReason;
	private Long returnTime;
	private String processingResult;

	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData = super.sensorData();
		sensorData.put(LIST_NAME,sensorString(listName));
		sensorData.put(AUTHENTICATION_FAILURE_REASON, sensorString(authenticationFailureReason));
		sensorData.put(AUTHENTICATION_RESULT, sensorString(authenticationResult));
		sensorData.put(PROCESSING_RESULT, sensorString(processingResult));
		sensorData.put(RETURN_TIME, sensorString(returnTime));
		sensorData.put(NUMBER_OF_PROCESS,sensorString(numberOfProcess));
		return sensorData;
	}
}

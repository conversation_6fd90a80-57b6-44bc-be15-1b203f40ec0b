package com.timevale.contractmanager.core.service.processstart;

import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.enums.ProcessStartMode;

/**
 * <AUTHOR>
 * @since 2021-08-20
 */
public interface ProcessStartCoreService {

    /**
     * 发起模式， 直接发起或者模版发起
     *
     * @return
     */
    ProcessStartMode startMode();
    /**
     * 发起流程
     *
     * @param startRequest
     * @return
     */
    ProcessStartResult start(ProcessStartCoreRequest startRequest);
}

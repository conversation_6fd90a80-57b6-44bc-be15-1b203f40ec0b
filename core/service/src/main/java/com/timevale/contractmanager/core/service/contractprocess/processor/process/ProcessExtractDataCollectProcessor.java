package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.timevale.contractanalysis.facade.api.ao.FormDataIdsRequest;
import com.timevale.contractanalysis.facade.api.bo.FormOriginalDataResult;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessExtractMsgEntity;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessFormDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessFormParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by tianlei on 2022/5/16
 */
@Slf4j
@Component
public class ProcessExtractDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessReadClient contractProcessReadClient;
    @Autowired
    private ContractAnalysisClient contractAnalysisClient;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processManageTopicName, ProcessChangeTagEnum.PROCESS_EXTRACT.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessExtractMsgEntity entity = JsonUtils.json2pojo(data, ProcessExtractMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        ProcessExtractMsgEntity processExtractMsg = (ProcessExtractMsgEntity) collectContext.getData();

        //
        FormDataIdsRequest request = new FormDataIdsRequest();
        request.setFormId(processExtractMsg.getFormId());
        request.setRowDataIds(Arrays.asList(processId));

        List<FormOriginalDataResult> list =
                contractAnalysisClient.listOriginalDataByRowDataIds(request);

        //
        if (CollectionUtils.isEmpty(list)) {
            log.info(LOG_PREFIX + "form data is empty processId: {}", processId);
            return;
        }

        FormOriginalDataResult result = list.get(0);
        ContractProcessFormParam param = ProcessDataCollectConverter.form2FormParam(result,
                processExtractMsg.getFormId(),
                processExtractMsg.getTenantGid());

        List<ContractProcessFormDTO> forms =
                Optional.ofNullable(contractProcessReadClient.getForms(processId)).orElse(new ArrayList<>());

        List<ContractProcessFormParam> formsParamList = ProcessDataCollectConverter.formDTO2FormParam(forms);
        Integer index = null;
        for (int i = 0; i < formsParamList.size(); i++) {
            ContractProcessFormDTO processFormDTO = forms.get(i);
            if (Objects.equals(param.getFormId(), processFormDTO.getFormId()) &&
                    Objects.equals(param.getTenantGid(), processFormDTO.getTenantGid())) {
                index = i;
                break;
            }
        }
        if (null != index) {
            formsParamList.set(index, param);
        } else {
            formsParamList.add(param);
        }
        ContractProcessUpdateParam updateParam = new ContractProcessUpdateParam();
        updateParam.setProcessId(processId);
        updateParam.setForms(formsParamList);
        updateParam.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_EXTRACT_DATA);
        contractProcessWriteClient.updateByProcessId(updateParam);

    }
}

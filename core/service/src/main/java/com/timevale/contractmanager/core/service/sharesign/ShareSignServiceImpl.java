package com.timevale.contractmanager.core.service.sharesign;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.account.flow.service.enums.ModelTypeEnums;
import com.timevale.account.flow.service.model.output.FlowModelOutput;
import com.timevale.account.flow.service.model.output.FlowNodeOutput;
import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignProcessDO;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignTaskDO;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignUrlDO;
import com.timevale.contractmanager.common.dal.query.shareSign.ShareSignTaskListParam;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.bean.ContractProcessGroupBean;
import com.timevale.contractmanager.common.service.bean.ProcessParticipant;
import com.timevale.contractmanager.common.service.bean.ProcessSimpleListBean;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.enums.*;
import com.timevale.contractmanager.common.service.enums.sharesign.*;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.*;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.bo.FileAuthBO;
import com.timevale.contractmanager.core.model.bo.FileDetailBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.contractmanager.core.model.dto.process.ProcessParticipantStatusOutputDTO;
import com.timevale.contractmanager.core.model.dto.request.GetProcessUrlRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.request.sharesign.*;
import com.timevale.contractmanager.core.model.dto.response.*;
import com.timevale.contractmanager.core.model.dto.response.process.ProcessParticipantStatusResponse;
import com.timevale.contractmanager.core.model.dto.response.sharesign.*;
import com.timevale.contractmanager.core.model.dto.user.OrgDeptDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessStartMode;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.configs.CommonBizConfig;
import com.timevale.contractmanager.core.service.contractapproval.ContractApprovalService;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalNode;
import com.timevale.contractmanager.core.service.contractapproval.param.UseApprovalFlowTemplateDTO;
import com.timevale.contractmanager.core.service.contractapproval.param.VirtualStartApprovalFlowDTO;
import com.timevale.contractmanager.core.service.contractapproval.result.VirtualStartApprovalFlowResult;
import com.timevale.contractmanager.core.service.grouping.CacheService;
import com.timevale.contractmanager.core.service.mq.producer.ProcessNotifyHandler;
import com.timevale.contractmanager.core.service.other.UserCenterCachedService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.*;
import com.timevale.contractmanager.core.service.process.door.ProcessDoor;
import com.timevale.contractmanager.core.service.process.impl.ProcessStartHelper;
import com.timevale.contractmanager.core.service.sharesign.bean.ShareSignParticipantAccount;
import com.timevale.contractmanager.core.service.sharesign.bean.ShareSignTaskBizExtra;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.contractmanager.core.service.util.QueryEsResultConverter;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.doccooperation.service.enums.CooperationerRoleSetEnum;
import com.timevale.doccooperation.service.enums.FlowTemplateSchemaTypeEnum;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.doccooperation.service.input.CooperationerListInput;
import com.timevale.doccooperation.service.model.Cooperationer;
import com.timevale.doccooperation.service.model.CooperationerInstance;
import com.timevale.doccooperation.service.result.BaseFlowTemplateExt;
import com.timevale.doccooperation.service.result.CooperationerListResult;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.flow.facade.service.model.input.ContractApprovalTemplateUserInput;
import com.timevale.footstone.rpc.enums.SignPlatformEnum;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.*;
import com.timevale.mandarin.common.lang.ValidateUtils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.saas.common.manage.common.service.model.output.VipFunctionQueryOutput;
import com.timevale.signflow.search.docSearchService.result.DocQueryResult;
import com.timevale.signflow.search.esdata.FlowGroupInfo;
import com.timevale.signflow.search.service.query.GroupQueryModel;
import com.timevale.signflow.search.service.result.GroupQueryResult;
import com.timevale.signflow.search.service.result.v2.GroupProcessCountResultV2;
import com.timevale.tool.ValidateUtil;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.enums.sharesign.ShareTaskStatusEnum.CAN_NOT_USE;
import static com.timevale.contractmanager.common.service.enums.sharesign.ShareTaskStatusEnum.CAN_USE;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;
import static com.timevale.contractmanager.core.service.processstart.handler.ProcessStartAdapter.buildProcessStartBizRequest;
import static com.timevale.contractmanager.core.service.util.RequestContextExtUtils.getClientId;
import static com.timevale.doccooperation.service.enums.CooperationerRoleEnum.FORMULATER;
import static com.timevale.doccooperation.service.enums.CooperationerRoleEnum.SIGNER;
import static com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant.INITIATE_CONTRACT_EXPIRE;

/**
 * <AUTHOR>
 * @since 2021-02-03
 */
@Slf4j
@Service
public class ShareSignServiceImpl implements ShareSignService {

    @Autowired private ShareSignBaseService baseService;
    @Autowired private UserCenterService userCenterService;
    @Autowired private FileSystemClient fileSystemClient;
    @Autowired private SaasCommonClient saasCommonClient;
    @Autowired private ProcessService processService;
    @Autowired private FlowTemplateService flowTemplateService;
    @Autowired private PreferencesService preferencesService;
    @Autowired private EsClient esClient;
    @Autowired private CacheService cacheService;
    @Autowired private ProcessNotifyHandler processNotifyHandler;
    @Autowired private ShareSignBaseService shareSignBaseService;
    @Resource private MapperFactory mapperFactory;
    @Autowired private ProcessChangeNotifyService processChangeNotifyService;
    @Autowired private ProcessStartHelper processStartHelper;
    @Autowired private DocCooperationClient docCooperationClient;
    @Autowired private ProcessDoor processDoor;
    @Autowired private ContractApprovalService contractApprovalService;
    @Autowired private ApprovalClient approvalClient;
    @Autowired private UserCenterCachedService userCenterCachedService;

    @Autowired private LockFactory lockFactory;

    private ShareSignTaskDO checkAndGetShareSignTask(String shareSignTaskId) {
        ShareSignTaskDO shareTask = baseService.queryTaskById(shareSignTaskId);
        if (null == shareTask) {
            throw new BizContractManagerException(SHARE_SIGN_TASK_NOT_EXIST);
        }
        return shareTask;
    }

    private boolean checkInitiator(ShareSignTaskDO shareTask, UserAccount userAccount) {
        return StringUtils.equals(shareTask.getAccountOid(), userAccount.getAccountOid())
            || StringUtils.equals(shareTask.getAccountGid(), userAccount.getAccountGid());
    }

    private boolean checkSubject(ShareSignTaskDO shareTask, UserAccount subjectAccount) {
        return StringUtils.equals(shareTask.getSubjectOid(), subjectAccount.getAccountOid())
                || StringUtils.equals(shareTask.getSubjectGid(), subjectAccount.getAccountGid());
    }

    @Override
    public ShareSignTaskInfoResponse getTaskInfo(
            String shareSignTaskId, String token, Boolean withProgress) {
        ShareSignTaskInfoResponse response = new ShareSignTaskInfoResponse();
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(shareSignTaskId);
        // 查询需要隐藏经办人的主体gid列表
        List<String> hideSignatorySubjectGids = queryHideSignatorySubjectGids(shareTask);
        // 填充扫码任务信息
        parseTaskInfoToResponse(shareTask, response, hideSignatorySubjectGids);
        response.setH5DetailUrl(
                String.format(
                        CommonBizConfig.SHARE_SIGN_TASK_H5_DETAIL_URL, shareSignTaskId, token));

        if (withProgress) {
            ShareSignTaskInfoResponse.TaskNode taskNode = new ShareSignTaskInfoResponse.TaskNode();
            populateStartAccount(taskNode, shareTask);
            populateProcessAccounts(taskNode, shareTask.getShareTemplateId());
            populateApprovalAccount(taskNode,shareTask);
            response.setTaskNode(taskNode);
        }
        return response;
    }

    @Override
    public ShareSignScanInfoResponse getTaskInfoByUrlId(String shareSignTaskUrlId) {
        ShareSignUrlDO shareSignUrlDO = baseService.queryTaskUrlById(shareSignTaskUrlId);
        if (null == shareSignUrlDO) {
            throw new BizContractManagerException(SHARE_SIGN_TASK_NOT_EXIST);
        }
        String shareSignTaskId = shareSignUrlDO.getShareSignTaskId();
        ShareSignScanInfoResponse response = new ShareSignScanInfoResponse();
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(shareSignTaskId);
        // 查询需要隐藏经办人的主体gid列表
        List<String> hideSignatorySubjectGids = queryHideSignatorySubjectGids(shareTask);
        // 获取流程模板id
        String flowTemplateId = shareTask.getShareTemplateId();
        // 查询签署方信息
        CooperationerListInput input = new CooperationerListInput();
        input.setFlowTemplateId(flowTemplateId);
        CooperationerListResult result = docCooperationClient.getCooperationers(input);
        // 若参与方信息不存在，抛出异常
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getCooperationers())) {
            parseTaskInfoToResponse(shareTask, response, hideSignatorySubjectGids);
            checkAsyncScanStart(shareTask, response);
            response.setParticipantType(shareSignUrlDO.getParticipantType());
            response.setParticipants(null);
            return response;
        }

        List<Cooperationer> cooperationers = result.getCooperationers();
        List<ProcessParticipantStatusOutputDTO.ParticipantStatusDTO> writeParticipants =
                Lists.newArrayList();
        List<ProcessParticipantStatusOutputDTO.ParticipantStatusDTO> signParticipants =
                Lists.newArrayList();
        for (Cooperationer c : cooperationers) {
            // 角色为空则跳过当前参与方
            if (StringUtils.isBlank(c.getRole())) {
                continue;
            }
            // 角色列表
            List<Integer> roles =
                    Arrays.stream(c.getRole().split(","))
                            .map(Integer::valueOf)
                            .collect(Collectors.toList());
            if (roles.contains(FORMULATER.getRole())) {
                ProcessParticipantStatusOutputDTO.ParticipantStatusDTO part =
                        new ProcessParticipantStatusOutputDTO.ParticipantStatusDTO();
                Cooperationer.Ext ext = c.getExt();
                // 放置填写顺序
                if (Objects.nonNull(ext)) {
                    part.setOrder(ext.getFillOrder());
                }
                part.setInstances(getParticipantInstanceDTOS(c, hideSignatorySubjectGids));
                writeParticipants.add(part);
            }
            if (roles.contains(SIGNER.getRole())) {
                ProcessParticipantStatusOutputDTO.ParticipantStatusDTO part =
                        new ProcessParticipantStatusOutputDTO.ParticipantStatusDTO();
                Cooperationer.Ext ext = c.getExt();
                // 放置签署顺序
                if (Objects.nonNull(ext)) {
                    part.setOrder(ext.getSignOrder());
                }
                // 放置是否或签
                part.setMultiOr(ParticipantModeEnum.isOrSign(c.getParticipantMode()));
                part.setInstances(getParticipantInstanceDTOS(c, hideSignatorySubjectGids));
                signParticipants.add(part);
            }
        }
        // 排序填写方
        writeParticipants = ProcessParticipantStatusOutputDTO.participantsSort(writeParticipants);
        // 排序签署方
        signParticipants = ProcessParticipantStatusOutputDTO.participantsSort(signParticipants);

        ProcessParticipantStatusOutputDTO outputDTO = new ProcessParticipantStatusOutputDTO();
        outputDTO.setWriteParticipants(writeParticipants);
        outputDTO.setSignParticipants(signParticipants);
        outputDTO.setOrderSign(ProcessParticipantStatusOutputDTO.isSortSign(signParticipants));
        ProcessParticipantStatusResponse participants =
                mapperFactory
                        .getMapperFacade()
                        .map(outputDTO, ProcessParticipantStatusResponse.class);
        parseTaskInfoToResponse(shareTask, response, hideSignatorySubjectGids);
        //填充审批节点
        ShareSignTaskInfoResponse.TaskNode taskNode = new ShareSignTaskInfoResponse.TaskNode();
        populateApprovalAccount(taskNode,shareTask);
        response.setTaskNode(taskNode);
        checkAsyncScanStart(shareTask, response);
        response.setParticipantType(shareSignUrlDO.getParticipantType());
        response.setParticipants(participants);
        return response;
    }

    /**
     * 查询需要隐藏经办人的主体gid列表
     * @param shareTask
     * @return
     */
    private List<String> queryHideSignatorySubjectGids(ShareSignTaskDO shareTask) {
        String orgId = shareTask.getSubjectOid();
        String orgGid = shareTask.getSubjectGid();
        String appId = RequestContextExtUtils.getAppId();
        // 判断是否隐藏经办人
        boolean hideInitiatorSignatory = preferencesService.isHideInitiatorSignatory(orgId, orgGid, appId);
        List<String> hideSignatorySubjectGids = Lists.newArrayList();
        if (hideInitiatorSignatory && StringUtils.isNotBlank(orgGid)) {
            hideSignatorySubjectGids.add(orgGid);
        }
        return hideSignatorySubjectGids;
    }

    /**
     * 获取参与方实体列表
     *
     * @param cooperationer 参与方信息
     * @return 参与方实体列表
     */
    private List<ProcessParticipantStatusOutputDTO.ParticipantInstanceDTO>
            getParticipantInstanceDTOS(Cooperationer cooperationer, List<String> hideSignatorySubjectGids) {
        List<ProcessParticipantStatusOutputDTO.ParticipantInstanceDTO> instanceDTOList = new ArrayList<>();
        List<CooperationerInstance> cooperationerInstances = cooperationer.getCooperationerInstances();
        // 如果没有参与人实例，则为扫码签方，固定字段
        if (CollectionUtils.isEmpty(cooperationerInstances)) {
            ProcessParticipantStatusOutputDTO.ParticipantInstanceDTO instanceDTO =
                    getParticipantInstanceDTO("微信/扫码", "个人");
            instanceDTOList.add(instanceDTO);
            return instanceDTOList;
        }
        // 非扫码签方：放置参与人
        instanceDTOList =
                cooperationerInstances.stream()
                        .map(j -> {
                            if (SubjectTypeEnum.PERSON.getType().equals(j.getSubjectType())
                                || StringUtils.isBlank(j.getSubjectId())) {
                                return getParticipantInstanceDTO(j.getAccountName(), j.getSubjectName());
                            }
                            String subjectGid = userCenterCachedService.getFatUserAccountGid(j.getSubjectId());
                            if (StringUtils.isBlank(subjectGid) || !hideSignatorySubjectGids.contains(subjectGid)) {
                                return getParticipantInstanceDTO(j.getAccountName(), j.getSubjectName());
                            }
                            return getParticipantInstanceDTO("", j.getSubjectName());

                        })
                        .collect(Collectors.toList());
        return instanceDTOList;
    }

    /**
     * 获取参与方实体DTO对象
     *
     * @param accountName 用户名称
     * @param subjectName 企业名称
     * @return 参与方实体DTO
     */
    private static ProcessParticipantStatusOutputDTO.ParticipantInstanceDTO
            getParticipantInstanceDTO(String accountName, String subjectName) {
        ProcessParticipantStatusOutputDTO.ParticipantInstanceDTO instanceDTO =
                new ProcessParticipantStatusOutputDTO.ParticipantInstanceDTO();
        // 创建个人信息对象
        ProcessParticipantStatusOutputDTO.AccountDTO person =
                new ProcessParticipantStatusOutputDTO.AccountDTO();
        person.setName(accountName);
        // 创建企业信息对象
        ProcessParticipantStatusOutputDTO.AccountDTO subject =
                new ProcessParticipantStatusOutputDTO.AccountDTO();
        subject.setName(subjectName);
        // 组装参数
        instanceDTO.setPerson(person);
        instanceDTO.setSubject(subject);
        return instanceDTO;
    }

    /**
     * check是否需要走异步发起
     *
     * @param shareTask
     * @param response
     * @return
     */
    private void checkAsyncScanStart(ShareSignTaskDO shareTask, ShareSignScanInfoResponse response) {
        // 获取流程模版id
        String flowTemplateId = StringUtils.defaultIfBlank(shareTask.getOriginTemplateId(), shareTask.getShareTemplateId());

        UserAccount tenantInfo = new UserAccount();
        tenantInfo.setAccountGid(shareTask.getSubjectGid());
        tenantInfo.setAccountOid(shareTask.getSubjectOid());

        BaseFlowTemplateExt flowTemplate;

        try {
            //优先使用原模板查询
            flowTemplate = flowTemplateService.getFlowTemplateBaseInfo(flowTemplateId);
        } catch (Exception e) {
            return;
        }

        response.setAsyncStart(Objects.equals(FlowTemplateSchemaTypeEnum.DYNAMIC.getSchemaType(), flowTemplate.getSchemaType())
                || flowTemplate.isEpaasTag());
        response.setEpaasTemplateTag(flowTemplate.isEpaasTag());
    }

    private void parseTaskInfoToResponse(
            ShareSignTaskDO shareTask,
            ShareSignTaskInfoResponse response,
            List<String> hideSignatorySubjectGids) {
        response.setTaskId(shareTask.getTaskId());
        response.setTaskName(shareTask.getTaskName());
        response.setTaskType(shareTask.getShareType());
        response.setTaskDone(shareTask.getShareDone());
        response.setTaskNum(shareTask.getShareNum());
        response.setTaskTotal(shareTask.getShareTotal());
        response.setTaskStatus(shareTask.getStatus());
        boolean hideAccountName = hideSignatorySubjectGids.contains(shareTask.getSubjectGid());
        response.setAccountName(hideAccountName ? "-" : shareTask.getAccountName());
        response.setSubjectName(shareTask.getSubjectName());
        response.setStartTime(shareTask.getCreateTime());
        response.setEndTime(shareTask.getEndTime());
        response.setShareBizId(shareTask.getShareBizId());
        response.setSubjectId(shareTask.getSubjectOid());
        response.setPrivateShare(shareTask.getPrivateShare());
        response.setRepeatSign(isRepeatSign(shareTask.getBizExtra()));
    }

    @Override
    public ShareSignTaskUrlResponse getTaskUrl(
            String shareSignTaskId, String participantId, String accountId) {
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(shareSignTaskId);
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(accountId);
        if (!checkInitiator(shareTask, userAccount)) {
            throw new BizContractManagerException(SHARE_SIGN_TASK_NOT_INITIATOR, "无权获取扫码任务地址");
        }
        ShareSignTaskUrlResponse response = new ShareSignTaskUrlResponse();
        response.setShareSignTaskId(shareSignTaskId);

        List<ShareSignUrlDO> shareSignUrls = baseService.queryTaskUrls(shareSignTaskId, participantId);
        shareSignUrls.forEach(
                i -> {
                    // 刷新二维码， 兼容历史数据
                    baseService.refreshShareSignQrCode(i);
                    // 组装返回参数
                    ShareSignTaskUrl taskUrl = new ShareSignTaskUrl();
                    taskUrl.setShareScanId(i.getUuid());
                    taskUrl.setParticipantId(i.getParticipantId());
                    taskUrl.setParticipantType(i.getParticipantType());
                    taskUrl.setParticipantLabel(i.getParticipantLabel());
                    taskUrl.setShareUrl(i.getShareUrl());
                    if (StringUtils.isNotBlank(i.getShareQrcodeFilekey())) {
                        taskUrl.setQrcodeUrl(
                                fileSystemClient.getDownloadUrl(i.getShareQrcodeFilekey()));
                    }
                    response.getShareUrls().add(taskUrl);
                });

        return response;
    }

    @Override
    public ShareSignTaskListResponse getTaskList(ShareSignTaskListRequest request) {
        String tenantId = RequestContextExtUtils.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            tenantId = request.getAccountId();
        }

        ShareSignTaskListResponse response = new ShareSignTaskListResponse();
        GroupQueryModel groupQueryModel = new GroupQueryModel();
        groupQueryModel.setCreatorAccountId(request.getAccountId());
        groupQueryModel.setOwnerAccountId(tenantId);
        groupQueryModel.setStatus(IdsUtil.getIntegerIdList(request.getProcessStatusList()));
        groupQueryModel.setGroupName(request.getTaskName());
        groupQueryModel.setExecutorKeyword(request.getSignerKeyWord());
        groupQueryModel.setIsShowFlow(false);
        groupQueryModel.setIsShowTemp(true);
        groupQueryModel.setPageSize(request.getPageSize());
        groupQueryModel.setPageNum(request.getPageNum());
        groupQueryModel.setGroupTypes(
                Lists.newArrayList(GroupTypeEnum.SHARE_START_SINGLE_SCAN.getType()));

        GroupQueryResult groupQueryResult = esClient.groupQueryV2(groupQueryModel);
        response.setTotal(groupQueryResult.getTotal());
        List<FlowGroupInfo> groups = groupQueryResult.getGroups();
        if (CollectionUtils.isNotEmpty(groups)) {
            List<String> groupIds =
                    groups.stream()
                            .map(i -> i.getGroupId())
                            .collect(Collectors.toList());
            List<ShareSignTaskDO> shareSignTasks = baseService.queryTasksByBizIds(groupIds, null);
            shareSignTasks.forEach(i -> response.getTasks().add(buildShareSignTaskListBean(i)));
        }
        return response;
    }

    private ShareSignTaskList buildShareSignTaskListBean(ShareSignTaskDO i) {
        ShareSignTaskList shareSignTask = new ShareSignTaskList();
        shareSignTask.setTaskId(i.getTaskId());
        shareSignTask.setTaskName(i.getTaskName());
        shareSignTask.setTaskType(i.getShareType());
        shareSignTask.setTaskBizId(i.getShareBizId());
        shareSignTask.setTaskDone(i.getShareDone());
        shareSignTask.setTaskNum(i.getShareNum());
        shareSignTask.setTaskTotal(i.getShareTotal());
        shareSignTask.setTaskStatus(i.getStatus());
        shareSignTask.setStartTime(i.getCreateTime());
        shareSignTask.setEndTime(i.getEndTime());
        shareSignTask.setPrivateShare(i.getPrivateShare());
        return shareSignTask;
    }

    @Override
    public ShareSignProcessListResponse getProcessList(QueryTaskProcessListRequest request) {
        ShareSignProcessListResponse response = new ShareSignProcessListResponse();

        // 校验用户
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(request.getAccountId());
        if (StringUtils.isAnyBlank(userAccount.getAccountGid())) {
            return response;
        }

        // 查询扫码签任务
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(request.getShareSignTaskId());

        GroupQueryModel groupQueryModel = new GroupQueryModel();
        groupQueryModel.setGroupId(shareTask.getShareBizId());
        groupQueryModel.setPageNum(request.getPageNum());
        groupQueryModel.setPageSize(request.getPageSize());
        groupQueryModel.setGroupName(request.getProcessGroupName());
        groupQueryModel.setExecutorKeyword(request.getSignerKeyWord());
        groupQueryModel.setStatus(IdsUtil.getIntegerIdList(request.getProcessStatusList()));

        // 查询es
        DocQueryResult docQueryResult = esClient.commonSearchFlows(groupQueryModel);
        GroupProcessCountResultV2 countResult = esClient.countGroupProcesses(shareTask.getShareBizId());
        response = QueryEsResultConverter.convertShareSignProcessList(docQueryResult, countResult);
        populateSharableAccount(response.getProcesses());
        if (null != shareTask.getShareTotal()) {
            response.setUpperLimit(shareTask.getShareTotal().longValue());
        }
        response.setPrivateShare(shareTask.getPrivateShare());

        return response;
    }

    @Override
    public void updateTaskStatus(ShareSignTaskStatusRequest request) {
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(request.getShareSignTaskId());
        String accountId = request.getAccountId();
        if (StringUtils.isBlank(accountId)) {
            accountId = RequestContextExtUtils.getOperatorId();
        }
        if (StringUtils.isNotBlank(accountId)) {
            UserAccount user = userCenterService.getUserAccountBaseByOid(accountId);
            if (!checkInitiator(shareTask, user)) {
                throw new BizContractManagerException(SHARE_SIGN_TASK_NOT_INITIATOR, "无权开启/关闭扫码任务");
            }
        }
        baseService.updateTaskStatus(request.getShareSignTaskId(), request.getTaskStatus());

        // 清除扫码任务对应的流程组缓存
        TedisUtil.delete(CacheUtil.getGroupInfoKey(shareTask.getShareBizId()));

        ContractProcessGroupBean processGroupBean = new ContractProcessGroupBean();
        processGroupBean.setProcessGroupId(shareTask.getShareBizId());
        processGroupBean.setProcessGroupStatus(
                BooleanUtils.toBoolean(shareTask.getStatus())
                        ? GroupStatusEnum.ENABLE.getStatus()
                        : GroupStatusEnum.CLOSED.getStatus());

        processNotifyHandler.processInfoGroupUpdate(processGroupBean);
    }

    @Override
    public void deleteTask(DeleteShareSignTaskRequest request) {
        String shareSignTaskId = request.getShareSignTaskId();
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(shareSignTaskId);
        UserAccount user = userCenterService.getUserAccountBaseByOid(request.getOperatorId());
        if (!checkInitiator(shareTask, user)) {
            throw new BizContractManagerException(SHARE_SIGN_TASK_NOT_INITIATOR, "无权删除扫码任务");
        }
        baseService.updateTaskStatus(shareSignTaskId, ShareTaskStatusEnum.DELETED.getStatus());
        // 清除扫码任务对应的流程组缓存
        String shareBizId = shareTask.getShareBizId();
        TedisUtil.delete(CacheUtil.getGroupInfoKey(shareBizId));
        processChangeNotifyService.truncateTempGroupInfo(shareBizId);
    }

    @Override
    public void updateTaskInfo(ShareSignTaskInfoRequest request, String accountId, String subjectId) {
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(request.getShareSignTaskId());
        UserAccount account = userCenterService.getUserAccountBaseByOid(accountId);
        UserAccount subject = userCenterService.getUserAccountBaseByOid(subjectId);

        // 检查是否是发起人
        if (!checkInitiator(shareTask, account) || !checkSubject(shareTask, subject)) {
            throw new BizContractManagerException(SHARE_SIGN_TASK_NOT_INITIATOR, "无权更新扫码任务");
        }

        // 更新任务信息
        ShareSignTaskDO updateData = new ShareSignTaskDO();
        updateData.setTaskId(request.getShareSignTaskId());
        // 只允许从私密改为非私密
        if (shareTask.getPrivateShare() && !request.getPrivateShare()) {
            updateData.setPrivateShare(request.getPrivateShare());
        }
        shareSignBaseService.updateByTaskId(updateData);
    }

    @Override
    public ShareSignTaskUrlResponse startTask(ShareSignTaskStartRequest request) {
        return processDoor.shareSignStart(request);
    }

    @Override
    public ProcessStartResponse startProcess(ShareSignProcessStartRequest request) {

        if (StringUtils.isBlank(request.getAccountId())) {
            request.setAccountId(RequestContextExtUtils.getOperatorId());
        }

        Integer participantType = getParticipantType(request);

        String accountId = request.getAccountId();
        UserAccountDetail userAccount = userCenterService.getLoginAccountDetailByOid(accountId);
        if (StringUtils.isBlank(userAccount.account())
                && StringUtils.isNotBlank(request.getAccount())) {
            if (ValidateUtils.emailValid(request.getAccount())) {
                userAccount.setAccountEmail(request.getAccount());
            } else if (ValidateUtils.mobileValid(request.getAccount())){
                userAccount.setAccountMobile(request.getAccount());
            } else {
                throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "签署人手机号或邮箱格式不正确");
            }
        }
        String shareSignTaskId = request.getShareSignTaskId();
        // 校验扫码任务状态
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(shareSignTaskId);
        // 校验发起人账号是否已冻结， 如果是， 报错
        if (userCenterService.checkAccountFreezed(shareTask.getAccountOid())) {
            throw new BizContractManagerException(SHARE_SIGN_PROCESS_INITIATOR_NOT_VALID);
        }

        ShareSignParticipantAccount signAccount = ShareSignParticipantAccount.valueOf(userAccount);
        signAccount.setSubjectName(request.getOrgName());
        signAccount.setParticipantType(participantType);

        // 触发发起流程
        return singleShareSignProcessStart(shareTask, signAccount, request);
    }

    /**
     * 获取参与方类型
     *
     * @param request 发起请求参数
     * @return
     */
    private Integer getParticipantType(ShareSignProcessStartRequest request) {
        Integer participantType;
        // 如果扫码签扫码id不为空
        if (StringUtils.isNotBlank(request.getShareScanId())) {
            ShareSignUrlDO shareSignUrl = baseService.queryTaskUrlById(request.getShareScanId());
            if (null == shareSignUrl) {
                throw new BizContractManagerException(SHARE_SIGN_TASK_NOT_EXIST);
            }
            // 从扫码签url表获取参与方类型
            participantType = shareSignUrl.getParticipantType();
            request.setShareSignTaskId(shareSignUrl.getShareSignTaskId());
            request.setParticipantId(shareSignUrl.getParticipantId());
        } else {
            if (StringUtils.isBlank(request.getShareSignTaskId())) {
                throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "shareSignTaskId不能为空");
            }
            if (StringUtils.isBlank(request.getParticipantId())) {
                throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "participantId不能为空");
            }
            // 从扫码签url表获取参与方类型，通过taskId和参与方id确定一条
            List<ShareSignUrlDO> shareSignUrls = baseService.queryTaskUrls(request.getShareSignTaskId(), request.getParticipantId());
            if (CollectionUtils.isEmpty(shareSignUrls)) {
                throw new BizContractManagerException(SHARE_SIGN_TASK_NOT_EXIST);
            }
            participantType = shareSignUrls.get(0).getParticipantType();
        }
        // 如果为企业类型则企业名称不能为空
        if (ParticipantSubjectType.ORG.getType() == participantType
                && StringUtils.isBlank(request.getOrgName())) {
            throw new BizContractManagerException(SHARE_SIGN_PARTICIPANT_MISSING_ORG_INFO);
        }
        return participantType;
    }

    @Override
    public ShareSignTaskManageUrlResponse getTaskManageUrl(ShareSignTaskManageUrlRequest request) {
        String tenantId = RequestContextExtUtils.getTenantId();
        String manageUrl = String.format(CommonBizConfig.SHARE_SIGN_TASK_MANAGE_URL, tenantId, request.getToken());
        return new ShareSignTaskManageUrlResponse(manageUrl);
    }

    @Override
    public void completeShareSignProcessInitiate(String processId, boolean initiated) {
        log.info("completeShareSignProcessInitiate start, processId:{}", processId);
        List<ShareSignProcessDO> processDOS = shareSignBaseService.queryByProcessId(processId);
        // 如果当前流程不是扫码流程， 跳过
        if (CollectionUtils.isEmpty(processDOS)) {
            log.info("completeShareSignProcessInitiate done,the process is not share scan sign, processId:{}", processId);
            return;
        }
        // 获取扫码流程
        ShareSignProcessDO shareSignProcess = processDOS.get(0);
        // 如果扫码流程发起失败
        if (!initiated) {
            // 删除流程记录
            shareSignBaseService.deleteByProcessId(processId);
            // 释放名额
            shareSignBaseService.degradeTaskNumOccupied(shareSignProcess.getShareSignTaskId());
            return;
        }
        // 更新发起状态为已完成
        shareSignBaseService.updateStatusByProcessId(
                processId, ShareSignProcessStatusEnum.INITIATED.getType());
        // 更新任务数量
        upgradeTaskNum(shareSignProcess.getShareSignTaskId());
    }

    /**
     * 发起单方扫码签
     *
     * @param shareTask
     * @param userAccount
     * @param request
     * @return
     */
    private ProcessStartResponse singleShareSignProcessStart(
            ShareSignTaskDO shareTask,
            ShareSignParticipantAccount userAccount,
            ShareSignProcessStartRequest request) {
        String taskId = shareTask.getTaskId();
        String accountId = userAccount.getAccountOid();
        String accountGid = userAccount.getAccountGid();
        if (StringUtils.isBlank(accountGid)) {
            throw new BizContractManagerException(SHARE_SIGN_PARTICIPANT_MISSING_GID);
        }
        String participantId = request.getParticipantId();

        //是否允许重复加入
        if (!isRepeatSign(shareTask.getBizExtra())) {
            //查询用户已扫码加入的流程
            ProcessStartResponse startResponse = queryJoinedTask(taskId, userAccount, request.getToken());
            if (startResponse != null) {
                return startResponse;
            }

            // 获取用户扫码参与的流程数
            int count = baseService.countByAccountGid(taskId, accountGid);
            // 获取扫码任务同一用户允许的最大参与次数
            Integer taskPsnProcessMax = CommonBizConfig.SHARE_SIGN_TASK_PSN_PROCESS_MAX;
            // 如果流程数大于等于允许的最大扫码参数， 报错
            if (count >= taskPsnProcessMax) {
                throw new BizContractManagerException(
                        SHARE_SIGN_PARTICIPANT_PER_PSN_MAX, taskPsnProcessMax);
            }
        }

        // 校验任务状态
        checkShareTaskStatus(shareTask);
        // 如果任务名额已满， 报错
        if (null != shareTask.getShareTotal() && shareTask.getShareNum() >= shareTask.getShareTotal()) {
            throw new BizContractManagerException(SHARE_SIGN_TASK_ACCOUNT_FULL);
        }

        Map<String, ShareSignParticipantAccount> participantAccountMap = Maps.newHashMap();
        participantAccountMap.put(request.getParticipantId(), userAccount);
        // 获取流程数据,  并设置发起场景
        ProcessStartDetailResponse response =
                getShareSignProcessInfo(shareTask, participantAccountMap);

        // 防重缓存
        String startProcessKey = taskId + "~" + accountId;
        if (!cacheService.setNx(startProcessKey, 15, TimeUnit.SECONDS)) {
            throw new BizContractManagerException(SERVICE_BUSY);
        }

        try {
            // 占用扫码任务名额
            if (!baseService.upgradeTaskNumOccupied(taskId)) {
                throw new BizContractManagerException(SHARE_SIGN_TASK_ACCOUNT_FULL);
            }
            // 提前生成processId
            String processId = UUIDUtil.genUUID();
            ProcessStartResponse startResponse;
            try {
                // 判断是否使用新版发起
                ProcessStartBizRequest bizRequest = buildProcessStartBizRequest(response);
                // 操作人
                bizRequest.setOperatorId(accountId);
                // 扫码发起均为基于流程模板发起，因此统一模板发起模式为基于流程模板发起，ProcessStartMode.TEMPLATE_START
                bizRequest.setStartMode(ProcessStartMode.TEMPLATE_START.getMode());
                // 扫码发起场景不删除临时模板
                bizRequest.setDeleteTempFlowTemplate(false);
                bizRequest.setTenantId(shareTask.getSubjectOid());
                // 判断是否走异步发起
                bizRequest.setAsyncStart(request.isAsyncStart());
                bizRequest.setSignPlatform(SignPlatformEnum.STANDARD_H5.getPlatform());
                bizRequest.setAssignProcessId(processId);
                //扫码签将token带到doc-cooperation，解决首次异步扫码时填写页面无法带token问题
                bizRequest.setToken(request.getToken());
                // 保存任务流程记录， 之所以在发起前保存记录，主要是为了防止异步发起场景更新状态时找不到记录导致的数据异常
                ShareSignProcessStatusEnum status = ShareSignProcessStatusEnum.INITIATING;
                baseService.addTaskProcess(taskId, participantId, processId, userAccount, status);
                // 触发流程发起
                ProcessStartResult startResult = processDoor.start(bizRequest);
                startResponse = processStartHelper.buildProcessStartResponse(startResult);
                //如果异步发起，就此结束
                if(request.isAsyncStart()){
                    return new ProcessStartResponse(startResponse.getProcessId(), startResponse.getLongResultUrl(), startResponse.getResultUrl());
                }
                // 结束扫码任务发起
                completeShareSignProcessInitiate(processId, true);
            } catch (Exception e) {
                // 结束扫码任务发起
                completeShareSignProcessInitiate(processId, false);
                if (e instanceof BizContractManagerException) {
                    String code = ((BizContractManagerException) e).getCode();
                    // 流程模板不存在异常
                    if (FLOW_TEMPLATE_NOT_EXIST.getCode().equals(code)) {
                        throw new BizContractManagerException(SHARE_SIGN_TASK_INVALID);
                    }
                    // 余额不足相关异常
                    if (USER_BALANCE_NOT_ENOUGH.getCode().equals(code)
                            || ADMIN_BALANCE_NOT_ENOUGH_NUM.getCode().equals(code)
                            || USER_BALANCE_NOT_ENOUGH_NUM.getCode().equals(code)) {
                        throw new BizContractManagerException(SHARE_SIGN_PARTICIPANT_BILL_BALANCE);
                    }
                }
                throw e;
            }

            GetProcessUrlResponse url = genProcessUrl(processId, accountId, request.getToken());
            // 组装返回参数
            ProcessStartResponse startResult = new ProcessStartResponse(processId, url.getLongUrl(), url.getUrl());
            startResult.setFlowId(startResponse.getFlowId());
            startResult.setCooperationId(startResponse.getCooperationId());
            startResult.setApprovalId(startResponse.getApprovalId());
            // 兜底获取子流程id
            if (ProcessCurrentStatusEnum.COOPERATION.getStatus() == url.getStatus()) {
                startResult.setCooperationId(url.getSubProcessId());
            } else if (ProcessCurrentStatusEnum.SIGN.getStatus() == url.getStatus()) {
                startResult.setFlowId(url.getSubProcessId());
            } else if (ProcessCurrentStatusEnum.CONTRACT_APPROVAL.getStatus() == url.getStatus()) {
                startResult.setApprovalId(url.getSubProcessId());
            }
            return startResult;
        } finally {
            // 清除防重缓存
            cacheService.remove(startProcessKey);
        }
    }

    //查询用户已扫码加入的流程
    private ProcessStartResponse queryJoinedTask(String taskId, ShareSignParticipantAccount userAccount, String token) {
        // 查询用户扫码参与的流程列表，列表最大数量与最大参与次数一致
        List<ShareSignProcessDO> existProcess =
                baseService.queryByAccountGid(taskId, userAccount.getAccountGid(), CommonBizConfig.SHARE_SIGN_TASK_PSN_PROCESS_MAX);
        // 如果当前用户已经参与任务， 直接返回流程详情
        if (CollectionUtils.isNotEmpty(existProcess)) {
            int size = existProcess.size();
            for (int i = size - 1; i >= 0; i--) {
                ShareSignProcessDO process = existProcess.get(i);
                // 校验当前扫码人+主体和已参与扫码流程的用户是否一致， 如果不一致，跳过
                if (!checkSameSignAccount(userAccount, process)) {
                    continue;
                }
                // 如果一致， 直接获取流程详情
                String processId = process.getProcessId();
                // 如果流程状态为发起中， 则报错， 否则返回流程地址
                if (ShareSignProcessStatusEnum.INITIATING.getType().equals(process.getStatus())) {
                    throw new BizContractManagerException(SHARE_SIGN_PROCESS_INITIATING);
                }
                GetProcessUrlResponse url = genProcessUrl(processId, userAccount.getAccountOid(), token);
                // 组装返回参数
                ProcessStartResponse startResult = new ProcessStartResponse(processId, url.getLongUrl(), url.getUrl());
                // 兜底获取子流程id
                if (ProcessCurrentStatusEnum.COOPERATION.getStatus() == url.getStatus()) {
                    startResult.setCooperationId(url.getSubProcessId());
                } else if (ProcessCurrentStatusEnum.SIGN.getStatus() == url.getStatus()) {
                    startResult.setFlowId(url.getSubProcessId());
                } else if (ProcessCurrentStatusEnum.CONTRACT_APPROVAL.getStatus() == url.getStatus()) {
                    startResult.setApprovalId(url.getSubProcessId());
                }
                return startResult;
            }
        }
        return null;
    }

    /**
     * 校验并获取发起参数
     *
     * @param shareTask
     * @param participantMap
     * @return
     */
    private ProcessStartDetailResponse getShareSignProcessInfo(
            ShareSignTaskDO shareTask, Map<String, ShareSignParticipantAccount> participantMap) {

        String flowTemplateId = shareTask.getShareTemplateId();
        String tenantId = shareTask.getSubjectOid();
        // 查询主体账号信息
        UserAccountDetail tenantInfo = userCenterService.getUserAccountDetailByOid(tenantId);
        // 查下流程模板详情
        ProcessStartDetailResponse response;
        try {
            response = flowTemplateService.startDetail(flowTemplateId, tenantInfo, false, false);
        } catch (BizContractManagerException e) {
            if (FLOW_TEMPLATE_NOT_EXIST.getCode().equals(e.getCode())) {
                throw new BizContractManagerException(SHARE_SIGN_TASK_INVALID);
            }
            throw e;
        }
        response.setInitiatorAccountId(shareTask.getAccountOid());
        // 设置发起场景
        int startScene = ProcessStartScene.DIRECT_START.getScene();
        int startType = ProcessStartType.SHARE_SCAN_DIRECT_START.getType();
        if (ShareSignFromEnum.TEMPLATE_SIGN.getType().equals(shareTask.getTaskFrom())) {
            startScene = ProcessStartScene.TEMPLATE_START.getScene();
            startType = ProcessStartType.SHARE_SCAN_TEMPLATE_START.getType();
        }
        response.setScene(startScene);
        for (ParticipantBO participant : response.getParticipants()) {
            ShareSignParticipantAccount accountInfo = participantMap.get(participant.getParticipantId());
            if (null == accountInfo) {
                continue;
            }
            buildShareParticipantBO(participant, accountInfo);
        }
        //补充扫码签附件文件授权
        fillScanStartFileAuth(response, tenantId);

        response.setFlowId(null);
        response.setProcessId(null);
        // 将refFlowTemplateId设置到源模板id上，不为空时，发起时会将这个模板id传给签署， 用于做自动落章权限校验
        response.setOriginFlowTemplateId(shareTask.getOriginTemplateId());
        // 如果refFlowTemplateId不为空， 发起时获取查询模板最新信息，扫码签场景不需要，所以置空
        response.setRefFlowTemplateId(null);
        // 设置发起类型为扫码发起
        response.setStartType(startType);
        if (ShareSignTypeEnum.MULTI_PARTICIPANT_SHARE.getType().equals(shareTask.getShareType())) {
            response.setProcessId(shareTask.getShareBizId());
        } else {
            response.setProcessGroupId(shareTask.getShareBizId());
            response.setNoNeedDeleteTemp(true);
        }

        // 多版本售卖: 模板发起时校验文件到期日期
        if (response.assignFileValidity()) {
            saasCommonClient.checkFunctionValid(tenantId, INITIATE_CONTRACT_EXPIRE, true);
        }
        //使用的水印模板
        response.setUseWatermarkTemplateId(response.getUseWatermarkTemplateId());
        response.setUseWatermarkTemplateSnapshotId(response.getUseWatermarkTemplateSnapshotId());
        return response;
    }


    //设置附件文件权限
    private void fillScanStartFileAuth(ProcessStartDetailResponse response, String tenantId){
        if(CollectionUtils.isEmpty(response.getFiles())){
            return;
        }
        List<FileDetailBO> attachments = response.getFiles().stream()
                .filter(i -> ProcessFileType.isAttachment(i.getFileType()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(attachments)){
            return;
        }
        boolean existFileAuth = attachments.stream().anyMatch(t -> CollectionUtils.isNotEmpty(t.getFileAuths()));
        if(!existFileAuth){
            return;
        }

        boolean support = saasCommonClient.checkFunctionValid(
                tenantId, FunctionCodeConstants.ATTACHMENT_AUTH, false);
        if(!support){
            for(FileDetailBO fileDetailBO : response.getFiles()){
                if(ProcessFileType.isAttachment(fileDetailBO.getFileType())){
                    fileDetailBO.setFileAuths(new ArrayList<>());
                }
            }
            return;
        }


        Map<String, List<ParticipantInstanceBO>> participantMap = response.getParticipants().stream().collect(Collectors.toMap(ParticipantBO::getParticipantLabel, ParticipantBO::getInstances, (o, n) -> n));;
        Map<String, Integer> participantTypeMap = response.getParticipants().stream().collect(Collectors.toMap(ParticipantBO::getParticipantLabel, ParticipantBO::getParticipantSubjectType , (o, n) -> n));;

        for(FileDetailBO fileDetailBO : attachments){
            if(CollectionUtils.isEmpty(fileDetailBO.getFileAuths())){
                continue;
            }
            for(FileAuthBO fileAuthBO : fileDetailBO.getFileAuths()){
                List<ParticipantInstanceBO> participantInstanceBOS = participantMap.get(fileAuthBO.getParticipantLabel());
                if(CollectionUtils.isEmpty(participantInstanceBOS)){
                    continue;
                }

                Integer type = participantTypeMap.get(fileAuthBO.getParticipantLabel());
                ParticipantInstanceBO instanceBO = participantInstanceBOS.get(0);
                if (ParticipantSubjectType.PSN.getType() == type) {
                    fileAuthBO.setAuthType(ProcessAuthTypeEnum.MEMBER.getType());
                    fileAuthBO.setAuthExtend(StringUtils.isNotBlank(instanceBO.getAccountOid()) ? instanceBO.getAccountOid() : "");
                    fileAuthBO.setAuthId(StringUtils.isNotBlank(instanceBO.getAccountGid()) ? instanceBO.getAccountGid() : "");
                    fileAuthBO.setAuthName(instanceBO.getAccountName());
                } else if (ParticipantSubjectType.ORG.getType() == type) {
                    fileAuthBO.setAuthType(ProcessAuthTypeEnum.ENTERPRISE.getType());
                    fileAuthBO.setAuthExtend(StringUtils.isNotBlank(instanceBO.getSubjectId()) ? instanceBO.getSubjectId() : "");
                    fileAuthBO.setAuthId(StringUtils.isNotBlank(instanceBO.getSubjectGid()) ? instanceBO.getSubjectGid() : "");
                    fileAuthBO.setAuthName(instanceBO.getSubjectName());
                }
            }
        }

        response.getFiles().removeIf(i -> ProcessFileType.isAttachment(i.getFileType()));
        response.getFiles().addAll(attachments);
    }


    /**
     * 校验任务状态
     *
     * @param shareTask
     */
    private void checkShareTaskStatus(ShareSignTaskDO shareTask) {
        if (!CAN_USE.getStatus().equals(shareTask.getStatus())) {
            throw new BizContractManagerException(SHARE_SIGN_TASK_ENDED);
        }
        if (null != shareTask.getEndTime() && shareTask.getEndTime().before(new Date())) {
            throw new BizContractManagerException(SHARE_SIGN_TASK_ENDED);
        }
    }

    /**
     * 校验是否同一个扫码用户+主体
     *
     * @param userAccount
     * @param process
     * @return
     */
    private boolean checkSameSignAccount(
            ShareSignParticipantAccount userAccount, ShareSignProcessDO process) {
        // 用户账号对不上， 返回false
        if (!userAccount.sameAccount(process.getAccountOid(), process.getAccountGid())) {
            return false;
        }
        // 主体类型对不上， 返回false
        if (!userAccount.getParticipantType().equals(process.getParticipantType())) {
            return false;
        }

        // 如果主体是个人， 返回true
        if (ParticipantSubjectType.PSN.getType() == process.getParticipantType()) {
            return true;
        }
        // 如果主体是企业， 判断企业信息是否一致， 一致则返回true
        if (StringUtils.isNoneBlank(process.getSubjectOid(), userAccount.getSubjectId())
                && StringUtils.equals(process.getSubjectOid(), userAccount.getSubjectId())) {
            return true;
        }
        return StringUtils.isNoneBlank(process.getSubjectName(), userAccount.getSubjectName())
                && StringUtils.equals(process.getSubjectName(), userAccount.getSubjectName());
    }

    /**
     * 组装扫码方数据
     *
     * @param participant
     * @param accountInfo
     */
    private void buildShareParticipantBO(
            ParticipantBO participant, ShareSignParticipantAccount accountInfo) {
        participant.setSharable(false);
        participant.setShareParticipant(true);
        participant.setInstances(Lists.newArrayList());
        ParticipantInstanceBO instanceBO = new ParticipantInstanceBO();
        instanceBO.setAccountGid(accountInfo.getAccountGid());
        instanceBO.setAccountUid(accountInfo.getAccountUid());
        instanceBO.setAccount(accountInfo.getAccount());
        if (StringUtils.isBlank(instanceBO.getAccount())) {
            throw new BizContractManagerException(SHARE_SIGN_PARTICIPANT_MISSING_ACCOUNT);
        }
        instanceBO.setAccountOid(accountInfo.getAccountOid());
        instanceBO.setAccountName(accountInfo.getAccountName());
        if (ParticipantSubjectType.ORG.getType() == accountInfo.getParticipantType()) {
            instanceBO.setSubjectId(accountInfo.getSubjectId());
            instanceBO.setSubjectName(accountInfo.getSubjectName());
            instanceBO.setSubjectType(ParticipantSubjectType.ORG.getType());
        } else {
            instanceBO.setSubjectId(accountInfo.getAccountOid());
            instanceBO.setSubjectName(accountInfo.getAccountName());
            instanceBO.setSubjectType(ParticipantSubjectType.PSN.getType());
        }
        participant.getInstances().add(instanceBO);
    }

    /**
     * 更新任务库存量
     * @param taskId
     */
    private void upgradeTaskNum(String taskId) {
        // 更新任务参与数
        baseService.upgradeTaskNum(taskId);
        ShareSignTaskDO shareSignTaskDO = baseService.queryTaskById(taskId);
        if (shareSignTaskDO.getShareNum() >= shareSignTaskDO.getShareTotal()) {
            // 删除临时模板
            flowTemplateService.deleteFlowTemplate(
                    shareSignTaskDO.getShareTemplateId(), shareSignTaskDO.getSubjectOid());
            // 删除es临时流程组
            processChangeNotifyService.removeTempGroupInfo(
                    shareSignTaskDO.getSubjectGid(), shareSignTaskDO.getShareBizId());
        }
    }

    private GetProcessUrlResponse genProcessUrl(String processId, String accountId, String token) {
        GetProcessUrlRequest urlRequest = new GetProcessUrlRequest();
        urlRequest.setProcessId(processId);
        urlRequest.setAppId(RequestContext.getAppId());
        urlRequest.setAccountId(accountId);
        urlRequest.setSubjectId(accountId);
        urlRequest.setPlatform(SignPlatformEnum.STANDARD_H5.getPlatform());
        urlRequest.setClient(getClientId());
        urlRequest.setToken(token);
        return processService.getUrl(urlRequest, true);
    }

    /**
     * 处理扫码任务
     *
     * @param process
     * @return
     */
    @Override
    public void handleShareSignTask(ProcessDO process) {
        String processId = process.getProcessId();
        // 流程撤回， 释放名额
        if (ProcessStatusEnum.REVOKED.getStatus() == process.getStatus()) {
            List<ShareSignProcessDO> shareSignProcessList =
                    shareSignBaseService.queryByProcessId(processId);
            // 如果当前流程不是扫码流程， 跳过
            if (CollectionUtils.isEmpty(shareSignProcessList)) {
                return;
            }
            String taskId = shareSignProcessList.get(0).getShareSignTaskId();
            log.info("current processId: {}, shareSignTaskId: {}", processId, taskId);
            // 释放名额
            Elock lock = lockFactory.getLock("degrade-share-task-num-" + taskId);
            try {
                lock.doWithLockProtect(
                        1,
                        () -> {
                            ShareSignTaskDO shareSignTaskDO =
                                    shareSignBaseService.queryTaskById(taskId);
                            // 如果扫码任务不存在或者扫码任务不是单方扫码签， 跳过
                            if (null == shareSignTaskDO
                                    || !ShareSignTypeEnum.SINGLE_PARTICIPANT_SHARE
                                    .getType()
                                    .equals(shareSignTaskDO.getShareType())) {
                                return true;
                            }
                            // 如果扫码任务名额已满， 即使流程撤回也不释放名额
                            if (shareSignTaskDO.getShareNum() >= shareSignTaskDO.getShareTotal()) {
                                log.info("share task num is full, skip");
                                return true;
                            }

                            // 移除绑定关系
                            if (shareSignBaseService.deleteByProcessId(processId) > 0) {
                                // 移除成功， 释放名额
                                shareSignBaseService.degradeTaskNumOccupied(taskId);
                                shareSignBaseService.degradeTaskNum(taskId);
                            }

                            return true;
                        });
            } catch (Exception e) {
                log.warn("degradeTaskNum failed, taskId:{}, exception:{}", taskId, e.getMessage());
            }
        }
    }

    @Override
    public ShareSignSupportScanResponse supportScan(String flowTemplateId) {
        ShareSignSupportScanResponse result = new ShareSignSupportScanResponse();
        UserAccount tenantInfo =
                userCenterService.getUserAccountBaseByOid(RequestContextExtUtils.getTenantId());
        GetFlowTemplateResult getFlowTemplateResult =
                flowTemplateService.getFlowTemplateResult(flowTemplateId, tenantInfo);
        result.setSupportScan(
                supportScan(
                        flowTemplateId,
                        mapperFactory
                                .getMapperFacade()
                                .mapAsList(
                                        getFlowTemplateResult.getCooperationers(),
                                        ParticipantBO.class)));
        // 设置当前用户使用的模板是否支持扫码签
        // 缓存5秒
        TedisUtil.set(
                CacheUtil.getSupportScan(RequestContextExtUtils.getOperatorId(), flowTemplateId),
                result.getSupportScan(),
                5,
                TimeUnit.SECONDS);
        return result;
    }

    @Override
    public ShareSignInProcessResponse inProcessNum() {
        String accountOid = RequestContextExtUtils.getOperatorId();
        if (StringUtils.isBlank(accountOid)) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM.getCode(),
                    PROCESS_ILLEGAL_PARAM.getMessage("用户信息获取失败"));
        }
        ShareSignInProcessResponse response = new ShareSignInProcessResponse();
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(RequestContextExtUtils.getOperatorId());
        if(StringUtils.isBlank(userAccount.getAccountGid())){
            return response;
        }
        response.setInProcessNum(
                baseService.countTaskByAccountGid(
                        userAccount.getAccountGid(),
                        NumberUtils.INTEGER_ONE,
                        ShareSignTypeEnum.SINGLE_PARTICIPANT_SHARE.getType()));
        return response;
    }

    @Override
    public ShareSignTaskCountResponse countTask(String accountOid, String subjectOid) {
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(accountOid);
        UserAccount subjectAccount = userCenterService.getUserAccountBaseByOid(subjectOid);
        if (Objects.isNull(userAccount)
                || StringUtils.isBlank(userAccount.getAccountGid())
                || Objects.isNull(subjectAccount)
                || StringUtils.isBlank(subjectAccount.getAccountGid())) {
            return new ShareSignTaskCountResponse(0L, 0L);
        }

        // 统计，这里会走覆盖索引，不回表
        List<Map<String, Object>> taskCounts =
                baseService.countByStatus(
                        userAccount.getAccountGid(), subjectAccount.getAccountGid());
        if (CollectionUtils.isEmpty(taskCounts)) {
            return new ShareSignTaskCountResponse(0L, 0L);
        }

        Map<Integer, Long> statusCount =
                taskCounts.stream()
                        .collect(
                                Collectors.toMap(
                                        x -> MapUtils.getInteger(x, "status"),
                                        x -> MapUtils.getLong(x, "count", 0L)));

        return new ShareSignTaskCountResponse(
                Optional.ofNullable(statusCount.get(CAN_USE.getStatus())).orElse(0L),
                Optional.ofNullable(statusCount.get(CAN_NOT_USE.getStatus())).orElse(0L));
    }

    @Override
    public ShareSignTaskUrlResponse startTaskByFlowTemplate(
            ShareSignTaskStartFlowTemplateRequest request) {
        // 1 获取模板信息
        ProcessFlowTemplateDetailResponse flowTemplateDetail =
                flowTemplateService.startDetail(request.getFlowTemplateId());
        // 2 判断模板是否支持扫码签和状态
        if (!supportScan(request.getFlowTemplateId(), flowTemplateDetail.getParticipants())) {
            log.warn("当前模板不支持扫码签: flowTemplateId:{}", request.getFlowTemplateId());
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.SHARE_SIGN_TASK_FLOW_TEMPLATE_NOT_SUPPORT);
        }
        // 3 获取多版本信息
        VipFunctionQueryOutput vipFunctionQueryOutput =
                saasCommonClient.queryVipFunctionInfo(
                        RequestContextExtUtils.getOperatorId(),
                        FunctionCodeConstants.SHARE_SCAN_SIGN,
                        RequestContextExtUtils.getClientId());
        // 4 设置扫码签标识
        ShareSignTaskStartRequest startRequest =
                mapperFactory
                        .getMapperFacade()
                        .map(flowTemplateDetail, ShareSignTaskStartRequest.class);
        //设置固定成员参与人信息
        flowTemplateDetail.getParticipants().stream().filter(e -> CooperationerRoleSetEnum.DESIGNATED_USER.getType()
                == e.getRoleSet()).findFirst().ifPresent(designatedUser -> startRequest.getParticipants().stream().filter(e -> CooperationerRoleSetEnum.DESIGNATED_USER.getType()
                == e.getRoleSet()).forEach(e -> {
            e.setInstances(designatedUser.getInstances());
        }));

        startRequest.setShareTaskTotal(vipFunctionQueryOutput.parseBatchLimit());
        startRequest.setScene(ProcessStartScene.TEMPLATE_START.getScene());
        startRequest.setInitiatorAccountId(RequestContextExtUtils.getOperatorId());
        startRequest.setShareSignStartByFlowTemplateId(true);
        startRequest.setClientId(request.getClientId());
        startRequest.setSubjectOid(request.getSubjectOid());
        startRequest.setOperatorOid(request.getOperatorOid());
        startRequest.setAppId(request.getAppId());
        startRequest.setAppName(request.getAppName());
        startRequest.setNeedSealAuthCheck(request.isNeedSealAuthCheck());
        return startTask(startRequest);
    }

    @Override
    public ShareSignTaskListResponse inProcessList(ShareSignTaskInProcessListRequest request) {
        ShareSignTaskListResponse response = new ShareSignTaskListResponse();
        response.setCurrPage(request.getPageNum());
        response.setTotal(0);
        response.setPages(0);
        // 获取操作人账号id
        String operatorId = RequestContextExtUtils.getOperatorId();
        // 查询操作人账号信息
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(operatorId);
        if(StringUtils.isBlank(userAccount.getAccountGid())){
            return response;
        }
        ShareSignTaskListParam input = new ShareSignTaskListParam();
        input.setPageSize(request.getPageSize());
        input.setPageNum(request.getPageNum());
        input.setShareType(ShareSignTypeEnum.SINGLE_PARTICIPANT_SHARE.getType());
        input.setStatus(request.getStatus());
        input.setAccountGid(userAccount.getAccountGid());
        // 判断是否过滤主体， 如果是，指定主体id
        if (request.isFilterSubject()) {
            // 获取操作主体账号id
            String tenantId = RequestContextExtUtils.getTenantId();
            UserAccount subjectAccount;
            // 如果操作主体账号为空 或 操作主体账号与操作人一致， 则设置操作主体账号信息和操作人账号信息一致
            if (StringUtils.isBlank(tenantId) || StringUtils.equals(operatorId, tenantId)) {
                subjectAccount = userAccount;
            } else {
                // 查询操作主体账号信息
                subjectAccount = userCenterService.getUserAccountBaseByOid(tenantId);
            }
            // 如果操作主体账号无GID, 返回空列表
            if(StringUtils.isBlank(subjectAccount.getAccountGid())){
                return response;
            }
            // 设置主体id查询条件
            input.setSubjectGid(subjectAccount.getAccountGid());
        }
        Page<ShareSignTaskDO> signTaskPage = baseService.getListByPage(input);
        response.setTotal(signTaskPage.getTotal());
        response.setPages(signTaskPage.getPages());
        if (CollectionUtils.isNotEmpty(signTaskPage.getResult())) {
            response.setTasks(
                    signTaskPage.getResult().stream()
                            .map(this::buildShareSignTaskListBean)
                            .collect(Collectors.toList()));
        }
        return response;
    }

    @Override
    public ShareSignTaskDetailCountResponse taskDetailCount(String shareSignTaskId,String subjectId) {
        ShareSignTaskDetailCountResponse response = new ShareSignTaskDetailCountResponse();
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(shareSignTaskId);
        response.setTaskDone(shareTask.getShareDone());
        response.setTaskNum(shareTask.getShareNum());
        // 查询待我签署数量
        GroupQueryModel groupQueryModel = new GroupQueryModel();
        groupQueryModel.setGroupId(shareTask.getShareBizId());
        groupQueryModel.setCreatorAccountId(RequestContextExtUtils.getOperatorId());
        groupQueryModel.setOwnerAccountId(
                StringUtils.isNotBlank(subjectId)
                        ? subjectId
                        : shareTask.getSubjectOid());
        groupQueryModel.setPageNum(1);
        groupQueryModel.setPageSize(1);
        groupQueryModel.setIsShowFlow(false);
        groupQueryModel.setFilterInvalidProcesses(ShareSignVersionEnum.v2(shareTask.getVersion()));
        //待签署
        groupQueryModel.setStatus(Collections.singletonList(ProcessStatusEnum.SIGN.getStatus()));
        DocQueryResult queryWaitResult = esClient.commonSearchFlows(groupQueryModel);
        response.setWaitingApproveNum(queryWaitResult.getTotal());
        //合同审批中
        groupQueryModel.setStatus(Collections.singletonList(ProcessStatusEnum.APPROVAL.getStatus()));
        DocQueryResult approvalResult = esClient.commonSearchFlows(groupQueryModel);
        response.setApprovalNum(approvalResult.getTotal());
        //填写中
        groupQueryModel.setStatus(Collections.singletonList(ProcessStatusEnum.COOPERATION.getStatus()));
        DocQueryResult cooperationResult = esClient.commonSearchFlows(groupQueryModel);
        response.setCooperationNum(cooperationResult.getTotal());
        return response;
    }

    @Override
    public ShareSignTaskJoinInfoResponse taskJoinInfo(String taskId, String accountId, String subjectName) {
        ShareSignTaskJoinInfoResponse response = new ShareSignTaskJoinInfoResponse();
        UserAccount userAccount = userCenterService.getUserAccountBaseByOid(accountId);
        UserAccount subjectAccount = userAccount;
        if (StringUtils.isNotBlank(subjectName)) {
            subjectAccount = userCenterService.getOrgInfoByNameForAccpect(subjectName);
        }
        //获取扫码签原任务
        ShareSignTaskDO shareSignTaskDO = checkAndGetShareSignTask(taskId);

        //是否为发起人，发起人只判断个人
        boolean personEQ = StringUtils.isNoneBlank(shareSignTaskDO.getAccountOid(), userAccount.getAccountOid())
                && StringUtils.equals(shareSignTaskDO.getAccountOid(), userAccount.getAccountOid());
        personEQ = personEQ || StringUtils.isNoneBlank(shareSignTaskDO.getAccountGid(), userAccount.getAccountGid())
                && StringUtils.equals(shareSignTaskDO.getAccountGid(), userAccount.getAccountGid());
        response.setTaskInitiator(personEQ);


        //加入次数
        int count;
        if (StringUtils.isBlank(subjectName)) {
            count = shareSignBaseService.countByAccountGidWithPerson(taskId, userAccount.getAccountGid());
        }else {
            List<ShareSignProcessDO> signProcessDOS =
                    shareSignBaseService.queryByAccountGid(
                            taskId, userAccount.getAccountGid(), 5000);
            count = signProcessDOS.stream()
                                    .filter(d -> subjectName.equals(d.getSubjectName()))
                                    .mapToInt(d -> 1).sum();
        }
        response.setSignTimes(count);

        return response;
    }

    @Override
    public ProcessStartResponse queryJoinedTask(QueryJoinProcessRequest request) {
        String accountId = request.getAccountOid();
        UserAccountDetail userAccount = userCenterService.getLoginAccountDetailByOid(accountId);
        // 校验并获取分享任务对象
        ShareSignTaskDO shareTask = checkAndGetShareSignTask(request.getShareSignTaskId());
        // 获取参与方类型
        ShareSignProcessStartRequest input = new ShareSignProcessStartRequest();
        input.setShareScanId(request.getShareScanId());
        input.setOrgName(request.getOrgName());
        Integer participantType = getParticipantType(input);
        // 获取签署用户
        ShareSignParticipantAccount signAccount = ShareSignParticipantAccount.valueOf(userAccount);
        signAccount.setSubjectName(request.getOrgName());
        signAccount.setParticipantType(participantType);
        // 查询
        ProcessStartResponse response = queryJoinedTask(shareTask.getTaskId(), signAccount, null);
        if (null == response) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.SHARE_SIGN_PROCESS_INFO_FAILED);
        }
        return response;
    }

    private boolean isRepeatSign(String bizExtra) {
        if (StringUtils.isBlank(bizExtra)) {
            return false;
        }

        ShareSignTaskBizExtra taskBizExtra = JsonUtils.json2pojo(bizExtra, ShareSignTaskBizExtra.class);
        return BooleanUtils.isTrue(taskBizExtra.getRepeatSign());
    }

    /** 判断是否支持扫码签任务 */
    private boolean supportScan(String flowTemplateId, List<ParticipantBO> participants) {
        // 1判断用户模板参与方类型 目前只支持个人和企业  2 只有一方是企业使用时指定模板
        int initiatorDesignateCount = 0;
        for (ParticipantBO participantBO : participants) {
            // 未指定参与人 目前这种情况出现在钉钉创建的模板
            if (ParticipantSubjectType.UNDETERMINED.getType() == participantBO.getParticipantSubjectType()) {
                return false;
            }
            if (CooperationerRoleSetEnum.INITIATOR_DESIGNATE.getType()
                    == participantBO.getRoleSet()) {
                initiatorDesignateCount++;
            }
        }
        if (initiatorDesignateCount != 1) {
            return false;
        }

        // 2 查询缓存 放在这里的目的是为了可以减少查询redis
        Boolean supportScan =
                TedisUtil.get(
                        CacheUtil.getSupportScan(
                                RequestContextExtUtils.getOperatorId(), flowTemplateId));
        if (Objects.nonNull(supportScan) && Boolean.TRUE.equals(supportScan)) {
            return true;
        }

        // 3 用户信息 如果未实名
        UserAccount userAccount =
                userCenterService.getUserAccountBaseByOid(RequestContextExtUtils.getOperatorId());
        if (StringUtils.isBlank(userAccount.getAccountGid())) {
            return false;
        }
        // 4 主体信息
        UserAccount userSubjectAccount =
                userCenterService.getUserAccountBaseByOid(RequestContextExtUtils.getTenantId());
        if (userSubjectAccount.isPerson()) {
            return true;
        }
        // 4 当前用户模板发起是否需要合同模板审批： 如果需要则不支持扫码发起
        ContractApprovalTemplateUserInput input = new ContractApprovalTemplateUserInput();
        input.setType(ModelTypeEnums.CONTRACT.name());
        input.setAccountOid(RequestContextExtUtils.getOperatorId());
        input.setOrgId(
                StringUtils.isNotBlank(RequestContextExtUtils.getTenantId())
                        ? RequestContextExtUtils.getTenantId()
                        : RequestContextExtUtils.getOperatorId());
        return CollectionUtils.isEmpty(userCenterService.getCanStartTemplates(input));
    }

    /**
     * 根据模板id查询任务节点信息
     *
     * @param flowTemplateId
     * @return
     */
    private void populateProcessAccounts(ShareSignTaskInfoResponse.TaskNode taskNode, String flowTemplateId) {
        ProcessFlowTemplateDetailResponse detailResponse =
                flowTemplateService.startDetail(flowTemplateId);
        if (Objects.isNull(detailResponse)
                || CollectionUtils.isEmpty(detailResponse.getParticipants())) {
            return;
        }

        // 把每个参与方根据是填写方还是签署方，放到对应的列表里
        List<ParticipantBO> fillParticipants = new ArrayList<>();
        List<ParticipantBO> signParticipants = new ArrayList<>();
        for (ParticipantBO participant : detailResponse.getParticipants()) {
            List<String> roles =
                    Lists.newArrayList(participant.getRole().replaceAll(" ", "").split(","));
            if (roles.contains(FORMULATER.getRole().toString())) {
                fillParticipants.add(participant);
            }
            if (roles.contains(SIGNER.getRole().toString())) {
                signParticipants.add(participant);
            }
        }

        // 构建填写人、签署人信息集合
        taskNode.setFillAccountList(buildAccountList(fillParticipants, FORMULATER.getRole()));
        taskNode.setSignAccountList(buildAccountList(signParticipants, SIGNER.getRole()));

        // 构建填写人、签署人信息集合，参与方下有多个实例
        taskNode.setCooperatorAccounts(buildMultiAccountList(fillParticipants, FORMULATER.getRole()));
        taskNode.setSignerAccounts(buildMultiAccountList(signParticipants, SIGNER.getRole()));
    }

    private void populateApprovalAccount(ShareSignTaskInfoResponse.TaskNode taskNode, ShareSignTaskDO shareTask){
        if(shareTask == null || StringUtils.isBlank(shareTask.getBizExtra())){
            return;
        }
        ShareSignTaskBizExtra bizExt = JSONObject.parseObject(shareTask.getBizExtra(),ShareSignTaskBizExtra.class);
        String approvalTemplateId = bizExt.getApprovalTemplateId();

        if(StringUtils.isBlank(approvalTemplateId)){
            return;
        }
        String deptId = null;
        List<OrgDeptDTO> deptList = userCenterService.getDeptInfosByMemberId(shareTask.getSubjectOid(), shareTask.getAccountOid());
        // 如果部门列表不为空， 默认获取第一个部门id
        if (CollectionUtils.isNotEmpty(deptList)) {
            deptId = deptList.get(0).getDeptId();
        }

        UseApprovalFlowTemplateDTO useParam = new UseApprovalFlowTemplateDTO();
        useParam.setApprovalTemplateId(approvalTemplateId);
        useParam.setSubjectGid(shareTask.getSubjectGid());
        // 新合同审批模板
        if (contractApprovalService.useApprovalFlowTemplate(useParam)) {
            VirtualStartApprovalFlowDTO param = new VirtualStartApprovalFlowDTO();
            param.setApprovalTemplateId(approvalTemplateId);
            param.setSubjectOid(shareTask.getSubjectOid());
            param.setSubjectGid(shareTask.getSubjectGid());
            param.setSubjectName(shareTask.getSubjectName());
            param.setAccountOid(shareTask.getAccountOid());
            param.setAccountGid(shareTask.getAccountGid());
            param.setAccountName(shareTask.getAccountName());
            param.setAccountDeptId(deptId);
            VirtualStartApprovalFlowResult result = contractApprovalService.virtualStartApprovalFlow(param);
            List<ProcessNodeUsersResponse.ProcessNodeAccount> approvalAccounts =buildAccountList(result.getTaskNodes());
            taskNode.setApproverAccounts(approvalAccounts);
            return;
        }
        // 3.1.1 查询审批模板，使用发起人的主体
        FlowModelOutput flowDefineDetail =
                approvalClient.getFlowDefineDetailByVersion(
                        Long.parseLong(approvalTemplateId),shareTask.getSubjectOid());
        if (Objects.nonNull(flowDefineDetail)
                && CollectionUtils.isNotEmpty(flowDefineDetail.getNodeList())) {
            taskNode.setApproverAccounts(buildOldApprovalAccountList(flowDefineDetail.getNodeList()));
        }
    }



    private void populateStartAccount(
            ShareSignTaskInfoResponse.TaskNode taskNode, ShareSignTaskDO shareTask) {
        ProcessNodeUsersResponse.ProcessAccountInfo accountInfo =
                new ProcessNodeUsersResponse.ProcessAccountInfo();
        AccountBean person = new AccountBean();
        person.setOid(shareTask.getAccountOid());
        person.setGid(shareTask.getAccountGid());
        person.setName(shareTask.getAccountName());

        AccountBean subject = new AccountBean();
        subject.setOid(shareTask.getSubjectOid());
        subject.setGid(shareTask.getSubjectGid());
        subject.setName(shareTask.getSubjectName());

        accountInfo.setPerson(person);
        accountInfo.setSubject(subject);
        // 设置主体类型
        if (StringUtils.equals(person.getOid(), subject.getOid())
                || StringUtils.equals(person.getGid(), subject.getGid())) {
            accountInfo.setType(SubjectTypeEnum.PERSON.getType());
        } else {
            accountInfo.setType(SubjectTypeEnum.ORG.getType());
        }

        taskNode.setStartAccount(accountInfo);
    }

    private List<ProcessNodeUsersResponse.ProcessAccountInfo> buildAccountList(
            List<ParticipantBO> participants, Integer role) {
        if (CollectionUtils.isEmpty(participants)) {
            return Collections.emptyList();
        }

        return participants.stream()
                .map(x -> buildProcessAccountInfo(x, role))
                .sorted(Comparator.comparing(ProcessNodeUsersResponse.ProcessAccountInfo::getOrder))
                .collect(Collectors.toList());
    }

    private List<ProcessNodeUsersResponse.ProcessNodeAccount> buildMultiAccountList(
            List<ParticipantBO> participants, Integer role) {
        if (CollectionUtils.isEmpty(participants)) {
            return Collections.emptyList();
        }

        return participants.stream()
                .map(x -> buildProcessNodeAccount(x, role))
                .sorted(Comparator.comparing(ProcessNodeUsersResponse.ProcessNodeAccount::getOrder))
                .collect(Collectors.toList());
    }

    private List<ProcessNodeUsersResponse.ProcessNodeAccount> buildAccountList(List<ApprovalNode> taskNodes){
        List<ProcessNodeUsersResponse.ProcessNodeAccount> result = new ArrayList<>();

        if(CollectionUtils.isEmpty(taskNodes)){
            return result;
        }
        for (ApprovalNode taskNode : taskNodes) {
            for (ApprovalNode.ApprovalNodeTask task : taskNode.getTasks()) {
                ProcessNodeUsersResponse.ProcessNodeAccount nodeAccount = new ProcessNodeUsersResponse.ProcessNodeAccount();
                nodeAccount.setOrder(taskNode.getOrder());
                nodeAccount.setRole(ProcessNodeUsersResponse.NodeRole.APPROVER.getRole());
                nodeAccount.setType(SubjectTypeEnum.ORG.getType());
                nodeAccount.setSharable(false);
                nodeAccount.setAccounts(task.getUsers().stream().map(p->{
                    ProcessNodeUsersResponse.AccountInfo accountInfo = new ProcessNodeUsersResponse.AccountInfo();
                    AccountBean person = new AccountBean();
                    person.setOid(p.getAccountId());
                    person.setGid(p.getAccountGid());
                    person.setName(p.getAccountName());
                    accountInfo.setPerson(person);
                    return accountInfo;
                }).collect(Collectors.toList()));
                result.add(nodeAccount);
            }}
        result.sort(Comparator.comparingInt(ProcessNodeUsersResponse.ProcessNodeAccount::getOrder));
        return result;
    }

    private List<ProcessNodeUsersResponse.ProcessNodeAccount> buildOldApprovalAccountList(List<FlowNodeOutput> nodeList){
        List<ProcessNodeUsersResponse.ProcessNodeAccount> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(nodeList)){
            return result;
        }
        int order = 0;
        for (FlowNodeOutput node:nodeList) {
            ProcessNodeUsersResponse.ProcessNodeAccount nodeAccount = new ProcessNodeUsersResponse.ProcessNodeAccount();
            nodeAccount.setOrder(order);
            nodeAccount.setRole(ProcessNodeUsersResponse.NodeRole.APPROVER.getRole());
            nodeAccount.setType(SubjectTypeEnum.ORG.getType());
            nodeAccount.setSharable(false);
            nodeAccount.setAccounts(node.getNodeUsers().stream().map(p->{
                ProcessNodeUsersResponse.AccountInfo accountInfo = new ProcessNodeUsersResponse.AccountInfo();
                AccountBean person = new AccountBean();
                person.setOid(p.getUserId());
                person.setName(p.getName());
                accountInfo.setPerson(person);
                return accountInfo;
            }).collect(Collectors.toList()));
            order++;
        }
        result.sort(Comparator.comparingInt(ProcessNodeUsersResponse.ProcessNodeAccount::getOrder));
        return result;
    }

    /**
     * 构建流程中的账号信息
     *
     * @param participantBO
     * @param role
     * @return
     */
    private ProcessNodeUsersResponse.ProcessAccountInfo buildProcessAccountInfo(
            ParticipantBO participantBO, Integer role) {
        ProcessNodeUsersResponse.ProcessAccountInfo accountInfo =
                new ProcessNodeUsersResponse.ProcessAccountInfo();

        accountInfo.setSharable(participantBO.isSharable());
        accountInfo.setType(participantBO.getParticipantSubjectType());
        accountInfo.setCurrentUser(false);

        // 账号信息填充，只有非扫码加入的方有账号信息(扫码加入的一方发起任务时不填，是空的，所以不填充)
        if (!accountInfo.isSharable()
                && CollectionUtils.isNotEmpty(participantBO.getInstances())
                && Objects.nonNull(participantBO.getInstances().get(0))) {
            ParticipantInstanceBO instanceBO = participantBO.getInstances().get(0);
            accountInfo.setPerson(buildPersonAccount(instanceBO));
            accountInfo.setSubject(buildSubjectAccount(instanceBO));
            accountInfo.setType(instanceBO.getSubjectType());
        }
        // 填写顺序 or 签署顺序
        if (FORMULATER.getRole().equals(role)) {
            accountInfo.setOrder(participantBO.getFillOrder());
        } else if (SIGNER.getRole().equals(role)) {
            accountInfo.setOrder(participantBO.getSignOrder());
        }

        return accountInfo;
    }

    /** 构建流程中的账号信息 */
    private ProcessNodeUsersResponse.ProcessNodeAccount buildProcessNodeAccount(
            ParticipantBO participantBO, Integer role) {
        ProcessNodeUsersResponse.ProcessNodeAccount accountInfo =
                new ProcessNodeUsersResponse.ProcessNodeAccount();

        accountInfo.setSharable(participantBO.isSharable());
        accountInfo.setType(participantBO.getParticipantSubjectType());
        accountInfo.setCurrentUser(false);

        // 账号信息填充，只有非扫码加入的方有账号信息(扫码加入的一方发起任务时不填，是空的，所以不填充)
        if (!accountInfo.isSharable()
                && CollectionUtils.isNotEmpty(participantBO.getInstances())
                && Objects.nonNull(participantBO.getInstances().get(0))) {
            ParticipantInstanceBO instanceBO = participantBO.getInstances().get(0);
            accountInfo.setType(instanceBO.getSubjectType());
            accountInfo.setAccounts(buildMultiAccounts(participantBO.getInstances()));
        }
        // 填写顺序 or 签署顺序
        if (FORMULATER.getRole().equals(role)) {
            accountInfo.setOrder(participantBO.getFillOrder());
        } else if (SIGNER.getRole().equals(role)) {
            accountInfo.setOrder(participantBO.getSignOrder());
        }

        return accountInfo;
    }

    private List<ProcessNodeUsersResponse.AccountInfo> buildMultiAccounts(
            List<ParticipantInstanceBO> instances) {
        if (CollectionUtils.isEmpty(instances)) {
            return Collections.emptyList();
        }

        return instances.stream()
                .map(
                        instance -> {
                            ProcessNodeUsersResponse.AccountInfo accountInfo =
                                    new ProcessNodeUsersResponse.AccountInfo();
                            accountInfo.setPerson(buildPersonAccount(instance));
                            accountInfo.setSubject(buildSubjectAccount(instance));
                            return accountInfo;
                        })
                .collect(Collectors.toList());
    }

    private AccountBean buildPersonAccount(ParticipantInstanceBO instanceBO) {
        AccountBean accountBean = new AccountBean();
        accountBean.setGid(instanceBO.getAccountGid());
        accountBean.setOid(instanceBO.getAccountOid());
        accountBean.setUid(instanceBO.getAccountUid());
        accountBean.setName(instanceBO.getAccountName());
        if (ValidateUtil.mobileValid(instanceBO.getAccount())) {
            accountBean.setMobile(instanceBO.getAccount());
        } else {
            accountBean.setEmail(instanceBO.getAccount());
        }
        accountBean.setNickname(instanceBO.getAccountNick());
        accountBean.setLicense(instanceBO.getAccountLicense());
        accountBean.setLicenseType(instanceBO.getAccountLicenseType());
        return accountBean;
    }

    private AccountBean buildSubjectAccount(ParticipantInstanceBO instanceBO) {
        AccountBean accountBean = new AccountBean();
        accountBean.setGid(instanceBO.getSubjectGid());
        accountBean.setOid(instanceBO.getSubjectId());
        accountBean.setUid(instanceBO.getSubjectUid());
        accountBean.setName(instanceBO.getSubjectName());
        accountBean.setName(instanceBO.getSubjectName());

        return accountBean;
    }

    /**
     * 补充扫码加入方信息
     *
     * @param processes
     */
    private void populateSharableAccount(List<ProcessSimpleListBean> processes) {
        if (CollectionUtils.isEmpty(processes)) {
            return;
        }
        // 扫码加入的流程信息
        List<String> processIds =
                processes.stream()
                        .map(ProcessSimpleListBean::getProcessId)
                        .collect(Collectors.toList());
        Map<String, ShareSignProcessDO> shareSignProcessMap =
                shareSignBaseService.queryByProcessIds(processIds);
        if (CollectionUtils.isEmpty(shareSignProcessMap)) {
            return;
        }

        // 扫码的用户信息（因为shareSignProcess表没存accountName所以要单独查用户信息）
        List<String> accountIds =
                shareSignProcessMap.values().stream()
                        .map(ShareSignProcessDO::getAccountOid)
                        .collect(Collectors.toList());
        Map<String, UserAccountDetail> userAccountDetailMap =
                userCenterService.queryAccountMapByAccountIds(accountIds);

        // 补充扫码加入方信息
        for (ProcessSimpleListBean process : processes) {
            ShareSignProcessDO shareSignProcessDO = shareSignProcessMap.get(process.getProcessId());
            if (Objects.isNull(shareSignProcessDO)) {
                continue;
            }
            UserAccountDetail shareSignUserDetail =
                    userAccountDetailMap.get(shareSignProcessDO.getAccountOid());

            process.setSharableParticipant(
                    buildProcessParticipant(shareSignProcessDO, shareSignUserDetail));
        }
    }

    /**
     * 构建参与人信息
     *
     * @param shareSignProcessDO 扫码加入流程信息
     * @param shareSignUserDetail 扫码加入用户信息
     * @return
     */
    private static ProcessParticipant buildProcessParticipant(
            ShareSignProcessDO shareSignProcessDO, UserAccountDetail shareSignUserDetail) {
        String accountName =
                Optional.ofNullable(shareSignUserDetail)
                        .map(UserAccountDetail::getAccountName)
                        .orElse("");

        ProcessParticipant sharableParticipant = new ProcessParticipant();
        sharableParticipant.setAccount(shareSignProcessDO.getAccount());
        sharableParticipant.setAccountName(accountName);
        sharableParticipant.setSubjectName(shareSignProcessDO.getSubjectName());
        sharableParticipant.setCurrentUser(false);
        sharableParticipant.setSubjectType(shareSignProcessDO.getParticipantType());

        return sharableParticipant;
    }

}

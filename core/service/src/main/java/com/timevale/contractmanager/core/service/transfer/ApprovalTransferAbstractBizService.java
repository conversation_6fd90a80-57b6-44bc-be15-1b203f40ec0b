package com.timevale.contractmanager.core.service.transfer;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.contractapproval.facade.input.ApprovalTransferByAccountInput;
import com.timevale.contractapproval.facade.input.ApprovalTransferInput;
import com.timevale.contractapproval.facade.input.QueryApprovalPendingCountInput;
import com.timevale.contractapproval.facade.input.QueryTransferToUserListInput;
import com.timevale.contractapproval.facade.output.ApprovalTransferOutput;
import com.timevale.contractapproval.facade.output.QueryApprovalPendingCountOutput;
import com.timevale.contractapproval.facade.output.QueryTransferToUserListOutput;
import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.ApprovalInstanceClient;
import com.timevale.contractmanager.core.model.bo.transfer.SingleTransferResultBO;
import com.timevale.contractmanager.core.model.bo.transfer.TransferUserListBO;
import com.timevale.contractmanager.core.model.dto.member.MemberDeptDetailModel;
import com.timevale.contractmanager.core.model.dto.member.MemberRoleModel;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.transfer.TransferResultDTO;
import com.timevale.contractmanager.core.model.dto.user.OrgMemberBaseDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.contractapproval.ContractApprovalConverter;
import com.timevale.contractmanager.core.service.mq.transfer.ProcessApprovalTransferProducer;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.enums.TaskStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.TRANSFER_APPROVAL_FAILED;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.USER_ACCOUNT_NOT_EXIST;

/**
 * <AUTHOR>
 * @since 3/31/25
 */
@Slf4j
public abstract class ApprovalTransferAbstractBizService extends TransferAbstractBizService {

    public static final String NO_TASK_ID_PREFIX = "NT_";
    public static final String TASK_CENTER_ID_PREFIX = "TC_";
    public static final String CACHE_APPROVAL_TRANSFER_TOTAL_PREFIX = "approval:transfer:total:";
    public static final String CACHE_APPROVAL_TRANSFER_PROGRESS_PREFIX = "approval:transfer:progress:";
    public static final String CACHE_APPROVAL_TRANSFER_SUCCESS_PREFIX = "approval:transfer:success:";
    public static final String PROP_APPROVAL_USER_TRANSFERRED_CACHE_TIMEOUT = "approval.user.transferred.cache.hour.timeout";
    /** 审批转交任务总数key */
    public static String userApprovalTransferTotalKey(String taskId) {
        return String.format("%s:%s:%s", CacheUtil.HEAD, CACHE_APPROVAL_TRANSFER_TOTAL_PREFIX, taskId);
    }
    /** 审批转交任务进度key */
    public static String userApprovalTransferProgressKey(String taskId) {
        return String.format("%s:%s:%s", CacheUtil.HEAD, CACHE_APPROVAL_TRANSFER_PROGRESS_PREFIX, taskId);
    }
    /** 审批转交任务成功总数key */
    public static String userApprovalTransferSuccessKey(String taskId) {
        return String.format("%s:%s:%s", CacheUtil.HEAD, CACHE_APPROVAL_TRANSFER_SUCCESS_PREFIX, taskId);
    }

    @Autowired UserCenterService userCenterService;
    @Autowired ApprovalInstanceClient approvalInstanceClient;
    @Autowired ProcessApprovalTransferProducer processApprovalTransferProducer;

    /** 审批类型 */
    protected abstract ApprovalTypeEnum approvalType();
    /** 转交原因 */
    protected abstract String transferReason(TransferBizContext transferBizContext);
    /** 构建转交失败原因 */
    protected abstract String transferFailReason(long successCount, long totalCount);
    /** 自定义设置转交缓存 */
    protected abstract void setCustomizeTransferCache(TransferBizContext transferBizContext);
    /** 自定义清除转交缓存 */
    protected abstract void clearCustomizeTransferCache(TransferBizContext transferBizContext);
    /** 自定义转交完成回调处理 */
    protected abstract void customizeTransferCompleteCallback(SingleTransferResultBO transferResultBO, long successCount);
    /** 获取部分转交审批id */
    protected abstract List<String> partTransferApprovalIds(TransferBizContext transferBizContext);

    /**
     * 判断任务中心任务id
     * @param taskId
     * @return
     */
    protected boolean checkTaskCenterTaskId(String taskId) {
        return taskId.startsWith(TASK_CENTER_ID_PREFIX);
    }

    /**
     * 获取可转交用户列表
     * @param transferUserListBO 请求参数
     * @return
     */
    protected OrgDeptListResponse approvalTransferUserList(TransferUserListBO transferUserListBO) {
        // 是否用户全部转交
        Boolean isUserTransfer =
                CollectionUtils.isNotEmpty(transferUserListBO.getTransferUserList());
        String transferAccountOid =
                isUserTransfer
                        ? transferUserListBO.getTransferUserList().get(0)
                        : transferUserListBO.getTransferProcessInfo().getTransferAccountOid();
        // 包含已注销账号
        Map<String, UserAccountDetail> accountDetailMap =
                userCenterService.queryFatAccountMapByAccountIds(
                        Collections.singletonList(transferAccountOid));
        if (MapUtils.isEmpty(accountDetailMap)) {
            throw new BizContractManagerException(USER_ACCOUNT_NOT_EXIST);
        }
        UserAccountDetail userAccountDetail = accountDetailMap.get(transferAccountOid);

        QueryTransferToUserListInput input = new QueryTransferToUserListInput();
        input.setOrgOid(transferUserListBO.getTenantId());
        input.setApproverGid(userAccountDetail.getAccountGid());
        input.setApprovalIds(isUserTransfer ? null : transferUserListBO.getTransferProcessInfo().getTransferProcessList());
        input.setApprovalType(approvalType().getCode());
        input.setUserKeyword(transferUserListBO.getKeyword());
        input.setOffset(transferUserListBO.getOffset());
        input.setSize(transferUserListBO.getSize());

        QueryTransferToUserListOutput output = approvalInstanceClient.queryTransferToUserList(input);
        // 构建返回结构
        return buildOrgDeptListResponse(output);
    }

    /**
     * 审批转交
     * @param transferBizContext
     * @param transferScene
     */
    protected TransferResultDTO approvalTransfer(
            TransferBizContext transferBizContext, TransferSceneEnum transferScene) {

        TransferBizContext.TransferUserInfo transferUserInfo = transferBizContext.getTransferUserInfo();
        // 转交主体信息
        UserAccountDetail tenantAccount = transferUserInfo.getTenantAccount();
        // 转交操作人信息
        UserAccountDetail operatorAccount = getTransferOperatorAccount(transferBizContext);
        // 获取转交总数
        Integer approveTransferCount = getApprovalTransferCount(transferBizContext, transferScene);
        // 转交人账户信息
        UserAccountDetail transferAccount = getApprovalTransferAccount(transferBizContext);
        if (null == approveTransferCount || approveTransferCount <= 0) {
            // 表示无数据转交，则默认转交成功
            return new TransferResultDTO(false);
        }

        UserAccountDetail targetAccount = transferUserInfo.getTransferToAccount();
        // 构建非任务中心任务id
        String noTaskCenterTaskId = NO_TASK_ID_PREFIX + String.format("%s_%s", transferBizContext.getTaskId(), transferScene.getCode());
        // 构建任务中心任务id
        String taskCenterTaskId = TASK_CENTER_ID_PREFIX + String.format("%s_%s", transferBizContext.getTaskId(), transferScene.getCode());
        // 构建任务中心任务名称
        String taskName = transferScene.getDescription() + "转交";
        // 部分转交审批id
        List<String> partApprovalIds = partTransferApprovalIds(transferBizContext);
        try {
            // 批量转交场景， 如果审批流程列表只有一个审批流程且未指定走任务中心， 则触发单个同步转交
            if (CollectionUtils.isNotEmpty(partApprovalIds) && approveTransferCount == 1 && !transferBizContext.isUseTaskCenter()) {
                // 设置缓存信息
                setTransferCache(noTaskCenterTaskId, transferBizContext, transferScene, approveTransferCount);
                // 构建转交参数
                ApprovalTransferInput input = ContractApprovalConverter.buildBaseInput(tenantAccount, operatorAccount, targetAccount, transferAccount);
                input.setReason(transferReason(transferBizContext));
                // 设置审批id
                input.setApprovalCode(partApprovalIds.get(0));
                // 使用非任务中心taskId
                input.setBizSerialId(noTaskCenterTaskId);
                ApprovalTransferOutput output = approvalInstanceClient.approvalTransfer(input);
                if (!output.isSuccess()) {
                    throw new BizContractManagerException(TRANSFER_APPROVAL_FAILED, output.getMessage());
                }
                return new TransferResultDTO(false);
            }
            // 批量转交或者基于用户全量转交
            // 设置缓存信息
            setTransferCache(taskCenterTaskId, transferBizContext, transferScene, approveTransferCount);
            // 创建任务中心任务
            createTask(transferUserInfo, taskCenterTaskId, TaskTypeEnum.APPROVAL_TRANSFER, taskName, approveTransferCount);
            // 如果审批流程列表为空， 则表示非指定审批列表转交， 默认基于用户全部转交
            if (CollectionUtils.isEmpty(partApprovalIds)) {
                // 基于用户全部转交
                ApprovalTransferByAccountInput input =
                        ContractApprovalConverter.buildTransferByAccountInput(
                                tenantAccount, operatorAccount, targetAccount, transferAccount);
                input.setApprovalType(approvalType().getCode());
                // 设置任务中心任务id
                input.setBizSerialId(taskCenterTaskId);
                approvalInstanceClient.approvalTransferByAccount(input);
                return new TransferResultDTO(true);
            }
            // 批量转交， 构建转交参数
            ApprovalTransferInput input = ContractApprovalConverter.buildBaseInput(tenantAccount, operatorAccount, targetAccount, transferAccount);
            input.setReason(transferReason(transferBizContext));
            // 发送用印审批转交消息
            for (String approvalId : partApprovalIds) {
                // 设置审批id
                input.setApprovalCode(approvalId);
                // 设置任务中心任务id
                input.setBizSerialId(taskCenterTaskId);
                processApprovalTransferProducer.sendTransferMsg(JsonUtils.obj2json(input));
            }
            return new TransferResultDTO(true);
        } catch (Exception e) {
            log.warn("seal transfer failed:{}", transferBizContext.getTaskId(), e);
            // 发生异常则清除缓存数据
            clearTransferCache(transferBizContext, transferScene);
            throw (e instanceof BizContractManagerException ? (BizContractManagerException) e : new BizContractManagerException(TRANSFER_APPROVAL_FAILED, e.getMessage()));
        }
    }

    /**
     * 审批结果回调处理
     * @param transferResultBO
     */
    protected void approvalTransferResultCallback(SingleTransferResultBO transferResultBO, TransferSceneEnum transferScene) {
        if (null == transferResultBO || StringUtils.isBlank(transferResultBO.getTaskId())) {
            return;
        }
        String taskId = transferResultBO.getTaskId();
        String tenantId = transferResultBO.getTenantId();
        // 目前无论任务成功还是失败，都需要将成功数-1，防止任务无法结束
        String taskProgressKey = userApprovalTransferProgressKey(transferResultBO.getTaskId());
        String taskSuccessKey = userApprovalTransferSuccessKey(transferResultBO.getTaskId());
        // 获取剩余未转交完成的审批数量
        Long taskCount = TedisUtil.string().increment(taskProgressKey, -1);
        // 获取转交成功数量
        Long taskSuccess = TedisUtil.string().increment(taskSuccessKey, transferResultBO.success() ? 1 : 0);
        // 如果当前审批转交成功
        if (transferResultBO.success()) {
            // 添加被转交人转交成功数量
            addTransferToUserCount(tenantId, transferResultBO.getTransferToAccountId());
        }
        if (taskCount > 0) {
            // 说明任务未结束
            return;
        }
        // 任务总数缓存key
        String taskTotalKey = userApprovalTransferTotalKey(transferResultBO.getTaskId());
        // 待删除的缓存key
        List<String> deleteCacheKeys = Lists.newArrayList();
        deleteCacheKeys.add(taskProgressKey);
        deleteCacheKeys.add(taskSuccessKey);
        deleteCacheKeys.add(taskTotalKey);
        // 任务中心场景下， 更新任务状态
        if (checkTaskCenterTaskId(taskId)) {
            // 获取任务总数
            Long taskTotal = TedisUtil.string().increment(taskTotalKey, 0);
            // 计算最终任务状态
            TaskStatusEnum taskStatus = taskSuccess == 0 ? TaskStatusEnum.FAILED : (taskTotal > taskSuccess ? TaskStatusEnum.PARTIAL_FAILED : TaskStatusEnum.FINISH);
            // 生成失败原因
            String failReason = TaskStatusEnum.checkFailed(taskStatus.getType()) ? transferFailReason(taskSuccess, taskTotal) : null;
            // 更新任务状态
            updateTask(taskId, TaskTypeEnum.APPROVAL_TRANSFER, taskStatus, taskSuccess, failReason);
            // 如果任务失败，移除用户是否已转交校验缓存key, 防止客户无法重试转交
            if (TaskStatusEnum.checkFailed(taskStatus.getType())) {
                deleteCacheKeys.add(CacheUtil.userTransferCheckKey(tenantId, transferResultBO.getTransferUser(), transferScene.getCode()));
            }
        }
        // 移除无用缓存key
        TedisUtil.delete(deleteCacheKeys.toArray(new String[]{}));
        // 执行自定义转交完成回调
        customizeTransferCompleteCallback(transferResultBO, taskSuccess);
    }

    /**
     * 获取转交操作人
     * @param transferBizContext
     * @return
     */
    protected UserAccountDetail getTransferOperatorAccount(TransferBizContext transferBizContext) {
        if (transferBizContext.isSystemTransfer()) {
            return transferBizContext.getTransferUserInfo().getTransferToAccount();
        }
        return transferBizContext.getTransferUserInfo().getOperatorAccount();
    }

    /**
     * 获取转交用户
     * @param transferBizContext
     * @return
     */
    protected UserAccountDetail getApprovalTransferAccount(TransferBizContext transferBizContext) {
        if (checkPartTransfer(transferBizContext)) {
            return transferBizContext.getTransferUserInfo().getTransferProcessAccount();
        }
        return transferBizContext.getTransferUserInfo().getTransferAccount().get(0);
    }

    /**
     * 获取用户被转交的审批数
     * @param tenantId
     * @param accountOid
     * @param transferScene
     * @return
     */
    protected Integer transferToUserCount(String tenantId, String accountOid, TransferSceneEnum transferScene) {
        return TedisUtil.get(CacheUtil.transferToUserCountKey(tenantId, accountOid, transferScene.getCode()));
    }

    /**
     * 设置用户被转交的审批数
     * @param tenantId
     * @param accountOid
     * @param transferScene
     * @return
     */
    protected Long addTransferToUserCount(String tenantId, String accountOid, TransferSceneEnum transferScene) {
        try {
            // 设置用户被转交数
            String transferUserCacheKey = CacheUtil.transferToUserCountKey(tenantId, accountOid, transferScene.getCode());
            TedisUtil.expire(transferUserCacheKey, 60, TimeUnit.DAYS);
            return TedisUtil.string().increment(transferUserCacheKey, 1);
        } catch (Exception e) {
            log.warn(
                    "approval addTransferToUserCount failed tenantId:{} accountOid:{}, transferScene: {}",
                    tenantId,
                    accountOid,
                    transferScene.getDescription(),
                    e);
        }
        return 0L;
    }

    /**
     * 判断是否部分转交
     * @param transferBizContext
     * @return
     */
    private boolean checkPartTransfer(TransferBizContext transferBizContext) {
        return CollectionUtils.isNotEmpty(partTransferApprovalIds(transferBizContext));
    }

    /**
     * 设置审批转交缓存
     * @param transferBizContext
     * @param transferScene
     * @param approveTransferCount
     */
    private void setTransferCache(String taskId, TransferBizContext transferBizContext, TransferSceneEnum transferScene, int approveTransferCount) {
        // 2 缓存当前任务总数
        TedisUtil.set(userApprovalTransferTotalKey(taskId), approveTransferCount, 1, TimeUnit.DAYS);
        // 3 缓存当前任务剩余进度
        TedisUtil.set(userApprovalTransferProgressKey(taskId), approveTransferCount, 1, TimeUnit.DAYS);
        // 全部转交打标， 记录是否已经转交过
        if (!checkPartTransfer(transferBizContext)) {
            UserAccountDetail tenantAccount = transferBizContext.getTransferUserInfo().getTenantAccount();
            UserAccountDetail transferAccount = getApprovalTransferAccount(transferBizContext);
            int timeout = ConfigService.getAppConfig().getIntProperty(PROP_APPROVAL_USER_TRANSFERRED_CACHE_TIMEOUT, 6);
            setUserIsTransferred(tenantAccount.getAccountOid(), transferAccount.getAccountOid(), transferScene, timeout, TimeUnit.HOURS);
        }
        // 业务自定义缓存处理
        setCustomizeTransferCache(transferBizContext);
    }

    /**
     * 清理审批转交缓存
     * @param transferBizContext
     * @param transferScene
     */
    private void clearTransferCache(TransferBizContext transferBizContext, TransferSceneEnum transferScene) {
        String taskId = String.format("%s:%s", transferBizContext.getTaskId(), transferScene.getCode());
        // 2 清除缓存当前任务总数
        CacheUtil.degradeDelete(userApprovalTransferTotalKey(taskId));
        // 3 清除缓存当前任务剩余进度
        CacheUtil.degradeDelete(userApprovalTransferProgressKey(taskId));
        // 业务自定义缓存清除处理
        clearCustomizeTransferCache(transferBizContext);
    }

    /**
     * 获取待转交的审批数
     * @param transferBizContext
     * @param transferScene
     * @return
     */
    private int getApprovalTransferCount(TransferBizContext transferBizContext, TransferSceneEnum transferScene) {
        UserAccountDetail tenantAccount = transferBizContext.getTransferUserInfo().getTenantAccount();
        List<String> partApprovalIds = partTransferApprovalIds(transferBizContext);
        if (CollectionUtils.isNotEmpty(partApprovalIds)) {
            return partApprovalIds.size();
        }
        UserAccountDetail transferAccount = getApprovalTransferAccount(transferBizContext);
        // 判断用户是否转交过
        checkUserIsTransferred(tenantAccount.getAccountOid(), transferAccount.getAccountOid(), transferScene);
        // 获取待转交审批数
        QueryApprovalPendingCountInput input = new QueryApprovalPendingCountInput();
        if (StringUtils.isNotBlank(transferAccount.getAccountGid())) {
            input.setOrgOid(tenantAccount.getAccountOid());
            input.setApproverGid(transferAccount.getAccountGid());
            input.setApprovalType(approvalType().getCode());
            QueryApprovalPendingCountOutput output = approvalInstanceClient.queryApprovalPendingCount(input);
            return output.getCommonNodeCount() + output.getSealRoleNodeCount();
        }
        return 0;
    }

    /**
     * 构建返回结构
     * @param output
     * @return
     */
    private OrgDeptListResponse buildOrgDeptListResponse(QueryTransferToUserListOutput output) {
        OrgDeptListResponse orgDeptListResponse = new OrgDeptListResponse();
        orgDeptListResponse.setMemberCount(output.getTotal());
        orgDeptListResponse.setMemberList(Lists.newArrayList());
        if (CollectionUtils.isEmpty(output.getUserList())) {
            return orgDeptListResponse;
        }
        output.getUserList().forEach(user -> {
            OrgMemberBaseDTO member = new OrgMemberBaseDTO();
            member.setMemberOid(user.getUserOid());
            member.setMemberAccountUid(user.getUserAccountUid());
            member.setMemberGid(user.getUserGid());
            member.setMemberName(user.getUserName());
            member.setRealnameStatus(RealnameStatus.valueOf(user.getRealnameStatus()));
            member.setActive(user.isActive());
            member.setProperties(user.getProperties());
            member.setMobile(user.getMobile());
            member.setEmail(user.getEmail());
            member.setRoleName(user.getRoleName());
            member.setDepartDetails(Lists.newArrayList());
            member.setRoleModels(Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(user.getDepartDetails())) {
                user.getDepartDetails().forEach(dept -> {
                    MemberDeptDetailModel deptModel = new MemberDeptDetailModel();
                    deptModel.setDeptId(dept.getDeptId());
                    deptModel.setDeptName(dept.getDeptName());
                    deptModel.setMainMember(dept.isMainMember());
                    member.getDepartDetails().add(deptModel);
                });
            }
            if (CollectionUtils.isNotEmpty(user.getRoleModels())) {
                user.getRoleModels().forEach(role -> {
                    MemberRoleModel roleModel = new MemberRoleModel();
                    roleModel.setId(role.getId());
                    roleModel.setRoleKey(role.getRoleKey());
                    roleModel.setName(role.getName());
                    roleModel.setDisc(role.getDisc());
                    roleModel.setParentRoleId(role.getParentRoleId());
                    roleModel.setOrganId(role.getOrganId());
                    roleModel.setCreateTime(role.getCreateTime());
                    member.getRoleModels().add(roleModel);
                });
            }
            orgDeptListResponse.getMemberList().add(member);
        });
        return orgDeptListResponse;
    }
}

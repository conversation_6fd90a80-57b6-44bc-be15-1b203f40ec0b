package com.timevale.contractmanager.core.service.watermark.bean;

import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkContentTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 校验水印权限的业务入参模型
 * @date Date : 2023年05月11日 17:06
 */
@Data
public class GenerateWatermarkSnapShootModel {

    /**
     * 流程ID
     */
    String processId;

    /**
     * 合同文件
     */
    private List<FileBO> files;

    /**
     * 使用的水印ID
     */
    String watermarkId;


    /**
     * 水印类型
     */
    WatermarkTypeEnum watermarkType;

    /**
     * 水印内容类型
     */
    WatermarkContentTypeEnum watermarkContentType;

    /**
     * 发起主体
     */
    String tenantId;

    /**
     * 发起操作人
     */
    String operatorId;

}

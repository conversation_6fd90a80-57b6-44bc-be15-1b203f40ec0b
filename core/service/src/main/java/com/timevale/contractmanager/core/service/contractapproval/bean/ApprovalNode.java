package com.timevale.contractmanager.core.service.contractapproval.bean;

import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/** 审批节点信息 */
@Data
public class ApprovalNode extends ToString {

    @ApiModelProperty("审批节点id")
    private String nodeId;

    /** @see com.timevale.contractapproval.facade.enums.ApprovalTemplateNodeTypeEnum */
    @ApiModelProperty("审批节点类型")
    private String nodeType;

    @ApiModelProperty("审批任务列表")
    private List<ApprovalNodeTask> tasks;

    @ApiModelProperty("审批节点顺序")
    private Integer order;


    /** 审批任务信息 */
    @Data
    public static class ApprovalNodeTask extends ToString {

        @ApiModelProperty("审批任务状态")
        private String taskId;

        @ApiModelProperty("审批任务状态")
        private Integer status;

        @ApiModelProperty("审批人列表")
        private List<ApprovalTaskUser> users;
    }

    /** 审批人信息 */
    @Data
    public static class ApprovalTaskUser {

        @ApiModelProperty("审批人oid")
        private String accountId;

        @ApiModelProperty("审批人gid")
        private String accountGid;

        @ApiModelProperty("审批人姓名")
        private String accountName;

        /**
         * 校验是否审批人
         *
         * @param accountId
         * @param accountGid
         * @return
         */
        public boolean sameUser(String accountId, String accountGid) {
            if (StringUtils.isAllBlank(accountId, accountGid)) {
                return false;
            }
            if (StringUtils.isNoneBlank(this.accountId, accountId)
                    && StringUtils.equals(this.accountId, accountId)) {
                return true;
            }
            return StringUtils.isNoneBlank(this.accountGid, accountGid)
                    && StringUtils.equals(this.accountGid, accountGid);
        }
    }
}

package com.timevale.contractmanager.core.service.sharedownload;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.dal.bean.*;
import com.timevale.contractmanager.common.dal.dao.ProcessShareDownloadConfigDAO;
import com.timevale.contractmanager.common.dal.dao.ProcessShareDownloadLogDAO;
import com.timevale.contractmanager.common.dal.dao.ProcessShareDownloadSubjectConfigDAO;
import com.timevale.contractmanager.common.service.bean.ProcessFileAuthSimpleBean;
import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.ProcessAuthFileTypeEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.enums.SubProcessTypeEnum;
import com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum;
import com.timevale.contractmanager.common.service.enums.sharedownload.ProcessShareDownloadAccessStatusEnum;
import com.timevale.contractmanager.common.service.enums.sharedownload.ProcessShareDownloadOperatingTypeEnum;
import com.timevale.contractmanager.common.service.enums.ProcessSecretEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.GrayConfigManageClient;
import com.timevale.contractmanager.common.service.integration.client.PdfServiceClient;
import com.timevale.contractmanager.common.service.integration.client.SignClient;
import com.timevale.contractmanager.common.service.model.AccountSimpleModel;
import com.timevale.contractmanager.common.service.model.QueryProcessFileAuthModel;
import com.timevale.contractmanager.common.service.result.ProcessFileAuthResult;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.common.utils.config.Constants;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadPreviewBO;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadProcessBO;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadProcessSignAndVerifyBO;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadShareInfoBO;
import com.timevale.contractmanager.core.model.dto.response.ProcessSignInfoResponse;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.FlowDocumentVO;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.ShareDownloadShareProcessInfoResponse;
import com.timevale.contractmanager.core.model.dto.sharedownload.FlowDocumentCacheDTO;
import com.timevale.contractmanager.core.model.dto.sharedownload.FlowDocumentDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.component.ShareDownloadConvert;
import com.timevale.contractmanager.core.service.grouping.PermissionService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.ProcessPermissionService;
import com.timevale.contractmanager.core.service.process.ProcessService;
import com.timevale.contractmanager.core.service.process.privilege.ProcessPrivilegeCheckerFactory;
import com.timevale.contractmanager.core.service.process.privilege.bean.ProcessAccountPrivilege;
import com.timevale.contractmanager.core.service.process.privilege.bean.ProcessPrivilegeBuilder;
import com.timevale.contractmanager.core.service.process.privilege.enums.ProcessPrivilegeEnum;
import com.timevale.contractmanager.core.service.process.privilege.enums.ProcessSubjectRangeEnum;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.contractmanager.core.service.util.SignUtil;
import com.timevale.footstone.rpc.model.flowmodel.bean.FlowDoc;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.pdf.common.service.model.PdfToImageResult;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;

/**
 * e签章分享下载相关功能
 *
 * @since e签章一期
 * <AUTHOR>
 * @since 2022/2/7
 */
@Service
@Slf4j
public class ShareDownloadServiceImpl implements ShareDownloadService {

    @Autowired private PermissionService permissionService;

    @Autowired private SystemConfig systemConfig;

    @Autowired private ProcessShareDownloadSubjectConfigDAO processShareDownloadSubjectConfigDAO;

    @Autowired private ProcessShareDownloadConfigDAO processShareDownloadConfigDAO;

    @Autowired private ProcessShareDownloadLogDAO processShareDownloadLogDAO;

    @Autowired private BaseProcessService baseProcessService;

    @Autowired private SignClient signClient;

    @Autowired private PdfServiceClient pdfServiceClient;

    @Autowired private UserCenterService userCenterService;

    @Autowired private ProcessPermissionService processPermissionService;

    @Resource private ProcessPrivilegeCheckerFactory privilegeCheckerFactory;

    @Resource private GrayConfigManageClient grayConfigManageClient;

    @Autowired private ProcessService processService;

    private static final String DOC_FILE_URL_CACHE_PREFIX = "DOC_FILE_URL_";

    private static final String VERIFY_CACHE_PREFIX = "VERIFY_CACHE_";

    @Autowired
    private ContractProcessReadClient contractProcessReadClient;

    @Override
    public ShareDownloadShareInfoBO getShareDownloadInfo(
            String processId, String operatorOid, String operatingSubjectOid) {
        ProcessDO processDO = baseProcessService.getProcess(processId);
        if (processDO == null) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }
        if (!Objects.equals(ProcessStatusEnum.DONE.getStatus(), processDO.getStatus())) {
            // 合同不存在或者不为签署完成，不使用新版分享
            return ShareDownloadShareInfoBO.newShareDownloadDisabled();
        }
        if (StringUtils.isBlank(processDO.getSubjectGid())) {
            // 发起主体未实名
            throw new BizContractManagerException(SHARE_DOWNLOAD_INITIATOR_SUBJECT__NO_REAL_NAME);
        }
        // 分享下载合同
        ShareDownloadProcessBO processBO =
                new ShareDownloadProcessBO(
                        processId,
                        processDO.getSubjectOid(),
                        processDO.getSubjectGid(),
                        operatorOid,
                        operatingSubjectOid,
                        ProcessShareDownloadOperatingTypeEnum.PROCESS_SHARE);
        // 获取分享的权限
        ShareDownloadShareInfoBO shareInfoBO =
                shareDownloadPrivilege(
                        processDO.getSubjectOid(),
                        processDO.getSubjectGid(),
                        processBO.getOperatingSubjectOid(),
                        operatorOid);

        if (!shareInfoBO.getIsNewShareDownload()) {
            // 不使用新版分享
            return ShareDownloadShareInfoBO.newShareDownloadDisabled();
        }
        wrapNewShareDownloadUrlAndId(processBO, shareInfoBO);
        // 访问记录
        recordUserAccessLogIgnoreError(shareInfoBO.getNewShareDownloadId(), processBO);
        return shareInfoBO;
    }

    @Override
    public ShareDownloadShareProcessInfoResponse getShareDownloadProcessFiles(
            String shareDownloadId, String operatorOid, String sign) {
        ShareDownloadProcessSignAndVerifyBO signAndVerifyBO =
                generateShareDownloadProcessSignAndVerifyBO(
                        sign,
                        shareDownloadId,
                        operatorOid,
                        ProcessShareDownloadOperatingTypeEnum.PROCESS_VIEW);
        // 签名验签 & 分享下载合同查看规则校验
        verifyAndCheckIsAllowView(signAndVerifyBO);
        // 缓存检验成功的参数
        cacheSuccessVerify(sign, shareDownloadId);
        // 访问记录
        recordUserAccessLogIgnoreError(shareDownloadId, signAndVerifyBO.getProcessBO());
        //获取签署文件
        SubProcessDO subProcessDO =
                baseProcessService.getSubProcess(signAndVerifyBO.getProcessBO().getProcessId(), SubProcessTypeEnum.SIGN);
        if (subProcessDO == null || StringUtils.isBlank(subProcessDO.getSubProcessId())) {
            throw new BizContractManagerException(SUB_PROCESS_NOT_EXIST);
        }
        List<FlowDocumentDTO> files = buildContractFile(subProcessDO.getSubProcessId(),false);
        if (CollectionUtils.isEmpty(files)) {
            return new ShareDownloadShareProcessInfoResponse(Lists.newArrayList(), null);
        }
        // 缓存文件地址
        cacheFlowDocumentsFileUrl(shareDownloadId, files);
        // 增加合同访问次数
        addAccessCount(signAndVerifyBO.getProcessConfigDO());
        ProcessShareDownloadLogDO shareLog =
                processShareDownloadLogDAO
                        .getByShareDownloadId(
                                shareDownloadId,
                                ProcessShareDownloadOperatingTypeEnum.PROCESS_SHARE.getCode())
                        .stream()
                        .findAny()
                        .orElse(null);
        return new ShareDownloadShareProcessInfoResponse(
                files.stream()
                        .map(ShareDownloadConvert::convert2FlowDocumentVO)
                        .collect(Collectors.toList()),
                shareLog == null ? null : shareLog.getSubjectOid());
    }

    @Override
    public FlowDocumentCacheDTO getShareDownloadProcessFileUrl(
            String shareDownloadId, String operatorOid, String sign, String fileKey) {

        ShareDownloadProcessSignAndVerifyBO signAndVerifyBO =
                generateShareDownloadProcessSignAndVerifyBO(
                        sign,
                        shareDownloadId,
                        operatorOid,
                        ProcessShareDownloadOperatingTypeEnum.PROCESS_DOWNLOAD);
        checkWithCacheVerify(sign, shareDownloadId);
        // 访问记录
        recordUserAccessLogIgnoreError(shareDownloadId, signAndVerifyBO.getProcessBO());
        return getFileDocFromCache(shareDownloadId, fileKey);
    }

    @Override
    public ShareDownloadPreviewBO.PdfPageImagesBO shareDownloadProcessFilePreview(
            String shareDownloadId,
            String operatorOid,
            String sign,
            ShareDownloadPreviewBO previewBO) {
        checkWithCacheVerify(sign, shareDownloadId);
        PdfToImageResult pdfToImageResult =
                pdfServiceClient.pdf2ImageByConvertWay(
                        previewBO.getFileKey(),
                        previewBO.getPageNum(),
                        previewBO.getPdf2ImageType(),
                        RequestContextExtUtils.getAppId(),
                        previewBO.getConvertWay());
        return ShareDownloadPreviewBO.PdfPageImagesBO.instanceWithPdfToImageResult(
                pdfToImageResult);
    }

    @Override
    public ProcessSignInfoResponse processSignInfo(
            String operatorOid, String subjectId, String processId) {
        // 获取流程用户权限鉴权主体范围
        ProcessSubjectRangeEnum subjectRange =
                StringUtils.equals(operatorOid, subjectId)
                        ? ProcessSubjectRangeEnum.PARTICIPANT
                        : ProcessSubjectRangeEnum.CURRENT;
        // 组装用户流程权限鉴权参数对象
        ProcessPrivilegeBuilder privilegeBuilder =
                new ProcessPrivilegeBuilder(processId, operatorOid, subjectId, subjectRange);
        // 获取用户流程查看权限
        ProcessAccountPrivilege processViewPrivilege =
                getProcessViewPrivilege(processId, operatorOid, privilegeBuilder);
        // 获取当前用户的保密状态
        boolean isSecret = processViewPrivilege.hasPrivilege(ProcessPrivilegeEnum.IS_SECRET.getType());
        // 1 当前用户是否有查看权限
        // 当用户无权限查看:  则提示无权限查看
        if (!processViewPrivilege.hasPrivilege(ProcessPrivilegeEnum.VIEW.getType())) {
            throw new BizContractManagerException(NO_QUERY_PERMISSION);
        }

        ContractProcessDTO contractProcessDTO = contractProcessReadClient.getByProcessId(processId);
        if (null == contractProcessDTO) {
            throw new BizContractManagerException(PROCESS_NOT_EXIST);
        }

        ProcessSignInfoResponse response = new ProcessSignInfoResponse();

        // 2 签署/附件文件信息
        buildFlowDocumentList(processId, response);
        // 3 当前合同是否对用户保密  流程参与人可见， 设置的可见人可见
        if (isSecret) {
            response.setSecretType(ProcessSecretEnum.ALL_SECRET.getType());
            return response;
        }
        // 4 检验下载权限: 流程已经完成并且有下载权限
        ProcessAccountPrivilege downloadPrivilege =
                privilegeCheckerFactory.checkAccountPrivilege(
                        privilegeBuilder, ProcessPrivilegeEnum.DOWNLOAD);
        response.setCanDownload(
                downloadPrivilege.hasPrivilege(ProcessPrivilegeEnum.DOWNLOAD.getType()));
        response.setSecretType(ProcessSecretEnum.NONE_SECRET.getType());
        response.setDedicatedCloudId(contractProcessDTO.getDedicatedCloudId());
        UserAccount account = userCenterService.getUserAccountBaseByOid(operatorOid);
        UserAccount subject = userCenterService.getUserAccountBaseByOid(subjectId);
        fileAuthFilter(response, processId, subject, account);
        attachmentAuthFilter(response, processId, subject, account, contractProcessDTO.getInitiator());
        return response;
    }

    private void fileAuthFilter(ProcessSignInfoResponse response, String processId, UserAccount subjectAccount, UserAccount operatorAccount){
        if(CollectionUtils.isEmpty(response.getContractFiles())){
            return;
        }
        ProcessFileAuthResult fileAuthResult = getProcessFileAuthResult(processId, subjectAccount, operatorAccount, ProcessAuthFileTypeEnum.SIGN.getType());
        if (ProcessSecretEnum.NONE_SECRET.getType().equals(fileAuthResult.getVisibleType())) {
            return;
        }
        if (ProcessSecretEnum.ALL_SECRET.getType().equals(fileAuthResult.getVisibleType())) {
            if (CollectionUtils.isEmpty(fileAuthResult.getFiles())) {
                response.setContractFiles(new ArrayList<>());
            } else {
                List<String> fileIdList = fileAuthResult.getFiles().stream().map(ProcessFileAuthSimpleBean::getFileId).collect(Collectors.toList());
                response.getContractFiles().removeIf(t -> !fileIdList.contains(t.getFileId()));
            }
        } else {
            List<String> fileIdList = fileAuthResult.getFiles().stream().map(ProcessFileAuthSimpleBean::getFileId).collect(Collectors.toList());
            response.getContractFiles().removeIf(t -> !fileIdList.contains(t.getFileId()));
        }
    }

    private void attachmentAuthFilter(ProcessSignInfoResponse response, String processId, UserAccount subjectAccount, UserAccount operatorAccount, ProcessAccount initiator){

        if(CollectionUtils.isEmpty(response.getAttachmentFiles())){
            return;
        }
        //发起方默认都可见
        if ((StringUtils.isNoneBlank(initiator.getSubject().getOid(), subjectAccount.getAccountOid()) && initiator.getSubject().getOid().equals(subjectAccount.getAccountOid()))
                || (StringUtils.isNoneBlank(initiator.getSubject().getGid(), subjectAccount.getAccountGid()) && initiator.getSubject().getGid().equals(subjectAccount.getAccountGid()))) {
            return;
        }
        ProcessFileAuthResult fileAuthResult = getProcessFileAuthResult(processId, subjectAccount, operatorAccount, ProcessAuthFileTypeEnum.ATTACHMENT.getType());
        if (ProcessSecretEnum.NONE_SECRET.getType().equals(fileAuthResult.getVisibleType())) {
            return;
        }
        List<String> canSeeFileId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fileAuthResult.getFiles())) {
            List<String> fileIdList = fileAuthResult.getFiles().stream().map(ProcessFileAuthSimpleBean::getFileId).collect(Collectors.toList());
            canSeeFileId.addAll(fileIdList);
        }
        response.getAttachmentFiles().removeIf(t -> !canSeeFileId.contains(t.getFileId()));
    }

    private ProcessFileAuthResult getProcessFileAuthResult(String processId, UserAccount subjectAccount, UserAccount operatorAccount, String type) {

        List<AccountSimpleModel> subjectList = new ArrayList<>();
        AccountSimpleModel subjectModel = new AccountSimpleModel();
        subjectModel.setOid(subjectAccount.getAccountOid());
        subjectModel.setGid(subjectAccount.getAccountGid());
        subjectList.add(subjectModel);

        QueryProcessFileAuthModel model = new QueryProcessFileAuthModel();
        model.setProcessId(processId);
        AccountSimpleModel operatorModel = new AccountSimpleModel();
        operatorModel.setOid(operatorAccount.getAccountOid());
        operatorModel.setGid(operatorAccount.getAccountGid());
        model.setAccount(operatorModel);
        model.setFileType(type);
        model.setSubjectList(subjectList);
        model.setContainSecret(false);
        return processService.queryProcessFileAuth(model);
    }

    @Override
    public ShareDownloadPreviewBO.PdfPageImagesBO processViewImage(
            String operatorOid,
            String subjectId,
            String processId,
            ShareDownloadPreviewBO previewBO) {
        String fileKey = previewBO.getFileKey();
        // 执行文件查看鉴权并缓存结果
        boolean hasPrivilege =
                CacheUtil.doActionWithObjCache(
                        () -> checkViewImagePrivilege(operatorOid, subjectId, processId, previewBO),
                        CacheUtil.getViewImagePrivilegeKey(fileKey, operatorOid, processId),
                        systemConfig.getProcessViewImagePrivilegeCacheSeconds(),
                        TimeUnit.SECONDS);
        if (!hasPrivilege) {
            throw new BizContractManagerException(NO_QUERY_PERMISSION);
        }
        // 执行pdf转图片
        PdfToImageResult pdfToImageResult =
                pdfServiceClient.pdf2ImageByConvertWay(
                        previewBO.getFileKey(),
                        previewBO.getPageNum(),
                        previewBO.getPdf2ImageType(),
                        RequestContextExtUtils.getAppId(),
                        previewBO.getConvertWay());
        return ShareDownloadPreviewBO.PdfPageImagesBO.instanceWithPdfToImageResult(
                pdfToImageResult);
    }

    /**
     * 校验合同查看权限
     *
     * @param operatorOid
     * @param subjectId
     * @param processId
     * @param previewBO
     */
    private boolean checkViewImagePrivilege(
            String operatorOid,
            String subjectId,
            String processId,
            ShareDownloadPreviewBO previewBO) {
        // 获取流程用户权限鉴权主体范围
        ProcessSubjectRangeEnum subjectRange =
                StringUtils.equals(operatorOid, subjectId)
                        ? ProcessSubjectRangeEnum.PARTICIPANT
                        : ProcessSubjectRangeEnum.CURRENT;
        // 组装用户流程权限鉴权参数对象
        ProcessPrivilegeBuilder privilegeBuilder =
                new ProcessPrivilegeBuilder(processId, operatorOid, subjectId, subjectRange);
        // 获取用户流程查看权限
        ProcessAccountPrivilege processAccountPrivilege =
                getProcessViewPrivilege(processId, operatorOid, privilegeBuilder);
        // 1 当前用户无查看权限
        if (Boolean.FALSE.equals(
                processAccountPrivilege.hasPrivilege(ProcessPrivilegeEnum.VIEW.getType()))) {
            throw new BizContractManagerException(NO_QUERY_PERMISSION);
        }
        // 2 当前合同是否对用户保密
        if (Boolean.TRUE.equals(
                processAccountPrivilege.hasPrivilege(ProcessPrivilegeEnum.IS_SECRET.getType()))) {
            throw new BizContractManagerException(CONTRACT_IS_SECRET_ERROR);
        }
        SubProcessDO subProcessDO =
                baseProcessService.getSubProcess(processId, SubProcessTypeEnum.SIGN);
        if (subProcessDO == null || StringUtils.isBlank(subProcessDO.getSubProcessId())) {
            return false;
        }
        // 3 附件逻辑判断
        List<FlowDocumentDTO> contractFiles =
                buildContractFile(subProcessDO.getSubProcessId(), true);
        if (CollectionUtils.isEmpty(contractFiles)
                || contractFiles.stream()
                        .noneMatch(e -> e.getFileKey().equals(previewBO.getFileKey()))) {
            return false;
        }
        return true;
    }

    /**
     * 获取用户流程查看权限， 优先获取缓存数据
     *
     * @param processId
     * @param accountId
     * @return
     */
    private ProcessAccountPrivilege getProcessViewPrivilege(
            String processId, String accountId, ProcessPrivilegeBuilder builder) {
        String cacheKey = CacheUtil.getViewProcessPrivilegeKey(accountId, processId);
        Integer timeout = systemConfig.getProcessViewPrivilegeCacheSeconds();
        return privilegeCheckerFactory.getAccountPrivilegeWithCache(
                builder, ProcessPrivilegeEnum.VIEW, cacheKey, timeout);
    }

    /**
     * 获取文件签署/附件信息
     *
     * @param processId 主流程id
     */
    private void buildFlowDocumentList(String processId, ProcessSignInfoResponse response) {
        SubProcessDO subProcessDO =
                baseProcessService.getSubProcess(processId, SubProcessTypeEnum.SIGN);
        if (subProcessDO == null || StringUtils.isBlank(subProcessDO.getSubProcessId())) {
            throw new BizContractManagerException(SUB_PROCESS_NOT_EXIST);
        }
        List<FlowDocumentVO> contractFiles = new ArrayList<>();
        List<FlowDocumentVO> attachmentFiles = new ArrayList<>();
        List<FlowDocumentDTO> flowDocumentList =
                buildContractFile(subProcessDO.getSubProcessId(), true);
        if (CollectionUtils.isEmpty(flowDocumentList)) {
            throw new BizContractManagerException(PROCESS_FILE_NOT_EXIST);
        }
        flowDocumentList.stream()
                .forEach(
                        e -> {
                            FlowDocumentVO flowDocumentVO = new FlowDocumentVO();
                            flowDocumentVO.setFileKey(e.getFileKey());
                            flowDocumentVO.setFileId(e.getFileId());
                            flowDocumentVO.setFileName(e.getFileName());
                            flowDocumentVO.setFileUrl(e.getFileUrl());
                            if (ProcessFileType.isContract(e.getFileType())) {
                                contractFiles.add(flowDocumentVO);
                            } else {
                                attachmentFiles.add(flowDocumentVO);
                            }
                        });
        response.setContractFiles(contractFiles);
        response.setAttachmentFiles(attachmentFiles);
        response.setSignFlowId(subProcessDO.getSubProcessId());
    }

    /**
     * 获取合同签署文件/附件信息
     *
     * @param signFlowId 签署流程id
     * @param needAttachment 是否需要附件信息
     * @return 返回文件信息
     */
    private List<FlowDocumentDTO> buildContractFile(String signFlowId, boolean needAttachment) {
        List<FlowDocumentDTO> files = new ArrayList<>();
        List<FlowDoc> flowDocs =
                signClient.getBatchFlowDocs(
                        Collections.singletonList(signFlowId), true, true, needAttachment);
        if (CollectionUtils.isEmpty(flowDocs)
                || CollectionUtils.isEmpty(flowDocs.get(0).getFileInfoBeans())) {
            return files;
        }
        FlowDoc flowDoc = flowDocs.get(0);
        files.addAll(
                flowDoc.getFileInfoBeans().stream()
                        .map(
                                e ->
                                        ShareDownloadConvert.convert2FlowDocumentDTO(
                                                e, ProcessFileType.CONTRACT_FILE.getType()))
                        .collect(Collectors.toList()));
        if (!needAttachment || CollectionUtils.isEmpty(flowDoc.getAttachmentBeans())) {
            return files;
        }
        files.addAll(
                flowDoc.getAttachmentBeans().stream()
                        .map(
                                e ->
                                        ShareDownloadConvert.convert2FlowDocumentDTO(
                                                e, ProcessFileType.ATTACHMENT_FILE.getType()))
                        .collect(Collectors.toList()));
        return files;
    }

    /*
     * 合同是否允许分享的权限获取
     * @param subjectOid
     * @param subjectGid
     * @param operatorOid
     * @return
     */
    private ShareDownloadShareInfoBO shareDownloadPrivilege(
            String subjectOid, String subjectGid, String tenantId, String operatorOid) {
        // 校验是否禁用新分享下载功能, 如果禁用， 直接返回false
        if (checkNewShareDisabled(subjectOid, subjectGid)) {
            log.info("当前主体禁用新版分享 主体id: {}", subjectOid);
            return new ShareDownloadShareInfoBO(false);
        }
        //合同偏好设置是否设置可下载
         boolean downloadPrivilege = processPermissionService.hasShareDownloadPermission(subjectOid);

        if (downloadPrivilege) {
            return new ShareDownloadShareInfoBO(true, true);
        }
        UserAccount account = userCenterService.getUserAccountBaseByOid(tenantId);
        if (account == null || account.isPerson()) {
            // 如果不存在或者是个人主体，用户中心的权限肯定就是false，新版下载也不能启用
            return new ShareDownloadShareInfoBO(false, false);
        }
        // 用户中心下载权限是否存在
        downloadPrivilege = isUserCenterDownloadPrivilegeExist(tenantId, operatorOid);
		if(downloadPrivilege){
            return new ShareDownloadShareInfoBO(true, true);
		}
        return new ShareDownloadShareInfoBO(true, false);
	}

    /**
     * 校验是否禁用新分享下载功能
     *
     * @param subjectOid
     * @param subjectGid
     * @return
     */
    private boolean checkNewShareDisabled(String subjectOid, String subjectGid) {
        // 判断主体Gid是否为空, 如果为空，默认不禁用
        if (StringUtils.isBlank(subjectGid)) {
            log.warn("当前主体缺少GID, 主体id: {}", subjectOid);
            return false;
        }
        // 判断是否在灰度中，如果在，则禁用， 否则不禁用
        return grayConfigManageClient.isInFunctionGrayGid(
                subjectGid, Constants.GRAY_FUNCTION_NEW_SHARE_DISABLED);
    }

    /*
     * 验签并校验是否能够查看
     * @param signAndVerifyBO
     */
    private void verifyAndCheckIsAllowView(ShareDownloadProcessSignAndVerifyBO signAndVerifyBO) {
        // 签名验签
        signVerify(
                signAndVerifyBO.getSign(),
                signAndVerifyBO.getProcessConfigDO().getShareDownloadId(),
                signAndVerifyBO.getProcessConfigDO().getSalt());
        // 分享下载合同查看规则校验
        checkIsAllowViewShareDownloadProcess(
                signAndVerifyBO.getProcessBO().getSubjectGid(),
                signAndVerifyBO.getProcessConfigDO().getShareDownloadId());
    }

    private void cacheSuccessVerify(String sign, String shareDownloadId) {
        String verifyKey = shareDownloadId + sign.hashCode();
        TedisUtil.set(VERIFY_CACHE_PREFIX + verifyKey, true, 10, TimeUnit.MINUTES);
    }

    private void checkWithCacheVerify(String sign, String shareDownloadId) {
        String verifyKey = shareDownloadId + sign.hashCode();
        Boolean result = TedisUtil.get(VERIFY_CACHE_PREFIX + verifyKey);
        if (BooleanUtils.isFalse(result)) {
            throw new BizContractManagerException(SHARE_DOWNLOAD_FILE_INFO_EXPIRE);
        }
    }

    /*校验分享下载的合同是否能查看
     * @param subjectGid 发起方的gid
     * @param processId 合同id
     */
    private void checkIsAllowViewShareDownloadProcess(String subjectGid, String shareDownloadId) {
        Boolean isClose = systemConfig.getIsProcessShareDownloadClose();
        if (isClose != null && isClose) {
            // 分享下载功能已关闭
            throw new BizContractManagerException(SHARE_DOWNLOAD_CLOSE);
        }
        ProcessShareDownloadSubjectConfigDO subjectConfigDO =
                processShareDownloadSubjectConfigDAO.getBySubjectGid(subjectGid);
        if (subjectConfigDO != null
                && ProcessShareDownloadAccessStatusEnum.ACCESS_FORBID
                        .getCode()
                        .equals(subjectConfigDO.getAccessStatus())) {
            log.info(
                    "发起方已经关闭了下载查看权限 subjectGid {} shareDownloadId {} ",
                    subjectGid,
                    shareDownloadId);
            // 发起方关闭了分享下载的查看权限
            throw new BizContractManagerException(SHARE_DOWNLOAD_CLOSE);
        }
        ProcessShareDownloadConfigDO processConfigDO =
                processShareDownloadConfigDAO.getByShareDownloadId(shareDownloadId);
        if (isAccessCountLimit(processConfigDO)) {
            log.info("当前文件访问已达上限次数 subjectGid {} shareDownloadId {} ", subjectGid, shareDownloadId);
            throw new BizContractManagerException(SHARE_DOWNLOAD_ACCESS_LIMIT);
        }
    }

    /** 记录用户的操作日志，忽略错误，替换用户的主体为发起方主体 */
    private void recordUserAccessLogIgnoreError(
            String shareDownloadId, ShareDownloadProcessBO processBO) {
        try {
            if(StringUtils.isBlank(shareDownloadId)){
                return; //shareDownloadId为空，说明不是新分享的操作，不用记录日志
            }
            if (StringUtils.isNotBlank(processBO.getOperatingSubjectOid())) {
                UserAccount userAccount =
                        userCenterService.getUserAccountBaseByOid(
                                processBO.getOperatingSubjectOid());
                if (userAccount != null) {
                    processBO.setOperatingSubjectGid(userAccount.getAccountGid());
                }
            }
            ProcessShareDownloadLogDO processShareDownloadLogDO =
                    ShareDownloadConvert.convert2InitProcessShareDownloadLogDO(
                            shareDownloadId, processBO);
            processShareDownloadLogDAO.insert(processShareDownloadLogDO);
        } catch (Exception e) {
            log.error("分享下载用户访问日志异常", e);
        }
    }

    /*
     * 用户中心主体下的下载权限是否存在
     * @param subjectOid
     * @param operatorOid
     * @return
     * todo
     */
    private boolean isUserCenterDownloadPrivilegeExist(String subjectOid, String operatorOid) {
        return permissionService.checkGlobalPermission(
                subjectOid,
                operatorOid,
                PrivilegeResourceEnum.CONTRACT.getType(),
                PrivilegeOperationEnum.DOWNLOAD.getType());
	}

    /**
     * 校验当前合同的分享下载是否达到访问上限
     *
     * @param configDO
     * @return
     */
    private boolean isAccessCountLimit(ProcessShareDownloadConfigDO configDO) {
        if (configDO == null
                || configDO.getAccessCount() == null
                || configDO.getAccessLimit() == null) {
            return false;
        }
        return configDO.getAccessCount() >= configDO.getAccessLimit();
    }

    /*
     * 包装新分享地址及对应的分享id
     * @param processBO
     * @param shareInfoBO
     */
    private void wrapNewShareDownloadUrlAndId(
            ShareDownloadProcessBO processBO, ShareDownloadShareInfoBO shareInfoBO) {
        if (BooleanUtils.isFalse(shareInfoBO.getDownloadPrivilegeExist())) {
            // 下载权限不存在，就没有分享权限
            return;
        }
        String shareDownloadId = UUIDUtil.genUUIDWithPrefix(UUIDUtil.PROCESS_SHARE_DOWNLOAD_PREFIX);
        int accessLimit = getOrInitShareDownloadSubjectConfig(processBO);
        String salt = UUIDUtil.genUUID().substring(24);
        String shareDownloadUrl =
                generateShareDownloadUrl(processBO.getProcessId(), salt, shareDownloadId);
        shareInfoBO.setSalt(salt);
        shareInfoBO.setAccessLimit(accessLimit);
        shareInfoBO.setNewShareDownloadId(shareDownloadId);
        shareInfoBO.setNewShareDownloadUrl(shareDownloadUrl);
        processShareDownloadConfigDAO.insert(
                ShareDownloadConvert.convert2InitProcessShareDownloadConfigDO(
                        processBO, shareInfoBO));
    }

    /*
     * 获取或者初始化（如果不存在）分享下载主体的对应配置
     * @param processBO
     * @return 访问限制的limit值
     */
    private int getOrInitShareDownloadSubjectConfig(ShareDownloadProcessBO processBO) {
        ProcessShareDownloadSubjectConfigDO subjectConfigDO =
                processShareDownloadSubjectConfigDAO.getBySubjectGid(processBO.getSubjectGid());
        if (subjectConfigDO == null) {
            int defaultLimit = systemConfig.getShareDownloadAccessDefaultLimit();
            processShareDownloadSubjectConfigDAO.insertIgnore(
                    ShareDownloadConvert.convert2InitProcessShareDownloadSubjectConfigDO(
                            processBO, defaultLimit));
            return defaultLimit;
        } else {
            return subjectConfigDO.getAccessLimit();
        }
    }

    /**
     * 生成分享下载跳转地址
     *
     * @return
     */
    private String generateShareDownloadUrl(String processId, String salt, String shareDownloadId) {
        String sign = SignUtil.sign(shareDownloadId, salt, systemConfig.getRsaPrivateKeyPem());
        // url的签名需要url encode
        String encodeSign = urlEncode(sign);
        // todo 后续可以根据key替换
        return MessageFormat.format(
                systemConfig.getNewShareDownloadBaseUrl(), shareDownloadId, processId, encodeSign);
    }

    /**
     * 验签
     *
     * @param sign
     * @param shareDownloadId
     * @param salt
     */
    private void signVerify(String sign, String shareDownloadId, String salt) {
        boolean result =
                SignUtil.verify(sign, shareDownloadId, salt, systemConfig.getRsaPublicKeyPem());
        if (!result) {
            throw new BizContractManagerException(SHARE_DOWNLOAD_VERIFY_SIGN_ERROR);
        }
    }

    private void cacheFlowDocumentsFileUrl(
            String shareDownloadId, List<FlowDocumentDTO> flowDocumentDTOS) {
        Map<String, FlowDocumentCacheDTO> cacheDocuments =
                Maps.newHashMapWithExpectedSize(flowDocumentDTOS.size());
        flowDocumentDTOS.forEach(
                doc -> {
                    if (doc.getFileKey() != null) {
                        cacheDocuments.put(
                                doc.getFileKey(),
                                new FlowDocumentCacheDTO(
                                        doc.getFileId(),
                                        doc.getFileKey(),
                                        doc.getFileName(),
                                        doc.getFileUrl()));
                    }
                });
        TedisUtil.set(
                DOC_FILE_URL_CACHE_PREFIX + shareDownloadId,
                JSON.toJSONString(cacheDocuments),
                10,
                TimeUnit.MINUTES);
    }

    private FlowDocumentCacheDTO getFileDocFromCache(String shareDownloadId, String fileKey) {
        String fileInfos = TedisUtil.get(DOC_FILE_URL_CACHE_PREFIX + shareDownloadId);
        if (StringUtils.isBlank(fileInfos)) {
            throw new BizContractManagerException(SHARE_DOWNLOAD_FILE_INFO_EXPIRE);
        }
        Map<String, FlowDocumentCacheDTO> cacheDocuments =
                JSON.parseObject(
                        fileInfos, new TypeReference<Map<String, FlowDocumentCacheDTO>>() {});
        if (MapUtils.isEmpty(cacheDocuments)) {
            throw new BizContractManagerException(SHARE_DOWNLOAD_FILE_INFO_NOT_EXIST);
        }
        return cacheDocuments.get(fileKey);
    }

    private ShareDownloadProcessSignAndVerifyBO generateShareDownloadProcessSignAndVerifyBO(
            String sign,
            String shareDownloadId,
            String operatorOid,
            ProcessShareDownloadOperatingTypeEnum operatingTypeEnum) {
        ProcessShareDownloadConfigDO processShareDownloadConfigDO =
                processShareDownloadConfigDAO.getByShareDownloadId(shareDownloadId);
        if (processShareDownloadConfigDO == null) {
            throw new BizContractManagerException(SHARE_DOWNLOAD_PROCESS_CONFIG_NOT_EXIST);
        }
        ShareDownloadProcessBO processBO =
                ShareDownloadProcessBO.instanceWithProcessShareDownloadConfigDO(
                        processShareDownloadConfigDO, operatingTypeEnum, operatorOid);
        return new ShareDownloadProcessSignAndVerifyBO(
                processBO, sign, processShareDownloadConfigDO);
    }

    /*
     * 添加访问次数访问次数
     * @param processConfigDO
     */
    private void addAccessCount(ProcessShareDownloadConfigDO processConfigDO) {
        if (processConfigDO == null) {
            return;
        }
        int accessCount =
                processConfigDO.getAccessCount() == null ? 1 : processConfigDO.getAccessCount() + 1;
        processShareDownloadConfigDAO.updateAccessCount(
                processConfigDO.getShareDownloadId(),
                accessCount,
                processConfigDO.getAccessCount());
    }

    /*
     * url encode 后期需要可以移到工具类
     * @param encodeSource
     * @return
     */
    private String urlEncode(String encodeSource) {
        try {
            return URLEncoder.encode(encodeSource, "utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("url编码异常", e);
            return null;
        }
    }


}
package com.timevale.contractmanager.core.service.infocollect.impl;

import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectQueryCollectRecordProcessResultDTO;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfoCollectQueryCollectRecordProcessRequest;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.service.enums.grouping.FilePermissionEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.InfoCollectClient;
import com.timevale.contractmanager.core.model.dto.response.base.process.BaseBizProcessParticipantAccountVO;
import com.timevale.contractmanager.core.model.dto.response.infocollect.InfoCollectProcessListResponse;
import com.timevale.contractmanager.core.model.dto.response.infocollect.InfoCollectProcessVO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.convert.BaseBizProcessConverter;
import com.timevale.contractmanager.core.service.grouping.PermissionService;
import com.timevale.contractmanager.core.service.infocollect.InfoCollectProcessListService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.ProcessPermissionService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessCooperationTaskDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/9/19
 */
@Service
public class InfoCollectProcessListServiceImpl implements InfoCollectProcessListService {

    @Autowired
    private InfoCollectClient infoCollectClient;
    @Autowired
    private BaseProcessService baseProcessService;
    @Autowired
    private ContractProcessReadClient contractProcessReadClient;
    @Autowired
    private ProcessPermissionService processPermissionService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private UserCenterService userCenterService;


    @Override
    public InfoCollectProcessListResponse collectDataProcessList(String tenantOid, String operatorOid, String formId,
                                                                 String infoCollectRecordOuterId) {

        AssertX.isTrue(StringUtils.isNotBlank(tenantOid), "tenantOid 必传");
        AssertX.isTrue(StringUtils.isNotBlank(formId), "formId 必传");
        AssertX.isTrue(StringUtils.isNotBlank(infoCollectRecordOuterId), "infoCollectRecordOuterId 必传");

        // 操作人
        UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorOid);

        InfoCollectQueryCollectRecordProcessRequest request = new InfoCollectQueryCollectRecordProcessRequest();
        request.setFormId(formId);
        request.setInfoCollectRecordOuterId(infoCollectRecordOuterId);
        request.setTenantOid(tenantOid);
        InfoCollectQueryCollectRecordProcessResultDTO resultDTO = infoCollectClient.queryCollectRecordProcess(request);

        List<String> configFormWriteContentFlowIds = resultDTO.getConfigFormWriteContentFlowIds();

        List<String> startByFormContentProcessIds =
                Optional.ofNullable(resultDTO.getStartByFormContentProcessIds()).orElse(new ArrayList<>());

        if (CollectionUtils.isEmpty(configFormWriteContentFlowIds) && CollectionUtils.isEmpty(startByFormContentProcessIds)) {
            return new InfoCollectProcessListResponse();
        }

        // 确认表单信息的processIds
        List<String> configFormWriteContentProcessIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(configFormWriteContentFlowIds)) {
            configFormWriteContentProcessIds = baseProcessService.getSubProcess(configFormWriteContentFlowIds).stream()
                    .map(SubProcessDO::getProcessId)
                    .distinct()
                    .collect(Collectors.toList());
        }

        // 使用表单内容发起的process信息


        List<String> allProcessIds = new ArrayList<>(configFormWriteContentProcessIds);
        allProcessIds.addAll(startByFormContentProcessIds);

        //
        List<ContractProcessDTO> contractProcessDTOS = contractProcessReadClient.listByProcessIds(allProcessIds);
        if (CollectionUtils.isEmpty(contractProcessDTOS)) {
            return new InfoCollectProcessListResponse();
        }

        boolean admin = userCenterService.checkUserHasAdminAllPrivilege(tenantOid, operatorOid);
        boolean hasDownloadPermission = false;
        if (!admin) {
            // 非admin 下载和查看权限
            List<String> permissionList =
                    processPermissionService.getProcessPermissionList(null, null, operatorOid, tenantOid, null);
            for (String permissionCode : permissionList) {
                if (FilePermissionEnum.DOWNLOAD.code().equals(permissionCode)) {
                    hasDownloadPermission = true;
                    break;
                }
            }
        }

        // 转换返回值
        List<InfoCollectProcessVO> list = convert(contractProcessDTOS, operatorAccount, admin, hasDownloadPermission);
        InfoCollectProcessListResponse response = new InfoCollectProcessListResponse();
        response.setList(list);
        return response;
    }

    private List<InfoCollectProcessVO> convert(List<ContractProcessDTO> contractProcessDTOS, UserAccount operatorAccount,
                                               boolean admin, boolean hasDownloadPermission) {
        if (CollectionUtils.isEmpty(contractProcessDTOS)) {
            return new ArrayList<>();
        }

        return contractProcessDTOS.stream().map(elm -> {

            InfoCollectProcessVO infoCollectProcessVO = new InfoCollectProcessVO();

            infoCollectProcessVO.setProcessId(elm.getProcessId());
            infoCollectProcessVO.setTitle(elm.getTitle());
            infoCollectProcessVO.setCompleteTime(Optional.ofNullable(elm.getSignCompleteTime()).map(Date::getTime).orElse(null));
            ProcessStatusEnum processStatusEnum = ProcessStatusEnum.valueOf(elm.getProcessStatus());
            infoCollectProcessVO.setProcessStatusDesc(processStatusEnum != null ? processStatusEnum.getStatusDesc() : "-");
            infoCollectProcessVO.setProcessStatus(elm.getProcessStatus());

            boolean complete = ProcessStatusEnum.getAllDoneProcessStatusList().contains(elm.getProcessStatus());
            if (admin) {
                infoCollectProcessVO.setCanLook(true);
                // admin，默认可以下载
                infoCollectProcessVO.setCanDownload(complete);
            } else {
                // 是否是参与人
                boolean isParticipant = isParticipant(elm, operatorAccount);
                infoCollectProcessVO.setCanLook(isParticipant);
                infoCollectProcessVO.setCanDownload(complete && isParticipant && hasDownloadPermission);
            }

            // 发起人
            infoCollectProcessVO.setInitiator(BaseBizProcessConverter.processAccountConvert(elm.getInitiator()));
            // 参与方
            List<BaseBizProcessParticipantAccountVO> participantAccountVOS = BaseBizProcessConverter.taskConvert(elm);
            infoCollectProcessVO.setParticipants(participantAccountVOS);
            return infoCollectProcessVO;
        }).collect(Collectors.toList());
    }

    private boolean isParticipant(ContractProcessDTO contractProcessDTO, UserAccount operatorAccount) {
        // 发起人
        if (null != contractProcessDTO.getInitiator()) {
            if (sameAccount(contractProcessDTO.getInitiator(), operatorAccount)) {
                return true;
            }
        }

        // 抄送人
        if (CollectionUtils.isNotEmpty(contractProcessDTO.getCc())) {
            for (ProcessAccount processAccount : contractProcessDTO.getCc()) {
                if (sameAccount(processAccount, operatorAccount)) {
                    return true;
                }
            }
        }

        // 填写人
        if (CollectionUtils.isNotEmpty(contractProcessDTO.getCooperationTasks())) {
            for (ContractProcessCooperationTaskDTO task : contractProcessDTO.getCooperationTasks()) {
                if (sameAccount(task.getExecute(), operatorAccount)) {
                    return true;
                }
                if (sameAccount(task.getOperator(), operatorAccount)) {
                    return true;
                }
            }
        }

        // 签署人
        if (CollectionUtils.isNotEmpty(contractProcessDTO.getSignTasks())) {
            for (ContractProcessSignTaskDTO task : contractProcessDTO.getSignTasks()) {
                if (sameAccount(task.getExecute(), operatorAccount)) {
                    return true;
                }
                if (sameAccount(task.getOperator(), operatorAccount)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean sameAccount(ProcessAccount processAccount, UserAccount operatorAccount) {
        return processAccount != null && processAccount.getPerson() != null &&
                (Objects.equals(processAccount.getPerson().getOid(), operatorAccount.getAccountOid()) ||
                        Objects.equals(processAccount.getPerson().getGid(), operatorAccount.getAccountGid()));

    }

}

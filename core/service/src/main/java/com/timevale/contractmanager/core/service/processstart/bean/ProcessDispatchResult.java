package com.timevale.contractmanager.core.service.processstart.bean;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流程调度结果
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDispatchResult extends ToString {

    /** 子流程id */
    private String flowId;

    /** 子流程类型 */
    private Integer flowType;
}

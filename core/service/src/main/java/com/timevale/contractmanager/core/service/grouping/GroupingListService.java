package com.timevale.contractmanager.core.service.grouping;

import com.timevale.contractmanager.core.model.dto.process.GroupingProcessListQueryContext;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.GroupingListRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.QueryGroupingProcessListRequest;
import com.timevale.contractmanager.core.model.dto.response.grouping.dept.DeptTreeResponse;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.GroupingProcessListResponse;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.v2.GroupingProcessListResponseV2;
import com.timevale.signflow.search.docSearchService.param.GroupingQueryParam;
import com.timevale.signflow.search.service.bean.v2.sort.BaseSortParam;

import java.util.List;

/**
 * 归档列表服务
 *
 * <p>归档列表搜索服务
 *
 * <p>1.待归档
 *
 * <p>2.已归档
 *
 * @author: xuanzhu
 * @since: 2019-09-09 10:15
 */
public interface GroupingListService {

    /**
     * 待归档列表
     *
     * @param request
     * @return
     */
    GroupingProcessListResponse getUnGroupingProcessList(QueryGroupingProcessListRequest request, BaseSortParam sortParam);

    GroupingProcessListResponse getUnGroupingProcessList(GroupingQueryParam queryParam, GroupingProcessListQueryContext context, String tenantId, String accountId);
    /**
     * 已归档列表
     *
     * @param request
     * @return
     */
    GroupingProcessListResponse getGroupingProcessList(QueryGroupingProcessListRequest request);

    /**
     * 台账2.0 企业合同列表
     * @param request 入参
     * @return 出参
     */
    GroupingProcessListResponseV2 getProcessList(GroupingListRequest request);

    /**
     * 获取是部门负责人的部门树
     * @param tenantId
     * @param operatorId
     */
    DeptTreeResponse getDptTree(String tenantId, String operatorId);

    /**
     * 待归档查询，并转换结果
     */
    GroupingProcessListResponse waitingGroupQuery(GroupingQueryParam param, List<String> childTenantGidList);

    /**
     * 状态转换
     */
    List<Integer> buildProcessStatusList(String processStatus, Boolean withApproving);

}

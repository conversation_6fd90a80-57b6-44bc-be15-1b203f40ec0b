package com.timevale.contractmanager.core.service.watermark;

import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkModel;
import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkResult;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootModel;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootResult;
import com.timevale.docmanager.service.input.watermark.AddWatermarkInput;
import com.timevale.docmanager.service.input.watermark.Font;
import com.timevale.docmanager.service.input.watermark.Position;
import com.timevale.docmanager.service.input.watermark.WatermarkBaseInfo;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkContentTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkTypeEnum;
import com.timevale.saas.common.manage.common.service.model.bean.watermark.WatermarkAttribute;
import com.timevale.saas.common.manage.common.service.model.bean.watermark.WatermarkTemplate;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: contract-manager-project
 * @Description: 水印策略类
 * @date Date : 2023年04月23日 11:43
 */
public interface WatermarkStrategy {

    /**
     * 水印类型
     * @return
     */
    String getType();

    /**
     * 组合得到策略类的ID
     * @param typeEnum
     * @param contentTypeEnum
     * @return
     */
    static String assembleServiceId (WatermarkTypeEnum typeEnum, WatermarkContentTypeEnum contentTypeEnum){
        return typeEnum.name() + "_" + contentTypeEnum.name();
    }

    /**
     * 合同发起时生成水印快照
     *
     * @param model@return
     */
    GenerateWatermarkSnapShootResult generateWatermarkSnapShoot(GenerateWatermarkSnapShootModel model);


    /**
     * 发起签署前添加水印
     *
     * @param model@return
     */
    AddWatermarkResult addWatermark(AddWatermarkModel model);


     /**
     * 将水印模板详情转为水印入参
     * @param fileId
     * @param watermarkTemplateDetail
     * @return
     */
     default AddWatermarkInput convertWatermarkTemplate2Input(String fileId, WatermarkTemplate watermarkTemplateDetail) {
        AddWatermarkInput addWatermarkInfoInput = new AddWatermarkInput();
        //设置文件
        addWatermarkInfoInput.setFileId(fileId);

        //设置水印基础信息
        WatermarkBaseInfo watermarkBaseInfo = new WatermarkBaseInfo();
        watermarkBaseInfo.setContext(watermarkTemplateDetail.getContent());
        watermarkBaseInfo.setWaterMarktype(Objects.equals(WatermarkContentTypeEnum.IMAGE.getType(), watermarkTemplateDetail.getContentType()) ? "2" : "1");
        addWatermarkInfoInput.setWatermarkBaseInfo(watermarkBaseInfo);
        WatermarkAttribute attribute = watermarkTemplateDetail.getAttribute();

        //构建水印位置信息
        Position position = new Position();
        position.setOpacity(attribute.getOpacity());
        position.setRotationAngle(Float.valueOf(attribute.getRotate()));
        // 图片DPI及大小的设置
        watermarkBaseInfo.setRenderDpi((float) (Objects.isNull(attribute.getImgDpi()) ? 72 : attribute.getImgDpi()));
        position.setImgWidth(attribute.getImgWidth());
        position.setImgHeight(attribute.getImgHeight());

        WatermarkAttribute.Position templatePosition = attribute.getPosition();
        position.setPositionType(templatePosition.getPositionType());
        position.setDensity(templatePosition.getTileDensity());
        position.setFloatMode(templatePosition.getFloatMode());
        position.setPages(templatePosition.getPages());
        position.setLeftX(Optional.ofNullable(templatePosition.getLeftX()).orElse(0F));
        position.setTopY(Optional.ofNullable(templatePosition.getTopY()).orElse(0F));
        addWatermarkInfoInput.setPosition(position);

        //设置水印字体
         if(!WatermarkContentTypeEnum.IMAGE.getType().equals(watermarkTemplateDetail.getContentType()) && Objects.nonNull(attribute.getFont())){
             Font font = new Font();
             font.setFontsize(String.valueOf(attribute.getFont().getSize()));
             font.setFontEnum(attribute.getFont().getFontFamily());
             addWatermarkInfoInput.setFont(font);
             watermarkBaseInfo.setColor(attribute.getFont().getColor());
         }
        return addWatermarkInfoInput;
    }

}

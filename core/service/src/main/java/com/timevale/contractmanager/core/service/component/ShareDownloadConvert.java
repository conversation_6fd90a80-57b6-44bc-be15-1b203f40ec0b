package com.timevale.contractmanager.core.service.component;

import com.timevale.contractmanager.common.dal.bean.ProcessShareDownloadConfigDO;
import com.timevale.contractmanager.common.dal.bean.ProcessShareDownloadLogDO;
import com.timevale.contractmanager.common.dal.bean.ProcessShareDownloadSubjectConfigDO;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadProcessBO;
import com.timevale.contractmanager.core.model.bo.sharedownload.ShareDownloadShareInfoBO;
import com.timevale.contractmanager.core.model.dto.response.sharedownload.FlowDocumentVO;
import com.timevale.contractmanager.core.model.dto.sharedownload.FlowDocumentDTO;
import com.timevale.footstone.rpc.model.flowmodel.bean.FileInfo;
import com.timevale.footstone.rpc.result.flowresult.bean.DocumentResBean;

/**
 * <AUTHOR>
 * @since 2022/2/9
 */
public abstract class ShareDownloadConvert {
    public static ProcessShareDownloadLogDO convert2InitProcessShareDownloadLogDO(
            String shareDownloadId, ShareDownloadProcessBO processBO) {
        ProcessShareDownloadLogDO logDO = new ProcessShareDownloadLogDO();
        logDO.setProcessId(processBO.getProcessId());
        logDO.setOperatingOid(processBO.getOperatingOid());
        logDO.setOperatingType(
                processBO.getOperatingType() == null
                        ? null
                        : processBO.getOperatingType().getCode());
        logDO.setSubjectOid(processBO.getOperatingSubjectOid());
        logDO.setSubjectGid(processBO.getOperatingSubjectGid());
        logDO.setShareDownloadId(shareDownloadId);
		return logDO;
    }


    public static ProcessShareDownloadSubjectConfigDO
            convert2InitProcessShareDownloadSubjectConfigDO(
                    ShareDownloadProcessBO processBO, int accessLimit) {
        ProcessShareDownloadSubjectConfigDO subjectConfigDO =
                new ProcessShareDownloadSubjectConfigDO();
        subjectConfigDO.setSubjectOid(processBO.getSubjectOid());
        subjectConfigDO.setSubjectGid(processBO.getSubjectGid());
        subjectConfigDO.setAccessLimit(accessLimit);
        return subjectConfigDO;
    }

    public static ProcessShareDownloadConfigDO convert2InitProcessShareDownloadConfigDO(
            ShareDownloadProcessBO processBO, ShareDownloadShareInfoBO shareInfoBO) {
        ProcessShareDownloadConfigDO downloadConfigDO = new ProcessShareDownloadConfigDO();
        downloadConfigDO.setProcessId(processBO.getProcessId());
        downloadConfigDO.setShareDownloadId(shareInfoBO.getNewShareDownloadId());
        downloadConfigDO.setAccessLimit(shareInfoBO.getAccessLimit());
        downloadConfigDO.setSubjectOid(processBO.getSubjectOid());
        downloadConfigDO.setSubjectGid(processBO.getSubjectGid());
        downloadConfigDO.setSalt(shareInfoBO.getSalt());
        return downloadConfigDO;
    }

    public static FlowDocumentVO convert2FlowDocumentVO(FlowDocumentDTO flowDocumentDTO) {
        FlowDocumentVO vo = new FlowDocumentVO();
        vo.setFileId(flowDocumentDTO.getFileId());
        vo.setFileKey(flowDocumentDTO.getFileKey());
        vo.setFileName(flowDocumentDTO.getFileName());
        return vo;
    }

    public static FlowDocumentDTO convert2FlowDocumentDTO(FileInfo fileInfo, int fileType) {
        FlowDocumentDTO documentDTO = new FlowDocumentDTO();
        documentDTO.setFileKey(fileInfo.getFileKey());
        documentDTO.setFileId(fileInfo.getFileId());
        documentDTO.setFileName(fileInfo.getFileName());
        documentDTO.setFileType(fileType);
        documentDTO.setFileUrl(fileInfo.getDownloadUrl());
        return documentDTO;
    }
}

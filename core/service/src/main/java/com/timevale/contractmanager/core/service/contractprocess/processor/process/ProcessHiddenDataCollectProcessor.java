package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.HbaseProcessDataAsyncCollectException;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.ProcessTaskHiddenMsgEntity;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.request.datacollect.AccountParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessCooperationTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSignTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessAccountParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessAccountSimpleParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessApprovalParam;
import com.timevale.signflow.search.service.request.datacollect.ProcessApprovalTaskInfoParam;
import com.timevale.signflow.search.service.request.datacollect.SealApprovalParam;
import com.timevale.signflow.search.service.request.datacollect.SealApprovalTaskInfoParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Created by tianlei on 2022/5/10
 */
@Slf4j
@Component
public class ProcessHiddenDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessReadClient contractProcessQueryClient;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private LockService lockService;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processTopicName(), ProcessChangeTagEnum.PROCESS_HIDDEN.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessTaskHiddenMsgEntity entity = JsonUtils.json2pojo(data, ProcessTaskHiddenMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();

        ProcessTaskHiddenMsgEntity entity = (ProcessTaskHiddenMsgEntity) collectContext.getData();
        if (Boolean.TRUE.equals(entity.getAdminHidden())) {
            // admin 隐藏
            ContractProcessUpdateParam updateParam = new ContractProcessUpdateParam();
            updateParam.setProcessId(processId);
            updateParam.setProcessHidden(true);
            contractProcessWriteClient.updateByProcessId(updateParam);
            return;
        }

        Lock lock =
                lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                doProcess(entity);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }


    private void doProcess(ProcessTaskHiddenMsgEntity entity) {

        // 其它隐藏
        // 发起人、抄送人、签、填
        String oid = entity.getAccountId();
        String tenantOid = entity.getSubjectId();
        if (StringUtils.isBlank(oid) || StringUtils.isBlank(tenantOid)) {
            log.warn("hidden param miss");
            return;
        }

        //
        AccountBean personAccount = userCenterService.getAccountBeanByOid(oid);
        AccountBean tenantAccount = userCenterService.getAccountBeanByOid(tenantOid);
        if (null == personAccount || null == tenantAccount) {
            log.warn("hidden param account null");
            return;
        }

        ContractProcessUpdateParam updateParam = new ContractProcessUpdateParam();
        updateParam.setProcessId(entity.getProcessId());

        ContractProcessDTO contractProcessDTO = contractProcessQueryClient.getByProcessId(entity.getProcessId());

        // 发起人
        if (null != contractProcessDTO.getInitiator()) {
            ProcessAccount initiator = contractProcessDTO.getInitiator();
            ProcessAccountParam initiatorAccountParam = ProcessDataCollectConverter.account2Param(initiator);
            if (same(personAccount, initiatorAccountParam.getPerson()) && same(tenantAccount, initiatorAccountParam.getTenant())) {
                initiatorAccountParam.setHidden(true);
                updateParam.setInitiator(initiatorAccountParam);
            }
        }

        // 抄送人
        if (CollectionUtils.isNotEmpty(contractProcessDTO.getCc())) {
            List<ProcessAccountParam> ccAccountList = ProcessDataCollectConverter.account2ParamList(contractProcessDTO.getCc());
            for (ProcessAccountParam processAccountParam : ccAccountList) {
                if (same(personAccount, processAccountParam.getPerson()) && same(tenantAccount, processAccountParam.getTenant())) {
                    processAccountParam.setHidden(true);
                    updateParam.setCc(ccAccountList);
                }
            }
        }

        // 填写人
        if (CollectionUtils.isNotEmpty(contractProcessDTO.getCooperationTasks())) {
            List<ContractProcessCooperationTaskParam> cooperationTaskParams =
                    ProcessDataCollectConverter.cooperationTask2ParamList(contractProcessDTO.getCooperationTasks());
            for (ContractProcessCooperationTaskParam cooperationTaskParam : cooperationTaskParams) {
                if (same(personAccount, cooperationTaskParam.getExecute().getPerson()) &&
                        same(tenantAccount, cooperationTaskParam.getExecute().getTenant())) {
                    cooperationTaskParam.setHidden(true);
                    updateParam.setCooperationTasks(cooperationTaskParams);
                }
            }
        }

        // 签署人
        if (CollectionUtils.isNotEmpty(contractProcessDTO.getSignTasks())) {
            List<ContractProcessSignTaskParam> signTaskParams =
                    ProcessDataCollectConverter.signTaskDTO2ParamList(contractProcessDTO.getSignTasks());
            for (ContractProcessSignTaskParam signTaskParam : signTaskParams) {
                if (same(personAccount, signTaskParam.getExecute().getPerson()) &&
                        same(tenantAccount, signTaskParam.getExecute().getTenant())) {
                    signTaskParam.setHidden(true);
                    updateParam.setSignTasks(signTaskParams);
                }
            }
        }

        // 印章审批
        List<SealApprovalParam> updateSealApprovalParams =
                hiddenSealApproval(contractProcessDTO, tenantAccount.getGid(), personAccount.getGid());
        if (CollectionUtils.isNotEmpty(updateSealApprovalParams)) {
            updateParam.setSealApproval(updateSealApprovalParams);
        }

        // 合同审批
        ProcessApprovalParam processApprovalParam = hiddenProcessApproval(contractProcessDTO, tenantAccount.getGid(),
                personAccount.getGid());
        if (null != processApprovalParam) {
            updateParam.setProcessApproval(processApprovalParam);
        }

        if (null != updateParam.getInitiator() || null != updateParam.getCc() ||
                null != updateParam.getSignTasks() || null != updateParam.getCooperationTasks() ||
                null != updateSealApprovalParams || null != updateParam.getProcessApproval()) {
            updateParam.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_HIDDEN);
            contractProcessWriteClient.updateByProcessId(updateParam);
        }

    }

    /**
     * 合同审批隐藏
     */
    private ProcessApprovalParam hiddenProcessApproval(ContractProcessDTO contractProcessDTO, String tenantGid,
                                                       String personGid) {
        if (null == contractProcessDTO.getProcessApproval()) {
            return null;
        }
        // 合同审批肯定是发起人主体
        if (null == contractProcessDTO.getInitiator() ||
                null == contractProcessDTO.getInitiator().getSubject() ||
                !Objects.equals(contractProcessDTO.getInitiator().getSubject().getGid(), tenantGid)) {
            // 合同审批主体只能是发起人所在主体
            return null;
        }

        boolean needUpdateApproval = false;
        ProcessApprovalParam processApprovalParam =
                ProcessDataCollectConverter.processApprovalParam2DTO(contractProcessDTO.getProcessApproval());

        if (CollectionUtils.isEmpty(processApprovalParam.getApprovalTaskInfo())) {
            return null;
        }

        for (ProcessApprovalTaskInfoParam taskInfoParam : processApprovalParam.getApprovalTaskInfo()) {
            if (null != taskInfoParam.getPerson() && Objects.equals(taskInfoParam.getPerson().getGid(), personGid)) {
                taskInfoParam.setHidden(true);
                needUpdateApproval = true;
            }
        }

        return needUpdateApproval ? processApprovalParam : null;
    }

    /**
     * 印章审批隐藏
     */
    private List<SealApprovalParam> hiddenSealApproval(ContractProcessDTO contractProcessDTO, String tenantGid, String personGid) {
        if (CollectionUtils.isEmpty(contractProcessDTO.getSealApproval())) {
            return null;
        }
        boolean needUpdateSealApproval = false;
        List<SealApprovalParam> sealApprovalParams =
                ProcessDataCollectConverter.sealApprovalDTO2ParamList(contractProcessDTO.getSealApproval());
        for (SealApprovalParam sealApprovalParam : sealApprovalParams) {

            if (!(null != sealApprovalParam.getSubject() &&
                    CollectionUtils.isNotEmpty(sealApprovalParam.getApprovalTaskInfo()) &&
                    Objects.equals(sealApprovalParam.getSubject().getGid(), tenantGid))) {
                continue;
            }
            // 任务维度
            for (SealApprovalTaskInfoParam sealApprovalTaskInfoParam : sealApprovalParam.getApprovalTaskInfo()) {
                List<String> hiddenPersonGidList = Optional.ofNullable(sealApprovalTaskInfoParam.getHiddenPersonGid())
                        .orElse(new ArrayList<>());
                if (hiddenPersonGidList.contains(personGid)) {
                    continue;
                }
                String findHiddenPersonGid = null;
                if (CollectionUtils.isNotEmpty(sealApprovalTaskInfoParam.getPerson())) {
                    for (ProcessAccountSimpleParam processAccountSimpleParam : sealApprovalTaskInfoParam.getPerson()) {
                        if (Objects.equals(processAccountSimpleParam.getGid(), personGid)) {
                            // 隐藏肯定是某个人隐藏的找到这个人就不找了
                            findHiddenPersonGid = personGid;
                            break;
                        }
                    }
                }
                // 计划执行人未找到，找实际执行人
                if (StringUtils.isBlank(findHiddenPersonGid) &&
                        null != sealApprovalTaskInfoParam.getActualPerson() &&
                        Objects.equals(sealApprovalTaskInfoParam.getActualPerson().getGid(), personGid)) {
                    findHiddenPersonGid = personGid;
                }
                if (StringUtils.isNotBlank(findHiddenPersonGid)) {
                    hiddenPersonGidList.add(findHiddenPersonGid);
                    sealApprovalTaskInfoParam.setHiddenPersonGid(hiddenPersonGidList);
                    needUpdateSealApproval = true;
                }
            }

        }
        return needUpdateSealApproval ? sealApprovalParams : null;
    }

    private boolean same(AccountBean accountBean, AccountParam accountParam) {
        return Objects.equals(accountBean.getOid(), accountParam.getOid()) ||
                (Objects.equals(accountBean.getGid(), accountParam.getGid()));
    }
}

package com.timevale.contractmanager.core.service.grouping;

import com.timevale.contractmanager.core.model.bo.UserDownloadConfigBO;

/**
 * @author: duhui
 * @since: 2021/11/15 7:19 下午
 **/
public interface UserDownloadConfigService {

    /**
     * 保存用户下载配置
     * @param configBO
     */
    void saveConfig(UserDownloadConfigBO configBO);

    /**
     * 获取用户配置
     * @param oid
     * @return
     */
    UserDownloadConfigBO getByOid(String oid);

    /**
     * 生成默认的配置对象
     * @return
     */
    UserDownloadConfigBO genDefaultConfig();
}

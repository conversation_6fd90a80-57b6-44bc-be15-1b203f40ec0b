package com.timevale.contractmanager.core.service.offlinecontract;

import com.timevale.contractmanager.core.service.offlinecontract.bean.input.*;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordContractsOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordInfoOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordsOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.enums.FailCodeEnum;

import java.util.List;
import java.util.Map;

/**
 * 线下合同业务层接口定义
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
public interface OfflineContractService {

    /** 保存线下合同导入记录 */
    String saveOfflineContractRecord(SaveOfflineContractRecordDTO input);

    /** 删除线下合同导入记录 */
    void deleteOfflineContractRecords(DeleteOfflineContractRecordsDTO input);

    /** 删除线下合同导入记录, 内部使用， 无鉴权 */
    void deleteOfflineContractRecords(List<String> recordIds);

    /** 停止导入 */
    void stopImportOfflineContract(StopImportOfflineContractDTO input);

    /** 恢复导入, 停止导入的线下合同继续开始导入 */
    void recoverImportOfflineContract(RecoverImportOfflineContractDTO input);

    /** 重新导入导入失败的线下合同 */
    void restartImportFailedOfflineContract(RestartImportFailedOfflineContractDTO input);

    /** 查询线下合同导入记录列表 */
    OfflineContractRecordsOutputDTO queryOfflineContractRecords(
            QueryOfflineContractRecordsDTO input);

    /** 查询线下合同导入记录基本信息 */
    OfflineContractRecordInfoOutputDTO queryOfflineContractRecordInfo(
            QueryOfflineContractRecordInfoDTO input);

    /** 批量查询线下合同导入记录基本信息 */
    List<OfflineContractRecordInfoOutputDTO> batchQueryOfflineContractRecordInfo(
            BatchQueryOfflineContractRecordInfoDTO input);

    /** 查询线下合同导入记录合同信息列表 */
    OfflineContractRecordContractsOutputDTO queryOfflineContractRecordContracts(
            QueryOfflineContractRecordContractsDTO input);

    /** 校验文件是否导入过 */
    boolean checkFileExist(List<String> fileIds);

    /** 基于文件流程组id查询单个线下合同信息 */
    OfflineContractOutputDTO queryOfflineContractByRecordProcessId(String recordProcessId);

    /** 提取合同信息 */
    void extractProcessInfo(String recordProcessId);

    /** 保存合同信息提取结果 */
    void saveExtractProcessInfoResult(String recordProcessId, Map<String, Object> extractResult, String failCode);

    /**
     * 导入记录是否存在可以重试的导入流程
     * @param recordId
     * @return
     */
    boolean existsFailCodeImportProcess(String recordId, FailCodeEnum failCode, String tenantGid);

    /** 基于文件流程组id更新导入状态 */
    void updateStatusByRecordProcessId(UpdateStatusByRecordProcessIdDTO input);

    /** 计算并更新导入记录状态 */
    void calculateAndUpdateRecordStatus(String recordId);
}

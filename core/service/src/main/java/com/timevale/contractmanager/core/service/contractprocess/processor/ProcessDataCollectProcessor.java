package com.timevale.contractmanager.core.service.contractprocess.processor;

import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;

/**
 * Created by t<PERSON>le<PERSON> on 2022/5/10
 */
public interface ProcessDataCollectProcessor {

    String LOG_PREFIX = ProcessDataCollectSupport.LOG_PREFIX;

    Route route();

    /**
     * 解析消息
     */
    DataAnalysisResult dataAnalysis(String data);

    /**
     * process数据存在时是否继续消费
     * @return true 继续处理, false 不处理
     */
    boolean processDataExistContinueProcess();

    /**
     * 刚初始化 process数据后, 是否需要继续处理
     * @return true 继续处理  false 不处理
     */
    boolean initProcessDataAfterContinueProcess();

    /**
     * 处理数据
     */
    void process(ProcessDataCollectContext collectContext);

    /**
     * 消息正常处理完的后置处理
     */
    default void postProcessAfter(ProcessDataCollectContext collectContext) {
        //default nothing
    };

}

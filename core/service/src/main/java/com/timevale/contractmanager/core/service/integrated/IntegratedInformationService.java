package com.timevale.contractmanager.core.service.integrated;

import com.timevale.contractmanager.core.model.dto.response.integrated.IntegratedInformationResponse;

import java.util.List;

/**
 * 融合服务
 *
 * <AUTHOR>
 * @since 2020-11-04 16:44
 */
public interface IntegratedInformationService {

    /**
     * 获取首页信息
     *
     * @param pageSize 每页大小
     * @param pageNum 页码
     * @param statusList 流程状态
     * @param docQueryType 查询类型
     * @return 首页信息
     */
    IntegratedInformationResponse getIntegratedInformation(
           String clientId,Integer pageSize, Integer pageNum, List<Integer> statusList,Integer docQueryType, Boolean approvingCount);
}

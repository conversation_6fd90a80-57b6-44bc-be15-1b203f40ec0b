package com.timevale.contractmanager.core.service.watermark;

import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkModel;
import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkResult;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootModel;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 水印权限策略上下文
 * @date Date : 2023年05月11日 17:12
 */
@Component
public class WatermarkStrategyContext {
    private Map<String, WatermarkStrategy> strategies;

    @Autowired
    public WatermarkStrategyContext(List<WatermarkStrategy> strategyList) {
        this.strategies = strategyList.stream()
                .collect(Collectors.toMap(WatermarkStrategy::getType, Function.identity()));
    }

    public GenerateWatermarkSnapShootResult generateWatermarkSnapShoot(GenerateWatermarkSnapShootModel model) {
        //使用具体策略类来生成水印快照
        String strategyServiceId = WatermarkStrategy.assembleServiceId(model.getWatermarkType(), model.getWatermarkContentType());
        return strategies.get(strategyServiceId).generateWatermarkSnapShoot(model);
    }

    public AddWatermarkResult addWatermark(AddWatermarkModel model) {
        //使用具体策略类来添加水印
        String strategyServiceId = WatermarkStrategy.assembleServiceId(model.getWatermarkType(), model.getWatermarkContentType());
        return strategies.get(strategyServiceId).addWatermark(model);
    }
}

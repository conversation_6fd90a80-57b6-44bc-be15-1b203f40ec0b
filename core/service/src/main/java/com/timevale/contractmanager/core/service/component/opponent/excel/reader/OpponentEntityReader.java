package com.timevale.contractmanager.core.service.component.opponent.excel.reader;

import com.timevale.contractmanager.core.model.bo.opponent.OpponentEntityExcelReadBO;

import java.util.LinkedHashMap;

/**
 * 相对方实体列读取
 *
 * <AUTHOR>
 * @since 2021-02-02 15:13
 */
public interface OpponentEntityReader {

    /**
     * 读取相对方实体信息
     *
     * @param data 每行的数据
     * @return 相对方实体信息
     */
    OpponentEntityExcelReadBO read(LinkedHashMap<Integer, Object> data);
}

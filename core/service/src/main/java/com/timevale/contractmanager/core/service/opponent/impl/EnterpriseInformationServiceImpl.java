package com.timevale.contractmanager.core.service.opponent.impl;

import com.timevale.contractmanager.common.service.integration.client.InfoAuthServiceClient;
import com.timevale.contractmanager.common.utils.DateUtil;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEnterpriseBasicInfoDetailBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEnterpriseInformationBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentSimpleInfoListBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentOrgSummaryBO;
import com.timevale.contractmanager.core.service.opponent.EnterpriseInformationService;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.infoauth.service.response.query.AccurateQueryEnterpriseInformationResponse;
import com.timevale.infoauth.service.response.query.FuzzyQueryEnterpriseInformationResponse;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * 查询工商信息by启信宝
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Service
@Slf4j
public class EnterpriseInformationServiceImpl implements EnterpriseInformationService {
    
    @Autowired
    private InfoAuthServiceClient infoauthServiceClient;
    @Autowired
    private MapperFactory mapperFactory;
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    
    @Override
    public OpponentEnterpriseInformationBO getEnterpriseInformation(String keyword) {
        return output2basic(infoauthServiceClient.getEnterpriseInformation(keyword));
    }

    @Override
    public OpponentSimpleInfoListBO enterpriseSimpleInfo(String keyword) {
        return output2InfoList(infoauthServiceClient.enterpriseSimpleInfo(keyword));
    }

    @Override
    public OpponentOrgSummaryBO getEnterpriseUrl(String orgName) {
        return mapperFactory.getMapperFacade().map(infoauthServiceClient.getEnterpriseUrl(orgName), OpponentOrgSummaryBO.class);
    }

    private OpponentEnterpriseInformationBO output2basic(RpcOutput<AccurateQueryEnterpriseInformationResponse> output) {
        OpponentEnterpriseInformationBO information = new OpponentEnterpriseInformationBO();
        information.setMessage(output.getMessage());
        information.setSuccess(output.isSuccess());
        if (output.getData() == null) {
            return information;
        }
        OpponentEnterpriseBasicInfoDetailBO detail = new OpponentEnterpriseBasicInfoDetailBO();
        AccurateQueryEnterpriseInformationResponse data = output.getData();
        detail.setCreditCode(data.getCodeUSC());
        detail.setName(data.getName());
        detail.setBusinessScope(data.getScope());
        detail.setStatus(data.getStatus());
        detail.setEntityType(data.getEconKind());
        detail.setLegalPersonName(data.getOperName());
        detail.setAddress(data.getAddress());
        detail.setAreaCode(data.getAreaCode());
        detail.setBusinessEndDate(DateUtil.date2String(DATE_FORMAT,data.getTermEnd()));
        //处理结束时间为null和9999-09-09
        if (StringUtils.isBlank(detail.getBusinessEndDate())
                || StringUtils.equals(detail.getBusinessEndDate(),"9999-09-09")) {
            detail.setBusinessEndDate("");
        }
        detail.setBusinessStartDate(DateUtil.date2String(DATE_FORMAT,data.getTermStart()));
        detail.setCancellationDate(DateUtil.date2String(DATE_FORMAT,data.getCancellationDate()));
        detail.setCheckDate(DateUtil.date2String(DATE_FORMAT,data.getCheckDate()));
        detail.setEstablishDate(DateUtil.date2String(DATE_FORMAT,data.getStartDate()));
        detail.setHistoryNameList(data.getHistoryNameList());
        detail.setRegInstitute(data.getRegInstitute());
        detail.setRegisterCapital(data.getRegistCapi());
        detail.setRegNo(data.getCodeREG());
        detail.setRevokeDate(DateUtil.date2String(DATE_FORMAT,data.getRevokeDate()));
        detail.setRevokeReason(data.getRevokeReason());
        information.setDetail(detail);
        return information;
    }

    private OpponentSimpleInfoListBO output2InfoList(RpcOutput<FuzzyQueryEnterpriseInformationResponse> output) {
        OpponentSimpleInfoListBO result = new OpponentSimpleInfoListBO();
        result.setSuccess(output.isSuccess());
        result.setMessage(output.getMessage());
        if (output.getData().getTotal() == 0 || CollectionUtils.isEmpty(output.getData().getEnterpriseInformationResultList())) {
            return result;
        }
        result.setTotal((long) output.getData().getTotal());
        result.setItems(output.getData().getEnterpriseInformationResultList().stream().map(item -> {
            OpponentEnterpriseBasicInfoDetailBO detail = new OpponentEnterpriseBasicInfoDetailBO();
            detail.setName(item.getName());
            detail.setLegalPersonName(item.getLegalName());
            detail.setCreditCode(item.getCodeUSC());
            detail.setRegNo(item.getCodeREG());
            return detail;
        }).collect(Collectors.toList()));
        return result;
    }
}

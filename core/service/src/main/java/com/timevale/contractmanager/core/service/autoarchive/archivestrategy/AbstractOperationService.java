package com.timevale.contractmanager.core.service.autoarchive.archivestrategy;

import com.timevale.account.organization.service.exception.OrganErrors;
import com.timevale.contractmanager.core.model.dto.autoarchive.ArchiveDeptPersonDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.easun.service.api.RpcDeptPlusService;
import com.timevale.easun.service.api.RpcEsAccountService;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.input.BizEasunDeptIdInput;
import com.timevale.easun.service.model.organization.input.BizEsMemberWithDeptSearchInput;
import com.timevale.easun.service.model.organization.output.BizDeptDetailOutput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: huifeng
 * @since: 2021-09-09 11:46
 */
@Component
@Slf4j
public class AbstractOperationService {

    @Autowired private UserCenterService userCenterService;

    @Autowired private RpcDeptPlusService rpcDeptPlusService;

    @Autowired private RpcEsAccountService esAccountService;

    protected static Integer EXPIRE_TIME_IN_MINTURES = 5;

    protected List<String> getDeptPersonAccounts(
            List<ArchiveDeptPersonDTO> deptIdPersonOids, String tenantOid) {
        boolean existDepts =
                deptIdPersonOids.stream()
                                .filter(item -> StringUtils.isNoneBlank(item.getDepartment()))
                                .count()
                        > 0;
        String organId = null;
        if (existDepts) {
            organId = userCenterService.orgIdToOrganId(tenantOid);
        }
        List<String> accountIds = Lists.newArrayList();

        for (ArchiveDeptPersonDTO deptPersonDTO : deptIdPersonOids) {
            if (StringUtils.isNotBlank(deptPersonDTO.getDepartment())) {
                String deptKey =
                        CacheUtil.getDepartmentPersonnelOidGidsKey(deptPersonDTO.getDepartment());
                List<String> personnels = TedisUtil.get(deptKey);
                if (CollectionUtils.isEmpty(personnels)) {
                    personnels = Lists.newArrayList();
                    BizEasunDeptIdInput input = new BizEasunDeptIdInput();
                    input.setOrganId(organId);
                    input.setDeptId(deptPersonDTO.getDepartment());
                    try {
                        BizDeptDetailOutput bizDeptDetailOutput =
                                rpcDeptPlusService.getDeptDetail(new RpcInput<>(input)).getData();
                        if (bizDeptDetailOutput != null) {
                            personnels.addAll(
                                    getDeptMemberIds(deptPersonDTO.getDepartment(), tenantOid));
                            TedisUtil.set(
                                    deptKey, personnels, EXPIRE_TIME_IN_MINTURES, TimeUnit.MINUTES);
                        }
                    } catch (OrganErrors.DeptNotExistForId e) {
                        log.info(
                                "自动分类规则校验 部门不存在。tenantOid:{},deptId:{}",
                                tenantOid,
                                deptPersonDTO.getDepartment());
                    }
                }

                if (CollectionUtils.isNotEmpty(personnels)) {
                    accountIds.addAll(personnels);
                }
            } else {
                String personKey = CacheUtil.getPersonOidGidKey(deptPersonDTO.getPerson());
                List<String> personOidGids = TedisUtil.get(personKey);
                if (CollectionUtils.isEmpty(personOidGids)) {
                    UserAccount userAccount =
                            userCenterService.getUserAccountBaseByOid(deptPersonDTO.getPerson());
                    personOidGids =
                            Arrays.asList(userAccount.getAccountGid(), userAccount.getAccountOid());
                    TedisUtil.set(
                            personKey, personOidGids, EXPIRE_TIME_IN_MINTURES, TimeUnit.MINUTES);
                }
                accountIds.addAll(personOidGids);
            }
        }

        return accountIds;
    }

    /**
     * 获取部门成员id包含oid和gid
     *
     * @param deptId
     * @param tenantId
     * @return
     */
    public List<String> getDeptMemberIds(String deptId, String tenantId) {
        PagerResult<MemberDetail> pagerResult = getDeptMembers(deptId, tenantId);
        List<String> participantIds = new ArrayList<>();
        pagerResult.getItems().stream()
                .forEach(
                        x -> {
                            participantIds.add(x.getMemberOid());
                            if (StringUtils.isNotBlank(x.getMemberGid())) {
                                participantIds.add(x.getMemberGid());
                            }
                        });
        return participantIds;
    }

    /**
     * 获取部门成员
     *
     * @param deptId
     * @param tenantId
     * @return
     */
    public PagerResult<MemberDetail> getDeptMembers(String deptId, String tenantId) {
        String organId = userCenterService.orgIdToOrganId(tenantId);
        // 获取组织成员架构
        BizEsMemberWithDeptSearchInput input = new BizEsMemberWithDeptSearchInput();
        input.setOrganId(organId);
        input.setOffset(0);
        input.setSize(1000);
        input.setDeptId(deptId);
        input.setMemberGuidExist(true);
        return esAccountService.getOrgMemberWithDeptList(new RpcInput<>(input)).getData();
    }
}

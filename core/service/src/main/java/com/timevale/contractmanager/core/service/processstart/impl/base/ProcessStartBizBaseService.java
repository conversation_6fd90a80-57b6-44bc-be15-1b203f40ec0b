package com.timevale.contractmanager.core.service.processstart.impl.base;

import com.google.common.collect.Lists;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum;
import com.timevale.contractmanager.common.dal.bean.ProcessConfigDO;
import com.timevale.contractmanager.common.service.bean.ProcessConfigBean;
import com.timevale.contractmanager.common.service.bean.ProcessSecretConfigBean;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.enums.*;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ProcessStartRequestCache;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.component.ProcessConfigConverter;
import com.timevale.contractmanager.core.service.contractapproval.param.StartContractApprovalCheckDTO;
import com.timevale.contractmanager.core.service.contractapproval.result.StartContractApprovalCheckResult;
import com.timevale.contractmanager.core.service.group.GroupService;
import com.timevale.contractmanager.core.service.mq.model.FileContractCategoryRelation;
import com.timevale.contractmanager.core.service.mq.model.ProcessContractCategoryMsgEntity;
import com.timevale.contractmanager.core.service.mq.model.RelationProcessMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.*;
import com.timevale.contractmanager.core.service.process.*;
import com.timevale.contractmanager.core.service.process.impl.ProcessStartHelper;
import com.timevale.contractmanager.core.service.processstart.factory.ProcessStartBatchServiceFactory;
import com.timevale.contractmanager.core.service.processstart.factory.ProcessStartCoreServiceFactory;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContext;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContextService;
import com.timevale.contractmanager.core.service.processstart.impl.context.VipFunctionBizContext;
import com.timevale.contractmanager.core.service.sharesign.ShareSignBaseService;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.doccooperation.service.model.CommonStartExtensionKeyConstants;
import com.timevale.doccooperation.service.result.FlowTemplateStructResult;
import com.timevale.docmanager.service.enums.StructComponentTypeEnum;
import com.timevale.docmanager.service.model.StructComponent;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;
import static com.timevale.docmanager.service.enums.StructComponentTypeEnum.CHECK_QR_CODE;

@Slf4j
@Service
public class ProcessStartBizBaseService extends ProcessStartRelationBaseService {

    @Autowired protected ProcessStartHelper processStartHelper;
    @Autowired protected ProcessStartContextService processStartContextService;
    @Autowired protected BaseProcessService baseProcessService;
    @Autowired ShareSignBaseService shareSignBaseService;
    @Autowired ProcessDraftService processDraftService;
    @Autowired ProcessStartCoreServiceFactory factory;
    @Autowired ProcessStartBatchServiceFactory batchFactory;
    @Autowired NoviceTaskCompleteMsgProducer noviceTaskCompleteMsgProducer;
    @Autowired DeleteTempFlowTemplateProducer deleteTempFlowTemplateProducer;
    @Autowired RelationProcessProducer relationProcessProducer;
    @Autowired ProcessTemplateService processTemplateService;
    @Autowired GroupService groupService;
    @Autowired ProcessContractCategoryProducer processContractCategoryProducer;

    /**
     * 校验实名状态
     *
     * @param initiator
     * @param tenant
     */
    public void checkRealNamed(UserAccountDetail initiator, UserAccountDetail tenant) {
        // 优先校验主体实名状态
        if (!tenant.isRealName()) {
            String userType = tenant.isOrganize() ? "企业" : "个人";
            throw new BizContractManagerException(ACCOUNT_NOT_REALNAMED, userType, "发起签署");
        }
        // 其次校验用户实名状态
        if (!initiator.isRealName()) {
            String userType = initiator.isOrganize() ? "企业" : "个人";
            throw new BizContractManagerException(ACCOUNT_NOT_REALNAMED, userType, "发起签署");
        }
    }

    /**
     * 校验签署批注是否可用
     *
     * @param context
     * @param flowTemplateId
     */
    public void checkSignRemarkAvailable(
            ProcessStartContext context, String flowTemplateId) {
        if (StringUtils.isBlank(flowTemplateId)) {
            return;
        }
        // 查询流程模板控件列表
        FlowTemplateStructResult flowTemplateStructs =
                processStartContextService.getFlowTemplateStructs(
                        flowTemplateId, context.getTenant());
        // 解析所有的控件列表
        List<StructComponent> structComponents =
                processStartContextService.parseStructComponents(flowTemplateStructs);
        // 判断是否包含备注签署区
        if (structComponents.stream()
                .anyMatch(i -> StructComponentTypeEnum.REMARK.getType().equals(i.getType()))) {
            // 校验会员功能是否支持
            context.getFunctionContext().checkFunctionValid(FunctionCodeConstant.SIGN_REMARK, true);
        }
    }

    /**
     * 校验参与方数量是否达到上限
     */
    public void checkParticipantCount(List<ParticipantBO> participants, ProcessStartContext context) {
        int participantSize = 0;
        for (ParticipantBO participant : participants) {
            // 非或签方， 参与方数量+1
            if (!ParticipantModeEnum.isOrSign(participant.getParticipantMode())) {
                participantSize += 1;
                continue;
            }
            boolean participantRoleStart = StringUtils.isNotBlank(participant.getParticipantStartType()) && ParticipantStartType.PARTICIPANT_ASSIGN_ROLE.getType().equals(participant.getParticipantStartType());
            // 或签方，校验或签方数量必须>=2
            if (!participantRoleStart && (CollectionUtils.isEmpty(participant.getInstances()) || participant.getInstances().size() < 2)) {
                throw new BizContractManagerException(OR_SIGN_PARTICIPANT_LESS_THAN_2);
            }
            if(CollectionUtils.isEmpty(participant.getInstances())){
                continue;
            }
            // 参与方数量追加实例数量， 签署能力升级项目需求修改20250110，或签也按1个方计算
            participantSize += 1;
        }
        // 判断参与方数量是否达到会员版本支持的上限
        context.getFunctionContext()
                .checkFunctionLimit(FunctionCodeConstants.PARTICIPANTS_NUM, participantSize, true);
    }

    /**
     * 校验是否支持或签
     */
    public void checkParticipantOrSign(List<ParticipantBO> participants, VipFunctionBizContext functionContext) {
        // 判断是否存在或签参与方
        boolean hasOrSign =
                participants.stream()
                        .map(ParticipantBO::getParticipantMode)
                        .anyMatch(ParticipantModeEnum::isOrSign);
        // 如果存在， 校验是否支持或签功能
        if (hasOrSign) {
            functionContext.checkFunctionValid(FunctionCodeConstants.OR_SIGN, true);
        }
    }

    /**
     * 合同审批模板校验
     *
     * @param request
     * @param context
     */
    protected void checkContractApprovalTemplate(
            ProcessStartBizRequest request, ProcessStartContext context) {
        if (!context.getTenant().isOrganize() || !context.getAccount().isPerson()) {
            return;
        }
        // 校验合同审批模板id是否有效，并返回真正使用的审批模板id
        StartContractApprovalCheckResult result = preCheckStartParam(request, context);
        request.setApproveTemplateId(result.getApprovalTemplateId());
        request.setApprovalTemplateConditionType(result.getApprovalTemplateConditionType());
        request.setApprovalTemplateConditionValue(result.getApprovalTemplateConditionValue());
    }

    /**
     * 校验文件并组装合同保密配置
     *
     * @param request
     * @param tenant
     */
    protected ProcessSecretConfigBean checkFileAndBuildSecretConfig(
            ProcessStartBizRequest request, UserAccountDetail tenant) {
        List<FileBO> files = Lists.newArrayList();
        ///
        if (CollectionUtils.isNotEmpty(request.getContracts())) {
            files.addAll(request.getContracts());
            // 校验合同类型是否有效， 专属云转移到 外部校验
//            processStartHelper.checkAndBuildFileContractCategories(request.getContracts(), tenant);
        }
        if (CollectionUtils.isNotEmpty(request.getAttachments())) {
            files.addAll(request.getAttachments());
        }
        if (CollectionUtils.isEmpty(files)) {
            return null;
        }

        // 文件拥有者校验   专属云 转移到前置校验
//        GetFlowTemplateResult flowTemplateResult = null;
//        if (StringUtils.isNotBlank(request.getFlowTemplateId())) {
//            flowTemplateResult = processStartContextService.getFlowTemplateResult(request.getFlowTemplateId(), tenant);
//        }
//        processStartChecker.checkFilesWithFlowTemplate(flowTemplateResult, files, true, tenant, null);

        // 获取合同保密配置, 发起场景，需要把可见人OID转成GID
        return processStartHelper.buildProcessSecretConfig(
                request.getSecretType(), files, request.getVisibleAccounts());
    }

    /**
     * 保存发起请求缓存 用于发起后跳转页判断
     * @param processId
     * @param request
     */
    protected void cacheStartRequest(String processId ,ProcessStartCoreRequest request,UserAccountDetail subject){
        try{
            if( Strings.isBlank(processId)){
                return;
            }
            ProcessStartRequestCache cache = new ProcessStartRequestCache();
            cache.setApproveTemplateId(request.getApproveTemplateId());
            cache.setParticipants(request.getParticipants());
            cache.setSubjectGid(subject.getAccountGid());
            TedisUtil.set(CacheUtil.getProcessStartRequest(processId),JsonUtils.obj2json(cache),5, TimeUnit.MINUTES);
        }catch (Exception e){
            //不影响主流程
            log.warn("保存发起参数缓存失效",e);
        }
    }

    /**
     * 发起流程
     *
     * @param request
     * @param coreRequest
     * @return
     */
    protected ProcessStartResult startFlow(
            ProcessStartBizRequest request, ProcessStartCoreRequest coreRequest, ProcessStartContext context) {
        // 业务层发起的流程默认都需要埋点
        coreRequest.setNeedStartSensor(true);
        coreRequest.setResetStart(request.checkResetProcess());
        // 业务层发起的流程根据会员功能判断是否需要进行相对方风控
        boolean checkOpponentRisk =
                context.getFunctionContext()
                        .checkFunctionValid(FunctionCodeConstants.RISK_LEVEL, false);
        coreRequest.setCheckOpponentRisk(checkOpponentRisk);
        // 业务层处理付费方策略， 获取真正的流程付费方
        if (null == coreRequest.getPayerAccount()) {
            coreRequest.setPayerAccount(
                    processStartHelper.getBillSubject(coreRequest.getTenantAccount()));
        }
        // 判断是否批量发起， 如果是，执行批量发起， 否则默认单个发起
        if (request.checkBatchStart()) {
            // 批量发起上限校验
            processStartHelper.checkBatchLimit(coreRequest, context);
            // 批量发起
            return startBatchProcess(request, coreRequest);
        }
        // 判断是否重置合同流程
        if (request.checkResetProcess()) {
            String processGroupId = baseProcessService.queryProcessGroupId(request.getOriginProcessId());
            // 如果原合同流程有流程组， 则设置流程组id
            coreRequest.setProcessGroupId(processGroupId);
        }
        ProcessBusinessType businessType = ProcessBusinessType.from(request.getBusinessType());
        // 如果关联类型不为空，触发关联流程发起逻辑
        ProcessStartResult startResult;
        if (null != businessType.getRelationType()) {
            startResult = startRelationProcess(request, () -> startProcess(coreRequest));
        } else {
            // 发起流程
            startResult = startProcess(coreRequest);
        }

        afterStart(request, coreRequest, startResult);

        return startResult;
    }

    private void afterStart(
            ProcessStartBizRequest request,
            ProcessStartCoreRequest coreRequest,
            ProcessStartResult startResult) {
        ProcessBusinessType businessType = ProcessBusinessType.from(request.getBusinessType());
        // 缓存标识主体已发起流程
        String appId = coreRequest.getAppId();
        String subjectId = coreRequest.getTenantAccount().getAccountOid();
        String subjectInitiatedKey = CacheUtil.getSubjectInitiatedKey(subjectId, appId);
        CacheUtil.degradeSet(subjectInitiatedKey, true, 1, TimeUnit.DAYS);

        // 发送新手任务完成消息
        noviceTaskCompleteMsgProducer.sendInitiateMessage(
                coreRequest.getInitiatorAccount().getAccountOid(),
                coreRequest.getTenantAccount().getAccountOid());

        // 删除草稿及临时模板
        deleteDraftOrTempTemplate(request);

        // 如果有要关联的合同，创建合同关联
        createRelations(businessType.getRelationType(), request, coreRequest, startResult);

        // 发送合同类型关联关系
        noticeContractCategoryRelations(coreRequest, startResult);

        // 判断是否需要合同流程，如果是，新流程发起成功后需撤回原流程
        if (request.checkResetProcess()) {
            groupService.revokeAndRemoveProcessGroup(request.getOriginProcessId());
        }
    }


    /**
     * 发送关联消息
     *
     * @param relationType {@link RelationTypeEnum 关联类型}
     * @param request
     * @param coreRequest
     * @param startResult
     */
    private void createRelations(
            Integer relationType,
            ProcessStartBizRequest request,
            ProcessStartCoreRequest coreRequest,
            ProcessStartResult startResult) {
        // 获取要新增的关联合同列表，如果没有直接退出
        List<String> relationProcessIds =
                Objects.isNull(relationType)
                        ? request.getRelationProcessIds()
                        : Collections.singletonList(request.getOriginProcessId());
        if (CollectionUtils.isEmpty(relationProcessIds)) {
            return;
        }

        Integer relationScene =
                Optional.ofNullable(relationType)
                        .map(RelationSceneEnum::get)
                        .map(RelationSceneEnum::getScene)
                        .orElse(RelationSceneEnum.MANUAL.getScene());

        RelationProcessMsgEntity msgEntity = new RelationProcessMsgEntity();
        msgEntity.setTenantGid(coreRequest.getTenantAccount().getAccountGid());
        msgEntity.setAccountOid(coreRequest.getInitiatorAccount().getAccountOid());
        msgEntity.setRelationScene(relationScene);
        msgEntity.setProcessId(startResult.getProcessId());
        msgEntity.setCreateTime(new Date());
        msgEntity.setRelationProcessIds(relationProcessIds);

        relationProcessProducer.sendMessage(JsonUtils.obj2json(msgEntity));
    }

    /**
     * 发送合同类型关联消息
     *
     * @param coreRequest
     * @param startResult
     */
    private void noticeContractCategoryRelations(
            ProcessStartCoreRequest coreRequest,
            ProcessStartResult startResult) {
        if (CollectionUtils.isEmpty(coreRequest.getContracts())) {
            return;
        }
        List<FileContractCategoryRelation> categoryRelations = Lists.newArrayList();
        for (FileBO contract : coreRequest.getContracts()) {
            if (StringUtils.isBlank(contract.getCategoryId())) {
                return;
            }
            FileContractCategoryRelation relation = new FileContractCategoryRelation();
            relation.setFileId(contract.getFileId());
            relation.setCategoryId(contract.getCategoryId());
            categoryRelations.add(relation);
        }
        if (CollectionUtils.isEmpty(categoryRelations)) {
            return;
        }
        ProcessContractCategoryMsgEntity msgEntity = new ProcessContractCategoryMsgEntity();
        msgEntity.setProcessId(startResult.getProcessId());
        msgEntity.setRelationList(categoryRelations);
        processContractCategoryProducer.sendMessage(JsonUtils.obj2json(msgEntity));
    }

    /** 调用单个发起， 发起成功之后删除草稿及临时模板 */
    private ProcessStartResult startProcess(ProcessStartCoreRequest coreRequest) {
        // 1.发起流程
        ProcessStartResult startResult = new ProcessStartResult();
        try {
            startResult = factory.getService(coreRequest).start(coreRequest);
        } catch (BizContractManagerException e) {
            processStartHelper.throwSpecificBillingBalance(
                    coreRequest.getClientId(),e, coreRequest.getInitiatorAccount(), coreRequest.getTenantAccount(),
                    coreRequest.getStartCoreConfig().getProductId(),
                    coreRequest.getStartCoreConfig().getNeedBillingIso());
        }

        return startResult;
    }

    /** 调用批量发起， 发起成功之后删除草稿及临时模板 */
    private ProcessStartResult startBatchProcess(
            ProcessStartBizRequest request, ProcessStartCoreRequest coreRequest) {
        // 1.批量发起流程
        ProcessStartResult startResult = new ProcessStartResult();
        try {
            startResult = batchFactory.getService(request.getStartType()).start(coreRequest);
        } catch (BizContractManagerException e) {
            processStartHelper.throwSpecificBillingBalance(
                    request.getClientId(),e, coreRequest.getInitiatorAccount(), coreRequest.getTenantAccount(),
                    coreRequest.getStartCoreConfig().getProductId(),
                    coreRequest.getStartCoreConfig().getNeedBillingIso());
        }
        // 2.删除草稿及临时模板
        deleteDraftOrTempTemplate(request);

        return startResult;
    }

    /**
     * 发起成功后删除临时/草稿模板
     *
     * @param request
     */
    private void deleteDraftOrTempTemplate(ProcessStartBizRequest request) {
        if (!request.isDeleteTempFlowTemplate()) {
            return;
        }
        // 获取模板id
        String accountId = request.getAccountId();
        String tenantId = request.getTenantId();
        String flowTemplateId = request.getFlowTemplateId();
        String tempFlowTemplateId = request.getTempFlowTemplateId();
        // 直接发起删除草稿并清除最近草稿标识
        if (ProcessStartScene.DIRECT_START.getScene() == request.getStartScene()) {
            // 如果直接发起场景模板id不为空， 发起成功后尝试删除草稿
            if (StringUtils.isNotBlank(flowTemplateId)) {
                processDraftService.deleteDraft(flowTemplateId);
            }
            // 不管是否草稿发起，均清除最近草稿标识
            processDraftService.safeRemoveLatestDraftId(accountId, tenantId, null);
        }
        // 扫码发起/直接发起场景无需删除临时模板
        if (ProcessStartType.isShareScanStart(request.getStartType())
                || StringUtils.isAllBlank(flowTemplateId, tempFlowTemplateId)) {
            return;
        }
        // 延时删除临时flowTemplate
        // 前提：非扫码任务关联的流程模板， 流程模板一旦关联扫码任务， 在发起时无需删除模板
        // 1、预览发起场景，tempFlowTemplateId非空 需要删除
        // 2、草稿、直接发起场景-直接发起、指定位置发起等场景需要删除flowTemplateId
        String delFlowTemplateId =
                StringUtils.isNotBlank(tempFlowTemplateId) ? tempFlowTemplateId : flowTemplateId;
        if (!shareSignBaseService.checkIsShareTemplateId(delFlowTemplateId)) {
            deleteTempFlowTemplateProducer.sendMessage(delFlowTemplateId, tenantId);
        }
    }

    /** 判断是否支持审批模板 */
    public StartContractApprovalCheckResult preCheckStartParam(
            ProcessStartBizRequest request, ProcessStartContext context) {
        // 判断是否支持审批模板， 若不支持， 审批模板id直接返回空
        if (!context.getFunctionContext()
                .checkFunctionValid(FunctionCodeConstant.ORG_APPROVE_TEMPLATE_MANAGE, false)) {
            return new StartContractApprovalCheckResult();
        }
        // 如果合同审批模板不为空，且未指定发起部门id
        if (StringUtils.isNotBlank(request.getApproveTemplateId())
                && StringUtils.isBlank(request.getInitiatorDeptId())) {
            // 查询发起人默认部门id
            String initiatorDeptId =
                    processStartHelper.queryDefaultInitiatorDeptId(
                            context.getAccount().getAccountOid(),
                            context.getTenant().getAccountOid());
            request.setInitiatorDeptId(initiatorDeptId);
        }
        // 获取用于合同审批模板校验的流程模板id
        String flowTemplateId =
                StringUtils.isNotBlank(request.getOriginFlowTemplateId())
                        ? request.getOriginFlowTemplateId()
                        : parseFlowTemplateIdForCheck(request);
        // 扫码发起场景下， 无需校验合同审批模板有效性, 直接返回
        if (ProcessStartType.isShareScanStart(request.getStartType())) {
            StartContractApprovalCheckResult result = new StartContractApprovalCheckResult();
            result.setApprovalTemplateId(request.getApproveTemplateId());
            // 模板发起场景下的扫码发起
            if (ProcessStartType.SHARE_SCAN_TEMPLATE_START.getType().equals(request.getStartType())) {
                result.setApprovalTemplateConditionType(ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE.getCode());
                result.setApprovalTemplateConditionValue(flowTemplateId);
            } else {
                // 直接发起场景下的扫码发起
                result.setApprovalTemplateConditionType(ApprovalTemplateConditionTypeEnum.START_DIRECT.getCode());
            }
            return result;
        }
        // 组装合同审批模板校验参数
        StartContractApprovalCheckDTO param = new StartContractApprovalCheckDTO();
        param.setApprovalTemplateId(request.getApproveTemplateId());
        param.setFlowTemplateId(flowTemplateId);
        param.setAccountId(context.getAccount().getAccountOid());
        param.setAccountGid(context.getAccount().getAccountGid());
        param.setAccountName(context.getAccount().getAccountName());
        param.setAccountDeptId(request.getInitiatorDeptId());
        param.setSubjectId(context.getTenant().getAccountOid());
        param.setSubjectGid(context.getTenant().getAccountGid());
        param.setClientId(request.getClientId());

        return processStartHelper.preCheckStartParam(param);
    }

    /**
     * 获取用于合同审批模板校验的流程模板id
     * @param request
     * @return
     */
    private String parseFlowTemplateIdForCheck(ProcessStartBizRequest request) {
        // 模板发起场景下， 直接使用参数中传入的流程模板id, 跳过
        if (ProcessStartScene.TEMPLATE_START.getScene() == request.getStartScene()) {
            boolean isUnStandard = Boolean.TRUE.toString().equals(request.getExtension(CommonStartExtensionKeyConstants.UN_STANDARD_TEMPLATE_START));
            return isUnStandard ? request.getExtension(CommonStartExtensionKeyConstants.ORIGIN_FLOW_TEMPLATE_ID) : request.getFlowTemplateId();
        }
        // 直接发起且非重新发起场景下，不存在用于合同审批模板校验的流程模板id, 直接返回null
        if (!ProcessBusinessType.RESTART.getBusinessType().equals(request.getBusinessType())) {
            return null;
        }
        // 重新发起的直接发起场景下， 由于模板仅签的重新发起做了特殊处理，前端选择了原流程对应的模板发起合同审批模板id, 并通过直接发起的模式进行发起。
        // 因此针对这种场景， 校验合同审批模板时，需要判断原流程是否存在流程模板id, 如果存在，则优先获取原流程对应的流程模板id
        String originProcessId = request.getOriginProcessId();
        if (StringUtils.isNotBlank(originProcessId)) {
            // 优先获取流程配置中的合同审批模板条件值作为校验时使用的流程模板id
            ProcessConfigDO processConfigDO = baseProcessService.queryProcessConfig(originProcessId);
            ProcessConfigBean configBean = ProcessConfigConverter.convertProcessConfig(processConfigDO, true);
            if (null != configBean.getApprovalTemplateConditionType()) {
                return configBean.getApprovalTemplateConditionValue();
            }
            // 获取原流程对应的流程模板id
            String originFlowTemplateId = processTemplateService.getFlowTemplateId(originProcessId);
            // 如果原流程的流程模板id不为空， 优先使用当前流程模板id进行合同审批鉴权
            if (StringUtils.isNotBlank(originFlowTemplateId)) {
                return originFlowTemplateId;
            }
        }
        // 其他场景默认为普通直接发起的重新发起，返回null
        return null;
    }

    /**
     * 判断是否包含核验控件
     *
     * @param flowTemplateId
     * @return
     */
    public boolean hasQrCodeStruct(String flowTemplateId, UserAccountDetail tenant) {
        if (StringUtils.isBlank(flowTemplateId)) {
            return false;
        }
        FlowTemplateStructResult flowTemplateStructs =
                processStartContextService.getFlowTemplateStructs(flowTemplateId, tenant);
        if (flowTemplateStructs != null) {
            List<StructComponent> structComponents =
                    processStartContextService.parseStructComponents(flowTemplateStructs);
            return structComponents.stream()
                    .anyMatch(struct -> CHECK_QR_CODE.getType().equals(struct.getType()));
        }
        return false;
    }
}

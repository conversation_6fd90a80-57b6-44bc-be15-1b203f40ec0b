package com.timevale.contractmanager.core.service.processstart.factory;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.service.processstart.ProcessStartBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_START_SIGN_FLOW_FAIL;

/**
 * <AUTHOR>
 * @since 2021-11-12
 */
@Slf4j
@Service
public class ProcessStartBatchServiceFactory {

    @Autowired List<ProcessStartBatchService> processStartBatchServiceList;

    public ProcessStartBatchService getService(Integer startType) {
        log.info("factory-get-processStartBatchService");
        for (ProcessStartBatchService processStartBatchService : processStartBatchServiceList) {
            if (processStartBatchService.startTypes().stream().anyMatch(i ->i.getType().equals(startType))) {
                return processStartBatchService;
            }
        }
        throw new BizContractManagerException(PROCESS_START_SIGN_FLOW_FAIL, "暂不支持当前批量发起类型");
    }
}

package com.timevale.contractmanager.core.service.contractprocess;

import com.google.common.collect.Sets;
import com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO;
import com.timevale.contractmanager.common.dal.dao.ProcessDataCollectErrorMsgDAO;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.group.GroupService;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.producer.CommonProducer;
import com.timevale.contractmanager.core.service.redis.RedisService;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.log.MDCHelper;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSaveParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by tianlei on 2022/5/10
 */
@Slf4j
@Component
public class ProcessDataCollector {

    private static final String PROCESS_DATA_EXIST = "pDataCollect:exist:";
    private static final String PROCESS_DATA_EXIST_VALUE = "1";


    private static final String LOG_PREFIX = ProcessDataCollectSupport.LOG_PREFIX;

    private static final Map<String, String> SUCCESS_BIZ_CODE_LOG_MAP = new HashMap<>();

    private static final Set<String> WARN_BIZ_CODE = Sets.newHashSet(
            BizContractManagerResultCodeEnum.PROCESS_PARTICIPANT_INFO_NOT_EXIST.getCode()
    );

    {
        SUCCESS_BIZ_CODE_LOG_MAP.put(BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST.getCode(), " process not exist");
        SUCCESS_BIZ_CODE_LOG_MAP.put(BizContractManagerResultCodeEnum.COOPERATION_FLOW_NOT_EXIST.getCode(),
                " cooperation not exist");
        SUCCESS_BIZ_CODE_LOG_MAP.put(BizContractManagerResultCodeEnum.SUB_PROCESS_NOT_EXIST.getCode(), " sub process not exist");
        SUCCESS_BIZ_CODE_LOG_MAP.put(BizContractManagerResultCodeEnum.USER_ACCOUNT_NOT_EXIST.getCode(), "account notExist");

    }


    private static final Map<String, ProcessDataCollectProcessor> PROCESSOR_MAP = new HashMap<>();

    public ProcessDataCollector(@Autowired List<ProcessDataCollectProcessor> processorList, @Autowired Environment environment) {
        if (CollectionUtils.isNotEmpty(processorList)) {
            for (ProcessDataCollectProcessor processor : processorList) {
                Route route = processor.route();
                String topic = route.getTopic();
                log.info(LOG_PREFIX + "init collector topic : {} tag : {} processor: {}", topic, route.getTag(), processor);
                PROCESSOR_MAP.put(routeKey(topic, route.getTag()), processor);
            }
        }
    }

    @Autowired
    private RedisService redisService;
    @Autowired
    private ContractProcessReadClient queryClient;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ProcessDataCollectConfigCenter configCenter;
    @Autowired
    private LockService lockService;
    @Autowired
    private ProcessDataCollectErrorMsgDAO errorMsgDAO;
    @Autowired
    private CommonProducer commonProducer;
    @Autowired
    private GroupService groupService;

    public boolean collect(ProcessDataCollectMsg msg) {
        boolean shouldSetTrackId = StringUtils.isBlank(MDCHelper.getTrackId());
        try {
            if (shouldSetTrackId) {
                MDCHelper.setTrackId(UUIDUtil.genUUID());
            }
            return innerCollect(msg);
        } finally {
            if (shouldSetTrackId) {
                MDCHelper.clear();
            }
        }
    }

    public boolean innerCollect(ProcessDataCollectMsg msg) {

        log.info(LOG_PREFIX + "start msgId : {} shadow: {} topic : {} tag : {} retryTimes : {} msg : {} group : {}",
                msg.getMsgId(), msg.isShadowTopic(), msg.getTopic(), msg.getTag(), msg.getReconsumeTimes(), msg.getMsg(), msg.getConsumerGroup());

        ProcessDataCollectProcessor collectProcessor = get(msg.getTopic(), msg.getTag());
        if (null == collectProcessor) {
            processNotExistPrintLog(msg);
            return true;
        }

        // 设置是否达到最大重试次数
        msg.setToMaxRetryCount(configCenter.maxReconsumeTimes() != null &&
                msg.getReconsumeTimes() >= configCenter.maxReconsumeTimes());

        DataAnalysisResult result;
        try {
            result = collectProcessor.dataAnalysis(msg.getMsg());
        } catch (Exception e) {
            log.warn(LOG_PREFIX + "biz exception dataAnalysis : {}", msg.getMsg(), e);
            return msg.isToMaxRetryCount();
        }
        String processId = result.getProcessId();
        if (StringUtils.isEmpty(processId)) {
            log.info(LOG_PREFIX + "processId is null,result: {}", JsonUtils.obj2json(result));
            return true;
        }
        // 构建处理上下文
        ProcessDataCollectContext dataCollectContext = new ProcessDataCollectContext();
        dataCollectContext.setMsg(msg);
        dataCollectContext.setProcessId(processId);
        dataCollectContext.setData(result.getData());

        if (Objects.equals(processId, configCenter.skipCollectProcessId())) {
            log.info(LOG_PREFIX + "develop skip processId : {}", processId);
            return true;
        }

        try {
            doCollect(dataCollectContext, collectProcessor);
            return true;
        } catch (HbaseProcessDataAsyncCollectException e) {
            if (msg.isToMaxRetryCount()) {
                saveErrorMsg(msg, processId);
                return true;
            }
            return false;
        } catch (Exception e) {
            if (e instanceof BizContractManagerException && WARN_BIZ_CODE.contains(((BizContractManagerException) e).getCode())) {
                log.warn(LOG_PREFIX + "biz exception processId : {}", result.getProcessId(), e);
            } else {
                if (msg.getReconsumeTimes() > configCenter.printErrorReconsumeTimes()) {
                    log.error(LOG_PREFIX + "unknown exception processId : {}", result.getProcessId(), e);
                } else {
                    log.warn(LOG_PREFIX + "unknown exception processId : {}", result.getProcessId(), e);
                }
            }
            if (msg.isToMaxRetryCount()) {
                saveErrorMsg(msg, processId);
                return true;
            }
            return false;
        }

    }

//    public void retryCollect(ProcessDataCollectErrorMsgDO errorMsgDO) {
//
//        String topic = errorMsgDO.getTopic();
//        String tag = errorMsgDO.getTag();
//
//        log.info(LOG_PREFIX + "begin retry topic : {} tag : {} msg : {}", topic, tag, errorMsgDO.getContent());
//        ProcessDataCollectProcessor collectProcessor = get(topic, tag);
//        if (null == collectProcessor) {
//            log.error(LOG_PREFIX + "retry processor not exist topic: {} tag: {}", topic, tag);
//            return;
//        }
//
//        // 解析消息
//        DataAnalysisResult result = collectProcessor.dataAnalysis(errorMsgDO.getContent());
//        String processId = result.getProcessId();
//
//        ProcessDataCollectMsg msg = new ProcessDataCollectMsg();
//        // 上线文
//        ProcessDataCollectContext dataCollectContext = new ProcessDataCollectContext();
//        dataCollectContext.setMsg(msg);
//        dataCollectContext.setProcessId(processId);
//        dataCollectContext.setData(result.getData());
//        doCollect(dataCollectContext, collectProcessor);
//    }

    private void saveErrorMsg(ProcessDataCollectMsg msg, String processId) {
        if (msg.isShadowTopic()) {
            // 影子topic不保存错误数据
            log.warn(LOG_PREFIX + "shadow topic failure msgId: {} processId : {}", msg.getMsgId(), processId);
            return;
        }
        try {
            ProcessDataCollectErrorMsgDO errorMsgDO = new ProcessDataCollectErrorMsgDO();
            errorMsgDO.setContent(msg.getMsg());
            errorMsgDO.setTopic(msg.getTopic());
            errorMsgDO.setTag(Optional.ofNullable(msg.getTag()).orElse(""));
            errorMsgDO.setProcessId(processId);
            errorMsgDAO.save(errorMsgDO);
        } catch (Exception e) {
            log.error(LOG_PREFIX + "save error msg failure", e);
        }
    }

    private void doCollect(ProcessDataCollectContext dataCollectContext, ProcessDataCollectProcessor collectProcessor) {

        //把消息数据解析出来
        String processId = dataCollectContext.getProcessId();

        String existCacheKey = processExistCacheKey(processId);

        // 数据已存在
        if (null != redisService.get(existCacheKey) || existInDB(processId, existCacheKey)) {
            // 数据已经存在
            if (collectProcessor.processDataExistContinueProcess()) {
                // 数据存在需要继续处理
                try {
                    // 进行业务处理
                    collectProcessor.process(dataCollectContext);
                    // 消息处理完后置处理
                    collectProcessor.postProcessAfter(dataCollectContext);
                } catch (BizContractManagerException e) {
                    handleBizException(e, processId);
                }
            }
            return;
        }

        // 数据不存在
        try {
            String lockName = ProcessDataCollectSupport.initDataLockKey(processId);
            Lock elock = lockService.getLock(lockName);
            if (elock.tryLock(1, TimeUnit.SECONDS)) {
                try {
                    // double check 防止并发初始化 eg: 一个消息初始化完后，有更新数据，另外一个初始化，把它覆盖掉了
                    boolean exist = queryClient.getByProcessId(processId) != null;
                    if (!exist) {
                        // 先进行数据构建
                        ContractProcessSaveParam saveInput = processDataBuilder.buildProcessInfoParam(processId);
                        if (null == saveInput) {
                            log.warn(LOG_PREFIX + "sub process data not exist");
                            return;
                        }
                        if (null == saveInput.getOperateClientType()) {

                            if (dataCollectContext.getMsg().isToMaxRetryCount()) {
                                log.info(ProcessDataCollectSupport.LOG_PREFIX + " operateClientType isNull maxRetry : {}", processId);

                            } else {
                                log.info(ProcessDataCollectSupport.LOG_PREFIX + " operateClientType isNull  : {}", processId);
                            }
                            throw new HbaseProcessDataAsyncCollectException();
                        }

                        saveInput.setBizScene(ProcessDataCollectBizSceneConstants.DATA_INIT);
                        contractProcessWriteClient.contractProcessSave(saveInput);
                        log.info(ProcessDataCollectSupport.LOG_PREFIX + "init process success : {}", processId);
                        // 数据存在缓存30分钟
                        setProcessExistCache(existCacheKey);
                        // 发送数据初始化消息
                        commonProducer.sendShardingMessage(ProcessBuildDataTopicConstants.TOPIC,
                                ProcessBuildDataTopicConstants.INIT_TAG, new ProcessBuildDataMsg(processId), processId);
                    }

                } finally {
                    elock.unlock();
                }
                // 刚构建数据之后是否继续往下处理
                if (collectProcessor.initProcessDataAfterContinueProcess()) {
                    // 进行业务处理
                    collectProcessor.process(dataCollectContext);
                }

                // 消息处理完后置处理
                collectProcessor.postProcessAfter(dataCollectContext);

            } else {
                log.info(LOG_PREFIX + "init data get lock failure processId : {}", processId);
                throw new HbaseProcessDataAsyncCollectException();
            }
        } catch (BizContractManagerException e) {
            handleBizException(e, processId);
        }
    }

    private void handleBizException(BizContractManagerException e, String processId) {
        String logMsg = SUCCESS_BIZ_CODE_LOG_MAP.get(e.getCode());
        if (StringUtils.isNotBlank(logMsg)) {
            log.warn(LOG_PREFIX + "biz success, processId : {}" + logMsg, processId, e);
        } else {
            throw e;
        }
    }

    private boolean existInDB(String processId, String existCacheKey) {
        boolean exist = queryClient.getByProcessId(processId) != null;
        if (exist) {
            setProcessExistCache(existCacheKey);
        }
        return exist;
    }

    private void setProcessExistCache(String existCacheKey) {
        redisService.set(existCacheKey, PROCESS_DATA_EXIST_VALUE, 30, TimeUnit.MINUTES);
    }

    private ProcessDataCollectProcessor get(String topic, String tag) {
        return PROCESSOR_MAP.get(routeKey(topic, tag));
    }

    private static String processExistCacheKey(String processId) {
        return PROCESS_DATA_EXIST + processId;
    }

    public static String routeKey(String topic, String tag) {
        return topic + "," + tag;
    }

    public void saveProcessGroup(String groupId, Integer source) {
        ContractProcessSaveParam param = processDataBuilder.buildTempGroupInfoParam(groupId, source);
        if (param == null) {
            log.warn(LOG_PREFIX + " temp group is data is null groupId : {}", groupId);
            return;
        }
        param.setBizScene(ProcessDataCollectBizSceneConstants.TEMP_GROUP);
        contractProcessWriteClient.contractProcessSave(param);
    }


    public void deleteProcessGroup(String groupId) {
        String falseProcessId = processDataBuilder.genGroupProcessId(groupId);
        contractProcessWriteClient.deleteProcessById(falseProcessId);
    }

    private void processNotExistPrintLog(ProcessDataCollectMsg msg) {
        Set<String> notPrintTagSet = configCenter.processNotExitNotPrintLog();
        if (CollectionUtils.isEmpty(notPrintTagSet)) {
            printLog(msg);
            return;
        }
        String checkTag = msg.getTopic() + "#" + Optional.ofNullable(msg.getTag()).orElse("");
        if (notPrintTagSet.contains(checkTag)) {
            // not log
            return;
        }
        printLog(msg);
    }

    private void printLog(ProcessDataCollectMsg msg) {
        log.error(LOG_PREFIX + "processor not exist topic: {} tag: {}", msg.getTopic(), msg.getTag());
    }

    public void truncateProcessGroup(String groupId) {
        log.info(LOG_PREFIX + " truncate process group groupId: {}", groupId);
        // 删除合同组es数据
        this.deleteProcessGroup(groupId);
        groupService.clearProcessGroup(groupId);
    }
}

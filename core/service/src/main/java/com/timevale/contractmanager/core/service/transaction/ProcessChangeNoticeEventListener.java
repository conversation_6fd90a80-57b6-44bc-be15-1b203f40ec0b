package com.timevale.contractmanager.core.service.transaction;

import com.timevale.contractmanager.core.service.mq.model.ProcessChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ProcessChangeProducer;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Collection;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class ProcessChangeNoticeEventListener {

    @Autowired ProcessChangeProducer producer;

    @TransactionalEventListener(fallbackExecution = true, phase = TransactionPhase.AFTER_COMMIT)
    public void handleProcessChangeEvent(ProcessChangeNoticeEvent event) {
        Collection<String> processIds = event.getProcessIds();
        String changeType = event.getChangeType();
        if (CollectionUtils.isEmpty(processIds) || StringUtils.isBlank(changeType)) {
            return;
        }
        log.debug("process changed, event: {}", JsonUtils.obj2json(event));
        // 判断是否异步发送通知
        if (!event.isAsyncNotice()) {
            // 发送合同流程变更消息
            sendNotice(processIds, changeType);
        } else {
            CompletableFuture.runAsync(() -> sendNotice(processIds, changeType));
        }
    }

    /**
     * 发送合同流程变更消息
     *
     * @param processIds
     * @param changeType
     */
    private void sendNotice(Collection<String> processIds, String changeType) {
        // 发送合同流程变更消息
        processIds.forEach(i -> producer.sendMessage(new ProcessChangeMsgEntity(i), changeType));
    }
}

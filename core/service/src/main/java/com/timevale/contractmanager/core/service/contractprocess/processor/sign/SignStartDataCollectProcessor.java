package com.timevale.contractmanager.core.service.contractprocess.processor.sign;

import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.dao.ProcessDAO;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.HbaseProcessDataAsyncCollectException;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ContractProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.enums.SignChangeTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.ProcessChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.model.SignChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ContractProcessChangeProducer;
import com.timevale.contractmanager.core.service.mq.producer.ProcessChangeProducer;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessSignTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by tianlei on 2022/5/11
 */
@Slf4j
@Component
public class SignStartDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient processWriteClient;
    @Autowired
    private ContractProcessReadClient processQueryClient;
    @Autowired
    private ProcessDAO processDAO;
    @Autowired
    private LockService lockService;
    @Autowired
    private ProcessChangeProducer producer;
    @Autowired
    private ContractProcessChangeProducer contractProcessChangeProducer;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.signTopicName(), SignChangeTagEnum.SIGN_START.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        SignChangeMsgEntity entity =
                JsonUtils.json2pojo(data, SignChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        Lock lock = lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                doProcess(collectContext);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }

    @Override
    public void postProcessAfter(ProcessDataCollectContext collectContext) {
        if (Boolean.TRUE.equals(dataCollectConfigCenter.newSelfStartMsgSwitch())) {
            ProcessChangeMsgEntity processChangeMsgEntity = new ProcessChangeMsgEntity();
            processChangeMsgEntity.setProcessId(collectContext.getProcessId());
            contractProcessChangeProducer.sendMessageDelay(processChangeMsgEntity, ContractProcessChangeTagEnum.SIGN_START.getTag());
        }
    }

    private void doProcess(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);

        SignChangeMsgEntity signChangeMsgEntity = (SignChangeMsgEntity) collectContext.getData();
        // 需要注意有  sign_task_change  消息 先与 sing_start  消息来的场景
        //下面构建签署任务 强依赖process状态
        // todo 要确认下 sign_start消息来 是否，process 状态是否变了
        ProcessDO processDO = processDAO.getByIdxProcessId(processId);
        if (null == processDO) {
            // process deleted = 0 目前是footstone 删除了，现在看是个异常流程，这里主要是为了防止报错空指针
            log.warn("process data not exist processId : {}", processId);
            return;
        }

        List<ContractProcessSignTaskDTO> oldSignTaskDTOS = contractProcessDTO.getSignTasks();
        List<ContractProcessSignTaskParam> newSignTaskDTOS =
                processDataBuilder.buildProcessAllSignTaskList(processId, processDO.getStatus());
        if (CollectionUtils.isEmpty(oldSignTaskDTOS)) {
            // 签署数据不存在
            // 构建所有签署数据, 进行更新
            ContractProcessUpdateParam input = new ContractProcessUpdateParam();
            input.setProcessId(processId);
            input.setSignTasks(newSignTaskDTOS);
            input.setSignFlowId(signChangeMsgEntity.getFlowId());
            input.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_START);
            processWriteClient.updateByProcessId(input);

        } else {
            // 签署数据存在，进行替换
            // 有签有填的场景 初始化数据的时候需要构建假的签署 task
            Map<String, ContractProcessSignTaskDTO> oidDataMap = new HashMap<>();
            Map<String, ContractProcessSignTaskDTO> gidDataMap = new HashMap<>();
            for (ContractProcessSignTaskDTO oldSignTaskDTO : oldSignTaskDTOS) {
                if (oldSignTaskDTO.getExecute() == null ||
                        oldSignTaskDTO.getExecute().getPerson() == null ||
                        oldSignTaskDTO.getExecute().getSubject() == null) {
                    log.error(LOG_PREFIX + "sign start data miss processId : {} error {}",
                            processId, JsonUtils.obj2json(oldSignTaskDTO));
                    continue;
                }
                Account account = oldSignTaskDTO.getExecute().getPerson();
                Account tenant = oldSignTaskDTO.getExecute().getSubject();
                // 注意这里 key 是个人id + 主题gid构成的
                oidDataMap.put(getUk(account.getOid(), tenant.getGid()), oldSignTaskDTO);
                if (StringUtils.isNotBlank(account.getGid())) {
                    gidDataMap.put(getUk(account.getGid(), tenant.getGid()), oldSignTaskDTO);
                }
            }

            // 遍历新数据
            for (ContractProcessSignTaskParam newSignParam : newSignTaskDTOS) {
                if (newSignParam.getExecute() == null ||
                        newSignParam.getExecute().getPerson() == null ||
                        newSignParam.getExecute().getTenant() == null) {
                    log.error(LOG_PREFIX + "sign start data processId : {} error {}",
                            processId, JsonUtils.obj2json(newSignParam));
                    continue;
                }
                String oid = newSignParam.getExecute().getPerson().getOid();
                String gid = newSignParam.getExecute().getPerson().getGid();

                String tenantGid = newSignParam.getExecute().getTenant().getGid();

                // 为什么要保留 转交, 因为 先填后签的场景，未填写完时，签署是购造的假task, 这个时候可能会发生转交
                ContractProcessSignTaskDTO oldSignTaskDTO = oidDataMap.get(getUk(oid, tenantGid));
                if (oldSignTaskDTO != null) {
                    newSignParam.setTransfer(oldSignTaskDTO.getTransfer());
                } else if (StringUtils.isNotBlank(gid) && (oldSignTaskDTO = gidDataMap.get(getUk(gid, tenantGid))) != null) {
                    newSignParam.setTransfer(oldSignTaskDTO.getTransfer());
                }
            }

            // 更新数据
            ContractProcessUpdateParam param = new ContractProcessUpdateParam();
            param.setProcessId(processId);
            param.setSignTasks(newSignTaskDTOS);
            param.setSignFlowId(signChangeMsgEntity.getFlowId());
            param.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_START);
            processWriteClient.updateByProcessId(param);
        }

        if (null == contractProcessDTO.getOperateClientType() ||
                Integer.valueOf(ProcessStatusEnum.INIT.getStatus()).equals(contractProcessDTO.getProcessStatus())) {
            // 如果source为 null 发送一天辅助消息
            ProcessChangeMsgEntity processChangeMsgEntity = new ProcessChangeMsgEntity();
            processChangeMsgEntity.setProcessId(collectContext.getProcessId());
            processChangeMsgEntity.setHelpMsg(true);
            producer.sendMessage(processChangeMsgEntity, ProcessChangeTagEnum.PROCESS_INFO_CHANGE.getTag());
        }

    }

    private static String getUk(String id, String tenantGid) {
        return id + ":" + tenantGid;
    }
}
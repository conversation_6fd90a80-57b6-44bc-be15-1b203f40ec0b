package com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.impl;

import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.contractmanager.common.service.enums.opponent.AuthorizeTypeEnum;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionProblemEnum;
import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.DetectionHandler;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Author:jianyang
 * @since 2021-08-17 18:04
 */
@Slf4j
@Component
public class OrgNameDetectionHandler implements DetectionHandler {

	@Autowired
	private SensorService sensorService;

	public static final Integer DETECTION_NO = 2;

	public static final String DESC = "未查询到名称";

	/**
	 * 名称是否存在检测
	 * @param chainBO
	 * @param chainResults
	 * @return
	 */
	@Override
	public List<OpponentDetectionChainResultBO> handler(DetectionChainBO chainBO, List<OpponentDetectionChainResultBO> chainResults) {
		log.info("开始相对方检测(名称是否存在) taskId:{} taskType:{} tenantGid:{} orgName:{}",
				chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
		if(Objects.equals(chainBO.getEntity().getAuthorizeType(), AuthorizeTypeEnum.INIT.name())
				|| Objects.equals(AuthorizeTypeEnum.AUTHENTICATING.getType(),chainBO.getEntity().getAuthorizeType())){
			if(chainBO.getDetail() == null || StringUtils.isBlank(chainBO.getDetail().getName())){
				OpponentDetectionChainResultBO chainResultBO = new OpponentDetectionChainResultBO();
				chainResultBO.setOrgName(chainResultBO.getOrgName());
				chainResultBO.setProblemDesc(DESC);
				chainResultBO.setRiskLevel(OpponentDetectionProblemEnum.ORG_NAME_NOT_EXIST.getProblemRiskLevel());
				chainResultBO.setProblemNo(OpponentDetectionProblemEnum.ORG_NAME_NOT_EXIST.getProblemNo());
				chainResultBO.setProblemNo(DETECTION_NO);
				chainResultBO.setSuggestDesc(OpponentDetectionProblemEnum.ORG_NAME_NOT_EXIST.getSuggestDesc());
				chainResults.add(chainResultBO);
				log.info("相对方检测(未查询到名称) taskId:{} taskType:{} tenantGid:{} orgName:{}",
						chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
				sensorService.opponentDetectionProblemReport(
						chainBO.getTenantGid(), chainBO.getOrgName(),
						OpponentDetectionProblemEnum.ORG_NAME_NOT_EXIST.getProblemNo());
			}
		}
		return chainResults;
	}

	@Override
	public boolean filter(List<Integer> items) {
		if(items.contains(DETECTION_NO)){
			return true;
		}
		return false;
	}
}

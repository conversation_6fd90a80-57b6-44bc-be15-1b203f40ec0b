package com.timevale.contractmanager.core.service.transfer.impl;

import com.alibaba.fastjson.JSONObject;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.model.bo.transfer.SingleTransferResultBO;
import com.timevale.contractmanager.core.model.bo.transfer.TransferUserListBO;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.transfer.TransferResultDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.transfer.ApprovalTransferAbstractBizService;
import com.timevale.contractmanager.core.service.transfer.TransferBizService;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;
import lombok.extern.slf4j.Slf4j;

import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.TRANSFER_BIZ_SERVICE_NON_EXIST;

/**
 * <AUTHOR>
 * @since 2023/7/24 合同审批转交业务类
 */
@Slf4j
@Service("transferContractApproveBizServiceImpl")
public class TransferContractApproveBizServiceImpl extends ApprovalTransferAbstractBizService
        implements TransferBizService {

    @Override
    public Boolean isSupport(Integer transferScene) {
        return Objects.equals(TransferSceneEnum.CONTRACT_APPROVE.getCode(), transferScene);
    }

    @Override
    public TransferResultDTO transfer(TransferBizContext transferBizContext) {
        if (Objects.isNull(transferBizContext)
                || Objects.isNull(transferBizContext.getTransferUserInfo())) {
            return new TransferResultDTO(false);
        }
        return approvalTransfer(transferBizContext, TransferSceneEnum.CONTRACT_APPROVE);
    }

    @Override
    public Map<String, Long> transferCount(
            UserAccountDetail tenantAccount, List<Account> userAccounts) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }

    @Override
    public OrgDeptListResponse transferUserList(TransferUserListBO transferUserListBO) {
        return super.approvalTransferUserList(transferUserListBO);
    }

    @Override
    public Integer transferToUserCount(String tenantId, String accountOid) {
        return super.transferToUserCount(tenantId, accountOid, TransferSceneEnum.CONTRACT_APPROVE);
    }

    @Override
    public Long addTransferToUserCount(String tenantId, String accountOid) {
        return super.addTransferToUserCount(tenantId, accountOid, TransferSceneEnum.CONTRACT_APPROVE);
    }

    @Override
    public void singleTransferResultCallback(SingleTransferResultBO transferResultBO) {
        log.info("contract approval singleTransferResultCallback, param: {}", JSONObject.toJSONString(transferResultBO));
        super.approvalTransferResultCallback(transferResultBO, TransferSceneEnum.CONTRACT_APPROVE);
    }

    @Override
    protected ApprovalTypeEnum approvalType() {
        return ApprovalTypeEnum.CONTRACT;
    }

    @Override
    protected String transferReason(TransferBizContext transferBizContext) {
        return transferBizContext.getContractApprovalTransferReason();
    }

    @Override
    protected String transferFailReason(long successCount, long totalCount) {
        if (successCount == 0) {
            return String.format("合同审批全部转交失败，失败原因可能是：被转交的审批流已被审批、撤回或拒绝");
        }
        return String.format("已成功转交%s条合同审批任务，转交失败%s条，失败原因可能是：被转交的审批流已被审批、撤回或拒绝", successCount, totalCount - successCount);
    }

    @Override
    protected void setCustomizeTransferCache(TransferBizContext transferBizContext) {
        // 合同审批转交无自定义缓存逻辑
    }

    @Override
    protected void clearCustomizeTransferCache(TransferBizContext transferBizContext) {
        // 合同审批转交无自定义缓存逻辑
    }

    @Override
    protected void customizeTransferCompleteCallback(SingleTransferResultBO transferResultBO, long successCount) {
        // 合同审批转交无自定义转交完成回调逻辑
    }

    @Override
    protected List<String> partTransferApprovalIds(TransferBizContext transferBizContext) {
        TransferBizContext.TransferApprovalInfo transferContractApprovalInfo = transferBizContext.getTransferContractApprovalInfo();
        if (null == transferContractApprovalInfo || CollectionUtils.isEmpty(transferContractApprovalInfo.getTransferApprovalIdList())) {
            return Lists.newArrayList();
        }
        return transferContractApprovalInfo.getTransferApprovalIdList();
    }
}

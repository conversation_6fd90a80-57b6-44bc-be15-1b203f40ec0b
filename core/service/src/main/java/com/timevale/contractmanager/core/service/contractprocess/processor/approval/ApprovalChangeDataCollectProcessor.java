package com.timevale.contractmanager.core.service.contractprocess.processor.approval;

import com.timevale.contractapproval.facade.message.ApprovalMessage;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.contractprocess.processor.approval.bean.ApprovalDoProcessParam;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ApprovalChangeDataCollectProcessor
 *
 * <AUTHOR>
 * @since 2024-01-12
 */
@Slf4j
@Component
public class ApprovalChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired private ApprovalComponent approvalComponent;
    @Autowired private ProcessDataCollectConfigCenter dataCollectConfigCenter;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.getApprovalChangeTopicName(), null);
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ApprovalMessage entity = JsonUtils.json2pojo(data, ApprovalMessage.class);
        return new DataAnalysisResult(entity.getBizId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return false;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        ApprovalMessage entity = (ApprovalMessage) collectContext.getData();
        ApprovalDoProcessParam param = new ApprovalDoProcessParam();
        param.setApprovalCode(entity.getApprovalCode());
        param.setApprovalType(entity.getApprovalType());
        param.setStandardPlatformStarted(entity.isStandardPlatformStarted());
        param.setBizGroupId(entity.getBizGroupId());
        approvalComponent.doProcess(collectContext, param);
    }
}

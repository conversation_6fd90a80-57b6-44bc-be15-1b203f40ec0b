package com.timevale.contractmanager.core.service.watermark.impl;

import com.timevale.contractmanager.core.service.watermark.WatermarkStrategy;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkContentTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkTypeEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 初霁
 * @version V1.0 @Project: contract-manager-project @Description: 文件水印--
 * @date Date : 2023年04月23日 14:47
 */
@Service
public class DocWordWatermarkStrategy extends DocCommonWatermarkStrategy {
    @Override
    public String getType() {
        return WatermarkStrategy.assembleServiceId(WatermarkTypeEnum.DOC_WATERMARK, WatermarkContentTypeEnum.NORMAL_WORD);
    }
}

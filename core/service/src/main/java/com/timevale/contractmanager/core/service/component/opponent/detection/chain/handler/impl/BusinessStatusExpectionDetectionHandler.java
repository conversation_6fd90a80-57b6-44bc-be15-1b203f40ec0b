package com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.impl;

import com.google.common.collect.Lists;
import com.netflix.eureka.registry.Key;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.contractanalysis.facade.api.enums.EnterpriseTypeEnum;
import com.timevale.contractanalysis.facade.api.enums.OperatingStatus;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionProblemEnum;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionSwitchEnum;
import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.DetectionHandler;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.Format;
import java.util.List;
import java.util.Objects;

/**
 * @Author:jianyang
 * @since 2021-08-19 16:34
 */
@Component
@Slf4j
public class BusinessStatusExpectionDetectionHandler implements DetectionHandler {

	@Autowired
	private SensorService sensorService;

	public static final Integer RISK_DETECTION_NO = 4;
	public static final Integer EXPECTION_DETECTION_NO = 3;

	public static final String DESC = "经营状态:%s";

	/**
	 * 经营状态检测
	 * @param chainBO
	 * @param chainResults
	 * @return
	 */
	@Override
	public List<OpponentDetectionChainResultBO> handler(DetectionChainBO chainBO, List<OpponentDetectionChainResultBO> chainResults) {
		if(chainBO.getDetail() == null || !chainBO.getDetail().getEntityType().equals(EnterpriseTypeEnum.MAINLAND.getType())){
			return chainResults;
		}
		/* 异常经营状态 */
		List<String> businessStatusExpectionList = Lists.newArrayList();
		businessStatusExpectionList.add(OperatingStatus.CLOSED.getStatus());
		businessStatusExpectionList.add(OperatingStatus.CANCEL.getStatus());
		businessStatusExpectionList.add(OperatingStatus.REVOKED.getStatus());
		businessStatusExpectionList.add(OperatingStatus.CLEAR.getStatus());
		/* 有风险的经营状态 */
		List<String> businessStatusRiskList = Lists.newArrayList();
		businessStatusRiskList.add(OperatingStatus.TRANSFER_IN.getStatus());
		businessStatusRiskList.add(OperatingStatus.TRANSFER_OUT.getStatus());
		log.info("开始相对方检测(经营状态检测) taskId:{} taskType:{} tenantGid:{} orgName:{}",
				chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());


		String businessStatus = chainBO.getDetail().getStatus();



		if(businessStatusExpectionList.contains(businessStatus)){
			OpponentDetectionChainResultBO chainResultBO = new OpponentDetectionChainResultBO();
			chainResultBO.setOrgName(chainResultBO.getOrgName());
			chainResultBO.setProblemNo(EXPECTION_DETECTION_NO);
			chainResultBO.setProblemDesc(String.format(DESC,OperatingStatus.getDescByStatus(businessStatus)));
			chainResultBO.setRiskLevel(OpponentDetectionProblemEnum.MANAGEMENT_FORMS_EXCEPTION.getProblemRiskLevel());
			chainResultBO.setProblemNo(OpponentDetectionProblemEnum.MANAGEMENT_FORMS_EXCEPTION.getProblemNo());
			chainResultBO.setSuggestDesc(OpponentDetectionProblemEnum.MANAGEMENT_FORMS_EXCEPTION.getSuggestDesc());
			chainResults.add(chainResultBO);
			log.info("相对方检测经营状态为停业、吊销、清算或注销 taskId:{} taskType:{} tenantGid:{} orgName:{}",
					chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
			sensorService.opponentDetectionProblemReport(
					chainBO.getTenantGid(), chainBO.getOrgName(),
					OpponentDetectionProblemEnum.MANAGEMENT_FORMS_EXCEPTION.getProblemNo());
		}

		if(businessStatusRiskList.contains(businessStatus)){
			OpponentDetectionChainResultBO chainResultBO = new OpponentDetectionChainResultBO();
			chainResultBO.setOrgName(chainResultBO.getOrgName());
			chainResultBO.setProblemNo(RISK_DETECTION_NO);
			chainResultBO.setProblemDesc(String.format(DESC,OperatingStatus.getDescByStatus(businessStatus)));
			chainResultBO.setRiskLevel(OpponentDetectionProblemEnum.MANAGEMENT_FORMS_CHANGE.getProblemRiskLevel());
			chainResultBO.setProblemNo(OpponentDetectionProblemEnum.MANAGEMENT_FORMS_CHANGE.getProblemNo());
			chainResultBO.setSuggestDesc(OpponentDetectionProblemEnum.MANAGEMENT_FORMS_CHANGE.getSuggestDesc());
			chainResults.add(chainResultBO);
			log.info("相对方检测经营状态为迁入、迁出 taskId:{} taskType:{} tenantGid:{} orgName:{}",
					chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
			sensorService.opponentDetectionProblemReport(
					chainBO.getTenantGid(), chainBO.getOrgName(),
					OpponentDetectionProblemEnum.MANAGEMENT_FORMS_CHANGE.getProblemNo());
		}

		return chainResults;
	}

	@Override
	public boolean filter(List<Integer> items) {
		if(items.contains(RISK_DETECTION_NO) || items.contains(EXPECTION_DETECTION_NO)){
			return true;
		}
		return false;
	}
}

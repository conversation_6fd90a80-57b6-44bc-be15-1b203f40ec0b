package com.timevale.contractmanager.core.service.fulfillment.converter;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.dal.bean.fulfillment.ContractFulfillmentRuleDO;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentScopeEnum;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentNoticeRuleModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleListModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentShardingRuleListModel;
import com.timevale.contractmanager.common.service.model.rule.RuleConditionModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleDetailResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ContractFulfillmentRuleConverter
 *
 * <AUTHOR>
 * @since 2023/10/12 11:42 上午
 */
public class ContractFulfillmentRuleConverter {

    public static  List<ContractFulfillmentRuleListModel> convertRuleModelList(List<ContractFulfillmentRuleDO> ruleDOList, List<RuleConditionModel> ruleConditionModels){
        if(CollectionUtils.isEmpty(ruleDOList)){
            return new ArrayList<>();
        }
        Map<String, List<RuleConditionModel>> ruleConditionMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(ruleConditionModels)){
            ruleConditionMap = ruleConditionModels.stream().collect(Collectors.groupingBy(RuleConditionModel::getRuleId, LinkedHashMap::new, Collectors.toList()));
        }
        List<ContractFulfillmentRuleListModel> ruleListModelList = new ArrayList<>();
        for(ContractFulfillmentRuleDO ruleDO : ruleDOList){
            ContractFulfillmentRuleListModel ruleListModel = new ContractFulfillmentRuleListModel();
            ruleListModel.setRuleId(ruleDO.getRuleId());
            ruleListModel.setType(ruleDO.getType());
            ruleListModel.setTypeName(ruleDO.getTypeName());
            ruleListModel.setName(ruleDO.getName());
            ruleListModel.setFormId(ruleDO.getFormId());
            ruleListModel.setScopeType(ruleDO.getScopeType());
            ruleListModel.setStatus(ruleDO.getStatus());
            if(FulfillmentScopeEnum.FORCE.getType().equals(ruleDO.getScopeType())){
                ruleListModel.setScopeConditions(ruleConditionMap.get(ruleDO.getRuleId()));
            }
            ruleListModel.setNoticeRule(JSON.parseObject(ruleDO.getNoticeRule(), ContractFulfillmentNoticeRuleModel.class));
            if(StringUtils.isNotEmpty(ruleDO.getNoticeChannel())){
                ruleListModel.setNoticeChannels(JSON.parseArray(ruleDO.getNoticeChannel(), String.class));
            }
            ruleListModel.setCreateByOid(ruleDO.getCreateByOid());
            ruleListModel.setModifiedByOid(ruleDO.getModifiedByOid());
            ruleListModel.setCreateTime(ruleDO.getCreateTime());
            ruleListModel.setModifiedTime(ruleDO.getModifiedTime());
            ruleListModelList.add(ruleListModel);
        }
        return ruleListModelList;
    }

    public static  List<ContractFulfillmentShardingRuleListModel> convertShardingRuleModelList(List<ContractFulfillmentRuleDO> ruleDOList){
        if(CollectionUtils.isEmpty(ruleDOList)){
            return new ArrayList<>();
        }

        List<ContractFulfillmentShardingRuleListModel> ruleListModelList = new ArrayList<>();
        for(ContractFulfillmentRuleDO ruleDO : ruleDOList){
            ContractFulfillmentShardingRuleListModel ruleListModel = new ContractFulfillmentShardingRuleListModel();
            ruleListModel.setId(ruleDO.getId());
            ruleListModel.setTenantOid(ruleDO.getTenantOid());
            ruleListModel.setTenantGid(ruleDO.getTenantGid());
            ruleListModel.setRuleId(ruleDO.getRuleId());
            ruleListModel.setType(ruleDO.getType());
            ruleListModel.setTypeName(ruleDO.getTypeName());
            ruleListModel.setName(ruleDO.getName());
            ruleListModel.setFormId(ruleDO.getFormId());
            ruleListModel.setScopeType(ruleDO.getScopeType());
            ruleListModel.setStatus(ruleDO.getStatus());
            ruleListModel.setNoticeRule(JSON.parseObject(ruleDO.getNoticeRule(), ContractFulfillmentNoticeRuleModel.class));
            if(StringUtils.isNotEmpty(ruleDO.getNoticeChannel())){
                ruleListModel.setNoticeChannels(JSON.parseArray(ruleDO.getNoticeChannel(), String.class));
            }
            ruleListModel.setCreateByOid(ruleDO.getCreateByOid());
            ruleListModel.setModifiedByOid(ruleDO.getModifiedByOid());
            ruleListModel.setCreateTime(ruleDO.getCreateTime());
            ruleListModel.setModifiedTime(ruleDO.getModifiedTime());
            ruleListModelList.add(ruleListModel);
        }
        return ruleListModelList;
    }

    public static ContractFulfillmentRuleDetailResult convertRuleDetailResult(ContractFulfillmentRuleDO ruleDO, List<RuleConditionModel> models){
        if(ruleDO == null){
            return new ContractFulfillmentRuleDetailResult();
        }
        ContractFulfillmentRuleDetailResult result = new ContractFulfillmentRuleDetailResult();
        result.setRuleId(ruleDO.getRuleId());
        result.setType(ruleDO.getType());
        result.setTypeName(ruleDO.getTypeName());
        result.setName(ruleDO.getName());
        result.setScopeType(ruleDO.getScopeType());
        result.setStatus(ruleDO.getStatus());
        result.setFormId(ruleDO.getFormId());
        if(StringUtils.isNotEmpty(ruleDO.getNoticeChannel())){
            result.setNoticeChannels(JSON.parseArray(ruleDO.getNoticeChannel(), String.class));
        }
        if(FulfillmentScopeEnum.FORCE.getType().equals(ruleDO.getScopeType())){
            result.setScopeConditions(models);
        }
        result.setNoticeRule(JSON.parseObject(ruleDO.getNoticeRule(), ContractFulfillmentNoticeRuleModel.class));
        result.setCreateByOid(ruleDO.getCreateByOid());
        result.setModifiedByOid(ruleDO.getModifiedByOid());
        return result;
    }

}

package com.timevale.contractmanager.core.service.resourceshare.actions.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.timevale.contractmanager.common.dal.bean.ProcessConfigDO;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.service.bean.ProcessConfigBean;
import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.enums.grouping.FilePermissionEnum;
import com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum;
import com.timevale.contractmanager.common.service.enums.resourceshare.ResourceShareCallSourceEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.AuthRelationRpcServiceClient;
import com.timevale.contractmanager.core.model.dto.request.GetProcessUrlRequest;
import com.timevale.contractmanager.core.model.dto.response.GetProcessUrlResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ParticipantAccountRole;
import com.timevale.contractmanager.core.service.aop.Sensor;
import com.timevale.contractmanager.core.service.component.ProcessConfigConverter;
import com.timevale.contractmanager.core.service.grouping.PermissionService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.ProcessService;
import com.timevale.contractmanager.core.service.process.handler.ProcessDetailHandler;
import com.timevale.contractmanager.core.service.process.handler.bean.ParticipantAccount;
import com.timevale.contractmanager.core.service.process.handler.bean.ProcessDetailBean;
import com.timevale.contractmanager.core.service.resourceshare.actions.DoResourceShareActionService;
import com.timevale.contractmanager.core.service.tracking.bean.ShareContractSensorBean;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.doccooperation.service.exception.DocCooperationBizException;
import com.timevale.doccooperation.service.exception.DocCooperationErrorEnum;
import com.timevale.doccooperation.service.exception.DocCooperationException;
import com.timevale.mandarin.base.util.*;
import com.timevale.saas.common.enums.SignModeEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizSceneEnum;
import com.timevale.saas.common.manage.common.service.enums.share.ResourceTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.share.ShareOperateTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.share.ShareTypeEnum;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.BaseDoResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceUrlActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoProcessParticipantAuthRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceShareActionResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceUrlActionResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoProcessParticipantAuthResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoResourceShareActionResponseDTO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;

/**
 * <AUTHOR>
 * @since 2020/12/8
 */
@Slf4j
@Service
public class DoProcessShareActionServiceImpl implements DoResourceShareActionService {

    private static final String SHARE_TITLE = "合同参与人: %s";
    private static final String RESOURCE_NAME = "《%s》";

    @Autowired private UserCenterService userCenterService;

    @Autowired private ProcessService processService;

    @Autowired private BaseProcessService baseProcessService;

    @Autowired private MapperFactory mapperFactory;

    @Autowired private PermissionService permissionService;

    @Autowired private AuthRelationRpcServiceClient authRelationRpcServiceClient;

    @Autowired private ProcessDetailHandler processDetailHandler;

    @Value("${contract.share.switch}")
    private boolean contractShareSwitch;

    @Override
    public String getResourceType() {
        return ResourceTypeEnum.PROCESS.name();
    }

    @Override
    @Sensor(sensorClass = ShareContractSensorBean.class,request = "request")
    public DoResourceShareActionResponseDTO doResourceShareAction(
            DoResourceShareActionRequestDTO request) {
        log.info("doResourceShareAction req : {}", JSON.toJSONString(request));
        // 校验并且获取流程
        ProcessDO process =
                checkAndGetProcess(request.getResourceId(),
                        request.getShareOperateType(), false, request.getShareType());

        // 校验下文件状态
        Integer processStatus = process.getStatus();

        // 进行资源分享的权限
        doCheckAuth(process, request, false);

        String shareTitle = "";
        String shareTitleValue = "";

        // 提取流程参与人
        Set<String> persons = parseParticipantPerson(process);
        //获取合同配置信息
        ProcessConfigDO processConfig = baseProcessService.queryProcessConfig(request.getResourceId());
        ProcessConfigBean processConfigBean = ProcessConfigConverter.convertProcessConfig(processConfig, true);
        //跳过实名，目前只有海外版
        boolean skipRealName=processConfig!=null&&SignModeEnum.GLOBAL.equalBiz(processConfigBean.getSignMode());

        if (CollectionUtils.isNotEmpty(persons)) {
            StringBuilder shareTitleSb = new StringBuilder();
            for (String person : persons) {
                shareTitleSb.append(person).append(";");
            }
            shareTitleValue = shareTitleSb.deleteCharAt(shareTitleSb.length() - 1).toString();
            shareTitle = String.format(SHARE_TITLE, shareTitleValue);
        }
        //分享人与发起人一致，隐藏操作人姓名
        String resourceOperatorName = StringUtils.defaultString(request.getResourceOperatorName(), "");
        if (Boolean.TRUE.equals(processConfigBean.getHideInitiatorName()) && Objects.equals(request.getAccountGid(), process.getInitiatorGid())) {
            resourceOperatorName = "*";
        }
        return DoResourceShareActionResponseDTO.builder()
                .initiatorAccountId(process.getInitiatorOid())
                .initiatorSubjectId(process.getSubjectOid())
                .resourceName(String.format(RESOURCE_NAME, process.getProcessTitle()))
                .shareTitle(shareTitle)
                .shareTitleValue(shareTitleValue)
                .skipRealName(skipRealName)
                .resourceOperatorName(resourceOperatorName)
                .build();
    }

    @Override
    public DoGetResourceShareActionResponseDTO doGetResourceShareAction(
            DoGetResourceShareActionRequestDTO request) {

        ProcessDO process =
                checkAndGetProcess(request.getResourceId(), request.getShareOperateType(),
                        true, request.getShareType());

        boolean canChangeShareConfig = true;

        try {
            doCheckAuth(process, request, true);
        } catch (BizContractManagerException e) {
            if (BizContractManagerResultCodeEnum.PROCESS_SHARE_CHECK_NO_CHANGE_CONFIG
                    .getCode()
                    .equals(e.getCode())) {
                canChangeShareConfig = false;
            } else {
                throw e;
            }
        }

        return DoGetResourceShareActionResponseDTO.builder()
                .canChangeShareConfig(canChangeShareConfig)
                .build();
    }

    @Override
    public DoGetResourceUrlActionResponseDTO doGetResourceUrlAction(
            DoGetResourceUrlActionRequestDTO request) {
        log.info("doGetResourceUrlAction param:{}", JsonUtils.obj2json(request));
        checkAndGetProcess(request.getResourceId(), request.getShareOperateType(),
                true, request.getShareType());

        GetProcessUrlRequest getProcessUrlRequest =
                mapperFactory
                        .getMapperFacade(
                                DoGetResourceUrlActionRequestDTO.class, GetProcessUrlRequest.class)
                        .map(request);
        getProcessUrlRequest.setProcessId(request.getResourceId());

        GetProcessUrlResponse processUrlResponse;
        try {
            // 普通的催办分享，不要给链接里面带resourceShareId，流程那边的鉴权就不会走分享的鉴权
            if (ShareOperateTypeEnum.NORMAL_SHARE.name().equals(request.getShareOperateType())) {
                getProcessUrlRequest.setResourceShareId(null);
            }
            processUrlResponse = processService.getUrl(getProcessUrlRequest, true);
        } catch (DocCooperationException | DocCooperationBizException e) {
            // 这里由于签署的环节，需要支持核验，所以就算不是参与人，也是可以获取地址
            // 无权获取填写地址，转换下错误码doGetResourceShareAction
            if (DocCooperationErrorEnum.OPERATOR_PERMISSION_DENY.getCode().equals(e.getCode())) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SHARE_NO_AUTH_VIEW);
            }
            throw e;
        }

        // 获取下签署/填写的详情有地址
        String processUrl = processUrlResponse.getLongUrl();

        // 普通的催办分享，不要给链接里面带resourceShareId，流程那边的鉴权就不会走分享的鉴权
        if (!ShareOperateTypeEnum.NORMAL_SHARE.name().equals(request.getShareOperateType())
            && StringUtils.isNotBlank(request.getResourceShareId())) {
            processUrl = processUrl.concat("&resourceShareId=")
                    .concat(request.getResourceShareId());
        }

        DoGetResourceUrlActionResponseDTO doGetResourceUrlActionResponseDTO =
                new DoGetResourceUrlActionResponseDTO();
        doGetResourceUrlActionResponseDTO.setResourceUrl(processUrl);

        DoGetResourceUrlActionResponseDTO.Ext ext = new DoGetResourceUrlActionResponseDTO.Ext();
        ext.setSubResourceId(processUrlResponse.getSubProcessId());
        ext.setResourceStatus(processUrlResponse.getStatus().toString());
        doGetResourceUrlActionResponseDTO.setExt(ext);

        return doGetResourceUrlActionResponseDTO;
    }

    @Override
    public DoProcessParticipantAuthResponseDTO doProcessParticipantAuth(DoProcessParticipantAuthRequestDTO requestDTO) {
        log.info("doProcessParticipantAuth, request: {}", JSONObject.toJSONString(requestDTO));
        DoProcessParticipantAuthResponseDTO responseDTO = new DoProcessParticipantAuthResponseDTO();
        Boolean hasAuth = false;

        ProcessDO process = baseProcessService.getProcess(requestDTO.getResourceId(), null);

        if (process == null) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST);
        }

        //如果有权限判断是否为流程参与方
        Pair<List<String>,List<ParticipantAccount>> processParticipantsPersonPair = getProcessParticipantsPersonPair(process.getProcessId());
        List<String> subjectGids = processParticipantsPersonPair.getLeft();
        if(!subjectGids.isEmpty() && subjectGids.contains(requestDTO.getTargetOperatorGid())){
            hasAuth = true;
        }

        // 如果用gid判断不匹配的话，尝试使用姓名+证件号校验
        if (!hasAuth) {
            List<ParticipantAccount> participantAccounts = processParticipantsPersonPair.getRight();
            
            UserAccountDetail shareTargetUser =
                    userCenterService.getUserAccountDetailByOid(requestDTO.getTargetOperatorOid());

            log.info("doProcessParticipantAuth participantAccounts info : {}", JSONObject.toJSONString(participantAccounts));
            
            for (ParticipantAccount participantAccount : participantAccounts) {
                if (Objects.equals(participantAccount.getAccountName(), shareTargetUser.getAccountName()) &&
                        Objects.equals(participantAccount.getLicense(), shareTargetUser.getRealLicense())) {
                    hasAuth = true;
                    break;
                }
            }
        }
        
        responseDTO.setHasAuth(hasAuth);
        return responseDTO;
    }

    /**
     * 通过流程id查询下流程信息
     *
     * @param processId 流程id
     * @return 流程信息
     */
    private ProcessDO checkAndGetProcess(
            String processId, String shareOperateType, boolean doCheckStatus, Integer shareType) {

        ProcessDO process = baseProcessService.getProcess(processId, null);

        if (process == null) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST);
        }

        if (ShareOperateTypeEnum.CHECK.name().equals(shareOperateType)) {

            // 如果要校验核验状态
            if (doCheckStatus
                    && !ProcessStatusEnum.doneStatus(process.getStatus())
                    && ProcessStatusEnum.PART_RESCINDED.getStatus() != process.getStatus()
                    && ProcessStatusEnum.RESCINDED.getStatus() != process.getStatus()) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SHARE_CHECK_STATUS_DISABLE);
            }
            // 获取流程配置
            ProcessConfigDO processConfigDO = baseProcessService.queryProcessConfig(processId);
            ProcessConfigBean processConfigBean = ProcessConfigConverter.convertProcessConfig(processConfigDO, true);
            // 如果是否可以核验， 如果不可以，则报错
            if (!processConfigBean.isCanProcessCheck()) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SHARE_CHECK_NOT_EXIST);
            }
        } else if (ShareOperateTypeEnum.NORMAL_SHARE.name().equals(shareOperateType)) {
            // 普通分享
            if (!(ProcessStatusEnum.COOPERATION.getStatus() == process.getStatus())
                    && !(ProcessStatusEnum.SIGN.getStatus() == process.getStatus())
                    && !(ProcessStatusEnum.DONE.getStatus() == process.getStatus())) {
                // 不在填写中和签署中，直接报错
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SHARE_RUSH_STATUS_DISABLE);
            }
        } else if(ShareOperateTypeEnum.BORROW_READ.name().equals(shareOperateType)){
            //流程完成才能分享给非合同参与人
            if(contractShareSwitch && shareType != null && ShareTypeEnum.SHARE_NOT_IN_PARTICIPANT.getStatus() == shareType){
                if(!ProcessStatusEnum.getAllDoneStatus().contains(process.getStatus())){
                    throw new BizContractManagerException(
                            BizContractManagerResultCodeEnum.PROCESS_SHARE_BORROW_READ_STATUS_DISABLE);
                }
            }
        } else if (ShareOperateTypeEnum.CHECK_EVIDENCE.name().equals(shareOperateType)) {
            // nothing
        } else {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_SHARE_TYPE_NOT_SUPPORT);
        }

        return process;
    }

    /**
     * 抽取流程中的参与人姓名
     *
     * @param process proces流程信息
     * @return 参与人信息集合
     */
    private Set<String> parseParticipantPerson(ProcessDO process) {
        // 获取流程参与方账号列表
        List<ParticipantAccount> participantAccounts =
                getProcessParticipantAccounts(process.getProcessId());
        // 填写/签署人姓名列表
        Set<String> persons = new HashSet<>();
        participantAccounts.forEach(
                participant -> {
                    // 非填写人和签署人， 跳过
                    if (!ParticipantAccountRole.COOPERATOR.equals(participant.getRole())
                            && !ParticipantAccountRole.SIGNER.equals(participant.getRole())) {
                        return;
                    }
                    // 优先获取参与人姓名
                    String name = participant.getAccountName();
                    // 如果姓名为空， 则取昵称
                    if (StringUtils.isBlank(name)) {
                        name = participant.getAccountNickname();
                    }
                    // 如果主体是企业主体， 则显示为“企业(个人)”
                    if (SubjectTypeEnum.ORG.getType().equals(participant.getSubjectType())) {
                        // name = participant.getSubjectName() + "(" + name + ")";
                        // 符合新版本的二维码设计稿，简单二维码，显示的参与方信息是 "个人(企业)"，那么老版本的也是这也显示了
                        name += "(" + participant.getSubjectName() + ")";
                    }
                    persons.add(name);
                });
        return persons;
    }

    private void doCheckAuth(
            ProcessDO process, BaseDoResourceShareActionRequestDTO request, boolean doCheckAuth) {
        // 普通分享
        if (ShareOperateTypeEnum.NORMAL_SHARE.name().equals(request.getShareOperateType())) {
            // 校验是否是发起人
            boolean hasAuth = checkInitiator(process, request);
            if (!hasAuth) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SHARE_NO_AUTH);
            }
        } else if (ShareOperateTypeEnum.CHECK.name().equals(request.getShareOperateType()) ||
                ShareOperateTypeEnum.CHECK_EVIDENCE.name().equals(request.getShareOperateType()) ) {

            // 在发起分享的时候 不需要校验权限，但是在获取和修改资源分享配置的时候需要鉴权
            if (!doCheckAuth) {
                return;
            }

            // 查询当前主体账号信息
            UserAccount subjectUserAccount =
                    userCenterService.getUserAccountBaseByOid(request.getSubjectId());

            boolean hasAuth = false;
            // 判断下当前主体是否是发起方，如果是校验下这个人在该企业下是否有权限

            if (!subjectUserAccount.isPerson()) {
                // 校验下 当前账号在该主体下有没有核验权限
                hasAuth =
                        permissionService.checkGlobalUserPermission(
                                request.getSubjectId(),
                                request.getAccountId(),
                                PrivilegeResourceEnum.CONTRACT.getType(),
                                PrivilegeOperationEnum.CHECK.getType());

                if (hasAuth) {
                    // 如果有核验权限
                    // 判断当前账号是否是流程发起方企业的员工
                    boolean isOrgMember =
                            userCenterService.checkMemberOrCreator(
                                    process.getSubjectOid(),
                                    request.getAccountId());

                    if (!isOrgMember) {
                        // 传入的主体 是 流程发起方的主体，并且没有核验权限
                        throw new BizContractManagerException(
                                BizContractManagerResultCodeEnum
                                        .PROCESS_SHARE_CHECK_NO_CHANGE_CONFIG);
                    }

                    hasAuth = true;
                }
            }
            if (!hasAuth) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SHARE_NO_AUTH);
            }
        } else if (ShareOperateTypeEnum.BORROW_READ.name().equals(request.getShareOperateType())) {
            boolean hasAuth = false;
            // 判断当前账号是否是当前主体企业的员工
            hasAuth = userCenterService.checkMemberOrCreator(
                    request.getSubjectId(),
                    request.getAccountId());

            //进行权限的校验
            ResourceShareCallSourceEnum type = StringUtils.isBlank(request.getCallSource()) ? null :
                    ResourceShareCallSourceEnum.valueOf(request.getCallSource());
            if(StringUtils.isNotBlank(request.getCallSource()) &&
                    ShareTypeEnum.SHARE_NOT_IN_PARTICIPANT.getStatus() == request.getShareType()
                    && hasAuth){
                switch (type){
                    /* 经办合同权限校验 */
                    case CONTRACT:
                        hasAuth = permissionService.checkGlobalUserPermission(
                                request.getSubjectId(),
                                request.getAccountId(),
                                PrivilegeResourceEnum.CONTRACT.getType(),
                                PrivilegeOperationEnum.SHARE.getType());
                        break;
                    /* 企业合同已归档权限校验 */
                    case ORG_ARCHIVE:
                        hasAuth = permissionService.checkGlobalPermission(
                                request.getSubjectId(),
                                request.getAccountId(),
                                PrivilegeResourceEnum.ORG_ARCHIVE.getType(),
                                PrivilegeOperationEnum.SHARE.getType());
                        break;
                    /* 企业合同待归档权限校验 */
                    case ORG_WAITING_ARCHIVE:
                        hasAuth = permissionService.checkGlobalPermission(
                                request.getSubjectId(),
                                request.getAccountId(),
                                PrivilegeResourceEnum.ORG_WAITING_ARCHIVE.getType(),
                                PrivilegeOperationEnum.SHARE.getType());
                        break;
                    /* 企业合同已归档分类中权限校验 */
                    case ORG_ARCHIVE_MENU_ID:
                        try {
                            permissionService.checkMenuOperatorPermission(
                                    request.getMenuId(), request.getAccountId(), request.getAccountGid(),
                                    request.getSubjectId(), request.getSubjectGid(), FilePermissionEnum.DOWNLOAD,
                                    null, false);
                            hasAuth = true;
                        }catch (BizContractManagerException e){
                            hasAuth = false;
                        }
                        break;
                    default:
                        break;
                }
            }

            /**
             * 是否流程参与方
             */
            if(hasAuth){
                hasAuth = hashSubjectAndMember(process,request, ResourceShareCallSourceEnum.CONTRACT == type);
            }

            if (!hasAuth) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SHARE_NO_AUTH);
            }
        } else {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_SHARE_TYPE_NOT_SUPPORT);
        }
    }

    private boolean checkInitiator(ProcessDO process, BaseDoResourceShareActionRequestDTO request) {
        // 校验下操作人是否是发起人
        return StringUtils.equals(process.getInitiatorOid(), request.getAccountId())
                || StringUtils.equals(process.getInitiatorGid(), request.getAccountGid());
    }


    /**
     * 判断传入的主体是否是流程参与方.
     * @param process
     * @param request
     * @return
     */
    private Boolean hashSubjectAndMember(ProcessDO process, BaseDoResourceShareActionRequestDTO request, boolean isManage){
        //如果有权限判断是否为流程参与方
        ParticipantGidAndNotExec participantGidAndNotExec = getProcessParticipantsSubjectGid(process.getProcessId());
        List<String> subjectGids = participantGidAndNotExec.getParticipantGidList();
        boolean subjectMatch = !subjectGids.isEmpty() && subjectGids.contains(request.getSubjectGid());
        if (!subjectMatch) {
            return false;
        }
        if (!isManage) {
            return true;
        }
        //经办合同需要再判断下或签逻辑
        List<ParticipantAccount> notExecutedAccounts = participantGidAndNotExec.getNotExecutedAccounts();
        if (ListUtils.isEmpty(notExecutedAccounts)) {
            return true;
        }
        for (ParticipantAccount notExecutedAccount : notExecutedAccounts) {
            boolean oidEq = Objects.equals(notExecutedAccount.getAccountOid(), request.getAccountId()) ||
                    Objects.equals(notExecutedAccount.getAccountGid(), request.getAccountGid());
            boolean accountEq = oidEq && (Objects.equals(notExecutedAccount.getSubjectOid(), request.getSubjectId())||
                    Objects.equals(notExecutedAccount.getSubjectGid(),  request.getSubjectGid()));
            if (accountEq) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取参与方主体gid
     *
     * @param processId 主流程id
     * @return 参与方主体gid列表
     */
    public ParticipantGidAndNotExec getProcessParticipantsSubjectGid(String processId) {
        // 获取流程参与方账号列表
        ParticipantGidAndNotExec participantGidsAndNotExec = new ParticipantGidCollector() {
            @Override
            protected void handle(
                    ParticipantAccount participant,
                    List<String> participantGids,
                    List<String> searchOids) {
                // 非企业主体，跳过
                if (!SubjectTypeEnum.ORG.getType().equals(participant.getSubjectType())) {
                    return;
                }
                // 如果流程已经固化了参与方主体gid, 则优先取固化的参与方主体gid
                if (StringUtils.isNotBlank(participant.getSubjectGid())) {
                    participantGids.add(participant.getSubjectGid());
                    return;
                }
                // 如果参与方主体gid未固化， 追加需要查询用户中心的参与方主体oid
                if (StringUtils.isNotBlank(participant.getSubjectOid())) {
                    searchOids.add(participant.getSubjectOid());
                }
            }
        }.getParticipantGidsAndNotExec(processId);
        List<String> subjectGids = participantGidsAndNotExec.getParticipantGidList();
        // 多企业合同管理-添加参与方的总部
        List<AuthRelationDTO> authRelationDTOList =
                authRelationRpcServiceClient.listEffectiveAuthRelationByChildTenantGidList(
                        subjectGids, AuthRelationBizSceneEnum.CONTRACT_MANAGE.getCode());
        if (CollectionUtils.isNotEmpty(authRelationDTOList)) {
            subjectGids.addAll(
                    authRelationDTOList.stream()
                            .map(AuthRelationDTO::getParentTenantGid)
                            .collect(Collectors.toList()));
        }

        return participantGidsAndNotExec;
    }

    /**
     * 获取流程参与人gid
     *
     * @param processId 主流程id
     * @return 流程参与人gid列表
     */
    public Pair<List<String>,List<ParticipantAccount>> getProcessParticipantsPersonPair(String processId) {
        // 获取流程参与方账号列表
        return new ParticipantGidCollector() {
            @Override
            protected void handle(
                    ParticipantAccount participant,
                    List<String> participantGids,
                    List<String> searchOids) {
                // 如果流程已经固化了参与人gid, 则优先取固化的参与人gid
                if (StringUtils.isNotBlank(participant.getAccountGid())) {
                    participantGids.add(participant.getAccountGid());
                    return;
                }
                // 如果参与人gid未固化， 追加需要查询用户中心的参与人oid
                if (StringUtils.isNotBlank(participant.getAccountOid())) {
                    searchOids.add(participant.getAccountOid());
                }
            }
        }.getParticipantGids(processId);
    }

    /**
     * 抽取流程中的参与方账号列表
     *
     * @param processId processId
     * @return 参与方账号列表
     */
    private List<ParticipantAccount> getProcessParticipantAccounts(String processId) {
        ProcessDetailBean processDetailBean = processDetailHandler.queryProcessDetail(processId);
        return processDetailBean.convertParticipantAccounts();
    }

    /** 参与方gid和无执行权限账号列表 */
    @Data
    public static class ParticipantGidAndNotExec {
        private List<String> participantGidList;
        private List<ParticipantAccount> notExecutedAccounts;
    }

    /** 参与方gid收集器 */
    public abstract class ParticipantGidCollector {
        /**
         * 获取流程参与方gid
         *
         * @param processId 主流程id
         * @return 流程参与方gid列表
         */
        public Pair<List<String>,List<ParticipantAccount>> getParticipantGids(String processId) {
            // 获取流程参与方账号列表
            List<ParticipantAccount> participantAccounts = getProcessParticipantAccounts(processId);
            // 参与方gid列表
            List<String> participantGids = new ArrayList<>();
            // 需要查询用户中心的oid列表
            List<String> searchOids = new ArrayList<>();
            participantAccounts.forEach(
                    participant -> handle(participant, participantGids, searchOids));
            // 如果需要查询用户中心的oid列表不为空
            if (CollectionUtils.isNotEmpty(searchOids)) {
                // 批量查询用户中心
                Map<String, UserAccountDetail> accountDetailMap =
                        userCenterService.queryFatAccountMapByAccountIds(searchOids);
                // 追加参与人/主体gid
                participantGids.addAll(
                        accountDetailMap.values().stream()
                                .map(UserAccount::getAccountGid)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toSet()));
            }
            return Pair.of(participantGids, participantAccounts);
        }

        public ParticipantGidAndNotExec getParticipantGidsAndNotExec(String processId) {
            ParticipantGidAndNotExec result = new ParticipantGidAndNotExec();

            // 获取流程参与方账号列表
            List<ParticipantAccount> participantAccounts = getProcessParticipantAccounts(processId);
            // 参与方gid列表
            List<String> participantGids = new ArrayList<>();
            // 需要查询用户中心的oid列表
            List<String> searchOids = new ArrayList<>();
            participantAccounts.forEach(
                    participant -> handle(participant, participantGids, searchOids));
            // 如果需要查询用户中心的oid列表不为空
            if (CollectionUtils.isNotEmpty(searchOids)) {
                // 批量查询用户中心
                Map<String, UserAccountDetail> accountDetailMap =
                        userCenterService.queryFatAccountMapByAccountIds(searchOids);
                // 追加参与人/主体gid
                participantGids.addAll(
                        accountDetailMap.values().stream()
                                .map(UserAccount::getAccountGid)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toSet()));
                // 回填gid
                for (ParticipantAccount participantAccount : participantAccounts) {
                    if (StringUtils.isNotBlank(participantAccount.getAccountGid())) {
                        UserAccountDetail accountDetail =
                                accountDetailMap.get(participantAccount.getAccountOid());
                        participantAccount.setAccountGid(
                                accountDetail == null ? null : accountDetail.getAccountGid());
                    }
                    if (StringUtils.isNotBlank(participantAccount.getSubjectGid())) {
                        UserAccountDetail accountDetail =
                                accountDetailMap.get(participantAccount.getSubjectOid());
                        participantAccount.setSubjectGid(
                                accountDetail == null ? null : accountDetail.getAccountGid());
                    }
                }
            }
            result.setParticipantGidList(participantGids);

            // 找出或签非执行人
            List<ParticipantAccount> notExecList =
                    participantAccounts.stream()
                            .filter(p -> BooleanUtils.isFalse(p.getExecPermission()))
                            .collect(Collectors.toList());
            // 过滤掉在其他参与方有权限的
            List<ParticipantAccount> hasExec =
                    participantAccounts.stream()
                            .filter(p -> !BooleanUtils.isFalse(p.getExecPermission()))
                            .collect(Collectors.toList());
            notExecList =
                    notExecList.stream()
                            .filter(ne -> hasExec.stream().noneMatch(he -> participantEq(he, ne)))
                            .collect(Collectors.toList());
            result.setNotExecutedAccounts(notExecList);

            return result;
        }

        protected abstract void handle(
                ParticipantAccount participant,
                List<String> participantGids,
                List<String> searchOids);
    }

    private boolean participantEq(ParticipantAccount a1, ParticipantAccount a2) {
        return (Objects.equals(a1.getAccountOid(), a2.getAccountOid()) ||
                Objects.equals(a1.getAccountGid(), a2.getAccountGid()))
                &&
                (Objects.equals(a1.getSubjectOid(), a2.getSubjectOid()) ||
                Objects.equals(a1.getSubjectGid(), a2.getSubjectGid()));
    }
}

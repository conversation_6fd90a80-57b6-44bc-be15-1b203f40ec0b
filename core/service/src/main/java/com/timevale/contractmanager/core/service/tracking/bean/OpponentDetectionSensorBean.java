package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import java.util.Map;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @Author:jiany<PERSON>
 * @since 2021-09-27 10:35
 */
@Data
public class OpponentDetectionSensorBean extends ToString {
	/**
	 * 企业名称
	 */
	private String orgName;

	/**
	 * 租户oid
	 */
	private String tenantId;

	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData.put(ORG_NAME,sensorString(orgName));
		sensorData.put(TENANT_ID,sensorString(tenantId));
		return sensorData;
	}

}

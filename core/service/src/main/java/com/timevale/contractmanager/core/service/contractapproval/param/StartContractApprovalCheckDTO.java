package com.timevale.contractmanager.core.service.contractapproval.param;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 发起合同审批前置模板校验请求参数
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class StartContractApprovalCheckDTO extends ToString {

    /** 合同审批模板id */
    private String approvalTemplateId;
    /** 流程模板id */
    private String flowTemplateId;
    /** 用户oid */
    private String accountId;
    /** 用户gid */
    private String accountGid;
    /** 用户姓名 */
    private String accountName;
    /** 用户所属部门id */
    private String accountDeptId;
    /** 主体oid */
    private String subjectId;
    /** 主体gid */
    private String subjectGid;
    /** 产品端id */
    private String clientId;
}

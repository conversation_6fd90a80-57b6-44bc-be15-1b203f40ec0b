package com.timevale.contractmanager.core.service.component.opponent.detection;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.core.service.mq.msg.OpponentDetectionMsg;
import com.timevale.contractmanager.core.service.mq.producer.opponent.OpponentDetectionMqProducer;
import com.timevale.contractmanager.core.service.util.IdGeneratorUtils;
import com.timevale.framework.mq.client.producer.DelayMsgLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 相对方检测异步发送mq
 * @Author:jianyang
 * @since 2021-08-16 16:42
 */
@Component
@Slf4j
public class OpponentDetectionAsyncSendMsg {

	@Autowired private OpponentDetectionMqProducer detectionMqProducer;

	@Async("opponentDetectionExecutor")
	public void sendMsg(List<OpponentEntityDO> opponentEntityDOS,Integer taskType, String taskId, String tenantGid){
		for (OpponentEntityDO opponentEntityDO : opponentEntityDOS){
			log.info("opponent detection start send msg orgName:{}, taskId:{}, tenantGid:{} ",
					opponentEntityDO.getEntityName(), taskId, tenantGid);
			OpponentDetectionMsg msg = new OpponentDetectionMsg();
			msg.setTaskId(taskId);
			msg.setOrgName(opponentEntityDO.getEntityUniqueId());
			msg.setTaskType(taskType);
			msg.setTenantGid(tenantGid);
			msg.setSocialCreditCode(opponentEntityDO.getSocialCreditCode());
			msg.setUniCode(String.valueOf(IdGeneratorUtils.getId()));
			/**延迟5s消费*/
			detectionMqProducer.sendMessage(JSON.toJSONString(msg),null,  DelayMsgLevel.TWO);
		}
	}
}

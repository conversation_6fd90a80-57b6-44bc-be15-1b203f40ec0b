package com.timevale.contractmanager.core.service.transfer.factory;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.service.transfer.TransferAbstractBizService;
import com.timevale.contractmanager.core.service.transfer.TransferBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.TRANSFER_BIZ_SERVICE_NON_EXIST;

/**
 * <AUTHOR>
 * @since 2022/11/28
 */
@Slf4j
@Service
public class TransferBizServiceFactory {

    @Autowired private List<TransferAbstractBizService> transferBizServiceList;

    /**
     * 获取场景值对应的业务实现类
     *
     * @param transferScene {@link
     *     com.timevale.contractmanager.common.service.enums.TransferSceneEnum}
     * @return 返回当前业务实现类
     */
    public TransferAbstractBizService getService(Integer transferScene) {
        for (TransferAbstractBizService bizService : transferBizServiceList) {
            if (bizService.isSupport(transferScene)) {
                return bizService;
            }
        }
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }
}

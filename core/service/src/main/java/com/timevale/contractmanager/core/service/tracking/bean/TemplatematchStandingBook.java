package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import java.util.Map;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @Author:jianyang
 * @since 2021-04-27 11:40
 */
@Data
public class TemplatematchStandingBook extends ToString {
	/**
	 * 流程id
	 */
	private String processId;

	/**
	 * 企业名称
	 */
	private String orgName;

	/**
	 * 租户oid
	 */
	private String tenantId;

	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData.put(PROCESS_ID,  sensorString(processId));
		sensorData.put(ORG_NAME,sensorString(orgName));
		sensorData.put(TENANT_ID,sensorString(tenantId));
		return sensorData;
	}
}

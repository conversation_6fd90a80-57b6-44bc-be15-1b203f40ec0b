package com.timevale.contractmanager.core.service.transaction;

import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 流程清除缓存事件监听器
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Slf4j
@Component
public class ProcessClearCacheEventListener {

    @TransactionalEventListener(fallbackExecution = true, phase = TransactionPhase.AFTER_COMMIT)
    public void handleProcessChangeEvent(ProcessClearCacheEvent event) {
        Set<String> processIds = event.getProcessIds();
        if (CollectionUtils.isEmpty(processIds)) {
            return;
        }
        ProcessCacheType eventType = event.getEventType();

        log.debug("process changed, event: {}", JsonUtils.obj2json(event));
        // 流程基本信息、配置信息、子流程信息缓存清除
        if (ProcessCacheType.PROCESS_CACHE.equals(eventType)
                || ProcessCacheType.PROCESS_CONFIG_CACHE.equals(eventType)
                || ProcessCacheType.SUB_PROCESS_CACHE.equals(eventType)) {
            // 获取缓存key列表
            String[] cacheKeys =
                    processIds.stream()
                            .map(processId -> CacheUtil.getProcessDetailResultCacheKey(processId))
                            .collect(Collectors.toList())
                            .toArray(new String[] {});
            // 批量删除缓存
            CacheUtil.degradeDelete(cacheKeys);
            return;
        }
    }
}

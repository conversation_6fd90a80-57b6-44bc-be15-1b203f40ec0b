package com.timevale.contractmanager.core.service.transfer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.bo.transfer.ProcessTransferNoticeBO;
import com.timevale.contractmanager.core.model.bo.transfer.TransferUserListBO;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.user.OrgMemberBaseDTO;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.contractmanager.core.service.util.MessageNotifyUtil;
import com.timevale.contractmanager.core.service.util.TransferUtils;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.notificationmanager.service.enums.NotifyChannelTypeEnum;
import com.timevale.notificationmanager.service.model.IDUser;
import com.timevale.notificationmanager.service.model.NotifyContactWay;
import com.timevale.notificationmanager.service.model.SendMessageRequest;
import com.timevale.saas.auth.api.starter.service.DingIsvInfoService;
import com.timevale.saas.common.manage.common.service.enums.TaskStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.TaskTypeEnum;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskAddInput;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskUpdateInput;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskAddBean;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskBizInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.TRANSFER_TASK_EXIST;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Component
@Slf4j
public abstract class TransferAbstractBizService implements TransferBizService{

    @Resource private UserCenterService userCenterService;
    @Autowired private MessageNotifyUtil messageNotifyUtil;
    @Autowired private DingIsvInfoService dingIsvInfoService;
    @Autowired private SaasCommonClient saasCommonClient;

    /**
     * 合同/用印转交通知消息模板
     *
     * <p>转交通知模板： 短信/邮件：
     * {tenantName}（操作者：{operator}，{loginUser}）向您移交了{originLabel}{total}合同，请登录e签宝电子合同平台（www.esign.cn）查看并处理
     * 站内信：{tenantName}（操作者：{operator}，{loginUser}）向您移交了{originLabel}{total}合同。
     *
     * @param processTransferNoticeBO 转交提示信息bo
     * @param total 通知总数
     * @param notifyTemplateName 消息通知模板 {@link
     *     com.timevale.contractmanager.common.service.enums.TransferSceneEnum}
     */
    public void sendProcessTransferNotify(
            ProcessTransferNoticeBO processTransferNoticeBO,
            long total,
            String notifyTemplateName) {

        if (processTransferNoticeBO == null
                || total < 1
                || StringUtils.isBlank(notifyTemplateName)) {
            return;
        }
        IDUser idUser = new IDUser();
        idUser.setUserGid(processTransferNoticeBO.getTransferToAccount().getAccountGid());
        idUser.setUserOid(processTransferNoticeBO.getTransferToAccount().getAccountOid());
        Map<String, String> templateParam = Maps.newHashMap();
        templateParam.put(
                "tenantName", processTransferNoticeBO.getTenantAccount().getAccountName());
        templateParam.put(
                "operator",
                processTransferNoticeBO.isSystemTransfer()
                        ? TransferUtils.getSystemOperatorName()
                        : processTransferNoticeBO.getOperatorAccount().obtainAccountName());
        templateParam.put(
                "loginUser",
                processTransferNoticeBO.isSystemTransfer()
                        ? ""
                        : processTransferNoticeBO.getOperatorAccount().account());
        templateParam.put("originLabel", "");
        templateParam.put("total", total + "份");
        List<NotifyChannelTypeEnum> noticeTypes =
                Lists.newArrayList(
                        NotifyChannelTypeEnum.INMAIL,
                        NotifyChannelTypeEnum.EMAIL,
                        NotifyChannelTypeEnum.MOBILE);
        if (StringUtils.isNotBlank(processTransferNoticeBO.getTenantAccount().getAccountOid())) {
            noticeTypes.add(NotifyChannelTypeEnum.FEISHU);
            templateParam.put("authorizedAccountOid", processTransferNoticeBO.getTenantAccount().getAccountOid());
        }
        // 钉签工作台通知
        // 获取钉签isvAppId
        String dingIsvAppId = dingIsvInfoService.getDefaultAppId();
        if (StringUtils.isNotBlank(dingIsvAppId)) {
            noticeTypes.add(NotifyChannelTypeEnum.DINGDING);
            templateParam.put(
                    "authorizedAccountOid",
                    processTransferNoticeBO.getTenantAccount().getAccountOid());
            templateParam.put("dingdingAppId", dingIsvAppId);
            templateParam.put("timestamp", String.valueOf(System.currentTimeMillis()));
        }
        List<NotifyContactWay> contactWays =
                noticeTypes.stream()
                        .map(
                                t -> {
                                    NotifyContactWay contactWay = new NotifyContactWay();
                                    contactWay.setType(t);
                                    return contactWay;
                                })
                        .collect(Collectors.toList());
        SendMessageRequest sendMessageRequest = new SendMessageRequest();
        sendMessageRequest.setAppId("0");
        sendMessageRequest.setId(idUser);
        sendMessageRequest.setTemplateParam(templateParam);
        sendMessageRequest.setTemplateName(notifyTemplateName);
        sendMessageRequest.setAppointContactWays(contactWays);

        messageNotifyUtil.sendNotification(sendMessageRequest);
        log.info("发送任务转交通知 {}", processTransferNoticeBO.getTransferToAccount().getAccountOid());
    }

    /**
     * 从缓存中获取转交的通知数据
     *
     * @param cacheKey 缓存key，上游根据业务传递
     * @return
     */
    public ProcessTransferNoticeBO getTransferNoticeBOFromCache(String cacheKey) {
        String transferCacheData = TedisUtil.get(cacheKey);
        if (StringUtils.isBlank(transferCacheData)) {
            return null;
        }
        return JSON.parseObject(transferCacheData, ProcessTransferNoticeBO.class);
    }
    /** 默认获取用户列表的方式，直接调用用户中心rpc */
    public OrgDeptListResponse getUserListDefault(TransferUserListBO transferUserListBO) {
        PagerResult<OrgMemberBaseDTO> pagerResult =
                userCenterService.getAllDeptMembers(
                        transferUserListBO.getTenantId(),
                        transferUserListBO.getOffset(),
                        transferUserListBO.getSize(),
                        transferUserListBO.getKeyword());
        OrgDeptListResponse deptSubListResponse = new OrgDeptListResponse();
        deptSubListResponse.setMemberCount(pagerResult.getTotal());
        deptSubListResponse.setMemberList(pagerResult.getItems());
        return deptSubListResponse;
    }

    /** 校验用户当天是否已经做过全量转交 */
    protected void checkUserIsTransferred(String tenantId, String transferAccountOid, TransferSceneEnum transferScene) {
        if (!transferScene.isTransferredCheck()) {
            return;
        }
        try {
            String checkKey = CacheUtil.userTransferCheckKey(tenantId, transferAccountOid, transferScene.getCode());
            if (Objects.nonNull(TedisUtil.get(checkKey))) {
                throw new BizContractManagerException(TRANSFER_TASK_EXIST);
            }
        } catch (BizContractManagerException e) {
            throw e;
        } catch (Exception e) {
            log.warn(
                    "checkUserIsTransferred error tenantId {} transferAccountOid {}",
                    tenantId,
                    transferAccountOid,
                    e);
        }
    }

    /** 设置用户当天已经做过全量转交 */
    protected void setUserIsTransferred(String tenantId, String transferAccountOid, TransferSceneEnum transferScene, int timeout, TimeUnit timeUnit) {
        if (!transferScene.isTransferredCheck()) {
            return;
        }
        String checkKey = CacheUtil.userTransferCheckKey(tenantId, transferAccountOid, transferScene.getCode());
        TedisUtil.set(checkKey, 1, timeout, timeUnit);
    }

    /**
     * 创建任务中心任务
     * @param transferUserInfo
     * @param taskType
     * @param taskName
     * @param total
     */
    protected void createTask(TransferBizContext.TransferUserInfo transferUserInfo, String taskId, TaskTypeEnum taskType, String taskName, int total) {
        TaskAddBean taskAddBean = new TaskAddBean();
        taskAddBean.setAccountOid(transferUserInfo.getOperatorAccount().getAccountOid());
        taskAddBean.setAccountGid(transferUserInfo.getOperatorAccount().getAccountGid());
        taskAddBean.setBizId(taskId);
        taskAddBean.setTotal(Long.valueOf(total));
        taskAddBean.setType(taskType.getType());
        taskAddBean.setName(taskName);
        TaskBizInfo taskBizInfo = new TaskBizInfo();
        taskBizInfo.setOrganizationId(transferUserInfo.getTenantAccount().getAccountOid());
        SaasTaskAddInput taskAddInput = new SaasTaskAddInput();
        taskAddInput.setTasks(Lists.newArrayList(taskAddBean));
        saasCommonClient.addTasks(taskAddInput);
    }

    /**
     * 更新任务中心任务
     * @param taskId
     * @param taskType
     */
    protected void updateTask(String taskId, TaskTypeEnum taskType, TaskStatusEnum taskStatus, long done, String failReason) {
        SaasTaskUpdateInput taskUpdateInput = new SaasTaskUpdateInput();
        taskUpdateInput.setBizId(taskId);
        taskUpdateInput.setType(taskType.getType());
        taskUpdateInput.setDone(done);
        taskUpdateInput.setStatus(taskStatus.getType());
        if (TaskStatusEnum.checkFailed(taskStatus.getType())) {
            taskUpdateInput.setReason(failReason);
        }
        saasCommonClient.updateTask(taskUpdateInput);
    }

}

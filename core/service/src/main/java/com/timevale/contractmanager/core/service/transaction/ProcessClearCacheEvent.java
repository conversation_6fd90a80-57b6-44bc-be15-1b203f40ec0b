package com.timevale.contractmanager.core.service.transaction;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Set;

/**
 * 流程清除缓存事件
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Getter
public class ProcessClearCacheEvent extends ApplicationEvent {

    private Set<String> processIds;
    private ProcessCacheType eventType;
    /**
     * Create a new ApplicationEvent.
     *
     * @param sceneDesc the object on which the event initially occurred (never {@code null})
     */
    public ProcessClearCacheEvent(
            Object sceneDesc, Set<String> processIds, ProcessCacheType eventType) {
        super(sceneDesc);
        this.processIds = processIds;
        this.eventType = eventType;
    }
}

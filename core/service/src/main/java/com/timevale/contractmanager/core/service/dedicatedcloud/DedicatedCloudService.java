package com.timevale.contractmanager.core.service.dedicatedcloud;

import com.timevale.saas.integration.service.model.output.model.QueryDedicatedProjectOutput;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/5 14:10
 */
public interface DedicatedCloudService {


    /**
     * 获取可用专属云,  不考虑合同偏好设置
     */
    QueryDedicatedProjectOutput availableDedicatedProject(String subjectGid);


    /**
     * 校验专属云企业是否可用,  不考虑合同偏好设置
     */
    boolean canUseDedicatedProject(String subjectGid, String dedicatedCloudId);

    /**
     * 校验专属云企业是否可用, 需要考虑合同偏好谁知
     */
    boolean canUseDedicatedCloudByDataAndPreference(String subjectOid, String subjectGid, String dedicatedCloudId);


    /**
     * 限制一些端的操作
     */
    void limitClientOperate(String client, String appName);


    /**
     * 从合同偏好设置获取支持的云类型
     */
    List<String> preferenceDedicatedType(String subjectOid, String subjectGid);

}

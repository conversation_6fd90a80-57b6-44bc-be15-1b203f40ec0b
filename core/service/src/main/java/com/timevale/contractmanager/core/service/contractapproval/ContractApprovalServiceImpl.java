package com.timevale.contractmanager.core.service.contractapproval;

import static com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum.START_DIRECT;
import static com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE;
import static com.timevale.contractapproval.facade.enums.StartApprovalCheckTemplateResultTypeEnum.*;
import static com.timevale.contractmanager.common.service.enums.ProcessOptTypeEnum.P_REJECT_CONTRACT_APPROVAL;
import static com.timevale.contractmanager.common.service.enums.ProcessOptTypeEnum.P_REVOKE_FLOW;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.INVALID_CONTRACT_APPROVAL_TEMPLATE;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.NEED_SELECT_CONTRACT_APPROVAL_TEMPLATE;

import com.alibaba.fastjson.JSONObject;
import com.timevale.contractapproval.facade.constants.SystemConstant;
import com.timevale.contractapproval.facade.dto.*;
import com.timevale.contractapproval.facade.dto.biz.contractprocess.BizContractProcessDTO;
import com.timevale.contractapproval.facade.enums.ApprovalStatusEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.contractapproval.facade.input.*;
import com.timevale.contractapproval.facade.output.*;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.bean.ProcessStartSignParamDO;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.dal.dao.ProcessStartSignParamDAO;
import com.timevale.contractmanager.common.service.enums.ProcessOptTypeEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.enums.SubProcessTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.ContractApprovalClient;
import com.timevale.contractmanager.common.utils.DateUtil;
import com.timevale.contractmanager.core.model.bo.process.ProcessTerminateDetailBO;
import com.timevale.contractmanager.core.service.aop.ProcessChangeNotice;
import com.timevale.contractmanager.core.service.aop.ProcessChangeNoticeContext;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalFlowDetail;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalFlowInfo;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalNode;
import com.timevale.contractmanager.core.service.contractapproval.builder.BizContractProcessDTOBuilder;
import com.timevale.contractmanager.core.service.contractapproval.param.*;
import com.timevale.contractmanager.core.service.contractapproval.result.StartContractApprovalCheckResult;
import com.timevale.contractmanager.core.service.contractapproval.result.VirtualStartApprovalFlowResult;
import com.timevale.contractmanager.core.service.enums.ContractProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ContractProcessChangeProducer;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessStartSignParam;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.base.util.ExceptionLogUtil;
import com.timevale.tlcache.TLConstants;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同审批流程接口
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Slf4j
@Component
public class ContractApprovalServiceImpl implements ContractApprovalService {

    /** 支持合同审批的产品端列表 */
    private static final String PROP_APPROVAL_SUPPORT_CLIENTS = "contract.approval.support.clients";
    /** 适配兼容场景下的合同审批模板id前缀 */
    private static final String PROP_APPROVAL_ADAPTER_PREFIX = "contract.approval.adapter.prefix";

    @Autowired ProcessStartSignParamDAO processStartSignParamDAO;
    @Autowired ContractApprovalClient contractApprovalClient;
    @Autowired BaseProcessService baseProcessService;
    @Autowired ContractProcessChangeProducer contractProcessChangeProducer;

    @Override
    @Cacheable(
            value = TLConstants.TL_CACHE,
            key =
                    "'contractApproval:useApprovalFlowTemplate:'+ #param.approvalTemplateId + ':' + #param.subjectGid",
            cacheManager = TLConstants.TL_CACHE_MANAGER)
    public boolean useApprovalFlowTemplate(UseApprovalFlowTemplateDTO param) {
        if (StringUtils.isBlank(param.getApprovalTemplateId())) {
            return true;
        }
        // 获取真正的审批模板id
        String approvalTemplateId = parseRealApprovalTemplateId(param.getApprovalTemplateId());
        return null != contractApprovalClient.queryApprovalTemplate(approvalTemplateId);
    }

    /**
     * 判断是否适配兼容场景下的审批模板id
     *
     * @param approvalTemplateId
     * @return
     */
    private boolean checkAdaptApprovalTemplateId(String approvalTemplateId) {
        // 获取适配兼容场景下的审批模板id前缀
        String adaptPrefix = getProperty(PROP_APPROVAL_ADAPTER_PREFIX, "adapt_");
        // 判断审批模板id是否包含前缀
        return StringUtils.isNoneBlank(adaptPrefix, approvalTemplateId)
                && approvalTemplateId.startsWith(adaptPrefix);
    }

    /**
     * 获取适配兼容场景下真正的审批模板id
     *
     * @param approvalTemplateId
     * @return
     */
    private String parseRealApprovalTemplateId(String approvalTemplateId) {
        // 判断是否适配兼容场景下的审批模板id, 如果不是， 直接返回
        if (!checkAdaptApprovalTemplateId(approvalTemplateId)) {
            return approvalTemplateId;
        }
        // 获取适配兼容场景下的审批模板id前缀
        String adaptPrefix = getProperty(PROP_APPROVAL_ADAPTER_PREFIX, "adapt_");
        // 去除适配兼容场景的前缀， 获取真正的审批模板id
        return approvalTemplateId.replaceFirst(adaptPrefix, "");
    }

    @Override
    public void createApprovalGroup(CreateContractApprovalGroupDTO param) {
        CreateApprovalGroupInput input = new CreateApprovalGroupInput();
        input.setSubjectOid(param.getSubjectId());
        input.setSubjectGid(param.getSubjectGid());
        input.setOperatorOid(param.getAccountId());
        input.setOperatorGid(param.getAccountGid());
        input.setOperatorName(param.getAccountName());
        input.setBizGroupId(param.getBizGroupId());
        input.setBizGroupName(param.getBizGroupName());
        input.setTotalCount(param.getTotalCount());
        contractApprovalClient.createApprovalGroup(input);
    }

    @Override
    @ProcessChangeNotice
    public String startApprovalFlow(ProcessStartSignParam param) {
        ProcessStartSignParamDO startSignParamDO =
                processStartSignParamDAO.getByUniqProcessId(param.getProcessId());
        if (null == startSignParamDO) {
            startSignParamDO = new ProcessStartSignParamDO();
            startSignParamDO.setProcessId(param.getProcessId());
            startSignParamDO.setStartSignParam(JSONObject.toJSONString(param));
            processStartSignParamDAO.insert(startSignParamDO);
        } else {
            startSignParamDO.setStartSignParam(JSONObject.toJSONString(param));
            processStartSignParamDAO.updateByUniqProcessId(startSignParamDO);
        }
        // 组装发起合同审批参数
        StartApprovalInput startApprovalInput = new StartApprovalInput();
        startApprovalInput.setSkipCheck(true);
        startApprovalInput.setApprovalTemplateCode(param.getFlowInfo().getApproveTemplateId());
        startApprovalInput.setApprovalName(param.getFlowName());
        startApprovalInput.setSubjectOid(param.getInitiator().getSubjectOid());
        startApprovalInput.setSubjectGid(param.getInitiator().getSubjectGid());
        startApprovalInput.setSubjectName(param.getInitiator().getSubjectName());
        startApprovalInput.setInitiatorOid(param.getInitiator().getAccountOid());
        startApprovalInput.setInitiatorGid(param.getInitiator().getAccountGid());
        startApprovalInput.setInitiatorName(param.getInitiator().getAccountName());
        startApprovalInput.setInitiatorDeptId(param.getInitiatorDeptId());
        startApprovalInput.setCooperationId(param.getCooperationId());
        startApprovalInput.setApprovalTemplateConditionType(
                param.getApprovalTemplateConditionType());
        startApprovalInput.setApprovalTemplateConditionValue(
                param.getApprovalTemplateConditionValue());

        ProcessDO process = baseProcessService.getProcess(param.getProcessId());
        // 设置合同审批业务数据
        startApprovalInput.setBizData(
                JSONObject.toJSONString(buildBizContractProcessDTO(process, param)));
        startApprovalInput.setBizId(param.getProcessId());
        startApprovalInput.setBizGroupId(param.getProcessGroupId());

        // 发起合同审批
        StartApprovalOutput output = contractApprovalClient.startApproval(startApprovalInput);
        // 判断是否跳过审批， 如果是， 直接返回
        if (SystemConstant.SKIP_APPROVAL.equalsIgnoreCase(output.getApprovalCode())) {
            return output.getApprovalCode();
        }

        // 绑定合同审批子流程
        SubProcessDO subProcessDO = new SubProcessDO();
        subProcessDO.setProcessId(param.getProcessId());
        subProcessDO.setSubProcessId(output.getApprovalCode());
        subProcessDO.setSubProcessType(SubProcessTypeEnum.CONTRACT_APPROVAL.getType());
        baseProcessService.addSubProcess(subProcessDO);
        // 更新主流程状态
        process.setStatus(ProcessStatusEnum.APPROVAL.getStatus());
        baseProcessService.updateProcess(process);

        // 保存流程过期预处理数据
        baseProcessService.addProcessExpirePreRecord(process);

        // 发送合同审批发起消息, 用于触发自动归档消息
        ProcessChangeMsgEntity msgEntity = new ProcessChangeMsgEntity();
        msgEntity.setProcessId(process.getProcessId());
        contractProcessChangeProducer.sendMessageDelay(
                msgEntity, ContractProcessChangeTagEnum.APPROVAL_START.getTag());

        // 发送流程状态变更通知
        ProcessChangeNoticeContext.setNoticeData(
                process.getProcessId(), ProcessChangeTagEnum.PROCESS_STATUS_CHANGE);

        return output.getApprovalCode();
    }

    @Override
    public VirtualStartApprovalFlowResult virtualStartApprovalFlow(
            VirtualStartApprovalFlowDTO param) {

        // 组装发起合同审批参数
        VirtualStartApprovalInput input = new VirtualStartApprovalInput();
        input.setSkipCheck(true);
        input.setApprovalTemplateCode(param.getApprovalTemplateId());
        input.setSubjectOid(param.getSubjectOid());
        input.setSubjectGid(param.getSubjectGid());
        input.setSubjectName(param.getSubjectName());
        input.setInitiatorOid(param.getAccountOid());
        input.setInitiatorGid(param.getAccountGid());
        input.setInitiatorName(param.getAccountName());
        input.setInitiatorDeptId(param.getAccountDeptId());
        input.setCooperationId(param.getCooperationId());
        input.setApprovalTemplateConditionType(param.getApprovalTemplateConditionType());
        input.setApprovalTemplateConditionValue(param.getApprovalTemplateConditionValue());
        // 发起合同审批
        VirtualStartApprovalOutput output = contractApprovalClient.virtualStartApproval(input);
        // 解析审批节点
        Pair<List<ApprovalNode>, List<ApprovalNode>> approvalNodePair =
                ContractApprovalConverter.convertApprovalLogNodes(output.getApprovalLogs());
        VirtualStartApprovalFlowResult result = new VirtualStartApprovalFlowResult();
        result.setCcNodes(approvalNodePair.getRight());
        result.setTaskNodes(approvalNodePair.getLeft());
        return result;
    }

    @Override
    public StartContractApprovalCheckResult startApprovalFlowCheck(
            StartContractApprovalCheckDTO param) {
        String supportClientIds =
                getProperty(PROP_APPROVAL_SUPPORT_CLIENTS, "WEB,APP_ANDROID,APP_IOS,MINI,WE_CHAT");
        // 判断当前端是否支持合同审批， 如果不支持， 审批模板id直接返回空
        if (!IdsUtil.getIdList(supportClientIds).contains(param.getClientId())) {
            return new StartContractApprovalCheckResult();
        }
        // 获取审批模板id
        String approvalTemplateId = param.getApprovalTemplateId();
        boolean onlyCheckNeedTrueApproval = false;
        // 判断是否适配兼容场景下的审批模板id, 如果是，则设置onlyCheckNeedTrueApproval为true并获取真正的审批模板id
        if (checkAdaptApprovalTemplateId(approvalTemplateId)) {
            onlyCheckNeedTrueApproval = true;
            // 获取真正的审批模板id
            approvalTemplateId = parseRealApprovalTemplateId(approvalTemplateId);
        }
        StartApprovalCheckTemplateInput input = new StartApprovalCheckTemplateInput();
        input.setApprovalTemplateCode(approvalTemplateId);
        input.setOnlyCheckNeedTrueApproval(onlyCheckNeedTrueApproval);
        input.setSubjectOid(param.getSubjectId());
        input.setSubjectGid(param.getSubjectGid());
        input.setInitiatorOid(param.getAccountId());
        input.setInitiatorGid(param.getAccountGid());
        input.setInitiatorName(param.getAccountName());
        input.setInitiatorDeptId(param.getAccountDeptId());
        if (StringUtils.isNotBlank(param.getFlowTemplateId())) {
            input.setApprovalTemplateConditionType(START_FLOW_TEMPLATE.getCode());
            input.setApprovalTemplateConditionValue(param.getFlowTemplateId());
        } else {
            input.setApprovalTemplateConditionType(START_DIRECT.getCode());
        }
        StartApprovalCheckTemplateOutput output =
                contractApprovalClient.startApprovalCheckTemplate(input);
        // 需审批且审批模板校验通过
        if (NEED_APPROVAL_TEMPLATE_OK.getCode().equals(output.getResultType())) {
            StartContractApprovalCheckResult result = new StartContractApprovalCheckResult();
            result.setApprovalTemplateId(approvalTemplateId);
            result.setApprovalTemplateConditionType(input.getApprovalTemplateConditionType());
            result.setApprovalTemplateConditionValue(input.getApprovalTemplateConditionValue());
            return result;
        }
        // 未选择模板
        if (NOT_CHOOSE.getCode().equals(output.getResultType())) {
            throw new BizContractManagerException(NEED_SELECT_CONTRACT_APPROVAL_TEMPLATE);
        }
        // 模板选择错误
        if (CHANGE_APPROVAL_TEMPLATE.getCode().equals(output.getResultType())) {
            throw new BizContractManagerException(INVALID_CONTRACT_APPROVAL_TEMPLATE);
        }
        // 其他场景默认无需审批， 清空合同审批模板id
        return new StartContractApprovalCheckResult();
    }

    @Override
    public ProcessStartSignParam queryApprovalStartSignParam(
            String processId, String approvalFlowId) {
        // 如果未指定processId，根据审批流程id反查processId
        if (StringUtils.isBlank(processId) && StringUtils.isNotBlank(approvalFlowId)) {
            SubProcessDO subProcess = baseProcessService.getSubProcess(approvalFlowId);
            if (null != subProcess) {
                processId = subProcess.getProcessId();
            }
        }
        ProcessStartSignParamDO startSignParamDO =
                processStartSignParamDAO.getByUniqProcessId(processId);
        if (null == startSignParamDO || StringUtils.isBlank(startSignParamDO.getStartSignParam())) {
            return null;
        }
        return JSONObject.parseObject(
                startSignParamDO.getStartSignParam(), ProcessStartSignParam.class);
    }

    @Override
    @Transactional
    public void updateApprovalStartSignParam(String approvalFlowId, ProcessStartSignParam param) {
        ProcessStartSignParamDO startSignParamDO = new ProcessStartSignParamDO();
        startSignParamDO.setProcessId(param.getProcessId());
        startSignParamDO.setStartSignParam(JSONObject.toJSONString(param));
        processStartSignParamDAO.updateByUniqProcessId(startSignParamDO);

        // 查询合同流程信息
        ProcessDO process = baseProcessService.getProcess(param.getProcessId());
        UpdateApprovalBizDataInput input = new UpdateApprovalBizDataInput();
        input.setApprovalCode(approvalFlowId);
        input.setBizData(JSONObject.toJSONString(buildBizContractProcessDTO(process, param)));
        contractApprovalClient.updateApprovalBizData(input);
    }

    @Override
    public void removeApprovalStartSignParam(String processId, String approvalFlowId) {
        // 如果未指定processId，根据审批流程id反查processId
        if (StringUtils.isBlank(processId) && StringUtils.isNotBlank(approvalFlowId)) {
            SubProcessDO subProcess = baseProcessService.getSubProcess(approvalFlowId);
            if (null != subProcess) {
                processId = subProcess.getProcessId();
            }
        }
        if (StringUtils.isNotBlank(processId)) {
            processStartSignParamDAO.deleteByUniqProcessId(processId);
        }
    }

    @Override
    public BizContractProcessDTO getApprovalBizProcessData(String approvalFlowId) {
        GetApprovalBizDataOutput output = contractApprovalClient.getApprovalBizData(approvalFlowId);
        if (StringUtils.isNotBlank(output.getBizData())) {
            return JSONObject.parseObject(output.getBizData(), BizContractProcessDTO.class);
        }
        return null;
    }

    @Override
    public BatchQueryApprovalBizFilesOutput batchQueryApprovalBizFiles(List<String> approvalCodes, List<Long> approvalIds) {
        BatchQueryApprovalBizFilesInput input = new BatchQueryApprovalBizFilesInput();
        input.setApprovalCodes(approvalCodes);
        input.setApprovalIds(approvalIds);
        return contractApprovalClient.batchQueryBizFiles(input);
    }

    /**
     * 组装合同审批业务域数据
     *
     * @param process
     * @param param
     * @return
     */
    private BizContractProcessDTO buildBizContractProcessDTO(
            ProcessDO process, ProcessStartSignParam param) {
        // 组装合同审批业务数据
        BizContractProcessDTO contractProcessDTO =
                BizContractProcessDTOBuilder.buildBizContractProcessDTO(param);
        // 追加流程发起时间
        contractProcessDTO.setCreateTime(
                null == process ? null : DateUtil.dateToLong(process.getCreateTime()));
        // 返回合同审批业务数据
        return contractProcessDTO;
    }

    @Override
    public ApprovalFlowInfo queryApprovalInfoByApprovalId(String approvalFlowId) {
        ApprovalGetInfoInput input = new ApprovalGetInfoInput();
        input.setApprovalCode(approvalFlowId);
        ApprovalDTO approvalInfo = contractApprovalClient.getApprovalInfo(input);

        ApprovalFlowInfo approvalFlowInfo = new ApprovalFlowInfo();
        approvalFlowInfo.setApprovalFlowId(approvalInfo.getApprovalCode());
        approvalFlowInfo.setApprovalFlowName(approvalInfo.getName());
        approvalFlowInfo.setApprovalFlowStatus(approvalInfo.getApprovalStatus());
        approvalFlowInfo.setSubjectOid(approvalInfo.getSubjectOid());
        approvalFlowInfo.setSubjectGid(approvalInfo.getSubjectGid());
        approvalFlowInfo.setInitiatorOid(approvalInfo.getInitiatorOid());
        approvalFlowInfo.setInitiatorGid(approvalInfo.getSubjectGid());
        approvalFlowInfo.setBizId(approvalInfo.getBizId());
        return approvalFlowInfo;
    }

    /**
     * 新版合同审批流的审批节点查询
     *
     * @param approvalFlowId
     * @return
     */
    @Override
    public ApprovalFlowDetail queryApprovalDetailByApprovalId(String approvalFlowId) {
        ApprovalGetDetailInput input = new ApprovalGetDetailInput();
        input.setApprovalCode(approvalFlowId);
        ApprovalDetailDTO approvalDetail = contractApprovalClient.getApprovalDetail(input);
        // 组装并返回合同审批详情
        return ContractApprovalConverter.convertApprovalFlowDetail(approvalDetail);
    }

    @Override
    public ProcessTerminateDetailBO getApprovalTerminateDetail(String approvalCode){
        ApprovalTerminateDetailDTO detailDTO= contractApprovalClient.getApprovalTerminateDetail(approvalCode);
        if(detailDTO==null){
            return null;
        }
        ProcessTerminateDetailBO detailBO=new ProcessTerminateDetailBO();
        detailBO.setOperatorOid(detailDTO.getOperatorOid());
        detailBO.setOperateTime(detailDTO.getOperateTime());
        detailBO.setTerminateReason(detailDTO.getTerminateReason());
        ProcessOptTypeEnum optTypeEnum = null;
        if(ApprovalTypeEnum.SEAL.getCode().equals(detailDTO.getApprovalType())){
            //目前暂不支持印章审批
        }else if(ApprovalTypeEnum.CONTRACT.getCode().equals(detailDTO.getApprovalType())){
            if(ApprovalStatusEnum.APPROVAL_REFUSE.getCode().equals(detailDTO.getApprovalStatus())){
                optTypeEnum=P_REJECT_CONTRACT_APPROVAL;
            }else if(ApprovalStatusEnum.APPROVAL_REVOKE.getCode().equals(detailDTO.getApprovalStatus())){
                optTypeEnum=P_REVOKE_FLOW;
            }
        }
        detailBO.setProcessOptType(optTypeEnum==null?null:optTypeEnum.getType());
        return detailBO;
    }

    /**
     * 新版合同审批流的审批节点查询
     *
     * @param approvalFlowId
     * @return
     */
    @Override
    public ApprovalFlowDetail queryApprovalDetailWithLogByApprovalId(String approvalFlowId) {
        GetApprovalDetailWithLogInput input = new GetApprovalDetailWithLogInput();
        input.setApprovalCode(approvalFlowId);
        ApprovalDetailWithLogDTO approvalDetail =
                contractApprovalClient.getApprovalDetailWithLog(input);
        // 组装并返回合同审批详情
        return ContractApprovalConverter.convertApprovalFlowDetail(approvalDetail);
    }

    @Override
    public boolean checkIsApproverOfHistoryApprovals(CheckIsApproverOfHistoryApprovalsDTO param) {
        try {
            List<ApprovalFlowDetail> approvalFlowDetails =
                    queryHistoryApprovalsByProcessId(param.getProcessId());
            if (CollectionUtils.isEmpty(approvalFlowDetails)) {
                return false;
            }
            return approvalFlowDetails.stream()
                    .anyMatch(i -> checkApprover(i, param.getAccountId(), param.getAccountGid()));
        } catch (Exception e) {
            // 降级处理， 默认返回false
            ExceptionLogUtil.messageLogIfWarn(
                    log, e, "checkIsApproverOfHistoryApprovals failed, {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据合同流程id查询关联的历史合同审批信息
     *
     * @param processId
     * @return
     */
    private List<ApprovalFlowDetail> queryHistoryApprovalsByProcessId(String processId) {
        GetHistoryApprovalsByBizInfoInput input = new GetHistoryApprovalsByBizInfoInput();
        input.setBizId(processId);
        input.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
        // 查询合同流程对应的历史合同审批信息
        GetHistoryApprovalsByBizInfoOutput output =
                contractApprovalClient.queryHistoryApprovalsByBizInfo(input);
        if (CollectionUtils.isEmpty(output.getHistoryApprovals())) {
            return Lists.newArrayList();
        }
        // 组装并返回历史合同审批详情
        return output.getHistoryApprovals().stream()
                .map(i -> ContractApprovalConverter.convertApprovalFlowDetail(i))
                .collect(Collectors.toList());
    }

    /**
     * 校验是否审批人
     *
     * @param approvalFlowDetail
     * @param accountId
     * @param accountGid
     * @return
     */
    private boolean checkApprover(
            ApprovalFlowDetail approvalFlowDetail, String accountId, String accountGid) {
        // 聚合审批人和审批抄送人
        List<ApprovalNode> approvalNodes = com.google.common.collect.Lists.newArrayList();
        approvalNodes.addAll(approvalFlowDetail.getTaskNodes());
        approvalNodes.addAll(approvalFlowDetail.getCcNodes());

        for (ApprovalNode approvalNode : approvalNodes) {
            for (ApprovalNode.ApprovalNodeTask task : approvalNode.getTasks()) {
                // 如果taskId值为空， 说明审批节点未轮到， 则当前节点的用户不可见， 直接跳过
                if (StringUtils.isBlank(task.getTaskId())) {
                    continue;
                }
                // 判断审批人中是否有当前用户， 如果有， 返回true
                if (task.getUsers().stream().anyMatch(i -> i.sameUser(accountId, accountGid))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取合同审批详情地址
     *
     * @param param
     * @return
     */
    @Override
    public String getApprovalUrl(GetContractApprovalUrlDTO param) {
        ApprovalUrlInput input = new ApprovalUrlInput();
        input.setApprovalCode(param.getApprovalFlowId());
        input.setToken(param.getToken());
        input.setH5Flag(param.isH5Url());
        input.setRedirectUrl(param.getRedirectUrl());
        input.setAccountId(param.getAccountId());
        input.setSubjectOid(param.getSubjectOid());
        input.setMenuId(param.getMenuId());
        return contractApprovalClient.getApprovalUrl(input).getUrl();
    }

    /**
     * 终止合同审批
     *
     * @param param
     * @return
     */
    @Override
    public void terminateApproval(TerminateContractApprovalDTO param) {
        ApprovalTerminateInput input = new ApprovalTerminateInput();
        input.setApprovalCode(param.getApprovalFlowId());
        input.setTerminateReason(param.getTerminateReason());
        input.setOperatorOid(param.getAccountId());
        input.setOperatorGid(param.getAccountGid());
        input.setSubjectOid(param.getSubjectId());
        input.setSubjectGid(param.getSubjectGid());
        contractApprovalClient.terminateApproval(input);
    }

    /**
     * 暂停合同审批
     *
     * @param param
     * @return
     */
    @Override
    public void pauseApproval(PauseContractApprovalDTO param) {
        ApprovalPauseInput input = new ApprovalPauseInput();
        input.setApprovalCode(param.getApprovalFlowId());
        input.setPauseReason(param.getPauseReason());
        input.setSubjectOid(param.getSubjectId());
        input.setSubjectGid(param.getSubjectGid());
        contractApprovalClient.pauseApproval(input);
    }

    /**
     * 取消暂停合同审批
     *
     * @param param
     * @return
     */
    @Override
    public void cancelPauseApproval(CancelPauseContractApprovalDTO param) {
        ApprovalCancelPauseInput input = new ApprovalCancelPauseInput();
        input.setApprovalCode(param.getApprovalFlowId());
        input.setSubjectOid(param.getSubjectId());
        input.setSubjectGid(param.getSubjectGid());
        contractApprovalClient.cancelPauseApproval(input);
    }

    /**
     * 催办合同审批
     *
     * @param param
     * @return
     */
    @Override
    public void remindApproval(RemindContractApprovalDTO param) {
        ApprovalUrgeInput input = new ApprovalUrgeInput();
        input.setApprovalCode(param.getApprovalFlowId());
        input.setOperatorOid(param.getAccountId());
        input.setOperatorGid(param.getAccountGid());
        input.setSubjectOid(param.getSubjectId());
        input.setSubjectGid(param.getSubjectGid());
        contractApprovalClient.remindApproval(input);
    }

    /**
     * 实时获取配置中心配置
     *
     * @param propKey
     * @param defaultVal
     * @return
     */
    private String getProperty(String propKey, String defaultVal) {
        return ConfigService.getAppConfig().getProperty(propKey, defaultVal);
    }
}

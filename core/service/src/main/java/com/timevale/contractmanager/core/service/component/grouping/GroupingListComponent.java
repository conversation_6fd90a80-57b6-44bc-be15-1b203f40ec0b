package com.timevale.contractmanager.core.service.component.grouping;

import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Joiner;
import com.timevale.contractanalysis.facade.api.bo.FieldResult;
import com.timevale.contractanalysis.facade.api.bo.FormResult;
import com.timevale.contractanalysis.facade.api.enums.LedgerFieldTypeEnum;
import com.timevale.contractmanager.common.dal.bean.grouping.MenuDO;
import com.timevale.contractmanager.common.dal.dao.grouping.MenuDAO;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.enums.ProcessRelationStatusEnum;
import com.timevale.contractmanager.common.service.enums.SourceEnum;
import com.timevale.contractmanager.common.service.enums.grouping.FieldCodeEnum;
import com.timevale.contractmanager.common.service.enums.grouping.MatchFieldEnum;
import com.timevale.contractmanager.common.service.integration.client.AuthRelationRpcServiceClient;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.AiParamInput;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.QueryGroupingProcessListRequest;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.enums.CloudTypeEnum;
import com.timevale.contractmanager.core.service.process.handler.ProcessSearchHandler;
import com.timevale.saas.common.enums.SignModeEnum;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.StandingBookUtils;
import com.timevale.easun.service.api.RpcEsAccountService;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.input.BizEsMemberWithDeptSearchInput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonRuntimeException;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.SearchAuthRelationLastEffectiveTimeInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.SearchAuthRelationLastEffectiveTimeOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationHistoryLastEffectiveTimeDTO;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.enums.ProcessSecretEnum;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import com.timevale.signflow.search.service.bean.v2.query.MatchingQueryParam;
import com.timevale.signflow.search.service.bean.v2.query.RangeQueryParam;
import com.timevale.signflow.search.service.bean.v2.query.SubSubjectQueryParam;
import com.timevale.signflow.search.service.bean.v2.query.TimeQueryParam;
import com.timevale.signflow.search.service.bean.v2.sort.BaseSortParam;
import com.timevale.signflow.search.service.model.v2.archive.ProcessArchiveQueryModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 归档列表组件
 *
 * @author: jinhuan
 * @since: 2020-05-10 15:16
 */
@Slf4j
@Component
public class GroupingListComponent {

    /** 升序字段 */
    private static final String ASC = "asc";

    @Autowired
    private UserCenterService userCenterService;
    @Autowired private RpcEsAccountService esAccountService;
    @Autowired private ContractAnalysisClient contractAnalysisClient;

    @Autowired
    private MenuDAO menuDAO;

    @Autowired
    private AuthRelationRpcServiceClient authRelationRpcServiceClient;
    @Autowired
    private ProcessSearchHandler processSearchHandler;
    /**
     * 根据动态入参获取查询条件
     *
     * @param matching 动态入参
     * @return true 通过，false 不通过
     */
    public boolean setQueryParam(ProcessArchiveQueryModel model, String matching, String tenantId) {
        // 非空校验
        if (StringUtils.isBlank(matching)) {
            return true;
        }
        try {
            // json转换
            List<AiParamInput> paramList = JsonUtils.json2list(matching, AiParamInput.class);
            model.setAiMatchingQueryParams(new ArrayList<>());
            model.setAiRangeQueryParams(new ArrayList<>());
            paramList.forEach(
                    param -> {
                        // 设置字段入参
                        setFieldParam(model, param,tenantId);
                    });
            return true;
        } catch (GroupingSetQueryParamErrorException e) {
            log.info("grouping setQueryParam field not match {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.warn("getMatchingModel error matching={}", matching, e);
            return true;
        }
    }

    /**
     * 根据matching返回排序字段及asc/desc
     * 如果matching为空 默认根据processCreateTime来排序
     * @param matching
     * @return
     */
    public List<BaseSortParam> getSortParam(String matching){
        if(StringUtils.isBlank(matching)){
            return Arrays.asList(getDefaultSortParam(null));
        }

        List<BaseSortParam> sortParams = Lists.newArrayList();
        try {
            // json转换
            List<AiParamInput> paramList = JsonUtils.json2list(matching, AiParamInput.class);

            paramList.stream().filter(param -> StringUtils.isNotBlank(param.getSort()))
                    .forEach(item -> sortParams.add(parseSortParam(item)));
        } catch (Exception e) {
            log.warn("getMatchingModel error matching={}", matching, e);
        }
        return sortParams;
    }

    private BaseSortParam parseSortParam(AiParamInput param){
        MatchFieldEnum matchFieldEnum = MatchFieldEnum.getByCode(param.getKey());
        // 是否对字段进行排序
        if (StringUtils.isNotBlank(param.getSort())) {
            String key = String.format(matchFieldEnum.getEsCode(), param.getKey());
            boolean asc = ASC.equals(param.getSort());
            boolean ai = MatchFieldEnum.AI.equals(matchFieldEnum);
            BaseSortParam sortParam = new BaseSortParam();
            sortParam.setKey(key);
            sortParam.setAsc(asc);
            sortParam.setAi(ai);
            return sortParam;
        }
        return getDefaultSortParam(null);
    }

    /**
     * 设置默认排序字段
     *
     * @param sortParam 排序字段
     */
    public BaseSortParam getDefaultSortParam(BaseSortParam sortParam) {
        // 如果没有设置排序字段,则默认按照发起时间倒序排序
        if (null == sortParam) {
            sortParam = new BaseSortParam();
            sortParam.setKey(MatchFieldEnum.CREATE_TIME.getEsCode());
            sortParam.setAsc(false);
            sortParam.setAi(false);
        }
        return sortParam;
    }

    /**
     * 根据动态入参获取查询条件
     *
     * @param matching 动态入参
     * @return 查询对象
     */
    public QueryGroupingProcessListRequest getUnGroupingRequest(String matching,String tenantId, String tenantGid) {
        QueryGroupingProcessListRequest model = new QueryGroupingProcessListRequest();
        model.setSubject(tenantId);
        // 非空校验
        if (StringUtils.isBlank(matching)) {
            return model;
        }
        if(StringUtils.isEmpty(tenantGid)){
            UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantId);
            model.setSubjectGid(tenantAccount.getAccountGid());
        } else {
            model.setSubjectGid(tenantGid);
        }
        try {
            // json转换
            List<AiParamInput> paramList = JsonUtils.json2list(matching, AiParamInput.class);
            paramList.forEach(
                    param -> {
                        // 设置字段入参
                        setFieldParam(model, param, tenantId);
                    });
        } catch (Exception e) {
            log.warn("getMatchingModel error matching={}", matching, e);
        }

        return model;
    }

    /**
     * 设置ai字段的入参
     *
     * @param model 需设置的入参
     * @param param 动态入参
     */
    private void setAiFieldParam(ProcessArchiveQueryModel model, AiParamInput param, String tenantId) {
        //todo check LedgerFieldType
        String fieldType = StandingBookUtils.getFieldType(param.getKey(), null);
        //1028迭代下述支持批量查询
        FormResult formResult = contractAnalysisClient.getForm(tenantId, model.getRuleId());
        List<Integer> contain = com.google.common.collect.Lists.newArrayList();
        contain.add(LedgerFieldTypeEnum.SINGLE_LINE_TEXT.getType());
        contain.add(LedgerFieldTypeEnum.RADIO.getType());
        contain.add(LedgerFieldTypeEnum.MULTI_LINE_TEXT.getType());
        contain.add(LedgerFieldTypeEnum.CHECKBOX.getType());
        contain.add(LedgerFieldTypeEnum.TICK_BOX.getType());
        Map<String, Integer> fielMap=formResult.getFields().stream()
        .collect(Collectors.toMap(FieldResult::getColumnName,FieldResult::getFieldType));
        // 文本类型搜索
        if (FieldCodeEnum.STRING.getCode().equals(fieldType) || FieldCodeEnum.TICK_BOX.getCode().equals(fieldType)) {
            // 进行字符串的模糊匹配
            MatchingQueryParam queryParam = new MatchingQueryParam();
            if (contain.contains(fielMap.get(param.getKey()))) {
                queryParam.setValues(param.getValue().toJavaList(String.class));
            } else {
                String value = param.getValue().getString(0);
                queryParam.setValue(value);
            }
            if (StringUtils.isBlank(queryParam.getValue()) && CollectionUtils.isEmpty(queryParam.getValues())) {
                return;
            }
            queryParam.setKey(param.getKey());
            queryParam.setExact(false);
            model.getAiMatchingQueryParams().add(queryParam);
        } else if (FieldCodeEnum.DOUBLE.getCode().equals(fieldType)
                || FieldCodeEnum.DATE.getCode().equals(fieldType)) {
            Long from = null;
            Long to = null;
            if (null != param.getValue().getLong(0)) {
                from = param.getValue().getLong(0);
            }
            if (null != param.getValue().getLong(1)) {
                to = param.getValue().getLong(1);
            }
            if (from == null && to == null) {
                return;
            }
            RangeQueryParam queryParam = new RangeQueryParam();
            queryParam.setKey(param.getKey());
            queryParam.setFrom(from);
            queryParam.setTo(to);
            model.getAiRangeQueryParams().add(queryParam);
        } else {
            log.warn("setFieldParam key {} not find", param.getKey());
        }
    }

    /**
     * 设置字段的入参
     *
     * @param model 需设置的入参
     * @param param 动态入参
     */
    private void setFieldParam(ProcessArchiveQueryModel model, AiParamInput param,String tenantId) throws GroupingSetQueryParamErrorException {
        MatchFieldEnum matchFieldEnum = MatchFieldEnum.getByCode(param.getKey());
        // 是否对字段进行排序
        if (StringUtils.isNotBlank(param.getSort())) {
            String key = String.format(matchFieldEnum.getEsCode(), param.getKey());
            boolean asc = ASC.equals(param.getSort());
            boolean ai = MatchFieldEnum.AI.equals(matchFieldEnum);
            BaseSortParam sortParam = new BaseSortParam();
            sortParam.setKey(key);
            sortParam.setAsc(asc);
            sortParam.setAi(ai);
            model.setSortParam(sortParam);
        }
        // 判断是否只对字段排序 不进行搜索
        if (ListUtils.isEmpty(param.getValue())) {
            return;
        }
        boolean noSearch =
                param.getValue().size() == 1 && StringUtils.isBlank(param.getValue().getString(0));
        // 判断是否传了空查询参数
        if (noSearch) {
            return;
        }
        switch (matchFieldEnum) {
            case INITIATOR:
                if(Objects.isNull(param.getIsPublic()) || Objects.equals(param.getIsPublic(),false)){
                    model.setInitiator(param.getValue().getString(0));
                }else {
                    //获取部门成员
                    List<String> initiatorGids = new ArrayList<>();
                    List<String> initiatorOids = new ArrayList<>();
                    for (Object object:param.getValue()){
                        Map<String, List<String>> idList = getMembersId(object.toString(),tenantId);
                        if(CollectionUtils.isNotEmpty(idList.get("participantOids"))){
                            initiatorOids.addAll(idList.get("participantOids"));
                        }
                        if(CollectionUtils.isNotEmpty(idList.get("participantGids"))){
                            initiatorGids.addAll(idList.get("participantGids"));
                        }
                    }
                    model.setInitiatorGids(initiatorGids);
                    model.setInitiatorOids(initiatorOids);
                }
                break;
            case PARTICIPANT:
                if(Objects.isNull(param.getIsPublic()) || Objects.equals(param.getIsPublic(),false)){
                    model.setParticipant(param.getValue().getString(0));
                }else {
                    //获取部门成员
                    List<String> participantGids = new ArrayList<>();
                    List<String> participantOids = new ArrayList<>();
                    for (Object object:param.getValue()){
                        Map<String, List<String>> idList = getMembersId(object.toString(),tenantId);
                        if(CollectionUtils.isNotEmpty(idList.get("participantOids"))){
                            participantOids.addAll(idList.get("participantOids"));
                        }
                        if(CollectionUtils.isNotEmpty(idList.get("participantGids"))){
                            participantGids.addAll(idList.get("participantGids"));
                        }
                    }
                    model.setParticipantGids(participantGids);
                    model.setParticipantOids(participantOids);
                }
                break;
            case CONTRACT_NO:
                model.setContractNo(param.getValue().getString(0));
                break;
            case TITLE:
                model.setTitle(param.getValue().getString(0));
                break;
            case CONTRACT_FILE_NAME:
                model.setContractFileName(param.getValue().getString(0));
                break;
            case ATTACHMENT_FILE_NAME:
                model.setAttachmentFileName(param.getValue().getString(0));
                break;
            case CREATE_TIME:
                model.setCreateTime(getTimeQueryParam(param.getValue()));
                break;
            case SIGN_VALID_TIME:
                model.setValidTime(getTimeQueryParam(param.getValue()));
                break;
            case SIGN_VALIDITY:
                model.setSignValidity(getTimeQueryParam(param.getValue()));
                break;
            case COMPLETE_TIME:
                model.setCompleteTime(getTimeQueryParam(param.getValue()));
                break;
            case PROCESS_STATUS:
                List<Integer> list = param.getValue().toJavaList(Integer.class);
                model.setStatus(list);
                break;
            case SOURCE:
                List<String> sourceList = param.getValue().toJavaList(String.class);
                List<Integer> source = new ArrayList<>();
                for (String code : sourceList) {
                    source.add(SourceEnum.getSource(code));
                }
                model.setSource(source);
                break;
            case ARCHIVE_NO:
                model.setArchiveNo(param.getValue().getString(0));
                break;
            case ARCHIVEDPERSONNAME:
                model.setArchiver(param.getValue().getString(0));
                break;
            case ARCHIVEDTIME:
                model.setArchiveTime(getTimeQueryParam(param.getValue()));
                break;
            case CC:
                if (Objects.isNull(param.getIsPublic()) || Objects.equals(param.getIsPublic(), false)) {
                    model.setCc(param.getValue().getString(0));
                } else {
                    //获取部门成员
                    List<String> ccGids = new ArrayList<>();
                    List<String> ccOids = new ArrayList<>();
                    for (Object object:param.getValue()){
                        Map<String, List<String>> idList = getMembersId(object.toString(),tenantId);
                        if(CollectionUtils.isNotEmpty(idList.get("participantOids"))){
                            ccOids.addAll(idList.get("participantOids"));
                        }
                        if(CollectionUtils.isNotEmpty(idList.get("participantGids"))){
                            ccGids.addAll(idList.get("participantGids"));
                        }
                    }
                    model.setCcGids(ccGids);
                    model.setCcOids(ccOids);
                }
                break;
            case RENEWAL_STATUS:
                buildRenewalStatus(param.getValue().toJavaList(Integer.class), model, tenantId);
                break;
            case PROCESS_BIZ_TYPE:
                List<Integer> processBizTypes = param.getValue().toJavaList(Integer.class);
                model.setProcessBizTypeList(processBizTypes);
                break;
            case PROCESS_SECRET:
                Integer searchType = param.getValue().getInteger(0);
                if (searchType == null){
                    break;
                }
                if (searchType == 1){
                    model.setFindSecret(ProcessSecretEnum.ALL_SECRET.getType());
                    model.setRenewableSubjectOid(tenantId);
                }else if (searchType == 0) {
                    model.setFindSecret(ProcessSecretEnum.NONE_SECRET.getType());
                    model.setRenewableSubjectOid(tenantId);
                }
                break;
            case INITIATOR_DEPT:
                List<String> deptList = param.getValue().toJavaList(String.class);
                model.setDeptList(deptList);
                break;
            case TEMPLATE:
                List<String> templateList = param.getValue().toJavaList(String.class);
                model.setTemplateList(filterBlank(templateList));
                break;
            case SEAL:
                List<String> sealList = param.getValue().toJavaList(String.class);
                model.setSealList(filterBlank(sealList));
                break;
            case INITIATOR_DEPT_MATCH:
                model.setDeptMatching(param.getValue().getString(0));
                break;
            case TEMPLATE_MATCH:
                model.setTemplateMatching(param.getValue().getString(0));
                break;
            case SEAL_MATCH:
                model.setSealMatching(param.getValue().getString(0));
                break;
            case UPDATE_TIME:
                model.setUpdateTime(getTimeQueryParam(param.getValue()));
                break;
            case MENU_ID_LIST:
                String menuMatch = param.getValue().getString(0);
                if (StringUtils.isNotBlank(menuMatch)) {
                    filterMenu(model, tenantId, menuMatch);
                }
                break;
            case AFFILIATED_ENTERPRISE:
                List<String> subSubjectGidList = filterBlank(param.getValue().toJavaList(String.class));
                List<SubSubjectQueryParam> subSubjectList = new ArrayList<>();
                for (String subSubjectGid : subSubjectGidList) {
                    SubSubjectQueryParam subSubjectQueryParam = new SubSubjectQueryParam();
                    subSubjectQueryParam.setSubjectGid(subSubjectGid);
                    subSubjectList.add(subSubjectQueryParam);
                }
                if (CollectionUtils.isEmpty(model.getSubSubjectList())) {
                    model.setSubSubjectList(subSubjectList);
                } else {
                    model.getSubSubjectList().addAll(subSubjectList);
                }
                break;
            case AFFILIATED_ENTERPRISE_MATCH:
                List<SubSubjectQueryParam> subjectList = searchSubSubjectGidByName(model.getSubjectGid(), model.getSubjectOid(), param);
                if (CollectionUtils.isEmpty(model.getSubSubjectList())) {
                    model.setSubSubjectList(subjectList);
                } else {
                    model.getSubSubjectList().addAll(subjectList);
                }
                break;
            case RESCIND_REMARK:
                model.setRescindRemark(param.getValue().getString(0));
                break;
            case PROCESS_ID:
                model.setProcessId(param.getValue().getString(0));
                break;
            case PROCESS_IDS:
                model.setProcessIds(param.getValue().toJavaList(String.class));
                break;
            case FLOW_ID:
                model.setFlowId(param.getValue().getString(0));
                break;
            case PERSON_NAME:
                model.setPersonName(param.getValue().getString(0));
                break;
            case PERSON_ACCOUNT:
                model.setPersonAccount(param.getValue().getString(0));
                break;
            case SUBJECT_NAME:
                model.setSubjectName(param.getValue().getString(0));
                break;
            case PROCESS_FROM:
                model.setProcessFromList(param.getValue().toJavaList(String.class));
                break;
            case CONTRACT_CATEGORY:
                model.setContractCategoryIds(param.getValue().toJavaList(String.class));
                break;
            case SIGN_MODE:
                List<String> signModes = param.getValue().toJavaList(String.class);
                if(!signModes.containsAll(SignModeEnum.ALL)) {
                    model.setSignMode(signModes.get(0));
                    model.setSearchSignModeEmpty(SignModeEnum.searchSignModeEmptyData(signModes.get(0)));
                }
                break;
            case FILE_STORAGE_TYPE:
                List<String> cloudTypes = param.getValue().toJavaList(String.class);
                if (!cloudTypes.containsAll(CloudTypeEnum.ALL)) {
                    model.setCloudType(cloudTypes.get(0));
                }
                break;
            case JOB_NUMBER:
                Account account = processSearchHandler.getAccount(tenantId, param.getValue().getString(0), null);
                appendRelatedPerson(model, account);
                break;
            case PERSON_NICK_NAME:
                Account person = processSearchHandler.getAccount(tenantId, null, param.getValue().getString(0));
                appendRelatedPerson(model, person);
                break;
            case PERSON_IDENTITY:
                Account identityPerson = processSearchHandler.getAccount(tenantId, null, null, param.getValue().getString(0));
                appendRelatedPerson(model, identityPerson);
                break;
            default:
                // 设置ai字段
                setAiFieldParam(model, param, tenantId);
                break;
        }
    }

    /**
     * 追加参与方oid或gid
     * @param model
     * @param person
     */
    private void appendRelatedPerson(ProcessArchiveQueryModel model, Account person) {
        if (null == person) {
            return;
        }
        ProcessArchiveQueryModel.Account account = new ProcessArchiveQueryModel.Account();
        account.setOid(person.getOid());
        account.setGid(person.getGid());
        if (CollectionUtils.isEmpty(model.getRelatedPersonList())) {
            model.setRelatedPersonList(Lists.newArrayList(account));
            return;
        }
        model.getRelatedPersonList().add(account);
    }

    private List<SubSubjectQueryParam> searchSubSubjectGidByName(String subjectGid, String subjectOid, AiParamInput param) {
        if (StringUtils.isEmpty(subjectGid)) {
            return new ArrayList<>();
        }
        List<SubSubjectQueryParam> subSubjectList = new ArrayList<>();
        String name = param.getValue().getString(0);
        if (StringUtils.isEmpty(name)) {
            return new ArrayList<>();
        }

        SearchAuthRelationLastEffectiveTimeInput input = new SearchAuthRelationLastEffectiveTimeInput();
        input.setTenantGid(subjectGid);
        input.setSearchTenantName(name);
        input.setPageIndex(null);
        input.setPageSize(null);
        SearchAuthRelationLastEffectiveTimeOutput output = authRelationRpcServiceClient.searchAuthRelationLastEffectiveTime(input);
        if (CollectionUtils.isEmpty(output.getList())) {
            //不存在也塞个值，为了强制走es检索v2版本
            SubSubjectQueryParam subSubjectQueryParam = new SubSubjectQueryParam();
            subSubjectQueryParam.setSubjectGid(name);
            subSubjectList.add(subSubjectQueryParam);
        } else {
            for (AuthRelationHistoryLastEffectiveTimeDTO authRelationHistoryLastEffectiveTimeDTO : output.getList()) {
                SubSubjectQueryParam subSubjectQueryParam = new SubSubjectQueryParam();
                subSubjectQueryParam.setSubjectGid(authRelationHistoryLastEffectiveTimeDTO.getChildTenantGid());
                subSubjectList.add(subSubjectQueryParam);
            }
        }
        AccountBean subjectAccount = userCenterService.getAccountBeanByOid(subjectOid);
        if (subjectAccount.getName().contains(name)) {
            // 自己搜自己, 把自己也家进入
            SubSubjectQueryParam subSubjectQueryParam = new SubSubjectQueryParam();
            subSubjectQueryParam.setSubjectGid(subjectGid);
            subSubjectList.add(subSubjectQueryParam);
        }
        return subSubjectList;
    }

    private void filterMenu(ProcessArchiveQueryModel model, String tenantId, String menuMatch) throws GroupingSetQueryParamErrorException {
        List<MenuDO> menuDOList = null;
        if (StringUtils.isBlank(model.getMenuId()) && ListUtils.isEmpty(model.getMenuIdList())) {
            menuDOList = menuDAO.queryByIdxOid(tenantId, DeletedEnum.NO.code());
        } else if (!ListUtils.isEmpty(model.getMenuIdList())) {
            menuDOList = menuDAO.queryByMenuIds(model.getMenuIdList());
        }
        if (!ListUtils.isEmpty(menuDOList)) {
            List<String> menuIdList = menuDOList.stream()
                    .filter(m -> m.getName().contains(menuMatch))
                    .map(MenuDO::getMenuId)
                    .collect(Collectors.toList());
            if (ListUtils.isEmpty(menuIdList)){
                //过滤后没有分类则查询无效
                throw new GroupingSetQueryParamErrorException("过滤后无有效分类");
            }else {
                model.setMenuId(null);
                model.setMenuIdList(menuIdList);
            }
        }
    }

    private void buildRenewalStatus(List<Integer> statusList, ProcessArchiveQueryModel model,
                                    String subjectOid){
        if (statusList.contains(ProcessRelationStatusEnum.NO_NEED.getStatus())){
            model.setNoNeedRenewable(true);
            model.setRenewableSubjectOid(subjectOid);
            statusList.remove(ProcessRelationStatusEnum.NO_NEED.getStatus());
        }

        if (statusList.contains(ProcessRelationStatusEnum.RELATABLE.getStatus())){
            model.setUnRenewable(true);
            model.setRenewableSubjectOid(subjectOid);
            statusList.remove(ProcessRelationStatusEnum.RELATABLE.getStatus());
        }

        if (!ListUtils.isEmpty(statusList)){
            model.setRenewalStatusList(statusList.stream().map(this::convertProcessStatus).collect(Collectors.toList()));
        }
    }

    private Integer convertProcessStatus(Integer renewalStatus){
        if (ProcessRelationStatusEnum.RELATING.getStatus().equals(renewalStatus)){
            return ProcessStatusEnum.RENEWING.getStatus();
        }else if (ProcessRelationStatusEnum.PART_RELATE.getStatus().equals(renewalStatus)){
            return ProcessStatusEnum.PART_RENEWED.getStatus();
        }else if (ProcessRelationStatusEnum.RELATED.getStatus().equals(renewalStatus)){
            return ProcessStatusEnum.RENEWED.getStatus();
        }
        throw new SaasCommonRuntimeException("无法转换的续签状态");
    }


    /**
     * 设置字段的入参
     *
     * @param model 需设置的入参
     * @param param 动态入参
     */
    private void setFieldParam(QueryGroupingProcessListRequest model, AiParamInput param,String tenantId) {
        MatchFieldEnum matchFieldEnum = MatchFieldEnum.getByCode(param.getKey());
        // 判断是否只对字段排序 不进行搜索
        if (ListUtils.isEmpty(param.getValue())) {
            return;
        }
        switch (matchFieldEnum) {
            case INITIATOR:
                if(Objects.isNull(param.getIsPublic()) || Objects.equals(param.getIsPublic(),false)){
                    model.setInitiatorMix(param.getValue().getString(0));
                }else {
                    //获取部门成员
                    List<String> initiatorGids = new ArrayList<>();
                    List<String> initiatorOids = new ArrayList<>();
                    for (Object object:param.getValue()){
                        Map<String, List<String>> idList = getMembersId(object.toString(),tenantId);
                        if(CollectionUtils.isNotEmpty(idList.get("participantOids"))){
                            initiatorOids.addAll(idList.get("participantOids"));
                        }
                        if(CollectionUtils.isNotEmpty(idList.get("participantGids"))){
                            initiatorGids.addAll(idList.get("participantGids"));
                        }
                    }
                    model.setInitiatorGids(initiatorGids);
                    model.setInitiatorOids(initiatorOids);
                }
                break;
            case PARTICIPANT:
                if(Objects.isNull(param.getIsPublic()) || Objects.equals(param.getIsPublic(),false)){
                    model.setFuzzyMatching(param.getValue().getString(0));
                }else {
                    //获取部门成员
                    List<String> participantGids = new ArrayList<>();
                    List<String> participantOids = new ArrayList<>();
                    for (Object object:param.getValue()){
                        Map<String, List<String>> idList = getMembersId(object.toString(),tenantId);
                        if(CollectionUtils.isNotEmpty(idList.get("participantOids"))){
                            participantOids.addAll(idList.get("participantOids"));
                        }
                        if(CollectionUtils.isNotEmpty(idList.get("participantGids"))){
                            participantGids.addAll(idList.get("participantGids"));
                        }
                    }
                    model.setParticipantGids(participantGids);
                    model.setParticipantOids(participantOids);
                }
                break;
            case CONTRACT_NO:
                model.setContractNo(param.getValue().getString(0));
                break;
            case TITLE:
                model.setTitle(param.getValue().getString(0));
                break;
            case CONTRACT_FILE_NAME:
                model.setContractFileName(param.getValue().getString(0));
                break;
            case ATTACHMENT_FILE_NAME:
                model.setAttachmentFileName(param.getValue().getString(0));
                break;
            case CREATE_TIME:
                if (null != param.getValue().getLong(0)) {
                    model.setCreateFrom(param.getValue().getLong(0));
                }
                if (param.getValue().size() > 1 && param.getValue() != null) {
                    model.setCreateEnd(param.getValue().getLong(1));
                }
                break;
            case SIGN_VALID_TIME:
                if (null != param.getValue().getLong(0)) {
                    model.setValidFrom(param.getValue().getLong(0));
                }
                if (param.getValue().size() > 1 && param.getValue() != null) {
                    model.setValidEnd(param.getValue().getLong(1));
                }
                break;
            case SIGN_VALIDITY:
                if (null != param.getValue().getLong(0)) {
                    model.setSignValidityFrom(param.getValue().getLong(0));
                }
                if (param.getValue().size() > 1 && param.getValue() != null) {
                    model.setSignValidityEnd(param.getValue().getLong(1));
                }
                break;
            case COMPLETE_TIME:
                if (null != param.getValue().getLong(0) && !param.getValue().equals("")) {
                    model.setCompleteTimeFrom(param.getValue().getLong(0));
                }
                if (param.getValue().size() > 1 && param.getValue() != null) {
                    model.setCompleteTimeEnd(param.getValue().getLong(1));
                }
                break;
            case PROCESS_STATUS:
                List<String> list = param.getValue().toJavaList(String.class);
                model.setProcessStatus(Joiner.on(",").join(list));
                break;
            case SOURCE:
                List<String> sourceList = param.getValue().toJavaList(String.class);
                model.setSource(Joiner.on(",").join(sourceList));
                break;
            case CC:
                if(Objects.isNull(param.getIsPublic()) || Objects.equals(param.getIsPublic(),false)){
                    model.setCcMatching(param.getValue().getString(0));
                }else {
                    //获取部门成员
                    List<String> ccGids = new ArrayList<>();
                    List<String> ccOids = new ArrayList<>();
                    for (Object object:param.getValue()){
                        Map<String, List<String>> idList = getMembersId(object.toString(),tenantId);
                        if(CollectionUtils.isNotEmpty(idList.get("participantOids"))){
                            ccOids.addAll(idList.get("participantOids"));
                        }
                        if(CollectionUtils.isNotEmpty(idList.get("participantGids"))){
                            ccGids.addAll(idList.get("participantGids"));
                        }
                    }
                    model.setCcGids(ccGids);
                    model.setCcOids(ccOids);
                }
                break;
            case RENEWAL_STATUS:
                buildRenewalStatus(param.getValue().toJavaList(Integer.class), model, tenantId);
                break;
            case PROCESS_BIZ_TYPE:
                List<Integer> processBizTypes = param.getValue().toJavaList(Integer.class);
                model.setProcessBizTypeList(processBizTypes);
                break;
            case INITIATOR_DEPT:
                List<String> deptList = param.getValue().toJavaList(String.class);
                model.setDeptList(deptList);
                break;
            case TEMPLATE:
                List<String> templateList = param.getValue().toJavaList(String.class);
                model.setTemplateList(filterBlank(templateList));
                break;
            case SEAL:
                List<String> sealList = param.getValue().toJavaList(String.class);
                model.setSealList(filterBlank(sealList));
                break;
            case INITIATOR_DEPT_MATCH:
                model.setDeptMatching(param.getValue().getString(0));
                break;
            case TEMPLATE_MATCH:
                model.setTemplateMatching(param.getValue().getString(0));
                break;
            case SEAL_MATCH:
                model.setSealMatching(param.getValue().getString(0));
                break;
            case UPDATE_TIME:
                if (null != param.getValue().getLong(0)) {
                    model.setUpdateTimeFrom(param.getValue().getLong(0));
                }
                if (null != param.getValue().getLong(1)) {
                    model.setUpdateTimeEnd(param.getValue().getLong(1));
                }
                break;
            case PROCESS_SECRET:
                Integer searchType = param.getValue().getInteger(0);
                if (searchType == null) {
                    break;
                }
                if (searchType == 1) {
                    model.setFindSecret(ProcessSecretEnum.ALL_SECRET.getType());
                    model.setRenewableSubjectOid(tenantId);
                } else if (searchType == 0) {
                    model.setFindSecret(ProcessSecretEnum.NONE_SECRET.getType());
                    model.setRenewableSubjectOid(tenantId);
                }
                break;
            case AFFILIATED_ENTERPRISE:
                List<String> subSubjectGidList = param.getValue().toJavaList(String.class);
                if (CollectionUtils.isEmpty(model.getSubSubjectGidList())) {
                    model.setSubSubjectGidList(subSubjectGidList);
                } else {
                    model.getSubSubjectGidList().addAll(subSubjectGidList);
                }
                break;
            case AFFILIATED_ENTERPRISE_MATCH:
                List<SubSubjectQueryParam> subjectList = searchSubSubjectGidByName(model.getSubjectGid(), model.getSubject(), param);
                List<String> subjectGidList = subjectList.stream().map(SubSubjectQueryParam::getSubjectGid).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(model.getSubSubjectGidList())) {
                    model.setSubSubjectGidList(subjectGidList);
                } else {
                    model.getSubSubjectGidList().addAll(subjectGidList);
                }
                break;
            case RESCIND_REMARK:
                model.setRescindRemark(param.getValue().getString(0));
                break;
            case PROCESS_ID:
                model.setProcessId(param.getValue().getString(0));
                break;
            case PROCESS_IDS:
                model.setProcessIds(param.getValue().toJavaList(String.class));
                break;
            case FLOW_ID:
                model.setFlowId(param.getValue().getString(0));
                break;
            case PERSON_NAME:
                model.setPersonName(param.getValue().getString(0));
                break;
            case PERSON_ACCOUNT:
                model.setPersonAccount(param.getValue().getString(0));
                break;
            case SUBJECT_NAME:
                model.setSubjectName(param.getValue().getString(0));
                break;
            case PROCESS_FROM:
                model.setProcessFromList(param.getValue().toJavaList(String.class));
                break;
            case CONTRACT_CATEGORY:
                model.setContractCategoryIds(param.getValue().toJavaList(String.class));
                break;
            case SIGN_MODE:
                List<String> signModes = param.getValue().toJavaList(String.class);
                if(!signModes.containsAll(SignModeEnum.ALL)) {
                    model.setSignMode(signModes.get(0));
                    model.setSearchSignModeEmpty(SignModeEnum.searchSignModeEmptyData(signModes.get(0)));
                }
                break;
            case FILE_STORAGE_TYPE:
                List<String> cloudTypes = param.getValue().toJavaList(String.class);
                if (!cloudTypes.containsAll(CloudTypeEnum.ALL)) {
                    model.setCloudType(cloudTypes.get(0));
                }
                break;
            case JOB_NUMBER:
                Account account = processSearchHandler.getAccount(tenantId, param.getValue().getString(0), null);
                appendRelatedPerson(model, account);
                break;
            case PERSON_NICK_NAME:
                Account person = processSearchHandler.getAccount(tenantId, null, param.getValue().getString(0));
                appendRelatedPerson(model, person);
                break;
            case PERSON_IDENTITY:
                Account identityPerson = processSearchHandler.getAccount(tenantId, null, null, param.getValue().getString(0));
                appendRelatedPerson(model, identityPerson);
                break;
            default:
                log.warn("setFieldParam key {} not find", param.getKey());
                break;
        }
    }

    /**
     * 追加参与方oid或gid
     * @param model
     * @param person
     */
    private void appendRelatedPerson(QueryGroupingProcessListRequest model, Account person) {
        if (null == person) {
            return;
        }
        if (CollectionUtils.isEmpty(model.getRelatedPersonList())) {
            model.setRelatedPersonList(Lists.newArrayList(person));
            return;
        }
        model.getRelatedPersonList().add(person);
    }

    private List<String> filterBlank(List<String> list){
        return list.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    private void buildRenewalStatus(List<Integer> statusList, QueryGroupingProcessListRequest model,
                                    String subjectOid){
        if (statusList.contains(ProcessRelationStatusEnum.NO_NEED.getStatus())){
            model.setNoNeedRenewable(true);
            model.setRenewableSubjectOid(subjectOid);
            statusList.remove(ProcessRelationStatusEnum.NO_NEED.getStatus());
        }

        if (statusList.contains(ProcessRelationStatusEnum.RELATABLE.getStatus())){
            model.setUnRenewable(true);
            model.setRenewableSubjectOid(subjectOid);
            statusList.remove(ProcessRelationStatusEnum.RELATABLE.getStatus());
        }

        if (!ListUtils.isEmpty(statusList)){
            model.setRenewalStatusList(statusList.stream().map(this::convertProcessStatus).collect(Collectors.toList()));
        }
    }

    public Map<String, List<String>> getMembersId(String deptId, String tenantId){
        Map<String, List<String>> participant = new HashMap<>();
        List<String> participantGids = new ArrayList<>();
        List<String> participantOids = new ArrayList<>();
        String organId = userCenterService.orgIdToOrganId(tenantId);
        //获取组织成员架构
        BizEsMemberWithDeptSearchInput input = new BizEsMemberWithDeptSearchInput();
        input.setOrganId(organId);
        input.setOffset(0);
        input.setSize(1000);
        input.setDeptId(deptId);
        input.setMemberGuidExist(true);
        PagerResult<MemberDetail> pagerResult = esAccountService.
                getOrgMemberWithDeptList(new RpcInput<>(input)).getData();
        pagerResult.getItems().stream().forEach(x ->{
            if(StringUtils.isNotBlank(x.getMemberGid())){
                participantGids.add(x.getMemberGid());
            }else {
                participantOids.add(x.getMemberOid());
            }
        });
        participant.put("participantOids",participantOids);
        participant.put("participantGids",participantGids);
        return participant;
    }
    /**
     * 获取时间查询入参
     *
     * @param value 入参
     * @return 返回参数
     */
    private TimeQueryParam getTimeQueryParam(JSONArray value) {
        Long start = null;
        Long end = null;
        if (null != value.get(0) && !value.get(0).equals("")) {
            start = value.getLong(0);
        }
        if(value.size() > 1 && value.get(1) != null){
            end = value.getLong(1);
        }
        TimeQueryParam param = new TimeQueryParam();
        param.setStart(start);
        param.setEnd(end);
        return param;
    }
}

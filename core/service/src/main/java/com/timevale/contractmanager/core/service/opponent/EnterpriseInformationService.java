package com.timevale.contractmanager.core.service.opponent;

import com.timevale.contractmanager.core.model.bo.opponent.OpponentEnterpriseInformationBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentOrgSummaryBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentSimpleInfoListBO;

/**
 * 获取企业信息对接启信宝
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
public interface EnterpriseInformationService {

    /**
     * 获取企业详情
     * @param keyword
     * @return
     */
    OpponentEnterpriseInformationBO getEnterpriseInformation(String keyword);

    /**
     * 获取企业跳转地址
     * @param orgName
     * @return
     */
    OpponentOrgSummaryBO getEnterpriseUrl(String orgName);

    /**
     * 高级查询
     * @param keyword
     * @return
     */
    OpponentSimpleInfoListBO enterpriseSimpleInfo(String keyword);
}

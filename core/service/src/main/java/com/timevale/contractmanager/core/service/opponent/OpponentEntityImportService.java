package com.timevale.contractmanager.core.service.opponent;

import com.timevale.contractmanager.common.service.enums.opponent.OpponentEntityTypeEnum;
import com.timevale.contractmanager.core.model.dto.response.opponent.OpponentEntityExcelResponse;
import com.timevale.contractmanager.core.service.mq.model.opponent.OpponentImportMsg;

/**
 * 批量导入service
 *
 * <AUTHOR>
 * @since 2021-02-01 17:23
 */
public interface OpponentEntityImportService {

    /**
     * 获取批量导入模板下载地址
     *
     * @param entityType 模板类型 个人或企业
     * @return 下载地址
     */
    String getDownloadUrl(OpponentEntityTypeEnum entityType);

    /**
     * 批量导入
     *
     * @param operatorOid 当前操作人
     * @param tenantOid 企业空间id
     * @param fileKey 导入文件fileKey
     * @param entityType 导入相对方类型 个人或企业
     * @return 导入结果
     */
    OpponentEntityExcelResponse batchImport(
            String operatorOid,
            String tenantOid,
            String fileKey,
            OpponentEntityTypeEnum entityType);


    /**
     * 异步导入
     */
    void asyncImportOpponent(OpponentImportMsg importMsg);

}

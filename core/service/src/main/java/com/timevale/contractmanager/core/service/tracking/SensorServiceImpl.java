package com.timevale.contractmanager.core.service.tracking;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.timevale.cat.toolkit.datacollect.DataCollector;
import com.timevale.contractmanager.common.dal.bean.ContractProcessGroupDO;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.dal.bean.SubProcessDO;
import com.timevale.contractmanager.common.dal.dao.ContractProcessGroupDAO;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.enums.*;
import com.timevale.contractmanager.common.service.enums.sharesign.ShareSignFromEnum;
import com.timevale.contractmanager.common.service.integration.client.AuthRelationRpcServiceClient;
import com.timevale.contractmanager.common.service.integration.client.DingSignClient;
import com.timevale.contractmanager.common.service.integration.client.DocCooperationClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.model.preference.GlobalWatermarkPreference;
import com.timevale.contractmanager.core.model.bo.*;
import com.timevale.contractmanager.core.model.dto.request.GenerateRescindDocRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.request.sharesign.ShareSignTaskStartRequest;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import com.timevale.contractmanager.core.service.configs.CommonBizConfig;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.bean.UniProcessTransferInput;
import com.timevale.contractmanager.core.service.tracking.bean.*;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.contractmanager.core.service.util.TemplateUtils;
import com.timevale.doccooperation.service.enums.*;
import com.timevale.doccooperation.service.model.DocTemplate;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saas.auth.api.facade.result.CorpBaseInfoRsp;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizHierarchyTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizSceneEnum;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkTypeEnum;
import com.timevale.saas.common.manage.common.service.model.bean.watermark.WatermarkTemplate;
import com.timevale.saas.common.manage.common.service.model.input.watermark.WatermarkTemplateDetailInput;
import com.timevale.saas.common.manage.common.service.model.output.AccountVipQueryOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationDTO;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SensorServiceImpl implements SensorService {
    
    public static final String PROCESS_ID = "processId";
    public static final String THIRD_PARTY_ORG_ID = "third_party_org_id"; // 三方组织id（神策唯一用户id）
    public static final String THIRD_PARTY_ORG_NAME = "ThirdPartyOrgName"; // 三方组织名称
    public static final String PLATFORM_TYPE = "PlatformType"; // 产品端

    @Autowired BaseProcessService baseProcessService;
    @Autowired SaasCommonClient saasCommonClient;
    @Autowired ContractProcessGroupDAO processGroupDAO;
    @Autowired DocCooperationClient docCooperationClient;
    @Autowired private UserCenterService userCenterService;
    @Autowired private DingSignClient dingSignClient;
    @Autowired private AuthRelationRpcServiceClient authRelationRpcServiceClient;
    /**
     * 神策埋点
     *
     * @param distinctId
     * @param event
     * @param data
     */
    @Override
    public void sensorsCollect(String distinctId, String event, Map<String, Object> data) {
        boolean isLoginId = StringUtils.isNotEmpty(distinctId);
        if (!isLoginId) {
            distinctId = UUID.randomUUID().toString();
        }
        log.info("埋点数据，distinctId:{}, event:{}, data:{}", distinctId, event, JSONObject.toJSONString(data));
        DataCollector.sensorsCollect(distinctId, isLoginId, event, data);
    }

    @Override
    @Async("sensorExecutor")
    public void processStartTracking(String processId, ProcessStartCoreRequest request) {

        // 如果神策埋点开关未打开， 跳过
        if (!CommonBizConfig.PROCESS_SENSOR_ENABLE) {
            return;
        }

        log.info("触发流程开启埋点，processId:{}", processId);
        ProcessStartSensorBean bean = new ProcessStartSensorBean();
        bean.setPlatformType(request.getClientId());
        bean.setOid(request.getInitiatorAccount().getAccountOid());
        bean.setOperatorOid(request.getInitiatorAccount().getAccountOid());
        bean.setOperatorGid(request.getInitiatorAccount().getAccountGid());
        bean.setAuthorizedOid(request.getTenantAccount().getAccountOid());
        bean.setAuthorizedGid(request.getTenantAccount().getAccountGid());
        AccountBean tenant = userCenterService.getAccountBeanByOid(request.getTenantAccount().getAccountOid());
        if (tenant != null) {
            bean.setEntityName(tenant.getName());
        }
        bean.setVipVersion(
                saasCommonClient.queryAccountVipLevel(
                        request.getTenantAccount().getAccountOid(), request.getClientId()));
        bean.setProcessId(processId);
        bean.setProcessType(ProcessBusinessType.from(request.getBusinessType()).getDesc());
        bean.setAppid(request.getAppId());
        bean.setProcessStartType(ProcessStartType.from(request.getStartType()).getLabel());
        // 设置合同保密类型
        if (null != request.getProcessSecretConfig()) {
            bean.setProcessSecretType(request.getProcessSecretConfig().getSecretType());
        }

        List<FileBO> contracts = request.getContracts();

        if (Objects.equals(ShareSignFromEnum.TEMPLATE_SIGN.getType(), request.getStartScene())) {


            String flowTemplateId = StringUtils.defaultIfBlank(request.getOriginFlowTemplateId(), request.getFlowTemplateId());
            GetFlowTemplateResult flowTemplateResult = docCooperationClient.getFlowTemplateByFlowTemplateId(flowTemplateId, request.getTenantAccount().getAccountGid(), request.getTenantAccount().getAccountOid(), null, null);
            if(Objects.nonNull(flowTemplateResult)){
                //是否共享模板
                bean.setTemplateOutside(flowTemplateResult.isShared());
                //是否非标模板
                boolean unStandardTemplate = Objects.nonNull(flowTemplateResult.getTemplateConfig()) && flowTemplateResult.getTemplateConfig().getUnStandardTemplate();
                bean.setUnStandardTemplate(unStandardTemplate);
                List<DocTemplate> docTemplates = flowTemplateResult.getDocTemplates();
                //是否替换模板文件
                boolean hasReplaceFiles = docTemplates.stream().anyMatch(docTemplate -> unStandardTemplate && docTemplate.getSupportReplace() && !docTemplate.getDisableEdit());
                bean.setReplaceFiles(hasReplaceFiles);

                //是否有添加文件
                boolean hasAddFiles = docTemplates.stream().anyMatch(docTemplate -> unStandardTemplate && Objects.isNull(docTemplate.getOriginalTemplateId()) && !docTemplate.getDisableEdit());
                bean.setAddFiles(hasAddFiles);
            }
        }

        // 签署文件个数
        bean.setFileamount(CollectionUtils.isEmpty(contracts) ? 0 : contracts.size());
        // 是否使用动态模板
        bean.setUseDynamicTemplate(TemplateUtils.hasDynamicTemplate(contracts));
        List<FileBO> attachments = request.getAttachments();
        bean.setAttachmentAmount(CollectionUtils.isEmpty(attachments) ? 0 : attachments.size());

        Set<Integer> signOrders = Sets.newHashSet();
        for (ParticipantBO participant : request.getParticipants()) {
            ParticipantInstanceBO instanceBO = participant.getInstances().get(0);
            List<String> roles = IdsUtil.getIdList(participant.getRole());
            // 如果当前参与方需填写， 填写方统计+1
            if (roles.contains(CooperationerRoleEnum.FORMULATER.getRole().toString())) {
                bean.setFillAmount(bean.getFillAmount() + 1);
            }

            if (Objects.equals(participant.getParticipantStartType(), ParticipantStartType.PARTICIPANT_ASSIGN_ACCOUNT.getType())) {
                bean.setOrgOperatorInitiation("1");
            }
            
            if (Objects.equals(participant.getParticipantStartType(), ParticipantStartType.PARTICIPANT_ASSIGN_ORG.getType())) {
                bean.setOrgNameInitiation("1");
            }

            if (Objects.equals(participant.getParticipantStartType(), ParticipantStartType.PARTICIPANT_ASSIGN_ROLE.getType())) {
                bean.setOrgRoleInitiation("1");
            }

            String name = instanceBO.getAccountName();
            String mobile = instanceBO.getAccount();
            String idCard = instanceBO.getAccountLicense();
            
            if (name != null && mobile != null && idCard != null) {
                bean.setPsnCompleteInitiation("1");
            } else if (name != null && idCard != null) {
                bean.setPsnIdCardInitiation("1");
            } else if (name != null && mobile != null) {
                bean.setPsnMobileInitiation("1");
            }
            

            // 如果当前参与方需签署， 统计签署相关数据
            if (roles.contains(CooperationerRoleEnum.SIGNER.getRole().toString())) {
                boolean psnSign;
                // 统计个人主体签署方和企业主体签署方数量
                if (SubjectTypeEnum.ORG.getType().equals(instanceBO.getSubjectType())) {
                    bean.setOrgSignatoryAmount(bean.getOrgSignatoryAmount() + 1);
                    List<String> requirements = IdsUtil.getIdList(participant.getSignRequirements());
                    psnSign = requirements.contains("2");
                } else {
                    bean.setPersonSignatoryAmount(bean.getPersonSignatoryAmount() + 1);
                    psnSign = true;
                }
                // 分别统计个人指定手绘章/模板章/AI手绘章数量
                if (psnSign) {
                    List<String> sealTypes = IdsUtil.getIdList(participant.getSealType());
                    if (sealTypes.contains(SealTypeEnum.HAND_DRAW.getType())) {
                        bean.setHanddrawAmount(bean.getHanddrawAmount() + 1);
                    }
                    if (sealTypes.contains(SealTypeEnum.AI_HAND.getType())) {
                        bean.setAiHanddrawAmount(bean.getAiHanddrawAmount() + 1);
                    }
                    if (sealTypes.contains(SealTypeEnum.SEAL.getType())) {
                        bean.setTemplateSealAmount(bean.getTemplateSealAmount() + 1);
                    }
                }
                // 收集签署顺序，用于判断是否顺序签署
                signOrders.add(participant.getSignOrder());

                // 判断当前空间是否签署方
                UserAccount signAuthorizer = new UserAccount();
                signAuthorizer.setAccountGid(instanceBO.getSubjectGid());
                signAuthorizer.setAccountOid(instanceBO.getSubjectId());
                if (request.getTenantAccount().isSameAccount(signAuthorizer)) {
                    bean.setAuthorizedOidSign(true);
                }
            }
            //指定印章和阅读时长埋点
            if(participant.getSignSealType() != null){
                if(participant.getSignSealType() == StartSignSpecifySealTypeEnum.BIZ_TYPE.getType()){
                    bean.setNumberOfFixedSealType(bean.getNumberOfFixedSealType() + 1);
                } else if (participant.getSignSealType() == StartSignSpecifySealTypeEnum.SEAL_ID.getType()){
                    bean.setNumberFixedSeal(bean.getNumberFixedSeal() + 1);
                }
            }
            if(participant.getForceReadEnd() != null && participant.getForceReadEnd()){
                bean.setNumberOfBxyddd(bean.getNumberOfBxyddd() + 1);
            }
            if(participant.getForceReadTime() != null && participant.getForceReadTime() != 0) {
                bean.setNumberOfZdydsc(bean.getNumberOfZdydsc() + 1);
            }
            // 神策埋点签署声明
            if (StringUtils.isNotBlank(participant.getSignTipsTitle()) || StringUtils.isNotBlank(participant.getSignTipsContent())) {
                bean.setNumberOfSignStatement(bean.getNumberOfSignStatement() + 1);
            }
            // 指定附件
            List<AttachmentConfigBO> attachmentConfigs = participant.getAttachmentConfigs();
            if (CollectionUtils.isNotEmpty(attachmentConfigs)) {
                bean.setNumberOfAttachments(bean.getNumberOfAttachments() + 1);
                // 指定附件核验
                if (hasAttachmentCheck(attachmentConfigs)) {
                    bean.setNumberOfAttachmentsVerification(bean.getNumberOfAttachmentsVerification() + 1);
                }
            }
            // 意愿认证
            List<String> willTypes = participant.getWillTypes();
            if (CollectionUtils.isNotEmpty(willTypes)) {
                for (String willType : willTypes) {
                    if (WillTypeEnum.FACE.getType().equals(willType)) {
                        bean.setNumberOfWillingnessFace(bean.getNumberOfWillingnessFace() + 1);
                    }
                    if (WillTypeEnum.MOBILE.getType().equals(willType)) {
                        bean.setNumberOfWillingnessMessage(bean.getNumberOfWillingnessMessage() + 1);
                    }
                    if (WillTypeEnum.EMAIL.getType().equals(willType)) {
                        bean.setNumberOfWillingnessEMail(bean.getNumberOfWillingnessEMail() + 1);
                    }
                    if (WillTypeEnum.PWD.getType().equals(willType)) {
                        bean.setNumberOfWillingnessPassword(bean.getNumberOfWillingnessPassword() + 1);
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(request.getUseWatermarkTemplateId())) {
            WatermarkTemplateDetailInput input = new WatermarkTemplateDetailInput();
            // 获取发起时指定的水印模板ID
            input.setWatermarkId(request.getUseWatermarkTemplateId());
            input.setNeedImageUrl(false);
            input.setOid(request.getTenantAccount().getAccountOid());
            input.setOperatorOid(request.getInitiatorAccount().getAccountOid());
            WatermarkTemplate watermarkDetail = saasCommonClient.getWatermarkTemplateDetail(input);
            bean.setWatermarkType(
                    WatermarkTypeEnum.DOC_WATERMARK.getType().equals(watermarkDetail.getType())
                            ? "wenjian"
                            : "pingmu");
        }
        bean.setCcAmount(null != request.getCcs() ? request.getCcs().size() : 0);
        bean.setSigndatelimit(null != request.getSignEndTime() ||
                ValidityTypeEnum.USE_ASSIGNED.getType() != request.getSignValidityConfig().getValidityType());
        bean.setContractdatelimit(null != request.getFileEndTime());
        bean.setOrdersign(signOrders.size() > 1);
        bean.setProcessStartWay(parseProcessCreateWay(request.getProcessGroupId()));

        if (StringUtils.isNotBlank(request.getCorpId())) {
            bean.setCorpId(request.getCorpId());
            try {
                CorpBaseInfoRsp rsp = dingSignClient.queryCorpBaseInfo(request.getCorpId());
                bean.setCorpName(rsp.getCorpName());
                bean.setMainEnterpriseName(rsp.getMainEnterpriseName());
            } catch (Exception e) {
                log.warn("queryCorpBaseInfo failed when sensor, corpId: {} , message: {}", request.getCorpId(), e.getMessage());
            }
        }

        sensorsCollect(bean.getOid(), SensorEventEnum.PROCESS_START.getEvent(), bean.sensorData());
        // 三方应用埋点
        ecoOrgSigningCompleted(processId, tenant, request);
        participantStartTracking(bean);

        String tenantId = request.getTenantAccount().getAccountOid();
        contracts.forEach(f -> createRuleTracking(tenantId, processId, f));
    }

    public void ecoOrgSigningCompleted(String processId, AccountBean tenant, ProcessStartCoreRequest request) {
        // 没有触发异常说明归档成功，触发埋点-查询流程信息，满足条件走埋点
        String source = request.getClientId();
        if (StringUtils.isBlank(source) || (!ClientEnum.DING_TALK.getClientId().equals(source)
                && !ClientEnum.FEI_SHU.getClientId().equals(source)
                && !ClientEnum.WE_WORK.getClientId().equals(source))) {
            return;
        }
        
        // 获取三方ID
        String thirdId = userCenterService.getAccountIdCardInfoByEs(tenant.getOid(), source);
        
        // 如果本获取不到三方id尝试使用主企业来获取三方Id
        if (StringUtils.isBlank(thirdId)) {

            List<AuthRelationDTO> authRelations = authRelationRpcServiceClient
                    .listEffectiveAuthRelationByChildTenantGidList(
                            Collections.singletonList(tenant.getGid()),
                            AuthRelationBizSceneEnum.DEFAULT.getCode());
            
            String parentTenantOid = authRelations.stream()
                    .filter(relation -> Objects.equals(relation.getBizHierarchyType(), AuthRelationBizHierarchyTypeEnum.DIRECT.getCode()))
                    .map(AuthRelationDTO::getParentTenantOid)
                    .filter(com.timevale.mandarin.base.util.StringUtils::isNotBlank)
                    .findFirst()
                    .orElseGet(() -> authRelations.stream()
                            .filter(relation -> Objects.equals(relation.getBizHierarchyType(), AuthRelationBizHierarchyTypeEnum.AUTH_AND_DIRECT.getCode()))
                            .map(AuthRelationDTO::getParentTenantOid)
                            .filter(com.timevale.mandarin.base.util.StringUtils::isNotBlank)
                            .findFirst()
                            .orElse(null));
            // 获取三方ID
            thirdId = userCenterService.getAccountIdCardInfoByEs(parentTenantOid, source);
        }
        
        Map<String, Object> properties = new HashMap<>();
        properties.put(PROCESS_ID, processId);
        properties.put(THIRD_PARTY_ORG_ID, thirdId);
        properties.put(THIRD_PARTY_ORG_NAME, tenant.getName());
        properties.put(PLATFORM_TYPE, source);
        log.info("ecoOrgSigning data {}", JsonUtils.obj2json(properties));
        DataCollector.sensorsCollect(
                thirdId, true, "ecoOrgSigning", properties);
    }

    /**
     * 按参与方数量埋点
     *
     * @param bean
     */
    private void participantStartTracking(ProcessStartSensorBean bean) {
        int personSignatoryAmount = bean.getPersonSignatoryAmount();
        int orgSignatoryAmount = bean.getOrgSignatoryAmount();
        int signatoryAmount = personSignatoryAmount + orgSignatoryAmount;

        if (signatoryAmount <= 0) {
            return;
        }

        if (signatoryAmount == 1) {
            // 单方发起埋点
            sensorsCollect(
                    bean.getOid(),
                    SensorEventEnum.ONE_PARTICIPATE_PROCESS_START.getEvent(),
                    bean.sensorData());
        } else if (signatoryAmount == 2) {
            // 双方发起埋点
            sensorsCollect(
                    bean.getOid(),
                    SensorEventEnum.TWO_PARTICIPATE_PROCESS_START.getEvent(),
                    bean.sensorData());
        } else {
            // 多方发起埋点
            sensorsCollect(
                    bean.getOid(),
                    SensorEventEnum.MORE_PARTICIPATE_PROCESS_START.getEvent(),
                    bean.sensorData());
        }
    }

    @Override
    public void processEndTracking(String accountId, ProcessDO process, Integer processStatus) {

        // 如果神策埋点开关未打开， 跳过
        if (!CommonBizConfig.PROCESS_SENSOR_ENABLE) {
            return;
        }
        log.info("触发流程结束埋点，processId:{}, processStatus:{}", process.getProcessId(), processStatus);
        ProcessEndSensorBean bean = new ProcessEndSensorBean();
        bean.setProcessId(process.getProcessId());
        bean.setProcessIdStatus(processStatus.toString());
        List<SubProcessDO> subProcesses = baseProcessService.getSubProcesses(process.getProcessId());
        for (SubProcessDO subProcess : subProcesses) {
            if (SubProcessTypeEnum.COOPERATION.getType() == subProcess.getSubProcessType()) {
                bean.setFillingId(subProcess.getSubProcessId());
            }
            if (SubProcessTypeEnum.SIGN.getType() == subProcess.getSubProcessType()) {
                bean.setFlowId(subProcess.getSubProcessId());
            }
            if (SubProcessTypeEnum.CONTRACT_APPROVAL.getType() == subProcess.getSubProcessType()) {
                bean.setContractApprovalId(subProcess.getSubProcessId());
            }
        }
        bean.setAppId(process.getAppId());
        bean.setSigndatelimit(process.getProcessEndTime() != null);
        bean.setDuration(System.currentTimeMillis() - process.getCreateTime().getTime());
        bean.setOperatorOid(process.getInitiatorOid());
        bean.setOperatorGid(process.getInitiatorGid());
        bean.setAuthorizedOid(process.getSubjectOid());
        bean.setAuthorizedGid(process.getSubjectGid());
        sensorsCollect(accountId, SensorEventEnum.PROCESS_END.getEvent(), bean.sensorData());
    }

    @Override
    public void processTransferTracking(
            ProcessDO process, String flowId, Integer flowType, UniProcessTransferInput input) {

        // 如果神策埋点开关未打开， 跳过
        if (!CommonBizConfig.PROCESS_SENSOR_ENABLE) {
            return;
        }
        log.info("触发流程转交埋点，processId:{}", process.getProcessId());
        ProcessTransferSensorBean bean = new ProcessTransferSensorBean();
        bean.setProcessId(process.getProcessId());
        bean.setFlowId(flowId);
        bean.setFlowType(SubProcessTypeEnum.COOPERATION.getType() == flowType ? "填写任务" : "签署任务");
        bean.setAppId(process.getAppId());
        bean.setTransferType(ProcessTransferSceneEnum.isAuto(input.getTransferScene()) ? "自动转交" : "手动转交");
        // 如果转交类型不为空，且仅转交待办任务，设置埋点中的转交模式为待办任务转交， 否则均为全流程转交
        if (CollectionUtils.isNotEmpty(input.getTransferTypes()) && input.getTransferTypes().size() == 1) {
            String transferType = input.getTransferTypes().get(0);
            ProcessTransferTypeEnum typeEnum = ProcessTransferTypeEnum.from(transferType);
            bean.setTransferWay(null != typeEnum ? typeEnum.getDesc() : transferType);
        } else {
            bean.setTransferWay("全流程转交");
        }

        sensorsCollect(input.getOperatorId(), SensorEventEnum.PROCESS_TRANSFER.getEvent(), bean.sensorData());
    }

    @Override
    public void shareSignTaskStartTracking(ShareSignTaskStartRequest request, UserAccount subject) {

        // 如果神策埋点开关未打开， 跳过
        if (!CommonBizConfig.PROCESS_SENSOR_ENABLE) {
            return;
        }
        log.info("触发扫码任务发起埋点");
        ShareSignTaskStartSensorBean bean = new ShareSignTaskStartSensorBean();
        bean.setInitiatorSubjectName(subject.getAccountName());
        bean.setShareTaskTotal(request.getShareTaskTotal());
        bean.setPlatformType(RequestContextExtUtils.getClientId());
        sensorsCollect(request.getInitiatorAccountId(), SensorEventEnum.SHARE_SIGN_TASK_START.getEvent(), bean.sensorData());
    }

    @Override
    public void templatematchStandingBookTracking(String tenantId, String processId, String orgName) {
        log.info("触发模板合同提取信息埋点，processId:{}, tenantId:{},orgName:{}", processId,tenantId,orgName);
        TemplatematchStandingBook standingBook = new TemplatematchStandingBook();
        standingBook.setTenantId(tenantId);
        standingBook.setProcessId(processId);
        standingBook.setOrgName(orgName);
        sensorsCollect(tenantId,SensorEventEnum.TEMPLATEMATCH_STANDINGBOOK.getEvent(),standingBook.sensorData());
    }

    @Override
    public void unTemplatematchStandingBookTracking(String tenantId, String processId, String orgName) {
        log.info("触发非模板合同提取信息埋点，processId:{}, tenantId:{},orgName:{}", processId,tenantId,orgName);
        TemplatematchStandingBook standingBook = new TemplatematchStandingBook();
        standingBook.setTenantId(tenantId);
        standingBook.setProcessId(processId);
        standingBook.setOrgName(orgName);
        sensorsCollect(tenantId,SensorEventEnum.UN_TEMPLATEMATCH_STANDINGBOOK.getEvent(),standingBook.sensorData());
    }

    @Override
    public void autoArchiveTracking(String tenantId, String orgName, String menuId) {
        try {
			log.info("触发新台账自动归档提取信息埋点，menuId:{}, tenantId:{},orgName:{}", menuId, tenantId, orgName);
			BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
			AutoArchiveSensorBean sensorBean = new AutoArchiveSensorBean();
			BeanUtils.copyProperties(baseAttributeSensorBean, sensorBean);
			sensorBean.setMenuId(StringUtils.isNotBlank(menuId)? menuId : "");
			sensorsCollect(tenantId,SensorEventEnum.AUTO_ARCHIVE.getEvent(), sensorBean.sensorData());
        }catch (Exception e){
            log.info("触发新台账自动归档提取信息埋点异常，menuId:{}, tenantId:{},orgName:{}", menuId, tenantId, orgName);
        }
    }

    @Override
    public void processDirectSwitchTracking(String tenantId, String orgName,boolean isOpen) {
        //todo 埋点信息模版
        log.info("触发合同发起权限开关埋点,tenantId:{},orgName:{},isOpen:{}",tenantId,orgName,isOpen);
        Map<String,Object> data = Maps.newHashMap();
        data.put(SensorConstants.ORG_NAME,orgName);
        sensorsCollect(tenantId,isOpen ? SensorEventEnum.OPEN_INITIATE_CONTRACT.getEvent() : SensorEventEnum.CLOSE_INITIATE_CONTRACT_ALL.getEvent(),data);
    }

    @Override
    public void opponentDetection(String tenantId, String orgName) {
        log.info("触发相对方检测企业埋点, tenantId:{},orgName:{}", tenantId, orgName);
        OpponentDetectionSensorBean sensorBean = new OpponentDetectionSensorBean();
        sensorBean.setOrgName(orgName);
        sensorBean.setTenantId(tenantId);
        sensorsCollect(tenantId,SensorEventEnum.OPPONENT_DETECTION.getEvent(), sensorBean.sensorData());
    }

    @Override
    public void opponentDetectionProblemReport(String tenantId, String orgName, Integer problemType) {
        log.info("触发相对方检测企业问题类型埋点, tenantId:{},orgName:{}", tenantId, orgName);
        OpponentDetectionProblemSensorBean sensorBean = new OpponentDetectionProblemSensorBean();
        sensorBean.setOrgName(orgName);
        sensorBean.setTenantId(tenantId);
        sensorBean.setProblemType(problemType);
        sensorsCollect(tenantId,SensorEventEnum.OPPONENT_DETECTION_PROBLEM_REPORT.getEvent(), sensorBean.sensorData());
    }

    @Override
    @Async("sensorExecutor")
    public void processesListTracking(String tenantId, String menuName, Set<Integer> status,
                                      Boolean isInitiateTime, Boolean isFinishTime, Set<Integer> bizTypeList,
                                      Long processNum, Long elapsedTime) {
        log.info("触发经办合同列表加载埋点, tenantId:{}", tenantId);
        BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
        ProcessListSensorBean processListSensorBean = new ProcessListSensorBean();
        BeanUtils.copyProperties(baseAttributeSensorBean, processListSensorBean);
        processListSensorBean.setProcessType(parseProcessBusinessType(bizTypeList));
        processListSensorBean.setListName(menuName);
        processListSensorBean.setChosenProcessStatus(parseProcessStatus(status));
        processListSensorBean.setIsInitiateTime(isInitiateTime);
        processListSensorBean.setIsFinishTime(isFinishTime);
        processListSensorBean.setNumberOfProcess(processNum);
        processListSensorBean.setReturnTime(elapsedTime);
        sensorsCollect(tenantId, SensorEventEnum.PROCESS_LIST.getEvent(), processListSensorBean.sensorData());
        log.info("触发经办合同列表加载埋点完成, tenantId:{}", tenantId);
    }

    @Override
    @Async("sensorExecutor")
    public void processBatchDeleteTracking(String tenantId, String menuName, Long processNum, Long elapsedTime, String authenticationResult, String authenticationFailureReason, String processingResult) {
        try {
            log.info("触发删除合同埋点, tenantId:{}", tenantId);
            BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
            ProcessBatchDeleteSensorBean processBatchDeleteSensorBean = new ProcessBatchDeleteSensorBean();
            BeanUtils.copyProperties(baseAttributeSensorBean, processBatchDeleteSensorBean);
            processBatchDeleteSensorBean.setNumberOfProcess(processNum);
            processBatchDeleteSensorBean.setProcessingResult(processingResult);
            processBatchDeleteSensorBean.setAuthenticationFailureReason(authenticationFailureReason);
            processBatchDeleteSensorBean.setListName(menuName);
            processBatchDeleteSensorBean.setReturnTime(elapsedTime);
            processBatchDeleteSensorBean.setAuthenticationResult(authenticationResult);
            sensorsCollect(tenantId, SensorEventEnum.PROCESS_DELETE.getEvent(), processBatchDeleteSensorBean.sensorData());
            log.info("触发删除合同埋点完成, tenantId:{}", tenantId);
        }catch (Exception e){
            log.info("触发合同埋点异常", e);
        }
    }

    @Override
    @Async("sensorExecutor")
    public void processDownloadContractTracking(String tenantId, String menuName, Long processNum, Long elapsedTime, String processingResult, String authenticationResult, String authenticationFailureReason) {
        log.info("触发下载合同埋点, tenantId:{}", tenantId);
        BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
        ProcessDownloadContractSensorBean downloadContractSensorBean = new ProcessDownloadContractSensorBean();
        BeanUtils.copyProperties(baseAttributeSensorBean, downloadContractSensorBean);
        downloadContractSensorBean.setNumberOfProcess(processNum);
        downloadContractSensorBean.setListName(menuName);
        downloadContractSensorBean.setReturnTime(elapsedTime);
        downloadContractSensorBean.setProcessingResult(processingResult);
        downloadContractSensorBean.setAuthenticationResult(authenticationResult);
        downloadContractSensorBean.setAuthenticationFailureReason(authenticationFailureReason);
        sensorsCollect(tenantId, SensorEventEnum.PROCESS_DOWNLOAD_CONTRACT.getEvent(), downloadContractSensorBean.sensorData());
        log.info("触发下载合同埋点完成, tenantId:{}", tenantId);
    }

    @Override
    @Async("sensorExecutor")
    public void processUpdateContractValidityTracking(String tenantId, String menuName, Long processNum, Long elapsedTime, String processingResult, String authenticationResult, String authenticationFailureReason) {
        try {
            log.info("触发修改到期日期埋点, tenantId:{}", tenantId);
            BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
            ProcessUpdateContractValiditySensorBean sensorBean = new ProcessUpdateContractValiditySensorBean();
            BeanUtils.copyProperties(baseAttributeSensorBean, sensorBean);
            sensorBean.setNumberOfProcess(processNum);
            sensorBean.setListName(menuName);
            sensorBean.setReturnTime(elapsedTime);
            sensorBean.setProcessingResult(processingResult);
            sensorBean.setAuthenticationResult(authenticationResult);
            sensorBean.setAuthenticationFailureReason(authenticationFailureReason);
            sensorsCollect(tenantId, SensorEventEnum.EDIT_DUE_DATE_SEVER.getEvent(), sensorBean.sensorData());
            log.info("触发修改到期日期埋点完成, tenantId:{}", tenantId);
        }catch (Exception e){
            log.info("触发修改到期日期埋点异常", e);
        }
    }

    @Override
    @Async("sensorExecutor")
    public void processUpdateRenewableTracking(String tenantId, String menuName, Long processNum, Long elapsedTime, String processingResult, String authenticationResult, String authenticationFailureReason) {
        try {
            log.info("触发修改续签埋点, tenantId:{}", tenantId);
            BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
            ProcessUpdateRenewableSensorBean sensorBean = new ProcessUpdateRenewableSensorBean();
            BeanUtils.copyProperties(baseAttributeSensorBean, sensorBean);
            sensorBean.setNumberOfProcess(processNum);
            sensorBean.setListName(menuName);
            sensorBean.setReturnTime(elapsedTime);
            sensorBean.setProcessingResult(processingResult);
            sensorBean.setAuthenticationResult(authenticationResult);
            sensorBean.setAuthenticationFailureReason(authenticationFailureReason);
            sensorsCollect(tenantId, SensorEventEnum.EDIT_RENEWAL_SEVER.getEvent(), sensorBean.sensorData());
            log.info("触发修改续签埋点完成, tenantId:{}", tenantId);
        }catch (Exception e){
            log.info("触发修改续签埋点异常", e);
        }
    }

    @Override
    @Async("sensorExecutor")
    public void exportProcessTracking(String tenantId, String menuName, Long processNum, Long elapsedTime, String processingResult, String authenticationResult, String authenticationFailureReason) {
        try {
            log.info("触发导出合同明细埋点, tenantId:{}", tenantId);
            BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
            ExportProcessSensorBean sensorBean = new ExportProcessSensorBean();
            BeanUtils.copyProperties(baseAttributeSensorBean, sensorBean);
            sensorBean.setNumberOfProcess(processNum);
            sensorBean.setListName(menuName);
            sensorBean.setReturnTime(elapsedTime);
            sensorBean.setProcessingResult(processingResult);
            sensorBean.setAuthenticationResult(authenticationResult);
            sensorBean.setAuthenticationFailureReason(authenticationFailureReason);
            sensorsCollect(tenantId, SensorEventEnum.EXPORT_CONTRACT_INFORMATION_SEVER.getEvent(), sensorBean.sensorData());
            log.info("触发导出合同明细完成埋点, tenantId:{}", tenantId);
        }catch (Exception e){
            log.info("触发合同导出明细埋点异常", e);
        }
    }

    @Override
    @Async("sensorExecutor")
    public void groupListTracking(String tenantId, String menuName, Long processNum, Long elapsedTime,
                                  String processingResult, Integer numOfField, String field) {
        log.info("触发企业合同列表加载埋点, tenantId:{}", tenantId);
        BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
        GroupListSensorBean sensorBean = new GroupListSensorBean();
        BeanUtils.copyProperties(baseAttributeSensorBean, sensorBean);
        sensorBean.setField(field);
        sensorBean.setListName(menuName);
        sensorBean.setNumberOfField(numOfField);
        sensorBean.setProcessingResult(processingResult);
        sensorBean.setReturnTime(elapsedTime);
        sensorBean.setNumberOfProcess(processNum);
        sensorsCollect(tenantId, SensorEventEnum.ENTERPRISE_CONTRACT_LIST_LOAD_SEVER.getEvent(), sensorBean.sensorData());
        log.info("触发企业合同列表加载埋点完成, tenantId:{}", tenantId);
    }

    @Override
    @Async("sensorExecutor")
    public void setMenuPrivilegeTracking(String tenantId, String setType, String folderName, String fatherFolderName, String folderClass, Integer numberOfViewAbleDepartment, Integer numberOfViewAbleStaff, Integer numberOfEditAbleStaff, Integer numberOfEditAbleDepartment, Integer numberOfDownloadAbleStaff, Integer numberOfDownloadAbleDepartment) {
        log.info("触发设置分类授权埋点，tenantId:{}", tenantId);
        BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
        SetMenuPrivilegeSensorBean sensorBean = new SetMenuPrivilegeSensorBean();
        BeanUtils.copyProperties(baseAttributeSensorBean, sensorBean);
        sensorBean.setSetType(setType);
        sensorBean.setFolderName(folderName);
        sensorBean.setFolderClass(folderClass);
        sensorBean.setFatherFolderName(fatherFolderName);
        sensorBean.setNumberOfDditAbleStaff(numberOfEditAbleStaff);
        sensorBean.setNumberOfEditAbleDepartment(numberOfEditAbleDepartment);
        sensorBean.setNumberOfDownloadAbleDepartment(numberOfDownloadAbleDepartment);
        sensorBean.setNumberOfDownloadAbleStaff(numberOfDownloadAbleStaff);
        sensorBean.setNumberOfViewAbleStaff(numberOfViewAbleStaff);
        sensorBean.setNumberOfViewAbleDepartment(numberOfViewAbleDepartment);
        sensorsCollect(tenantId, SensorEventEnum.AUTHORIZE_CONTRACT_FOLDER_SEVER.getEvent(), sensorBean.sensorData());
        log.info("触发设置分类授权完成埋点，tenantId:{}", tenantId);
    }

    @Override
    @Async("sensorExecutor")
    public void moveMenuTracking(String tenantId, String folderName, Long processNum, Long elapsedTime, String processingResult) {
        try {
            BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
            log.info("触发企业合同移动分类埋点，tenantId:{}", tenantId);
            MoveMenuSensorBean moveMenuSensorBean = new MoveMenuSensorBean();
            BeanUtils.copyProperties(baseAttributeSensorBean, moveMenuSensorBean);
            moveMenuSensorBean.setFolderName(folderName);
            moveMenuSensorBean.setProcessingResult(processingResult);
            moveMenuSensorBean.setReturnTime(elapsedTime);
            moveMenuSensorBean.setNumberOfProcess(processNum);
            sensorsCollect(tenantId, SensorEventEnum.CLASSIFY_CONTRACTS_SEVER.getEvent(), moveMenuSensorBean.sensorData());
            log.info("触发企业合同移动分类埋点完成，tenantId:{}", tenantId);
        }catch (Exception e){
            log.info("企业合同移动分类埋点异常",e);
        }
    }

    @Override
    @Async("sensorExecutor")
    public void generateRescindFileTracking(String tenantId, GenerateRescindDocRequest request) {
        try {
            BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, null);
            log.info("触发生成解约协议埋点，request:{}", JSONObject.toJSONString(request));
            GenerateRescindFileSensorBean sensorBean = new GenerateRescindFileSensorBean();
            BeanUtils.copyProperties(baseAttributeSensorBean, sensorBean);
            sensorBean.setReason(request.getRescindRemark());
            sensorBean.setNumberOfOriginalFile(Long.valueOf(request.getRescindFileIds().size()));
            sensorsCollect(tenantId, SensorEventEnum.CREATE_TERMINATION_AGREEMENT.getEvent(), sensorBean.sensorData());
        } catch (Exception e) {
            log.error("generateRescindFileTracking failed", e);
        }
    }

    @Override
    @Async("sensorExecutor")
    public void opponentIndividualListTracking(String tenantId, String operateId) {
        try {
            BaseAttributeSensorBean baseAttributeSensorBean = aggregateProperty(PlatfromEnum.STANDARD_WEB.getDescription(),"",tenantId, operateId);
            OpponentIndividualListSensorBean sensorBean = new OpponentIndividualListSensorBean();
            BeanUtils.copyProperties(baseAttributeSensorBean, sensorBean);
            sensorsCollect(tenantId, SensorEventEnum.GET_PERSON_FROM_OPPONENT.getEvent(), sensorBean.sensorData());
        }catch (Exception e){
            log.info("opponentIndividualListTracking failed tenantId:{}", tenantId, e);
        }
    }

    /**
     * 聚合saas埋点通用属性
     * @param platformType
     * @param module
     * @param tenantOId
     * @return
     */
    public BaseAttributeSensorBean aggregateProperty(String platformType, String module, String tenantOId, String operateId){
        BaseAttributeSensorBean bean = new BaseAttributeSensorBean();
        bean.setPlatformType(platformType);
        bean.setModule(module);
        bean.setVipVersion(StringUtils.isNotBlank(tenantOId) ? saasCommonClient.queryAccountVipLevel(tenantOId, "")+ "" : "");
        bean.setIsFirstTime(true);
        bean.setUserRole("");
        if(StringUtils.isNotBlank(tenantOId)){
            UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOId);
            bean.setEntityType("企业");
            bean.setEntityName(tenantAccount.getAccountName());
            bean.setAuthorizedGid(tenantAccount.getAccountGid());
            bean.setAuthorizedOid(tenantAccount.getAccountOid());
        }else {
            bean.setEntityType("个人");
            bean.setEntityName("");
            bean.setAuthorizedGid("");
            bean.setAuthorizedOid("");
        }
        bean.setOperatorOid("");
        bean.setOperatorGid("");
        bean.setOperatorEntityNum(0);
        return bean;
    }

    /**
     * 获取流程发起模式
     * @param processGroupId
     * @return
     */
    private String parseProcessCreateWay(String processGroupId) {
        if (StringUtils.isBlank(processGroupId)) {
            return "单任务普通发起";
        }
        ContractProcessGroupDO processGroup = processGroupDAO.queryByProcessGroupId(processGroupId);
        if (null == processGroup) {
            return "单任务普通发起";
        }
        Integer processGroupType = processGroup.getProcessGroupType();
        if (GroupTypeEnum.SHARE_START_SINGLE_SCAN.getType().equals(processGroupType)) {
            return "批量扫码发起";
        }
        if (GroupTypeEnum.BATCH_START_MULTI_TEMPLATE_FLOW.getType().equals(processGroupType)) {
            return GroupTypeEnum.BATCH_START_MULTI_TEMPLATE_FLOW.getDesc();
        }
        return "批量普通发起";
    }

    /**
     *
     * @param status
     * @return
     */
    public String parseProcessStatus(Set<Integer> status){
        List<String> descs = status.stream().map(x ->{
            return ProcessStatusEnum.valueOf(x).getStatusDesc();
        }).collect(Collectors.toList());

        return String.join(";", descs);
    }

    /**
     *
     * @param bizTypeList
     * @return
     */
    public String parseProcessBusinessType(Set<Integer> bizTypeList){
        if(bizTypeList.size() == 0){
            return "全部合同";
        }
        List<String> desc = bizTypeList.stream().map(x ->{
            return ProcessBusinessType.from(x).getFrontDesc();
        }).collect(Collectors.toList());
        return String.join(";", desc);
    }


    @Async("sensorExecutor")
    @Override
    public void relationContractQuery(String tenantGid, String tenantName, String operatorGid, int relationContractNum) {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("entityType", "企业");
            data.put("entityName", tenantName);
            data.put("authorizedGid", tenantGid);
            data.put("operatorGid", operatorGid);
            data.put("num_of_relation_contract", relationContractNum);
            sensorsCollect(tenantGid, SensorEventEnum.RELATION_CONTRACT_QUERY.getEvent(), data);
        } catch (Exception e) {
            log.info("relation contract data tracking failure", e);
        }
    }

    @Async("sensorExecutor")
    @Override
    public void saveRuleTracking(String tenantGid, String prefix, Integer timeType, Integer tailType, Integer tailNumber, Integer initNumber, String ruleName) {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("prefix", "企业");
            data.put("timeType", timeType);
            data.put("tailType", tenantGid);
            data.put("tailNumber", tailNumber);
            data.put("initNumber", initNumber);
            data.put("ruleName", ruleName);
            sensorsCollect(tenantGid, SensorEventEnum.SAVE_CONTRACT_NO_RULE_SEVER.getEvent(), data);
        } catch (Exception e) {
            log.info("saveRule data tracking failure", e);
        }
    }

    @Async("sensorExecutor")
    @Override
    public void setGlobalWatermarkSettingSensor(String tenantOid, String globalWatermarksettingJson) {

        UserAccountDetail accountDetail = userCenterService.getFatUserAccountDetailByOid(tenantOid);
        if(StringUtils.isBlank(globalWatermarksettingJson)){
            globalWatermarksettingJson = ProcessPreferenceEnum.PROCESS_WATERMARK_SET.getDefaultValue();
        }
        GlobalWatermarkPreference globalWatermarkPreference = JsonUtils.json2pojo(globalWatermarksettingJson, GlobalWatermarkPreference.class);
        Map<String, Object> data = new HashMap<>();
        data.put("entityType", "企业");
        data.put("entityName", accountDetail.getAccountName());
        data.put("authorizedOid", accountDetail.getAccountOid());
        data.put("authorizedGid", accountDetail.getAccountGid());
        String sensorValue = WatermarkPreferenceTypeEnum.getSensorKeyByType(globalWatermarkPreference.getType());
        data.put("watermark_type", sensorValue);
        sensorsCollect(tenantOid, SensorEventEnum.SAVE_GLOBAL_WATERMARK_SETTING.getEvent(), data);
    }

    @Async("sensorExecutor")
    @Override
    public void preferenceAutoTransferSensor(String tenantOid, String value, String event) {

        UserAccountDetail accountDetail = userCenterService.getFatUserAccountDetailByOid(tenantOid);
        
        boolean isOpen = Boolean.TRUE.toString().equals(value);
        
        AccountVipQueryOutput vipQueryOutput = saasCommonClient.queryAccountVip(tenantOid, null);
        
        Map<String, Object> data = new HashMap<>();
        data.put(SensorConstants.ORG_NAME, accountDetail.getAccountName());
        data.put(SensorConstants.AUTHORIZED_OID, accountDetail.getAccountOid());
        data.put(SensorConstants.VIP_VERSION, vipQueryOutput.getVipCode());
        data.put(SensorConstants.CONFIGURATION_ITEMS, isOpen);
        sensorsCollect(tenantOid, event, data);
    }

    /**
     * 创建合同编号埋点
     *
     * @param tenantId
     * @param processId
     * @param file
     */
    private void createRuleTracking(String tenantId, String processId, FileBO file) {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("fileId", SensorConstants.sensorString(file.getFileId()));
            data.put("processId", SensorConstants.sensorString(processId));
            data.put("contractNo", SensorConstants.sensorString(file.getContractNo()));
            data.put("contractNoType", SensorConstants.sensorString(file.getContractNoType()));
            data.put("contractNoRule", SensorConstants.sensorString(file.getContractNoRule()));
            sensorsCollect(tenantId, SensorEventEnum.CREATE_CONTRACT_NO_SEVER.getEvent(), data);
        } catch (Exception e) {
            log.info("createRule data tracking failure", e);
        }
    }

    /**
     * 判断签署附件中是否含有需校验的附件
     *
     * @param attachmentConfigs 签署附件列表
     * @return true：含有，false：不含有
     */
    private boolean hasAttachmentCheck(List<AttachmentConfigBO> attachmentConfigs) {
        if (CollectionUtils.isEmpty(attachmentConfigs)) {
            return false;
        }
        for (AttachmentConfigBO config : attachmentConfigs) {
            if (config.getCheckInfo()) {
                return true;
            }
        }
        return false;
    }
}

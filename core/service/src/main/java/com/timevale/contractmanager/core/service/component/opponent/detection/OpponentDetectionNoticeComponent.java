package com.timevale.contractmanager.core.service.component.opponent.detection;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionTaskTypeEnum;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.contratc.notice.NoticeComponent;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.MessageNotifyUtil;
import com.timevale.notificationmanager.service.enums.NotifyChannelTypeEnum;
import com.timevale.notificationmanager.service.model.IDUser;
import com.timevale.notificationmanager.service.model.NotifyContactWay;
import com.timevale.notificationmanager.service.model.SendMessageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author:jianyang
 * @since 2021-08-23 11:09
 */
@Component
@Slf4j
public class OpponentDetectionNoticeComponent {

	private static final String OPPONENT_DETECTION_COMPLETE = "opponent_detection_complete";
	private static final String OPPONENT_DETECTION_SINGLE = "opponent_detection_single";
	private static final String OPPONENT_DETECTION_YESTERDAY = "opponent_detection_yesterday";
	private static final String URL = "/opponent/detection/batch/history/detail/%s";
	private static final String SINGLEURL = "/opponent/detection/add";
	public static final String PATH_URL="/startup?path=%s&type=organ&ouid=%s";

	@Autowired MessageNotifyUtil messageNotifyUtil;
	@Autowired private UserCenterService userCenterService;
	@Autowired private NoticeComponent noticeComponent;

	@Value("${dns}")
	private String dns;

	/**
	 * 批量检测完成通知或每日定时推送
	 * @param tenantGid
	 * @param detectionTtotality
	 * @param ventureBusinessNum
	 * @param reportProblemNum
	 * @param noticeOids
	 * @param taskId
	 * @param taskType
	 */
	public void batchDetectionNotice(String tenantGid, Integer detectionTtotality,
					   Integer ventureBusinessNum, long reportProblemNum,
					   List<String> noticeOids, String taskId, Integer taskType ){
		noticeOids.forEach(x ->{
			if(taskType.equals(OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType())){
				log.info("相对方检测批量任务完成开始发送通知 taskId{} noticeOid:{}", taskId, x);
			}
			UserAccount userAccountTenant = userCenterService.getUserAccountDetailByGid(tenantGid);
			boolean res = userCenterService.checkMemberOrCreator(userAccountTenant.getAccountOid(), x);
			if(res){
				SendMessageRequest sendMessageRequest = new SendMessageRequest();
				// 指定通知参数
				Map<String, String> templateParam = Maps.newHashMap();
				templateParam.put("detectionTotality",detectionTtotality.toString());
				templateParam.put("ventureBusinessNum",ventureBusinessNum.toString());
				templateParam.put("reportProblemNum",String.valueOf(reportProblemNum));
				if(taskType.equals(OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType())){
					sendMessageRequest.setTemplateName(OPPONENT_DETECTION_COMPLETE);
					templateParam.put("url", genUrl(URL, taskId, userAccountTenant.getAccountOid()));
					templateParam.put("dnsUrl",dns + genUrl(URL, taskId, userAccountTenant.getAccountOid()));
				}else {
					sendMessageRequest.setTemplateName(OPPONENT_DETECTION_YESTERDAY);
					templateParam.put("url", genUrl(SINGLEURL, null, userAccountTenant.getAccountOid()));
					templateParam.put("dnsUrl",dns + genUrl(SINGLEURL, null ,userAccountTenant.getAccountOid()));
				}
				sendMessageRequest.setTemplateParam(templateParam);
				notice(sendMessageRequest, x, userAccountTenant);
			}else {
				log.info("相对方检测批量任务完成通知员工已离职 taskId{} noticeOid:{}", taskId, x);
			}
		});
	}

	/**
	 * 新增检测通知
	 * @param tenantGid
	 * @param orgName
	 * @param noticeOids
	 * @param chainResults
	 */
	public void singleDetectionNotice(String tenantGid, String orgName, List<String> noticeOids,
									  List<OpponentDetectionChainResultBO> chainResults){
		for (String noticeOid : noticeOids){
			log.info("相对方检测新增检测立即推送开始发送通知:{}", JSON.toJSONString(noticeOid));
			UserAccount userAccountTenant = userCenterService.getUserAccountDetailByGid(tenantGid);

			SendMessageRequest sendMessageRequest = new SendMessageRequest();
			// 指定通知参数
			Map<String, String> templateParam = Maps.newHashMap();
			templateParam.put("orgName",orgName);
			String problemList = chainResults.stream().map(OpponentDetectionChainResultBO::getProblemDesc).collect(Collectors.joining(";"));

			templateParam.put("url", genUrl(SINGLEURL, null ,userAccountTenant.getAccountOid()));
			templateParam.put("problemList",problemList);
			templateParam.put("dnsUrl",dns + genUrl(SINGLEURL, null ,userAccountTenant.getAccountOid()));
			sendMessageRequest.setTemplateParam(templateParam);
			sendMessageRequest.setTemplateName(OPPONENT_DETECTION_SINGLE);
			notice(sendMessageRequest, noticeOid, userAccountTenant);
			log.info("相对方检测新增检测立即推送开始发送通知完成:{}", JSON.toJSONString(noticeOid));
		}
	}

	public void notice(SendMessageRequest sendMessageRequest, String noticeOid, UserAccount userAccountTenant){
		sendMessageRequest.setAppId("0");
		// 指定通知用户账号
		IDUser idUser = new IDUser();
		idUser.setUserOid(noticeOid);
		sendMessageRequest.setId(idUser);
		// 通知方式
		List<NotifyChannelTypeEnum> noticeTypes = Lists.newArrayList(NotifyChannelTypeEnum.INMAIL);
		// 发送通知需过滤个人的通知开关
		List<NotifyChannelTypeEnum> userTypes = noticeComponent.getUserOpenNotifyChannel(userAccountTenant.getAccountOid());
		if(userTypes.contains(NotifyChannelTypeEnum.EMAIL)){
			noticeTypes.add(NotifyChannelTypeEnum.EMAIL);
		}

		List<NotifyContactWay> contactWays = noticeTypes.stream().map(t -> {
			NotifyContactWay contactWay = new NotifyContactWay();
			contactWay.setType(t);
			return contactWay;
		}).collect(Collectors.toList());
		sendMessageRequest.setAppointContactWays(contactWays);
		messageNotifyUtil.sendNotification(sendMessageRequest);
		log.info("相对方检测通知完成:{}", JSON.toJSONString(noticeOid));

	}

	public String genUrl(String url , String uuid, String oid){
		String encodePath = url;
		if(uuid != null){
			encodePath = String.format(url, uuid,oid);
		}
		try {
			encodePath = URLEncoder.encode(encodePath, "utf-8");
		} catch (UnsupportedEncodingException e) {
			log.error("encoding error:", e);
		}
		return String.format(PATH_URL, encodePath, oid);
	}
}

package com.timevale.contractmanager.core.service.contractprocess.processor.sign;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.HbaseProcessDataAsyncCollectException;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.SignChangeTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.SignChangeMsgEntity;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Created by tianlei on 2022/5/11
 */
@Slf4j
@Component
public class AppendSignDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient processWriteClient;
    @Autowired
    private ContractProcessReadClient processQueryClient;
    @Autowired
    private LockService lockService;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.signTopicName(), SignChangeTagEnum.APPEND_SIGN.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        SignChangeMsgEntity entity =
                JsonUtils.json2pojo(data, SignChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        Lock lock = lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                doProcess(collectContext);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }


    private void doProcess(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();

        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);

        List<ContractProcessSignTaskDTO> oldSignTaskDTOList =
                Optional.ofNullable(contractProcessDTO.getSignTasks()).orElse(new ArrayList<>());

        SignChangeMsgEntity signChangeMsgEntity = (SignChangeMsgEntity) collectContext.getData();

        // 为什么不判断 空直接返回，因为 可能原来没有签署区 然后追加了
        if (CollectionUtils.isNotEmpty(oldSignTaskDTOList)) {
            // 看下是否是构造的假数据
            boolean falseData = StringUtils.isBlank(oldSignTaskDTOList.get(0).getTaskId());
            if (falseData) {
                // 说明是提前构造的假签署任务，消息先与 signStart 到了，丢弃等signStart来到在进行构造
                log.info(LOG_PREFIX + "signTaskChange before signStart processId : {}", processId);
                return;
            }
        }

        ContractProcessSignTaskDTO newSignTaskDTO = processDataBuilder.buildUpdateSignTask(signChangeMsgEntity);
        if (null == newSignTaskDTO) {
            log.info("signTasks build is null");
            return;
        }


        boolean findExistData = false;
        for (int i = 0; i < oldSignTaskDTOList.size(); i++) {
            if (Objects.equals(oldSignTaskDTOList.get(i).getTaskId(), newSignTaskDTO.getTaskId())) {
                findExistData = true;
                break;
            }
        }

        if (findExistData) {
            // 这条数据已经存在
            log.info("appendSign data has exist");
            return;
        }

        // 追加数据
        oldSignTaskDTOList.add(newSignTaskDTO);

        // 数据更新
        ContractProcessUpdateParam input = new ContractProcessUpdateParam();
        input.setProcessId(processId);
        input.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_APPEND_SIGN);
        input.setSignTasks(ProcessDataCollectConverter.signTaskDTO2ParamList(oldSignTaskDTOList));
        processWriteClient.updateByProcessId(input);
    }

}

package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.PLATFORM_TYPE;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.SCAN_CODE_NUMBER_MAXIMUM;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.SCAN_INITIATE_SUBJECT;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * <AUTHOR>
 * @since 2021-05-31
 */
@Data
public class ShareSignTaskStartSensorBean extends ToString {

    /** 发起端 */
    private String platformType;

    /** 发起主体名称 */
    private String initiatorSubjectName;

    /** 扫码任务上限 */
    private Integer shareTaskTotal;

    public Map<String, Object> sensorData() {
        Map<String, Object> sensorData = Maps.newHashMap();
        sensorData.put(PLATFORM_TYPE,  sensorString(platformType));
        sensorData.put(SCAN_INITIATE_SUBJECT, sensorString(initiatorSubjectName));
        sensorData.put(SCAN_CODE_NUMBER_MAXIMUM, shareTaskTotal);
        return sensorData;
    }
}

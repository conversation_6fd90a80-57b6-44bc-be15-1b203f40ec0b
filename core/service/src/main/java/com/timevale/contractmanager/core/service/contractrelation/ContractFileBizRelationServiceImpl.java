package com.timevale.contractmanager.core.service.contractrelation;

import com.timevale.contractmanager.common.dal.bean.ContractFileBizRelationDO;
import com.timevale.contractmanager.common.dal.dao.ContractFileBizRelationDAO;
import com.timevale.contractmanager.common.dal.query.ContractBizRelationQuery;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.contractmanager.core.service.other.DocManagerService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.api.ContractNoRpcService;
import com.timevale.saas.common.manage.common.service.enums.contractNo.ContractNoGenerateTypeEnum;
import com.timevale.saas.common.manage.common.service.model.input.contractNo.ContractNoGenerateParam;
import com.timevale.saas.common.manage.common.service.model.input.contractNo.ContractNoGenerateRuleParam;
import com.timevale.saas.common.manage.common.service.model.output.contractNo.ContractNoGenerateResultDTO;
import com.timevale.saas.common.manage.common.service.util.ContractNoUtils;
import lombok.Data;
import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.timevale.saas.common.manage.common.service.enums.contractNo.ContractNoGenerateTypeEnum.*;

/**
 * <AUTHOR>
 * @since 2022-10-27 10:38
 */
@Service
public class ContractFileBizRelationServiceImpl implements ContractFileBizRelationService {

    @Autowired private ContractFileBizRelationDAO contractFileBizRelationDAO;

    @Autowired private ContractNoRpcService contractNoRpcService;

    @Autowired private DocManagerService docManagerService;

    @Autowired MapperFactory mapperFactory;

    @Override
    public void saveBatch(List<ContractFileBizRelationDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        supplyFileName(list);

        contractFileBizRelationDAO.insertBatch(list);
    }

    @Override
    @Transactional
    public void updateBatch(String processId, List<ContractFileBizRelationDO> newList) {
        // 失效原有的合同编号
        invalidByProcessId(processId);
        // 保存新的合同编号
        saveBatch(newList);
    }

    @Override
    public Map<String, String> genContractNo(List<FileBO> files) {
        GenerateNoGroup genRuleGroup = groupByContractRule(files);
        List<String> systemNoFiles = genRuleGroup.getGenSystemNoFiles();
        Map<String, Set<String>> customNoFiles = genRuleGroup.getGenCustomNoFiles();

        // Map<fileId, contractNo>
        Map<String, String> res = new HashMap<>();

        // 系统生成
        if (CollectionUtils.isNotEmpty(systemNoFiles)) {
            systemNoFiles.stream().forEach(x -> res.put(x, ContractNoUtils.generateSystemNo()));
        }

        // 自定义规则生成
        if (MapUtils.isNotEmpty(customNoFiles)) {
            ContractNoGenerateResultDTO result =
                    contractNoRpcService.getContractNo(buildGenNoParams(customNoFiles));
            //
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getContractNoList())) {
                result.getContractNoList().stream()
                        .map(contractNoFile -> contractNoFile.getFileList())
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(Collection::stream)
                        .filter(Objects::nonNull)
                        .forEach(x -> res.put(x.getFileId(), x.getNumber()));
            }
        }

        return res;
    }

    @Override
    public List<ContractFileBizRelationDO> listByProcessId(String processId) {
        return listByProcessIdAndStatus(processId, DeletedEnum.NO.code());
    }

    @Override
    public List<ContractFileBizRelationDO> listByProcessIdAndStatus(String processId, Integer status) {
        if (StringUtils.isBlank(processId)) {
            return Collections.emptyList();
        }

        ContractBizRelationQuery condition = new ContractBizRelationQuery();
        condition.setProcessId(processId);
        condition.setDeleted(status);
        return contractFileBizRelationDAO.selectByCondition(condition);
    }

    @Override
    public List<ContractFileBizRelationDO> listByFileIdsAndStatus(String processId, List<String> fileIds, Integer deleted) {
        if (StringUtils.isBlank(processId) || CollectionUtils.isEmpty(fileIds)) {
            return Collections.emptyList();
        }

        ContractBizRelationQuery condition = new ContractBizRelationQuery();
        condition.setProcessId(processId);
        condition.setFileIds(fileIds);
        condition.setDeleted(deleted);
        return contractFileBizRelationDAO.selectByCondition(condition);
    }

    @Override
    public void invalidByProcessId(String processId) {
        if (StringUtils.isBlank(processId)) {
            return;
        }

        ContractFileBizRelationDO update = new ContractFileBizRelationDO();
        update.setProcessId(processId);
        update.setDeleted(DeletedEnum.YES.code());
        contractFileBizRelationDAO.updateByProcessId(update);
    }

    /**
     * 校验规则参数是否合法
     *
     * @param type
     * @param ruleId
     * @return
     */
    @Override
    public boolean validateRule(Integer type, String ruleId) {
        List<Integer> supportType =
                Arrays.stream(ContractNoGenerateTypeEnum.values())
                        .map(ContractNoGenerateTypeEnum::getType)
                        .collect(Collectors.toList());

        // 传了错误类型
        if (type == null || !supportType.contains(type)) {
            return false;
        }

        // 设置了自定义，又没传自定义规则
        if (CUSTOM.getType().equals(type) && StringUtils.isBlank(ruleId)) {
            return false;
        }

        return true;
    }

    /**
     * 修正合同编号类型
     *
     * @param file
     * @return
     */
    @Override
    public void amendContractNoType(FileBO file) {
        // 类型正确则直接返回
        if (validateRule(file.getContractNoType(), file.getContractNoRule())) {
            return;
        }

        // 根据实际传参修正type
        if (StringUtils.isNotBlank(file.getContractNo())) {
            file.setContractNoType(INPUT.getType());
        } else if (StringUtils.isNotBlank(file.getContractNoRule())) {
            file.setContractNoType(CUSTOM.getType());
        } else {
            file.setContractNoType(SYSTEM.getType());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shiftContractFileBizRelations(String processId, Map<String, String> toSignFileMap) {
            // 获取合同文件关联信息
            List<String> templateIds = new ArrayList<>(toSignFileMap.keySet());
        List<ContractFileBizRelationDO> relations = listByFileIdsAndStatus(processId, templateIds, DeletedEnum.NO.code());
            if (CollectionUtils.isEmpty(relations)) {
                return;
            }

            // 失效合同文件的关联信息
            invalidByProcessId(processId);

            // 创建签署文件的关联信息
            MapperFacade mapperFacade = mapperFactory.getMapperFacade();
            List<ContractFileBizRelationDO> signFileRelations =
                    relations.stream()
                            .map(x -> mapperFacade.map(x, ContractFileBizRelationDO.class))
                            .peek(r -> r.setProcessId(processId))
                            .peek(r -> r.setFileId(toSignFileMap.get(r.getFileId())))
                            .filter(relation -> StringUtils.isNotBlank(relation.getFileId()))
                            .collect(Collectors.toList());
            saveBatch(signFileRelations);
    }

    private void supplyFileName(List<ContractFileBizRelationDO> relations) {
        if (relations.stream()
                .map(ContractFileBizRelationDO::getFileName)
                .allMatch(StringUtils::isNotBlank)) {
            return;
        }

        List<String> fileIds =
                relations.stream()
                        .map(ContractFileBizRelationDO::getFileId)
                        .collect(Collectors.toList());
        Map<String, String> fileNameMap = docManagerService.getFileNameMapByIds(fileIds);

        for (ContractFileBizRelationDO relation : relations) {
            if (StringUtils.isNotBlank(relation.getFileName())
                    || StringUtils.isBlank(fileNameMap.get(relation.getFileId()))) {
                continue;
            }
            relation.setFileName(fileNameMap.get(relation.getFileId()));
        }
    }

    /**
     * 按生成编号的规则对文件分组
     *
     * @param files
     * @return
     */
    private GenerateNoGroup groupByContractRule(List<FileBO> files) {
        GenerateNoGroup group = new GenerateNoGroup();
        // 按文件的order顺序进行排序
        if (files.stream().map(FileBO::getOrder).anyMatch(Objects::nonNull)) {
            files =
                    files.stream()
                            .sorted(Comparator.comparing(FileBO::getOrder))
                            .collect(Collectors.toList());
        }

        for (FileBO file : files) {
            if (StringUtils.isNotBlank(file.getContractNo())) {
                // 用户填了编号，不用生成
            } else if (StringUtils.isNotBlank(file.getContractNoRule())) {
                group.addCustomNoFile(file.getContractNoRule(), file.getFileId());
            } else {
                group.addSystemNoFile(file.getFileId());
            }
        }

        return group;
    }

    /**
     * 构建生成编号的参数列表
     *
     * @param customNoFiles 每个规则需要生成编号的文件
     * @return
     */
    private ContractNoGenerateParam buildGenNoParams(Map<String, Set<String>> customNoFiles) {

        List<ContractNoGenerateRuleParam> ruleList =
                customNoFiles.entrySet().stream()
                        .map(
                                entry -> {
                                    ContractNoGenerateRuleParam genRuleParam =
                                            new ContractNoGenerateRuleParam();
                                    genRuleParam.setRuleId(entry.getKey());
                                    genRuleParam.setFileIdList(new ArrayList<>(entry.getValue()));
                                    return genRuleParam;
                                })
                        .collect(Collectors.toList());

        ContractNoGenerateParam param = new ContractNoGenerateParam();
        param.setRuleList(ruleList);

        return param;
    }

    @Data
    private static class GenerateNoGroup {
        /** List<fileId> */
        List<String> genSystemNoFiles = new ArrayList<>();

        /** Map<ruleId, Set<fileId>> */
        Map<String, Set<String>> genCustomNoFiles = new HashMap<>();

        public void addSystemNoFile(String fileId) {
            genSystemNoFiles.add(fileId);
        }

        public void addCustomNoFile(String ruleId, String fileId) {
            genCustomNoFiles.put(ruleId, genCustomNoFiles.getOrDefault(ruleId, new HashSet<>()));
            genCustomNoFiles.get(ruleId).add(fileId);
        }
    }
}

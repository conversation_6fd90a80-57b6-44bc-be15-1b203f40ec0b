package com.timevale.contractmanager.core.service.opponent;

import com.timevale.contractmanager.core.model.dto.response.opponent.detection.OpponentEnterpriseInfoResponse;

import java.util.Optional;

/**
 * @author: huifeng
 * @since: 2021-12-28 16:18
 **/
public interface OrganizationQueryService {
    /**
     * 根据企业名查询基本信息
     * @param orgName
     * @return 如果不存在
     */
    Optional<OpponentEnterpriseInfoResponse> getSimpleInfoByOrgName(String orgName);

    /**
     * 根据统一社会信用代码查询企业信息
     * @param socialCreditCode
     * @return
     */
    Optional<OpponentEnterpriseInfoResponse> getSimpleInfoByCreditCode(String socialCreditCode);
}

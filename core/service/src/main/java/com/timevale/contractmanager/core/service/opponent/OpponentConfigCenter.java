package com.timevale.contractmanager.core.service.opponent;

import com.timevale.framework.puppeteer.ConfigService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Created by t<PERSON><PERSON><PERSON> on 2022/8/8
 */
@Component
public class OpponentConfigCenter {

    @Value("${opponent.addOrganizeNameMaxLength:100}")
    public Integer organizeNameMaxLength;

    @Value("${opponent.addOrganizeNameMinLength:2}")
    public Integer organizeNameMinLength;

    public String logPrefix() {
        return "opponent ";
    }

    public boolean syncGidSwitch() {
        return ConfigService.getAppConfig().getBooleanProperty("opponentSyncGidSwitch", true);
    }

    /**
     * 最大值M
     */
    public int importExcelMaxSizeM() {
        return ConfigService.getAppConfig().getIntProperty("opponent.importExcelMaxSize", 1);
    }

}

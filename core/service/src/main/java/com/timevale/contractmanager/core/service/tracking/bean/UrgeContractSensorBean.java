package com.timevale.contractmanager.core.service.tracking.bean;

import com.timevale.contractmanager.core.model.dto.request.BaseBatchRequest;
import com.timevale.contractmanager.core.model.enums.SensorEnum;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * 催办埋点模版
 * <AUTHOR>
 *
 * @date 2022/2/21
 */
@Data
public class UrgeContractSensorBean extends SensorBaseBean {

    private BaseBatchRequest request;

    @Override
    public SensorEventEnum sensorKey() {
        return SensorEventEnum.URGE_CONTRACT_SEVER;
    }

    @Override
    public List<SensorEnum> sensorTemplate() {
        return Arrays.asList(SensorEnum.NUMBER_OF_PROCESS, SensorEnum.RETURN_TIME);
    }

    @Override
    public void initData() {
        if (request != null) {
            setAuthorizedOid(request.getSubjectId());
            setOperatorOid(request.getAccountId());
            getTempData()
                    .put(SensorEnum.NUMBER_OF_PROCESS.getKey(), request.getProcessIds().size());
        }
    }

    @Override
    public void setRequest(Object request) {
        this.request = (BaseBatchRequest) request;
    }

    public void setRequest(BaseBatchRequest request) {
        this.request = request;
    }
}

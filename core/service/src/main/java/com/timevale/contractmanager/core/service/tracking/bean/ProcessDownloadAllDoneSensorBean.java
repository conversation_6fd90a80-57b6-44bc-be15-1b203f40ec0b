package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @author: duhui
 * @since: 2021/9/27 10:29 上午
 **/
@Data
public class ProcessDownloadAllDoneSensorBean extends ToString {
    /**
     * 企业主体oid
     */
    private String tenantId;
    /**
     * 操作人oid
     */
    private String oid;

    public Map<String, Object> sensorData() {
        Map<String, Object> sensorData = Maps.newHashMap();
        sensorData.put(OID,sensorString(oid));
        sensorData.put(TENANT_ID,sensorString(tenantId));
        return sensorData;
    }
}

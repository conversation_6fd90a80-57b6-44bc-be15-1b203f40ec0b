package com.timevale.contractmanager.core.service.watermark.impl;

import com.timevale.contractmanager.core.service.watermark.WatermarkStrategy;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkContentTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkTypeEnum;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: contract-manager-project
 * @Description: 文件水印--图片类型处理器
 * @date Date : 2023年05月13日 14:02
 */
@Service
public class DocImageWatermarkStrategy extends DocCommonWatermarkStrategy {
    @Override
    public String getType() {
        return WatermarkStrategy.assembleServiceId(WatermarkTypeEnum.DOC_WATERMARK, WatermarkContentTypeEnum.IMAGE);
    }
}

package com.timevale.contractmanager.core.service.offlinecontract.impl;

import static com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractExtractWayEnum.MANUAL_INPUT;
import static com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum.*;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;
import static com.timevale.contractmanager.common.utils.config.SystemConfig.RECORD_REMAIN_DAYS;
import static com.timevale.contractmanager.core.service.mq.consumer.clmcapability.ClmJobResultTagConstants.OFFLINE_EXTRACT_PROCESS_TAG;
import static com.timevale.contractmanager.core.service.offlinecontract.enums.ExtractConfigFieldEnum.*;
import static com.timevale.contractmanager.core.service.offlinecontract.handler.OfflineContractStatusHandler.calculateStatusByImportProcessDOs;
import static com.timevale.contractmanager.core.service.transaction.OfflineContractOperateEvent.recordEvent;
import static com.timevale.contractmanager.core.service.transaction.OfflineContractOperateEvent.recordProcessEvent;
import static com.timevale.contractmanager.core.service.transaction.OfflineContractOperateType.*;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.clmc.facade.api.enums.ContractFileTypeEnum;
import com.timevale.clmc.facade.api.enums.JobFailEnum;
import com.timevale.clmc.facade.api.model.bean.ExtractField;
import com.timevale.clmc.facade.api.model.input.RpcAsyncExtractContractV2Input;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.GetContractCategoryDetailResultDTO;
import com.timevale.contractanalysis.facade.api.enums.ContractCategoryStatusEnum;
import com.timevale.contractmanager.common.dal.bean.OfflineContractImportFilesDO;
import com.timevale.contractmanager.common.dal.bean.OfflineContractImportProcessDO;
import com.timevale.contractmanager.common.dal.bean.OfflineContractImportRecordDO;
import com.timevale.contractmanager.common.dal.bean.grouping.MenuDO;
import com.timevale.contractmanager.common.dal.dao.OfflineContractImportFilesDAO;
import com.timevale.contractmanager.common.dal.dao.OfflineContractImportProcessDAO;
import com.timevale.contractmanager.common.dal.dao.OfflineContractImportRecordDAO;
import com.timevale.contractmanager.common.service.bean.offlinecontract.*;
import com.timevale.contractmanager.common.service.constant.SystemConstant;
import com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractFileModeEnum;
import com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractStatusEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.common.service.integration.util.FileUtil;
import com.timevale.contractmanager.common.utils.DateUtil;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.bo.ClmCExtendInfo;
import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.contractmanager.core.service.offlinecontract.OfflineContractService;
import com.timevale.contractmanager.core.service.offlinecontract.bean.input.*;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordContractsOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordInfoOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.bean.output.OfflineContractRecordsOutputDTO;
import com.timevale.contractmanager.core.service.offlinecontract.enums.ExtractConfigFieldEnum;
import com.timevale.contractmanager.core.service.offlinecontract.enums.FailCodeEnum;
import com.timevale.contractmanager.core.service.other.ClmCapabilityService;
import com.timevale.contractmanager.core.service.other.DocManagerService;
import com.timevale.contractmanager.core.service.transaction.OfflineContractOperateType;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.base.util.ExceptionLogUtil;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 线下合同业务层实现
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Slf4j
@Service
public class OfflineContractServiceImpl implements OfflineContractService {

    private static final String AI_BIZ_SOURCE = "offline-contract";
    private static final String AI_BIZ_SOURCE_DESC = "纸质合同";
    private static final String BILLING_DATA_FUNCTION_TYPE = "智能抽取";

    @Autowired OfflineContractImportRecordDAO offlineContractImportRecordDAO;
    @Autowired OfflineContractImportFilesDAO offlineContractImportFilesDAO;
    @Autowired OfflineContractImportProcessDAO offlineContractImportProcessDAO;
    @Autowired ApplicationEventPublisher publisher;
    @Autowired MenuService menuService;
    @Autowired DocManagerService docManagerService;
    @Autowired ClmCapabilityService clmCapabilityService;
    @Autowired ContractAnalysisClient contractAnalysisClient;

    @Override
    @Transactional
    public String saveOfflineContractRecord(SaveOfflineContractRecordDTO input) {
        // 校验菜单及文件拥有者
        checkMenuAndFileOwner(input);
        // 生成导入记录id
        String recordId = UUIDUtil.genUUID();
        // 判断是否手动录入合同信息方式
        boolean isManualInput = MANUAL_INPUT.getWay().equals(input.getExtractWay());
        // 组装线下合同导入记录
        OfflineContractImportRecordDO recordDO = new OfflineContractImportRecordDO();
        recordDO.setMenuId(input.getMenuId());
        recordDO.setRecordId(recordId);
        recordDO.setImportWay(input.getImportWay());
        recordDO.setExtractWay(input.getExtractWay());
        recordDO.setSubjectGid(input.getSubjectGid());
        recordDO.setSubjectOid(input.getSubjectOid());
        recordDO.setSubjectName(input.getSubjectName());
        recordDO.setImporterGid(input.getAccountGid());
        recordDO.setImporterOid(input.getAccountOid());
        recordDO.setImporterName(input.getAccountName());
        recordDO.setImportClient(input.getClientId());
        recordDO.setContractSize(input.getContracts().size());
        recordDO.setStatus(IMPORTING.getStatus());
        // 如果非手动录入方式， 即AI提取方式
        if (!isManualInput) {
            // 判断提取配置是否为空， 如果为空，则报错
            if (null == input.getExtractConfig()) {
                throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "缺少合同信息提取配置");
            }
            recordDO.setExtractConfig(JSONObject.toJSONString(input.getExtractConfig()));
        }
        offlineContractImportRecordDAO.insert(recordDO);

        List<OfflineContractImportFilesDO> offlineFileDOs = Lists.newArrayList();
        List<OfflineContractImportProcessDO> processInfoDOs = Lists.newArrayList();
        List<String> recordProcessIds = Lists.newArrayList();
        for (OfflineContract contract : input.getContracts()) {
            String recordProcessId = UUIDUtil.genUUID();
            // 组装并追加文件列表信息
            List<OfflineContractFile> contractFiles = contract.getContractFiles();
            offlineFileDOs.addAll(
                    buildOfflineContractImportFilesDOs(recordId, recordProcessId, contractFiles));
            OfflineContractProcessInfo processInfo = contract.getProcessInfo();
            if (null == processInfo) {
                processInfo = new OfflineContractProcessInfo();
                processInfo.setTitle(FileUtil.getPureFileName(contractFiles.get(0).getFileName()));
            }
            processInfo.setMasterProcessId(input.getMasterProcessId());
            processInfo.setDedicatedCloudId(input.getDedicatedCloudId());
            OfflineContractImportProcessDO processInfoDO = new OfflineContractImportProcessDO();
            processInfoDO.setRecordId(recordId);
            processInfoDO.setRecordProcessId(recordProcessId);
            processInfoDO.setProcessInfo(JSONObject.toJSONString(processInfo));
            processInfoDO.setStatus(IMPORTING.getStatus());
            processInfoDOs.add(processInfoDO);
            recordProcessIds.add(recordProcessId);
        }
        if (CollectionUtils.isNotEmpty(offlineFileDOs)) {
            offlineContractImportFilesDAO.batchInsert(offlineFileDOs);
        }
        if (CollectionUtils.isNotEmpty(processInfoDOs)) {
            offlineContractImportProcessDAO.batchInsert(processInfoDOs);
        }
        // 获取后续操作， 生成流程 或 提取合同信息， 默认生成合同信息
        OfflineContractOperateType operateType = GENERATE_PROCESS;
        // 非手动录入，且提取配置中提取字段不为空， 则提取合同信息
        if (!isManualInput
                && input.getExtractConfig().getFields().stream().anyMatch(i -> i.isEnable())) {
            operateType = EXTRACT_PROCESS_INFO;
        }
        publisher.publishEvent(recordProcessEvent(recordProcessIds, operateType,input.getMasterProcessId()));

        return recordId;
    }

    /**
     * 校验菜单及文件拥有者
     * @param input
     */
    private void checkMenuAndFileOwner(SaveOfflineContractRecordDTO input) {
        // 查询菜单信息
        MenuDO menuDO = menuService.getMenuByMenuId(input.getMenuId());
        // 如果菜单为空 或者 菜单拥有者不匹配， 报错
        if (null == menuDO || !StringUtils.equals(menuDO.getGid(), input.getSubjectGid())) {
            throw new BizContractManagerException(MENUID_NOT_CONTAIN);
        }
        // 获取所有的文件id列表
        List<String> fileIds = Lists.newArrayList();
        input.getContracts().forEach(
                i -> fileIds.addAll(i.getContractFiles().stream().map(f -> f.getFileId()).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(fileIds)) {
            return;
        }
        UserAccount subject = new UserAccount();
        subject.setAccountOid(input.getSubjectOid());
        subject.setAccountGid(input.getSubjectGid());
        // 校验文件拥有者并返回
        List<FileBO> fileBOS = docManagerService.checkFileAndGet(fileIds, false, subject);
        // 如果文件列表为空， 报错文件不存在
        if (CollectionUtils.isEmpty(fileBOS)) {
            throw new BizContractManagerException(PROCESS_FILE_NOT_EXIST);
        }
    }

    /**
     * 组装线下合同导入文件BO对象列表
     *
     * @param recordId
     * @param recordProcessId
     * @param contractFiles
     * @return
     */
    private List<OfflineContractImportFilesDO> buildOfflineContractImportFilesDOs(
            String recordId, String recordProcessId, List<OfflineContractFile> contractFiles) {
        // 获取合同类型id列表
        Set<String> categoryIds =
                contractFiles.stream()
                        .filter(i -> StringUtils.isNotBlank(i.getCategoryId()))
                        .map(i -> i.getCategoryId())
                        .collect(Collectors.toSet());
        // 查询合同类型详情
        List<GetContractCategoryDetailResultDTO> categories =
                contractAnalysisClient.batchQueryContractCategories(
                        Lists.newArrayList(categoryIds));
        Map<String, GetContractCategoryDetailResultDTO> categoryNameMap =
                categories.stream()
                        .collect(Collectors.toMap(i -> i.getCategoryId(), i -> i));
        List<OfflineContractImportFilesDO> importFilesDOS = Lists.newArrayList();
        for (OfflineContractFile contractFile : contractFiles) {
            OfflineContractImportFilesDO filesDO = new OfflineContractImportFilesDO();
            filesDO.setRecordId(recordId);
            filesDO.setRecordProcessId(recordProcessId);
            filesDO.setOriginFileId(contractFile.getFileId());
            filesDO.setFileId(contractFile.getFileId());
            filesDO.setFileName(FileUtil.cutOffFileName(contractFile.getFileName(), 100));
            filesDO.setFileMode(OfflineContractFileModeEnum.PDF.getMode());
            filesDO.setFileType(contractFile.getFileType());
            filesDO.setContractNo(contractFile.getContractNo());
            if (StringUtils.isNotBlank(contractFile.getCategoryId())) {
                // 获取合同类型信息
                GetContractCategoryDetailResultDTO category =
                        categoryNameMap.get(contractFile.getCategoryId());
                // 校验合同类型有效性
                if (null == category) {
                    throw new BizContractManagerException(CONTRACT_CATEGORY_NOT_EXIST);
                }
                if (!ContractCategoryStatusEnum.isEnable(category.getCategoryStatus())) {
                    throw new BizContractManagerException(
                            CONTRACT_CATEGORY_NOT_ENABLE, category.getCategoryName());
                }
                filesDO.setContractCategoryId(contractFile.getCategoryId());
                filesDO.setContractCategoryName(contractFile.getCategoryName());
            }
            importFilesDOS.add(filesDO);
        }
        return importFilesDOS;
    }

    @Override
    @Transactional
    public void deleteOfflineContractRecords(DeleteOfflineContractRecordsDTO input) {
        List<OfflineContractImportRecordDO> records =
                offlineContractImportRecordDAO.getByRecordIds(input.getRecordIds());
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (OfflineContractImportRecordDO record : records) {
            if (!StringUtils.equals(record.getSubjectGid(), input.getSubjectGid())) {
                throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_OWNER);
            }
        }
        deleteOfflineContractRecords(input.getRecordIds());
    }

    @Override
    @Transactional
    public void deleteOfflineContractRecords(List<String> recordIds) {
        offlineContractImportRecordDAO.deleteByRecordIds(recordIds);
        offlineContractImportFilesDAO.deleteByRecordIds(recordIds);
        offlineContractImportProcessDAO.deleteByRecordIds(recordIds);
    }

    @Override
    @Transactional
    public void stopImportOfflineContract(StopImportOfflineContractDTO input) {
        List<OfflineContractImportRecordDO> recordDOs =
                offlineContractImportRecordDAO.getByRecordIds(input.getRecordIds());
        if (recordDOs.stream()
                .anyMatch(i -> !StringUtils.equals(i.getSubjectGid(), input.getSubjectGid()))) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_OWNER);
        }
        String originStatus = IMPORTING.getStatus();
        String newStatus = IMPORT_STOPPED.getStatus();
        // 更新导入记录状态
        offlineContractImportRecordDAO.updateStatusByRecordIds(
                input.getRecordIds(), Lists.newArrayList(originStatus), newStatus);
        // 更新导入流程状态
        offlineContractImportProcessDAO.updateStatusByRecordIds(
                input.getRecordIds(), originStatus, newStatus);
    }

    @Override
    @Transactional
    public void recoverImportOfflineContract(RecoverImportOfflineContractDTO input) {
        List<OfflineContractImportRecordDO> recordDOs =
                offlineContractImportRecordDAO.getByRecordIds(input.getRecordIds());
        // 校验导入记录拥有者
        if (recordDOs.stream()
                .anyMatch(i -> !StringUtils.equals(i.getSubjectGid(), input.getSubjectGid()))) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_OWNER);
        }
        // 基于recordId转为Map
        Map<String, OfflineContractImportRecordDO> recordDOMap =
                recordDOs.stream().collect(Collectors.toMap(i -> i.getRecordId(), i -> i));
        // 原始状态为停止导入
        String originStatus = IMPORT_STOPPED.getStatus();
        // 新状态为导入中
        String newStatus = IMPORTING.getStatus();
        // 查询到所有停止导入线下合同列表
        List<OfflineContractImportProcessDO> importProcessDOS =
                offlineContractImportProcessDAO.selectByRecordIds(
                        input.getRecordIds(), Lists.newArrayList(originStatus));
        if (CollectionUtils.isEmpty(importProcessDOS)) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_ALL_SUCCESS);
        }
        // 恢复导入记录状态
        offlineContractImportRecordDAO.updateStatusByRecordIds(
                input.getRecordIds(), Lists.newArrayList(originStatus), newStatus);
        // 恢复导入流程状态
        offlineContractImportProcessDAO.updateStatusByRecordIds(
                input.getRecordIds(), originStatus, newStatus);
        // 基于提取方式对导入流程进行分组
        Map<String, Set<String>> recordProcessIdMap =
                importProcessDOS.stream()
                        .collect(
                                Collectors.groupingBy(
                                        i -> recordDOMap.get(i.getRecordId()).getExtractWay(),
                                        Collectors.mapping(p -> p.getRecordProcessId(), Collectors.toSet())));
        // 针对不同的提取方式发送不同的消息
        recordProcessIdMap.forEach((k, v) -> {
            // 判断是否手动录入合同信息方式
            boolean isManualInput = MANUAL_INPUT.getWay().equals(k);
            OfflineContractOperateType operateType =
                    isManualInput ? GENERATE_PROCESS : EXTRACT_PROCESS_INFO;
            publisher.publishEvent(recordProcessEvent(v, operateType));
        });
    }

    @Override
    @Transactional
    public void restartImportFailedOfflineContract(RestartImportFailedOfflineContractDTO input) {
        OfflineContractImportRecordDO recordDO =
                offlineContractImportRecordDAO.getByRecordId(input.getRecordId());
        if (null == recordDO) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_EXIST);
        }
        // 查询导入失败的流程
        List<OfflineContractImportProcessDO> failedProcess =
                offlineContractImportProcessDAO.queryByRecordId(
                        input.getRecordId(), Lists.newArrayList(IMPORT_FAILED.getStatus()));
        if (CollectionUtils.isEmpty(failedProcess)) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NO_FAILED);
        }
        // 恢复导入记录状态, 导入失败或部分成功 -> 导入中
        offlineContractImportRecordDAO.updateStatusByRecordIds(
                Lists.newArrayList(input.getRecordId()),
                Lists.newArrayList(IMPORT_FAILED.getStatus(), PART_IMPORTED.getStatus()),
                IMPORTING.getStatus());
        // 清空导入失败数量
        offlineContractImportRecordDAO.clearFailed(input.getRecordId());
        // 恢复导入流程状态, 导入失败 -> 导入中
        offlineContractImportProcessDAO.updateStatusByRecordIds(
                Lists.newArrayList(input.getRecordId()),
                IMPORT_FAILED.getStatus(),
                IMPORTING.getStatus());

        Set<String> recordProcessIds =
                failedProcess.stream().map(i -> i.getRecordProcessId()).collect(Collectors.toSet());
        // 判断是否手动录入合同信息方式
        boolean isManualInput = MANUAL_INPUT.getWay().equals(recordDO.getExtractWay());
        OfflineContractOperateType operateType =
                isManualInput ? GENERATE_PROCESS : EXTRACT_PROCESS_INFO;
        publisher.publishEvent(recordProcessEvent(recordProcessIds, operateType));
    }

    @Override
    public OfflineContractRecordsOutputDTO queryOfflineContractRecords(
            QueryOfflineContractRecordsDTO input) {
        PageHelper.startPage(input.getPageNum(), input.getPageSize());
        // 获取记录保留天数
        Integer remainDays = ConfigService.getAppConfig().getIntProperty(RECORD_REMAIN_DAYS, 90);
        Date fromDate = DateUtils.addDays(new Date(), -remainDays);
        List<OfflineContractImportRecordDO> recordDOS;
        // 是否只展示当前操作人的数据
        if (input.isNeedFilterOperator()) {
            recordDOS = offlineContractImportRecordDAO.queryBySubjectGidAndImporterGid(input.getSubjectGid(),input.getAccountGid(), fromDate);
        }else {
            recordDOS = offlineContractImportRecordDAO.queryBySubjectGid(input.getSubjectGid(), fromDate);
        }
        PageInfo pageInfo = new PageInfo(recordDOS);
        OfflineContractRecordsOutputDTO outputDTO = new OfflineContractRecordsOutputDTO();
        outputDTO.setTotal(pageInfo.getTotal());
        outputDTO.setRecords(Lists.newArrayList());
        if (CollectionUtils.isEmpty(recordDOS)) {
            return outputDTO;
        }
        // 查询菜单信息
        Set<String> menuIds = recordDOS.stream().map(i -> i.getMenuId()).collect(Collectors.toSet());
        MenuInfoResult menuInfoResult = batchQueryMenuInfo(menuIds, input.isWithMenuPath());
        // 获取菜单id和菜单名称的映射关系
        Map<String, String> menuNameMap = menuInfoResult.getMenuNameMap();
        // 获取菜单id和菜单名称路径的映射关系
        Map<String, String> menuNamePathMap = menuInfoResult.getMenuNamePathMap();
        for (OfflineContractImportRecordDO recordDO : recordDOS) {
            OfflineContractRecordListBean listBean = new OfflineContractRecordListBean();
            listBean.setRecordId(recordDO.getRecordId());
            listBean.setMenuId(recordDO.getMenuId());
            listBean.setMenuName(menuNameMap.get(recordDO.getMenuId()));
            listBean.setMenuPath(menuNamePathMap.getOrDefault(recordDO.getMenuId(), ""));
            listBean.setImporter(recordDO.getImporterName());
            listBean.setImportTime(recordDO.getCreateTime().getTime());
            listBean.setImportWay(recordDO.getImportWay());
            listBean.setExtractWay(recordDO.getExtractWay());
            listBean.setContractSize(recordDO.getContractSize());
            listBean.setSuccessSize(recordDO.getSuccessSize());
            listBean.setFailedSize(recordDO.getFailedSize());
            listBean.setStatus(recordDO.getStatus());
            outputDTO.getRecords().add(listBean);
        }
        return outputDTO;
    }

    @Override
    public OfflineContractRecordInfoOutputDTO queryOfflineContractRecordInfo(
            QueryOfflineContractRecordInfoDTO input) {
        BatchQueryOfflineContractRecordInfoDTO batchInput = new BatchQueryOfflineContractRecordInfoDTO();
        batchInput.setSubjectGid(input.getSubjectGid());
        batchInput.setRecordIds(Lists.newArrayList(input.getRecordId()));
        batchInput.setWithMenuPath(input.isWithMenuPath());
        List<OfflineContractRecordInfoOutputDTO> records = batchQueryOfflineContractRecordInfo(batchInput);
        if (CollectionUtils.isEmpty(records)) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_EXIST);
        }
        return records.get(0);
    }

    @Override
    public List<OfflineContractRecordInfoOutputDTO> batchQueryOfflineContractRecordInfo(
            BatchQueryOfflineContractRecordInfoDTO input) {
        List<OfflineContractImportRecordDO> recordDOS =
                offlineContractImportRecordDAO.getByRecordIds(input.getRecordIds());
        if (CollectionUtils.isEmpty(recordDOS)) {
            return Lists.newArrayList();
        }
        // 查询菜单信息
        Set<String> menuIds = recordDOS.stream().map(i -> i.getMenuId()).collect(Collectors.toSet());
        MenuInfoResult menuInfoResult = batchQueryMenuInfo(menuIds, input.isWithMenuPath());
        // 获取菜单id和菜单名称的映射关系
        Map<String, String> menuNameMap = menuInfoResult.getMenuNameMap();
        // 获取菜单id和菜单名称路径的映射关系
        Map<String, String> menuNamePathMap = menuInfoResult.getMenuNamePathMap();
        boolean batchQuery = input.getRecordIds().size() > 1;
        List<OfflineContractRecordInfoOutputDTO> records = Lists.newArrayList();
        for (OfflineContractImportRecordDO record : recordDOS) {
            // 判断导入记录是否属于当前企业
            if (!StringUtils.equals(record.getSubjectGid(), input.getSubjectGid())) {
                throw new BizContractManagerException(
                        batchQuery
                                ? OFFLINE_CONTRACT_RECORD_NOT_ALL_OWNER
                                : OFFLINE_CONTRACT_RECORD_NOT_OWNER);
            }
            OfflineContractRecordInfoOutputDTO outputDTO = new OfflineContractRecordInfoOutputDTO();
            outputDTO.setRecordId(record.getRecordId());
            outputDTO.setSubjectOid(record.getSubjectOid());
            outputDTO.setSubjectGid(record.getSubjectGid());
            outputDTO.setMenuId(record.getMenuId());
            outputDTO.setMenuName(menuNameMap.get(outputDTO.getMenuId()));
            outputDTO.setMenuPath(menuNamePathMap.getOrDefault(outputDTO.getMenuId(), ""));
            outputDTO.setImportWay(record.getImportWay());
            outputDTO.setImportTime(record.getCreateTime().getTime());
            outputDTO.setExtractWay(record.getExtractWay());
            outputDTO.setContractSize(record.getContractSize());
            outputDTO.setSuccessSize(record.getSuccessSize());
            outputDTO.setFailedSize(record.getFailedSize());
            outputDTO.setStatus(record.getStatus());
            if (StringUtils.isNotBlank(record.getExtractConfig())) {
                outputDTO.setExtractConfig(
                        JSONObject.parseObject(
                                record.getExtractConfig(), OfflineContractExtractConfig.class));
            }
            records.add(outputDTO);
        }
        return records;
    }

    @Override
    public OfflineContractRecordContractsOutputDTO queryOfflineContractRecordContracts(
            QueryOfflineContractRecordContractsDTO input) {
        // 查询导入记录， 判断导入记录是否存在
        OfflineContractImportRecordDO record =
                offlineContractImportRecordDAO.getByRecordId(input.getRecordId());
        if (null == record) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_EXIST);
        }
        // 判断导入记录是否属于当前企业
        if (!StringUtils.equals(record.getSubjectGid(), input.getSubjectGid())) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_OWNER);
        }
        // 查询导入记录文件列表
        List<OfflineContractImportFilesDO> filesDOS =
                offlineContractImportFilesDAO.getByRecordId(input.getRecordId());
        // 将文件列表基于recordProcessId分组
        Map<String, List<OfflineContractImportFilesDO>> fileGroupMap =
                filesDOS.stream().collect(Collectors.groupingBy(i -> i.getRecordProcessId()));
        PageHelper.startPage(input.getPageNum(), input.getPageSize());
        // 查询导入记录合同信息列表
        List<OfflineContractImportProcessDO> processInfoDOS =
                offlineContractImportProcessDAO.queryByRecordId(input.getRecordId(), input.getStatusList());
        PageInfo pageInfo = new PageInfo(processInfoDOS);
        // 组装返回数据
        OfflineContractRecordContractsOutputDTO outputDTO =
                new OfflineContractRecordContractsOutputDTO();
        outputDTO.setTotal(pageInfo.getTotal());
        outputDTO.setContracts(Lists.newArrayList());
        if (CollectionUtils.isEmpty(processInfoDOS)) {
            return outputDTO;
        }
        List<OfflineContractDetail> offlineContracts = Lists.newArrayList();
        for (OfflineContractImportProcessDO processInfoDO : processInfoDOS) {
            OfflineContractDetail offlineContract = new OfflineContractDetail();
            offlineContract.setRecordProcessId(processInfoDO.getRecordProcessId());
            offlineContract.setProcessId(processInfoDO.getProcessId());
            offlineContract.setStatus(processInfoDO.getStatus());
            if (IMPORT_FAILED.getStatus().equals(processInfoDO.getStatus())) {
                offlineContract.setFailReason(processInfoDO.getFailReason());
            }
            List<OfflineContractImportFilesDO> importFiles =
                    fileGroupMap.get(processInfoDO.getRecordProcessId());
            offlineContract.setContractFiles(convert2OfflineContractFiles(importFiles));
            if (input.isWithExtract()) {
                offlineContract.setProcessInfo(
                        JSONObject.parseObject(
                                processInfoDO.getProcessInfo(), OfflineContractProcessInfo.class));
            }
            offlineContracts.add(offlineContract);
        }
        outputDTO.setContracts(offlineContracts);

        return outputDTO;
    }

    @Override
    public boolean checkFileExist(List<String> fileIds) {
        List<OfflineContractImportFilesDO> importFilesDOS = offlineContractImportFilesDAO.queryByFileIds(fileIds);
        return CollectionUtils.isNotEmpty(importFilesDOS);
    }

    /**
     * 组装线下合同文件信息
     *
     * @param importFiles
     * @return
     */
    private List<OfflineContractFile> convert2OfflineContractFiles(
            List<OfflineContractImportFilesDO> importFiles) {
        if (CollectionUtils.isEmpty(importFiles)) {
            return Lists.newArrayList();
        }
        return importFiles.stream()
                .map(
                        i -> {
                            OfflineContractFile offlineContractFile = new OfflineContractFile();
                            offlineContractFile.setFileId(i.getFileId());
                            offlineContractFile.setFileName(i.getFileName());
                            offlineContractFile.setFileType(i.getFileType());
                            offlineContractFile.setContractNo(i.getContractNo());
                            offlineContractFile.setCategoryId(i.getContractCategoryId());
                            offlineContractFile.setCategoryName(i.getContractCategoryName());
                            return offlineContractFile;
                        })
                .collect(Collectors.toList());
    }

    @Override
    public OfflineContractOutputDTO queryOfflineContractByRecordProcessId(String recordProcessId) {
        // 查询文件列表
        List<OfflineContractImportFilesDO> importFilesDOS =
                offlineContractImportFilesDAO.queryByRecordProcessId(recordProcessId);
        if (CollectionUtils.isEmpty(importFilesDOS)) {
            return null;
        }
        String recordId = importFilesDOS.get(0).getRecordId();
        // 查询导入记录
        OfflineContractImportRecordDO recordDO =
                offlineContractImportRecordDAO.getByRecordId(recordId);
        if (null == recordDO) {
            return null;
        }
        // 查询合同信息
        OfflineContractImportProcessDO processInfoDO =
                offlineContractImportProcessDAO.queryByRecordProcessId(recordProcessId);
        // 组装返回数据
        OfflineContractOutputDTO outputDTO = new OfflineContractOutputDTO();
        outputDTO.setImporterOid(recordDO.getImporterOid());
        outputDTO.setImporterGid(recordDO.getImporterGid());
        outputDTO.setSubjectOid(recordDO.getSubjectOid());
        outputDTO.setSubjectGid(recordDO.getSubjectGid());
        outputDTO.setMenuId(recordDO.getMenuId());
        outputDTO.setExtractWay(recordDO.getExtractWay());
        outputDTO.setImportClient(recordDO.getImportClient());
        if (StringUtils.isNotBlank(recordDO.getExtractConfig())) {
            outputDTO.setExtractConfig(
                    JSONObject.parseObject(
                            recordDO.getExtractConfig(), OfflineContractExtractConfig.class));
        }
        outputDTO.setStatus(processInfoDO.getStatus());
        OfflineContract offlineContract = new OfflineContract();
        offlineContract.setContractFiles(convert2OfflineContractFiles(importFilesDOS));
        if (null != processInfoDO) {
            outputDTO.setProcessId(processInfoDO.getProcessId());
            if (StringUtils.isNotBlank(processInfoDO.getProcessInfo())) {
                offlineContract.setProcessInfo(
                        JSONObject.parseObject(
                                processInfoDO.getProcessInfo(), OfflineContractProcessInfo.class));
            }
        }
        outputDTO.setContract(offlineContract);

        return outputDTO;
    }

    @Override
    public void extractProcessInfo(String recordProcessId) {
        try {
            // 查询线下合同导入信息
            OfflineContractOutputDTO outputDTO = queryOfflineContractByRecordProcessId(recordProcessId);
            // 如果信息为空 或者 已经存在合同信息， 直接跳过
            if (null == outputDTO
                    || null == outputDTO.getContract()
                    || CollectionUtils.isEmpty(outputDTO.getContract().getContractFiles())) {
                return;
            }
            // 如果停止导入， 跳过
            if (IMPORT_STOPPED.getStatus().equals(outputDTO.getStatus())) {
                return;
            }
            OfflineContractExtractConfig extractConfig = outputDTO.getExtractConfig();
            if (null == extractConfig) {
                return;
            }
            List<OfflineContractFile> contractFiles = outputDTO.getContract().getContractFiles();
            List<String> fileIds =
                    contractFiles.stream().map(i -> i.getFileId()).collect(Collectors.toList());
            // 发送提取消息
            RpcAsyncExtractContractV2Input input = new RpcAsyncExtractContractV2Input();
            input.setBizId(recordProcessId);
            input.setFileType(ContractFileTypeEnum.OFFLINE_PDF.getType());
            input.setFileIds(fileIds);
            input.setFields(buildExtractFields(extractConfig));
            input.setNotifyTag(OFFLINE_EXTRACT_PROCESS_TAG);
            // 扩展信息
            ClmCExtendInfo clmCExtendInfo =
                        new ClmCExtendInfo(recordProcessId, outputDTO.getSubjectOid(), AI_BIZ_SOURCE, AI_BIZ_SOURCE_DESC, BILLING_DATA_FUNCTION_TYPE,Boolean.FALSE);

            clmCapabilityService.asyncExtractContract(input, clmCExtendInfo);
        } catch (Exception e) {
            ExceptionLogUtil.traceLog(log, e, "extract process failed");
            UpdateStatusByRecordProcessIdDTO input = new UpdateStatusByRecordProcessIdDTO();
            input.setRecordProcessId(recordProcessId);
            input.setStatus(IMPORT_FAILED);
            String message = e instanceof BaseBizRuntimeException ? e.getMessage() : "系统异常";
            input.setFailMessage("合同信息提取失败: " + message);
            updateStatusByRecordProcessId(input);
        }
    }

    @Override
    public void saveExtractProcessInfoResult(String recordProcessId, Map<String, Object> extractResult, String failCode) {
        OfflineContractImportProcessDO importProcessDO =
                offlineContractImportProcessDAO.queryByRecordProcessId(recordProcessId);
        if (null == importProcessDO) {
            return;
        }
        // 如果processId不为空， 表示已经导入成功， 直接跳过
        if (StringUtils.isNotBlank(importProcessDO.getProcessId())) {
            return;
        }
        List<OfflineContractImportFilesDO> filesDOS =
                offlineContractImportFilesDAO.queryByRecordProcessId(recordProcessId);
        if (CollectionUtils.isEmpty(filesDOS)) {
            return;
        }
        String recordId = importProcessDO.getRecordId();
        OfflineContractImportRecordDO recordDO = offlineContractImportRecordDAO.getByRecordId(recordId);
        if (null == recordDO) {
            return;
        }
        // 组装合同信息
        OfflineContractProcessInfo processInfo =
                JSONObject.parseObject(
                        importProcessDO.getProcessInfo(), OfflineContractProcessInfo.class);
        // 如果提取信息全部为空， 则无需更新合同信息， 直接生成合同流程
        if (MapUtils.isEmpty(extractResult) || extractResult.values().stream()
                .allMatch(i -> null == i || StringUtils.isBlank(i.toString()))) {
            // 如果是因欠费导致ai提取失败，则更新状态为失败，以便后续重试
            if (JobFailEnum.INSUFFICIENT_BALANCE.getCode().equals(failCode)) {
                offlineContractImportProcessDAO.updateFailReason(
                        recordProcessId, "ai提取失败", FailCodeEnum.AI_ARREARS.getCode());
                offlineContractImportRecordDAO.increaseFailed(recordId);
                publisher.publishEvent(recordEvent(recordId, UPDATE_RECORD_STATUS));
                return;
            }
            publisher.publishEvent(recordProcessEvent(Lists.newArrayList(recordProcessId), GENERATE_PROCESS,processInfo.getMasterProcessId()));
            return;
        }
        // 更新合同编号
        Object contractNo = extractResult.get(CONTRACT_NO.getFieldKeyword());
        if (null != contractNo && StringUtils.isNotBlank(contractNo.toString())) {
            // 校验合同编号格式， 如果不符合格式要求， 直接忽略
            String contractNoValue = contractNo.toString();
            if (!contractNoValue.matches(SystemConstant.REGEXP_CONTRACT_NO)) {
                log.info("{} 不符合合同编号格式， 正确格式：{}", contractNoValue, SystemConstant.REGEXP_CONTRACT_NO);
            } else {
                String fileId = filesDOS.get(0).getFileId();
                offlineContractImportFilesDAO.updateContractNoByRecordId(recordId, fileId, contractNoValue);
            }
        }

        // 查询提取配置
        OfflineContractExtractConfig extractConfig =
                JSONObject.parseObject(
                        recordDO.getExtractConfig(), OfflineContractExtractConfig.class);


        // 设置发起方信息
        OfflineContractInitiator initiator = new OfflineContractInitiator();
        initiator.setAccountId(recordDO.getSubjectOid());
        processInfo.setInitiator(initiator);
        // 设置签署方信息
        List<OfflineContractAccount> signers = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(extractConfig.getSigners())) {
            extractConfig.getSigners().forEach(i -> {
                String accountNameKeyWord = i.getSignerKeyword()+ SIGNER_NAME.getFieldKeyword();
                String subjectNameKeyWord = i.getSignerKeyword()+ SIGNER_ORGAN_NAME.getFieldKeyword();
                // 获取提取结果
                Object accountNameResult = extractResult.get(accountNameKeyWord);
                Object subjectNameResult = extractResult.get(subjectNameKeyWord);
                // 转换为字符串
                String accountName = null == accountNameResult ? null : String.valueOf(accountNameResult);
                String subjectName = null == subjectNameResult ? null : String.valueOf(subjectNameResult);
                // 如果提取到的签署人姓名和企业名称均为空， 跳过不保存
                if (StringUtils.isAllBlank(accountName, subjectName)) {
                    return;
                }
                OfflineContractAccount signer = new OfflineContractAccount();
                signer.setAccountName(accountName);
                signer.setSubjectName(subjectName);
                signer.setSubjectType(i.getSignerSubjectType());
                if (SubjectTypeEnum.PERSON.getType().equals(i.getSignerSubjectType())) {
                    if (StringUtils.isBlank(signer.getSubjectName())) {
                        signer.setSubjectName(signer.getAccountName());
                    }
                }
                signers.add(signer);
            });
        }
        processInfo.setSigners(signers);
        // 设置合同到期时间
        Object contractValidity = extractResult.get(CONTRACT_VALIDITY.getFieldKeyword());
        if (null != contractValidity && StringUtils.isNumeric(contractValidity.toString()) && Long.parseLong(contractValidity.toString()) < DateUtil.MAX_TIMESTAMP) {
            // 转换为日期格式
            Date toDate = new Date(Long.valueOf(contractValidity.toString()));
            // 合同到期时间为当前的23:59:59
            processInfo.setContractValidity(DateUtils.getDayEnd(toDate).getTime());
        }
        importProcessDO.setProcessInfo(JSONObject.toJSONString(processInfo));
        offlineContractImportProcessDAO.updateProcessInfo(importProcessDO);
        publisher.publishEvent(recordProcessEvent(Lists.newArrayList(recordProcessId), GENERATE_PROCESS,processInfo.getMasterProcessId()));
    }

    @Override
    public boolean existsFailCodeImportProcess(String recordId, FailCodeEnum failCode, String subjectGid) {
        // 查询导入记录， 判断导入记录是否存在
        OfflineContractImportRecordDO record =
                offlineContractImportRecordDAO.getByRecordId(recordId);
        if (null == record) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_EXIST);
        }
        // 判断导入记录是否属于当前企业
        if (!StringUtils.equals(record.getSubjectGid(), subjectGid)) {
            throw new BizContractManagerException(OFFLINE_CONTRACT_RECORD_NOT_OWNER);
        }
        OfflineContractImportProcessDO processDO = offlineContractImportProcessDAO.queryCanRetryByRecordProcessId(recordId, failCode.getCode());
        return null!= processDO;
    }

    /**
     * 组装提取字段信息
     * @param extractConfig
     * @return
     */
    private List<ExtractField> buildExtractFields(OfflineContractExtractConfig extractConfig) {
        List<ExtractField> extractFields = Lists.newArrayList();
        for (OfflineContractExtractConfig.OfflineExtractField field : extractConfig.getFields()) {
            if (!field.isEnable()) {
                continue;
            }
            ExtractConfigFieldEnum fieldEnum = from(field.getFieldCode());
            if (null == fieldEnum) {
                continue;
            }
            // 签署方姓名提取字段
            if (CollectionUtils.isNotEmpty(extractConfig.getSigners())
                    && SIGNER_NAME.equals(fieldEnum)) {
                extractConfig.getSigners().forEach(i -> {
                    ExtractField extractField = new ExtractField();
                    extractField.setFieldKey(i.getSignerKeyword()+ fieldEnum.getFieldKeyword());
                    extractField.setFieldType(fieldEnum.getFieldType().getType());
                    extractFields.add(extractField);
                });
                continue;
            }
            // 签署方企业名称提取字段
            if (CollectionUtils.isNotEmpty(extractConfig.getSigners())
                    && SIGNER_ORGAN_NAME.equals(fieldEnum)) {
                extractConfig.getSigners().forEach(i -> {
                    if (!SubjectTypeEnum.ORG.getType().equals(i.getSignerSubjectType())) {
                        return;
                    }
                    ExtractField extractField = new ExtractField();
                    extractField.setFieldKey(i.getSignerKeyword()+ fieldEnum.getFieldKeyword());
                    extractField.setFieldType(fieldEnum.getFieldType().getType());
                    extractFields.add(extractField);
                });
                continue;
            }
            // 其他字段
            ExtractField extractField = new ExtractField();
            extractField.setFieldKey(fieldEnum.getFieldKeyword());
            extractField.setFieldType(fieldEnum.getFieldType().getType());
            extractFields.add(extractField);
        }
        return extractFields;
    }

    @Override
    @Transactional
    public void updateStatusByRecordProcessId(UpdateStatusByRecordProcessIdDTO input) {
        String recordProcessId = input.getRecordProcessId();
        OfflineContractStatusEnum status = input.getStatus();
        // 获取文件列表
        List<OfflineContractImportFilesDO> importFilesDOS =
                offlineContractImportFilesDAO.queryByRecordProcessId(recordProcessId);
        if (CollectionUtils.isEmpty(importFilesDOS)) {
            return;
        }
        // 获取导入记录id
        String recordId = importFilesDOS.get(0).getRecordId();
        // 更新导入状态
        if (IMPORTED.equals(status)) {
            offlineContractImportProcessDAO.updateProcessId(recordProcessId, input.getProcessId());
            offlineContractImportRecordDAO.increaseSuccess(recordId);
        } else if (IMPORT_FAILED.equals(status)) {
            offlineContractImportProcessDAO.updateFailReason(
                    recordProcessId, input.getFailMessage(), FailCodeEnum.BIZ_ERROR.getCode());
            offlineContractImportRecordDAO.increaseFailed(recordId);
        }
        publisher.publishEvent(recordEvent(recordId, UPDATE_RECORD_STATUS));
    }

    @Override
    public void calculateAndUpdateRecordStatus(String recordId) {
        // 查询导入记录所有流程状态
        List<OfflineContractImportProcessDO> recordProcessList =
                offlineContractImportProcessDAO.queryByRecordId(recordId, null);
        // 基于计算导入记录状态
        OfflineContractStatusEnum contractStatus = calculateStatusByImportProcessDOs(recordProcessList);
        // 更新导入记录状态
        if (contractStatus.isComplete()) {
            offlineContractImportRecordDAO.updateStatus(recordId, contractStatus.getStatus());
        }
    }

    /**
     * 批量查询菜单信息
     * @param menuIds
     * @param withMenuPath
     * @return
     */
    private MenuInfoResult batchQueryMenuInfo(Set<String> menuIds, boolean withMenuPath) {
        List<MenuDO> menuDOs = menuService.getMenuByMenuIds(menuIds);
        // 获取菜单id和菜单名称的映射关系
        Map<String, String> menuNameMap =
                menuDOs.stream().collect(Collectors.toMap(i -> i.getMenuId(), i -> i.getName()));
        Map<String, String> menuNamePathMap = Maps.newHashMap();
        if (withMenuPath) {
            // 获取菜单id和菜单id路径的映射关系
            Map<String, String> menuPathMap =
                    menuDOs.stream()
                            .filter(i -> StringUtils.isNotBlank(i.getPath()))
                            .collect(Collectors.toMap(i -> i.getMenuId(), i -> i.getPath()));
            // 将菜单id路径转为菜单名称路径
            menuNamePathMap = menuService.batchConvertMenuNamePath(menuPathMap);
        }
        // 组装返回值
        MenuInfoResult menuInfoResult = new MenuInfoResult();
        menuInfoResult.setMenuNameMap(menuNameMap);
        menuInfoResult.setMenuNamePathMap(menuNamePathMap);
        return menuInfoResult;
    }

    /**
     * 归档菜单信息
     */
    @Data
    public static class MenuInfoResult {

        /** 菜单名称MAP, MAP<菜单id, 菜单名称> */
        private Map<String, String> menuNameMap;

        /** 菜单名称路径MAP, MAP<菜单id, 菜单名称路径> */
        private Map<String, String> menuNamePathMap;
    }
}

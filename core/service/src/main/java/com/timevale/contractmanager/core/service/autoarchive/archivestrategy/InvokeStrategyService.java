package com.timevale.contractmanager.core.service.autoarchive.archivestrategy;

import com.timevale.contractmanager.core.service.autoarchive.OperationConditionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author:jianyang
 * @since 2021-05-10 14:46
 */
@Service
@Slf4j
public class InvokeStrategyService {
	Map<String, OperationConditionService> strategyServiceMap = new HashMap<>();

	/**
	 *构造实现类
	 * @param operationConditionServices spring容器中所有OperationConditionService的实现类
	 */
	public InvokeStrategyService(List<OperationConditionService> operationConditionServices){
		for (OperationConditionService conditionService : operationConditionServices){
			strategyServiceMap.put(conditionService.getFieldId(),conditionService);
		}
	}

	/**
	 * 校验条件
	 * @param fieldId
	 * @param tenantOid
	 * @param tenantGid
	 * @param ruleId
	 * @param conditionParams
	 * @return
	 */
	public Boolean checkCondition(String fieldId,String tenantOid, String tenantGid,String ruleId, String conditionParams){
		OperationConditionService conditionService = strategyServiceMap.get(fieldId);
		if(conditionService == null){
			log.info("auto archive get strategy service fail: conditionService is null");
			return true;
		}
		if(Boolean.FALSE.equals(conditionService.checkCondition(fieldId,tenantOid,tenantGid,ruleId,conditionParams))){
			throw conditionService.getErrorException();
		}
		return true;
	}
}

package com.timevale.contractmanager.core.service.transfer.impl;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.TRANSFER_BIZ_SERVICE_NON_EXIST;

import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.model.bo.transfer.SingleTransferResultBO;
import com.timevale.contractmanager.core.model.bo.transfer.TransferUserListBO;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.transfer.TransferResultDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.mq.transfer.ApprovalGroupTransferProducer;
import com.timevale.contractmanager.core.service.transfer.TransferAbstractBizService;
import com.timevale.contractmanager.core.service.transfer.TransferBizService;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023-04-13 15:07
 */
@Component
public class TransferApprovalGroupBizServiceImpl extends TransferAbstractBizService
        implements TransferBizService {
    @Autowired private TransferMixApprovalBizServiceImpl transferMixApprovalBizService;
    @Autowired private ApprovalGroupTransferProducer approvalGroupTransferProducer;

    @Override
    public Boolean isSupport(Integer transferScene) {
        return Objects.equals(TransferSceneEnum.APPROVE_GROUP.getCode(), transferScene);
    }

    @Override
    public TransferResultDTO transfer(TransferBizContext transferBizContext) {
        approvalGroupTransferProducer.sendMessage(JsonUtils.obj2json(transferBizContext), null);
        return new TransferResultDTO(false);
    }

    @Override
    public Map<String, Long> transferCount(
            UserAccountDetail tenantAccount, List<Account> userAccounts) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }

    @Override
    public OrgDeptListResponse transferUserList(TransferUserListBO transferUserListBO) {
        return transferMixApprovalBizService.transferUserList(transferUserListBO);
    }

    @Override
    public Integer transferToUserCount(String tenantId, String accountOid) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }

    @Override
    public Long addTransferToUserCount(String tenantId, String accountOid) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }

    @Override
    public void singleTransferResultCallback(SingleTransferResultBO transferResultBO) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }
}

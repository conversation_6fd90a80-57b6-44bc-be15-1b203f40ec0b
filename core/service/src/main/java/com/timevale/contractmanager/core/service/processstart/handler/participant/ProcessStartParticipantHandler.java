package com.timevale.contractmanager.core.service.processstart.handler.participant;

import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessStartSignParam;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.doccooperation.service.enums.CooperationerRoleEnum;
import com.timevale.doccooperation.service.input.flow.StartCooperationFlowInput;
import com.timevale.footstone.base.model.enums.NoticeTypeEnum;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 流程发起参与方处理类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Component
public class ProcessStartParticipantHandler {

    /** 发起子流程场景下，当前操作用户移除的通知方式列表 */
    private static final String PROP_REMOVE_NOTICE_TYPES = "current.operator.remove.notice.types";
    /** 发起子流程场景下，当前操作用户是否移除全部通知方式，即不发送通知 */
    private static final String PROP_REMOVE_ALL_NOTICE = "current.operator.remove.all.notice";
    /** 默认移除的通知方式 */
    private static final String DEFAULT_REMOVE_NOTICE_TYPES = NoticeTypeEnum.SMS.getIntType().toString();

    @Autowired UserCenterService userCenterService;

    /**
     * 处理填写方通知方式
     * @param startSignParam
     */
    public void handleCooperatorNoticeTypes(ProcessStartCoreRequest request, StartCooperationFlowInput startSignParam) {
        if (CollectionUtils.isEmpty(startSignParam.getCooperationers())) {
            return;
        }
        List<String> cooperateParticipantIds = request.getParticipants().stream().filter(i -> CooperationerRoleEnum.containFormulaterRole(i.getRole())).map(i -> i.getParticipantId()).collect(Collectors.toList());
        // 获取操作用户
        String operatorId = request.getOperatorAccount().getAccountOid();
        // 处理首个顺序填写方移除指定通知方式
        handleFirstOrderCooperatorRemoveNoticeType(startSignParam, i -> cooperateParticipantIds.contains(i.getCooperationerId()), operatorId);
    }

    /**
     * 处理签署方通知方式
     * @param startSignParam
     */
    public void handleSignerNoticeTypes(ProcessStartSignParam startSignParam, String operatorId) {
        // 处理首个顺序签署方移除指定通知方式
        handleFirstOrderSignerRemoveNoticeType(startSignParam, operatorId);
    }

    /**
     * 处理首个顺序签署方移除通知方式
     * @param startSignParam
     * @param accountId
     */
    private void handleFirstOrderSignerRemoveNoticeType(ProcessStartSignParam startSignParam, String accountId) {
        if (StringUtils.isBlank(accountId) || CollectionUtils.isEmpty(startSignParam.getSigners())) {
            return;
        }
        log.info("handleFirstOrderSignerRemoveNoticeType , accountId: {}", accountId);
        UserAccount userAccount = userCenterService.getUserAccountDetailByOid(accountId);
        // 如果当前操作人为发起人，不处理
        ProcessStartSignParam.FlowAccount initiator = startSignParam.getInitiator();
        if (userAccount.isSameAccount(initiator.getAccountOid(), initiator.getAccountGid())) {
            return;
        }
        // 如果当前操作人为签署流程首个顺序签署人， 则不发送指定通知
        startSignParam.getSigners().sort(Comparator.comparingInt(i -> i.getSignOrder()));
        Integer firstOrder = startSignParam.getSigners().get(0).getSignOrder();
        List<Integer> defaultNoticeTypes = IdsUtil.getIntegerIdList(startSignParam.getNoticeType());
        startSignParam.getSigners().stream().filter(i -> i.getSignOrder() == firstOrder).forEach(i -> {
            i.obtainAccounts().stream()
                    .filter(a -> userAccount.isSameAccount(a.getAccountOid(), a.getAccountGid()))
                    .forEach(a -> a.setSignNoticeTypes(calculateFinalNoticeTypes(a.getSignNoticeTypes(), i.getExt().getSignNoticeTypes(), defaultNoticeTypes)));
        });
    }

    /**
     * 处理首个顺序填写方移除通知方式
     *
     * @param startSignParam
     * @param accountId
     */
    private void handleFirstOrderCooperatorRemoveNoticeType(
            StartCooperationFlowInput startSignParam,
            Predicate<StartCooperationFlowInput.FlowCooperationer> cooperatorFilter,
            String accountId) {
        if (StringUtils.isBlank(accountId) || CollectionUtils.isEmpty(startSignParam.getCooperationers())) {
            return;
        }
        log.info("handleFirstOrderCooperatorRemoveNoticeType , accountId: {}", accountId);
        List<StartCooperationFlowInput.FlowCooperationer> cooperationers =
                startSignParam.getCooperationers().stream().filter(cooperatorFilter).collect(Collectors.toList());
        // 获取首个填写顺序
        cooperationers.sort(Comparator.comparingInt(i -> i.getCooperateOrder()));
        Integer firstOrder = cooperationers.get(0).getCooperateOrder();
        // 如果当前操作人为填写流程首个顺序填写人， 则不发送指定通知
        UserAccount userAccount = userCenterService.getUserAccountDetailByOid(accountId);
        List<Integer> defaultNoticeTypes = IdsUtil.getIntegerIdList(startSignParam.getNoticeType());
        cooperationers.stream().filter(i -> i.getCooperateOrder() == firstOrder).forEach(i -> {
            i.obtainAccounts().stream()
                    .filter(a -> userAccount.isSameAccount(a.getAccountOid(), a.getAccountGid()))
                    .forEach(a -> a.setCooperateNoticeTypes(calculateFinalNoticeTypes(a.getCooperateNoticeTypes(), i.getExt().getCooperateNoticeTypes(), defaultNoticeTypes)));
        });
    }

    /**
     * 计算获取最终用户通知方式
     *
     * @param accountNoticeTypes
     * @param participantNoticeTypes
     * @param flowNoticeTypes
     * @return
     */
    private List<Integer> calculateFinalNoticeTypes(
            List<Integer> accountNoticeTypes,
            List<Integer> participantNoticeTypes,
            List<Integer> flowNoticeTypes) {
        // 判断是否移除全部通知方式
        if (ConfigService.getAppConfig().getBooleanProperty(PROP_REMOVE_ALL_NOTICE, false)) {
            return Lists.newArrayList(NoticeTypeEnum.INMAIL.getIntType());
        }
        // 获取用户通知方式
        List<Integer> noticeTypes =
                Optional.ofNullable(accountNoticeTypes).orElse(Optional.ofNullable(participantNoticeTypes).orElse(flowNoticeTypes));
        // 获取需移除的通知方式配置
        String propRemoveNoticeTypes = ConfigService.getAppConfig().getProperty(PROP_REMOVE_NOTICE_TYPES, DEFAULT_REMOVE_NOTICE_TYPES);
        // 需移除的通知方式列表
        Set<Integer> needRemoveNoticeTypes = IdsUtil.getIntegerSet(propRemoveNoticeTypes);
        // 移除通知
        noticeTypes.removeAll(needRemoveNoticeTypes);

        return noticeTypes;
    }
}

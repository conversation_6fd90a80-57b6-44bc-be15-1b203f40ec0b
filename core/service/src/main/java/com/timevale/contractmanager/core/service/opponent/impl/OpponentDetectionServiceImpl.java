package com.timevale.contractmanager.core.service.opponent.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionOrgDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionProblemInfoDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionSettingDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionTaskDO;
import com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionOrgDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionProblemInfoDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionSettingDAO;
import com.timevale.contractmanager.common.dal.dao.opponententity.detection.OpponentDetectionTaskDAO;
import com.timevale.contractmanager.common.service.enums.opponent.detection.*;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.common.service.integration.client.FileSystemClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.model.BatchExportKeyModel;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionProblemInfoBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionReportBO;
import com.timevale.contractmanager.core.model.dto.request.opponent.detection.OpponentDetectioBatchRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.detection.OpponentDetectionReportRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.detection.OpponentDetectionSaveSettingRequest;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEnterpriseInformationBO;
import com.timevale.contractmanager.core.model.dto.response.opponent.detection.*;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.component.opponent.detection.OpponentDetectionAdapter;
import com.timevale.contractmanager.core.service.enums.DeletedEnum;
import com.timevale.contractmanager.core.service.enums.OpponentBusinessTagEnum;
import com.timevale.contractmanager.core.service.mq.model.opponent.OpponentDetectionExportMsg;
import com.timevale.contractmanager.core.service.mq.producer.OpponentMqProducer;
import com.timevale.contractmanager.core.service.opponent.EnterpriseInformationService;
import com.timevale.contractmanager.core.service.opponent.OpponentDetectionService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.saas.common.manage.common.service.enums.TaskStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.TaskTypeEnum;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskAddInput;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskUpdateInput;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskAddBean;
import com.timevale.saas.common.manage.common.service.model.input.bean.TaskBizInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:jianyang
 * @since 2021-08-11 11:12
 */
@Service
@Slf4j
public class OpponentDetectionServiceImpl implements OpponentDetectionService {

	@Value("${opponent.detection.problem.export.async.threshold:100}")
	private Integer opponentDetectionProblemExportAsyncThreshold;

	private static final String[][] detectionProblemHeader = { { "被检测企业" }, { "问题或风险" }, { "严重等级" }, { "处理建议" }};


	@Autowired private UserCenterService userCenterService;

	@Autowired
	private OpponentDetectionSettingDAO settingDAO;

	@Autowired private OpponentDetectionTaskDAO taskDAO;

	@Autowired private OpponentDetectionOrgDAO orgDAO;

	@Autowired private OpponentDetectionAdapter detectionAdapter;

	@Autowired private OpponentDetectionProblemInfoDAO problemInfoDAO;
	
	@Autowired private FileSystemClient fileSystemClient;

	@Autowired private SaasCommonClient saasCommonClient;

	@Autowired private SystemConfig systemConfig;

	@Autowired private OpponentMqProducer opponentMqProducer;
	
	@Autowired private EnterpriseInformationService enterpriseInformationService;
	@Autowired
    private OpponentEntityDAO opponentEntityDAO;


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveSetting(String tenantOid, OpponentDetectionSaveSettingRequest request) {
		// 租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);

		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}

		/** 开启经验范围检测，关键词不能为空*/
		if(request.getBusinessScopeDetection() != null
				&& request.getBusinessScopeDetection() == OpponentDetectionSwitchEnum.OPEN.getType()
				&& CollectionUtils.isEmpty(request.getScope())){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_SCOPE_NOT_EMPTY.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_SCOPE_NOT_EMPTY.getMessage());
		}


		OpponentDetectionSettingDO existSetting = settingDAO.getByTenantGid(userAccountTenant.getAccountGid(), DeletedEnum.NO.code());
		if (existSetting != null) {
			existSetting.setBusinessScope(request.getScope() != null ? JSON.toJSONString(request.getScope()) : null);
			existSetting.setImmediatelyPush(request.getImmediatelyPush());
			existSetting.setPushObject(request.getPushObject());
			existSetting.setRealTimeDetection(request.getRealTimeDetection());
			existSetting.setPush(request.getPush());
			existSetting.setBusinessScopeDetection(request.getBusinessScopeDetection());
			existSetting.setAutoSaveOpponent(request.getAutoSaveOpponent() !=null ? request.getAutoSaveOpponent() : 1);
			settingDAO.update(existSetting);
			return;
		}

		OpponentDetectionSettingDO settingDO = OpponentDetectionSettingDO.builder()
				.businessScope(request.getScope() != null && request.getBusinessScopeDetection() == OpponentDetectionSwitchEnum.OPEN.getType() ? JSON.toJSONString(request.getScope()) : null)
				.realTimeDetection(request.getRealTimeDetection() != null ? request.getRealTimeDetection() : OpponentDetectionSwitchEnum.CLOSE.getType())
				.immediatelyPush(request.getImmediatelyPush() != null ? request.getImmediatelyPush() : OpponentDetectionSwitchEnum.CLOSE.getType())
				.usedDetectionNum(0)
				.pushObject(StringUtils.isNotBlank(request.getPushObject()) ? request.getPushObject() : null)
				.push(request.getPush() != null ? request.getPush() : OpponentDetectionSwitchEnum.CLOSE.getType())
				.businessScopeDetection(request.getBusinessScopeDetection() != null ? request.getBusinessScopeDetection() : OpponentDetectionSwitchEnum.CLOSE.getType())
				.detectionNum(detectionAdapter.getVipInfo(tenantOid, RequestContextExtUtils.getClientId()))
				.tenantGid(userAccountTenant.getAccountGid())
				.autoSaveOpponent(request.getAutoSaveOpponent() !=null ? request.getAutoSaveOpponent() : 1)
				.build();

		settingDAO.insert(settingDO);
	}


	@Override
	public OpponentDetectionSettingResponse getSetting(String tenantOid) {
		// 租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);

		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}

		OpponentDetectionSettingDO settingDO = settingDAO.getByTenantGid(userAccountTenant.getAccountGid(), DeletedEnum.NO.code());
		if (settingDO == null) {
			return OpponentDetectionSettingResponse
					.builder()
					.immediatelyPush(OpponentDetectionSwitchEnum.CLOSE.getType())
					.realTimeDetection(OpponentDetectionSwitchEnum.CLOSE.getType())
					.businessScopeDetection(OpponentDetectionSwitchEnum.CLOSE.getType())
					.push(OpponentDetectionSwitchEnum.CLOSE.getType())
					.autoSaveOpponent(OpponentDetectionSwitchEnum.OPEN.getType())
					.build();
		}

		return OpponentDetectionSettingResponse.builder()
				.push(settingDO.getPush())
				.pushObject(StringUtils.isNotBlank(settingDO.getPushObject()) ? settingDO.getPushObject() : null)
				.realTimeDetection(settingDO.getRealTimeDetection())
				.scope(StringUtils.isNotBlank(settingDO.getBusinessScope()) ? settingDO.getBusinessScope() : null)
				.immediatelyPush(settingDO.getImmediatelyPush())
				.autoSaveOpponent(settingDO.getAutoSaveOpponent())
				.businessScopeDetection(settingDO.getBusinessScopeDetection())
				.build();
	}

	@Override
	public Integer batchDetection(String tenantOid, OpponentDetectioBatchRequest request, String operatorOid) {
		// 租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		Date startTime = request.getStartTime();
		Date endTime = request.getEndTime();

		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}
		
		/** 时间填充并且校验 */
		if (endTime == null) {
			endTime = new Date();
		}

		if(startTime != null && startTime.after(endTime)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TIME_ILLEGALITY.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TIME_ILLEGALITY.getMessage());
		}

		try {
			detectionAdapter.batchDetectionTaskStart(userAccountTenant, startTime, endTime, operatorOid);
		}catch (BizContractManagerException e){
			return Integer.valueOf(e.getMessage());
		}
		return ResultEnum.SUCCESS.getCode();
	}

	@Override
	public OpponentDetectioInitDataResponse getOpponentDetectionTaskInfo(String tenantOid, Integer taskType) {
		OpponentDetectioInitDataResponse response = new OpponentDetectioInitDataResponse();
		// 租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}

		/** 查询最近一次任务 **/
		OpponentDetectionTaskDO taskDO = taskDAO.getByTaskLast(userAccountTenant.getAccountGid(), taskType);
		if (taskDO != null) {
			response.setDetectionTaskId(taskDO.getDetectionTaskId());
			response.setTaskType(taskDO.getTaskType());
			response.setTaskStatus(taskDO.getTaskStatus());
		}
		/** 查询配置信息 */
		OpponentDetectionSettingDO settingDO = settingDAO.getByTenantGid(userAccountTenant.getAccountGid(), DeletedEnum.NO.code());
		if (settingDO != null) {
			response.setSocp(StringUtils.isNotBlank(settingDO.getBusinessScope()) ? settingDO.getBusinessScope() : null);
			response.setRealTimeDetection(settingDO.getRealTimeDetection());
			response.setUsableDetectionNum((settingDO.getDetectionNum() - settingDO.getUsedDetectionNum())>0 ?settingDO.getDetectionNum() - settingDO.getUsedDetectionNum():0);
		}else {
			response.setRealTimeDetection(OpponentDetectionSwitchEnum.CLOSE.getType());
			response.setUsableDetectionNum(detectionAdapter.getVipInfo(tenantOid, RequestContextExtUtils.getClientId()));
		}
		return response;
	}

	@Override
	public OpponentDetectioTaskListResponse getDetectionTasks(
			Integer pageSize, Integer pageNum,
			String tenantOid, Long startTime, Long endTime) {
		OpponentDetectioTaskListResponse response = new OpponentDetectioTaskListResponse();
		List<OpponentDetectioTaskResponse> tasks = Lists.newArrayList();
		// 租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}

		Date start = null;
		Date end = null;
		if(startTime != null){
			start = new Date(startTime);
		}
		if(endTime != null){
			end = new Date(endTime);
		}
		long count;
		// 计算分页参数
		int offset = (pageNum - 1) * pageSize;
		count = taskDAO.getStatistics(userAccountTenant.getAccountGid(), OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType(), start, end);
		if (count == 0) {
			response.setTotal(count);
			response.setTaskResponses(tasks);
			return response;
		}
		/** 查询任务列表 */
		List<OpponentDetectionTaskDO> taskDOS = taskDAO.getTaskList(userAccountTenant.getAccountGid(),
				OpponentDetectionTaskTypeEnum.BATCH_DETECTION.getType(), pageSize, offset, start, end);

		if(CollectionUtils.isEmpty(taskDOS)){
			response.setTotal(count);
			response.setTaskResponses(tasks);
			return response;
		}
		/** 封装信息 */
		tasks = taskDOS.stream().map(x -> {
			OpponentDetectioTaskResponse task = new OpponentDetectioTaskResponse();
			task.setDetectionTime(x.getCreateTime());
			task.setDetectionTotality(x.getDetectionTotality());
			task.setElapsedTime(x.getElapsedTime());
			task.setReportProblems(x.getReportProblemNum());
			task.setTaskType(x.getTaskType());
			task.setTaskStatus(x.getTaskStatus());
			task.setVentureBusinessNum(x.getVentureBusinessNum());
			task.setDetectionQuantityCompletion(x.getDetectionQuantityCompletion());
			task.setDetectionTaskId(x.getDetectionTaskId());
			return task;
		}).collect(Collectors.toList());
		response.setTotal(count);
		response.setTaskResponses(tasks);
		return response;
	}

	@Override
	public OpponentDetectioTaskResponse getTaskStatus(String tenantOid, String detectionTaskId) {
		OpponentDetectioTaskResponse response = new OpponentDetectioTaskResponse();
		// 租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}

		OpponentDetectionTaskDO taskDO = taskDAO.getTaskByTaskId(userAccountTenant.getAccountGid(), detectionTaskId);
		if (taskDO != null) {
			response.setDetectionTime(taskDO.getCreateTime());
			response.setReportProblems(taskDO.getReportProblemNum());
			response.setElapsedTime(taskDO.getElapsedTime());
			response.setDetectionTotality(taskDO.getDetectionTotality());
			response.setTaskType(taskDO.getTaskType());
			response.setTaskStatus(taskDO.getTaskStatus());
			response.setDetectionQuantityCompletion(taskDO.getDetectionQuantityCompletion());
			response.setVentureBusinessNum(taskDO.getVentureBusinessNum());
			response.setElapsedTime(taskDO.getModifyTime().getTime() - taskDO.getCreateTime().getTime());
		}
		return response;
	}

	@Override
	public OpponentDetectioTaskReportListResponse getDetectionReport(String tenantOid,
																	 OpponentDetectionReportRequest request) {
		// 租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);

		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}

		OpponentDetectionTaskDO taskDO = taskDAO.getTaskByTaskId(userAccountTenant.getAccountGid(), request.getDetectionTaskId());
		if(taskDO == null){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TASK_NO_PRIVILEGE.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TASK_NO_PRIVILEGE.getMessage());
		}

		// 计算分页参数
		int offset = (request.getPageNum() - 1) * request.getPageSize();

		/**构造查询条件*/
		OpponentDetectionReportBO reportBO = converListParam(request,offset);

		/**统计总数*/
		long count = orgDAO.countList(userAccountTenant.getAccountGid(),
				reportBO.getProblemNo(), reportBO.getRiskNo(),
				reportBO.getDetectionTaskId(),
				reportBO.getOrgName(),
				OpponentDetectionOrgStatusEnum.COMPLETE_ABNORMAL.getType(),
				reportBO.getStartDate(), reportBO.getEndDate());
		if (count == 0) {
			OpponentDetectioTaskReportListResponse response = new OpponentDetectioTaskReportListResponse();
			response.setTotal(count);
			response.setReportResponses(Lists.newArrayList());
			return response;
		}

		OpponentDetectioTaskReportListResponse response = getReport(request, userAccountTenant, reportBO);
		response.setTotal(count);
		return response;
	}

	private OpponentDetectioTaskReportListResponse getReport(OpponentDetectionReportRequest request,
															 UserAccount userAccountTenant,
															 OpponentDetectionReportBO reportBO)
	{
		OpponentDetectioTaskReportListResponse response = new OpponentDetectioTaskReportListResponse();

		/**获取问题企业 */
		List<OpponentDetectionOrgDO> list = orgDAO.getList(userAccountTenant.getAccountGid(),
				reportBO.getProblemNo(), reportBO.getRiskNo(),
				reportBO.getDetectionTaskId(), reportBO.getOrgName(),
				reportBO.getPageSize(), reportBO.getOffset(), request.getSort(),
				OpponentDetectionOrgStatusEnum.COMPLETE_ABNORMAL.getType(),
				reportBO.getStartDate(), reportBO.getEndDate());

		if(CollectionUtils.isEmpty(list)) {
			response.setReportResponses(Arrays.asList());
			return response;
		}
		List<String> entityIdList = list.stream()
				.map(OpponentDetectionOrgDO::getOpponentEntityId)
				.distinct()
				.collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(entityIdList)) {
			Map<String, OpponentEntityDO> entityIdDataMap = Optional.ofNullable(opponentEntityDAO.getByUuids(entityIdList, null)).orElse(new ArrayList<>())
					.stream()
					.collect(Collectors.toMap(OpponentEntityDO::getUuid, Function.identity()));
			if (MapUtils.isNotEmpty(entityIdDataMap)) {
				list.forEach(elm -> {
					if (StringUtils.isBlank(elm.getOpponentEntityId())) {
						return;
					}
					OpponentEntityDO entity = entityIdDataMap.get(elm.getOpponentEntityId());
					if (null == entity) {
						return;
					}
					elm.setOrgName(entity.getEntityName());
					elm.setBlackLevel(entity.getRiskLevel());
					elm.setEntityOid(entity.getEntityOid());
					elm.setSocialCreditCode(entity.getSocialCreditCode());
				});
			}
		}

		/**获取企业下的问题*/
		List<String> orgIds = list.stream().map(OpponentDetectionOrgDO::getDetectionOrgId).collect(Collectors.toList());
		List<OpponentDetectionProblemInfoDO> problemInfoDOS = problemInfoDAO.getList(userAccountTenant.getAccountGid(),
				orgIds);
		List<OpponentDetectionProblemInfoBO> problemInfoBOS = Lists.newArrayList();
		problemInfoDOS.forEach(x ->{
			OpponentDetectionProblemInfoBO problemInfoBO = new OpponentDetectionProblemInfoBO();
			BeanUtils.copyProperties(x,problemInfoBO);
			problemInfoBOS.add(problemInfoBO);
		});

		/**分组转为map*/
		Map<String, List<OpponentDetectionProblemInfoBO>> map = problemInfoBOS.stream()
				.collect(Collectors.groupingBy(x ->x.getDetectionOrgId()));

		map.forEach(new BiConsumer<String, List<OpponentDetectionProblemInfoBO>>() {
			@Override
			public void accept(String s, List<OpponentDetectionProblemInfoBO> opponentDetectionProblemInfoBOS) {
				opponentDetectionProblemInfoBOS.sort(new Comparator<OpponentDetectionProblemInfoBO>() {
					@Override
					public int compare(OpponentDetectionProblemInfoBO o1, OpponentDetectionProblemInfoBO o2) {
						if(o1.getRiskLevel()==null||o2.getRiskLevel()==null) return o2.getRiskLevel()==null? -1 : o1.getRiskLevel()==null? 1: -1;
						return o2.getRiskLevel()-o1.getRiskLevel();
					}
				});
			}
		});

		List<OpponentDetectioTaskReportResponse> reportList = list.stream().map(x ->{
			OpponentDetectioTaskReportResponse reportResponse =  OpponentDetectioTaskReportResponse
					.builder()
					.blackLevel(x.getBlackLevel())
					.detectionTime(x.getCreateTime())
					.orgName(x.getOrgName())
					.entityOid(x.getEntityOid())
					.socialCreditCode(x.getSocialCreditCode())
					.organizationId(x.getOpponentEntityId())
					.problemInfoDOS(map.get(x.getDetectionOrgId()) != null ? map.get(x.getDetectionOrgId()) : new ArrayList<OpponentDetectionProblemInfoBO>() )
					.build();
			return reportResponse;
		}).collect(Collectors.toList());
		response.setReportResponses(reportList);
		return response;
	}

	@Override
	public OpponentDetectionTaskReportExportResponse exportTaskReports(UserAccountDetail tenantAccount,
																	   UserAccount operatorAccount,
																	   String taskId,
																	   String orgName,
																	   List<Integer> riskLevels,
																	   List<Integer> problemLevels,
																	   Boolean genAsyncTask,
																	   Long startDate,
																	   Long endDate) throws Exception
	{
		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantAccount.getAccountOid())){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT);
		}

		OpponentDetectionReportRequest request = new OpponentDetectionReportRequest();
		request.setDetectionTaskId(taskId);
		request.setOrgName(orgName);
		request.setRiskLevel(riskLevels);
		request.setProblemLevel(problemLevels);
		request.setPageSize(100);
		request.setStartDate(startDate);
		request.setEndDate(endDate);
		request.setPageNum(1);

		// 计算分页参数
		int offset = (request.getPageNum() - 1) * request.getPageSize();

		/**构造查询条件*/
		OpponentDetectionReportBO reportBO = converListParam(request,offset);

		/**统计总数*/
		long count = orgDAO.countList(tenantAccount.getAccountGid(), reportBO.getProblemNo(), reportBO.getRiskNo(),
				reportBO.getDetectionTaskId(), reportBO.getOrgName(), OpponentDetectionOrgStatusEnum.COMPLETE_ABNORMAL.getType(),
				startDate==null?null:new Date(startDate), endDate==null?null:new Date(endDate));
		if (count == 0) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TASK_PROBLEM_NONE);
		}else if (count> systemConfig.getOpponentMaxExportSize()) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TASK_PROBLEM_TOO_MANY);
		}

		String exportAsyncTaskId = null;
		if (genAsyncTask!=null && genAsyncTask && count>opponentDetectionProblemExportAsyncThreshold) {
			exportAsyncTaskId = UUIDUtil.genUUID();
			addTask(operatorAccount, count, tenantAccount, exportAsyncTaskId);

			OpponentDetectionExportMsg exportMsg = new OpponentDetectionExportMsg(exportAsyncTaskId,
													tenantAccount.getAccountOid(),taskId, orgName, riskLevels, problemLevels,
													startDate, endDate);
			opponentMqProducer.sendMessage(JsonUtils.obj2json(exportMsg), OpponentBusinessTagEnum.DETECTION_EXPORT.getType());
			OpponentDetectionTaskReportExportResponse response = new OpponentDetectionTaskReportExportResponse();
			response.setOperationType(2);
			return response;
		}

		return doExportDetectionTasks(tenantAccount, taskId, request, reportBO, exportAsyncTaskId);
	}

	private OpponentDetectionTaskReportExportResponse doExportDetectionTasks(UserAccountDetail tenantAccount,
																			 String taskId, OpponentDetectionReportRequest request,
																			 OpponentDetectionReportBO reportBO, String exportAsyncTaskId)
																throws Exception
	{
		int offset;
		int pageNum = 1;
		String fileKey = null;
		boolean success = false;
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
		String filePath = "/tmp/相对方检测结果_" + tenantAccount.getAccountOid() + "_" + simpleDateFormat.format(new Date()) + ".xlsx";
		File file = new File(filePath);
		FileOutputStream fileOutputStream = new FileOutputStream(filePath);
		ExcelWriter writer = EasyExcelFactory.getWriter(fileOutputStream);
		Sheet sheet = new Sheet(1, 1);
		sheet.setSheetName("sheet");
		sheet.setAutoWidth(Boolean.FALSE);
		Map<Integer, Integer> columnWidthMap = new HashMap<>();
		columnWidthMap.put(0,10000);
		columnWidthMap.put(1,20000);
		columnWidthMap.put(2,2000);
		columnWidthMap.put(3,20000);
		sheet.setColumnWidthMap(columnWidthMap);
		sheet.setHead(JSON.parseObject(JSON.toJSONString(detectionProblemHeader), new TypeReference<List<List<String>>>() {}));

		try{
			OpponentDetectioTaskReportListResponse currentShot = getReport(request, tenantAccount, reportBO);
			int rowIndex = 1;
			long doneExportOrgCnt = 0;
			Map<Integer,Integer> mergeBegEndMap = Maps.newHashMap();

			while (currentShot!=null && CollectionUtils.isNotEmpty(currentShot.getReportResponses())){
				List<List<Object>> fieldListObject = new ArrayList<>();

				for(OpponentDetectioTaskReportResponse orgReport : currentShot.getReportResponses()) {
					String rowOrgName = orgReport.getOrgName();
					if(CollectionUtils.isNotEmpty(orgReport.getProblemInfoDOS()) && orgReport.getProblemInfoDOS().size()>1) {
						mergeBegEndMap.put(rowIndex, rowIndex+orgReport.getProblemInfoDOS().size()-1);
					}
					for(OpponentDetectionProblemInfoBO problem : orgReport.getProblemInfoDOS()) {
						rowIndex++;
						List<Object> rowObjectList = Lists.newArrayList();
						rowObjectList.add(rowOrgName);
						rowObjectList.add(problem.getProblemDesc());
						rowObjectList.add(OpponentDetectionProblemEnum.getEnumByProblemNo(problem.getProblemNo()).getRiskLevelDesc());
						rowObjectList.add(problem.getSuggestDesc());
						fieldListObject.add(rowObjectList);
					}
					doneExportOrgCnt++;
				}
				writer.write1(fieldListObject, sheet);
				pageNum++;
				request.setPageNum(pageNum);
				offset = (request.getPageNum() - 1) * request.getPageSize();
				/**构造查询条件*/
				reportBO.setOffset(offset);
				currentShot = getReport(request,tenantAccount,reportBO);

				if(null != exportAsyncTaskId) {
					updateTaskProcess(exportAsyncTaskId,doneExportOrgCnt);
				}
			}
			for(Map.Entry<Integer,Integer> entry : mergeBegEndMap.entrySet()) {
				writer.merge(entry.getKey(),entry.getValue(),0,0);
			}
			writer.finish();
			fileOutputStream.close();
			fileKey = fileSystemClient.uploadFile(FileUtils.readFileToByteArray(file), filePath.replace("/tmp/", ""));
			success = true;
		}catch (Exception e){
			log.warn("fail to export opponent problem reports taskId:{}", taskId, e);
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.SERVICE_BIZ_ERROR);
		}finally{
			OpponentDetectionTaskReportExportResponse response = new OpponentDetectionTaskReportExportResponse();

			if(null != exportAsyncTaskId) {
				SaasTaskUpdateInput saasInput = new SaasTaskUpdateInput();
				saasInput.setBizId(exportAsyncTaskId);
				saasInput.setType(TaskTypeEnum.EXPORT_OPPONENT_DETECTION.getType());
				saasInput.setStatus(success ? TaskStatusEnum.FINISH.getType() : TaskStatusEnum.FAILED.getType());
				if (success) {
					BatchExportKeyModel result = new BatchExportKeyModel();
					result.setExportFileKey(fileKey);
					saasInput.setResult(JsonUtils.obj2json(result));
				} else {
					saasInput.setReason("导出相对方检测结果失败");
				}
				saasCommonClient.updateTask(saasInput);
				response.setOperationType(2);
			}else {
				String downloadUrl = fileSystemClient.getDownloadUrl(fileKey);
				response.setOperationType(1);
				response.setOperationUrl(downloadUrl);
			}

			return response;
		}
	}

	@Override
	public void asyncExportTaskReports(OpponentDetectionExportMsg exportMsg) throws Exception{
		OpponentDetectionReportRequest request = new OpponentDetectionReportRequest();
		request.setDetectionTaskId(exportMsg.getTaskId());
		request.setOrgName(exportMsg.getOrgName());
		request.setRiskLevel(exportMsg.getRiskLevels());
		request.setProblemLevel(exportMsg.getProblemLevels());
		request.setStartDate(exportMsg.getStartDate());
		request.setEndDate(exportMsg.getEndDate());
		request.setPageSize(100);
		request.setPageNum(1);

		// 计算分页参数
		int offset = (request.getPageNum() - 1) * request.getPageSize();

		/**构造查询条件*/
		OpponentDetectionReportBO reportBO = converListParam(request,offset);

		UserAccountDetail tenantAccount = userCenterService.getUserAccountDetailByOid(exportMsg.getTenantOid());

		doExportDetectionTasks(tenantAccount, exportMsg.getTaskId(), request, reportBO, exportMsg.getUniCode());
	}

	private void updateTaskProcess(String taskBizId, Long done) {
		/** 更新任务状态 */
		if (com.timevale.mandarin.base.util.StringUtils.isNotBlank(taskBizId)) {
			try {
				SaasTaskUpdateInput saasInput = new SaasTaskUpdateInput();
				saasInput.setBizId(taskBizId);
				saasInput.setType(TaskTypeEnum.EXPORT_OPPONENT_DETECTION.getType());
				saasInput.setDone(done);
				saasCommonClient.updateTask(saasInput);
			} catch (Exception e) {
				log.warn("updateOpponentTask failed!", e);
			}
		}
	}

	private void addTask(UserAccount operator, long total, UserAccountDetail tenantAccount, String uniCode) {
		SaasTaskAddInput taskAddInput = new SaasTaskAddInput();
		List<TaskAddBean> taskAddBeans = new ArrayList<>();
		TaskAddBean taskAddBean = new TaskAddBean();
		String taskName = tenantAccount.getAccountName() + "-";
		taskAddBean.setType(TaskTypeEnum.EXPORT_OPPONENT_DETECTION.getType());
		taskAddBean.setName(taskName+TaskTypeEnum.EXPORT_OPPONENT_DETECTION.getDesc());
		taskAddBean.setBizId(uniCode);
		TaskBizInfo taskBizInfo = new TaskBizInfo();
		taskBizInfo.setOrganizationId(tenantAccount.getAccountOid());
		taskAddBean.setBizInfo(taskBizInfo);
		taskAddBean.setTotal(total);
		taskAddBean.setAccountOid(operator.getAccountOid());
		taskAddBean.setAccountGid(operator.getAccountGid());
		taskAddBeans.add(taskAddBean);

		taskAddInput.setTasks(taskAddBeans);
		saasCommonClient.addTasks(taskAddInput);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void stopTask(String tenantOid, String detectionTaskId) {
		// 租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		//停止任务
		OpponentDetectionTaskDO taskDO = taskDAO.getTaskByTaskId(userAccountTenant.getAccountGid(), detectionTaskId);

		if(taskDO == null || taskDO.getTaskType() == OpponentDetectionTaskTypeEnum.SINGLE_DETECTION.getType()){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TASK_NOT_EXIST.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_TASK_NOT_EXIST.getMessage());
		}
		taskDO.setTaskStatus(OpponentDetectionTaskStatusEnum.STOP.getType());
		taskDAO.update(taskDO);
	}

	@Override
	public OpponentEnterpriseInfoResponse getEnterpriseData(String orgName, String tenantOid, String socialCreditNo) {
		/** 校验会员版本是否符合 */
		if(!detectionAdapter.checkDetectionFunction(tenantOid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getCode(),
					BizContractManagerResultCodeEnum.OPPONENT_DETECTION_NO_SUPPORT.getMessage());
		}
		OpponentEnterpriseInfoResponse infoResponse = new OpponentEnterpriseInfoResponse();
		OpponentEnterpriseInformationBO infoResult = enterpriseInformationService.getEnterpriseInformation(StringUtils.isNotBlank(socialCreditNo)?socialCreditNo:orgName);
		if(null != infoResult && infoResult.isSuccess() && infoResult.getDetail() != null){
			BeanUtils.copyProperties(infoResult.getDetail(), infoResponse);
		}

		return infoResponse;
	}

	/**
	 * 转换查询参数
	 * @param request
	 * @param offset
	 * @return
	 */
	public OpponentDetectionReportBO converListParam(OpponentDetectionReportRequest request, int offset){
		OpponentDetectionReportBO reportBO = new OpponentDetectionReportBO();
		List<Integer> problemNos = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(request.getProblemLevel())){
			for(Integer x :request.getProblemLevel()){
				problemNos.add ((int) Math.pow(2, x-1));
			}
		}

		List<Integer> riskNos = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(request.getRiskLevel())){
			for (Integer x :  request.getRiskLevel()){
				riskNos.add((int) Math.pow(2,(x/10)-1));
			}
		}
		if(request.getStartDate() != null){
			reportBO.setStartDate(new Date(request.getStartDate()));
		}
		if(request.getEndDate() != null){
			reportBO.setEndDate(new Date(request.getEndDate()));
		}
		reportBO.setDetectionTaskId(request.getDetectionTaskId());
		reportBO.setRiskNo(riskNos);
		reportBO.setProblemNo(problemNos);
		reportBO.setOrgName(StringUtils.isNotBlank(request.getOrgName()) ? request.getOrgName() : null);
		reportBO.setOffset(offset);
		reportBO.setPageSize(request.getPageSize());
		return reportBO;
	}
}

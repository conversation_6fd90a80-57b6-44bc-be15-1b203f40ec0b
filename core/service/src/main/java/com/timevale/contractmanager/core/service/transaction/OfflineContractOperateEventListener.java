package com.timevale.contractmanager.core.service.transaction;

import com.alibaba.fastjson.JSONObject;
import com.timevale.contractmanager.core.service.mq.model.offlinecontract.OfflineContractExtractInfoMsgEntity;
import com.timevale.contractmanager.core.service.mq.model.offlinecontract.OfflineContractGenerateProcessMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.offlinecontract.OfflineContractExtractProcessProducer;
import com.timevale.contractmanager.core.service.mq.producer.offlinecontract.OfflineContractGenerateProcessProducer;
import com.timevale.contractmanager.core.service.offlinecontract.OfflineContractService;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Collection;

@Slf4j
@Component
public class OfflineContractOperateEventListener {

    @Autowired OfflineContractService offlineContractService;
    @Autowired OfflineContractGenerateProcessProducer generateProcessProducer;
    @Autowired OfflineContractExtractProcessProducer extractProcessProducer;

    @TransactionalEventListener(fallbackExecution = true, phase = TransactionPhase.AFTER_COMMIT)
    public void handleOfflineContractOperateEvent(OfflineContractOperateEvent event) {
        String operateType = event.getOperateType();
        if (StringUtils.isBlank(operateType)) {
            return;
        }
        log.info("offline contract operate, event: {}", JsonUtils.obj2json(event));
        if (OfflineContractOperateType.GENERATE_PROCESS.getType().equals(operateType)) {
            sendGenerateProcess(event);
            return;
        }
        if (OfflineContractOperateType.EXTRACT_PROCESS_INFO.getType().equals(operateType)) {
            sendExtractProcess(event);
            return;
        }
        if (OfflineContractOperateType.UPDATE_RECORD_STATUS.getType().equals(operateType)) {
            updateRecordStatus(event);
            return;
        }
    }

    /**
     * 发送线下合同生成流程消息
     *
     * @param event
     */
    private void sendGenerateProcess(OfflineContractOperateEvent event) {
        Collection<String> recordProcessIds = event.getRecordProcessIds();
        if (CollectionUtils.isEmpty(recordProcessIds)) {
            return;
        }
        for (String recordProcessId : recordProcessIds) {
            OfflineContractGenerateProcessMsgEntity msg =
                    new OfflineContractGenerateProcessMsgEntity();
            msg.setRecordProcessId(recordProcessId);
            msg.setMasterProcessId(event.getMasterProcessId());
            generateProcessProducer.sendMessage(JSONObject.toJSONString(msg));
        }
    }

    /**
     * 发送线下合同提取合同信息消息
     *
     * @param event
     */
    private void sendExtractProcess(OfflineContractOperateEvent event) {
        Collection<String> recordProcessIds = event.getRecordProcessIds();
        if (CollectionUtils.isEmpty(recordProcessIds)) {
            return;
        }
        for (String recordProcessId : recordProcessIds) {
            OfflineContractExtractInfoMsgEntity msg = new OfflineContractExtractInfoMsgEntity();
            msg.setRecordProcessId(recordProcessId);
            extractProcessProducer.sendMessage(JSONObject.toJSONString(msg));
        }
    }

    /**
     * 更新导入记录状态
     *
     * @param event
     */
    private void updateRecordStatus(OfflineContractOperateEvent event) {
        String recordId = event.getRecordId();
        if (StringUtils.isBlank(recordId)) {
            return;
        }
        offlineContractService.calculateAndUpdateRecordStatus(recordId);
    }
}

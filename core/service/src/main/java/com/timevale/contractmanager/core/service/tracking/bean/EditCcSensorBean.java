package com.timevale.contractmanager.core.service.tracking.bean;

import com.timevale.contractmanager.core.model.dto.request.UpdateCcRequest;
import com.timevale.contractmanager.core.model.enums.SensorEnum;
import com.timevale.contractmanager.core.model.enums.SensorEventEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * 修改抄送人埋点模版
 * <AUTHOR>
 *
 * @date 2022/2/22
 */
@Data
public class EditCcSensorBean extends SensorBaseBean {
    private UpdateCcRequest request;

    @Override
    public SensorEventEnum sensorKey() {
        return SensorEventEnum.EDIT_CC_SEVER;
    }

    @Override
    public List<SensorEnum> sensorTemplate() {
        return Arrays.asList(
                SensorEnum.AUTHENTICATION_RESULT,
                SensorEnum.AUTHENTICATION_FAILURE_REASON,
                SensorEnum.RETURN_TIME,
                SensorEnum.PROCESSING_RESULT,
                SensorEnum.NUMBER_OF_PEOPLE);
    }

    @Override
    public void initData() {

        if (CollectionUtils.isNotEmpty(request.getCcs())) {
            getTempData().put(SensorEnum.NUMBER_OF_PEOPLE.getKey(), request.getCcs().size());
        }
    }

    public void setRequest(UpdateCcRequest request) {
        this.request = request;
    }

    @Override
    public void setRequest(Object request) {
        this.request = (UpdateCcRequest) request;
    }
}

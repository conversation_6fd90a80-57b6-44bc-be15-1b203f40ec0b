package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @Author:jianyang
 * @since 2022-02-17 10:56
 */
@Data
public class ProcessListSensorBean extends BaseAttributeSensorBean {
	private String listName;
	private String chosenProcessStatus;
	private Boolean isInitiateTime;
	private Boolean isFinishTime;
	private String processType;
	private Long numberOfProcess;
	private Long returnTime;


	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData = super.sensorData();
		sensorData.put(LIST_NAME,sensorString(listName));
		sensorData.put(CHOSEN_PROCESS_STATUS,sensorString(chosenProcessStatus));
		sensorData.put(IS_INITIATE_TIME,sensorString(isInitiateTime));
		sensorData.put(IS_FINISH_TIME,sensorString(isFinishTime));
		sensorData.put(NUMBER_OF_PROCESS,sensorString(numberOfProcess));
		sensorData.put(RETURN_TIME,sensorString(returnTime));
		sensorData.put(PROCESS_LIST_TYPE,sensorString(processType));
		return sensorData;
	}
}

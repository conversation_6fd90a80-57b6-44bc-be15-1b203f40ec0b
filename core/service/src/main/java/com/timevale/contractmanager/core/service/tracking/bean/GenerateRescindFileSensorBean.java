package com.timevale.contractmanager.core.service.tracking.bean;

import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.model.enums.SensorEnum.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * 生成解约协议书埋点
 * <AUTHOR>
 *
 * @date 2022/4/12
 */
@Data
public class GenerateRescindFileSensorBean extends BaseAttributeSensorBean {

    private String reason;
    private Long numberOfOriginalFile;

    public Map<String, Object> sensorData() {
        Map<String, Object> sensorData = super.sensorData();
        sensorData.put(REASON.getKey(), sensorString(reason));
        sensorData.put(NUMBER_OF_ORIGINAL_FILE.getKey(), sensorString(numberOfOriginalFile));
        return sensorData;
    }
}

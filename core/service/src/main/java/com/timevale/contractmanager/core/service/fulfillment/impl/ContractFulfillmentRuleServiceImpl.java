package com.timevale.contractmanager.core.service.fulfillment.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.ProcessPreferenceDO;
import com.timevale.contractmanager.common.dal.bean.fulfillment.ContractFulfillmentRuleDO;
import com.timevale.contractmanager.common.dal.dao.ProcessPreferenceDAO;
import com.timevale.contractmanager.common.dal.dao.fulfillment.ContractFulfillmentRuleDAO;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentCreateWayEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentMiddleTypeEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentNoticeEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentNoticeTimeKeyEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentRuleStatusEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentScopeEnum;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentTypeEnum;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentNoticeQueryScriptModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentNoticeRuleModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleListModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleQueryListModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleSaveModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleSyncModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleUpdateModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentShardingRuleListModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentShardingRuleQueryListModel;
import com.timevale.contractmanager.common.service.model.rule.RuleConditionModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleDetailResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleListResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleSaveResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentShardingRuleListResult;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.fulfillment.ContractFulfillmentRuleService;
import com.timevale.contractmanager.core.service.fulfillment.builder.ContractFulfillmentRuleQueryScriptBuilder;
import com.timevale.contractmanager.core.service.fulfillment.converter.ContractFulfillmentRuleConverter;
import com.timevale.contractmanager.core.service.mq.model.ContractFulfillmentRuleChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.ContractFulfillmentRuleChangeProducer;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.rule.RuleConditionService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.notificationmanager.service.enums.NotifyChannelTypeEnum;
import com.timevale.saas.common.manage.common.service.model.output.AccountVipListQueryOutput;
import com.timevale.saas.common.manage.common.service.model.output.AccountVipQueryOutput;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.constant.FunctionCodeConstants.CONTRACT_FULFILLMENT;

/**
 * ContractFulfillmentRuleServiceImpl
 *
 * <AUTHOR>
 * @since 2023/10/11 5:09 下午
 */
@Slf4j
@Service
public class ContractFulfillmentRuleServiceImpl implements ContractFulfillmentRuleService {

    @Autowired
    private ContractFulfillmentRuleDAO contractFulfillmentRuleDAO;

    @Autowired
    private RuleConditionService ruleConditionService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private MapperFactory mapperFactory;

    @Autowired
    private SaasCommonClient saasCommonClient;

    @Autowired
    private ProcessPreferenceDAO preferenceDAO;

    @Autowired
    private ContractFulfillmentRuleChangeProducer contractFulfillmentRuleChangeProducer;

    @Autowired
    private ContractFulfillmentRuleQueryScriptBuilder contractFulfillmentRuleQueryScriptBuilder;

    @Transactional
    @Override
    public ContractFulfillmentRuleSaveResult save(ContractFulfillmentRuleSaveModel model) {

        if(FulfillmentScopeEnum.FORCE.getType().equals(model.getScopeType())){
            AssertX.isTrue(CollectionUtils.isNotEmpty(model.getScopeConditions()), "条件不能为空");
        }
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(model.getTenantId());

        String ruleId = UUIDUtil.genUUID();
        ContractFulfillmentRuleDO saveRule = mapperFactory.getMapperFacade().map(model, ContractFulfillmentRuleDO.class);
        saveRule.setTenantOid(userAccountTenant.getAccountOid());
        saveRule.setTenantGid(userAccountTenant.getAccountGid());
        saveRule.setRuleId(ruleId);
        saveRule.setModifiedByOid(model.getAccountId());
        saveRule.setNoticeRule(JSON.toJSONString(model.getNoticeRule()));
        saveRule.setNoticeChannel(JSON.toJSONString(model.getNoticeChannels()));
        saveRule.setCreateByOid(model.getAccountId());
        saveRule.setModifiedByOid(model.getAccountId());
        if(StringUtils.isEmpty(saveRule.getTypeName())){
            saveRule.setTypeName(FulfillmentTypeEnum.getTypeEnum(saveRule.getType()) != null ? FulfillmentTypeEnum.getTypeEnum(saveRule.getType()).getDesc() : "");
        }
        contractFulfillmentRuleDAO.insert(saveRule);

        if(FulfillmentScopeEnum.FORCE.getType().equals(model.getScopeType())){
            ruleConditionService.saveRuleCondition(userAccountTenant, ruleId, 3, model.getScopeConditions(), true);
        }
        // 发送履约规则变更消息
        sendRuleChangeMessage(ruleId, userAccountTenant, true);

        return new ContractFulfillmentRuleSaveResult(ruleId);
    }

    @Transactional
    @Override
    public void update(ContractFulfillmentRuleUpdateModel model) {
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(model.getTenantId());

        ContractFulfillmentRuleDO ruleDetail = contractFulfillmentRuleDAO.getByRuleId(userAccountTenant.getAccountGid(), model.getRuleId());
        AssertX.isTrue(ruleDetail != null, "履约规则不存在");

        ContractFulfillmentRuleDO updateRule = mapperFactory.getMapperFacade().map(model, ContractFulfillmentRuleDO.class);
        updateRule.setModifiedByOid(model.getAccountId());
        updateRule.setNoticeRule(JSON.toJSONString(model.getNoticeRule()));
        updateRule.setNoticeChannel(JSON.toJSONString(model.getNoticeChannels()));
        if(StringUtils.isEmpty(updateRule.getTypeName())){
            updateRule.setTypeName(FulfillmentTypeEnum.getTypeEnum(updateRule.getType()) != null ? FulfillmentTypeEnum.getTypeEnum(updateRule.getType()).getDesc() : "");
        }
        contractFulfillmentRuleDAO.update(updateRule);
        if(FulfillmentScopeEnum.FORCE.getType().equals(model.getScopeType())){
            ruleConditionService.updateRuleCondition(userAccountTenant, model.getRuleId(), 3, model.getScopeConditions(), true);
        }
        // 发送履约规则变更消息
        sendRuleChangeMessage(model.getRuleId(), userAccountTenant, false);
    }

    /**
     * 发送履约规则变更消息
     * @param ruleId
     * @param tenant
     */
    private void sendRuleChangeMessage(String ruleId, UserAccount tenant, boolean newRule) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                ContractFulfillmentRuleChangeMsgEntity msgEntity = new ContractFulfillmentRuleChangeMsgEntity();
                msgEntity.setRuleId(ruleId);
                msgEntity.setNewRule(newRule);
                msgEntity.setTenantOid(tenant.getAccountOid());
                msgEntity.setTenantGid(tenant.getAccountGid());
                contractFulfillmentRuleChangeProducer.sendMessage(msgEntity);
            }
        });
    }

    @Override
    public void delete(String ruleId, String tenantId, String accountId) {
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
        ContractFulfillmentRuleDO ruleDetail = contractFulfillmentRuleDAO.getByRuleId(userAccountTenant.getAccountGid(), ruleId);
        AssertX.isTrue(ruleDetail != null, "履约规则不存在");
        contractFulfillmentRuleDAO.delete(ruleId, accountId);
    }

    @Override
    public void updateStatus(String ruleId, String status, String tenantId, String accountId) {
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
        ContractFulfillmentRuleDO ruleDetail = contractFulfillmentRuleDAO.getByRuleId(userAccountTenant.getAccountGid(), ruleId);
        AssertX.isTrue(ruleDetail != null, "履约规则不存在");
        AssertX.isTrue(FulfillmentRuleStatusEnum.getStatusEnum(status) != null, "状态不符合");
        contractFulfillmentRuleDAO.updateStatus(ruleId, status, accountId);
    }

    @Override
    public ContractFulfillmentRuleDetailResult detail(String ruleId, String tenantId) {
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
        ContractFulfillmentRuleDO ruleDetail = contractFulfillmentRuleDAO.getByRuleId(userAccountTenant.getAccountGid(), ruleId);

        List<RuleConditionModel> models = ruleConditionService.dbListByRuleId(ruleId);
        return ContractFulfillmentRuleConverter.convertRuleDetailResult(ruleDetail, models);
    }

    @Override
    public ContractFulfillmentRuleListResult pageList(ContractFulfillmentRuleQueryListModel model) {
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(model.getTenantId());
        int offset = (model.getPageNum() - 1) * model.getPageSize();
        long count = contractFulfillmentRuleDAO.count(userAccountTenant.getAccountGid(), model.getStatus(), model.getTypeName(), model.getName());
        if(count == 0){
            return new ContractFulfillmentRuleListResult(0L, new ArrayList<>());
        }
        List<ContractFulfillmentRuleDO> ruleDOList = contractFulfillmentRuleDAO.list(userAccountTenant.getAccountGid(), model.getStatus(), model.getTypeName(), model.getName(), offset, model.getPageSize());
        if(CollectionUtils.isEmpty(ruleDOList)){
            return new ContractFulfillmentRuleListResult(0L, new ArrayList<>());
        }
        List<String> ruleIds = ruleDOList.stream().filter(t->t.getScopeType().equals(FulfillmentScopeEnum.FORCE.getType())).map(ContractFulfillmentRuleDO::getRuleId).collect(Collectors.toList());
        List<RuleConditionModel> ruleConditionModels = ruleConditionService.dbListByRuleIds(ruleIds);
        List<ContractFulfillmentRuleListModel> ruleModelList = ContractFulfillmentRuleConverter.convertRuleModelList(ruleDOList, ruleConditionModels);
        return new ContractFulfillmentRuleListResult(count, ruleModelList);
    }

    @Override
    public List<String> queryCustomTypeNameList(String tenantId) {
        UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
        return contractFulfillmentRuleDAO.typeList(userAccountTenant.getAccountGid());
    }

    @Override
    public ContractFulfillmentShardingRuleListResult shardingList(ContractFulfillmentShardingRuleQueryListModel model) {

        int offset = (model.getPageNum() - 1) * model.getPageSize();
        long count = contractFulfillmentRuleDAO.shardingCount(model.getStartId(), model.getEndId(), model.getStatus());
        if(count == 0){
            return new ContractFulfillmentShardingRuleListResult(0L, new ArrayList<>());
        }
        List<ContractFulfillmentRuleDO> ruleDOList = contractFulfillmentRuleDAO.shardingList(model.getStartId(), model.getEndId(), model.getStatus(), offset, model.getPageSize());
        if(CollectionUtils.isEmpty(ruleDOList)){
            return new ContractFulfillmentShardingRuleListResult(0L, new ArrayList<>());
        }
        List<ContractFulfillmentShardingRuleListModel> ruleModelList = ContractFulfillmentRuleConverter.convertShardingRuleModelList(ruleDOList);
        return new ContractFulfillmentShardingRuleListResult(count, ruleModelList);
    }

    @Override
    public void syncHistoryNoticeRule(ContractFulfillmentRuleSyncModel model) {
        Set<String> gidSet = new HashSet<>();

        if (StringUtils.isNotEmpty(model.getType()) && model.getType().equals("all")) {
            int pageSize = 500;
            int offset = 0;

            while (true) {
                AccountVipListQueryOutput listOutput =
                        saasCommonClient.scrollQueryTenantByFuncCode(CONTRACT_FULFILLMENT, offset, pageSize);
                if (ListUtils.isEmpty(listOutput.getAccountVipQueryOutputs())) {
                    break;
                }
                for (AccountVipQueryOutput output : listOutput.getAccountVipQueryOutputs()) {
                    gidSet.add(output.getGid());
                }
                offset += pageSize;
            }
        } else {

            AssertX.isTrue(CollectionUtils.isNotEmpty(model.getOidList()), "oid不能为空");

            for (String oid : model.getOidList()) {

                boolean functionValid = saasCommonClient.checkFunctionValid(oid, CONTRACT_FULFILLMENT, false);
                if (!functionValid) {
                    continue;
                }
                UserAccount userAccount = userCenterService.getUserAccountBaseByOid(oid);
                gidSet.add(userAccount.getAccountGid());
            }
        }

        for (String gid : gidSet) {
            initSystemRule(gid);
        }
    }

    private void initSystemRule(String gid) {
        if(StringUtils.isEmpty(gid)){
            return;
        }

        ContractFulfillmentRuleDO ruleDO = contractFulfillmentRuleDAO.getSystemRule(gid);
        if (ruleDO != null) {
            return;
        }

        List<ProcessPreferenceDO> preferences = preferenceDAO.listByGidAndType(Lists.newArrayList(gid), Lists.newArrayList(ProcessPreferenceEnum.CONTRACT_EXPIRE_NOTICE_ADVANCE.getKey(),
                ProcessPreferenceEnum.CONTRACT_EXPIRE_NOTICE_SWITCH.getKey()));
        if (CollectionUtils.isEmpty(preferences)) {
            return;
        }
        String value = null;
        boolean switchNotice = false;
        for (ProcessPreferenceDO processPreferenceDO : preferences) {
            if (processPreferenceDO.getAccountType().equals(0)) {
                continue;
            }
            if (processPreferenceDO.getPreferenceType().equals(ProcessPreferenceEnum.CONTRACT_EXPIRE_NOTICE_ADVANCE.getKey())) {
                value = processPreferenceDO.getPreferenceValue();
            }
            if (processPreferenceDO.getPreferenceType().equals(ProcessPreferenceEnum.CONTRACT_EXPIRE_NOTICE_SWITCH.getKey())) {
                switchNotice = Boolean.parseBoolean(processPreferenceDO.getPreferenceValue());
            }
        }
        if (StringUtils.isEmpty(value)) {
            return;
        }
        String[] valueList = value.split(",");
        Set<String> valueSet = new HashSet<>(Arrays.asList(valueList));
        ContractFulfillmentNoticeRuleModel noticeRule = new ContractFulfillmentNoticeRuleModel();
        ContractFulfillmentNoticeRuleModel.NoticeRuleReminder noticeRuleReminder = new ContractFulfillmentNoticeRuleModel.NoticeRuleReminder();
        List<ContractFulfillmentNoticeRuleModel.NoticeRuleCondition> conditions = new ArrayList<>();
        for(String day : valueSet){
            day = Integer.parseInt(day) > 30 ? "30" : day;
            day = Integer.parseInt(day) < 1 ? "1" : day;
            ContractFulfillmentNoticeRuleModel.NoticeRuleCondition noticeRuleCondition = new ContractFulfillmentNoticeRuleModel.NoticeRuleCondition();
            noticeRuleCondition.setKey(FulfillmentNoticeTimeKeyEnum.CONTRACT_VALIDITY.getKey());
            noticeRuleCondition.setMiddleType(FulfillmentMiddleTypeEnum.BEFORE.getType());
            noticeRuleCondition.setValue(day);
            conditions.add(noticeRuleCondition);
        }

        noticeRuleReminder.setType(Lists.newArrayList(FulfillmentNoticeEnum.INITIATOR.getType(), FulfillmentNoticeEnum.SIGNER.getType(), FulfillmentNoticeEnum.CC.getType()));
        noticeRule.setReminder(noticeRuleReminder);
        noticeRule.setCondition(conditions);

        ruleDO = new ContractFulfillmentRuleDO();
        ruleDO.setTenantOid(preferences.get(0).getAccountOid());
        ruleDO.setTenantGid(gid);
        ruleDO.setName("系统默认合同到期提醒");
        ruleDO.setType(FulfillmentTypeEnum.EXPIRE.getType());
        ruleDO.setTypeName(FulfillmentTypeEnum.EXPIRE.getDesc());
        ruleDO.setScopeType(FulfillmentScopeEnum.ALL.getType());
        ruleDO.setStatus(switchNotice ? FulfillmentRuleStatusEnum.ENABLE.getStatus() : FulfillmentRuleStatusEnum.DISABLE.getStatus());
        ruleDO.setRuleId(UUIDUtil.genUUID());
        ruleDO.setNoticeRule(JSON.toJSONString(noticeRule));
        ruleDO.setNoticeChannel(JSON.toJSONString(Lists.newArrayList(NotifyChannelTypeEnum.INMAIL.name())));
        ruleDO.setCreateWay(FulfillmentCreateWayEnum.SYSTEM.getType());
        contractFulfillmentRuleDAO.insert(ruleDO);
    }

    @Override
    public void syncNoticeRule(String gid) {

        UserAccountDetail userAccountDetail = userCenterService.getUserAccountDetailByGid(gid);
        if(userAccountDetail == null){
            return;
        }

        boolean functionValid = saasCommonClient.checkFunctionValid(userAccountDetail.getAccountOid(), CONTRACT_FULFILLMENT, false);
        if (!functionValid) {
            return;
        }
        initSystemRule(gid);
    }

    @Override
    public void generateAndSaveRuleQueryScript(String ruleId, String tenantGid) {
        ContractFulfillmentRuleDO ruleDetail = contractFulfillmentRuleDAO.getByRuleId(tenantGid, ruleId);
        if (null == ruleDetail) {
            log.info("skip generateAndSaveRuleQueryScript, tenantGid:{} ruleId: {} not exist", tenantGid, ruleId);
            return;
        }
        List<RuleConditionModel> models = ruleConditionService.dbListByRuleId(ruleId);
        ContractFulfillmentNoticeQueryScriptModel queryScriptModel = contractFulfillmentRuleQueryScriptBuilder.buildNoticeQueryScript(ruleDetail, models);
        if (null == queryScriptModel) {
            log.info("skip generateAndSaveRuleQueryScript, query script is null, tenantGid:{} ruleId: {}", tenantGid, ruleId);
            return;
        }
        contractFulfillmentRuleDAO.updateQueryScript(ruleId, JSON.toJSONString(queryScriptModel));
    }
}

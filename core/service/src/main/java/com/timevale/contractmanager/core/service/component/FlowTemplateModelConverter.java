package com.timevale.contractmanager.core.service.component;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.integration.client.DocServiceClient;
import com.timevale.contractmanager.core.model.bo.*;
import com.timevale.contractmanager.common.service.enums.ParticipantSubjectType;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.doccooperation.service.enums.AccessTokenTypeEnum;
import com.timevale.doccooperation.service.enums.DocTemplateFromEnum;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.doccooperation.service.model.*;
import com.timevale.docmanager.service.model.SignAreaContextExt;
import com.timevale.docmanager.service.model.StructComponent;
import com.timevale.docmanager.service.result.BaseDocSourceResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import ma.glasnost.orika.CustomMapper;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.MappingContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.constant.SystemConstant.DEFAULT_CONTRACT_NOTICE_WAYS;

/**
 * 流程模板模型转换器
 *
 * <AUTHOR>
 * @since 2020/1/14
 */
@Component
public class FlowTemplateModelConverter {

    @Autowired private MapperFactory mapperFactory;

    @Autowired private DocServiceClient docServiceClient;

    @PostConstruct
    public void init() {
        mapperFactory
                .classMap(UserBO.class, CopyTo.class)
                .field("accountOid", "accountId")
                .field("accountName", "name")
                .field("accountRealName", "realName")
                .field("subjectId", "copyToSubjectId")
                .field("subjectName", "copyToSubjectName")
                .field("subjectType", "copyToType")
                .field("subjectRealName", "orgRealName")
                .field("accountNick", "nickname")
                .byDefault()
                .register();

        mapperFactory
                .classMap(ParticipantInstanceBO.class, CooperationerInstance.class)
                .field("accountOid", "accountId")
                .field("accountNick", "nickname")
                .field("subjectRealName", "realName")
                .byDefault()
                .register();

        mapperFactory
                .classMap(FileBO.class, DocTemplate.class)
                .field("fileId", "templateId")
                .field("fileName", "templateName")
                .customize(
                        new CustomMapper<FileBO, DocTemplate>() {
                            @Override
                            public void mapBtoA(
                                    DocTemplate docTemplate,
                                    FileBO fileDetail,
                                    MappingContext context) {
                                fileDetail.setFileType(ProcessFileType.CONTRACT_FILE.getType());
                            }
                        })
                .byDefault()
                .register();

        mapperFactory
                .classMap(FileBO.class, FlowInfo.Attachment.class)
                .customize(
                        new CustomMapper<FileBO, FlowInfo.Attachment>() {
                            @Override
                            public void mapBtoA(
                                    FlowInfo.Attachment attachment,
                                    FileBO fileDetail,
                                    MappingContext context) {
                                fileDetail.setFileType(ProcessFileType.ATTACHMENT_FILE.getType());
                                fileDetail.setFrom(DocTemplateFromEnum.CONTRACT_FILE.getFrom());
                            }
                        })
                .byDefault()
                .register();

        mapperFactory
                .classMap(FileDetailBO.class, DocTemplate.class)
                .field("fileId", "templateId")
                .field("fileName", "templateName")
                .field("originalFileId", "originalTemplateId")
                .customize(
                        new CustomMapper<FileDetailBO, DocTemplate>() {
                            @Override
                            public void mapBtoA(
                                    DocTemplate docTemplate,
                                    FileDetailBO fileDetail,
                                    MappingContext context) {
                                // 来自模板文件
                                if (DocTemplateFromEnum.TEMPLATE_FILE.getFrom()
                                        == docTemplate.getFrom()) {
                                    if (!docTemplate.getTemplateName().endsWith(".pdf")) {
                                        fileDetail.setFileName(
                                                docTemplate.getTemplateName() + ".pdf");
                                    }
                                }
                            }
                        })
                .byDefault()
                .register();

        mapperFactory
                .classMap(ParticipantBO.class, Cooperationer.class)
                .field("participantId", "cooperationerId")
                .field("participantLabel", "cooperationerLabel")
                .field("instances", "cooperationerInstances")
                .customize(
                        new CustomMapper<ParticipantBO, Cooperationer>() {
                            @Override
                            public void mapAtoB(
                                    ParticipantBO participantBO,
                                    Cooperationer cooperationer,
                                    MappingContext context) {
                                Cooperationer.Ext ext = new Cooperationer.Ext();
                                if (StringUtils.isNotBlank(participantBO.getSealType())) {
                                    ext.setSealType(participantBO.getSealType());
                                }
                                if (StringUtils.isNotBlank(participantBO.getSignRequirements())) {
                                    ext.setSignRequirements(participantBO.getSignRequirements());
                                }
                                ext.setSignOrder(participantBO.getSignOrder());
                                ext.setFillOrder(participantBO.getFillOrder());
                                // 先设置是否扫码参与方
                                ext.setSharable(participantBO.isSharable());
                                // 再设置扫码上限
                                ext.setShareMax(
                                        ext.isSharable() ? participantBO.getShareMax() : null);
                                ext.setCooperationerSubjectType(
                                        participantBO.getParticipantSubjectType().toString());
                                ext.setWillTypes(participantBO.getWillTypes());
                                ext.setNeedWill(participantBO.getNeedWill());
                                // 未指定通知方式 或者 指定的通知方式不为空， 则表示需要发送通知
                                ext.setNeedNotice(null == participantBO.getNoticeTypes() || CollectionUtils.isNotEmpty(participantBO.getNoticeTypes()));
                                ext.setNoticeTypes(participantBO.getNoticeTypes());
                                ext.setSignSealType(participantBO.getSignSealType());
                                ext.setSignSeal(participantBO.getSignSeal());
                                ext.setForceReadEnd(participantBO.getForceReadEnd());
                                ext.setForceReadTime(participantBO.getForceReadTime());
                                if(CollectionUtils.isNotEmpty(participantBO.getAttachmentConfigs())){
                                    ext.setAttachmentConfigs(participantBO.getAttachmentConfigs().stream().map(p->{
                                        AttachmentConfig config = new AttachmentConfig();
                                        BeanUtils.copyProperties(p,config);
                                        return config;
                                    }).collect(Collectors.toList()));
                                }
                                // 映射签署声明
                                ext.setSignTipsTitle(participantBO.getSignTipsTitle());
                                ext.setSignTipsContent(participantBO.getSignTipsContent());
                                cooperationer.setExt(ext);
                            }

                            @Override
                            public void mapBtoA(
                                    Cooperationer cooperationer,
                                    ParticipantBO participantBO,
                                    MappingContext context) {
                                Cooperationer.Ext ext = cooperationer.getExt();
                                if (ext != null) {
                                    participantBO.setFillOrder(ext.getFillOrder());
                                    participantBO.setSignOrder(ext.getSignOrder());
                                    participantBO.setSealType(ext.getSealType());
                                    participantBO.setSignRequirements(ext.getSignRequirements());
                                    // 先设置是否扫码参与方
                                    participantBO.setSharable(ext.isSharable());
                                    // 再设置扫码上限
                                    participantBO.setShareMax(
                                            participantBO.isSharable() ? ext.getShareMax() : null);
                                    participantBO.setWillTypes(ext.getWillTypes());
                                    participantBO.setNeedWill(ext.getNeedWill());
                                    // 如果不需要发送通知， 默认设置空数组
                                    if (!ext.isNeedNotice()) {
                                        participantBO.setNoticeTypes(Lists.newArrayList());
                                    } else {
                                        // 需要发送通知， 兜底返回默认通知方式
                                        participantBO.setNoticeTypes(null != ext.getNoticeTypes() ? ext.getNoticeTypes() : IdsUtil.getIntegerIdList(DEFAULT_CONTRACT_NOTICE_WAYS));
                                    }
                                    participantBO.setParticipantSubjectType(
                                            StringUtils.isBlank(ext.getCooperationerSubjectType())
                                                    ? ParticipantSubjectType.UNDETERMINED.getType()
                                                    : Integer.parseInt(
                                                    ext.getCooperationerSubjectType()));
                                    participantBO.setSignSealType(ext.getSignSealType());
                                    participantBO.setSignSeal(ext.getSignSeal());
                                    participantBO.setForceReadEnd(ext.getForceReadEnd());
                                    participantBO.setForceReadTime(ext.getForceReadTime());
                                    if(CollectionUtils.isNotEmpty(ext.getAttachmentConfigs())){
                                        participantBO.setAttachmentConfigs(ext.getAttachmentConfigs().stream().map(p->{
                                            AttachmentConfigBO config = new AttachmentConfigBO();
                                            BeanUtils.copyProperties(p,config);
                                            return config;
                                        }).collect(Collectors.toList()));
                                    }
                                    // 映射签署声明
                                    participantBO.setSignTipsTitle(ext.getSignTipsTitle());
                                    participantBO.setSignTipsContent(ext.getSignTipsContent());
                                } else {
                                    participantBO.setParticipantSubjectType(
                                            ParticipantSubjectType.UNDETERMINED.getType());
                                }
                            }
                        })
                .byDefault()
                .register();

        mapperFactory
                .classMap(StructComponent.class, StructBO.class)
                .customize(
                        new CustomMapper<StructComponent, StructBO>() {
                            @Override
                            public void mapAtoB(
                                    StructComponent struct, StructBO structBO, MappingContext context) {
                                String ext = struct.getContext().getExt();
                                // 获取控件的扩展字段
                                if (StringUtils.isNotBlank(ext)) {
                                    SignAreaContextExt signAreaContextExt =
                                            JsonUtils.json2pojo(
                                                    struct.getContext().getExt(),
                                                    SignAreaContextExt.class);
                                    StructBO.Ext outExt = new StructBO.Ext();
                                    outExt.setQiFeng(signAreaContextExt.isQiFeng());
                                    if (outExt.isQiFeng()) {
                                        structBO.getContext()
                                                .getPos()
                                                .setPage(signAreaContextExt.getPage());
                                    }
                                    outExt.setFillLengthLimit(
                                            signAreaContextExt.getFillLengthLimit());
                                    outExt.setSignRequirements(
                                            signAreaContextExt.getSignRequirements());
                                    outExt.setSignDatePos(
                                            mapperFactory
                                                    .getMapperFacade()
                                                    .map(
                                                            signAreaContextExt.getSignDatePos(),
                                                            StructBO.Pos.class));
                                    structBO.getContext().setExt(outExt);
                                }
                            }
                        });
    }

    /**
     * 发起抄送人转流程模板抄送人
     *
     * @param ccs 发起抄送人
     * @return 流程模板抄送人
     */
    public List<CopyTo> ccToCopyTo(List<UserBO> ccs) {
        if (CollectionUtils.isNotEmpty(ccs)) {
            return mapperFactory.getMapperFacade().mapAsList(ccs, CopyTo.class);
        }
        return null;
    }

    /**
     * 流程模板抄送人转发起抄送人
     *
     * @param copyTos 流程模板抄送人
     * @return 发起抄送人
     */
    public List<UserBO> copyToToCc(List<CopyTo> copyTos) {
        if (CollectionUtils.isNotEmpty(copyTos)) {
            return mapperFactory.getMapperFacade().mapAsList(copyTos, UserBO.class);
        }
        return null;
    }

    /**
     * 发起参与方转协作方
     *
     * @param participants 参与方
     * @return 协作方
     */
    public List<Cooperationer> participantToCooperationer(List<ParticipantBO> participants) {
        if (CollectionUtils.isEmpty(participants)) {
            return null;
        }

        return participants.stream().map(this::buildCooperationer).collect(Collectors.toList());
    }

    /**
     * 协作方转发起参与方
     *
     * @param cooperationers 协作方
     * @return 参与方
     */
    public List<ParticipantBO> cooperationerToParticipant(List<Cooperationer> cooperationers) {
        if (CollectionUtils.isEmpty(cooperationers)) {
            return null;
        }

        return cooperationers.stream().map(this::buildCooperationer).collect(Collectors.toList());
    }

    /**
     * 流程模板文件转发起文件
     *
     * @param docTemplates 发起文件
     * @param canEdit 是否判断是否可以编辑
     * @return 流程模板文件
     */
    public List<FileDetailBO> docTemplateToFile(List<DocTemplate> docTemplates, boolean canEdit) {

        List<FileDetailBO> files = null;
        if (CollectionUtils.isNotEmpty(docTemplates)) {
            files = mapperFactory.getMapperFacade().mapAsList(docTemplates, FileDetailBO.class);
            // 判断是否可编辑， 如果是，填充源文件名称
            if (canEdit) {
                fillResourceFileName(files);
            }
        }
        return files;
    }

    /**
     * 填充原文件名称
     *
     * @param files
     */
    private void fillResourceFileName(List<FileDetailBO> files) {
        // 把list 转 map 方便查询
        Map<String, FileDetailBO> fileMap =
                files.stream().collect(Collectors.toMap(FileBO::getFileId, (f) -> f));
        // 过滤出普通文件的id
        List<String> docIds =
                files.stream()
                        .filter(i -> DocTemplateFromEnum.CONTRACT_FILE.getFrom() == i.getFrom())
                        .map(FileDetailBO::getFileId)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(docIds)) {
            return;
        }
        // 如果普通文件id集合不为空，查询源文件
        List<BaseDocSourceResult> docSources = docServiceClient.getDocSourceByIds(docIds);
        if (CollectionUtils.isEmpty(docSources)) {
            return;
        }
        // 源文件不为空
        for (BaseDocSourceResult docSource : docSources) {
            // 如果是word能编辑
            FileDetailBO file = fileMap.get(docSource.getDocId());
            if (file != null) {
                file.setSourceFileName(docSource.getFileName());
            }
        }
    }

    /**
     * 发起文件转流程模板文件
     *
     * @param files 发起文件
     * @return 流程模板文件
     */
    public List<DocTemplate> fileToDocTemplate(List<FileBO> files) {
        if (CollectionUtils.isEmpty(files)) {
            return null;
        }
        // 过滤掉数据源关联的
        List<FileBO> normalFiles =
                files.stream()
                        .filter(file -> StringUtils.isNotBlank(file.getFileId()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(normalFiles)) {
            return mapperFactory.getMapperFacade().mapAsList(normalFiles, DocTemplate.class);
        }
        return null;
    }

    /**
     * 发起附件转流程模板附件
     *
     * @param files 发起附件
     * @return 流程模板附件
     */
    public List<FlowInfo.Attachment> fileToAttachment(List<FileBO> files) {
        if (CollectionUtils.isEmpty(files)) {
            return null;
        }
        // 过滤掉数据源关联的
        List<FileBO> normalFiles =
                files.stream()
                        .filter(file -> StringUtils.isNotBlank(file.getFileId()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(files)) {
            return mapperFactory
                    .getMapperFacade()
                    .mapAsList(normalFiles, FlowInfo.Attachment.class);
        }
        return null;
    }

    /**
     * 流程模板附件转发起附件
     *
     * @param attachments 流程模板附件
     * @param convertClazz 要转的具体类
     * @return 发起附件
     */
    public <T extends BaseFileBO> List<T> attachementToFile(
            List<FlowInfo.Attachment> attachments, Class<T> convertClazz) {
        if (CollectionUtils.isNotEmpty(attachments)) {
            return mapperFactory.getMapperFacade().mapAsList(attachments, convertClazz);
        }
        return null;
    }

    private Cooperationer buildCooperationer(ParticipantBO participant) {
        Cooperationer cooperationer =
                mapperFactory.getMapperFacade().map(participant, Cooperationer.class);

        Cooperationer.Ext ext =
                Optional.ofNullable(cooperationer.getExt()).orElse(new Cooperationer.Ext());
        ext.setParticipantMode(
                ParticipantModeEnum.isOrSign(participant.getParticipantMode())
                        ? ParticipantModeEnum.OR_SIGN.getMode()
                        : ParticipantModeEnum.NORMAL.getMode());
        ext.setAuthWay(participant.getAuthWay());
        if (Objects.equals(participant.getAccessTokenType(), AccessTokenTypeEnum.START_APPOINT.getType()) &&
                !ParticipantModeEnum.isOrSign(participant.getParticipantMode())) {
            // 如果复制出来的模板携带指定发起时携带口令也将它保存模板中
            ext.setAccessToken(Optional.ofNullable(participant.getInstances())
                    .filter(instances -> !instances.isEmpty()).map(instances -> instances.get(0).getAccessToken())
                    .filter(StringUtils::isNotEmpty).orElse(participant.getAccessToken()));
        }else {
            ext.setAccessToken(participant.getAccessToken());
        }
        ext.setNeedWill(participant.getNeedWill());
        ext.setDynamic(participant.getDynamic());
        ext.setAssignedSubjectId(participant.getAssignedSubjectId());
        ext.setAssignedSubjectName(participant.getAssignedSubjectName());
        ext.setAccessTokenType(participant.getAccessTokenType());
        ext.setParticipantStartType(participant.getParticipantStartType());
        ext.setParticipantStartValue(participant.getParticipantStartValue());
        cooperationer.setExt(ext);

        return cooperationer;
    }

    private ParticipantBO buildCooperationer(Cooperationer cooperationer) {
        ParticipantBO participant =
                mapperFactory.getMapperFacade().map(cooperationer, ParticipantBO.class);

        Cooperationer.Ext ext =
                Optional.ofNullable(cooperationer.getExt()).orElse(new Cooperationer.Ext());
        if (ParticipantModeEnum.isOrSign(ext.getParticipantMode())) {
            participant.setParticipantMode(ParticipantModeEnum.OR_SIGN.getMode());
        } else {
            participant.setParticipantMode(ParticipantModeEnum.NORMAL.getMode());
        }
        participant.setAuthWay(ext.getAuthWay());
        participant.setAccessToken(ext.getAccessToken());
        participant.setAccessTokenType(ext.getAccessTokenType());
        participant.setNeedWill(ext.getNeedWill());
        participant.setDynamic(ext.getDynamic());
        participant.setAssignedSubjectId(ext.getAssignedSubjectId());
        participant.setAssignedSubjectName(ext.getAssignedSubjectName());
        participant.setParticipantStartType(ext.getParticipantStartType());
        participant.setParticipantStartValue(ext.getParticipantStartValue());
        return participant;
    }
}

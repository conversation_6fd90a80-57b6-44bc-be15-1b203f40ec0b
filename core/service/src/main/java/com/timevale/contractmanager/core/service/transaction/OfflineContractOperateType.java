package com.timevale.contractmanager.core.service.transaction;

import lombok.Getter;

/**
 * 线下合同操作通知类型
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Getter
public enum OfflineContractOperateType {
    GENERATE_PROCESS("GENERATE_PROCESS", "生成process"),
    EXTRACT_PROCESS_INFO("EXTRACT_PROCESS_INFO", "提取合同信息"),
    UPDATE_RECORD_STATUS("UPDATE_RECORD_STATUS", "更新导入记录状态"),
    ;
    /** 操作类型 */
    private String type;
    /** 操作描述 */
    private String desc;

    OfflineContractOperateType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}

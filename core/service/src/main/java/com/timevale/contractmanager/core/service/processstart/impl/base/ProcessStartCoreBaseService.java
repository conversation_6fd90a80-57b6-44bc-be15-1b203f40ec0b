package com.timevale.contractmanager.core.service.processstart.impl.base;

import static com.timevale.contractmanager.common.service.enums.ProcessBusinessType.RESCIND;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;
import static com.timevale.doccooperation.service.model.CommonStartExtensionKeyConstants.*;
import static com.timevale.saas.common.manage.common.service.enums.contractNo.ContractNoGenerateTypeEnum.SYSTEM;

import com.google.common.collect.Maps;
import com.timevale.besp.lowcode.integration.response.FormDataResponse;
import com.timevale.contractmanager.common.dal.bean.ContractFileBizRelationDO;
import com.timevale.contractmanager.common.service.bean.FileAuthBean;
import com.timevale.contractmanager.common.service.bean.ProcessFileAuthBean;
import com.timevale.contractmanager.common.service.bean.ProcessSecretConfigBean;
import com.timevale.contractmanager.common.service.bean.process.ProcessFileContractCategory;
import com.timevale.contractmanager.common.service.constant.SystemConstant;
import com.timevale.contractmanager.common.service.enums.*;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.LowcodeClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.integration.client.ShortUrlClient;
import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.common.service.model.AccountSimpleModel;
import com.timevale.contractmanager.common.service.model.BatchUpdateProcessFileAuthModel;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.BaseUserBO;
import com.timevale.contractmanager.core.model.bo.FileAuthBO;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.contractmanager.core.model.bo.process.ProcessStartDataModel;
import com.timevale.contractmanager.core.model.dto.cooperation.CooperationParticipant;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.request.opponent.ProcessStartedEvent;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.model.validationgroup.ProcessStartValidation;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.component.CooperationFlowConverter;
import com.timevale.contractmanager.core.service.contractapproval.ContractApprovalService;
import com.timevale.contractmanager.core.service.contractapproval.param.CreateContractApprovalGroupDTO;
import com.timevale.contractmanager.core.service.contractapproval.param.UseApprovalFlowTemplateDTO;
import com.timevale.contractmanager.core.service.contractrelation.ContractFileBizRelationService;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.flow.FlowOperationFactory;
import com.timevale.contractmanager.core.service.flow.bean.base.UrlExtraParam;
import com.timevale.contractmanager.core.service.flow.bean.param.GetFlowUrlDTO;
import com.timevale.contractmanager.core.service.flow.bean.result.GetFlowUrlResult;
import com.timevale.contractmanager.core.service.mq.model.ProcessChangeMsgEntity;
import com.timevale.contractmanager.core.service.mq.model.ProcessStartedMsgEntity;
import com.timevale.contractmanager.core.service.mq.producer.*;
import com.timevale.contractmanager.core.service.process.*;
import com.timevale.contractmanager.core.service.process.bean.ProcessStartRemarkBizRequest;
import com.timevale.contractmanager.core.service.process.builder.ProcessStartInputBuilder;
import com.timevale.contractmanager.core.service.process.datasource.ProcessStartDataManager;
import com.timevale.contractmanager.core.service.process.impl.ProcessStartHelper;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessDispatchResult;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessDispatchSignFlowParam;
import com.timevale.contractmanager.core.service.processstart.handler.ProcessStartChecker;
import com.timevale.contractmanager.core.service.processstart.handler.participant.ProcessStartParticipantHandler;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.contractmanager.core.service.watermark.WatermarkStrategyContext;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootModel;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootResult;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.doccooperation.service.input.flow.StartFlowBaseInput;
import com.timevale.doccooperation.service.model.startflow.StartSignFlowData;
import com.timevale.doccooperation.service.result.flow.StartFlowResult;
import com.timevale.footstone.rpc.enums.CreateWayEnum;
import com.timevale.framework.mq.client.producer.DelayMsgLevel;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.base.util.ExceptionLogUtil;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkContentTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkTypeEnum;
import com.timevale.saas.common.manage.common.service.model.bean.watermark.WatermarkTemplate;
import com.timevale.saas.common.manage.common.service.model.input.watermark.WatermarkTemplateDetailInput;
import com.timevale.saas.common.manage.common.service.util.ContractNoUtils;
import com.timevale.user.resource.service.input.contact.AddContactInput;
import com.timevale.user.resource.service.model.contact.ContactContentModel;

import lombok.extern.slf4j.Slf4j;

import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;

import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProcessStartCoreBaseService {
    /** 默认发起流程数，1 */
    private static final Integer DEFAULT_PROCESS_COUNT = 1;

    @Autowired protected ProcessStartHelper processStartHelper;
    @Autowired protected ProcessStartChecker processStartChecker;
    @Autowired protected ProcessConfigService processConfigService;
    @Autowired protected ProcessMqProducer processMqProducer;
    @Autowired protected CooperationService cooperationService;
    @Autowired protected ProcessChangeProducer processChangeProducer;
    @Autowired protected ProcessStartInputBuilder processStartInputBuilder;
    @Autowired protected ContractApprovalService contractApprovalService;
    @Autowired protected FlowTemplateService flowTemplateService;
    @Autowired protected ProcessStartParticipantHandler processStartParticipantHandler;
    @Autowired SensorService sensorService;
    @Autowired UserContactSaveProducer userContactSaveProducer;
    @Autowired CooperationFlowConverter cooperationFlowConverter;
    @Autowired ProcessStartedProducer processStartedProducer;
    @Autowired ContractFileBizRelationService contractFileBizRelationService;
    @Autowired MapperFactory mapperFactory;
    @Autowired SystemConfig systemConfig;
    @Autowired ShortUrlClient shortUrlClient;
    @Autowired FlowOperationFactory flowOperationFactory;
    @Autowired ProcessDispatchService processDispatchService;
    @Autowired SaasCommonClient saasCommonClient;
    @Autowired ProcessFileAuthService processFileAuthService;
    @Autowired private ProcessRemarkService processRemarkService;
    @Autowired WatermarkStrategyContext watermarkStrategyContext;
    @Autowired ProcessStartDataManager startDataManager;

    /**
     * 创建合同流程
     *
     * @param request
     * @return
     */
    protected String createProcess(ProcessStartCoreRequest request) {
        String processId = processStartHelper.createProcess(request);
        // 获取合同保密配置
        ProcessSecretConfigBean secretConfig = request.getProcessSecretConfig();
        // 获取合同类型配置
        List<ProcessFileContractCategory> fileContractCategories = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(request.getContracts())) {
            for (FileBO contract : request.getContracts()) {
                if (StringUtils.isBlank(contract.getCategoryId())) {
                    continue;
                }
                ProcessFileContractCategory category = new ProcessFileContractCategory();
                category.setFileId(contract.getFileId());
                category.setCategoryId(contract.getCategoryId());
                fileContractCategories.add(category);
            }
        }
        // 初始化并保存自定义配置信息
        processConfigService.initProcessCustomizeConfig(
                processId, request.getTenantAccount(), secretConfig, fileContractCategories);

        return processId;
    }

    private void saveFileAuth(ProcessStartCoreRequest request, String processId) {

        try {
            List<FileBO> attachments = request.getAttachments();
            if (CollectionUtils.isEmpty(attachments)) {
                return;
            }
            boolean existFileAuth = attachments.stream().anyMatch(t -> CollectionUtils.isNotEmpty(t.getFileAuths()));
            if (!existFileAuth) {
                return;
            }

            fillFileAuthId(request, attachments);
            fillUnLimitFileAuth(attachments);

            BatchUpdateProcessFileAuthModel model = new BatchUpdateProcessFileAuthModel();
            UserAccountDetail tenantAccount = request.getTenantAccount();
            model.setProcessId(processId);
            AccountSimpleModel tenant = new AccountSimpleModel();
            tenant.setOid(tenantAccount.getAccountOid());
            tenant.setGid(tenantAccount.getAccountGid());
            model.setSubject(tenant);
            List<ProcessFileAuthBean> files = new ArrayList<>();
            for (FileBO attachment : attachments) {
                if (CollectionUtils.isEmpty(attachment.getFileAuths())) {
                    continue;
                }
                ProcessFileAuthBean fileAuthBean = new ProcessFileAuthBean();
                fileAuthBean.setFileId(attachment.getFileId());
                fileAuthBean.setFileType(ProcessAuthFileTypeEnum.ATTACHMENT.getType());
                fileAuthBean.setFileAuths(mapperFactory.getMapperFacade().mapAsList(attachment.getFileAuths(), FileAuthBean.class));
                files.add(fileAuthBean);
            }
            model.setFiles(files);
            if (CollectionUtils.isEmpty(files)) {
                return;
            }
            processFileAuthService.updateProcessFileAuth(model);
        } catch (Exception e) {
            ExceptionLogUtil.traceLog(log, e, "saveFileAuth fail, msg: {}, processId:{}", e.getMessage(), processId);
        }
    }

    /**
     * 填充授权的Oid和Gid
     * @param request
     * @param attachments
     */
    private void fillFileAuthId(ProcessStartCoreRequest request, List<FileBO> attachments) {
        List<ParticipantBO> participants = request.getParticipants();
        List<ParticipantInstanceBO> instances = participants.stream().flatMap(t -> t.getInstances().stream()).collect(Collectors.toList());

        Map<String, ParticipantInstanceBO> personMap = instances.stream().filter(t -> ParticipantSubjectType.PSN.getType() == t.getSubjectType()).collect(Collectors.toMap(BaseUserBO::getAccount, Function.identity(), (k1, k2) -> k1));
        Map<String, ParticipantInstanceBO> orgMap = instances.stream().filter(t -> ParticipantSubjectType.ORG.getType() == t.getSubjectType()).collect(Collectors.toMap(BaseUserBO::getSubjectName, Function.identity(), (k1, k2) -> k1));

        for (FileBO attachment : attachments) {
            if (CollectionUtils.isEmpty(attachment.getFileAuths())) {
                continue;
            }

            List<FileAuthBO> fileAuths = attachment.getFileAuths();
            for (FileAuthBO fileAuthBO : fileAuths) {
                if (fileAuthBO.getAuthType().equals(ProcessAuthTypeEnum.MEMBER.getType())) {
                    ParticipantInstanceBO user = personMap.get(fileAuthBO.getAuthName());
                    if (user == null || StringUtils.isEmpty(user.getAccountOid())) {
                        continue;
                    }
                    fileAuthBO.setAuthExtend(user.getAccountOid());
                    fileAuthBO.setAuthId(StringUtils.isEmpty(user.getAccountGid()) ? "" : user.getAccountGid());
                }
                if (fileAuthBO.getAuthType().equals(ProcessAuthTypeEnum.ENTERPRISE.getType())) {
                    ParticipantInstanceBO user = orgMap.get(fileAuthBO.getAuthName());
                    if (user == null || StringUtils.isEmpty(user.getSubjectId())) {
                        continue;
                    }
                    fileAuthBO.setAuthExtend(user.getSubjectId());
                    fileAuthBO.setAuthId(StringUtils.isEmpty(user.getSubjectGid()) ? "" : user.getSubjectGid());
                }
            }
        }
    }

    /**
     * 不限制的文件权限增加授权对象
     * @param attachments
     */
    private void fillUnLimitFileAuth(List<FileBO> attachments) {

        for (FileBO attachment : attachments) {
            if (CollectionUtils.isNotEmpty(attachment.getFileAuths())) {
                continue;
            }
            List<FileAuthBO> fileAuths = new ArrayList<>();
            FileAuthBO fileAuthBO = new FileAuthBO();
            fileAuthBO.setAuthType(ProcessAuthTypeEnum.UNLIMITED.getType());
            fileAuthBO.setAuthId(ProcessAuthTypeEnum.UNLIMITED.getType());
            fileAuthBO.setAuthExtend(ProcessAuthTypeEnum.UNLIMITED.getType());
            fileAuthBO.setPermissions(Lists.newArrayList(ProcessAuthFilePermissionEnum.QUERY.getType()));
            fileAuths.add(fileAuthBO);
            attachment.setFileAuths(fileAuths);
        }
    }

    /**
     * 校验批量发起参数
     *
     * @param startRequest
     * @param processCount
     */
    protected void checkBatchStartParam(ProcessStartCoreRequest startRequest, Integer processCount) {
        List<CooperationParticipant> participants = Lists.newArrayList();
        Map<String, Object> structPrefillContentMap = Maps.newLinkedHashMap();
        for (ParticipantBO part : startRequest.getParticipants()) {
            if (CollectionUtils.isEmpty(part.getInstances())) {
                throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "参与方数量不合法");
            }
            ParticipantInstanceBO participantInstance = part.getInstances().get(0);
            CooperationParticipant cooperationParticipant =
                    cooperationFlowConverter.boToCooperationParticipant(part);
            cooperationParticipant.setInstance(
                    cooperationFlowConverter.boToParticipantInstance(participantInstance));
            participants.add(cooperationParticipant);

            if (MapUtils.isNotEmpty(participantInstance.getPreFillValues())) {
                structPrefillContentMap.putAll(participantInstance.getPreFillValues());
            }
        }

        /** 这里做个优化 在check能否批量发起时传入processGroupId，在这一步doc-cooperation先根据groupId缓存flow-template信息 */
        GroupTypeEnum batchType = GroupTypeEnum.BATCH_START_SIGN_FLOW;
        if (startRequest.needFill()) {
            batchType = startRequest.isMultiBatch() ? GroupTypeEnum.BATCH_START_MULTI_TEMPLATE_FLOW : GroupTypeEnum.BATCH_START_TEMPLATE_FLOW;
        }
        String processGroupId = processStartHelper.createProcessGroup(batchType, startRequest);
        startRequest.setProcessGroupId(processGroupId);
        startRequest.setProcessGroupName(startRequest.getProcessName());

        // 如果需要发起合同审批，且使用新版合同审批， 则创建新版合同审批组
        String approvalTemplateId = startRequest.getApproveTemplateId();
        if (StringUtils.isNotBlank(approvalTemplateId)) {
            UseApprovalFlowTemplateDTO useParam = new UseApprovalFlowTemplateDTO();
            useParam.setApprovalTemplateId(startRequest.getApproveTemplateId());
            useParam.setSubjectGid(startRequest.getTenantAccount().getAccountGid());
            CreateContractApprovalGroupDTO groupParam = new CreateContractApprovalGroupDTO();
            groupParam.setAccountId(startRequest.getInitiatorAccount().getAccountOid());
            groupParam.setAccountGid(startRequest.getInitiatorAccount().getAccountGid());
            groupParam.setAccountName(startRequest.getInitiatorAccount().getAccountName());
            groupParam.setSubjectId(startRequest.getTenantAccount().getAccountOid());
            groupParam.setSubjectGid(startRequest.getTenantAccount().getAccountGid());
            groupParam.setBizGroupId(startRequest.getProcessGroupId());
            groupParam.setBizGroupName(startRequest.getProcessGroupName());
            groupParam.setTotalCount(processCount);
            contractApprovalService.createApprovalGroup(groupParam);
        }
    }

    /**
     * 发送流程发起成功消息
     *
     * @param request
     * @param processId
     */
    protected void sendStartMessage(ProcessStartCoreRequest request, String processId) {
        try {
            // 发送保存联系人消息
            userContactSave(request);
            // 推送流程发起成功事件
            // 如果是定时发起场景， doc-cooperation会监听这个消息， 删除定时发起记录
            // 后续如果支持基于模板的定时发起， 传入的flowTemplateId要考虑一下是否调整逻辑
            ProcessStartedEvent processStartedEvent = new ProcessStartedEvent();
            processStartedEvent.setProcessId(processId);
            processStartedEvent.setOperatorAccount(request.getInitiatorAccount());
            processStartedEvent.setTenantAccount(request.getTenantAccount());
            processStartedEvent.setFlowTemplateId(request.getFlowTemplateId());

            // 发送三方业务关联消息
            sendBizProcessMessage(request, processId);

            /** 消费合同发起时有些数据没有写入es，延迟5s发送消息 */
            String msg = JsonUtils.obj2json(processStartedEvent);
            processMqProducer.sendDelayMessage(
                    ProducerWhatEnum.PROCESS_STARTED, msg, null, DelayMsgLevel.TWO);

            // 同步合同流程发起后补充的流程信息
            processChangeProducer.sendMessage(
                    new ProcessChangeMsgEntity(processId),
                    ProcessChangeTagEnum.PROCESS_INFO_CHANGE.getTag());
        } catch (Exception e) {
            log.error("sendMessage failed, request: {}", JsonUtils.obj2json(request), e);
        }
    }

    /**
     * 组装返回结果
     *
     * @param processId
     * @param flowResult
     * @return
     */
    protected ProcessStartResult buildStartResponse(String processId, StartFlowResult flowResult) {
        ProcessStartResult response = new ProcessStartResult();
        response.setProcessId(processId);
        response.setResultUrl(flowResult.getRestUrl());
        response.setLongResultUrl(flowResult.getLongRestUrl());
        response.setFlowId(flowResult.getFlowId());
        response.setFlowType(flowResult.getFlowType());
        return response;
    }

    /**
     * 发起必要参数校验
     *
     * @param request
     */
    protected void validParam(ProcessStartCoreRequest request) {
        validParam(request, DEFAULT_PROCESS_COUNT);
    }

    /**
     * 发起必要参数校验
     *
     * @param request
     */
    protected void validParam(ProcessStartCoreRequest request, int processCount) {
        // 通用参数校验
        ValidationUtil.validateBean(request, ProcessStartValidation.class);

        processCount = processCount < DEFAULT_PROCESS_COUNT ? DEFAULT_PROCESS_COUNT : processCount;

        // 非批量发起场景 或者 扫码发起 或 API发起指定流程组 场景， 需校验余额等信息
        if (StringUtils.isBlank(request.getProcessGroupId())
                || ProcessStartType.isShareScanStart(request.getStartType())
                || request.isRpcStart() || request.isResetStart()) {
            // 校验文件有效性
            processStartChecker.checkContractFile(request.getContracts());
            processStartChecker.checkAttachmentFile(request.getAttachments());
            // 校验参与方实例
            processStartChecker.checkParticipantAndCcs(
                    request.getParticipants(), request.getCcs(), processCount > 1, request.getClientId());
            // 扩展字段补充
            if (StringUtils.isBlank(request.getExtension(SIGN_CREATE_WAY))) {
                boolean templateStart =
                        ProcessStartScene.TEMPLATE_START.getScene() == request.getStartScene();
                CreateWayEnum createWay =
                        templateStart ? CreateWayEnum.TEMPLATE : CreateWayEnum.NORMAL;
                request.addExtension(SIGN_CREATE_WAY, createWay.getType());
            }
            if (request.getSignPlatform() != null) {
                request.addExtension(SIGN_PLATFORM, request.getSignPlatform().toString());
            }
            request.addExtension(SIGN_REDIRECT_URL, request.getRedirectUrl());
            request.addExtension(SIGN_EXTEND_DATA, processStartHelper.getSignFlowExtendData());
            request.addExtension(SIGN_PRE_FLOW_ID, request.getAssignFlowId());
            // 扫码发起场景， 需要补充原始模板id, 其他场景忽略
            if (ProcessStartType.isShareScanStart(request.getStartType())) {
                request.addExtension(ORIGIN_FLOW_TEMPLATE_ID, request.getOriginFlowTemplateId());
            }
            // 校验付费方余额是否充足
            String productNo = Optional.ofNullable(request.getStartCoreConfig().getProductNo())
                    .orElse(SystemConstant.SAAS_PRODUCT_NO);
            // 校验付费方余额是否充足
            Long productId = Optional.ofNullable(request.getStartCoreConfig().getProductId())
                    .orElse(SystemConstant.SAAS_PRODUCT_ID);
            // 计费是否需要隔离
            boolean billingNeedIsolate  = request.getStartCoreConfig().getNeedBillingIso();

            processStartHelper.checkBillBalance(
                    request.getClientId(),
                    request.getInitiatorAccount(),
                    request.getPayerAccount(),
                    productNo,
                    productId,
                    request.getContracts().size() * processCount,
                    billingNeedIsolate);
        }
    }

    /**
     * 发起流程
     *
     * @param request
     * @param startFlowAction
     * @return
     */
    protected ProcessStartResult startFlow(
            ProcessStartCoreRequest request, CoreStartFlowAction startFlowAction) {
        // 1. 生成processId
        String processId = createProcess(request);

        //2. 准备流程关联数据
        prepareProcessRelationData(processId, request);

        // 3. 创建流程
        ProcessStartResult startResult = startFlowAction.doAction(processId, request);
        log.info("start success, processId:{} ", processId);

        //保存文件授权
        saveFileAuth(request, processId);

        // 保存合同备注
        saveRemarks(request, processId);

        // 4. 发送通知消息
        sendStartMessage(request, processId);

        // 5.埋点
        processStartTracking(request, processId);

        // 记录审计日志信息必要参数
        acceptAuditLog(request, startResult);

        // 6. 返回参数
        return startResult;
    }

    private void saveRemarks(ProcessStartCoreRequest coreRequest, String processId) {
        if (CollectionUtils.isEmpty(coreRequest.getRemarks()) && CollectionUtils.isEmpty(coreRequest.getProcessRemarks())) {
            return;
        }
        ProcessStartRemarkBizRequest remarkBizRequest = new ProcessStartRemarkBizRequest();
        remarkBizRequest.setProcessId(processId);
        remarkBizRequest.setRemarks(coreRequest.getRemarks());
        remarkBizRequest.setProcessRemarks(coreRequest.getProcessRemarks());
        remarkBizRequest.setAccountId(coreRequest.getInitiatorAccount().getAccountOid());
        remarkBizRequest.setAccountGid(coreRequest.getInitiatorAccount().getAccountGid());
        remarkBizRequest.setAccountName(coreRequest.getInitiatorAccount().getAccountName());
        remarkBizRequest.setAccountLoginMobile(coreRequest.getInitiatorAccount().getAccountLoginMobile());
        remarkBizRequest.setAccountLoginEmail(coreRequest.getInitiatorAccount().getAccountLoginEmail());
        remarkBizRequest.setSubjectId(coreRequest.getTenantAccount().getAccountOid());
        remarkBizRequest.setSubjectGid(coreRequest.getTenantAccount().getAccountGid());
        try {
            processRemarkService.processStartAddRemark(remarkBizRequest);
        } catch (Exception e) {
            // 异常不卡流程
            log.error("startFlow processStartAddRemark error:{}, processId:{}", e.getMessage(), processId);
        }
    }

    private void acceptAuditLog(ProcessStartCoreRequest request, ProcessStartResult startResult) {
        if (startResult != null) {
            LogRecordContext.putVariable(AuditLogConstant.Field.PROCESS_ID, startResult.getProcessId());
        }

        LogRecordContext.putVariable(AuditLogConstant.Field.BUSINESS_TYPE, request.getBusinessType());
        LogRecordContext.putVariable(AuditLogConstant.Field.START_TYPE, request.getStartType());
    }

    private void prepareProcessRelationData(String processId, ProcessStartCoreRequest request) {

        // 1. 添加合同文件关联信息
        addContractBizRelations(processId, request);

        //2. 生成并关联水印快照
        if(ConfigService.getAppConfig().getBooleanProperty(SystemConfig.ADD_CONTRACT_WATERMARK_SWITCH, false)){
            generateWatermarkSnapShoot(processId, request);
        }

        //3. 保存数据源发起的processid
        addProcessIdToStartData(processId, request);

    }

    private void addProcessIdToStartData(String processId, ProcessStartCoreRequest request) {
        if(Objects.isNull(request.getStartDataSource()) || StringUtils.isBlank(request.getStartDataSource().getDataId())){
            return;
        }
        ProcessStartDataModel startDataModel = startDataManager.getByDataId(request.getStartDataSource().getDataId());
        startDataModel.setProcessId(processId);
        startDataManager.updateByDataId(startDataModel);
    }

    /**
     * 生成水印快照
     * @param processId
     * @param request
     */
    private void generateWatermarkSnapShoot(String processId, ProcessStartCoreRequest request) {
        String watermarkId = StringUtils.defaultIfBlank(request.getUseWatermarkTemplateSnapshotId(), request.getUseWatermarkTemplateId());
        if(StringUtils.isBlank(watermarkId)){
            //如果未使用水印模板，则直接结束
            return;
        }

        try {
            WatermarkTemplateDetailInput input = new WatermarkTemplateDetailInput();
            //获取发起时指定的水印模板ID
            input.setWatermarkId(watermarkId);
            input.setNeedImageUrl(false);
            input.setOid(request.getTenantAccount().getAccountOid());
            input.setOperatorOid(request.getInitiatorAccount().getAccountOid());
            WatermarkTemplate watermarkDetail = saasCommonClient.getWatermarkTemplateDetail(input);
            //策略模式获取对应水印处理类
            GenerateWatermarkSnapShootModel strategyModel = new GenerateWatermarkSnapShootModel();
            strategyModel.setProcessId(processId);
            strategyModel.setWatermarkId(watermarkId);
            strategyModel.setWatermarkType(WatermarkTypeEnum.getEnumByType(watermarkDetail.getType()));
            strategyModel.setWatermarkContentType(WatermarkContentTypeEnum.getEnumByType(watermarkDetail.getContentType()));
            strategyModel.setTenantId(request.getTenantAccount().getAccountOid());
            strategyModel.setOperatorId(request.getInitiatorAccount().getAccountOid());
            strategyModel.setFiles(request.getContracts());
            //分类型处理(返回一个对象易于扩展)
            GenerateWatermarkSnapShootResult result = watermarkStrategyContext.generateWatermarkSnapShoot(strategyModel);

            if(Objects.isNull(result) || CollectionUtils.isEmpty(result.getFileIdAndWatermarkSSIdMap())){
                log.warn("generateWatermarkSnapShoot failed，processId:{}, result: {}", processId, result);
                throw new BizContractManagerException(GENERATE_WATERMARK_SNAPSHOOT_FAILED);
            }
            //置入对应文件中
            request.getContracts().forEach(fileBO -> {
                fileBO.setWatermarkId(result.getFileIdAndWatermarkSSIdMap().get(fileBO.getFileId()));
            });

        } catch (Exception e) {
            log.warn("generateWatermarkSnapShoot failed，processId: {}", processId, e);
            // 降级开关，打开则忽略合同编号异常，默认打开
            if (!systemConfig.isSilenceWatermarkExp()) {
                if(e instanceof BaseBizRuntimeException){
                    throw e;
                }
                throw new BizContractManagerException(GENERATE_WATERMARK_SNAPSHOOT_FAILED);
            }
        }
    }

    /**
     * 组装地址扩展信息
     *
     * @param request
     * @return
     */
    protected UrlExtraParam buildUrlExtraParam(ProcessStartCoreRequest request) {
        UrlExtraParam extraParam = new UrlExtraParam();
        extraParam.setOperatorAccount(request.getOperatorAccount());
        extraParam.setRedirectUrl(request.getRedirectUrl());
        extraParam.setToken(request.getToken());
        extraParam.setPlatform(request.getSignPlatform());
        return extraParam;
    }

    /**
     * 发起合同审批或签署流程
     *
     * @param input
     * @return
     */
    protected ProcessStartResult startApprovalOrSignFlow(StartSignFlowData input, UrlExtraParam param) {
        ProcessDispatchSignFlowParam dispatchParam = new ProcessDispatchSignFlowParam();
        dispatchParam.setInput(input);
        dispatchParam.setOperatorId(param.getOperatorAccount().getAccountOid());
        ProcessDispatchResult result = processDispatchService.dispatchApprovalOrSignFlow(dispatchParam);
        // 组装发起返回数据
        ProcessStartResult startResult =
                new ProcessStartResult(
                        input.getProcessId(), result.getFlowId(), result.getFlowType());
        // 没有流程id, 无法获取详情地址， 直接返回
        if (StringUtils.isBlank(result.getFlowId()) || null == result.getFlowType()) {
            return startResult;
        }
        // 获取详情地址
        GetFlowUrlDTO urlParam = new GetFlowUrlDTO();
        urlParam.setProcessId(input.getProcessId());
        urlParam.setFlowId(result.getFlowId());
        urlParam.setAppId(input.getAppId());
        urlParam.setClientId(input.getSource());
        urlParam.setAccountId(input.getInitiatorOid());
        urlParam.setSubjectId(input.getSubjectOid());
        urlParam.setPlatform(param.getPlatform());
        urlParam.setRedirectUrl(param.getRedirectUrl());
        urlParam.setToken(param.getToken());
        GetFlowUrlResult flowUrl =
                flowOperationFactory.getService(result.getFlowType()).getFlowUrl(urlParam);
        startResult.setLongResultUrl(flowUrl.getFlowUrl());
        startResult.setResultUrl(shortUrlClient.convertShortUrl(flowUrl.getFlowUrl()));
        return startResult;
    }

    /**
     * 保存联系人
     *
     * @param request
     */
    protected void userContactSave(ProcessStartCoreRequest request) {
        // 获取参与方列表
        List<ParticipantBO> cooperationerList = request.getParticipants();
        // 获取发起人oid
        String ouid = request.getInitiatorAccount().getAccountOid();
        // 如果参与方列表为空，无需保存联系人，跳过
        if (CollectionUtils.isEmpty(cooperationerList)) {
            return;
        }
        AddContactInput input = new AddContactInput();
        input.setOuid(ouid);
        for (ParticipantBO cooperationer : cooperationerList) {
            if (CollectionUtils.isEmpty(cooperationer.getInstances())) {
                continue;
            }
            ParticipantInstanceBO instance = cooperationer.getInstances().get(0);
            // 如果参与人没有手机号/邮箱， 无需保存联系人，跳过
            if (StringUtils.isBlank(instance.getAccount())) {
                continue;
            }
            // 发送添加联系人信息
            ContactContentModel contactContentModel = new ContactContentModel();
            contactContentModel.setUniqueId(instance.getAccount());
            contactContentModel.setName(instance.getAccountName());
            if(Objects.equals(cooperationer.getParticipantSubjectType(), ParticipantSubjectType.ORG.getType())){
                contactContentModel.setAffliation(instance.getSubjectName());
            }
            if (contactContentModel != null) {
                input.setContact(contactContentModel);
                userContactSaveProducer.sendMessage(input);
            }
        }
    }

    /**
     * 发起埋点
     *
     * @param request
     * @param processId
     */
    private void processStartTracking(ProcessStartCoreRequest request, String processId) {
        try {
            // 判断是否需要发起埋点， 如果是，触发埋点
            if (request.isNeedStartSensor()) {
                sensorService.processStartTracking(processId, request);
            }
        } catch (Exception e) {
            log.error("processStartTracking failed, processId: {}", processId, e);
        }
    }

    private void sendBizProcessMessage(ProcessStartCoreRequest request, String processId) {
        ParticipantInstanceBO bizInstance = getBizInstance(request);
        ProcessStartedMsgEntity msgEntity = new ProcessStartedMsgEntity();
        msgEntity.setProcessId(processId);
        msgEntity.setProcessName(request.getProcessName());
        msgEntity.setInitiatorOid(request.getInitiatorAccount().getAccountOid());
        msgEntity.setInitiatorTenantId(request.getTenantAccount().getAccountOid());

        if (StringUtils.isNotBlank(request.getBizProcessId())) {
            msgEntity.setBizProcessId(request.getBizProcessId());
            msgEntity.setBizProcessType(request.getBizProcessType());
            supplementDataSourceInfo(request, msgEntity);
            processStartedProducer.sendMessage(JsonUtils.obj2json(msgEntity));
        } else if (Objects.nonNull(bizInstance)) {
            msgEntity.setBizProcessId(bizInstance.getSubTaskBizId());
            msgEntity.setBizProcessType(bizInstance.getSubTaskBizType());
            processStartedProducer.sendMessage(JsonUtils.obj2json(msgEntity));
        }
    }

    private void supplementDataSourceInfo(ProcessStartCoreRequest request, ProcessStartedMsgEntity msgEntity) {
        try {
            if(Objects.nonNull(request.getStartDataSource()) && StringUtils.isNotBlank(request.getStartDataSource().getDataId())){
                //如果是协商起草，要带上渠道信息和表单id
                ProcessStartDataModel startDataModel = startDataManager.getByDataId(request.getStartDataSource().getDataId());
                if(Objects.isNull(startDataModel)){
                    return;
                }
                //添加数据源相关信息
                msgEntity.setBizChannel(startDataModel.getDataSourceChannel());
                msgEntity.setBizDataSourceFormId(startDataModel.getDataSourceId());
            }
        } catch (Exception e) {
            ExceptionLogUtil.traceLog(log, e, "发送发起后置通知查询数据源失败！");
        }
    }

    /**
     * FIXME 获取需要关联三方业务的实例
     *
     * @param request
     * @return
     */
    private ParticipantInstanceBO getBizInstance(ProcessStartCoreRequest request) {
        return request.getParticipants().stream()
                .map(ParticipantBO::getInstances)
                .flatMap(Collection::stream)
                .filter(instance -> StringUtils.isNotBlank(instance.getSubTaskBizId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 给合同加上合同编号
     *
     * @param request
     */
    private void populateContractNo(ProcessStartCoreRequest request) {
        List<FileBO> contracts = request.getContracts();
        // 解约发起编号生成
        if (RESCIND.getBusinessType().equals(request.getBusinessType())) {
            for (FileBO contract : contracts) {
                contract.setContractNoType(SYSTEM.getType());
                contract.setContractNo(ContractNoUtils.generateRescindNo());
            }
            return;
        }

        // 其他场景编号生成，Map<fileId, contractNo>
        Map<String, String> fileContractNoMap =
                contractFileBizRelationService.genContractNo(contracts);
        for (FileBO contract : contracts) {
            contractFileBizRelationService.amendContractNoType(contract);
            String contractNo =
                    Optional.ofNullable(contract.getContractNo())
                            .filter(StringUtils::isNotBlank)
                            .orElse(fileContractNoMap.get(contract.getFileId()));
            // 为空兜底生成系统编号
            contract.setContractNo(
                    StringUtils.isNotBlank(contractNo)
                            ? contractNo
                            : ContractNoUtils.generateSystemNo());
        }
    }

    /**
     * 保存合同文件关联信息
     *
     * @param processId
     * @param request
     */
    private void addContractBizRelations(String processId, ProcessStartCoreRequest request) {
        try {
            List<FileBO> contracts = request.getContracts();
            if (CollectionUtils.isEmpty(contracts)) {
                return;
            }
            populateContractNo(request);

            MapperFacade mapperFacade = mapperFactory.getMapperFacade();
            List<ContractFileBizRelationDO> relations =
                    contracts.stream()
                            .map(x -> mapperFacade.map(x, ContractFileBizRelationDO.class))
                            .peek(relation -> relation.setOriginFileId(relation.getFileId()))
                            .peek(relation -> relation.setProcessId(processId))
                            .collect(Collectors.toList());

            contractFileBizRelationService.saveBatch(relations);
        } catch (Exception e) {
            log.warn("合同编号保存失败，processId: {}", processId, e);
            // 降级开关，打开则忽略合同编号异常，默认关闭
            if (!systemConfig.isSilenceContractNoExp()) {
                throw e;
            }
        }
    }

    /** 发起流程执行器 */
    protected interface CoreStartFlowAction {
        ProcessStartResult doAction(String processId, ProcessStartCoreRequest request);
    }

    protected void checkDuplicateParticipantAccounts(
            List<StartFlowBaseInput.FlowAccount> flowAccounts) {
        if (CollectionUtils.isEmpty(flowAccounts) || flowAccounts.size() == 1) {
            return;
        }

        Set<String> oidSet = new HashSet<>();
        Set<String> gidSet = new HashSet<>();

        for (StartFlowBaseInput.FlowAccount flowAccount : flowAccounts) {
            if (!oidSet.add(flowAccount.getAccountOid())) {
                throw new BizContractManagerException(OR_SIGN_ACCOUNT_DUPLICATE);
            }

            if (StringUtils.isNotBlank(flowAccount.getAccountGid())
                    && !gidSet.add(flowAccount.getAccountGid())) {
                throw new BizContractManagerException(OR_SIGN_ACCOUNT_DUPLICATE);
            }
        }
    }
}

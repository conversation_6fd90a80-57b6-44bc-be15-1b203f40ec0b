package com.timevale.contractmanager.core.service.processstart.builder;

import com.timevale.contractmanager.common.dal.bean.ProcessConfigDO;
import com.timevale.contractmanager.common.service.bean.ProcessConfigBean;
import com.timevale.contractmanager.common.service.integration.client.DingSignClient;
import com.timevale.contractmanager.common.service.integration.client.DocCooperationClient;
import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.contractmanager.core.model.bo.fda.FDASignatureConfig;
import com.timevale.contractmanager.core.service.component.ProcessConfigConverter;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessStartSignParam;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.doccooperation.service.input.flow.StartFlowBaseInput;
import com.timevale.doccooperation.service.input.flow.StartSignFlowInput;
import com.timevale.doccooperation.service.model.startflow.StartSignFlowData;
import com.timevale.doccooperation.service.result.CooperationFilledSignInfoResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.timevale.footstone.base.model.enums.ClientEnum.DING_TALK;
import static com.timevale.footstone.base.model.enums.ClientEnum.DING_TALK_LABOR;

/**
 * 发起流程底层input参数组装器
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Slf4j
@Component
public class DirectStartSignParamBuilder {

    @Autowired BaseProcessService baseProcessService;
    @Autowired DingSignClient dingSignClient;
    @Autowired DocCooperationClient docCooperationClient;
    @Autowired private MapperFactory mapperFactory;

    /**
     * 根据填写流程id查询并构建创建签署流程参数
     *
     * @param
     * @return
     */
    public ProcessStartSignParam buildProcessStartSignBean(String cooperationId) {
        CooperationFilledSignInfoResult filledSignInfo =
                docCooperationClient.getCooperationFilledSignInfo(cooperationId);
        return buildProcessStartSignBean(filledSignInfo.getSignInfo());
    }

    /**
     * 构建创建签署流程参数
     *
     * @param
     * @return
     */
    public ProcessStartSignParam buildProcessStartSignBean(StartSignFlowData input) {

        // 1, 构建参数
        ProcessStartSignParam startSignBean = new ProcessStartSignParam();
        startSignBean.setAppId(input.getAppId());
        startSignBean.setFlowName(input.getFlowName());
        startSignBean.setNoticeType(input.getNoticeType());
        ProcessStartSignParam.FlowAccount initiator = new ProcessStartSignParam.FlowAccount();
        initiator.setAccount(
                StringUtils.isNotBlank(input.getInitiatorMobile())
                        ? input.getInitiatorMobile()
                        : input.getInitiatorEmail());
        initiator.setAccountName(input.getInitiatorAccountName());
        initiator.setAccountOid(input.getInitiatorOid());
        initiator.setAccountGid(input.getInitiatorGid());
        initiator.setAccountUid(input.getInitiatorUid());
        initiator.setSubjectName(input.getSubjectAccountName());
        initiator.setSubjectOid(input.getSubjectOid());
        initiator.setSubjectGid(input.getSubjectGid());
        initiator.setSubjectUid(input.getSubjectUid());
        initiator.setSubjectType(
                !StringUtils.equals(input.getInitiatorOid(), input.getSubjectOid())
                        ? SubjectTypeEnum.ORG.getType()
                        : SubjectTypeEnum.PERSON.getType());

        startSignBean.setInitiator(initiator);
        if (null != input.getFlowInfo()) {
            startSignBean.setFlowId(input.getFlowInfo().getFlowId());
            startSignBean.setFlowInfo(convertFlowInfoToInput(input.getFlowInfo()));
            // 处理付费方账号id， 调用前必须准备好发起端及flowInfo信息
            // 主要为了兼容历史钉签发起的未固化付费方账号id的填写流程发起签署的场景，需要获取钉签付费方账号id
            handlePayAccountId(startSignBean);
        }
        startSignBean.setSignMode(input.getSignMode());
        startSignBean.setProcessId(input.getProcessId());
        startSignBean.setCooperationId(input.getCooperationId());
        startSignBean.setFlowTemplateId(input.getFlowTemplateId());
        startSignBean.setProcessGroupId(input.getProcessGroupId());
        startSignBean.setProcessGroupName(input.getProcessGroupName());
        startSignBean.setSource(input.getSource());
        startSignBean.setBizScene(input.getBizScene());
        startSignBean.setStartFlowExtendMap(input.getStartFlowExtendMap());
        // 获取发起人部门id
        ProcessConfigDO processConfigDO = baseProcessService.queryProcessConfig(input.getProcessId());
        ProcessConfigBean configBean = ProcessConfigConverter.convertProcessConfig(processConfigDO, true);
        startSignBean.setInitiatorDeptId(configBean.getInitiatorDeptId());
        startSignBean.setApprovalTemplateConditionType(configBean.getApprovalTemplateConditionType());
        startSignBean.setApprovalTemplateConditionValue(configBean.getApprovalTemplateConditionValue());
        startSignBean.getFlowInfo().setAllowRescind(configBean.getAllowRescind());
        // 合同
        startSignBean.setDocuments(
                input.getDocuments().stream()
                        .map(i -> buildFlowFile(i))
                        .collect(Collectors.toList()));
        // 附件
        if (CollectionUtils.isNotEmpty(input.getFlowInfo().getAttachments())) {
            startSignBean.setAttachments(
                    input.getFlowInfo().getAttachments().stream()
                            .map(i -> buildFlowFile(i))
                            .collect(Collectors.toList()));
        }
        // 抄送人
        if (CollectionUtils.isNotEmpty(input.getFlowInfo().getCopyTos())) {
            startSignBean.setCcs(
                    input.getFlowInfo().getCopyTos().stream()
                            .map(i -> buildFlowCcs(i))
                            .collect(Collectors.toList()));
        }
        // 签署人
        if (CollectionUtils.isNotEmpty(input.getSigners())) {
            startSignBean.setSigners(
                    input.getSigners().stream()
                            .map(i -> buildFlowSigner(i,configBean))
                            .sorted(Comparator.comparing(i->i.getExt() != null && i.getExt().getSignOrder() != null ? i.getExt().getSignOrder() : 0))
                            .collect(Collectors.toList()));
        }
        //fda
        if (Objects.nonNull(input.getFdaSignatureConfig())) {
            startSignBean.setFdaSignatureConfig(buildFDASignatureConfig(input.getFdaSignatureConfig()));
        }

        return startSignBean;
    }

    private FDASignatureConfig buildFDASignatureConfig(com.timevale.doccooperation.service.model.FDASignatureConfig fdaSignatureConfig) {
        FDASignatureConfig fdaSignatureBean = new FDASignatureConfig();
        if (Objects.isNull(fdaSignatureConfig)) {
            return fdaSignatureBean;
        }
        fdaSignatureBean = mapperFactory.getMapperFacade().map(fdaSignatureConfig,FDASignatureConfig.class);
        return fdaSignatureBean;
    }

    /**
     * 组装抄送人信息
     *
     * @param flowAccount
     * @return
     */
    private ProcessStartSignParam.FlowAccount buildFlowCcs(
            StartSignFlowInput.FlowAccount flowAccount) {
        ProcessStartSignParam.FlowAccount cc = new ProcessStartSignParam.FlowAccount();
        fillFlowAccount(flowAccount, cc);
        return cc;
    }

    /**
     * 组装签署人信息
     *
     * @param i
     * @return
     */
    private ProcessStartSignParam.FlowSigner buildFlowSigner(StartSignFlowInput.FlowSigner i,ProcessConfigBean configBean) {
        ProcessStartSignParam.FlowSigner flowSigner = new ProcessStartSignParam.FlowSigner();
        flowSigner.setSignerLabel(i.getSignerLabel());
        flowSigner.setSignOrder(i.getSignOrder());
        flowSigner.setSignAreas(i.getSignAreas());
        flowSigner.setRemarkSignAreas(i.getRemarkSignAreas());
        flowSigner.setDateSignAreas(
                i.getDateSignAreas());
        ProcessStartSignParam.FlowParticipant.Ext ext =
                new ProcessStartSignParam.FlowParticipant.Ext();
        ext.setParticipantMode(i.getExt().getParticipantMode());
        ext.setSealType(i.getExt().getSealType());
        ext.setSignRequirements(i.getExt().getSignRequirements());
        ext.setWillTypes(i.getExt().getWillTypes());
        ext.setNeedWill(i.getExt().getNeedWill());
        ext.setSignOrder(i.getExt().getSignOrder());
        ext.setSignSealType(i.getExt().getSignSealType());
        ext.setSignSeal(i.getExt().getSignSeal());
        ext.setSupportCrossSignSeal(i.getExt().getSupportCrossSignSeal());
        ext.setForceReadEnd(i.getExt().getForceReadEnd());
        ext.setForceReadTime(i.getExt().getForceReadTime());
        ext.setAttachmentConfigs(i.getExt().getAttachmentConfigs());
        // 组装签署声明信息
        ext.setSignTipsTitle(i.getExt().getSignTipsTitle());
        ext.setSignTipsContent(i.getExt().getSignTipsContent());
        ext.setAuthWay(i.getExt().getAuthWay());
        ext.setAccessToken(i.getExt().getAccessToken());
        ext.setShareParticipant(i.getExt().getShareParticipant());
        ext.setSignNoticeTypes(i.getExt().getSignNoticeTypes());
        ext.setParticipantStartType(i.getExt().getParticipantStartType());
        ext.setParticipantStartValue(i.getExt().getParticipantStartValue());
        ext.setForceLicense(i.getExt().getForceLicense());
        flowSigner.setExt(ext);
        flowSigner.setAccounts(Lists.newArrayList());
        //设置签署区大小类型
        flowSigner.setSignSealSizeType(configBean.getSignSealSizeType());
        for (StartFlowBaseInput.FlowAccount signerAccount : i.obtainAccounts()) {
            ProcessStartSignParam.FlowAccount flowAccount = new ProcessStartSignParam.FlowAccount();
            flowAccount.setAccount(signerAccount.getAccount());
            flowAccount.setAccountName(signerAccount.getAccountName());
            flowAccount.setAccountNickname(signerAccount.getAccountNickname());
            flowAccount.setAccountOid(signerAccount.getAccountOid());
            flowAccount.setAccountGid(signerAccount.getAccountGid());
            flowAccount.setAccountUid(signerAccount.getAccountUid());
            flowAccount.setAccountLicenseType(signerAccount.getAccountLicenseType());
            flowAccount.setAccountLicense(signerAccount.getAccountLicense());
            flowAccount.setSubjectName(signerAccount.getSubjectName());
            flowAccount.setSubjectOid(signerAccount.getSubjectOid());
            flowAccount.setSubjectGid(signerAccount.getSubjectGid());
            flowAccount.setSubjectUid(signerAccount.getSubjectUid());
            flowAccount.setSubjectType(signerAccount.getSubjectType());
            flowAccount.setSignNoticeTypes(signerAccount.getSignNoticeTypes());
            flowSigner.getAccounts().add(flowAccount);
        }
        // 填充账号信息
        fillFlowAccount(i.obtainAccounts().get(0), flowSigner);
        return flowSigner;
    }

    /**
     * 组装抄送人信息
     *
     * @param cc
     * @return
     */
    public ProcessStartSignParam.FlowAccount parseCopyTo(UserBO cc) {
        ProcessStartSignParam.FlowAccount copyTo = new ProcessStartSignParam.FlowAccount();
        parseInfoToFlowAccount(cc, copyTo);
        return copyTo;
    }

    /**
     * 填充参与方实例账号及扩展信息， UserBO如果OID不为空，需在前置校验步骤中查询账号并补充GID
     *
     * @param userBO
     * @param flowAccount
     */
    public void parseInfoToFlowAccount(
            UserBO userBO, ProcessStartSignParam.FlowAccount flowAccount) {

        // 10.2.1设置参与人信息
        flowAccount.setAccount(userBO.getAccount());
        flowAccount.setAccountOid(userBO.getAccountOid());
        flowAccount.setAccountGid(userBO.getAccountGid());
        flowAccount.setAccountUid(userBO.getAccountUid());
        flowAccount.setAccountName(userBO.getAccountName());
        flowAccount.setAccountNickname(userBO.getAccountNick());
        // 10.2.2设置参与人主体信息
        flowAccount.setSubjectOid(userBO.getSubjectId());
        flowAccount.setSubjectGid(userBO.getSubjectGid());
        flowAccount.setSubjectUid(userBO.getSubjectUid());
        flowAccount.setSubjectName(userBO.getSubjectName());
        flowAccount.setSubjectType(userBO.getSubjectType());
    }

    /**
     * 组装流程文件
     *
     * @param file
     * @return
     */
    private ProcessStartSignParam.FlowFile buildFlowFile(StartSignFlowInput.FlowFile file) {
        ProcessStartSignParam.FlowFile flowFile = new ProcessStartSignParam.FlowFile();
        flowFile.setFileId(file.getFileId());
        flowFile.setFileName(file.getFileName());
        flowFile.setFileKey(file.getFileKey());
        flowFile.setFrom(file.getFrom());
        flowFile.setWatermarkId(file.getWatermarkId());
        return flowFile;
    }

    /**
     * 填充流程账号信息
     *
     * @param account
     * @param flowAccount
     */
    private void fillFlowAccount(
            StartSignFlowInput.FlowAccount account, ProcessStartSignParam.FlowAccount flowAccount) {
        flowAccount.setAccount(account.getAccount());
        flowAccount.setAccountName(account.getAccountName());
        flowAccount.setAccountNickname(account.getAccountNickname());
        flowAccount.setAccountOid(account.getAccountOid());
        flowAccount.setAccountGid(account.getAccountGid());
        flowAccount.setAccountUid(account.getAccountUid());
        flowAccount.setSubjectName(account.getSubjectName());
        flowAccount.setSubjectOid(account.getSubjectOid());
        flowAccount.setSubjectGid(account.getSubjectGid());
        flowAccount.setSubjectUid(account.getSubjectUid());
        flowAccount.setSubjectType(account.getSubjectType());
    }

    /**
     * 将模板的FlowInfo转换成发起参数中需要的FlowInfo
     *
     * @param flowInfo
     * @return
     */
    private ProcessStartSignParam.FlowInfo convertFlowInfoToInput(
            StartSignFlowInput.FlowInfo flowInfo) {
        ProcessStartSignParam.FlowInfo inputFlowInfo = new ProcessStartSignParam.FlowInfo();
        inputFlowInfo.setSignValidity(flowInfo.getSignValidity());
        inputFlowInfo.setSignValidityConfig(flowInfo.getSignValidityConfig());
        inputFlowInfo.setFileValidity(flowInfo.getFileValidity());
        inputFlowInfo.setFileValidityConfig(flowInfo.getFileValidityConfig());
        inputFlowInfo.setComment(flowInfo.getComment());
        inputFlowInfo.setContractType(flowInfo.getContractType());
        inputFlowInfo.setSealType(flowInfo.getSealType());
        inputFlowInfo.setRedirectUrl(flowInfo.getRedirectUrl());
        inputFlowInfo.setRedirectDelay(flowInfo.getRedirectDelay());
        inputFlowInfo.setSignedNoticeUrl(flowInfo.getSignedNoticeUrl());
        inputFlowInfo.setProcessNoticeUrl(flowInfo.getProcessNoticeUrl());
        inputFlowInfo.setSignFlowExtData(flowInfo.getSignFlowExtData());
        inputFlowInfo.setApproveTemplateId(flowInfo.getApproveTemplateId());
        inputFlowInfo.setProcessId(flowInfo.getProcessId());
        inputFlowInfo.setThirdProcessId(flowInfo.getThirdProcessId());
        inputFlowInfo.setBatchDropSeal(flowInfo.getBatchDropSeal());
        inputFlowInfo.setPayerAccountId(flowInfo.getPayerAccountId());
        inputFlowInfo.setDedicatedCloudId(flowInfo.getDedicatedCloudId());
        inputFlowInfo.setAllowRescind(flowInfo.getAllowRescind());
        return inputFlowInfo;
    }

    /**
     * 处理付费方账号id, 主要针对历史钉签发起的填写流程发起签署场景，需要重新计算付费方账号
     *
     * @param request
     */
    private void handlePayAccountId(ProcessStartSignParam request) {
        // 如果流程信息为空， 或者已指定付费方账号， 则直接跳过， 无需处理付费方账号id
        if (null == request.getFlowInfo()
                || StringUtils.isNotBlank(request.getFlowInfo().getPayerAccountId())) {
            return;
        }
        // 判断发起端， 非钉签场景无需处理付费方账号id
        if (!StringUtils.equals(DING_TALK.getClientId(), request.getSource())
                && !StringUtils.equals(DING_TALK_LABOR.getClientId(), request.getSource())) {
            return;
        }
        // 根据扩展信息查询钉签付费方账号id
        String extendJson = request.getFlowInfo().getSignFlowExtData();
        String payerAccountId = dingSignClient.parseDingPayerAccountId(extendJson);
        // 设置付费方账号id
        request.getFlowInfo().setPayerAccountId(payerAccountId);
    }
}

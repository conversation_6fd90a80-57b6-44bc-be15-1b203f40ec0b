package com.timevale.contractmanager.core.service.tracking.service;

import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.FileDetailBO;
import com.timevale.contractmanager.core.model.dto.request.SaveFlowTemplateRequest;
import com.timevale.contractmanager.core.service.tracking.consts.TrackingKeyConstant;
import com.timevale.contractmanager.core.service.tracking.enums.TrackingFieldEnum;
import com.timevale.contractmanager.core.service.tracking.service.base.BaseTrackingService;
import com.timevale.contractmanager.core.service.util.TemplateUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saas.tracking.bean.TrackingCollectBean;
import com.timevale.saas.tracking.service.custom.ICustomTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.core.service.tracking.consts.TrackingServiceConstant.FLOW_TEMPLATE_SAVE_TRACKING;
import static com.timevale.contractmanager.core.service.tracking.enums.TrackingFieldEnum.USE_DYNAMIC_HTML_TEMPLATE;

/**
 * <AUTHOR>
 * @since 2023-12-13 18:22
 */
@Slf4j
@Component(FLOW_TEMPLATE_SAVE_TRACKING)
public class FlowTemplateSaveTrackingService extends BaseTrackingService implements ICustomTrackingService {
    // 模版设置数据源设置
    private static final String FLOW_TEMPLATE_SET_DATA_SOURCE = "moban_guanlianshujuyuan";

    @Override
    public String distinctId() {
        return getAuthorizedOid();
    }

    @Override
    public String trackingName() {
        return FLOW_TEMPLATE_SAVE_TRACKING;
    }

    @Override
    public List<TrackingFieldEnum> trackingFields() {
        return Lists.newArrayList(USE_DYNAMIC_HTML_TEMPLATE);
    }

    @Override
    public Map<String, Object> buildTrackingFieldData(TrackingCollectBean trackingBean) {
        Map<String, Object> stringObjectMap = super.trackingFieldData();
        // 处理自定义业务字段
        SaveFlowTemplateRequest request = (SaveFlowTemplateRequest) trackingBean.getTrackingData();
        // 是否使用动态模板
        boolean useDynamicTemplate = false;
        List<FileDetailBO> files = request.getFiles();
        if (CollectionUtils.isNotEmpty(files)) {
            List<FileBO> fileBOS = files.stream().map(fileDetailBO -> (FileBO) fileDetailBO).collect(Collectors.toList());
            useDynamicTemplate = TemplateUtils.hasDynamicTemplate(fileBOS);
        }

        // 数据源埋点
        if (request.getDataSourceConfig() != null && CollectionUtils.isNotEmpty(request.getDataSourceConfig().getDataSources())) {
            doTracking(request.getSubjectOid(), FLOW_TEMPLATE_SET_DATA_SOURCE, stringObjectMap);
        }

        // 其它埋点
        stringObjectMap.put(USE_DYNAMIC_HTML_TEMPLATE.getKey(), useDynamicTemplate);
        return stringObjectMap;
    }

}

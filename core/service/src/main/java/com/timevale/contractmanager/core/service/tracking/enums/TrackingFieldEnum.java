package com.timevale.contractmanager.core.service.tracking.enums;
/**
 * <AUTHOR>
 * @since 2023-12-13 18:16
 */
public enum TrackingFieldEnum {
    // public
    PLATFORM_TYPE(
            "微信小程序：WE_CHAT,支付宝小程序：ALI_PAY_MINI,安卓app：APP_ANDROID,ios app：APP_IOS,H5：H5,WEB：WEB,钉钉微应用：DING_TALK,开放服务：OPEN_SERVICE",
                    "PlatformType",
                    "platformType"),

    APP_ID("应用id","appId", "appId"),
    SET_TYPE("设置类型", "set_type", ""),

    ENTITY_TYPE("主体类型", "entityType",""),

    ENTITY_NAME("主体名称", "entityName",""),

    OPERATOR_OID("操作人oid","operatorOid", "operatorOid" ),

    OPERATOR_GID("操作人gid","operatorGid", "operatorGid" ),

    AUTHORIZED_OID("操作主体oid","authorizedOid", "authorizedOid"),

    AUTHORIZED_GID("操作人主体gid","authorizedGid", "authorizedGid" ),

    USER_ROLE("SaaS用户角色", "user_role", ""),

    OPERATOR_ENTITY_NUM("操作人归属主体数量", "operatorEntityNum", ""),

    MODULE("功能模块", "Module", ""),

    IS_FIRST_TIME("是否首次触发事件", "is_first_time", ""),
    VIP_VERSION("会员版本","vipVersion", "" ),
    // flowTemplate
    USE_DYNAMIC_HTML_TEMPLATE("使用动态html模板", "Present_Dynamic_HTML_Template", ""),
    // menu
    FOLDER_NAME("分类名称", "folder_name", ""),
    FATHER_FOLDER_NAME("父分类名称", "father_folder_name", ""),
    FOLDER_CLASS("分类class", "folder_class", ""),
    IS_RELATED_STANDING_BOOK_RULE("是否关联台账规则", "is_related_standing_book_rule", ""),
    STANDING_BOOK_NAME("台账名称", "standing_book_name", ""),
    IS_AUTO_CLASSIFY("是否自动", "is_auto_classify", ""),
    CLASSIFY_CONDITION("条件", "classify_condition", ""),
    IS_UPDATE_HISTORY("是否历史更新", "is_update_history", ""),
    FOLDER_DEPT("分类深度", "Folder_Hierarchy", "")
    ;

    /** 埋点key （产品提供） */
    private String key;
    /** 埋点映射字段 */
    private String field;
    /** 字段备注 */
    private String desc;

    TrackingFieldEnum(String desc, String key, String field) {
        this.key = key;
        this.field = field;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getField() {
        return field;
    }
}

package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessShowMsgEntity;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateIsShowParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by tianlei on 2022/5/24
 */
@Slf4j
@Component
public class ProcessShowDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processTopicName(), ProcessChangeTagEnum.PROCESS_SHOW.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessShowMsgEntity entity = JsonUtils.json2pojo(data, ProcessShowMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {

        ProcessShowMsgEntity msg = (ProcessShowMsgEntity) collectContext.getData();

        if (StringUtils.isBlank(msg.getFlowId())) {
            log.info(ProcessDataCollectSupport.LOG_PREFIX + "processId : {} flowId not exist", collectContext.getProcessId());
            return;
        }
        //
        ContractProcessUpdateIsShowParam updateParam = new ContractProcessUpdateIsShowParam();
        updateParam.setProcessId(msg.getProcessId());
        updateParam.setShow(msg.getIsShow());
        updateParam.setFlowId(msg.getFlowId());
        contractProcessWriteClient.contractProcessUpdateIsShow(updateParam);
    }

}

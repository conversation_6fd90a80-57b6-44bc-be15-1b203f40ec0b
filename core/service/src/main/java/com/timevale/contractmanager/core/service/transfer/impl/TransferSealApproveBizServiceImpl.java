package com.timevale.contractmanager.core.service.transfer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.contractapproval.facade.input.QueryApprovalPendingCountInput;
import com.timevale.contractapproval.facade.output.QueryApprovalPendingCountOutput;
import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.ApprovalInstanceClient;
import com.timevale.contractmanager.core.model.bo.transfer.ProcessTransferNoticeBO;
import com.timevale.contractmanager.core.model.bo.transfer.SealTransferNodeCountBO;
import com.timevale.contractmanager.core.model.bo.transfer.SingleTransferResultBO;
import com.timevale.contractmanager.core.model.bo.transfer.TransferUserListBO;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.response.transfer.TransferSealNodeCountResponse;
import com.timevale.contractmanager.core.model.dto.transfer.TransferResultDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.transfer.ApprovalTransferAbstractBizService;
import com.timevale.contractmanager.core.service.transfer.TransferSealApproveBizService;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.TRANSFER_TRANSFER_COUNT_MISS_ACCOUNT;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.USER_ACCOUNT_NOT_EXIST;

/**
 * <AUTHOR>
 * @since 2022/11/25
 */
@Slf4j
@Service
public class TransferSealApproveBizServiceImpl extends ApprovalTransferAbstractBizService
        implements TransferSealApproveBizService {

    @Resource private UserCenterService userCenterService;
    @Resource private ApprovalInstanceClient approvalInstanceClient;

    @Override
    public Boolean isSupport(Integer transferScene) {
        return null != transferScene && TransferSceneEnum.SEAL_APPROVE.getCode() == transferScene;
    }

    @Override
    public TransferResultDTO transfer(TransferBizContext transferBizContext) {
        return approvalTransfer(transferBizContext, TransferSceneEnum.SEAL_APPROVE);
    }

    @Override
    public Map<String, Long> transferCount(
            UserAccountDetail tenantAccount, List<Account> userAccounts) {
        if (CollectionUtils.isEmpty(userAccounts)) {
            throw new BizContractManagerException(TRANSFER_TRANSFER_COUNT_MISS_ACCOUNT);
        }
        // key:oid,value:数量
        Map<String, Long> fullSealApproveMapCount =
                Maps.newHashMapWithExpectedSize(userAccounts.size());
        for (Account userAccount : userAccounts) {
            // 获取gid,为空则不查询用印审批数
            if (StringUtils.isBlank(userAccount.getGid())) {
                fullSealApproveMapCount.put(userAccount.getOid(), 0L);
                continue;
            }
            QueryApprovalPendingCountInput input = new QueryApprovalPendingCountInput();
            input.setOrgOid(tenantAccount.getAccountOid());
            input.setApproverGid(userAccount.getGid());
            input.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
            QueryApprovalPendingCountOutput output = approvalInstanceClient.queryApprovalPendingCount(input);
            int count = output.getCommonNodeCount() + output.getSealRoleNodeCount();
            fullSealApproveMapCount.put(userAccount.getOid(), Long.valueOf(count));
        }
        return fullSealApproveMapCount;
    }

    @Override
    public OrgDeptListResponse transferUserList(TransferUserListBO transferUserListBO) {
        return super.approvalTransferUserList(transferUserListBO);
    }


    @Override
    public Integer transferToUserCount(String tenantId, String accountOid) {
        return super.transferToUserCount(tenantId, accountOid, TransferSceneEnum.SEAL_APPROVE);
    }

    @Override
    public Long addTransferToUserCount(String tenantId, String accountOid) {
        return super.addTransferToUserCount(tenantId, accountOid, TransferSceneEnum.SEAL_APPROVE);
    }

    @Override
    public void singleTransferResultCallback(SingleTransferResultBO transferResultBO) {
        log.info("seal approval singleTransferResultCallback, param: {}", JSONObject.toJSONString(transferResultBO));
        super.approvalTransferResultCallback(transferResultBO, TransferSceneEnum.SEAL_APPROVE);
    }

    @Override
    public TransferSealNodeCountResponse sealNodeCount(SealTransferNodeCountBO request) {
        // 包含已注销账号
        Map<String, UserAccountDetail> accountDetailMap =
                userCenterService.queryFatAccountMapByAccountIds(
                        Collections.singletonList(request.getTransferUser()));
        if (MapUtils.isEmpty(accountDetailMap)) {
            throw new BizContractManagerException(USER_ACCOUNT_NOT_EXIST);
        }
        QueryApprovalPendingCountInput input = new QueryApprovalPendingCountInput();
        input.setOrgOid(request.getTenantId());
        input.setApproverGid(accountDetailMap.get(request.getTransferUser()).getAccountGid());
        input.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
        input.setApprovalIds(request.getSealApproveIds());
        QueryApprovalPendingCountOutput output =
                approvalInstanceClient.queryApprovalPendingCount(input);
        TransferSealNodeCountResponse response = new TransferSealNodeCountResponse();
        response.setPersonalNodeCount(output.getCommonNodeCount());
        response.setFinalNodeCount(output.getSealRoleNodeCount());
        return response;
    }

    @Override
    protected ApprovalTypeEnum approvalType() {
        return ApprovalTypeEnum.SEAL;
    }

    @Override
    protected String transferReason(TransferBizContext transferBizContext) {
        return transferBizContext.getSealApprovalTransferReason();
    }

    @Override
    protected String transferFailReason(long successCount, long totalCount) {
        if (successCount == 0) {
            return String.format("用印审批转交失败，失败原因可能是：被转交人无权限用印，被转交的审批流已被审批、撤回或拒绝");
        }
        return String.format("已成功转交%s条用印审批任务，转交失败%s条，失败原因可能是：被转交人无权限用印，被转交的审批流已被审批、撤回或拒绝", successCount, totalCount - successCount);
    }

    @Override
    protected void customizeTransferCompleteCallback(SingleTransferResultBO transferResultBO, long successCount) {
        // 1 发放消息通知
        String transferNotifyKey = CacheUtil.transferNoticeKey(transferResultBO.getTaskId());
        sendProcessTransferNotify(
                getTransferNoticeBOFromCache(transferNotifyKey),
                successCount,
                TransferSceneEnum.SEAL_APPROVE.getNotifyTemplateName());
        // 2 移除无用缓存key
        TedisUtil.delete(transferNotifyKey);
    }

    /** 1 缓存全量转交频率 2 缓存当前task 任务总数 3 缓存当前task转交完成之后的通知用户消息信息 */
    @Override
    protected void setCustomizeTransferCache(TransferBizContext transferBizContext) {
        TransferBizContext.TransferUserInfo transferUserInfo = transferBizContext.getTransferUserInfo();
        String taskId = transferBizContext.getTaskId();
        // 缓存任务完成之后通知消息
        ProcessTransferNoticeBO noticeBO =
                ProcessTransferNoticeBO.builder()
                        .transferToAccount(transferUserInfo.getTransferToAccount())
                        .operatorAccount(transferUserInfo.getOperatorAccount())
                        .tenantAccount(transferUserInfo.getTenantAccount())
                        .isSystemTransfer(transferBizContext.isSystemTransfer())
                        .build();
        TedisUtil.set(CacheUtil.transferNoticeKey(taskId), JSON.toJSONString(noticeBO), 1, TimeUnit.DAYS);
    }

    @Override
    protected void clearCustomizeTransferCache(TransferBizContext transferBizContext) {
        String taskId = transferBizContext.getTaskId();
        try {
            TransferBizContext.TransferUserInfo transferUserInfo = transferBizContext.getTransferUserInfo();
            // 主体账户信息
            UserAccountDetail tenantAccountDetail = transferUserInfo.getTenantAccount();
            // 当前转交人oid
            UserAccountDetail transferUser = getApprovalTransferAccount(transferBizContext);
            List<String> deleteKeys = new ArrayList<>();
            // 1 全部转交频率key
            if (!transferBizContext.isPartTransfer()) {
                deleteKeys.add(
                        CacheUtil.userTransferCheckKey(
                                tenantAccountDetail.getAccountOid(),
                                transferUser.getAccountOid(),
                                TransferSceneEnum.SEAL_APPROVE.getCode()));
            }
            // 2 任务完成之后通知消息
            deleteKeys.add(CacheUtil.transferNoticeKey(taskId));
            TedisUtil.delete(deleteKeys.toArray(new String[0]));
        } catch (Exception e) {
            log.warn("clear seal transfer cache failed,taskId:{}", taskId, e);
        }
    }

    @Override
    protected List<String> partTransferApprovalIds(TransferBizContext transferBizContext) {
        TransferBizContext.TransferProcessListInfo transferProcessListInfo = transferBizContext.getTransferProcessListInfo();
        if (null == transferProcessListInfo || CollectionUtils.isEmpty(transferProcessListInfo.getTransferProcessList())) {
            return Lists.newArrayList();
        }
        return transferProcessListInfo.getTransferProcessList();
    }
}

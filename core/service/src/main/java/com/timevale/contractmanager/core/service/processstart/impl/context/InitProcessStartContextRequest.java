package com.timevale.contractmanager.core.service.processstart.impl.context;

import com.timevale.contractmanager.core.model.dto.process.config.ProcessStartBizRuleConfig;
import lombok.Data;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/2 18:01
 */
@Data
public class InitProcessStartContextRequest {

    private String operatorOid;
    private String accountOid;
    private String subjectOid;
    private String spaceOid;
    private String clientId;
    private String appName;
    private String appId;
    private String flowTemplateId;

    private Integer scene;


    /**
     * 会员功能code
     */
    private List<String> functionCodes;

    /**
     * 偏好设置的key
     */
    private List<String> preferenceKeys;


    /**
     * 业务规则
     */
    private ProcessStartBizRuleConfig startBizRuleConfig;

}

package com.timevale.contractmanager.core.service.resourceshare.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.core.service.resourceshare.actions.DoResourceShareActionService;
import com.timevale.mandarin.common.annotation.RestService;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceUrlActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoProcessParticipantAuthRequestDTO;
import com.timevale.saas.common.manage.spi.dto.request.share.DoResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceShareActionResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceUrlActionResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoProcessParticipantAuthResponseDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoResourceShareActionResponseDTO;
import com.timevale.saas.common.manage.spi.extender.DoGetResourceShareActionPluginExtender;
import com.timevale.saas.common.manage.spi.extender.DoGetResourceUrlActionPluginExtender;
import com.timevale.saas.common.manage.spi.extender.DoProcessParticipantAuthPluginExtender;
import com.timevale.saas.common.manage.spi.extender.DoResourceShareActionPluginExtender;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/11/27
 */
@Slf4j
@RestService
public class ResourceSharePluginExtenderImpl
        implements DoResourceShareActionPluginExtender,
                DoGetResourceShareActionPluginExtender,
                DoGetResourceUrlActionPluginExtender,
                DoProcessParticipantAuthPluginExtender {

    @Autowired private List<DoResourceShareActionService> doResourceShareActionServices;

    @Override
    public DoResourceShareActionResponseDTO doResourceShareAction(
            DoResourceShareActionRequestDTO request) {
        log.info("receive resource share request:{}", JSON.toJSONString(request));
        return factory(request.getResourceType()).doResourceShareAction(request);
    }

    @Override
    public DoGetResourceShareActionResponseDTO doGetResourceShareAction(
            DoGetResourceShareActionRequestDTO request) {
        return factory(request.getResourceType()).doGetResourceShareAction(request);
    }

    @Override
    public DoGetResourceUrlActionResponseDTO doGetResourceUrlAction(
            @RequestBody DoGetResourceUrlActionRequestDTO request) {
        return factory(request.getResourceType()).doGetResourceUrlAction(request);
    }

    @Override
    public DoProcessParticipantAuthResponseDTO doProcessParticipantAuth(DoProcessParticipantAuthRequestDTO requestDTO) {
        return factory(requestDTO.getResourceType()).doProcessParticipantAuth(requestDTO);
    }

    private DoResourceShareActionService factory(String resourceType) {
        for (DoResourceShareActionService doResourceShareActionService :
                doResourceShareActionServices) {
            if (doResourceShareActionService.getResourceType().equals(resourceType)) {
                return doResourceShareActionService;
            }
        }
        throw new SaasCommonBizException(ResultEnum.RESOURCE_SHARE_NOT_EXIST);
    }
}

package com.timevale.contractmanager.core.service.processstart.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.other.DocManagerService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.DesensitizationUtil;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.doccooperation.service.model.DocTemplate;
import com.timevale.doccooperation.service.model.FlowInfo;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.tool.ValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;

/**
 * <AUTHOR>
 * @since 2021-08-20
 */
@Slf4j
@Service
public class ProcessStartChecker {

    /** 抄送人名称 */
    private static final String CC_LABEL = "抄送人";
    /** 签署人名称 */
    private static final String PARTICIPANT_LABEL = "签署人";

    @Autowired SystemConfig systemConfig;
    @Autowired DocManagerService docManagerService;
    @Autowired UserCenterService userCenterService;
    /**
     * 校验发起的签署文件合法性
     *
     * @param files 签署文件列表
     */
    public void checkContractFile(List<? extends FileBO> files) {
        if (CollectionUtils.isEmpty(files)) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "至少上传一个文件");
        }

        Set<String> fileIds = files.stream().map(i -> i.getFileId()).collect(Collectors.toSet());
        if (fileIds.size() != files.size()) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "文件不能重复");
        }

        // 合同文件最大个数校验
        if (files.size() > systemConfig.getContractFileMax()) {
            throw new BizContractManagerException(
                    PROCESS_ILLEGAL_PARAM,
                    String.format("最大支持%d个文件", systemConfig.getContractFileMax()));
        }
    }

    /**
     * 校验发起的附件合法性
     *
     * @param files 附件列表
     */
    public void checkAttachmentFile(List<? extends FileBO> files) {

        if (CollectionUtils.isEmpty(files)) {
            return;
        }

        Set<String> fileIds = files.stream().map(i -> i.getFileId()).collect(Collectors.toSet());
        if (fileIds.size() != files.size()) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "文件不能重复");
        }

        // 合同文件最大个数校验
        if (files.size() > systemConfig.getContractFileMax()) {
            throw new BizContractManagerException(
                    PROCESS_ILLEGAL_PARAM,
                    String.format("最大支持%d个文件", systemConfig.getContractFileMax()));
        }
        // 附件最大个数校验
        if (CollectionUtils.isNotEmpty(files)
                && files.size() > systemConfig.getAttachmentFileMax()) {
            throw new BizContractManagerException(
                    PROCESS_ILLEGAL_PARAM,
                    String.format("最大支持%d个附件", systemConfig.getAttachmentFileMax()));
        }
    }

    /**
     * 校验发起的参与方/抄送方实例
     *
     * @param participants 参与方列表
     * @param ccs 抄送方列表
     * @param batchStart 是否批量发起场景
     * @param clientId 请求头中的来源
     */
    public void checkParticipantAndCcs(
            List<ParticipantBO> participants, List<UserBO> ccs, boolean batchStart, String clientId) {
        if (CollectionUtils.isEmpty(participants)) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "参与方信息不能为空");
        }

        List<UserBO> processUsers = Lists.newArrayList();
        Map<Integer,Set<String>> participantLabelsMap= new HashMap<>();
        // 筛选参与方账号oid, 并校验参与方实例数量
        for (ParticipantBO participant : participants) {
            List<ParticipantInstanceBO> instances = participant.getInstances();
            // 判断是否或签
            boolean isOrSign = ParticipantModeEnum.isOrSign(participant.getParticipantMode());
            // 校验发起方实例信息，非或签、非批量发起场景下，流程发起每个参与方实例只能有一个
            if (CollectionUtils.isEmpty(instances) || (!batchStart && !isOrSign && instances.size() > 1)) {
                throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "参与方数量不合法");
            }
            checkParticipantSubjectTypeLabel(participant,participantLabelsMap);
            if (isOrSign) {
                processUsers.addAll(instances);
            } else {
                processUsers.add(instances.get(0));
            }
        }

        if (CollectionUtils.isNotEmpty(ccs)) {
            processUsers.addAll(ccs);
        }
        // 获取参与方/抄送方实例中所有的oid
        Set<String> oidSet = Sets.newHashSet();
        for (UserBO userBO : processUsers) {
            if (StringUtils.isNotBlank(userBO.getAccountOid())) {
                oidSet.add(userBO.getAccountOid());
            }
            if (StringUtils.isNotBlank(userBO.getSubjectId())) {
                oidSet.add(userBO.getSubjectId());
            }
            userBO.setRegisterSource(clientId);
        }
        if (oidSet.isEmpty()) {
            log.info("no participant/cc oid, skip check...");
            return;
        }
        // 批量查询oid信息， 用于参与方账号校验（含已注销账号）
        Map<String, UserAccountDetail> oidAccountMap =
                userCenterService.queryFatAccountMapByAccountIds(oidSet);

        // 检查参与方账号
        participants.forEach(
                participant -> {
                    if (CollectionUtils.isEmpty(participant.getInstances())) {
                        return;
                    }
                    List<ParticipantInstanceBO> instances = participant.getInstances();
                    if (instances.size() == 1 || ParticipantModeEnum.isOrSign(participant.getParticipantMode())) {
                        checkParticipantAccount(participant, oidAccountMap);
                    } else {
                        // 如果是批量，只检查第一个，其他放到消费者去检查
                        checkAccount(instances.get(0), oidAccountMap, PARTICIPANT_LABEL);
                    }
                });
        // 检查抄送人账号
        if (CollectionUtils.isNotEmpty(ccs)) {
            ccs.forEach(cc -> checkAccount(cc, oidAccountMap, CC_LABEL));
        }
    }

    /**
     * 校验不同参与主体的参与人名称不能重复
     * @param participant
     * @param participantLabelsMap
     */
    public void checkParticipantSubjectTypeLabel(ParticipantBO participant,Map<Integer,Set<String>> participantLabelsMap){
        Set<String> labelSet = participantLabelsMap.get(participant.getParticipantSubjectType());
        if(CollectionUtils.isNotEmpty(labelSet)&&labelSet.contains(participant.getParticipantLabel())){
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "参与方名称不能重复");
        }

        if(CollectionUtils.isEmpty(labelSet)){
            Set<String> participantLabels = Sets.newHashSet();
            participantLabels.add(participant.getParticipantLabel());
            participantLabelsMap.put(participant.getParticipantSubjectType(),participantLabels);
        }

        if(CollectionUtils.isNotEmpty(labelSet)){
            labelSet.add(participant.getParticipantLabel());
        }
    }

    /**
     * 校验流程模板下的文件合法性
     *
     * @param flowTemplateDetail
     * @param startFiles
     * @param checkOwnerAndStatus 是否校验文件归属和文件状态
     * @param fileOwner
     */
    public void checkFilesWithFlowTemplate(
            GetFlowTemplateResult flowTemplateDetail,
            List<? extends FileBO> startFiles,
            boolean checkOwnerAndStatus,
            UserAccount fileOwner,
            String dedicatedCloudId) {
        // 1, 首先校验下文件是否有重复等基础信息
        List<FileBO> normalFiles = checkFile(startFiles);
        // 2， 需要校验文件拥有者
        if (checkOwnerAndStatus && fileOwner != null && CollectionUtils.isNotEmpty(normalFiles)) {
            long time = System.currentTimeMillis();
            log.info("start check start files...");
            checkOwnerAndStatus(flowTemplateDetail,normalFiles, fileOwner, dedicatedCloudId);
            log.info(
                    "start check start files end time = {} ms ", System.currentTimeMillis() - time);
        }
    }

    private void checkOwnerAndStatus(GetFlowTemplateResult flowTemplateDetail, List<? extends FileBO> startFiles,
                                     UserAccount fileOwner, String dedicatedCloudId) {
        // 授权的模版需要进行 文件校验
        if(null != flowTemplateDetail && flowTemplateDetail.isShared()){
            UserAccount account = new UserAccount();
            account.setAccountOid(flowTemplateDetail.getOwnerOid());
            account.setAccountGid(flowTemplateDetail.getOwnerGid());
            List<DocTemplate> docTemplates = flowTemplateDetail.getDocTemplates();
            List<FlowInfo.Attachment> attachments = flowTemplateDetail.getFlowInfo().getAttachments();
            //过滤出模板文件
            Set<String> flowTemplateFileIds = Sets.newHashSet();
            if(CollectionUtils.isNotEmpty(docTemplates)){
                flowTemplateFileIds.addAll(docTemplates.stream().map(DocTemplate::getTemplateId).collect(Collectors.toSet()));
            }
            if(CollectionUtils.isNotEmpty(attachments)){
                flowTemplateFileIds.addAll(attachments.stream().map(FlowInfo.Attachment::getFileId).collect(Collectors.toSet()));
            }
            // 校验模板文件
            List<? extends FileBO> flowTemplateFiles = startFiles.stream().filter(fileBO ->flowTemplateFileIds.contains(fileBO.getFileId())).collect(Collectors.toList());
            //校验文件状态
            docManagerService.checkFileOwnerAndStatus(flowTemplateFiles, account, dedicatedCloudId);
            startFiles.removeIf(fileBO -> flowTemplateFileIds.contains(fileBO.getFileId()));
        }

        //校验文件归属和状态
        docManagerService.checkFileOwnerAndStatus(startFiles, fileOwner, dedicatedCloudId);
    }

    /**
     * 校验发起的文件合法性
     *
     * @param startFiles 发起文件列表
     * @return 按照文件类型分类 是合同文件还是附件
     */
    public List<FileBO> checkFile(List<? extends FileBO> startFiles) {
        // 根据文件类型分组下 拆分出不同类型的文件列表
        if (CollectionUtils.isEmpty(startFiles)) {
            return null;
        }

        List<FileBO> normalFiles =
                startFiles.stream()
                        .filter(fileBO -> fileBO.getFileDataSourceConfig() == null)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalFiles)) {
            return null;
        }

        Map<Integer, List<FileBO>> group =
                normalFiles.stream().collect(Collectors.groupingBy(FileBO::getFileType));

        group.forEach(
                (fileType, files) -> {
                    if (CollectionUtils.isNotEmpty(files)) {
                        Set<String> fileIds =
                                files.stream().map(i -> i.getFileId()).collect(Collectors.toSet());
                        if (fileIds.size() != files.size()) {
                            throw new BizContractManagerException(
                                    BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM,
                                    "文件不能重复");
                        }
                    }

                    if (ProcessFileType.CONTRACT_FILE.getType() == fileType) {
                        // 合同文件最大个数校验
                        if (CollectionUtils.isEmpty(files)) {
                            throw new BizContractManagerException(
                                    BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM,
                                    "至少上传一个文件");
                        } else {
                            if (files.size() > systemConfig.getContractFileMax()) {
                                throw new BizContractManagerException(
                                        BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM,
                                        String.format(
                                                "最大支持%d个文件", systemConfig.getContractFileMax()));
                            }
                        }

                    } else if (ProcessFileType.ATTACHMENT_FILE.getType() == fileType) {
                        // 附件最大个数校验
                        if (CollectionUtils.isNotEmpty(files)
                                && files.size() > systemConfig.getAttachmentFileMax()) {
                            throw new BizContractManagerException(
                                    BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM,
                                    String.format(
                                            "最大支持%d个附件", systemConfig.getAttachmentFileMax()));
                        }
                    } else {
                        throw new BizContractManagerException(
                                BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "文件类型不正确");
                    }
                });
        return normalFiles;
    }

    /**
     * 校验参与方信息，如果账号不存在或已注销，抛出异常提示
     *
     * @param participantBO 参与方
     * @param accountMap Map<oid, AccountDetail>，包含个人、主体信息(含已注销)
     */
    public void checkParticipantAccount(
            ParticipantBO participantBO, Map<String, UserAccountDetail> accountMap) {
        if (Objects.isNull(participantBO) || CollectionUtils.isEmpty(participantBO.getInstances())) {
            return;
        }

        List<ParticipantInstanceBO> instances = participantBO.getInstances();

        // 校验当前参与方下用户的账号，包含主体与个人账号信息
        for (ParticipantInstanceBO userBO : instances) {
            checkAccount(userBO, accountMap, PARTICIPANT_LABEL);
        }
    }

    /**
     * 校验参与方账号有效性
     *
     * @param userBO 抄送人账号
     * @param accountMap Map<oid, AccountDetail>，包含个人、主体信息(含已注销)
     */
    public void checkAccount(
            UserBO userBO, Map<String, UserAccountDetail> accountMap, String participantLabel) {
        if (Objects.isNull(userBO)) {
            return;
        }

        // 校验参与人账号
        if (StringUtils.isNotBlank(userBO.getAccountOid())) {
            UserAccountDetail accountDetail = accountMap.get(userBO.getAccountOid());

            checkPersonAccount(accountDetail, userBO, participantLabel);

            // 如果参与人账号姓名不为空， 优先使用账号姓名
            if (StringUtils.isNotBlank(accountDetail.getAccountName())) {
                userBO.setAccountName(accountDetail.getAccountName());
            }
            // 如果用户未指定登录手机号邮箱， 默认获取账号中的登录手机或邮箱
            if (StringUtils.isBlank(userBO.getAccount())) {
                userBO.setAccount(accountDetail.loginAccount());
            }
            userBO.setAccountGid(accountDetail.getAccountGid());
            userBO.setAccountUid(accountDetail.getAccountUid());
        }

        // 校验参与主体账号是否存在
        if (StringUtils.isNotBlank(userBO.getSubjectId())) {
            UserAccountDetail subjectDetail = accountMap.get(userBO.getSubjectId());

            checkSubjectAccount(subjectDetail, userBO, participantLabel);

            // 如果参与主体账号名称不为空， 优先使用账号名称
            if (StringUtils.isNotBlank(subjectDetail.getAccountName())) {
                userBO.setSubjectName(subjectDetail.getAccountName());
            }
            userBO.setSubjectGid(subjectDetail.getAccountGid());
            userBO.setSubjectUid(subjectDetail.getAccountUid());
        }

    }

    /**
     * 检查个人账号信息
     *
     * @param person 个人账号信息
     * @param userBO 用户传入的账号信息
     * @param participantLabel 参与方名称，主要用于异常提示
     */
    private void checkPersonAccount(
            UserAccountDetail person, UserBO userBO, String participantLabel) {
        // 校验参与人账号是否存在
        if (person == null) {
            if (Objects.isNull(userBO)
                    || StringUtils.isAllBlank(userBO.getAccountName(), userBO.getAccount())) {
                throw new BizContractManagerException(USER_ACCOUNT_NOT_EXIST);
            }

            BizContractManagerResultCodeEnum codeEnum =
                    CC_LABEL.equals(participantLabel)
                            ? FLOW_TEMPLATE_CC_DELETED
                            : FLOW_TEMPLATE_SIGNER_DELETED;
            throw new BizContractManagerException(
                    codeEnum,
                    Optional.ofNullable(userBO.getAccountName()).orElse(""),
                    Optional.ofNullable(userBO.getAccount()).orElse(""));
        }

        // 账号注销
        if (person.isDeleted()) {
            BizContractManagerResultCodeEnum codeEnum =
                    CC_LABEL.equals(participantLabel)
                            ? FLOW_TEMPLATE_CC_DELETED
                            : FLOW_TEMPLATE_SIGNER_DELETED;
            throw new BizContractManagerException(
                    codeEnum,
                    Optional.ofNullable(userBO.getAccountName()).orElse(""),
                    Optional.ofNullable(userBO.getAccount()).orElse(""));
        }
        // 获取参与方名称，补充默认名称
        participantLabel = StringUtils.defaultIfBlank(participantLabel, "签署方");
        // 判断账号是否匹配， 如果不匹配，报错
        if (checkAccountNotMatched(userBO, person)) {
            String label = ValidateUtil.emailValid(userBO.getAccount()) ? "邮箱" : "手机号";
            String participantName = StringUtils.defaultIfBlank(userBO.obtainPopName(), "-");
            throw new BizContractManagerException(
                    PARTICIPANT_ACCOUNT_NOT_MATCHED, participantLabel, participantName, label);
        }

        // 判断姓名是否匹配，如果不匹配，报错
        if (StringUtils.isNoneBlank(userBO.getAccountName(), person.getAccountName())
                && !StringUtils.equals(userBO.getAccountName(), person.getAccountName())) {
            // 如果填入的账号不为空， 提示文案优化
            String account = userBO.getAccount();
            if (StringUtils.isNotBlank(account)) {
                String label = ValidateUtil.emailValid(account) ? "邮箱" : "手机号";
                throw new BizContractManagerException(PARTICIPANT_NAME_NOT_MATCHED, label, account, DesensitizationUtil.desensitizeName(person.getAccountName()));
            }
            throw new BizContractManagerException(
                    PARTICIPANT_NAME_NOT_MATCHED, participantLabel, userBO.getAccountName(), DesensitizationUtil.desensitizeName(person.getAccountName()));
        }
    }

    /**
     * 检查主体账号是否可用
     *
     * @param subject 企业信息
     * @param participantLabel 参与方名称，主要用于异常提示
     */
    private void checkSubjectAccount(
            UserAccountDetail subject, UserBO userBO, String participantLabel) {
        // 账号信息未查到
        if (subject == null) {
            if (Objects.isNull(userBO) || StringUtils.isBlank(userBO.getSubjectName())) {
                throw new BizContractManagerException(USER_ACCOUNT_NOT_EXIST);
            }

            BizContractManagerResultCodeEnum codeEnum =
                    CC_LABEL.equals(participantLabel)
                            ? FLOW_TEMPLATE_CC_DELETED
                            : FLOW_TEMPLATE_SIGNER_DELETED;
            throw new BizContractManagerException(
                    codeEnum, Optional.ofNullable(userBO.getSubjectName()).orElse(""), "");
        }

        // 账号已注销
        if (subject.isDeleted()) {
            String subjectName =
                    Optional.ofNullable(userBO.getSubjectName())
                            .filter(StringUtils::isNotBlank)
                            .orElse(subject.getAccountName());

            BizContractManagerResultCodeEnum codeEnum =
                    CC_LABEL.equals(participantLabel)
                            ? FLOW_TEMPLATE_CC_DELETED
                            : FLOW_TEMPLATE_SIGNER_DELETED;
            throw new BizContractManagerException(
                    codeEnum, Optional.ofNullable(subjectName).orElse(""), "");
        }
    }

//    /**
//     * 是否指定意愿方式
//     *
//     * @param tenant
//     * @param participants
//     */
//    public boolean isAssignedWillType(UserAccount tenant, List<ParticipantBO> participants) {
//        // 查询全局意愿认证配置, 默认全部可用
//        List<String> availableWillTypes = WillTypeEnum.allWillTypes();
//        // 如果发起主体是企业， 获取合同偏好设置中指定的可用意愿认证方式
//        if (tenant.isOrganize()) {
//            QueryPreferenceRequest preferenceRequest = new QueryPreferenceRequest();
//            preferenceRequest.setOrgId(tenant.getAccountOid());
//            preferenceRequest.setOrgGid(tenant.getAccountGid());
//            preferenceRequest.setPreferenceKeys(Lists.newArrayList(PROCESS_WILL_TYPES.getKey()));
//            PreferenceResponse preferenceResponse =
//                    preferencesService.queryByCondition(preferenceRequest);
//            Preference preference = preferenceResponse.getPreferences().get(0);
//            // 重置可用意愿方式
//            availableWillTypes.clear();
//            availableWillTypes.addAll(IdsUtil.getIdList(preference.getPreferenceValue()));
//        }
//        // 移除视频认证, 这个意愿方式已删除
//        availableWillTypes.remove(WillTypeEnum.VIDEO.getType());
//
//        // 判断参与方指定的意愿方式是否包含所有可用意愿方式， 如果存在未包含的场景， 说明当前参与方指定了意愿认证方式
//        return participants.stream()
//                .map(ParticipantBO::getWillTypes)
//                .filter(CollectionUtils::isNotEmpty)
//                .anyMatch(willTypes -> !willTypes.containsAll(availableWillTypes));
//    }

    /**
     * 校验二要素是否不匹配， 如果不匹配， 返回true, 其他场景返回false
     *
     * @param userBO
     * @param accountDetail
     * @return
     */
    private boolean checkAccountNotMatched(UserBO userBO, UserAccountDetail accountDetail) {
        // 如果参与人为企业或者未指定账号， 跳过校验
        if (accountDetail.isOrganize() || StringUtils.isBlank(userBO.getAccount())) {
            return false;
        }
        String accountMobile = accountDetail.getAccountMobile();
        String accountEmail = accountDetail.getAccountEmail();
        // 如果账号没有手机号邮箱， 跳过校验
        if (StringUtils.isBlank(accountEmail) && StringUtils.isBlank(accountMobile)) {
            return false;
        }
        // 判断指定的account和账号信息中的手机号邮箱是否匹配， 匹配则跳过
        if (userBO.getAccount().equalsIgnoreCase(accountEmail)
                || userBO.getAccount().equalsIgnoreCase(accountMobile)) {
            return false;
        }
        log.info(
                "input oid:{} account:{} accountMobile:{} accountEmail:{}",
                userBO.getAccountOid(),
                userBO.getAccount(),
                accountMobile,
                accountEmail);
        return true;
    }
}

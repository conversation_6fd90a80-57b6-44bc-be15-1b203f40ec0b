package com.timevale.contractmanager.core.service.autoarchive.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.contractanalysis.facade.api.bo.FieldResult;
import com.timevale.contractanalysis.facade.api.bo.FormListResult;
import com.timevale.contractanalysis.facade.api.bo.FormResult;
import com.timevale.contractanalysis.facade.api.enums.LedgerFormTypeEnum;
import com.timevale.contractmanager.common.dal.bean.autoarchive.AutoArchiveOperatorConfigDO;
import com.timevale.contractmanager.common.dal.bean.autoarchive.AutoArchiveOperatorDO;
import com.timevale.contractmanager.common.dal.bean.autoarchive.AutoArchiveRuleDO;
import com.timevale.contractmanager.common.dal.bean.grouping.AiRuleMenuDO;
import com.timevale.contractmanager.common.dal.bean.grouping.MenuDO;
import com.timevale.contractmanager.common.dal.bean.rule.RuleConditionDO;
import com.timevale.contractmanager.common.dal.dao.autoarchive.AutoArchiveOperatorDAO;
import com.timevale.contractmanager.common.dal.dao.autoarchive.AutoArchiveRuleDAO;
import com.timevale.contractmanager.common.dal.dao.grouping.AiRuleMenuDAO;
import com.timevale.contractmanager.common.dal.dao.grouping.MenuDAO;
import com.timevale.contractmanager.common.dal.dao.rule.RuleConditionDAO;
import com.timevale.contractmanager.common.dal.dao.rule.RuleConditionSysConfigDAO;
import com.timevale.contractmanager.common.service.bean.PreferenceModel;
import com.timevale.contractmanager.common.service.constant.PreferenceConstant;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.enums.RuleSysConfigTypeEnum;
import com.timevale.contractmanager.common.service.enums.autoarchive.ArchiveRuleRemoveEnum;
import com.timevale.contractmanager.common.service.enums.autoarchive.ArchiveRuleStatusEnum;
import com.timevale.contractmanager.common.service.enums.autoarchive.OperationConditionStrategyEnum;
import com.timevale.contractmanager.common.service.enums.grouping.MenuPermissionEnum;
import com.timevale.contractmanager.common.service.enums.grouping.RuleVersionEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.*;
import com.timevale.contractmanager.common.service.model.QueryBizPreferenceModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveOperator;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveRuleModel;
import com.timevale.contractmanager.common.service.model.autoarchive.ProcessStatus;
import com.timevale.contractmanager.common.service.model.autoarchive.ProcessStatusModel;
import com.timevale.contractmanager.common.service.model.rule.RuleConditionModel;
import com.timevale.contractmanager.common.service.result.autoarchive.ProcessStatusResult;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveOperatorRequest;
import com.timevale.contractmanager.core.model.dto.request.autoArchive.AutoArchiveUpdateOperatorRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.menu.CreateMenuRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.menu.ModifyMenuRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.menu.MoveMenuRequest;
import com.timevale.contractmanager.core.model.dto.response.autoArchive.*;
import com.timevale.contractmanager.core.model.dto.user.SimpleUserAccountInfoResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.auditlog.constants.AuditLogConstant;
import com.timevale.contractmanager.core.service.auditlog.handler.AuditLogHelper;
import com.timevale.contractmanager.core.service.autoarchive.AutoArchiveService;
import com.timevale.contractmanager.core.service.autoarchive.archivestrategy.InvokeStrategyService;
import com.timevale.contractmanager.core.service.autobind.AutoBindArchiveRuleService;
import com.timevale.contractmanager.core.service.cache.CacheUtil;
import com.timevale.contractmanager.core.service.component.autoarchive.AutoArchiveTreeComponent;
import com.timevale.contractmanager.core.service.dto.autoarchive.BuildAutoArchiveDTO;
import com.timevale.contractmanager.core.service.enums.YesNoEnum;
import com.timevale.contractmanager.core.service.grouping.AiRuleService;
import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.contractmanager.core.service.grouping.PermissionService;
import com.timevale.contractmanager.core.service.grouping.impl.menu.BuildMenuAuthorizeTreeResultDTO;
import com.timevale.contractmanager.core.service.grouping.impl.menu.MenuAuthorizeHelper;
import com.timevale.contractmanager.core.service.grouping.impl.menu.MenuNode;
import com.timevale.contractmanager.core.service.mq.msg.AutoArchiveHistoryProcessAmendmentMsg;
import com.timevale.contractmanager.core.service.mq.producer.autoarchive.AutoArchiveHistoryProcessAmendmentProducer;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.rule.RuleConditionService;
import com.timevale.contractmanager.core.service.util.IdGeneratorUtils;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.dayu.sdk.annotation.AuditLogAnnotation;
import com.timevale.dayu.sdk.context.LogRecordContext;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.saas.tracking.annotation.Tracking;
import com.timevale.saas.tracking.constants.TrackingModeConstants;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.enums.autoarchive.ArchiveRuleStatusEnum.*;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;
import static com.timevale.contractmanager.core.service.tracking.consts.TrackingKeyConstant.CREATE_AUTO_ARCHIVE_SAVE_MENU;
import static com.timevale.contractmanager.core.service.tracking.consts.TrackingKeyConstant.UPDATE_RULE_SAVE_MENU;
import static com.timevale.contractmanager.core.service.tracking.consts.TrackingServiceConstant.MENU_SAVE_TRACKING;

/**
 * @Author:jianyang
 * @since 2021-05-06 19:31
 */
@Service
@Slf4j
public class AutoArchiveServiceImpl implements AutoArchiveService {

	@Autowired private UserCenterService userCenterService;
	@Autowired private MenuService menuService;
	@Autowired private AutoArchiveRuleDAO autoArchiveRuleDAO;

	@Autowired
	private AutoArchiveOperatorDAO archiveOperatorDAO;

	@Autowired
	private MenuDAO menuDAO;

	@Autowired
	private PermissionService permissionService;

	@Autowired
	private AutoArchiveTreeComponent autoArchiveTreeComponent;

	@Autowired
	private ContractAnalysisClient contractAnalysisClient;

	@Autowired
	private InvokeStrategyService strategyService;

	@Autowired
	private AiRuleMenuDAO aiRuleMenuDAO;

	@Autowired
	private AutoArchiveHistoryProcessAmendmentProducer amendmentProducer;

	@Autowired
	private AiRuleService aiRuleService;

	@Autowired
	private RuleConditionDAO ruleConditionDAO;

	@Autowired
	private RuleConditionService ruleConditionService;

	@Autowired
	private MapperFactory mapperFactory;

	@Autowired
	private RuleConditionSysConfigDAO ruleConditionSysConfigDAO;
	@Autowired
	private AutoBindArchiveRuleService autoBindArchiveRuleService;
	@Autowired
	private LockFactory lockFactory;
	@Autowired
	private PreferencesService preferencesService;

	private final static String RULESTATUSUPDATE = "RULESTATUSUPDATE";

	// 归档规则启用上限白名单, GID级别
	public static final String PROP_RULE_OPEN_WHITE_LIST = "auto.archive.rule.open.white.list";
	// 归档规则启用上限-白名单用户
	public static final String PROP_RULE_OPEN_LIMIT_FOR_WHITE = "auto.archive.rule.open.limit-for-white";
	// 归档规则启用上限-非白名单用户
	public static final String PROP_RULE_OPEN_LIMIT = "auto.archive.rule.open.limit";
	// 归档条件上限
	public static final String PROP_RULE_CONDITION_LIMIT = "auto.archive.rule.condition.limit";

	@AuditLogAnnotation(
			enterpriseSpaceUnique1 = "#tenantOid",
			userUnique1 = "#operatorId",
			resourceEntSpaceUnique = "#tenantOid",
			resourceName = "#operatorRequest.name",
			result = "\"成功\"",
			detailTactics = "1",
			condition = "{{#_success}}",
			postHandle = "auditLogCreateAutoArchiveOperatorHandle")
	@Tracking(
			trackingKey = CREATE_AUTO_ARCHIVE_SAVE_MENU,
			trackingService = MENU_SAVE_TRACKING,
			trackingData = "{{#operatorRequest}}",
			operatorId = "{{#operatorId}}",
			tenantId = "{{#tenantOid}}",
			condition = "{{#_success}}",
			mode = TrackingModeConstants.MODE_MQ)
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void createAutoArchiveOperator(String tenantOid, AutoArchiveOperatorRequest operatorRequest,String operatorId) {
		AuditLogHelper.acceptHeaderFields();
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		CreateMenuRequest request =  new CreateMenuRequest();
		request.setName(operatorRequest.getName());
		request.setParentMenuId(operatorRequest.getParentMenuId());
		request.setAccountId(operatorId);
		if(StringUtils.isNotBlank(operatorRequest.getBindingFormId())){
			request.setBindingFormId(operatorRequest.getBindingFormId());
			request.setUnityForm(operatorRequest.getUnityForm());
		}

		if(CollectionUtils.isEmpty(operatorRequest.getConditions())){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.CONDITION_IS_EMPTY.getCode(),
					BizContractManagerResultCodeEnum.CONDITION_IS_EMPTY.getMessage());
		}

		String menuId = menuService.create(tenantOid, request);

		//保存分类和台账的绑定关系
		if(StringUtils.isNotBlank(request.getBindingFormId())){
			if (!contractAnalysisClient.canBuildMenu(tenantGid, request.getBindingFormId())){
				throw new BizContractManagerException(BizContractManagerResultCodeEnum.LEDGER_CAN_NOT_BUILD.getCode(),
						BizContractManagerResultCodeEnum.LEDGER_CAN_NOT_BUILD.getMessage());
			}
			AiRuleMenuDO aiRuleMenuDO = buildAiRuleMenu(menuId, request.getBindingFormId(), userAccountTenant);
			aiRuleMenuDAO.insertBatch(Collections.singletonList(aiRuleMenuDO));
		}
		//归档规则初始化
		if(operatorRequest.getRevomeContract() == null){
			operatorRequest.setRevomeContract(1);
		}
		if(CollectionUtils.isEmpty(operatorRequest.getConditions())){
			operatorRequest.setStatus(ArchiveRuleStatusEnum.CLOSEING.getCode());
		}

		AutoArchiveRuleDO archiveRuleDO = AutoArchiveRuleDO.builder()
				.ruleStatus(operatorRequest.getStatus())
				.bindingMenuId(menuId)
				.tenantGid(tenantGid)
				.uuid(UUIDUtil.genUUID())
				.createByOid(operatorId)
				.modifyByOid(operatorId)
				.revomeContract(operatorRequest.getRevomeContract())
				.build();
		autoArchiveRuleDAO.insert(archiveRuleDO);

		if (CollectionUtils.isNotEmpty(operatorRequest.getConditions())) {
			BuildAutoArchiveDTO buildAutoArchiveDTO = buildAutoArchiveOperatorDO(operatorRequest.getConditions(), archiveRuleDO.getUuid());

			List<AutoArchiveOperatorDO> operatorDOS = buildAutoArchiveDTO.getAutoArchiveOperatorDOS();
//			//规则条件校验
//			for (AutoArchiveOperatorDO archiveOperatorDO : buildAutoArchiveDTO.getAutoArchiveOperatorDOS()) {
//				if (StringUtils.isNotBlank(archiveOperatorDO.getConditionParams())) {
//					strategyService.checkCondition(buildAutoArchiveDTO.getMap().get(archiveOperatorDO.getOperatorId()), tenantOid, tenantGid, archiveRuleDO.getUuid(),
//							archiveOperatorDO.getConditionParams());
//				}
//			}

			//插入规则算子
			operatorDOS.stream().forEach(x -> {
				archiveOperatorDAO.insert(x);
			});

			if (getSyncProperty()) {
				List<RuleConditionModel> conditions = mapperFactory.getMapperFacade().mapAsList(operatorRequest.getConditions(), RuleConditionModel.class);
				boolean cache = operatorRequest.getStatus() == ArchiveRuleStatusEnum.OPENING.getCode();
				ruleConditionService.saveRuleCondition(userAccountTenant, archiveRuleDO.getUuid(), 1, conditions, cache);
			}
		}
	}

	/**
	 *
	 * @param autoArchiveOperators
	 * @param archiveRuleId
	 * @return
	 */
	public BuildAutoArchiveDTO buildAutoArchiveOperatorDO(List<AutoArchiveOperator> autoArchiveOperators, String archiveRuleId){
		BuildAutoArchiveDTO buildAutoArchiveDTO = new BuildAutoArchiveDTO();
		List<AutoArchiveOperatorDO> operatorDOS = new ArrayList<>();
		Map<String,String> map = new HashMap<>();
		autoArchiveOperators.forEach(x ->{
			AutoArchiveOperatorDO operatorDO = new AutoArchiveOperatorDO();
			operatorDO.setOperatorId(String.valueOf(IdGeneratorUtils.getId()));
			operatorDO.setOperatorType(x.getOperatorType());
			operatorDO.setOperatorWeight(x.getOperatorWeight());
			operatorDO.setArchiveRuleId(archiveRuleId);
			operatorDO.setFieldId(x.getFieldId());
			operatorDO.setFieldType(x.getFieldType());
			operatorDO.setFieldName(x.getFieldName());
			operatorDO.setMatchType(x.getMatchType());
			operatorDOS.add(operatorDO);
			if(!x.getChildOperators().isEmpty()){
				x.getChildOperators().forEach(y ->{
					AutoArchiveOperatorDO operatorDOChild = new AutoArchiveOperatorDO();
					operatorDOChild.setOperatorId(String.valueOf(IdGeneratorUtils.getId()));
					operatorDOChild.setParentOperatorId(operatorDO.getOperatorId());
					operatorDOChild.setArchiveRuleId(archiveRuleId);
					operatorDOChild.setConditionParams(y.getConditionParams());
					operatorDOChild.setOperatorType(y.getOperatorType());
					operatorDOS.add(operatorDOChild);
					map.put(operatorDOChild.getOperatorId(),x.getFieldId());
				});
			}
		});
		buildAutoArchiveDTO.setAutoArchiveOperatorDOS(operatorDOS);
		buildAutoArchiveDTO.setMap(map);
		return buildAutoArchiveDTO;
	}
	@AuditLogAnnotation(
			enterpriseSpaceUnique1 = "#tenantId",
			resourceEntSpaceUnique = "#tenantId",
			userUnique1 = "#operatorOid",
			result = "{{#result == '' ? " + AuditLogConstant.RESULT + "}}",
			resourceId = "#ruleId",
			resourceName = "\"修改条件状态\"",
			detailTactics = "1",
			condition = "{{T(com.timevale.mandarin.base.util.StringUtils).isNotBlank(#ruleId)}}",
			postHandle = "auditLogUpdateRuleStatusHandle")
	@Override
	@Transactional(rollbackFor = Exception.class)
	public String updateRuleStatus(String ruleId,String tenantId, String operatorOid, Integer status, boolean checkEditPermission) {
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
		String tenantGid = userAccountTenant.getAccountGid();
		// 审计日志字段记录
		LogRecordContext.putVariable(AuditLogConstant.Field.RULE_STATUS, status);

		AutoArchiveRuleDO ruleDO = autoArchiveRuleDAO.queryByRuleIdAndTenantGid(ruleId, userAccountTenant.getAccountGid());
		if(Objects.isNull(ruleDO)){
			return "规则不存在";
		}

		UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorOid);
		if (checkEditPermission) {
			checkHaveEditPermission(userAccountTenant, operatorAccount, ruleDO.getBindingMenuId());
		}

		//规则运行中直接返回
		if(ruleDO.getRuleStatus().equals(ArchiveRuleStatusEnum.RUNNING.getCode())){
			return "规则运行中";
		}

		if(ruleDO.getRuleStatus().equals(ArchiveRuleStatusEnum.DISABLED.getCode())){
			return "规则已失效";
		}
        // 如果规则从停用改为已启用，校验已启用的规则数量，如果超出上限不能启用
        if (CLOSEING.getCode().equals(ruleDO.getRuleStatus()) && OPENING.getCode() == status) {
            checkOpenRuleLimit(tenantGid, false);
        }
		log.info("rulestatusupdateWithLock :{}", ruleId+tenantGid);
		Elock lock = lockFactory.getLock(RULESTATUSUPDATE + ruleId + tenantGid);
		boolean isAcquired = lock.tryLock(1, TimeUnit.SECONDS);
		log.info(
				"rulestatusupdateWithLock isAcquired lock  ruleId :{} tenantGid :{}  true/false :{}",
				ruleId,tenantGid,
				isAcquired);

		if(isAcquired){
			try{
				//更新规则状态
				autoArchiveRuleDAO.updateStatus(status,ruleId);
				//缓存更新
				if(status.equals(ArchiveRuleStatusEnum.OPENING.getCode())){
					List<AutoArchiveOperatorDO> operatorDOS = archiveOperatorDAO.listByRuleId(ruleId);
					if(CollectionUtils.isEmpty(operatorDOS)){
						throw new BizContractManagerException(BizContractManagerResultCodeEnum.CONDITION_IS_EMPTY.getCode(),
								BizContractManagerResultCodeEnum.CONDITION_IS_EMPTY.getMessage());
					}
					autoArchiveTreeComponent.cacheRuleTree(tenantGid, ruleId, operatorDOS);
				}
				//删除缓存
				if (status.equals(ArchiveRuleStatusEnum.CLOSEING.getCode())) {
					String key = CacheUtil.getAutoArchiveRuleKey(tenantGid, ruleId);
					TedisUtil.delete(key);
				}
			}catch (BizContractManagerException e){
				throw  e;
			}
			catch (Exception e){
				log.error("update auto archive rule  error:{}",e.getMessage());
				throw new BizContractManagerException(BizContractManagerResultCodeEnum.UPDATE_RULE_STATUS_FAIL.getCode(),
						BizContractManagerResultCodeEnum.UPDATE_RULE_STATUS_FAIL.getMessage());
			}finally{
				log.info("rulestatusupdateWithLock finally unlock :ruleId :{}  tenantGid :{} ",
						ruleId,tenantGid);
				lock.unlock();
			}
		}else {
			log.warn("等待后, 仍未获取到锁！ruleId :{}  tenantGid :{}", ruleId,tenantGid);
		}
		return "";
	}

	@Override
	public void updateRuleStatus(String ruleId, Integer status) {
		autoArchiveRuleDAO.updateStatus(status,ruleId);
	}

	@Override
	public AutoArchiveMenuResponse list(String tenantOid,String operatorId) {

		//租户企业的信息
		UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = tenantAccount.getAccountGid();
		UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorId);
		//获取分类规则
		List<AutoArchiveRuleDO> ruleDOS = autoArchiveRuleDAO.queryByTenantGid(tenantGid);

		Map<String,AutoArchiveRuleDO> archiveRuleDOMap = ruleDOS.stream()
				.collect(Collectors.toMap(AutoArchiveRuleDO::getBindingMenuId,x->x));
		List<AutoArchiveOperatorDO> autoArchiveOperatorDOS = new ArrayList<>();

		Map<String, String> configMap = ruleConditionSysConfigDAO.queryListByType(RuleSysConfigTypeEnum.AUTO_ARCHIVE.getType()).stream()
				.collect(Collectors.toMap(AutoArchiveOperatorConfigDO::getFieldId, AutoArchiveOperatorConfigDO::getFieldName));
		//获取所有规则一级
		if(CollectionUtils.isNotEmpty(ruleDOS)){
			int i = 0;
			int size = 0;
			List<String> ruleIds = new ArrayList<>();
			for (AutoArchiveRuleDO archiveRuleDO : ruleDOS){
				ruleIds.add(archiveRuleDO.getUuid());
				i++;
				size++;
				if(i== 20){
					List<AutoArchiveOperatorDO> autoArchiveOperatorDOList = queryOperatorList(ruleIds);
					autoArchiveOperatorDOS.addAll(autoArchiveOperatorDOList);
					ruleIds.clear();
					i=0;
					continue;
				}
				if(size == ruleDOS.size()){
					List<AutoArchiveOperatorDO> autoArchiveOperatorDOList  = queryOperatorList(ruleIds);
					autoArchiveOperatorDOS.addAll(autoArchiveOperatorDOList);
				}
			}
		}

		AutoArchiveMenuResponse response = new AutoArchiveMenuResponse();
		//新逻辑-灰度
		if (Objects.equals(tenantOid, operatorAccount.getAccountOid())) {
			// 个人空间直接返回
			return response;
		}

		BuildMenuAuthorizeTreeResultDTO resultDTO = permissionService.buildRebirthMenuAuthorizeTree(operatorId,
				operatorAccount.getAccountGid(), tenantOid, tenantGid);
		if (resultDTO.noAnyMenuPermission()) {
			return response;
		}

		FormListResult formListResult = contractAnalysisClient.listAllForms(tenantOid, LedgerFormTypeEnum.SAAS_TENANT_LEDGER.getType());
		Map<String, FormResult> formListMap = new HashMap<>();
		if (formListResult != null && formListResult.getForms() != null) {
			formListMap = formListResult.getForms().stream()
					.collect(Collectors.toMap(FormResult::getFormId, Function.identity()));
		}

		List<AutoArchiveMenuDTO> menuListDTOList = convertToMenuListDTO(resultDTO.getIntTreeMenuList(),
				resultDTO.isReadOnly(), resultDTO.getHaveQueryPermissionByAuthMenuIds(),
				resultDTO.getMenuNode(), archiveRuleDOMap, formListMap, autoArchiveOperatorDOS, configMap);

		response.setMenuList(menuListDTOList);
		return response;
	}

	private List<AutoArchiveOperatorDO> queryOperatorList(List<String> ruleIds){
		List<RuleConditionDO> ruleConditionDOS = ruleConditionDAO.listByRuleIds(ruleIds);
		List<AutoArchiveOperatorDO> autoArchiveOperatorDOList = new ArrayList<>();
		for(RuleConditionDO ruleConditionDO : ruleConditionDOS){
			AutoArchiveOperatorDO autoArchiveOperatorDO = mapperFactory.getMapperFacade().map(ruleConditionDO, AutoArchiveOperatorDO.class);
			autoArchiveOperatorDO.setArchiveRuleId(ruleConditionDO.getRuleId());
			autoArchiveOperatorDOList.add(autoArchiveOperatorDO);
		}
		return autoArchiveOperatorDOList;
	}

	@Override
	public AutoArchiveRuleResponse getAutoArchiveRule(String ruleId, String tenantOid) {
		UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOid);
		AutoArchiveRuleResponse archiveRuleResponse = new AutoArchiveRuleResponse();
		AutoArchiveRuleDO ruleDO = autoArchiveRuleDAO.queryByRuleIdAndTenantGid(ruleId, tenantAccount.getAccountGid());
		if(Objects.isNull(ruleDO)){
			return archiveRuleResponse;
		}
		BeanUtils.copyProperties(ruleDO,archiveRuleResponse);
		archiveRuleResponse.setMenuId(ruleDO.getBindingMenuId());
		//获取目录
		MenuDO menuDO = menuDAO.getByMenuId(ruleDO.getBindingMenuId());
		if(menuDO != null){
			archiveRuleResponse.setName(menuDO.getName());
			archiveRuleResponse.setParentMenuId(menuDO.getParentId());
			archiveRuleResponse.setBindingFormId(menuDO.getBindingFormId());
		}
		//获取绑定的台账
		AiRuleMenuDO aiRuleMenuDO = aiRuleMenuDAO.getRuleByMenuId(ruleDO.getBindingMenuId());
		if(aiRuleMenuDO != null){
			archiveRuleResponse.setBindingFormId(aiRuleMenuDO.getRuleId());
		}

		List<AutoArchiveOperator> operators = new ArrayList<>();
		List<RuleConditionDO> ruleConditionDOS = ruleConditionDAO.listByRuleId(ruleDO.getUuid());
		List<AutoArchiveOperatorDO> operatorDOS = mapperFactory.getMapperFacade().mapAsList(ruleConditionDOS, AutoArchiveOperatorDO.class);

		if(!operatorDOS.isEmpty()){
			operatorDOS.stream()
					.filter(x -> StringUtils.isBlank(x.getParentOperatorId()))
					.forEach(x ->{
						AutoArchiveOperator operator = new AutoArchiveOperator();
						BeanUtils.copyProperties(x,operator);
						List<AutoArchiveOperator> child = setOperatorChild(x.getOperatorId(),operatorDOS);
						if(!child.isEmpty()){
							operator.setChildOperators(child);
						}
						operators.add(operator);
					});
		}
		archiveRuleResponse.setConditions(operators);
		return archiveRuleResponse;
	}

	@AuditLogAnnotation(
			enterpriseSpaceUnique1 = "#tenantId",
			resourceEntSpaceUnique = "#tenantId",
			userUnique1 = "#operatorId",
			result = "\"成功\"",
			resourceId = "#ruleId",
			resourceName = "\"修改归档条件\"",
			detailTactics = "1",
			condition = "{{T(com.timevale.mandarin.base.util.StringUtils).isNotBlank(#ruleId)}}",
			postHandle = "auditLogUpdateRuleHandle")
	@Tracking(
			trackingKey = UPDATE_RULE_SAVE_MENU,
			trackingService = MENU_SAVE_TRACKING,
			trackingData = "{{#updateOperatorRequest}}",
			operatorId = "{{#operatorId}}",
			tenantId = "{{#tenantId}}",
			condition = "{{#_success}}",
			mode = TrackingModeConstants.MODE_MQ)
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateRule(String ruleId, AutoArchiveUpdateOperatorRequest updateOperatorRequest,String tenantId,String operatorId, boolean checkEditPermission) {
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
		String tenantGid = userAccountTenant.getAccountGid();
		UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorId);
		// 校验归档条件是否超出上限
		checkRuleConditionsLimit(updateOperatorRequest.getConditions());

		AutoArchiveRuleDO ruleDO = autoArchiveRuleDAO.queryByRuleIdAndTenantGid(ruleId, tenantGid);
		if(Objects.isNull(ruleDO)){
			return;
		}

		//规则运行中直接返回
		if(ruleDO.getRuleStatus().equals(ArchiveRuleStatusEnum.RUNNING.getCode())){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_IS_OPENING.getCode(),
					BizContractManagerResultCodeEnum.RULE_IS_OPENING.getMessage());
		}
		//企业空间下目前只有管理员具有企业空间的操作权限,但是全局权限可以将合同归档到任意菜单
		//新版本只要能看到就可以编辑，不校验编辑权限
		if (checkEditPermission) {
			checkHaveEditPermission(userAccountTenant, operatorAccount, ruleDO.getBindingMenuId());
		}

		MenuDO menuDO = menuDAO.getByMenuId(ruleDO.getBindingMenuId());

		if(Objects.isNull(menuDO)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_IS_NOT_EXIST.getCode(),
					BizContractManagerResultCodeEnum.RULE_IS_NOT_EXIST.getMessage());
		}

		AiRuleMenuDO newAiRuleMenuDO = aiRuleMenuDAO.getRuleByMenuId(updateOperatorRequest.getBindingMenuId());
		// 当前传入的关联数据
		AutoBindArchiveRuleService.AutoBindLedgerForm autoBindLedgerForm = autoBindArchiveRuleService.buildAutoBindBean(tenantId, newAiRuleMenuDO);
		checkAutoBindRule(tenantId, autoBindLedgerForm, updateOperatorRequest.getBindingMenuId(), updateOperatorRequest.getBindingFormId());

		if(StringUtils.isNotBlank(updateOperatorRequest.getParentMenuId())
				&& updateOperatorRequest.getSourceParentMenuId() != null
				&& !Objects.equals(updateOperatorRequest.getParentMenuId(),updateOperatorRequest.getSourceParentMenuId())){
			//更换父节点
			MoveMenuRequest moveMenuRequest = new MoveMenuRequest();
			moveMenuRequest.setAccountId(operatorId);
			moveMenuRequest.setSourceMenuId(updateOperatorRequest.getSourceParentMenuId());
			moveMenuRequest.setTargetMenuId(updateOperatorRequest.getParentMenuId());
			moveMenuRequest.setTargetOrder(updateOperatorRequest.getTargetOrder());
			menuService.move(tenantId,menuDO.getMenuId(),moveMenuRequest);
		}
		//分类重命名
		if(!Objects.equals(updateOperatorRequest.getName(),updateOperatorRequest.getSourceName())){
			ModifyMenuRequest request = new ModifyMenuRequest();
			request.setName(updateOperatorRequest.getName());
			request.setAccountId(operatorId);
			menuService.modify(tenantId,ruleDO.getBindingMenuId(),request);
		}

		// 当前的台账和分类关系
		AiRuleMenuDO aiRuleMenuDO = aiRuleMenuDAO.getRuleByMenuId(ruleDO.getBindingMenuId());

		if (StringUtils.isNotBlank(updateOperatorRequest.getBindingFormId())){
			if (!contractAnalysisClient.canBuildMenu(tenantGid, updateOperatorRequest.getBindingFormId())){
				throw new BizContractManagerException(BizContractManagerResultCodeEnum.LEDGER_CAN_NOT_BUILD.getCode(),
						BizContractManagerResultCodeEnum.LEDGER_CAN_NOT_BUILD.getMessage());
			}
			menuDO.setUnityForm(YesNoEnum.from(updateOperatorRequest.getUnityForm()).code());
		}else {
			//未绑定台账时统一子分类台账强制为否
			menuDO.setUnityForm(YesNoEnum.N.code());
		}

		if(StringUtils.isNotEmpty(updateOperatorRequest.getBindingMenuId())){
			ruleDO.setBindingMenuId(updateOperatorRequest.getBindingMenuId());
		}

		// 数据库中的关联数据
		AutoBindArchiveRuleService.AutoBindLedgerForm oldAutoBindLedgerForm = autoBindArchiveRuleService.buildAutoBindBean(tenantId, aiRuleMenuDO);

		// 自动绑定的台账
		if (autoBindLedgerForm.isHit() || oldAutoBindLedgerForm.isHit()) {
			if (oldAutoBindLedgerForm.isAutoBind() && autoBindLedgerForm.isNormal()) {
				// 从自动台账到普通台账
				AiRuleMenuDO aiRuleMenuDONew = buildAiRuleMenu(ruleDO.getBindingMenuId(), updateOperatorRequest.getBindingFormId(), userAccountTenant);
				aiRuleMenuDAO.insertBatch(Collections.singletonList(aiRuleMenuDONew));
				menuDO.setBindingFormId(autoBindLedgerForm.getFormDetailDTO().getFormId());
			} else if (oldAutoBindLedgerForm.isNormal() && autoBindLedgerForm.isAutoBind()) {
				// 从普通台账绑定到自动台账
				menuDO.setBindingFormId(null);
				aiRuleMenuDAO.deleteById(aiRuleMenuDO.getId());
			} else {
				// 其他 更新后的台账为 自动台账/解绑台账, 需要讲菜单绑定的台账解除关系
				menuDO.setBindingFormId(null);
			}
		} else

		// 更新台账 - 历史逻辑
		if(aiRuleMenuDO != null){
			//台账更新
			if(StringUtils.isNotBlank(updateOperatorRequest.getSourceBindingFormId())
					&& StringUtils.isNotBlank(updateOperatorRequest.getBindingFormId())){
				aiRuleMenuDAO.updateRuleIdAndMenuId(updateOperatorRequest.getBindingFormId(), ruleDO.getBindingMenuId(), aiRuleMenuDO.getId());
				menuDO.setBindingFormId(updateOperatorRequest.getBindingFormId());
			}else if(StringUtils.isNotBlank(updateOperatorRequest.getSourceBindingFormId())
						&& StringUtils.isBlank(updateOperatorRequest.getBindingFormId())){
				// 删除台账关系
				menuDO.setBindingFormName(null);
				menuDO.setBindingFormId(null);
				aiRuleMenuDAO.deleteById(aiRuleMenuDO.getId());
			}
		}else {
			// 原来没有绑定台账，现在绑定了台账，
			if(StringUtils.isBlank(updateOperatorRequest.getSourceBindingFormId())
					&& StringUtils.isNotBlank(updateOperatorRequest.getBindingFormId())){
				AiRuleMenuDO aiRuleMenuDONew = buildAiRuleMenu(ruleDO.getBindingMenuId(), updateOperatorRequest.getBindingFormId(), userAccountTenant);
				aiRuleMenuDAO.insertBatch(Collections.singletonList(aiRuleMenuDONew));
			}
			if(StringUtils.isBlank(updateOperatorRequest.getSourceBindingFormId())
					&& StringUtils.isNotBlank(updateOperatorRequest.getBindingFormId())){
				menuDO.setBindingFormId(updateOperatorRequest.getBindingFormId());
			}
			//可能存在两个表对不上的脏数据
			if(StringUtils.isNotBlank(updateOperatorRequest.getSourceBindingFormId())
					&& StringUtils.isBlank(updateOperatorRequest.getBindingFormId())){
				menuDO.setBindingFormName(null);
				menuDO.setBindingFormId(null);
			}
		}
		menuDAO.updateForm(menuDO);

		//更新状态
		log.info("rulestatusupdateWithLock :{}", ruleId+tenantGid);
		Elock lock = lockFactory.getLock(RULESTATUSUPDATE + ruleId + tenantGid);
		boolean isAcquired = lock.tryLock(1, TimeUnit.SECONDS);
		log.info(
				"rulestatusupdateWithLock isAcquired lock  ruleId :{} tenantGid :{}  true/false :{}",
				ruleId,tenantGid,
				isAcquired);

		if(isAcquired){
			try{
				//更新规则状态
				if(CollectionUtils.isEmpty(updateOperatorRequest.getConditions())){
					updateOperatorRequest.setStatus(ArchiveRuleStatusEnum.CLOSEING.getCode());
				}

				ruleDO.setRuleStatus(updateOperatorRequest.getStatus());
				ruleDO.setModifyByOid(operatorId);
				ruleDO.setRevomeContract(updateOperatorRequest.getRevomeContract());
				autoArchiveRuleDAO.update(ruleDO);
			}catch (Exception e){
				log.error("update auto archive rule  error:{}",e.getMessage());
				throw new BizContractManagerException(BizContractManagerResultCodeEnum.UPDATE_RULE_STATUS_FAIL.getCode(),
						BizContractManagerResultCodeEnum.UPDATE_RULE_STATUS_FAIL.getMessage());
			}finally{
				log.info("rulestatusupdateWithLock finally unlock :ruleId :{}  tenantGid :{} ",
						ruleId,tenantGid);
				lock.unlock();
			}
		}else {
			log.warn("等待后, 仍未获取到锁！ruleId :{}  tenantGid :{}", ruleId,tenantGid);
		}
		boolean sync = getSyncProperty();

		//删除分类规则算子
		if(Boolean.TRUE.equals(updateOperatorRequest.getModifyMark())){
			archiveOperatorDAO.delete(ruleDO.getUuid());
			if(sync){
				ruleConditionDAO.delete(ruleDO.getUuid());
			}
		}
		if(updateOperatorRequest.getConditions() != null
				&& !updateOperatorRequest.getConditions().isEmpty()
				&& Boolean.TRUE.equals(updateOperatorRequest.getModifyMark())){
			//新增分类规则算子
			BuildAutoArchiveDTO buildAutoArchiveDTO =
					buildAutoArchiveOperatorDO(updateOperatorRequest.getConditions(), ruleDO.getUuid());

			List<AutoArchiveOperatorDO> operatorDOAdds = buildAutoArchiveDTO.getAutoArchiveOperatorDOS();
			Map<String,String> map = buildAutoArchiveDTO.getMap();

			//规则条件校验
			for (AutoArchiveOperatorDO archiveOperatorDO: operatorDOAdds){
				if(StringUtils.isNotBlank(archiveOperatorDO.getConditionParams())
						&& StringUtils.isNotBlank(map.get(archiveOperatorDO.getOperatorId()))){
					strategyService.checkCondition(map.get(archiveOperatorDO.getOperatorId()),tenantId,tenantGid,ruleDO.getUuid(),
							archiveOperatorDO.getConditionParams());
				}
			}

			operatorDOAdds.stream().forEach(x ->{
				archiveOperatorDAO.insert(x);
			});

			boolean isOpen = updateOperatorRequest.getStatus() == ArchiveRuleStatusEnum.OPENING.getCode();
			if(sync){
				List<RuleConditionModel> conditions = mapperFactory.getMapperFacade().mapAsList(updateOperatorRequest.getConditions(), RuleConditionModel.class);
				ruleConditionService.saveRuleCondition(userAccountTenant, ruleDO.getUuid(), 1, conditions, isOpen);
			}

			if(isOpen){
				//序列化规则树
				autoArchiveTreeComponent.cacheRuleTree(tenantGid, ruleId, operatorDOAdds);
			}
		}
	}

	/**
	 * 对于自动绑定台账的校验
	 * @param tenantId
	 * @param autoBindLedgerForm 分类隐含的台账信息
	 * @param menuId 分类id
	 * @param reversedFormId 归档设置的台账信息
	 */
	private void checkAutoBindRule(String tenantId, AutoBindArchiveRuleService.AutoBindLedgerForm autoBindLedgerForm, String menuId, String reversedFormId) {
		if (autoBindLedgerForm.isHit()) {
			// 分类和隐含的关联关系校验
			String formId = autoBindLedgerForm.getFormDetailDTO().getFormId();
			autoBindArchiveRuleService.checkRuleMatchMenu(tenantId, formId, menuId);
		} else if (StringUtils.isNotBlank(reversedFormId)) {
			// 校验台账是否和分类绑定
			autoBindArchiveRuleService.checkRuleMatchMenu(tenantId, reversedFormId, menuId);
		}
	}

	private AiRuleMenuDO buildAiRuleMenu(String menuId, String ruleId, UserAccount userAccountTenant) {
		AiRuleMenuDO aiRuleMenuDONew = new AiRuleMenuDO();
		aiRuleMenuDONew.setMenuId(menuId);
		aiRuleMenuDONew.setRuleId(ruleId);
		aiRuleMenuDONew.setOid(userAccountTenant.getAccountOid());
		aiRuleMenuDONew.setGid(userAccountTenant.getAccountGid());
		aiRuleMenuDONew.setRuleVersion(RuleVersionEnum.THREE.getVersion());
		return aiRuleMenuDONew;
	}

	public void checkHaveEditPermission(UserAccount tenantAccount, UserAccount operatorAccount, String bindingMenuId) {
		String tenantGid = tenantAccount.getAccountGid();
		String tenantOid = tenantAccount.getAccountOid();
		String operatorGid = operatorAccount.getAccountGid();
		String operatorOid = operatorAccount.getAccountOid();

		BuildMenuAuthorizeTreeResultDTO resultDTO =
				permissionService.buildRebirthMenuAuthorizeTree(operatorOid, operatorGid, tenantOid, tenantGid);
		if (resultDTO.noAnyMenuPermission()) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_NO_PRIVILEGE.getCode(),
					BizContractManagerResultCodeEnum.RULE_NO_PRIVILEGE.getMessage());
		}
		MenuNode menuTree = resultDTO.getMenuNode();
		boolean havePermission = MenuAuthorizeHelper.checkTargetMenuHavePermission(menuTree, bindingMenuId,
				MenuPermissionEnum.AUTHORIZE.code());
		if (!havePermission) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_NO_PRIVILEGE.getCode(),
					BizContractManagerResultCodeEnum.RULE_NO_PRIVILEGE.getMessage());
		}

	}


	@Override
	public List<SimpleUserAccountInfoResponse> getAccountDetail(List<String> accountOids) {
		List<SimpleUserAccountInfoResponse> userAccountDetails = new ArrayList<>();
		
		List<MemberDetail> members = 
				userCenterService.getSubjectMembers(RequestContextExtUtils.getTenantId(), accountOids);

		if (CollectionUtils.isEmpty(members)) {
			return userAccountDetails;
		}
		
		members.forEach(memberDetail -> {
			SimpleUserAccountInfoResponse simpleUserAccount = new SimpleUserAccountInfoResponse();
			simpleUserAccount.setAccountOid(memberDetail.getMemberOid());
			simpleUserAccount.setAccountName(memberDetail.getMemberName());
			userAccountDetails.add(simpleUserAccount);
		});
		return userAccountDetails;
	}


	@Override
	public void unBindRule(String menuId,String tenantOid) {
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		AutoArchiveRuleDO ruleDO = autoArchiveRuleDAO.queryByMenuId(tenantGid, menuId);
		if(ruleDO != null){
			autoArchiveRuleDAO.updateDeleteStatus(ruleDO.getUuid());
			//删除缓存
			String key = CacheUtil.getAutoArchiveRuleKey(tenantGid,ruleDO.getUuid());
			TedisUtil.delete(key);

			String dataKey = CacheUtil.getSaasAutoArchiveRuleKey(tenantGid,ruleDO.getUuid());
			TedisUtil.delete(dataKey);
		}
	}

	@AuditLogAnnotation(
			enterpriseSpaceUnique1 = "#tenantOid",
			userUnique1 = "#operatorId",
			resourceEntSpaceUnique = "#tenantOid",
			result = "\"成功\"",
			resourceName = "\"创建归档条件\"",
			detailTactics = "1",
			condition = "#_result == 0",
			postHandle = "auditLogAutoArchiveBindRuleHandle")
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Integer bindRule(String tenantOid, AutoArchiveOperatorRequest operatorRequest, String operatorId) {
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantOid);
		String tenantGid = userAccountTenant.getAccountGid();

		// 创建归档规则后马上启用场景，校验已启用的规则数量，如果超出上限不能创建
		if (OPENING.getCode() == operatorRequest.getStatus()) {
			checkOpenRuleLimit(tenantGid, true);
		}
		// 校验归档条件是否超出上限
		checkRuleConditionsLimit(operatorRequest.getConditions());

		if(StringUtils.isBlank(operatorRequest.getOldMenuId())){
			return 0;
		}

		//检查规则和合同设置偏好是否匹配
		checkStatusComparePreferences(operatorRequest,userAccountTenant);

		// 查询该分类的归档是否存在
		AutoArchiveRuleDO ruleDO = autoArchiveRuleDAO.queryByMenuId(tenantGid, operatorRequest.getOldMenuId());
		if (ruleDO != null) {
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.MENU_BIND_EXIST);
		}

		// 查询台账和分类绑定关系是否存在
		AiRuleMenuDO oldAiRuleMenuDO = aiRuleMenuDAO.getRuleByMenuId(operatorRequest.getOldMenuId());
		AutoBindArchiveRuleService.AutoBindLedgerForm autoBindLedgerForm = autoBindArchiveRuleService.buildAutoBindBean(tenantOid, oldAiRuleMenuDO);

		if(oldAiRuleMenuDO != null && !autoBindLedgerForm.isHit()){
			if (operatorRequest.getUnBindOldAiRule() != null && operatorRequest.getUnBindOldAiRule()){
				aiRuleService.unBindMenu(tenantOid, operatorRequest.getOldMenuId(), new ArrayList<>());
			} else {
				return BizContractManagerResultCodeEnum.MENUID_BIND.getnCode();
			}
		}

		//
		MenuDO menuDO = menuDAO.getByMenuId(operatorRequest.getOldMenuId());
		if(menuDO == null){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.MENUID_NOT_CONTAIN.getCode(),
					BizContractManagerResultCodeEnum.MENUID_NOT_CONTAIN.getMessage());
		}
		String menuId = menuDO.getMenuId();

		checkAutoBindRule(tenantOid, autoBindLedgerForm, menuId, operatorRequest.getBindingFormId());

		if(StringUtils.isNotBlank(operatorRequest.getBindingFormId()) && !autoBindLedgerForm.isHit()){
			menuDO.setBindingFormId(operatorRequest.getBindingFormId());
			YesNoEnum yesNoEnum = YesNoEnum.from(operatorRequest.getUnityForm());
			if (StringUtils.isBlank(menuDO.getParentId()) && yesNoEnum.equals(YesNoEnum.Y)){
				menuDO.setUnityForm(yesNoEnum.code());
			}
			menuDAO.updateForm(menuDO);
		}

		if(StringUtils.isNotBlank(operatorRequest.getBindingFormId()) && !autoBindLedgerForm.isHit()){
			AiRuleMenuDO aiRuleMenuDO = buildAiRuleMenu(operatorRequest.getOldMenuId(), operatorRequest.getBindingFormId(), userAccountTenant);
			aiRuleMenuDAO.insertBatch(Collections.singletonList(aiRuleMenuDO));
		}
		if(operatorRequest.getRevomeContract() == null){
			operatorRequest.setRevomeContract(1);
		}
		AutoArchiveRuleDO archiveRuleDO = AutoArchiveRuleDO.builder()
				.ruleStatus(operatorRequest.getStatus())
				.bindingMenuId(menuId)
				.tenantGid(tenantGid)
				.createByOid(operatorId)
				.modifyByOid(operatorId)
				.uuid(UUIDUtil.genUUID())
				.revomeContract(operatorRequest.getRevomeContract())
				.build();
		autoArchiveRuleDAO.insert(archiveRuleDO);

		if(operatorRequest.getConditions() != null && !operatorRequest.getConditions().isEmpty()){

			//构造map、list
			BuildAutoArchiveDTO buildAutoArchiveDTO = buildAutoArchiveOperatorDO(operatorRequest.getConditions(), archiveRuleDO.getUuid());
			List<AutoArchiveOperatorDO> operatorDOS = buildAutoArchiveDTO.getAutoArchiveOperatorDOS();
//			Map<String,String> map = buildAutoArchiveDTO.getMap();
//
//			//规则条件校验
//			for (AutoArchiveOperatorDO archiveOperatorDO: operatorDOS){
//				if(StringUtils.isNotBlank(archiveOperatorDO.getConditionParams())){
//					strategyService.checkCondition(map.get(archiveOperatorDO.getOperatorId()),tenantOid,tenantGid,archiveRuleDO.getUuid(),
//							archiveOperatorDO.getConditionParams());
//				}
//			}

			//插入规则算子
			operatorDOS.stream().forEach(x ->{
				archiveOperatorDAO.insert(x);
			});
			boolean isOpen = operatorRequest.getStatus() == ArchiveRuleStatusEnum.OPENING.getCode();
			if(getSyncProperty()){
				List<RuleConditionModel> conditions = mapperFactory.getMapperFacade().mapAsList(operatorRequest.getConditions(), RuleConditionModel.class);
				ruleConditionService.saveRuleCondition(userAccountTenant, archiveRuleDO.getUuid(), 1, conditions, isOpen);
			}

			if(isOpen){
				//将规则序列化成树缓存进redis
				autoArchiveTreeComponent.cacheRuleTree(tenantGid, archiveRuleDO.getUuid(), operatorDOS);
			}
		}

		LogRecordContext.putVariable(AuditLogConstant.Field.ARCHIVE_RULE_ID, archiveRuleDO.getUuid());
		return 0;
	}

    /**
     * 校验归档条件是否超出上限
     * @param conditions
     */
	private void checkRuleConditionsLimit(List<AutoArchiveOperator> conditions) {
		if (CollectionUtils.isEmpty(conditions)) {
			return;
		}
		// 条件上限
		Integer limitSize = ConfigService.getAppConfig().getIntProperty(PROP_RULE_CONDITION_LIMIT, null);
		if (null == limitSize) {
			return;
		}
		// 判断是否已超出上限， 如果是， 报错
		if (conditions.size() > limitSize) {
			throw new BizContractManagerException(RULE_CONDITION_LIMIT, conditions.size() - limitSize);
		}
	}

	/**
	 * 校验已开启的归档规则是否超出上限
	 * @param tenantGid
	 * @param createAndOpen
	 */
    private void checkOpenRuleLimit(String tenantGid, boolean createAndOpen) {
        // 白名单用户列表
		List<String> whiteList = IdsUtil.getIdList(ConfigService.getAppConfig().getProperty(PROP_RULE_OPEN_WHITE_LIST, ""));
		// 白名单用户上限
		Integer whiteListLimit = ConfigService.getAppConfig().getIntProperty(PROP_RULE_OPEN_LIMIT_FOR_WHITE, null);
		// 普通用户上限
		Integer limit = ConfigService.getAppConfig().getIntProperty(PROP_RULE_OPEN_LIMIT, null);
		// 获取最终上限
		Integer limitSize = whiteList.contains(tenantGid) ? whiteListLimit : limit;
		if (null == limitSize) {
			return;
		}
        // 查询已启用的归档规则列表
        List<AutoArchiveRuleDO> openRules =
                autoArchiveRuleDAO.queryByTenantGidAndStatus(
                        tenantGid, Lists.newArrayList(OPENING.getCode(), RUNNING.getCode()));
		// 判断是否已超出上限， 如果是， 报错
		if (openRules.size() >= limitSize) {
			throw new BizContractManagerException(createAndOpen ? RULE_CREATE_LIMIT : RULE_OPEN_LIMIT);
		}
	}

	@Override
	public void runRule(String tenantId, String ruleId) {
		//获取规则
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);

		AutoArchiveRuleDO ruleDO = autoArchiveRuleDAO.queryByRuleIdAndTenantGid(ruleId, userAccountTenant.getAccountGid());
		if(ruleDO == null){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.MENUID_NOT_CONTAIN);
		}

		//规则打开状态才可以重新运行
		if(!ruleDO.getRuleStatus().equals(ArchiveRuleStatusEnum.OPENING.getCode())){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_NOT_OPENING.getCode(),
					BizContractManagerResultCodeEnum.RULE_NOT_OPENING.getMessage());
		}
		//判断是否存在运行中的归档规则，如果是，报错提示
		List<AutoArchiveRuleDO> runningRules = autoArchiveRuleDAO.queryByTenantGidAndStatus(userAccountTenant.getAccountGid(), Lists.newArrayList(RUNNING.getCode()));
		if (CollectionUtils.isNotEmpty(runningRules)) {
			throw new BizContractManagerException(EXISTED_RUNNING_RULE);
		}

		//发送历史数据修正mq
		AutoArchiveHistoryProcessAmendmentMsg msg = new AutoArchiveHistoryProcessAmendmentMsg();
		msg.setMenuId(ruleDO.getBindingMenuId());
		msg.setTenantOid(userAccountTenant.getAccountOid());
		if (ruleDO.getRevomeContract().equals(ArchiveRuleRemoveEnum.REMOVE.getCode())) {
			msg.setRemoveProcess(true);
		} else {
			msg.setRemoveProcess(false);
		}
		amendmentProducer.sendDataRefreshMessage(JSON.toJSONString(msg), "archive");
	}

	@Override
	public void updateTopTime(String ruleId, String tenantId, String operatorId) {
		//租户企业的信息
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
		String tenantGid = userAccountTenant.getAccountGid();

		AutoArchiveRuleDO ruleDO = autoArchiveRuleDAO.queryByRuleId(ruleId);
		if(Objects.isNull(ruleDO)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_MENUID_NO_EXIST);
		}

		if(!ruleDO.getTenantGid().equals(tenantGid)){
			throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_NO_PRIVILEGE);
		}

		Date topTime = null;
		if(ruleDO.getTopTime() == null){
			topTime = new Date();

			//校验是否超过限制
			List<AutoArchiveRuleDO> autoArchiveRuleDOS = autoArchiveRuleDAO.queryTopTimeList(tenantGid);
			if(autoArchiveRuleDOS.size() >= 10){
				throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_TOP_LIMIT_ERROR);
			}
		}
		autoArchiveRuleDAO.updateTopTime(topTime, ruleId);
	}

	@Override
	public List<AutoArchiveRuleModel> listRuleByTenantIdAndStatus(String tenantGid, List<Integer> status) {
		List<AutoArchiveRuleDO> autoArchiveRuleDOS;
		if (CollectionUtils.isNotEmpty(status)) {
			autoArchiveRuleDOS = autoArchiveRuleDAO.queryByTenantGidAndStatus(tenantGid, status);
		} else {
			autoArchiveRuleDOS = autoArchiveRuleDAO.queryByTenantGid(tenantGid);
		}
		return mapperFactory.getMapperFacade().mapAsList(autoArchiveRuleDOS, AutoArchiveRuleModel.class);
	}

	@Override
	public void addArchiveNum(String ruleId, Integer num) {
		autoArchiveRuleDAO.addArchiveNum(ruleId, num);
	}

	@Override
	public String getRuleIdByMenuId(String menuId, String tenantId) {
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);
		String tenantGid = userAccountTenant.getAccountGid();
		AutoArchiveRuleDO autoArchiveRuleDO = autoArchiveRuleDAO.queryByMenuId(tenantGid, menuId);
		return autoArchiveRuleDO == null ? null : autoArchiveRuleDO.getUuid();
	}

	@Transactional
	@Override
	public void unbindRuleMenu(String menuId, String tenantId, String operatorId) {
		MenuDO menuDO = menuService.getMenuByMenuId(menuId);
		if (null == menuDO) {
			return;
		}
		if(StringUtils.isNotEmpty(menuDO.getBindingFormId())){
			menuDAO.unbindMenu(menuDO.getId());
		}
		unBindRule(menuId, tenantId);

		AiRuleMenuDO aiRuleMenuDO = aiRuleMenuDAO.getRuleByMenuId(menuDO.getMenuId());
		AutoBindArchiveRuleService.AutoBindLedgerForm autoBindLedgerForm = autoBindArchiveRuleService.buildAutoBindBean(tenantId, aiRuleMenuDO);
		if (!autoBindLedgerForm.isHit()) {
			aiRuleService.unBindMenu(tenantId, menuId, new ArrayList<>());
		}
	}

	@Override
	public boolean existArchiveRule(String tenantGid) {
		if(StringUtils.isEmpty(tenantGid)){
			return false;
		}
		List<AutoArchiveRuleDO> autoArchiveRuleDOS = autoArchiveRuleDAO.queryByTenantGidIgnoreDeleted(tenantGid);
		return CollectionUtils.isNotEmpty(autoArchiveRuleDOS);
	}

	private boolean getSyncProperty() {
		return ConfigService.getAppConfig().getBooleanProperty("rule.condition.sync", false);
	}

	public List<AutoArchiveOperator> setOperatorChild(String operatorId, List<AutoArchiveOperatorDO> operatorDOS) {
		List<AutoArchiveOperator> child = new ArrayList<>();
		operatorDOS.stream()
				.filter(x -> StringUtils.isNotBlank(x.getParentOperatorId())
						&& x.getParentOperatorId().equals(operatorId))
				.forEach(x -> {
					AutoArchiveOperator operator = new AutoArchiveOperator();
					BeanUtils.copyProperties(x, operator);
					child.add(operator);
				});
		return child;
	}

	/**
	 * 转换MenuListDTO对象,含父子目录关系
	 *
	 * @param menuDOList
	 * @param isReadOnly 是否只读，true-是（无权归档），false-否（可归档）
	 * @param menuIdList 有权限的菜单id列表,个人空间和企业管理员不需要
	 * @return
	 */
	public static List<AutoArchiveMenuDTO> convertToMenuListDTO(List<MenuDO> menuDOList, boolean isReadOnly,
																Set<String> menuIdList, MenuNode menuTree,
																Map<String,AutoArchiveRuleDO> archiveRuleDOMap,
																Map<String,FormResult> formListMap,
																List<AutoArchiveOperatorDO> autoArchiveOperatorDOS,
																Map<String, String> configMap) {
		List<AutoArchiveMenuDTO> newMenuList = new ArrayList<>();
		for (MenuDO menuDO : menuDOList) {
			if (StringUtils.isNotBlank(menuDO.getParentId())) {
				continue;
			}
			// 找父节点
			AutoArchiveMenuDTO response = getMenuListDTO(menuDO,menuDOList,isReadOnly,menuIdList,
					menuTree,archiveRuleDOMap,formListMap, autoArchiveOperatorDOS, configMap);
			newMenuList.add(response);
		}
		newMenuList.sort(Comparator.comparing(AutoArchiveMenuDTO::getOrder));
		return newMenuList;
	}

	/**
	 * 设置子节点信息
	 *
	 * @param parentId 父节点id
	 * @param menuDOList 菜单列表
	 * @param isReadOnly 是否只读，true-是（无权归档），false-否（可归档）
	 * @param menuIdList 有权限的菜单列表
	 * @return
	 */
	private static List<AutoArchiveMenuDTO> setChildMenus(String parentId,
														  List<MenuDO> menuDOList,
														  boolean isReadOnly,
														  Set<String> menuIdList,
														  MenuNode menuTree,
														  Map<String,AutoArchiveRuleDO> archiveRuleDOMap,
														  Map<String,FormResult> formListMap,
														  List<AutoArchiveOperatorDO> autoArchiveOperatorDOS,
														  Map<String, String> configMap) {
		List<AutoArchiveMenuDTO> list = new ArrayList<>();
		for (MenuDO menuDO : menuDOList) {
			if (!parentId.equals(menuDO.getParentId())) {
				continue;
			}
			AutoArchiveMenuDTO response = getMenuListDTO(menuDO,menuDOList,isReadOnly,
					menuIdList, menuTree,archiveRuleDOMap,
					formListMap, autoArchiveOperatorDOS,
					configMap);
			list.add(response);
		}

		// 子节点为空 返回null 前端需求
		if(ListUtils.isEmpty(list)){
			return null;
		}
		return list;
	}

	private static AutoArchiveMenuDTO getMenuListDTO(MenuDO menuDO, List<MenuDO> menuDOList,boolean isReadOnly,Set<String> menuIdList,
													 MenuNode menuTree,
													 Map<String,AutoArchiveRuleDO> archiveRuleDOMap,
													 Map<String,FormResult> formListMap,
													 List<AutoArchiveOperatorDO> autoArchiveOperatorDOS,
													 Map<String, String> configMap){
		AutoArchiveMenuDTO response = new AutoArchiveMenuDTO();
		response.setMenuId(menuDO.getMenuId());
		response.setMenuName(menuDO.getName());
		response.setOrder(menuDO.getOrder());
		Map<String, String> formFileNameMap = new HashMap<>();
		if(StringUtils.isNotBlank(menuDO.getBindingFormId())){
			FormResult formResult = formListMap.get(menuDO.getBindingFormId());
			if(formResult != null){
				response.setBindingFormName(formResult.getFormName());
				formFileNameMap = formResult.getFields().stream()
						.collect(Collectors.toMap(FieldResult::getFieldId,FieldResult::getFieldName));
			}
		}

		AutoArchiveRuleDO archiveRuleDO = archiveRuleDOMap.get(menuDO.getMenuId());
		if(archiveRuleDO != null){
			response.setAutoArchiveNum(archiveRuleDO.getAutoArchiveNum());
			response.setRuleStatus(archiveRuleDO.getRuleStatus());
			response.setRuleId(archiveRuleDO.getUuid());
			response.setUnityForm(menuDO.getUnityForm());
			response.setCreateTime(archiveRuleDO.getCreateTime());
			response.setUpdateTime(archiveRuleDO.getModifiedTime());
			response.setTopTime(archiveRuleDO.getTopTime());
			response.setPath(menuDO.getPath());
			//设置条件标题头
			List<String> fileNames = new ArrayList<>();
			for (AutoArchiveOperatorDO x : autoArchiveOperatorDOS){
				if(x.getArchiveRuleId().equals(archiveRuleDO.getUuid())){
					if(configMap.get(x.getFieldId()) != null){
						fileNames.add(configMap.get(x.getFieldId()));
					}else {
						fileNames.add(formFileNameMap.get(x.getFieldId()));
					}
				}
			}
			response.setFieldNames(fileNames);

		}else {
			response.setAutoArchiveNum(0);
		}
		//设置菜单是否有权限进行归档操作
		if(!isReadOnly || menuIdList.contains(menuDO.getMenuId())){
			response.setReadOnly(false);
		}
		response.setChildNode(setChildMenus(menuDO.getMenuId(), menuDOList,response.isReadOnly(),
				menuIdList,menuTree,archiveRuleDOMap,formListMap, autoArchiveOperatorDOS, configMap));
		//对菜单进行排序操作
		if(!ListUtils.isEmpty(response.getChildNode())){
			response.getChildNode().sort(Comparator.comparing(AutoArchiveMenuDTO::getOrder));
		}
		return response;
	}

	/**
	 * 检查规则对比合同设置偏好是否匹配(只有选择合同偏好设置-归档设置-完成合同 才能选择合同状态归档条件)
	 */
	public void checkStatusComparePreferences(AutoArchiveOperatorRequest operatorRequest,UserAccount userAccountTenant) {

		List<AutoArchiveOperator> collect = operatorRequest.getConditions().stream()
				.filter(item -> StringUtils.equals(item.getFieldId(), OperationConditionStrategyEnum.CONTRACT_STATUS.getFieldId())).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(collect)) {
			return;
		}

		ProcessStatusResult optionalStatusList = getOptionalStatusList(userAccountTenant.getAccountOid());

		List<Integer> canChoiceStatus = optionalStatusList.getProcessStatusList().stream()
				.filter(item -> Boolean.TRUE.equals(item.getFlag()))
				.map(ProcessStatusModel::getStatus).collect(Collectors.toList());

		for (AutoArchiveOperator autoArchiveOperator : collect) {
			autoArchiveOperator.getChildOperators().forEach(childOperator -> {
				List<ProcessStatus> conditionList =
						JSON.parseArray(childOperator.getConditionParams(), ProcessStatus.class);
				conditionList.forEach(item -> {
					if (!canChoiceStatus.contains(item.getStatus())) {
						throw new BizContractManagerException(BizContractManagerResultCodeEnum.RULE_NOT_ADAPT_PREFERENCES);
					}
				});
			});
		}


	}

	@Override
	public ProcessStatusResult getOptionalStatusList(String tenantId) {
		UserAccount userAccountTenant = userCenterService.getUserAccountBaseByOid(tenantId);

		QueryBizPreferenceModel model = new QueryBizPreferenceModel();
		model.setOrgId(userAccountTenant.getAccountOid());
		model.setAppId(userAccountTenant.getAppId());
		model.setOrgGid(userAccountTenant.getAccountGid());
		model.setPreferenceKeys(Collections.singletonList(ProcessPreferenceEnum.PROCESS_ARCHIVE_RANGE.getKey()));
		PreferenceModel preferenceModel = preferencesService.queryBizPreference(model);
		ProcessStatusResult res = new ProcessStatusResult();

		boolean all = StringUtils.equals(preferenceModel.getPreferences().get(0).getPreferenceValue(),
				PreferenceConstant.PROCESS_ARCHIVE_RANGE_ALL);

		res.setProcessStatusList(ProcessStatusEnum.optionalFinalStatusList().stream().map(item -> {
					ProcessStatusModel processStatusModel = new ProcessStatusModel();
					if (ProcessStatusEnum.DONE.equals(item)) {
						processStatusModel.setStatus(item.getStatus());
						processStatusModel.setStatusDesc(item.getStatusDesc());
						processStatusModel.setFlag(Boolean.TRUE);
						return processStatusModel;
					}
					processStatusModel.setStatus(item.getStatus());
					processStatusModel.setStatusDesc(item.getStatusDesc());
					processStatusModel.setFlag(all);
					return processStatusModel;
				}).collect(Collectors.toList()));
		return res;
	}

}

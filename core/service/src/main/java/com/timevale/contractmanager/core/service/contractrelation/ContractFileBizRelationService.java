package com.timevale.contractmanager.core.service.contractrelation;

import com.timevale.contractmanager.common.dal.bean.ContractFileBizRelationDO;
import com.timevale.contractmanager.core.model.bo.FileBO;

import java.util.List;
import java.util.Map;

/**
 * 合同文件关联信息
 *
 * <AUTHOR>
 * @since 2022-10-27 10:38
 */
public interface ContractFileBizRelationService {

    /**
     * 保存一批合同关联数据
     *
     * @param list
     */
    void saveBatch(List<ContractFileBizRelationDO> list);

    /**
     * 更新合同关联数据
     * @param processId
     * @param newList
     */
    void updateBatch(String processId, List<ContractFileBizRelationDO> newList);

    /**
     * 为传入的文件生成文件列表
     *
     * @param files 文件列表
     * @return Map<fileId, contractNo>
     */
    Map<String, String> genContractNo(List<FileBO> files);

    /**
     * 获取一个合同流程的所有文件关联信息
     *
     * @param processId
     * @return
     */
    List<ContractFileBizRelationDO> listByProcessId(String processId);

    /**
     * 获取一个合同流程的所有文件关联信息，根据processId和状态
     *
     * @param processId
     * @param status
     * @return
     */
    List<ContractFileBizRelationDO> listByProcessIdAndStatus(String processId, Integer status);

    /**
     * 获取一个合同流程的所有文件关联信息
     * @param processId
     * @param fileIds
     * @param deleteStatus
     * @return
     */
    List<ContractFileBizRelationDO> listByFileIdsAndStatus(String processId, List<String> fileIds, Integer deleteStatus);

    /**
     * 按照fileId批量删除规则
     *
     * @param processId
     */
    void invalidByProcessId(String processId);

    /**
     * 判断规则参数是否正确
     *
     * @param type
     * @param ruleId
     * @return
     */
    boolean validateRule(Integer type, String ruleId);

    /**
     * 修正合同编号类型
     *
     * @param file
     */
    void amendContractNoType(FileBO file);


    /**
     * 转移合同文件的关联关系（更新为最新）
     * @param processId
     * @param toSignFileMap
     */
    void shiftContractFileBizRelations(String processId, Map<String, String> toSignFileMap);
}

package com.timevale.contractmanager.core.service.watermark.bean;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: contract-manager-project
 * @Description: 策略生成水印快照的返回结果
 * @date Date : 2023年05月11日 20:09
 */
@Data
public class AddWatermarkResult extends ToString {

    /**
     * 是否是屏幕水印
     */
    private boolean isScreenWatermark;


    /**
     * 屏幕水印ID
     * isScreenWatermark =true 取此值
     */
    private String screenWatermarkId;

    /**
     * 文件ID和对应的水印快照ID
     *  isScreenWatermark = false 取此值
     */
    private Map<String, String> originalAndWatermarkFileIdMap;

}

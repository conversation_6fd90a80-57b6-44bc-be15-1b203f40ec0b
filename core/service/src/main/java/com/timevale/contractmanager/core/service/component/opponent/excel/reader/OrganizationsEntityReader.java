package com.timevale.contractmanager.core.service.component.opponent.excel.reader;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.core.model.bo.opponent.ExcelInfoBO;
import com.timevale.contractmanager.core.model.bo.opponent.OpponentEntityExcelReadBO;
import com.timevale.contractmanager.core.model.enums.OpponentEntityCreditCodeTypeEnum;

import java.util.LinkedHashMap;
import java.util.Objects;

/**
 * 企业相对方实体读取
 *
 * <AUTHOR>
 * @since 2021-02-02 15:46
 */
public class OrganizationsEntityReader implements OpponentEntityReader {

    /** 表格列数 */
    public static final int HEAD_COUNT = 5;

    @Override
    public OpponentEntityExcelReadBO read(LinkedHashMap<Integer, Object> data) {
        OpponentEntityExcelReadBO excelReadBO = new OpponentEntityExcelReadBO();
        ExcelInfoBO excelInfo = new ExcelInfoBO();
        // 读取每列的值
        for (int i = 0; i < HEAD_COUNT; i++) {
            Object object = data.get(i);
            // 非空校验
            if (null == object) {
                // 为空时添加空字符串保证列的完整性
                excelReadBO.getData().add("");
                continue;
            }
            excelReadBO.getData().add(object);
            switch (i) {
                case 0:
                    // 企业名称
                    excelInfo.setOrganizationName(object.toString().trim());
                    break;
                case 1:
                    // 企业证件类型
                    OpponentEntityCreditCodeTypeEnum byName =
                            OpponentEntityCreditCodeTypeEnum.getByName(object.toString().trim());
                    if (Objects.nonNull(byName)) {
                        excelInfo.setCreditCodeType(byName.getCode());
                    }
                    break;
                case 2:
                    // 企业证件代码
                    excelInfo.setSocialCreditCode(object.toString().trim());
                    break;
                case 3:
                    // 法定代表人姓名
                    excelInfo.setLegalPersonName(object.toString().trim());
                    break;
                case 4:
                    // 备注
                    excelInfo.setDescription(object.toString());
                    break;
                default:
                    // 不在处理范围内
                    break;
            }
        }
        excelReadBO.setEntity(excelInfo);
        return excelReadBO;
    }
}

package com.timevale.contractmanager.core.service.contractprocess.processor.cooperation;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.*;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.CooperationChangeTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.CooperationChangeMsgEntity;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessCooperationTaskDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessCooperationTaskParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Created by tianlei on 2022/5/11
 */
@Slf4j
@Component
public class CooperationTaskChangeDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessReadClient contractProcessQueryClient;
    @Autowired
    private LockService lockService;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.cooperationTopicName(), CooperationChangeTagEnum.COOPERATION_TASK_CHANGE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        CooperationChangeMsgEntity entity =
                JsonUtils.json2pojo(data, CooperationChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {

        Lock lock = lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                doProcess(collectContext);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }

    private void doProcess(ProcessDataCollectContext collectContext) {

        CooperationChangeMsgEntity cooperationChangeMsgEntity = (CooperationChangeMsgEntity) collectContext.getData();

        String processId = collectContext.getProcessId();
        String taskId = cooperationChangeMsgEntity.getTaskId().toString();

        ContractProcessDTO processDTO = contractProcessQueryClient.getByProcessId(processId);

        List<ContractProcessCooperationTaskDTO> cooperationTaskDTOS = processDTO.getCooperationTasks();
        if (CollectionUtils.isEmpty(cooperationTaskDTOS)) {
            log.warn("processId : {} cooperation data not has initValue", processId);
            cooperationTaskDTOS = new ArrayList<>();
        }

        List<ContractProcessCooperationTaskParam> cooperationTaskParams =
                ProcessDataCollectConverter.cooperationTask2ParamList(cooperationTaskDTOS);

        boolean findExistData = false;
        Integer existIndex = null;
        for (int i = 0; i < cooperationTaskParams.size(); i++) {
            if (Objects.equals(taskId, cooperationTaskParams.get(i).getTaskId())) {
                findExistData = true;
                existIndex = i;
                break;
            }
        }

        ContractProcessCooperationTaskParam param = processDataBuilder.buildUpdateWriteTask(processId, taskId);
        if (findExistData) {
            // 该taskId已经存在

            // 转移transfer
            param.setTransfer(cooperationTaskParams.get(existIndex).getTransfer());
            // 把值覆盖掉
            cooperationTaskParams.set(existIndex, param);

            ContractProcessUpdateParam input = new ContractProcessUpdateParam();
            input.setProcessId(processId);
            input.setCooperationTasks(cooperationTaskParams);
            input.setBizScene(ProcessDataCollectBizSceneConstants.COOPERATION_TASK_CHANGE);
            contractProcessWriteClient.updateByProcessId(input);
        } else {
            // 该taskId不存在, 任务加进去然后更新
            cooperationTaskParams.add(param);

            ContractProcessUpdateParam input = new ContractProcessUpdateParam();
            input.setProcessId(processId);
            input.setCooperationTasks(cooperationTaskParams);
            input.setBizScene(ProcessDataCollectBizSceneConstants.COOPERATION_TASK_CHANGE);
            contractProcessWriteClient.updateByProcessId(input);
        }


    }

}

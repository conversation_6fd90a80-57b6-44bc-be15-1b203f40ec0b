package com.timevale.contractmanager.core.service.component.grouping;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.AccountDeptInfo;
import com.timevale.contractmanager.common.service.bean.OrderAccountBean;
import com.timevale.contractmanager.common.service.enums.SourceEnum;
import com.timevale.contractmanager.common.service.enums.grouping.MatchFieldEnum;
import com.timevale.contractmanager.common.service.integration.client.AuthRelationRpcServiceClient;
import com.timevale.contractmanager.common.service.integration.client.DocCooperationClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.dto.process.GroupingProcessListQueryContext;
import com.timevale.contractmanager.core.model.dto.request.grouping.process.QueryGroupingProcessListRequest;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.v2.ProcessSealInfo;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.v2.ProcessTemplateInfo;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.CustomListDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.doccooperation.service.input.ListFlowTemplateInput;
import com.timevale.doccooperation.service.model.BaseFlowTemplate;
import com.timevale.doccooperation.service.result.PageResult;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.manage.common.service.model.output.AccountVipQueryOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationHistoryLastEffectiveTimeDTO;
import com.timevale.signflow.search.docSearchService.bean.AccountBase;
import com.timevale.signflow.search.docSearchService.bean.DeptInfo;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.bean.SealInfo;
import com.timevale.signflow.search.docSearchService.bean.TaskInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.bean.TemplateInfo;
import com.timevale.signflow.search.docSearchService.enums.TaskTypeEnum;
import com.timevale.signflow.search.docSearchService.param.GroupingQueryParam;
import com.timevale.signflow.search.service.bean.v2.query.SubSubjectQueryParam;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * GroupingPropertyComponent
 *
 * <AUTHOR>
 * @since 2021/10/15 10:23 上午
 */
@Slf4j
@Component
public class GroupingPropertyComponent {

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private AuthRelationRpcServiceClient authRelationRpcServiceClient;

    @Autowired
    private DocCooperationClient docCooperationClient;

    @Autowired
    private MapperFactory mapperFactory;

    @Autowired
    private SaasCommonClient saasCommonClient;

    public ProcessTemplateInfo buildProcessTemplateInfo(String tenantId, Map<String, BaseFlowTemplate> ownerTemplateMap, ProcessAccount processAccount, TemplateInfo templateInfo) {
        ProcessTemplateInfo processTemplateInfo = new ProcessTemplateInfo();

        if (templateInfo == null) {
            processTemplateInfo.setTemplateName("未使用模板");
            return processTemplateInfo;
        }

        boolean isCurrentOrgan = processAccount.getSubject().getOrgan() && tenantId.equals(processAccount.getSubject().getOid());

        String subjectName = isCurrentOrgan ? "" : "(" + processAccount.getSubject().getName() + ")";
        if (templateInfo.getOriginTemplateId() != null) {
            processTemplateInfo.setTemplateId(templateInfo.getOriginTemplateId());
            processTemplateInfo.setTemplateName(templateInfo.getOriginTemplateName() + subjectName);
        } else {
            processTemplateInfo.setTemplateId(templateInfo.getTemplateId());
            processTemplateInfo.setTemplateName(templateInfo.getTemplateName() + subjectName);
        }
        //如果使用总部模板，显示总部企业的名称
        if (!ownerTemplateMap.isEmpty()) {
            BaseFlowTemplate baseFlowTemplate = ownerTemplateMap.get(processTemplateInfo.getTemplateId());
            if (baseFlowTemplate != null && baseFlowTemplate.isShared() && !baseFlowTemplate.getOwnerGid().equals(processAccount.getSubject().getGid())) {
                if (tenantId.equals(baseFlowTemplate.getOwnerOid())) {
                    processTemplateInfo.setTemplateName(templateInfo.getTemplateName());
                } else {
                    processTemplateInfo.setTemplateName(templateInfo.getTemplateName() + "(" + baseFlowTemplate.getOwnerName() + ")");
                }
            }
        }
        processTemplateInfo.setTemplateType(templateInfo.getTemplateType() == null ? 1 : templateInfo.getTemplateType());
        return processTemplateInfo;
    }

    public List<ProcessSealInfo> buildSealInfoList(String tenantId, List<TaskInfoTotalInfo> taskInfo) {
        List<ProcessSealInfo> processSealInfoList = new ArrayList<>();

        for (TaskInfoTotalInfo taskInfoTotalInfo : taskInfo) {
            if (!taskInfoTotalInfo.getTaskType().equals(TaskTypeEnum.SIGN.getType())) {
                continue;
            }
            List<SealInfo> sealInfoList = taskInfoTotalInfo.getSealInfo();
            if (CollectionUtils.isEmpty(sealInfoList)) {
                continue;
            }
            boolean isCurrentOrgan = taskInfoTotalInfo.getExecute().getSubject().getOrgan() && tenantId.equals(taskInfoTotalInfo.getExecute().getSubject().getOid());
            String subjectName = isCurrentOrgan ? "" : taskInfoTotalInfo.getExecute().getSubject().getName();
            
            for (SealInfo sealInfo : sealInfoList) {
                ProcessSealInfo processSealInfo = new ProcessSealInfo();
                if (StringUtils.isEmpty(sealInfo.getSealName())) {
                    processSealInfo.setSealName("-");
                } else {
                    processSealInfo.setSealName(sealInfo.getSealName());
                }
                processSealInfo.setSubjectName(subjectName);
                processSealInfo.setSealId(sealInfo.getSealId());
                processSealInfo.setBizType(sealInfo.getBizType());
                processSealInfoList.add(processSealInfo);
            }
        }
        return processSealInfoList;
    }

    public AccountDeptInfo buildAccountDeptInfo(String tenantId, ProcessAccount processAccount) {
        AccountDeptInfo accountDeptInfo = new AccountDeptInfo();
        List<DeptInfo> deptInfoList = processAccount.getDeptInfo();
        if (CollectionUtils.isEmpty(deptInfoList)) {
            return null;
        }
        DeptInfo ownerDept = null;

        boolean isCurrentOrgan = processAccount.getSubject().getOrgan() && tenantId.equals(processAccount.getSubject().getOid());

        if (!processAccount.getSubject().getOrgan()) {
            accountDeptInfo.setDeptName("个人发起");
        } else {
            List<DeptInfo> ownerDeptList = deptInfoList.stream().filter(DeptInfo::getCurrent).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ownerDeptList)) {
                ownerDept = ownerDeptList.get(0);
            }
            if (ownerDept == null) {
                accountDeptInfo.setDeptName("未分配部门");
            } else {
                String deptName = ownerDept.getDeptName();
                accountDeptInfo.setDeptId(ownerDept.getDeptId());
                deptName = isCurrentOrgan ? findParentDeptName(ownerDept, deptInfoList, deptName) : deptName + "(" + processAccount.getSubject().getName() + ")";
                accountDeptInfo.setDeptName(deptName);
            }
        }
        return accountDeptInfo;
    }

    private String findParentDeptName(DeptInfo ownerDept, List<DeptInfo> deptInfoList, String deptName) {
        if (ownerDept.getParentId() == null) {
            return null;
        }
        for (DeptInfo deptInfo : deptInfoList) {
            if (deptInfo.getDeptId().equals(ownerDept.getParentId())) {
                if (deptName != null) {
                    deptName = deptInfo.getDeptName() + "-" + deptName;
                } else {
                    deptName = deptInfo.getDeptName();
                }
                return findParentDeptName(deptInfo, deptInfoList, deptName);
            }
        }
        return deptName;
    }

    public CustomListDTO removeCustomProperty(CustomListDTO customListDTO, String tenantOid, String clientId) {
        if (tenantOid == null) {
            return customListDTO;
        }
        UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOid);
        String tenantGid = tenantAccount == null ? null : tenantAccount.getAccountGid();
        customListDTO = removeAffiliatedEnterpriseProperty(customListDTO, tenantGid);
        customListDTO = removeInitiateDeptProperty(customListDTO, clientId);
//        customListDTO = removeContractNoProperty(customListDTO, tenantGid);
        return customListDTO;
    }



    public CustomListDTO removeInitiateDeptProperty(CustomListDTO customListDTO, String clientId) {
        if (StringUtils.isBlank(clientId)) {
            return customListDTO;
        }

        if (clientId.equals(SourceEnum.DING_TALK.getCode()) || clientId.equals(SourceEnum.DING_TALK_LABOR.getCode())) {
            customListDTO.getDisplayList().removeIf(customField -> MatchFieldEnum.INITIATOR_DEPT.getCode().equals(customField.getFieldCode()));
            customListDTO.getHiddenList().removeIf(customField -> MatchFieldEnum.INITIATOR_DEPT.getCode().equals(customField.getFieldCode()));
        }
        return customListDTO;
    }

    public CustomListDTO removeAffiliatedEnterpriseProperty(CustomListDTO customListDTO, String tenantGid) {
        if (tenantGid == null) {
            return customListDTO;
        }
        //判断当前登录企业是否是主企业
        Boolean wasParent = authRelationRpcServiceClient.checkTenantWasParent(tenantGid);
        if (wasParent) {
            return customListDTO;
        }
        customListDTO.getDisplayList().removeIf(customField -> MatchFieldEnum.AFFILIATED_ENTERPRISE.getCode().equals(customField.getFieldCode()));
        customListDTO.getHiddenList().removeIf(customField -> MatchFieldEnum.AFFILIATED_ENTERPRISE.getCode().equals(customField.getFieldCode()));
        return customListDTO;
    }

    public CustomListDTO removeCustomProperty(CustomListDTO customListDTO, String tenantOid, String clientId, Boolean wasParent) {
        customListDTO = removeAffiliatedEnterpriseProperty(customListDTO, wasParent);
        customListDTO = removeInitiateDeptProperty(customListDTO, clientId);
        UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOid);
        String tenantGid = tenantAccount == null ? null : tenantAccount.getAccountGid();
//        customListDTO = removeContractNoProperty(customListDTO, tenantGid);
        return customListDTO;
    }

    public CustomListDTO removeAffiliatedEnterpriseProperty(CustomListDTO customListDTO, Boolean wasParent) {
        if (wasParent) {
            return customListDTO;
        }
        customListDTO.getDisplayList().removeIf(customField -> MatchFieldEnum.AFFILIATED_ENTERPRISE.getCode().equals(customField.getFieldCode()));
        customListDTO.getHiddenList().removeIf(customField -> MatchFieldEnum.AFFILIATED_ENTERPRISE.getCode().equals(customField.getFieldCode()));
        return customListDTO;
    }

    public Map<String, BaseFlowTemplate> buildOwnerTemplateMap(Set<String> flowTemplateIdList) {
        if (CollectionUtils.isEmpty(flowTemplateIdList)) {
            return new HashMap<>();
        }
        flowTemplateIdList = flowTemplateIdList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(flowTemplateIdList)) {
            return new HashMap<>();
        }
        Map<String, BaseFlowTemplate> map = new HashMap<>();
        //分页查询
        List<List<String>> loopFlowTempList = Lists.partition(new ArrayList<>(flowTemplateIdList), 50);

        for (List<String> tempList : loopFlowTempList){
            searchFlowTemplate(map, new HashSet<>(tempList));
        }
        return map;
    }

    /**
     * 搜索模板列表
     * @param map
     * @param loopFlowTempList
     */
    private void searchFlowTemplate(Map<String, BaseFlowTemplate> map, Set<String> loopFlowTempList){
        ListFlowTemplateInput listFlowTemplateInput = new ListFlowTemplateInput();
        listFlowTemplateInput.setFlowTemplateIdList(loopFlowTempList);
        listFlowTemplateInput.setContainsDynamic(true);
        listFlowTemplateInput.setContainsDesignatedOrg(true);
        PageResult<BaseFlowTemplate> pageResult = docCooperationClient.listFlowTemplate(listFlowTemplateInput);
        if (CollectionUtils.isNotEmpty(pageResult.getList())) {
            Map<String, BaseFlowTemplate> mapResult = pageResult.getList().stream().collect(Collectors.toMap(BaseFlowTemplate::getFlowTemplateId, Function.identity()));
            map.putAll(mapResult);
        }
    }

    public com.timevale.contractmanager.common.service.bean.ProcessAccount buildAffiliatedEnterprises(String subjectGid, List<String> childTenantGidList, List<TaskInfoTotalInfo> taskInfo, List<ProcessAccount> participant,
            List<ProcessAccount> cc,
            ProcessAccount initiatorAccount) {
        if (CollectionUtils.isEmpty(childTenantGidList)) {
            return new com.timevale.contractmanager.common.service.bean.ProcessAccount();
        }
        if (StringUtils.isNotEmpty(subjectGid) && initiatorAccount.getSubject() != null && initiatorAccount.getSubject().getOrgan() && StringUtils.isNotEmpty(initiatorAccount.getSubject().getGid()) && initiatorAccount.getSubject().getGid()
                .equals(subjectGid)) {
            return mapperFactory.getMapperFacade().map(initiatorAccount, com.timevale.contractmanager.common.service.bean.ProcessAccount.class);
        }
        List<OrderAccountBean> processAccountList = new ArrayList<>();
        com.timevale.contractmanager.common.service.bean.ProcessAccount result = new com.timevale.contractmanager.common.service.bean.ProcessAccount();
        if (initiatorAccount.getSubject() != null && childTenantGidList.contains(initiatorAccount.getSubject().getGid())) {
            OrderAccountBean orderAccountBean = mapperFactory.getMapperFacade().map(initiatorAccount, OrderAccountBean.class);
            orderAccountBean.setOrder(1);
            processAccountList.add(orderAccountBean);
        }

        if (CollectionUtils.isNotEmpty(taskInfo)) {
            for (TaskInfoTotalInfo taskInfoTotalInfo : taskInfo) {
                if (taskInfoTotalInfo.getExecute() != null && taskInfoTotalInfo.getExecute().getSubject() != null && taskInfoTotalInfo.getExecute().getSubject().getOrgan() && childTenantGidList
                        .contains(taskInfoTotalInfo.getExecute().getSubject().getGid())) {
                    OrderAccountBean orderAccountBean = mapperFactory.getMapperFacade().map(taskInfoTotalInfo.getExecute(), OrderAccountBean.class);
                    if (taskInfoTotalInfo.getTaskType().equals(TaskTypeEnum.SIGN.getType())) {
                        orderAccountBean.setOrder(2);
                    } else {
                        orderAccountBean.setOrder(3);
                    }
                    processAccountList.add(orderAccountBean);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(cc)) {
            for (ProcessAccount processAccount : cc) {
                if (processAccount != null && processAccount.getSubject() != null && processAccount.getSubject().getOrgan() != null
                        && processAccount.getSubject().getOrgan() && childTenantGidList.contains(processAccount.getSubject().getGid())) {
                    OrderAccountBean orderAccountBean = mapperFactory.getMapperFacade().map(processAccount, OrderAccountBean.class);
                    orderAccountBean.setOrder(4);
                    processAccountList.add(orderAccountBean);
                }
            }
        }
        boolean existSign = processAccountList.stream().anyMatch(t -> t.getOrder().equals(2));
        //操作人如果只有填写人，判断下参与方是否有签署人
        if (CollectionUtils.isNotEmpty(participant) && !existSign) {
            List<OrderAccountBean> cooperationProcessAccountList = processAccountList.stream().filter(t -> t.getOrder().equals(3)).collect(Collectors.toList());
            List<String> cooperationAccountGidList = cooperationProcessAccountList.stream().map(t -> t.getSubject().getGid()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cooperationAccountGidList)) {
                for (AccountBase accountBase : participant) {
                    //如果不是参与人不是填写方，则是签署人员，加入到排序中
                    if (accountBase.getSubject() != null && accountBase.getSubject().getOrgan() && childTenantGidList.contains(accountBase.getSubject().getGid()) && !cooperationAccountGidList.contains(accountBase.getSubject().getGid())) {
                        OrderAccountBean orderAccountBean = mapperFactory.getMapperFacade().map(accountBase, OrderAccountBean.class);
                        orderAccountBean.setOrder(2);
                        processAccountList.add(orderAccountBean);
                    }
                }
            }
        }
        processAccountList = processAccountList.stream().sorted(Comparator.comparing(OrderAccountBean::getOrder)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(processAccountList)) {
            OrderAccountBean orderAccountBean = processAccountList.get(0);
            result = mapperFactory.getMapperFacade().map(orderAccountBean, com.timevale.contractmanager.common.service.bean.ProcessAccount.class);
        }
        return result;
    }

    public void fillSubSubjectListParam(QueryGroupingProcessListRequest request, GroupingQueryParam queryParam, GroupingProcessListQueryContext context) {
        if (queryParam.getSubject() == null || StringUtils.isEmpty(queryParam.getSubject().getGid())) {
            return;
        }
        List<SubSubjectQueryParam> subSubjectList = new ArrayList<>();
        List<AuthRelationHistoryLastEffectiveTimeDTO> authRelationDTOList = null;
        if(context != null && BooleanUtils.isTrue(context.isQueryChildTenant())){
            if(CollectionUtils.isNotEmpty(context.getChildTenantList())){
                authRelationDTOList = context.getChildTenantList();
            }
        } else {
            authRelationDTOList = authRelationRpcServiceClient.queryAuthRelationLastEffectiveTimeByTenantGid(queryParam.getSubject() == null ? null : queryParam.getSubject().getGid());
        }
        if (CollectionUtils.isEmpty(authRelationDTOList)) {
            return;
        }
        String subjectOid = queryParam.getSubject().getOid();
        Long lastTime = null;
        if (StringUtils.isNotEmpty(subjectOid)) {
            boolean result = saasCommonClient.checkFunctionValid(subjectOid, FunctionCodeConstant.AFFILIATED_ENTERPRISES, false);
            if (result) {
                AccountVipQueryOutput accountVipQueryOutput = saasCommonClient.queryAccountVip(subjectOid, RequestContextExtUtils.getClientId());
                if (accountVipQueryOutput != null && accountVipQueryOutput.getEffectiveTo() != null) {
                    lastTime = accountVipQueryOutput.getEffectiveTo().getTime();
                }
            }
        }
        if (CollectionUtils.isNotEmpty(request.getSubSubjectGidList())) {
            authRelationDTOList = authRelationDTOList.stream().filter(t -> request.getSubSubjectGidList().contains(t.getChildTenantGid())).collect(Collectors.toList());
            queryParam.setSubSubjectSearch(true);
            if (CollectionUtils.isEmpty(authRelationDTOList)) {
                //强制走v2版本
                SubSubjectQueryParam subSubjectQueryParam = new SubSubjectQueryParam();
                subSubjectQueryParam.setSubjectGid("notExist");
                subSubjectQueryParam.setEndTime(System.currentTimeMillis());
                subSubjectList.add(subSubjectQueryParam);
            }
        }
        for (AuthRelationHistoryLastEffectiveTimeDTO authRelationDTO : authRelationDTOList) {
            SubSubjectQueryParam subSubjectQueryParam = new SubSubjectQueryParam();
            subSubjectQueryParam.setSubjectGid(authRelationDTO.getChildTenantGid());
            if (lastTime == null) {
                subSubjectQueryParam.setEndTime(authRelationDTO.getLastEffectiveTime().getTime());
            } else {
                subSubjectQueryParam.setEndTime(lastTime < authRelationDTO.getLastEffectiveTime().getTime() ? lastTime : authRelationDTO.getLastEffectiveTime().getTime());
            }
            subSubjectList.add(subSubjectQueryParam);
        }
        //特殊处理，可以支持搜索自身
        if (CollectionUtils.isNotEmpty(request.getSubSubjectGidList()) && queryParam.getSubject().getGid() != null && request.getSubSubjectGidList().contains(queryParam.getSubject().getGid())) {
            SubSubjectQueryParam subSubjectQueryParam = new SubSubjectQueryParam();
            subSubjectQueryParam.setSubjectGid(queryParam.getSubject().getGid());
            subSubjectQueryParam.setEndTime(System.currentTimeMillis());
            subSubjectList.add(subSubjectQueryParam);
        }
        queryParam.setSubSubjectList(subSubjectList);
    }
    
}

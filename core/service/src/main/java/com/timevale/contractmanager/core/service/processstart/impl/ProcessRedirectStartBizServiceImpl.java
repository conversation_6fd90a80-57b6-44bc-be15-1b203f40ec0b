package com.timevale.contractmanager.core.service.processstart.impl;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum;
import com.timevale.contractmanager.common.service.enums.process.ProcessSignSealSizeTypeEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.core.model.bo.ParticipantsSignConfigBO;
import com.timevale.contractmanager.core.model.bo.StartSuccessCacheBO;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.common.service.enums.ProcessBusinessType;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.process.ProcessConfigService;
import com.timevale.contractmanager.core.service.process.impl.ProcessStartHelper;
import com.timevale.contractmanager.core.service.processstart.ProcessStartBizService;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContext;
import com.timevale.contractmanager.core.service.processstart.impl.base.ProcessStartBizBaseService;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum.PROCESS_SIGN_FILED_SIZE_TYPE;
import static com.timevale.contractmanager.common.service.enums.grouping.PrivilegeOperationEnum.DIRECT_START;
import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_OPERATE_NO_AUTH;

/**
 * 直接发起业务类
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Slf4j
@Service
public class ProcessRedirectStartBizServiceImpl extends ProcessStartBizBaseService
        implements ProcessStartBizService {

    @Autowired ProcessStartHelper processStartHelper;
    @Autowired MapperFactory mapperFactory;
    @Autowired UserCenterService userCenterService;
    @Autowired PreferencesService preferencesService;
    @Autowired ProcessConfigService processConfigService;

    @Override
    public ProcessStartScene startScene() {
        return ProcessStartScene.DIRECT_START;
    }

    @Override
    public List<ProcessStartType> supportStartTypes() {
        return Lists.newArrayList(
                ProcessStartType.NORMAL_DIRECT_START,
                ProcessStartType.SHARE_SCAN_DIRECT_START,
                ProcessStartType.DING_ISV_DIRECT_START,
                ProcessStartType.DIRECT_START_DATA_SOURCE);
    }

    /**
     * 业务参数校验
     *
     * @param request
     */
    private void validParam(
            ProcessStartBizRequest request, ProcessStartContext startContext) {
        // 通用参数校验
        ValidationUtil.validateBean(request);
        UserAccountDetail account = startContext.getAccount();
        UserAccountDetail tenant = startContext.getTenant();

        // 校验合同到期时间是否可用
//        checkContractValidity(request, startContext);  专属云注释

        // 校验参与方数量是否达到上限
//        checkParticipantCount(request, startContext); 专属云注释

        // 校验是否支持或签
//        checkParticipantOrSign(request, startContext);  专属云注释

        // 校验参与方附件配置是否达到上限
//        checkParticipantAttachmentConfig(request,startContext); 专属云注释

        // 校验AI手绘是否可用,  换成新的校验方式
//        checkAiDrawAvailable(startContext, request.getParticipants());  海外签注释掉

        // 校验是否支持水印
        //checkWatermark(request,startContext); 专属云注释

        // 非重新发起场景
        if (!ProcessBusinessType.RESTART.getBusinessType().equals(request.getBusinessType())) {
            // 校验指定意愿方式是否可用
//            checkAssignWillTypeAvailable(startContext, request.getParticipants());
            // 校验签署批注是否可用
            checkSignRemarkAvailable(startContext, request.getFlowTemplateId());
        }

        // 校验发起人及发起主体是否实名
//        checkRealNamed(account, tenant); 专属云注释
        // 企业直接发起 权限校验
        checkRedirectStartPrivilege(request, startContext);
        // 合同审批id校验
        checkContractApprovalTemplate(request, startContext);
        // 校验发起人是否有设置合同保密的权限
//        checkProcessSecretPrivilege(request, startContext); 专属云注释
//         校验签署设置
//        processStartHelper.checkParticipantsSignConfig(tenant, request.getParticipants(), startContext.getFunctionContext());
        // 新的参与方配置校验
//        signSetProcessService.checkAndChangeParam(startContext, request, request.getParticipants()); 专属云注释
        // 其他校验
    }

    /**
     * 发起流程， start方法调用之前， 需先调用checkParamValidity， 这块逻辑在ProcessStartHandler内部封装
     *
     * @param request
     * @return
     */
    @Override
    public ProcessStartResult start(ProcessStartContext context, ProcessStartBizRequest request) {
        // 查询发起主体信息
        UserAccountDetail tenant = context.getTenant();
        // 查询发起人信息
        UserAccountDetail initiator = context.getAccount();
        // 业务参数校验
        validParam(request, context);
        // 转换ProcessStartCoreRequest
        ProcessStartCoreRequest coreRequest =
                mapperFactory.getMapperFacade().map(request, ProcessStartCoreRequest.class);
        processStartHelper.populateConfig(coreRequest, request);
        coreRequest.setParticipants(request.getParticipants());
        coreRequest.setInitiatorAccount(initiator);
        coreRequest.setTenantAccount(tenant);
        coreRequest.setOperatorAccount(context.getOperatorAccount());
        coreRequest.setPayerAccount(request.getPayerAccount());
        //设置签署区大小是否自适应
        coreRequest.setSignSealSizeType(checkSignFiledSizeAdaptable(context));
        // 文件校验 and 组装合同保密配置
        coreRequest.setProcessSecretConfig(checkFileAndBuildSecretConfig(request, tenant));
        // 判断并设置是否支持扫码核验
        coreRequest.setCanProcessCheck(hasQrCodeStruct(request.getFlowTemplateId(),tenant));
        // 发起流程
        ProcessStartResult startResult = startFlow(request, coreRequest, context);
        //保存发起请求缓存
        cacheStartRequest(startResult.getProcessId(), coreRequest, tenant);
        //缓存直接发起的签署设置
        cacheStartParticipantsSignConfig(startResult.getProcessId(), initiator.getAccountOid(), tenant.getAccountOid(), coreRequest);
        // 组装返回数据
        return startResult;
    }

    private String checkSignFiledSizeAdaptable(ProcessStartContext context) {
        return context.getPreferenceData().getOrDefault(PROCESS_SIGN_FILED_SIZE_TYPE.getKey(), ProcessSignSealSizeTypeEnum.DEFAULT.getCode());
    }


    private void cacheStartParticipantsSignConfig(String processId, String operatorId, String tenantOid, ProcessStartCoreRequest coreRequest) {
        if (Strings.isBlank(processId)) {
            return;
        }
        try {
            List<ParticipantsSignConfigBO> cache = mapperFactory.getMapperFacade().mapAsList(coreRequest.getParticipants(), ParticipantsSignConfigBO.class);
            StartSuccessCacheBO startSuccessCacheBO = new StartSuccessCacheBO();
            startSuccessCacheBO.setSignMode(coreRequest.getSignMode());
            startSuccessCacheBO.setWatermarkTemplateId(coreRequest.getUseWatermarkTemplateId());
            startSuccessCacheBO.setParticipantsSignConfigs(cache);
            // 不缓存签署声明内容
            cache.forEach(i -> {
                i.setSignTipsTitle(null);
                i.setSignTipsContent(null);
            });
            if (CollectionUtils.isNotEmpty(cache)) {
                processConfigService.startSuccessCache(startSuccessCacheBO, operatorId, tenantOid, coreRequest.getClientId());
            }
        } catch (Exception e) {
            //不影响主流程
            log.warn("保存发起签署设置缓存异常,processId:{}", processId, e);
        }
    }

    /**
     * 企业直接发起 权限校验
     *
     * @param request
     * @param context
     */
    private void checkRedirectStartPrivilege(
            ProcessStartBizRequest request, ProcessStartContext context) {
        // 个人主体默认有直接发起权限， 跳过
        if (!context.getTenant().isOrganize()) {
            return;
        }
        // 判断是否跳过直接发起权限鉴权， 如果是， 跳过
        // 可跳过直接发起鉴权场景：一、指定跳过鉴权 二、解约/续签
        if (Boolean.TRUE.equals(request.getSkipStartValid())
                || ProcessBusinessType.RESCIND.getBusinessType().equals(request.getBusinessType())
                || ProcessBusinessType.RENEW.getBusinessType().equals(request.getBusinessType())) {
            return;
        }
        // 如果企业开启了全局的直接发起权限， 则默认用户都有权限， 跳过
        if (preferencesService.isProcessDirectSwitch(
                context.getTenant().getAccountOid(), context.getTenant().getAccountGid())) {
            return;
        }
        // 企业未开启全局的直接发起权限， 则校验用户是否被分配了直接发起权限， 如果没有，报错
        PrivilegeOperationEnum operation = DIRECT_START;
        if (!userCenterService.checkUserHasPrivilege(request.getTenantId(), request.getAccountId(), PrivilegeResourceEnum.CONTRACT.getType(), operation.getType())) {
            throw new BizContractManagerException(PROCESS_OPERATE_NO_AUTH, operation.getDesc());
        }
    }
}

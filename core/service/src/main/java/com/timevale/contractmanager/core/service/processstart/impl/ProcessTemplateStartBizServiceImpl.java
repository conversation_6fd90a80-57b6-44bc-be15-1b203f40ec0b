package com.timevale.contractmanager.core.service.processstart.impl;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.enums.ProcessBusinessType;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.process.ProcessStartDataModel;
import com.timevale.contractmanager.core.model.dto.process.StartCoreDataSource;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartBizRequest;
import com.timevale.contractmanager.core.model.dto.request.ProcessStartCoreRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartDetailResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResult;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.core.service.grouping.AuthService;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.core.service.process.bean.FlowTemplateDetailParseConfig;
import com.timevale.contractmanager.core.service.process.datasource.ProcessStartDataManager;
import com.timevale.contractmanager.core.service.processstart.ProcessStartBizService;
import com.timevale.contractmanager.core.service.processstart.impl.base.ProcessStartBizBaseService;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContext;
import com.timevale.doccooperation.service.enums.*;
import com.timevale.doccooperation.service.exception.DocCooperationBizException;
import com.timevale.doccooperation.service.model.CommonStartExtensionKeyConstants;
import com.timevale.doccooperation.service.model.Cooperationer;
import com.timevale.doccooperation.service.model.FlowInfo;
import com.timevale.doccooperation.service.model.FlowTemplateConfig;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.privilege.service.enums.PrivilegeOperationRegister;
import ma.glasnost.orika.MapperFactory;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.timevale.doccooperation.service.exception.DocCooperationErrorEnum.BATCH_START_PARAM_ERROR;


/**
 * 模板发起业务类
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Service
public class ProcessTemplateStartBizServiceImpl extends ProcessStartBizBaseService
        implements ProcessStartBizService {

    @Autowired AuthService authService;
    @Autowired MapperFactory mapperFactory;
    @Autowired FlowTemplateService flowTemplateService;
    @Autowired SystemConfig systemConfig;
    @Autowired
    private ProcessStartDataManager processStartDataManager;

    @Override
    public ProcessStartScene startScene() {
        return ProcessStartScene.TEMPLATE_START;
    }

    @Override
    public List<ProcessStartType> supportStartTypes() {
        return Lists.newArrayList(
                ProcessStartType.NORMAL_TEMPLATE_START,
                ProcessStartType.SHARE_SCAN_TEMPLATE_START,
                ProcessStartType.DING_TEMPLATE_RELATE_START,
                ProcessStartType.FLOW_TEMPLATE_DATA_SOURCE);
    }

    /**
     * 业务参数校验
     *
     * @param request
     */
    private void validParam(
            ProcessStartBizRequest request, ProcessStartContext startContext) {
        // 通用参数校验
        ValidationUtil.validateBean(request);
        // 发起人
        UserAccountDetail account = startContext.getAccount();
        // 发起主体
        UserAccountDetail tenant = startContext.getTenant();

        // 处理合同到期时间，如果不支持就置空
//        handelContractValidity(request, startContext); // 专属云注释

        // 校验参与方数量是否达到上限
//        checkParticipantCount(request, startContext);  专属云注释

        // 校验参与方附件配置是否达到上限
//        checkParticipantAttachmentConfig(request,startContext); 专属云注释

        // 校验发起人及发起主体是否实名
//        checkRealNamed(account, tenant);  专属云注释

        // 合同审批id校验
        checkContractApprovalTemplate(request, startContext);

        //水印判断是否有权限
//        checkWatermark(request,startContext); 专属云注释

        // 校验发起人是否有设置合同保密的权限(不再校验 2022-8-22)
        //        checkProcessSecretPrivilege(request, tenant);
        // 当前场景可能存在两个入口调用的场景，一种是直接发起指定位置场景，一种是模板发起，只有模板发起场景需要校验模板使用权限
        // 如果batch环节已经校验过，则转单个后可忽略此步骤
        if (StringUtils.isBlank(request.getProcessGroupId())
                && !ProcessStartType.skipTemplateAuth(request.getStartType())
                && ProcessStartScene.TEMPLATE_START.getScene() == request.getStartScene()) {
            String action = PrivilegeOperationRegister.USE;
            authService.auth(action, tenant, request.getFlowTemplateId(), request.getSignPlatform());
        }

        // 校验签署设置
//        processStartHelper.checkParticipantsSignConfig(tenant, request.getParticipants(), startContext.getFunctionContext());
        // 新的参与方配置校验 - 转移到上层
//        signSetUpService.checkAndChangeParam(startContext, request, request.getParticipants());
        // 其他校验
    }

    @Override
    public ProcessStartResult start(ProcessStartContext context, ProcessStartBizRequest request) {
        // 查询发起主体信息
        UserAccountDetail tenant = context.getTenant();
        // 查询发起人信息
        UserAccountDetail initiator = context.getAccount();
        // 处理模板数据
        handleRefTemplateInfo(request, tenant);
        // 业务参数校验
        validParam(request, context);
        // 转换ProcessStartBizRequest
        ProcessStartCoreRequest coreRequest =
                mapperFactory.getMapperFacade().map(request, ProcessStartCoreRequest.class);
        processStartHelper.populateConfig(coreRequest, request);
        coreRequest.setParticipants(request.getParticipants());
        coreRequest.setInitiatorAccount(initiator);
        coreRequest.setTenantAccount(tenant);
        coreRequest.setOperatorAccount(context.getOperatorAccount());
        coreRequest.setPayerAccount(request.getPayerAccount());
        // 文件校验 and 组装合同保密配置
        //如果发起client不支持合同保密配置，则无需合同保密配置
        if(!systemConfig.getNotSupportSecretConfigClientList().contains(request.getClientId())){
            coreRequest.setProcessSecretConfig(checkFileAndBuildSecretConfig(request, tenant));
        }
        // 判断并设置是否支持扫码核验
        coreRequest.setCanProcessCheck(hasQrCodeStruct(request.getFlowTemplateId(),tenant));
        //发起前置处理
        startBefore(context, request, coreRequest);
        // 发起流程
        ProcessStartResult startResult = startFlow(request, coreRequest, context);
        //保存发起请求缓存
        cacheStartRequest(startResult.getProcessId(),coreRequest,tenant);
        // 组装返回数据
        return startResult;
    }

    /**
     * 处理关联模板数据
     *
     * @param request
     * @param tenant
     * @return boolean 是否存在关联流程
     */
    private void handleRefTemplateInfo(ProcessStartBizRequest request, UserAccount tenant) {
        // 发起方式为普通模板发起或钉钉模板关联审批单发起
        if((ProcessStartType.NORMAL_TEMPLATE_START.getType().equals(request.getStartType())
                || ProcessStartType.DING_TEMPLATE_RELATE_START.getType().equals(request.getStartType())
                || ProcessStartType.FLOW_TEMPLATE_DATA_SOURCE.getType().equals(request.getStartType())
            )
            && StringUtils.isNotBlank(request.getFlowTemplateId())){
            GetFlowTemplateResult response = processStartContextService.getFlowTemplateResult(request.getFlowTemplateId(), tenant);
            checkFlowTemplateEnable(response);
            checkFlowTemplateCooperationers(request, response);
            //打上标签
            request.setEpaasTemplateTag(response.isEpaasTag());
            FlowInfo flowInfo = response.getFlowInfo();
            //修改参与方签署方式
            updateSignSealType(response.getCooperationers(), request.getParticipants());
            if (null != flowInfo && BooleanUtils.isTrue(flowInfo.getForUnStandardStart())){
                //非标模板发起，不需要删除临时模板（TODO 重新发起重构后需要删除临时模板）
                request.setDeleteTempFlowTemplate(false);
                //非标模板发起，创建流程时需要传originFlowTemplateId，只是不会传给签署这个值
                request.addExtension(CommonStartExtensionKeyConstants.UN_STANDARD_TEMPLATE_START, "true");
                String flowTemplateId = StringUtils.defaultIfBlank(request.getOriginFlowTemplateId(), request.getRefFlowTemplateId());
                if (StringUtils.isBlank(flowTemplateId)) {
                    flowTemplateId = response.getRefFlowTemplateId();
                }
                request.addExtension(CommonStartExtensionKeyConstants.ORIGIN_FLOW_TEMPLATE_ID, flowTemplateId);
                //非标模板发起，不能使用原模板创建流程
                request.setRefFlowTemplateId(null);
                return;
            }
            FlowTemplateConfig templateConfig = response.getTemplateConfig();
            // 判断是否重新发起
            boolean restart = ProcessBusinessType.RESTART.getBusinessType().equals(request.getBusinessType());
            // 判断是否临时模板
            boolean tempFlowTemplate = FlowTemplateTypeEnum.TEMP.getType() == response.getType();
            // 判断是否扫码发起使用的模板
            boolean shareScanUseScene = null != templateConfig && FlowTemplateUseSceneEnum.SHARE_SIGN.getScene().equals(templateConfig.getUseScene());
            // 重新发起使用扫码发起临时模板场景下, 如果临时模板关联了正式的流程模板， 设置原模板id
            if (restart && tempFlowTemplate && shareScanUseScene) {
                String originFlowTemplateId = StringUtils.defaultIfBlank(request.getOriginFlowTemplateId(), response.getRefFlowTemplateId());
                request.addExtension(CommonStartExtensionKeyConstants.ORIGIN_FLOW_TEMPLATE_ID, originFlowTemplateId);
                request.setOriginFlowTemplateId(originFlowTemplateId);
            }
        }
        /**
         * 模版发起-预览模版-确认提交 场景时 refFlowTemplateId非空 startRequest.flowTemplateId对应的是新创建的flowTemplate
         * 它起到临时保存预览前填写数据(参与人实例、新增的附件、新增的抄送人、修改的processName)的作用
         * 所以这种场景需要读refFlowTemplate构建完整的模版实例化信息来发起流程
         */
        if (StringUtils.isBlank(request.getRefFlowTemplateId())) {
            return;
        }
        // 获取真正的流程模板数据， 用于构建完整的实例化信息
        GetFlowTemplateResult flowTemplateDetail =
                processStartContextService.getFlowTemplateResult(
                        request.getRefFlowTemplateId(), tenant);
        FlowTemplateDetailParseConfig config = new FlowTemplateDetailParseConfig();
        config.setSubject(tenant);
        config.setWithDocSource(false);
        config.setWithFileCategory(true);
        config.setWithWatermarkConfig(false);
        ProcessStartDetailResponse response =
                flowTemplateService.parseFlowTemplateDetail(flowTemplateDetail, config);
        Map<Integer, List<FileBO>> useTemplateFiles =
                processStartHelper.checkFile(response.getFiles(), false, tenant);
        if (useTemplateFiles != null) {
            mergeContractInfo(
                    request.getContracts(),
                    useTemplateFiles.get(ProcessFileType.CONTRACT_FILE.getType()));
            request.setContracts(useTemplateFiles.get(ProcessFileType.CONTRACT_FILE.getType()));
        }
        // 特别是指定位置和预览发起场景
        if (null == request.getSecretType()) {
            request.setSecretType(response.getSecretType());
        }

        /** 从refFlowTemplate中拿cooperationer非实例化信息补充participants */
        if (CollectionUtils.isNotEmpty(response.getParticipants())) {
            request.convertParticipantId(response.getParticipants());
        }

        /** 后续struct等静态模版信息从refFlowTemplate取 */
        request.setTempFlowTemplateId(request.getFlowTemplateId());
        request.setFlowTemplateId(request.getRefFlowTemplateId());
    }

    private void checkFlowTemplateEnable(GetFlowTemplateResult flowTemplate) {
        if (!Objects.equals(FlowTemplateStatusEnum.ENABLE.getStatus(), flowTemplate.getStatus())) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.FLOW_TEMPLATE_NOT_ENABLE);
        }
    }

    /**
     * 校验流程模板参与方
     * @param request
     * @param flowTemplateResult
     */
    private void checkFlowTemplateCooperationers(ProcessStartBizRequest request, GetFlowTemplateResult flowTemplateResult) {
        // 校验协作方数量
        if (flowTemplateResult.getCooperationers().size() != request.getParticipants().size()) {
            throw new DocCooperationBizException(BATCH_START_PARAM_ERROR, "协作方实例和流程模板协作方数量不一致");
        }

        // 获取流程模板协作方id列表
        Set<String> cooperationerIdSet = flowTemplateResult.getCooperationers().stream().map(i -> i.getCooperationerId()).collect(Collectors.toSet());

        // 校验协作方是否在流程模板中及是否重复
        Set<String> participantIdSet = Sets.newHashSet();
        for (ParticipantBO participant : request.getParticipants()) {
            String participantId = participant.getParticipantId();
            // 校验协作方是否在流程模板中
            if (!cooperationerIdSet.contains(participantId)) {
                throw new DocCooperationBizException(BATCH_START_PARAM_ERROR, "流程模板中未找到协作方");
            }
            // 校验协作方是否重复
            if (participantIdSet.contains(participantId)) {
                throw new DocCooperationBizException(BATCH_START_PARAM_ERROR, "协作方id不可重复");
            } else {
                participantIdSet.add(participantId);
            }
        }
    }

    private void updateSignSealType(List<Cooperationer> cooperationers, List<ParticipantBO> participants){
        if (CollectionUtils.isEmpty(cooperationers) || CollectionUtils.isEmpty(participants)) {
            return;
        }

        //获取指定签署类型的协作方
        Map<String, Cooperationer> cooperationerMap = cooperationers
                .stream()
                .filter(cooperationer -> Optional.ofNullable(cooperationer)
                        .map(Cooperationer::getExt)
                        .map(Cooperationer.Ext::getSignSealType)
                        .map(type -> StartSignSpecifySealTypeEnum.BIZ_TYPE.getType() == type||StartSignSpecifySealTypeEnum.SEAL_ID.getType() == type)
                        .orElse(false))
                .collect(Collectors.toMap(Cooperationer::getCooperationerId, Function.identity(), (o, n) -> n));
        if (CollectionUtils.isEmpty(cooperationerMap)) {
            return;
        }

        //修改对应的参与方签署方式
        for (ParticipantBO participant : participants) {
            Cooperationer cooperationer = cooperationerMap.get(participant.getParticipantId());
            if (cooperationer == null || cooperationer.getExt() == null) {
                continue;
            }

            participant.setSignSealType(cooperationer.getExt().getSignSealType());
            participant.setSignSeal(cooperationer.getExt().getSignSeal());
        }
    }

    private void mergeContractInfo(List<FileBO> reqFiles, List<FileBO> userTemplateFiles) {
        if (CollectionUtils.isEmpty(reqFiles) || CollectionUtils.isEmpty(userTemplateFiles)) {
            return;
        }
        Map<String, FileBO> reqMap =
                reqFiles.stream().collect(Collectors.toMap(FileBO::getFileId, Function.identity()));

        for (FileBO templateFile : userTemplateFiles) {
            FileBO reqFile = reqMap.get(templateFile.getFileId());
            if (Objects.isNull(reqFile)) {
                continue;
            }

            templateFile.setContractNo(reqFile.getContractNo());
            templateFile.setContractNoType(reqFile.getContractNoType());
            templateFile.setContractNoRule(reqFile.getContractNoRule());
        }
    }



    private void startBefore(ProcessStartContext context, ProcessStartBizRequest request, ProcessStartCoreRequest coreRequest) {
        // 参与方
        processParticipant(context, request);
        // 数据源
        processDataSource(context, request, coreRequest);
    }

    /**
     * 处理参与方
     * @param context
     * @param request
     */
    private static void processParticipant(ProcessStartContext context, ProcessStartBizRequest request) {
        if (null == context.getFlowTemplateDetail()) {
            return;
        }
        List<Cooperationer> cooperationers = context.getFlowTemplateDetail().getCooperationers();
        if (CollectionUtils.isEmpty(cooperationers)) {
            return;
        }
        Map<String, Cooperationer> cooperationerMap = cooperationers.stream().collect(Collectors.toMap(i -> i.getCooperationerId(), i -> i));
        for (ParticipantBO participant : request.getParticipants()) {
            Cooperationer cooperationer = cooperationerMap.get(participant.getParticipantId());
            if (null == cooperationer || null == cooperationer.getExt()) {
                continue;
            }
            // 设置通知方式, 优先获取流程模板参与方设置的通知方式
            List<Integer> noticeTypes = cooperationer.getExt().getNoticeTypes();
            participant.setNoticeTypes(null != noticeTypes ? noticeTypes : participant.getNoticeTypes());
        }
    }

    private void processDataSource(ProcessStartContext context, ProcessStartBizRequest request, ProcessStartCoreRequest coreRequest) {
        if (!ProcessStartType.FLOW_TEMPLATE_DATA_SOURCE.getType().equals(request.getStartType())) {
            return;
        }
        String dataId = request.getStartDataSource().getDataId();

        //1. 处理数据源
        ProcessStartDataModel processStartDataModel = processStartDataManager.getByDataId(dataId);
        StartCoreDataSource coreDataSource = new StartCoreDataSource();
        coreDataSource.setDataId(processStartDataModel.getDataId());
        coreDataSource.setDataSourceId(processStartDataModel.getDataSourceId());
        coreDataSource.setDataSourceChannel(processStartDataModel.getDataSourceChannel());
        coreDataSource.setOuterDataId(processStartDataModel.getOuterDataId());
        coreRequest.setStartDataSource(coreDataSource);
    }

}

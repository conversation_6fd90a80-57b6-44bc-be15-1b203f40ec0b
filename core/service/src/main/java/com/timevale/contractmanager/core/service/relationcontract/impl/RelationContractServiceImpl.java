package com.timevale.contractmanager.core.service.relationcontract.impl;

import com.google.common.collect.Lists;
import com.timevale.contractanalysis.facade.api.dto.relationcontract.RelationContractDTO;
import com.timevale.contractanalysis.facade.api.dto.relationcontract.RelationContractRelationCountDTO;
import com.timevale.contractanalysis.facade.api.enums.RelationContractRelationSourceEnum;
import com.timevale.contractanalysis.facade.api.request.relationcontract.QueryProcessRelationProcessRequest;
import com.timevale.contractanalysis.facade.api.request.relationcontract.QueryRelationCountRequest;
import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.bean.process.ContractFileBean;
import com.timevale.contractmanager.common.service.enums.PrivilegeResourceEnum;
import com.timevale.contractmanager.common.service.enums.ProcessFromEnum;
import com.timevale.contractmanager.common.service.enums.grouping.FilePermissionEnum;
import com.timevale.contractmanager.common.service.enums.grouping.MenuIdEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.EsClient;
import com.timevale.contractmanager.common.service.integration.client.RelationContractClient;
import com.timevale.contractmanager.core.model.dto.process.BuildQueryAllHavePermissionProcessResultDTO;
import com.timevale.contractmanager.core.model.dto.process.BuildQueryMenuAllProcessResultDTO;
import com.timevale.contractmanager.core.model.dto.request.relationcontract.RelationContractChooseProcessRequest;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractAccount;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractChooseProcessVO;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractProcessAccount;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractProcessParticipantAccountVO;
import com.timevale.contractmanager.core.model.dto.response.relationcontract.RelationContractProcessVO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.enums.RelationContractListEntranceType;
import com.timevale.contractmanager.core.service.grouping.MenuService;
import com.timevale.contractmanager.core.service.grouping.PermissionService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.ProcessUserHavePermissionDataService;
import com.timevale.contractmanager.core.service.relationcontract.RelationContractService;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.contractmanager.core.service.util.AssertX;
import com.timevale.contractmanager.core.service.util.ProcessUtils;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.AccountBase;
import com.timevale.signflow.search.docSearchService.bean.ContractFileInfo;
import com.timevale.signflow.search.docSearchService.bean.CustomizeConfig;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.bean.ProcessGrouping;
import com.timevale.signflow.search.docSearchService.bean.ProcessInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.bean.TaskInfoTotalInfo;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import com.timevale.signflow.search.docSearchService.enums.TaskStatusEnum;
import com.timevale.signflow.search.docSearchService.param.QueryByProcessIdsParam;
import com.timevale.signflow.search.docSearchService.param.RelationContractChooseQuery;
import com.timevale.signflow.search.docSearchService.result.DocQuerySimpleResult;
import com.timevale.signflow.search.docSearchService.result.ProcessInfoSimpleResult;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdResult;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdsResult;

import lombok.extern.slf4j.Slf4j;

import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/7/5
 */
@Slf4j
@Service
public class RelationContractServiceImpl implements RelationContractService {

    private static final Integer SEARCH_STATUS_ALL = 0;
    private static final Integer SEARCH_STATUS_DOING = 1;
    private static final Integer SEARCH_STATUS_COMPLETE = 2;


    @Autowired
    private EsClient docSearchClient;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private MenuService menuService;

    @Autowired
    private RelationContractClient relationContractClient;

    @Autowired
    private SensorService sensorService;

    @Autowired
    private ProcessUserHavePermissionDataService processUserHavePermissionService;

    static MapperFactory mapperFactory = new DefaultMapperFactory.Builder().build();

    @Override
    public Pair<List<RelationContractChooseProcessVO>, Long> chooseProcessList(RelationContractChooseProcessRequest chooseProcessRequest) {
        String tenantOid = chooseProcessRequest.getTenantOid();
        String operatorOid = chooseProcessRequest.getOperatorOid();
        Integer pageNum = chooseProcessRequest.getPageNum();
        Integer pageSize = chooseProcessRequest.getPageSize();
        String currentProcessId = chooseProcessRequest.getCurrentProcessId();
        Integer processSearchStatus = chooseProcessRequest.getProcessSearchStatus();
        String fuzzyMatching = chooseProcessRequest.getFuzzyMatching();
        String title = chooseProcessRequest.getTitle();
        String personName = chooseProcessRequest.getPersonName();
        String subjectName = chooseProcessRequest.getSubjectName();
        String account = chooseProcessRequest.getAccount();
        String contractNo = chooseProcessRequest.getContractNo();

        AssertX.isTrue(null != pageNum, "pageNum 必传");
        AssertX.isTrue(null != pageSize, "pageSize 必传");
        processSearchStatus = Optional.ofNullable(processSearchStatus).orElse(SEARCH_STATUS_ALL);

        AccountBean operatorAccount = userCenterService.getAccountBeanByOid(operatorOid);
        AssertX.isTrue(null != operatorAccount, "当前用户不存在");

        AccountBean tenantAccount = userCenterService.getAccountBeanByOid(tenantOid);
        AssertX.isTrue(null != tenantAccount, "当前主体不存在");

        String personGid = operatorAccount.getGid();
        String tenantGid = tenantAccount.getGid();


        RelationContractChooseQuery param = new RelationContractChooseQuery();
        param.setTenantGid(tenantGid);
        param.setTenantOid(tenantOid);
        param.setPageNum(pageNum);
        param.setPageSize(pageSize);
        param.setFuzzyMatching(fuzzyMatching);
        param.setTitle(title);
        param.setPersonName(personName);
        param.setSubjectName(subjectName);
        param.setAccount(account);
        param.setPersonGid(personGid);
        param.setContractNo(contractNo);

        // 状态
        if (SEARCH_STATUS_ALL.equals(processSearchStatus)) {
            param.setProcessStatusList(ProcessStatusEnum.getAllProcessStatusList());
        } else if (SEARCH_STATUS_DOING.equals(processSearchStatus)) {
            param.setProcessStatusList(
                    Lists.newArrayList(ProcessStatusEnum.WRITING.getStatus(),
                            ProcessStatusEnum.SIGNING.getStatus(),
                            ProcessStatusEnum.APPROVE.getStatus()));
        } else if (SEARCH_STATUS_COMPLETE.equals(processSearchStatus)) {
            param.setProcessStatusList(Lists.newArrayList(ProcessStatusEnum.DONE.getStatus()));
        }


        BuildQueryAllHavePermissionProcessResultDTO havePermissionQueryParamDTO =
                processUserHavePermissionService.buildCommonQuery(tenantOid, operatorOid);
        param.setSubSubjectQueryParam(havePermissionQueryParamDTO.getWaitingGroupQueryParam());
        BuildQueryMenuAllProcessResultDTO queryMenuAllProcessResultDTO =
                havePermissionQueryParamDTO.getQueryMenuAllProcessResultDTO();
        param.setNeedQueryPersonGrouping(queryMenuAllProcessResultDTO.getSearchPersonRelated());
        param.setMenuIdList(queryMenuAllProcessResultDTO.getMenuIds());


        // search
        DocQuerySimpleResult result = docSearchClient.queryRelationContractChooseList(param);
        List<RelationContractChooseProcessVO> processVOList = chooseProcessListConvert(result.getProcessInfoList());
        if (CollectionUtils.isEmpty(processVOList)) {
            return Pair.of(processVOList, result.getTotal());
        }


        List<String> processIds = processVOList.stream()
                .map(RelationContractChooseProcessVO::getProcessId)
                .collect(Collectors.toList());

        // 是否达到最大关联数量
        QueryRelationCountRequest request = new QueryRelationCountRequest();
        request.setProcessIds(processIds);
        request.setTenantGid(tenantGid);
        Map<String, Integer> processIdRelationCountMap = relationContractClient.queryRelationCount(request)
                        .stream().collect(Collectors.toMap(RelationContractRelationCountDTO::getProcessId, RelationContractRelationCountDTO::getCount, (k1, k2) -> k2));
        //是否已关联
        Set<String> guestProcessIdSet = new HashSet<>();
        if (StringUtils.isNotBlank(currentProcessId)) {
            QueryProcessRelationProcessRequest queryProcessRelationProcessRequest = new QueryProcessRelationProcessRequest();
            queryProcessRelationProcessRequest.setTenantGid(tenantGid);
            queryProcessRelationProcessRequest.setMasterProcessId(currentProcessId);
            List<String> guestProcessIdsSet =
                   Optional.ofNullable(relationContractClient.queryProcessRelationProcess(queryProcessRelationProcessRequest))
                           .orElse(new ArrayList<>())
                           .stream()
                           .map(RelationContractDTO::getGuestProcessId)
                           .collect(Collectors.toList());
            guestProcessIdSet.addAll(guestProcessIdsSet);
        }

        Integer maxCount = relationContractClient.maxRelationCount();
        for (RelationContractChooseProcessVO contractChooseProcessVO : processVOList) {
            String processId = contractChooseProcessVO.getProcessId();
            // 是否已经达到最大数量
            Integer thisRelationCount = processIdRelationCountMap.get(processId);
            contractChooseProcessVO.setToMaxRelationCount(thisRelationCount != null && thisRelationCount.compareTo(maxCount) >=0);
            // 已经存在关联关系，“自己”也算一种关联
            contractChooseProcessVO.setExistRelation(guestProcessIdSet.contains(processId));
        }

        return Pair.of(processVOList, result.getTotal());
    }

    @Override
    public List<RelationContractProcessVO> list(String tenantOid,
                                                String operatorOid,
                                                String processId, Integer entranceType, String menuId) {

        AssertX.isTrue(StringUtils.isNotBlank(tenantOid), "tenantOid 必传");
        AssertX.isTrue(StringUtils.isNotBlank(operatorOid), "operatorOid 必传");
        AssertX.isTrue(StringUtils.isNotBlank(processId), "processId 必传");
        AssertX.isTrue(null != entranceType, "entranceType 必传");

        RelationContractListEntranceType entranceTypeEnum = RelationContractListEntranceType.from(entranceType);
        AssertX.isTrue(null != entranceTypeEnum, "未知的 entranceType");
//        if (RelationContractListEntranceType.ENTERPRISE_GROUPING_MENU == entranceTypeEnum ||
//                RelationContractListEntranceType.ENTERPRISE_GROUPING == entranceTypeEnum ) {
//            AssertX.isTrue(StringUtils.isNotBlank(menuId), "归档具体分类 menuId 必传");
//        }

        UserAccount tenantAccount = userCenterService.getUserAccountBaseByOid(tenantOid);
        String tenantGid = tenantAccount.getAccountGid();

        UserAccount operatorAccount = userCenterService.getUserAccountBaseByOid(operatorOid);
        String personGid = operatorAccount.getAccountGid();

        //拦截或签非执行人
        checkMultiExec(entranceTypeEnum, processId, operatorAccount, tenantAccount);

        QueryProcessRelationProcessRequest request = new QueryProcessRelationProcessRequest();
        request.setTenantGid(tenantGid);
        request.setMasterProcessId(processId);
        List<RelationContractDTO> relationContractList =
                relationContractClient.queryProcessRelationProcess(request);
        if (CollectionUtils.isEmpty(relationContractList)) {
            return new ArrayList<>();
        }

        // 是否有归档全局权限
        Set<String> menuIdSet = new HashSet<>();
        boolean hasGlobalQueryPermission =
                permissionService.checkGlobalPermission(
                        tenantOid,
                        operatorOid,
                        PrivilegeResourceEnum.ORG_ARCHIVE.name(),
                        FilePermissionEnum.QUERY.name());

        if (!hasGlobalQueryPermission) {
            menuIdSet.addAll(menuService.listByAuthAndChild(tenantOid, operatorOid, Lists.newArrayList(personGid)));
        }

        List<String> guestProcessIds = new ArrayList<>();
        Map<String, RelationContractDTO> guestProcessIdDataMap = new HashMap<>();
        for (RelationContractDTO contractModel : relationContractList) {
            guestProcessIds.add(contractModel.getGuestProcessId());
            guestProcessIdDataMap.put(contractModel.getGuestProcessId(), contractModel);
        }

        QueryByProcessIdsParam queryByProcessIdsParam = new QueryByProcessIdsParam();
        queryByProcessIdsParam.setProcessId(guestProcessIds);
        List<ProcessInfoTotalInfo> processInfoList = Optional.ofNullable(docSearchClient.queryByProcessIds(queryByProcessIdsParam))
                .map(QueryByProcessIdsResult::getProcessInfoTotalInfo)
                .orElse(new ArrayList<>());
        List<RelationContractProcessVO> contractProcessList = processListConvert(processInfoList,
                guestProcessIdDataMap, tenantOid, entranceTypeEnum, hasGlobalQueryPermission, menuIdSet);

        // 埋点
        sensorService.relationContractQuery(tenantGid, tenantAccount.getAccountName(), personGid, contractProcessList.size());
        return contractProcessList;
    }

    private void checkMultiExec(RelationContractListEntranceType entranceTypeEnum, String processId, UserAccount operatorAccount,
                                UserAccount subjectAccount) {
        if (!RelationContractListEntranceType.ABOUT_YOU.equals(entranceTypeEnum)) {
            //非经办合同不拦截
            return;
        }

        QueryByProcessIdResult queryByProcessIdResult = docSearchClient.queryById(processId);
        if (queryByProcessIdResult == null
                || queryByProcessIdResult.getProcessInfoTotalInfo() == null) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_NOT_EXIST);
        }
        ProcessFromEnum processFrom= ProcessFromEnum.from(queryByProcessIdResult.getProcessInfoTotalInfo().getProcessFrom());
        if(ProcessFromEnum.OFFLINE==processFrom){
            //线下合同只校验发起方主体
            Boolean isInitiatorOrg= isInitiatorOrg(subjectAccount==null?null:subjectAccount.getAccountGid(),queryByProcessIdResult.getProcessInfoTotalInfo());
            AssertX.isTrue(BooleanUtils.isTrue(isInitiatorOrg), "当前主体不是该线下合同发起方不能添加关联合同");
        }else {
            boolean relevant =
                    ProcessUtils.checkRelevantIgnoreMultiNotExec(
                            queryByProcessIdResult.getProcessInfoTotalInfo(),
                            operatorAccount,
                            subjectAccount,
                            null);
            if (!relevant) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.PROCESS_SIGNER_NOT_EXECUTOR, "关联合同");
            }
        }

    }

    @Override
    public List<RelationContractProcessVO> processInfo(String tenantOid, List<String> processIds) {
        //
        AccountBean tenantAccount = userCenterService.getAccountBeanByOid(tenantOid);
        String tenantGid = tenantAccount.getGid();

        //
        QueryByProcessIdsParam queryByProcessIdsParam = new QueryByProcessIdsParam();
        queryByProcessIdsParam.setProcessId(processIds);
        List<ProcessInfoTotalInfo> processInfoList = Optional.ofNullable(docSearchClient.queryByProcessIds(queryByProcessIdsParam))
                .map(QueryByProcessIdsResult::getProcessInfoTotalInfo)
                .orElse(new ArrayList<>());


        return processInfoList.stream()
                .filter(elm -> isParticipant(tenantGid, elm))
                .filter(elm -> notDelete(tenantGid, elm))
                .map(elm -> convert(elm))
                .collect(Collectors.toList());
    }

    @Override
    public Integer relationCount(String tenantOid, String processId) {
        AccountBean tenantAccount = userCenterService.getAccountBeanByOid(tenantOid);
        if (null == tenantAccount || StringUtils.isBlank(tenantAccount.getGid())) {
            return 0;
        }
        QueryRelationCountRequest request = new QueryRelationCountRequest();
        request.setProcessIds(Lists.newArrayList(processId));
        request.setTenantGid(tenantAccount.getGid());
        List<RelationContractRelationCountDTO> list = relationContractClient.queryRelationCount(request);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0).getCount();
        }
        return CollectionUtils.isNotEmpty(list) ? list.get(0).getCount() : 0;
    }

    private static List<RelationContractChooseProcessVO> chooseProcessListConvert(List<ProcessInfoSimpleResult> processInfoSimpleResultList) {
        if (CollectionUtils.isEmpty(processInfoSimpleResultList)) {
            return new ArrayList<>();
        }
        List<RelationContractChooseProcessVO> relationContractChooseProcessList = new ArrayList();
        for (ProcessInfoSimpleResult processInfoSimpleResult : processInfoSimpleResultList) {
            relationContractChooseProcessList.add(chooseProcessConvert(processInfoSimpleResult));
        }
        return relationContractChooseProcessList;
    }

    public static RelationContractChooseProcessVO chooseProcessConvert(ProcessInfoSimpleResult processInfoSimpleResult) {
        if (null == processInfoSimpleResult) {
            return null;
        }
        RelationContractChooseProcessVO relationContractChooseProcess = new RelationContractChooseProcessVO();
        relationContractChooseProcess.setProcessId(processInfoSimpleResult.getProcessId());
        relationContractChooseProcess.setTitle(processInfoSimpleResult.getTitle());
        relationContractChooseProcess.setCompleteTime(processInfoSimpleResult.getCompleteTime());
        ProcessStatusEnum processStatusEnum = ProcessStatusEnum.valueOf(processInfoSimpleResult.getProcessStatus());
        relationContractChooseProcess.setProcessStatusDesc(processStatusEnum != null ? processStatusEnum.getStatusDesc() : "-");
        relationContractChooseProcess.setProcessStatus(processInfoSimpleResult.getProcessStatus());

        relationContractChooseProcess.setInitiator(processAccountConvert(processInfoSimpleResult.getInitiatorAccount(),processInfoSimpleResult.getHideInitiator()));


        // 组装参与人
        Set<String> participantRemoveRepeatSet = new HashSet<>();
        List<RelationContractProcessParticipantAccountVO> participants = new ArrayList<>();

        // 填充通用逻辑
        BiConsumer<ProcessAccount, Integer> fillParticipantFunction = (execute, taskStatus) -> {
            if (null == execute || participantRemoveRepeatSet.contains(getRepeatKey(execute))) {
                return;
            }
            participants.add(processParticipantAccountConvert(execute, taskStatus));
            participantRemoveRepeatSet.add(getRepeatKey(execute));
        };

        // taskInfo
        if (CollectionUtils.isNotEmpty(processInfoSimpleResult.getTaskInfo())) {
            for (TaskInfoTotalInfo taskInfoTotalInfo : processInfoSimpleResult.getTaskInfo()) {
                ProcessAccount execute = taskInfoTotalInfo.getExecute();
                fillParticipantFunction.accept(execute, taskInfoTotalInfo.getStatus());
            }
        }

        //参与人
        if (CollectionUtils.isNotEmpty(processInfoSimpleResult.getParticipantAccountList())) {
            for (ProcessAccount execute : processInfoSimpleResult.getParticipantAccountList()) {
                fillParticipantFunction.accept(execute, null);
            }
        }
        relationContractChooseProcess.setParticipants(participants);
        relationContractChooseProcess.setContractFiles(fileInfo2BeanList(processInfoSimpleResult.getContractFiles()));
        return relationContractChooseProcess;
    }

    private static List<ContractFileBean> fileInfo2BeanList(List<ContractFileInfo> contractFileInfos) {
        if (CollectionUtils.isEmpty(contractFileInfos)) {
            return new ArrayList<>();
        }
        List<ContractFileBean> fileBeans = new ArrayList<>();
        for (ContractFileInfo fileInfo : contractFileInfos) {
            ContractFileBean fileBean = new ContractFileBean();
            fileBean.setFileName(fileInfo.getFileName());
            fileBean.setContractNo(fileInfo.getContractNo());
            fileBeans.add(fileBean);
        }
        return fileBeans;
    }

    private static RelationContractProcessAccount processAccountConvert(AccountBase processAccount, Boolean hideInitiator) {
        if (null == processAccount) {
            return null;
        }
        RelationContractProcessAccount relationContractProcessAccount = new RelationContractProcessAccount();
        relationContractProcessAccount.setTenant(accountConvert(processAccount.getSubject(),Boolean.FALSE));
        relationContractProcessAccount.setPerson(accountConvert(processAccount.getPerson(),hideInitiator));
        return relationContractProcessAccount;
    }

    // 参与人转换
    private static RelationContractProcessParticipantAccountVO processParticipantAccountConvert(AccountBase processAccount,
                                                                                                Integer taskStatus) {
        if (null == processAccount) {
            return null;
        }
        RelationContractProcessParticipantAccountVO relationContractProcessAccount = new RelationContractProcessParticipantAccountVO();
        relationContractProcessAccount.setTenant(accountConvert(processAccount.getSubject(),Boolean.FALSE));
        relationContractProcessAccount.setPerson(accountConvert(processAccount.getPerson(),Boolean.FALSE));

        boolean currentUser = taskStatus != null &&
                (TaskStatusEnum.WRITING.getStatus().equals(taskStatus) || TaskStatusEnum.SIGNING.getStatus().equals(taskStatus));
        relationContractProcessAccount.setCurrentUser(currentUser);
        return relationContractProcessAccount;
    }

    private static RelationContractAccount accountConvert(Account account, Boolean hideInitiator) {
        if (account == null) {
            return null;
        }
        RelationContractAccount relationContractAccount = new RelationContractAccount();
        relationContractAccount.setGid(account.getGid());
        relationContractAccount.setOid(account.getOid());
        relationContractAccount.setName(Boolean.TRUE.equals(hideInitiator) ? "-" : account.getName());
        relationContractAccount.setOrgan(account.getOrgan());
//        if (StringUtils.isNotBlank(account.getMobile())) {
//            try {
//                relationContractAccount.setMobile(SaasCommonDataMaskingUtil.maskingCnPhoneNo(account.getMobile()));
//            } catch (Exception e) {
//                log.error("masking phone", e);
//                relationContractAccount.setMobile(account.getMobile());
//            }
//        }
//        if (StringUtils.isNotBlank(account.getEmail())) {
//            try {
//                relationContractAccount.setEmail(SaasCommonDataMaskingUtil.maskingEmail(account.getEmail()));
//            } catch (Exception e) {
//                log.error("masking email", e);
//                relationContractAccount.setEmail(account.getEmail());
//            }
//        }
        relationContractAccount.setMobile(account.getMobile());
        relationContractAccount.setEmail(account.getEmail());
        relationContractAccount.setNickname(account.getNickname());
        relationContractAccount.setDeleted(account.getDeleted());
        return relationContractAccount;
    }

    private static String getRepeatKey(AccountBase execute) {
        if (null == execute) {
            return "";
        }
        return execute.getPerson().getOid() + "-" + execute.getSubject().getOid();
    }

    private static List<RelationContractProcessVO> processListConvert(List<ProcessInfoTotalInfo> processInfoList,
                                                                   Map<String, RelationContractDTO> guestProcessIdDataMap,
                                                                   String tenantOid,
                                                                   RelationContractListEntranceType entranceType,
                                                                   boolean hasGlobalPermission,
                                                                   Set<String> hasPermissionMenuIds) {
        if (CollectionUtils.isEmpty(processInfoList)) {
            return new ArrayList<>();
        }

        // 排序
        processInfoList.sort(Comparator.comparing(ProcessInfoTotalInfo::getProcessCreateTime)
                .reversed()
                .thenComparing(ProcessInfoTotalInfo::getProcessId));

        return processInfoList.stream().map(elm -> {

            String processId = elm.getProcessId();
            RelationContractProcessVO chooseProcess = convert(elm);

            // 是否可取消
            RelationContractDTO relationContractModel = guestProcessIdDataMap.get(processId);
            chooseProcess.setCanCancel(relationContractModel != null &&
                    RelationContractRelationSourceEnum.CAN_DELETE.contains(relationContractModel.getRelationSource()));


            // 设置menuId
            Supplier<ProcessGrouping> groupingSupplier = () -> {
                for (ProcessGrouping processGrouping : Optional.ofNullable(elm.getGroupingInfo()).orElse(new ArrayList<>())) {
                    if (Objects.equals(processGrouping.getSubjectId(), tenantOid) &&
                            CollectionUtils.isNotEmpty(processGrouping.getMenuIdList())) {
                        return processGrouping;
                    }
                }
                return null;
            };

            ProcessGrouping processGrouping = groupingSupplier.get();
            if (processGrouping != null && CollectionUtils.isNotEmpty(processGrouping.getMenuIdList())) {
                // 已归档
                if (hasGlobalPermission) {
                    // 有权限 随便返回一个
                    chooseProcess.setMenuId(processGrouping.getMenuIdList().get(0));
                } else {
                    // 没权限 返回一个有权限的menuId
                    Set<String> commonMenuIds = new HashSet<>(Optional.ofNullable(hasPermissionMenuIds).orElse(new HashSet<>()));
                    commonMenuIds.retainAll(processGrouping.getMenuIdList());
                    if (CollectionUtils.isNotEmpty(commonMenuIds)) {
                        // 返回一个有菜单的权限
                        chooseProcess.setMenuId((String) commonMenuIds.toArray()[0]);
                    }
                    // 找不到有权限的菜单，按经办合同去鉴权
                }

            } else {
                // 待归档交给后续逻辑去鉴权
                chooseProcess.setMenuId(MenuIdEnum.UNGROUPING.getMenuId());
            }

            return chooseProcess;
        }).collect(Collectors.toList());
    }


    private static RelationContractProcessVO convert(ProcessInfoTotalInfo elm) {
        RelationContractProcessVO chooseProcess = new RelationContractProcessVO();
        chooseProcess.setProcessId(elm.getProcessId());
        chooseProcess.setTitle(elm.getTitle());
        chooseProcess.setCompleteTime(elm.getCompleteTime());
        ProcessStatusEnum processStatusEnum = ProcessStatusEnum.valueOf(elm.getProcessStatus());
        chooseProcess.setProcessStatusDesc(processStatusEnum != null ? processStatusEnum.getStatusDesc() : "-");
        chooseProcess.setProcessStatus(elm.getProcessStatus());

        chooseProcess.setInitiator(processAccountConvert(elm.getInitiator(),elm.getHideInitiator()));


        // 组装参与人
        Set<String> participantRemoveRepeatSet = new HashSet<>();
        List<RelationContractProcessParticipantAccountVO> participants = new ArrayList<>();

        // 填充通用逻辑
        // 填充通用逻辑
        BiConsumer<AccountBase, Integer> fillParticipantFunction = (execute, taskStatus) -> {
            if (null == execute || participantRemoveRepeatSet.contains(getRepeatKey(execute))) {
                return;
            }
            participants.add(processParticipantAccountConvert(execute, taskStatus));
            participantRemoveRepeatSet.add(getRepeatKey(execute));
        };

        // taskInfo
        if (CollectionUtils.isNotEmpty(elm.getTaskInfo())) {
            for (TaskInfoTotalInfo taskInfoTotalInfo : elm.getTaskInfo()) {
                ProcessAccount execute = taskInfoTotalInfo.getExecute();
                fillParticipantFunction.accept(execute, taskInfoTotalInfo.getStatus());
            }
        }

        //参与人
        if (CollectionUtils.isNotEmpty(elm.getParticipant())) {
            for (AccountBase execute : elm.getParticipant()) {
                fillParticipantFunction.accept(execute, null);
            }
        }
        chooseProcess.setParticipants(participants);

        if (CollectionUtils.isNotEmpty(elm.getContractFiles())) {
            chooseProcess.setContractFiles(mapperFactory.getMapperFacade().mapAsList(elm.getContractFiles(), ContractFileBean.class));
        }
        return chooseProcess;
    }

    private static boolean  isParticipant(String tenantGid, ProcessInfoTotalInfo process) {
        if (null == process) {
            return false;
        }

        if (null != process.getInitiator() && process.getInitiator().getSubject() != null) {
            if (Objects.equals(process.getInitiator().getSubject().getGid(), tenantGid)) {
                return true;
            }
        }
        if (CollectionUtils.isNotEmpty(process.getTaskInfo())) {
            for (TaskInfoTotalInfo taskInfo : process.getTaskInfo()) {
                if (null != taskInfo.getExecute() && null != taskInfo.getExecute().getSubject()) {
                    if (Objects.equals(taskInfo.getExecute().getSubject().getGid(), tenantGid)) {
                        return true;
                    }
                }
                if (null != taskInfo.getOperator() && null != taskInfo.getOperator().getSubject()) {
                    if (Objects.equals(taskInfo.getOperator().getSubject().getGid(), tenantGid)) {
                        return true;
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(process.getCc())) {
            for (ProcessAccount processAccount : process.getCc()) {
                if (null != processAccount.getSubject()) {
                    if (Objects.equals(processAccount.getSubject().getGid(), tenantGid)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // 企业合同没被删除
    private static boolean notDelete(String tenantGid, ProcessInfoTotalInfo process) {
        if (null == process) {
            return false;
        }
        Optional<CustomizeConfig> optionalCustomizeConfig = Optional.ofNullable(process.getCustomizeConfig()).orElse(new ArrayList<>())
                .stream()
                .filter(elm -> Objects.equals(elm.getSubjectGid(), tenantGid)).findFirst();
        return optionalCustomizeConfig.map(customizeConfig -> !Boolean.TRUE.equals(customizeConfig.getHidden()))
                .orElse(true);
    }


    /**
     * 校验发起方主体
     * @param tenantGid
     * @param process
     * @return false 非发起方主体，true 发起方主体， null 表示 不满足校验条件
     */
    private static  Boolean isInitiatorOrg(String tenantGid, ProcessInfoTotalInfo process){
        if (null == process) {
            return false;
        }

        if (null != process.getInitiator() && process.getInitiator().getSubject() != null) {
            if (Objects.equals(process.getInitiator().getSubject().getGid(), tenantGid)) {
                return true;
            }
        }
        return null;
    }

}

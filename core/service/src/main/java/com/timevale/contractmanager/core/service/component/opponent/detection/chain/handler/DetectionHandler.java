package com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler;


import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;

import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-17 10:52
 */
public interface DetectionHandler {

	List<OpponentDetectionChainResultBO> handler(DetectionChainBO chainBO,List<OpponentDetectionChainResultBO> chainResults);

	boolean filter(List<Integer> items);
}

package com.timevale.contractmanager.core.service.integrated.convert;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.timevale.billing.facade.entity.CostContext;
import com.timevale.billing.facade.entity.SignContext;
import com.timevale.billing.facade.request.SceneInfoReq;
import com.timevale.contractmanager.common.contract.notice.context.BillContext;
import com.timevale.contractmanager.common.contract.notice.context.MsgContext;
import com.timevale.contractmanager.common.contract.notice.context.bill.BillCostContext;
import com.timevale.contractmanager.common.contract.notice.context.bill.CooperationContext;
import com.timevale.contractmanager.common.contract.notice.context.bill.SceneInfo;
import com.timevale.contractmanager.common.contract.notice.context.bill.SignBillContext;
import com.timevale.contractmanager.common.contract.notice.dto.ContractNoticeMsgDTO;
import com.timevale.contractmanager.common.service.constant.SystemConstant;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.notificationmanager.service.enums.NotifyChannelTypeEnum;
import com.timevale.notificationmanager.service.model.IDUser;
import com.timevale.notificationmanager.service.model.NotifyContactWay;
import com.timevale.notificationmanager.service.model.optimize.request.SendBillingContext;
import com.timevale.notificationmanager.service.model.optimize.request.SendToBillingRequest;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.ClassUtils;

import java.util.List;
import java.util.stream.Collectors;

/** Created by fengmo */
@Slf4j
public abstract class NotificationManagerConvert {
    private static final String MSG_PARAM_PROCESS_KEY = "processId";
    private static final String INITIATOR_SUBJECT_ID_KEY = "initiatorSubjectId";


    private static final String NOTICE_SCENE = "NOTICE";

    public static SendToBillingRequest convert2SendToBillingRequest(
            String tempName,
            List<NotifyChannelTypeEnum> notifyChannels,
            ContractNoticeMsgDTO noticeMsgDTO) {

        MsgContext msgContext = noticeMsgDTO.getMsgContext();

        SendToBillingRequest billingRequest = new SendToBillingRequest();
        IDUser idUser = new IDUser();
        idUser.setUserOid(msgContext.getNoticeUserOid());
        billingRequest.setId(idUser);
        billingRequest.setAppId(noticeMsgDTO.getAppId());
        billingRequest.setCode(SystemConstant.SERVICEID);
        List<NotifyContactWay> notifyContactWays =
                convert2NotifyContactWays(notifyChannels, msgContext.getNoticeUserOid());
        billingRequest.setAppointContactWays(notifyContactWays);
        billingRequest.setTaskId(noticeMsgDTO.getRqSeq());
        billingRequest.setTemplateName(StringUtils.isBlank(tempName)
                ? noticeMsgDTO.getMsgContext().getTemplateName()
                : tempName);
        if (msgContext.getTemplateParams() == null) {
            msgContext.setTemplateParams(Maps.newHashMap());
        }
        msgContext.getTemplateParams().put(MSG_PARAM_PROCESS_KEY, noticeMsgDTO.getProcessId());
        billingRequest.setTemplateParam(msgContext.getTemplateParams());
        billingRequest.setPrincipalId(msgContext.getTemplateParams().get(INITIATOR_SUBJECT_ID_KEY));
        billingRequest.setExcludeDefaultInmail(msgContext.isExcludeDefaultInmail());

        BillContext billContext = noticeMsgDTO.getBillContext();

        if (billContext == null) {
            return billingRequest;
        }

        // 构建通知计费上下文对象
        SendBillingContext sendBillingContext = new SendBillingContext();
        billingRequest.setBillingContext(sendBillingContext);

        sendBillingContext.setSaleType(billContext.getSaleType());
        sendBillingContext.setSaleSchemaId(
                billContext.getSaleSchemaId() != null
                        ? billContext.getSaleSchemaId().toString()
                        : null);
        sendBillingContext.setScene(NOTICE_SCENE);
        sendBillingContext.setBillGid(billContext.getBillGid());
        sendBillingContext.setSceneInfos(convert2SceneInfoReq(billContext.getSceneInfos()));
        sendBillingContext.setIsolationCode(billContext.getIsolationCode());

        CostContext costContext =
                convert2CostContext(noticeMsgDTO.getBillContext().getCostContext());
        sendBillingContext.setCostContext(costContext);

        return billingRequest;
    }
    
    private static List<SceneInfoReq> convert2SceneInfoReq(List<SceneInfo> SceneInfos) {
        if (CollectionUtils.isEmpty(SceneInfos)) {
            return null;
        }

        return SceneInfos.stream().map(item -> {
            SceneInfoReq sceneInfoReq = new SceneInfoReq();
            sceneInfoReq.setSceneType(item.getSceneType());
            sceneInfoReq.setSceneValue(item.getSceneValue());
            return sceneInfoReq;
        }).collect(Collectors.toList());
    }

    private static CostContext convert2CostContext(String billCostContextStr) {
        if (StringUtils.isBlank(billCostContextStr)) {
            return null;
        }
        BillCostContext billCostContext =
                JSON.parseObject(billCostContextStr, BillCostContext.class);
        if (billCostContext == null) {
            return null;
        }
        Class costClass = null;
        try {
            costClass = ClassUtils.getClass(billCostContext.getContextClassName());

        } catch (Exception e) {
            log.info("计费类型转换异常", e);
        }
        if (costClass == null) {
            return null;
        }
        if (costClass == SignBillContext.class) {
            SignBillContext signBillContext =
                    (SignBillContext) JSON.parseObject(billCostContextStr, costClass);
            return SignContext.builder()
                    .signId(signBillContext.getSignId())
                    .signTaskName(signBillContext.getSignTaskName())
                    .build();

        } else if (costClass == CooperationContext.class) {
            CooperationContext cooperationContext =
                    (CooperationContext) JSON.parseObject(billCostContextStr, costClass);
            return SignContext.builder()
                    .signId(cooperationContext.getCooperationId())
                    .signTaskName(cooperationContext.getCooperationTaskName())
                    .build();
        }
        return null;
    }

    private static List<NotifyContactWay> convert2NotifyContactWays(
            List<NotifyChannelTypeEnum> notifyChannels, String userOid) {
        return notifyChannels.stream()
                .map(
                        notify -> {
                            NotifyContactWay notifyContactWay = new NotifyContactWay();
                            notifyContactWay.setOid(userOid);
                            notifyContactWay.setType(notify);
                            return notifyContactWay;
                        })
                .collect(Collectors.toList());
    }
}

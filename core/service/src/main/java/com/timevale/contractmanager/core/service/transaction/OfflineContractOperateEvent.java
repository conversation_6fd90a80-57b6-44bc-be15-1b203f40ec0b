package com.timevale.contractmanager.core.service.transaction;

import lombok.Getter;

import org.springframework.context.ApplicationEvent;

import java.util.Collection;

@Getter
public class OfflineContractOperateEvent extends ApplicationEvent {

    /** 导入记录id */
    private String recordId;
    /** 导入记录流程id列表 */
    private Collection<String> recordProcessIds;
    /** 操作类型 */
    private String operateType;

    /** 上传文件后关联的合同 */
    private String masterProcessId;

    private OfflineContractOperateEvent(
            Collection<String> recordProcessIds, OfflineContractOperateType operateType) {
        super(operateType.getDesc());
        this.recordProcessIds = recordProcessIds;
        this.operateType = operateType.getType();
    }

    private OfflineContractOperateEvent(String recordId, OfflineContractOperateType operateType) {
        super(operateType.getDesc());
        this.recordId = recordId;
        this.operateType = operateType.getType();
    }

    private OfflineContractOperateEvent(Collection<String> recordProcessIds,OfflineContractOperateType operateType,String masterProcessId) {
        super(operateType.getDesc());
        this.recordProcessIds = recordProcessIds;
        this.operateType = operateType.getType();
        this.masterProcessId=masterProcessId;
    }

    public static OfflineContractOperateEvent recordProcessEvent(
            Collection<String> recordProcessIds, OfflineContractOperateType operateType) {
        return new OfflineContractOperateEvent(recordProcessIds, operateType);
    }
    public static OfflineContractOperateEvent recordProcessEvent(
            Collection<String> recordProcessIds, OfflineContractOperateType operateType,String masterProcessId) {
       return new OfflineContractOperateEvent(recordProcessIds, operateType,masterProcessId);
    }

    public static OfflineContractOperateEvent recordEvent(
            String recordId, OfflineContractOperateType operateType) {
        return new OfflineContractOperateEvent(recordId, operateType);
    }
}

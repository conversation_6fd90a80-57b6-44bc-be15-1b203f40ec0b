package com.timevale.contractmanager.core.service.sharesign;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignTaskDO;
import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignUrlDO;
import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.contractmanager.common.service.enums.*;
import com.timevale.contractmanager.common.service.enums.sharesign.ShareSignFromEnum;
import com.timevale.contractmanager.common.service.enums.sharesign.ShareSignTypeEnum;
import com.timevale.contractmanager.common.service.enums.sharesign.ShareSignVersionEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.service.integration.util.SaasVipFunctionHelper;
import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.common.utils.UUIDUtil;
import com.timevale.contractmanager.core.model.bo.*;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.ImportFlowTemplateRequest;
import com.timevale.contractmanager.core.model.dto.request.process.SaveShareSignInnerRequest;
import com.timevale.contractmanager.core.model.dto.request.sharesign.ShareSignTaskStartRequest;
import com.timevale.contractmanager.core.model.dto.response.ProcessFlowTemplateDetailResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartDetailResponse;
import com.timevale.contractmanager.core.model.dto.response.ProcessStartResponse;
import com.timevale.contractmanager.core.model.dto.response.sharesign.ShareSignQrCodeBean;
import com.timevale.contractmanager.core.model.dto.response.sharesign.ShareSignTaskUrl;
import com.timevale.contractmanager.core.model.dto.response.sharesign.ShareSignTaskUrlResponse;
import com.timevale.contractmanager.core.model.dto.user.AppInfo;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.contractmanager.core.model.validationgroup.ProcessStartValidation;
import com.timevale.contractmanager.core.service.contractapproval.ContractApprovalService;
import com.timevale.contractmanager.core.service.contractapproval.param.CreateContractApprovalGroupDTO;
import com.timevale.contractmanager.core.service.contractapproval.param.UseApprovalFlowTemplateDTO;
import com.timevale.contractmanager.core.service.enums.FlowTemplateCopyTypeEnum;
import com.timevale.contractmanager.core.service.grouping.AuthService;
import com.timevale.contractmanager.core.service.mq.model.DeleteTempFlowTemplateMsqInput;
import com.timevale.contractmanager.core.service.mq.producer.DeleteTempFlowTemplateProducer;
import com.timevale.contractmanager.core.service.mq.producer.ShareSignStartProducer;
import com.timevale.contractmanager.core.service.other.ItsmService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.FlowTemplatePlusService;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.core.service.process.ProcessChangeNotifyService;
import com.timevale.contractmanager.core.service.process.ProcessDraftService;
import com.timevale.contractmanager.core.service.process.door.ProcessDoor;
import com.timevale.contractmanager.core.service.process.impl.FlowTemplateCopyHelper;
import com.timevale.contractmanager.core.service.process.impl.ProcessStartHelper;
import com.timevale.contractmanager.core.service.process.impl.factory.FlowTemplateSaveServiceFactory;
import com.timevale.contractmanager.core.service.processstart.impl.context.ProcessStartContext;
import com.timevale.contractmanager.core.service.sharesign.bean.ShareSignTaskBizExtra;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.doccooperation.service.enums.CooperationerRoleSetEnum;
import com.timevale.doccooperation.service.enums.FlowTemplateStatusEnum;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.doccooperation.service.exception.DocCooperationErrorEnum;
import com.timevale.doccooperation.service.result.CopyFlowTemplateResult;
import com.timevale.doccooperation.service.util.LambdaUtil;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.privilege.service.enums.PrivilegeOperationRegister;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.manage.common.service.model.output.bean.VipFunction;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.enums.AccountTypeEnum;
import com.timevale.signflow.search.docSearchService.param.IndexTempGroupInfoParam;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;
import static com.timevale.docmanager.service.enums.StructComponentTypeEnum.REMARK;
import static com.timevale.saas.common.manage.common.service.enums.contractNo.ContractNoGenerateTypeEnum.SYSTEM;
import static java.util.function.Function.identity;

/**
 * @author: qianyi
 * @since: 2021-02-05 11:29
 */
@Service
@Slf4j
public class ShareSignStartServiceImpl implements ShareSignStartService {

    @Autowired private ItsmService itsmService;
    @Autowired private AuthService authService;
    @Autowired private MapperFactory mapperFactory;
    @Autowired private SaasCommonClient saasCommonClient;
    @Autowired private UserCenterService userCenterService;
    @Autowired private ProcessStartHelper processStartHelper;
    @Autowired private FlowTemplateCopyHelper flowTemplateCopyHelper;
    @Autowired private ShareSignBaseService shareSignBaseService;
    @Autowired private FlowTemplateService flowTemplateService;
    @Autowired private FlowTemplateSaveServiceFactory flowTemplateSaveServiceFactory;
    @Autowired private ProcessDraftService processDraftService;
    @Autowired private SensorService sensorService;
    @Autowired private DeleteTempFlowTemplateProducer deleteTempFlowTemplateProducer;

    @Autowired private ShareSignStartProducer shareSignStartProducer;
    @Autowired private ProcessChangeNotifyService processChangeNotifyService;

    @Autowired private FlowTemplatePlusService flowTemplatePlusService;
    @Autowired private ContractApprovalService contractApprovalService;
;
    @Autowired
    private ProcessDoor processDoor;

    @Override
    public ShareSignTaskUrlResponse start(ProcessStartContext context, ShareSignTaskStartRequest startBO) {
        // 通用参数校验
        ValidationUtil.validateBean(startBO, ProcessStartValidation.class);

        UserAccountDetail tenantUserAccount = context.getTenant();
        UserAccountDetail initiatorUserAccount = context.getAccount();
        // 设置参与方信息
        startByFlowTemplateId(startBO,tenantUserAccount,initiatorUserAccount);

        if (StringUtils.isBlank(initiatorUserAccount.getAccountGid())) {
            throw new BizContractManagerException(PROCESS_SUBJECT_MISSING_INFO, "用户", "发起");
        }
        String flowTemplateId = startBO.getFlowTemplateId();
        // 模板使用权限校验
        if (startBO.isTemplateStartProcess()) {
            String action = PrivilegeOperationRegister.USE;
            authService.auth(action, tenantUserAccount, flowTemplateId, startBO.getPlatform());
        }

        int shareParticipantCount = startBO.shareParticipantCount();
        if (shareParticipantCount == 0) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "未指定扫码参与方");
        } else if (shareParticipantCount > 1) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "仅支持指定单个扫码参与方");
        } else if (null == startBO.getShareTaskTotal()) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "未指定扫码参与上限");
        }

        // 校验vip相关功能限制
        checkShareSignVipLimit(tenantUserAccount, context.getClientId(), startBO);

        // 发起前置校验
        preCheckStartBO(startBO, tenantUserAccount, initiatorUserAccount, context);

        ShareSignTaskUrlResponse response = doStartInner(context, startBO);
        log.info("start task success, shareSignTaskId:{}", response.getShareSignTaskId());

        // 扫码任务发起成功埋点
        sensorService.shareSignTaskStartTracking(startBO, tenantUserAccount);

        afterStart(startBO.isTemplateStartProcess(), flowTemplateId, response.getShareSignTaskId(), tenantUserAccount.getAccountOid(), initiatorUserAccount);
        return response;
    }

    /**
     * 校验扫码签功能限制
     *
     * @param tenantAccount
     * @param clientId
     * @param startBO
     */
    private void checkShareSignVipLimit(
            UserAccountDetail tenantAccount, String clientId, ShareSignTaskStartRequest startBO) {
        ArrayList<String> functionCodes =
                Lists.newArrayList(
                        FunctionCodeConstant.SIGN_REMARK,
                        FunctionCodeConstant.SAAS_START_WILL_TYPE_SPECIFY,
                        FunctionCodeConstants.SHARE_SCAN_SIGN);
        Map<String, VipFunction> vipFunctionsMap =
                saasCommonClient.getVipFunctionsMap(
                        tenantAccount.getAccountOid(), clientId, functionCodes);

        // 版本是否支持扫码签、扫码签参与人数上限校验
        if (Objects.nonNull(startBO.getShareTaskTotal())) {
            SaasVipFunctionHelper.checkFunctionLimit(
                    vipFunctionsMap.get(FunctionCodeConstants.SHARE_SCAN_SIGN),
                    Long.valueOf(startBO.getShareTaskTotal()));
        }

        // 版本是否支持批注(仅直接发起指定位置判断，模板发起模板里已有批注控件的不拦截)
        if (startBO.isAssignPosStartProcess()
                && flowTemplatePlusService.hasStructs(
                        startBO.getFlowTemplateId(), Arrays.asList(REMARK.getType()))) {
            SaasVipFunctionHelper.checkSupportFunction(
                    vipFunctionsMap.get(FunctionCodeConstant.SIGN_REMARK));
        }

        // 版本是否支持指定认证方式  海外签注释
//        if (processStartChecker.isAssignedWillType(tenantAccount, startBO.getParticipants())) {
//            SaasVipFunctionHelper.checkSupportFunction(
//                    vipFunctionsMap.get(FunctionCodeConstant.SAAS_START_WILL_TYPE_SPECIFY));
//        }
    }

    /** 使用模板发起扫码签时设置参与方信息： 只有参与方是发起人自己时需要设置 */
    private void startByFlowTemplateId(
            ShareSignTaskStartRequest startBO,
            UserAccountDetail tenantUserAccount,
            UserAccountDetail initiatorUserAccount) {
        if(!Boolean.TRUE.equals(startBO.getShareSignStartByFlowTemplateId())){
            return;
        }
        startBO.getParticipants()
                .forEach(
                        e -> {
                            if (CooperationerRoleSetEnum.INITIATOR_DESIGNATE.getType()
                                    == e.getRoleSet()) {
                                // 设置是否扫码签参与方
                                e.setSharable(true);
                            } else if (CooperationerRoleSetEnum.INITIATOR_SELF.getType()
                                    == e.getRoleSet()) {
                                // 设置发起人本人信息
                                ParticipantInstanceBO temp = new ParticipantInstanceBO();
                                temp.setSubjectType(
                                        Boolean.TRUE.equals(tenantUserAccount.isPerson())
                                                ? SubjectTypeEnum.PERSON.getType()
                                                : SubjectTypeEnum.ORG.getType());
                                temp.setSubjectName(tenantUserAccount.getAccountName());
                                temp.setSubjectId(tenantUserAccount.getAccountOid());
                                temp.setSubjectRealName(tenantUserAccount.isRealName());
                                temp.setSubjectName(tenantUserAccount.getAccountName());
                                temp.setAccountOid(initiatorUserAccount.getAccountOid());
                                temp.setAccountRealName(initiatorUserAccount.isRealName());
                                temp.setAccountName(initiatorUserAccount.getAccountName());
                                temp.setAccount(
                                        StringUtils.isNotBlank(
                                                        initiatorUserAccount.getAccountMobile())
                                                ? initiatorUserAccount.getAccountMobile()
                                                : initiatorUserAccount.getAccountEmail());
                                e.setInstances(Collections.singletonList(temp));
                            }
                        });
    }

    /**
     * 发起扫码签前置参数校验
     * @param startBO
     * @param tenantUserAccount
     * @param initiatorUserAccount
     */
    private void preCheckStartBO(
            ShareSignTaskStartRequest startBO,
            UserAccountDetail tenantUserAccount,
            UserAccountDetail initiatorUserAccount,
            ProcessStartContext context)
    {
        // 专属云 转移到外部进行校验
//        if (startBO.getFiles() != null) {
//            /** 发起时contract_doc和attach_doc都是FileBO类型 */
//            processStartHelper.checkFile(startBO.getFiles(), true, tenantUserAccount);
//        }

        // 校验合同审批模板
        if (tenantUserAccount.isOrganize() && initiatorUserAccount.isPerson()) {
            processStartHelper.preCheckShareSignStartParam(
                    startBO, initiatorUserAccount, tenantUserAccount, startBO.getClientId());
        }
        // 校验签署设置 专属云-转移到外面
//        signSetProcessService.checkAndChangeParam(context, startBO, startBO.getParticipants());
    }

    private void afterStart(boolean templateStart, String flowTemplateId, String shareSignTaskId, String tenantId, UserAccount initiator){
        // 发起成功后, 发送扫码任务发起消息
        shareSignStartProducer.sendMessage(tenantId, initiator.getAccountGid(), flowTemplateId, shareSignTaskId);

        // 使用模板发起扫码任务
        if(templateStart){
            return;
        }

        // 本地文件发起扫码任务， 如果没有流程模板id, 表示是直接发起， 非草稿发起或指定位置发起， 无需删除模板
        if(StringUtils.isBlank(flowTemplateId)){
            return;
        }
        // 草稿发起或指定位置发起场景会指定流程模板id, 这两种场景会直接使用指定的流程模板id作为扫码任务固化的流程模板
        // 因此无需删除模板， 只需删除列表显示的草稿。 由于此时无法确定流程模板是否草稿模板， 均尝试删除草稿
        try {
            processDraftService.deleteDraft(flowTemplateId);
        } catch (Exception e) {
            // 可能不是草稿，是临时模板
            log.warn("delete draft fail, may be drat not exist");
        }
    }

    private ShareSignTaskUrlResponse doStartInner(
            ProcessStartContext context,
            ShareSignTaskStartRequest request) {

        UserAccountDetail tenantUserAccount = context.getTenant();
        UserAccountDetail initiatorUserAccount = context.getAccount();

        AppInfo appInfo = itsmService.checkApp(request.getAppId());
        if (appInfo == null) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.PROCESS_QUERY_APP_EMPTY);
        }

        // 是否使用原模板，用于后续发起失败场景下判断是否删除模板
        boolean useOriginFlowTemplate = false;
        // 生成临时模板
        ProcessStartBO copyTemplateData;
        // 扫码签如果不是指定位置不需要创建临时模板
        if (ProcessStartScene.DIRECT_START.getScene() == request.getScene() && request.getShareSignDirectStartByFlowTemplateId() != Boolean.TRUE) {
            // 设置发起类型为 本地文件扫码发起  直接扫码发起
            request.setStartType(ProcessStartType.SHARE_SCAN_DIRECT_START.getType());
            // 如果是指定位置发起或草稿发起，则固化发起时指定的流程模板id作为扫码任务的模板快照, 并设置useOriginFlowTemplate为true
            useOriginFlowTemplate = StringUtils.isNotBlank(request.getFlowTemplateId());
            // 保存/创建模板
            ProcessStartResponse createTemplateResp =
                    flowTemplateSaveServiceFactory.save(context,
                            ProcessStartScene.CREATE_SET_TEMPLATE, request);
            copyTemplateData =
                    flowTemplateService.startDetail(createTemplateResp.getProcessId(), tenantUserAccount);
        } else {
            // 设置发起类型为 使用模板扫码发起
            request.setStartType(ProcessStartType.SHARE_SCAN_TEMPLATE_START.getType());
            // 复制模板
            copyTemplateData = copyProcessStartTemplate(context, request);
        }

        // 补充模板数据中缺失的发起参数
        ShareSignTaskStartRequest startRequest = mapperFactory
                .getMapperFacade(ProcessStartBO.class, ShareSignTaskStartRequest.class)
                .map(copyTemplateData);
        startRequest.setInitiatorAccountId(request.getInitiatorAccountId());
        startRequest.setScene(request.getScene());
        startRequest.setShareTaskTotal(request.getShareTaskTotal());

        try {
            // 保存扫码签任务信息
            ShareSignTaskDO shareSignTask = new ShareSignTaskDO();
            // 直接发起、使用模板指定
            shareSignTask.setEndTime(processStartHelper
                    .calculateValidityDate(startRequest.getSignValidityConfig(), startRequest.getSignEndTime()));
            shareSignTask.setTaskId(UUIDUtil.genUUID());
            shareSignTask.setShareTemplateId(startRequest.getFlowTemplateId());
            shareSignTask.setOriginTemplateId(startRequest.getOriginFlowTemplateId());
            shareSignTask.setTaskName(startRequest.getTaskName());
            shareSignTask.setTaskFrom(
                    startRequest.isTemplateStartProcess()
                            ? ShareSignFromEnum.TEMPLATE_SIGN.getType()
                                : ShareSignFromEnum.DIRECT_SIGN.getType());
            shareSignTask.setAccountGid(initiatorUserAccount.getAccountGid());
            shareSignTask.setAccountOid(initiatorUserAccount.getAccountOid());
            shareSignTask.setAccountName(initiatorUserAccount.getAccountName());
            shareSignTask.setSubjectGid(tenantUserAccount.getAccountGid());
            shareSignTask.setSubjectOid(tenantUserAccount.getAccountOid());
            shareSignTask.setSubjectName(tenantUserAccount.getAccountName());
            shareSignTask.setShareTotal(startRequest.getShareTaskTotal());

            ShareSignTypeEnum shareType = ShareSignTypeEnum.SINGLE_PARTICIPANT_SHARE;
            GroupTypeEnum batchType = GroupTypeEnum.SHARE_START_SINGLE_SCAN;

            shareSignTask.setShareType(shareType.getType());
            shareSignTask.setVersion(ShareSignVersionEnum.V2.getVersion());
            shareSignTask.setPrivateShare(
                    Optional.ofNullable(startRequest.getPrivateShare()).orElse(false));

            //业务扩展数据
            ShareSignTaskBizExtra bizExtra = new ShareSignTaskBizExtra();
            bizExtra.setRepeatSign(BooleanUtils.isTrue(request.getRepeatSign()));
            bizExtra.setApprovalTemplateId(request.getApproveTemplateId());
            shareSignTask.setBizExtra(JsonUtils.obj2json(bizExtra));

            ShareSignTaskUrlResponse response = new ShareSignTaskUrlResponse();
            response.setShareSignTaskId(shareSignTask.getTaskId());
            // 生成并保存扫码签二维码地址
            List<ParticipantBO> participants = startRequest.getParticipants();
            List<ShareSignUrlDO> shareSignUrlDOS = Lists.newArrayList();
            for (ParticipantBO participant : participants) {
                if (!participant.isSharable()) {
                    continue;
                }
                // 生成二维码
                ShareSignQrCodeBean qrCodeBean =
                        shareSignBaseService.genShareSignQrCode(
                                shareSignTask.getTaskId(), participant.getParticipantId());
                // 保存二维码记录
                ShareSignUrlDO shareSignUrl = new ShareSignUrlDO();
                shareSignUrl.setUuid(qrCodeBean.getShareScanId());
                shareSignUrl.setShareUrl(qrCodeBean.getShareUrl());
                shareSignUrl.setParticipantId(participant.getParticipantId());
                shareSignUrl.setParticipantType(participant.getParticipantSubjectType());
                shareSignUrl.setParticipantLabel(participant.getParticipantLabel());
                shareSignUrl.setShareSignTaskId(shareSignTask.getTaskId());
                shareSignUrl.setShareQrcodeFilekey(qrCodeBean.getQrCodeFileKey());
                shareSignUrlDOS.add(shareSignUrl);

                // 组装返回数据
                ShareSignTaskUrl taskUrl = new ShareSignTaskUrl();
                taskUrl.setParticipantId(participant.getParticipantId());
                taskUrl.setParticipantType(participant.getParticipantSubjectType());
                taskUrl.setParticipantLabel(participant.getParticipantLabel());
                taskUrl.setShareUrl(qrCodeBean.getShareUrl());
                taskUrl.setQrcodeUrl(qrCodeBean.getQrCodeUrl());
                response.getShareUrls().add(taskUrl);
            }

            // 保存扫码任务及二维码地址
            SaveShareSignInnerRequest saveShareSignInnerRequest = new SaveShareSignInnerRequest();
            saveShareSignInnerRequest.setAppId(appInfo.getAppId());
            saveShareSignInnerRequest.setTaskDO(shareSignTask);
            saveShareSignInnerRequest.setUrlDOS(shareSignUrlDOS);
            saveShareSignInnerRequest.setGroupType(batchType);
            saveShareSignInnerRequest.setSignMode(request.getSignMode());
            saveShareSignInnerRequest.setDedicatedCloudId(request.getDedicatedCloudId());
            shareSignBaseService.saveShareSignTaskAndUrl(saveShareSignInnerRequest);

            //判断是否要发起合同审批组
            checkAndCreateApprovalGroup(request.getApproveTemplateId(),shareSignTask);
            // 添加临时扫码任务es数据
            IndexTempGroupInfoParam indexTempGroupInfoParam = new IndexTempGroupInfoParam();

            indexTempGroupInfoParam.setAppId(appInfo.getAppId());
            indexTempGroupInfoParam.setAppName(appInfo.getName());
            indexTempGroupInfoParam.setGroupCreateTime(System.currentTimeMillis());
            indexTempGroupInfoParam.setGroupUpdateTime(System.currentTimeMillis());
            indexTempGroupInfoParam.setSource(ClientEnum.getClientNo(RequestContextExtUtils.getClientId()));

            ProcessAccount processAccount = new ProcessAccount();
            processAccount.setType(AccountTypeEnum.P_INITIATOR.getType());
            processAccount.setHidden(false);

            Account account = new Account();
            account.setGid(initiatorUserAccount.getAccountGid());
            account.setOid(initiatorUserAccount.getAccountOid());
            account.setRid(initiatorUserAccount.getAccountRid());
            account.setOrgan(initiatorUserAccount.isOrganize());
            account.setMobile(initiatorUserAccount.getAccountMobile());
            account.setEmail(initiatorUserAccount.getAccountEmail());
            account.setName(initiatorUserAccount.getAccountName());
            processAccount.setPerson(account);

            Account subject = new Account();
            subject.setGid(tenantUserAccount.getAccountGid());
            subject.setOid(tenantUserAccount.getAccountOid());
            subject.setRid(tenantUserAccount.getAccountRid());
            subject.setOrgan(tenantUserAccount.isOrganize());
            subject.setMobile(tenantUserAccount.getAccountMobile());
            subject.setEmail(tenantUserAccount.getAccountEmail());
            subject.setName(tenantUserAccount.getAccountName());

            processAccount.setSubject(subject);
            indexTempGroupInfoParam.setProcessAccountInfoList(Lists.newArrayList(processAccount));
            indexTempGroupInfoParam.setGroupType(batchType.getType());
            indexTempGroupInfoParam.setGroupId(shareSignTask.getShareBizId());
            indexTempGroupInfoParam.setGroupName(startRequest.getTaskName());
            indexTempGroupInfoParam.setGroupStatus(GroupStatusEnum.ENABLE.getStatus());
            processChangeNotifyService.indexTempGroupInfo(tenantUserAccount.getAccountGid(), indexTempGroupInfoParam);

            return response;
        } catch (Exception e) {
            // 如果未使用原模板， 则发起失败场景下，要将新创建或者新复制的模板删除
            deleteTempTemplateId(useOriginFlowTemplate ? null : startRequest.getFlowTemplateId());
            throw e;
        }
    }

    private void checkAndCreateApprovalGroup(String approvalTemplateId, ShareSignTaskDO taskDO) {
        // 如果需要发起合同审批，且使用新版合同审批， 则创建新版合同审批组
        if (StringUtils.isBlank(approvalTemplateId)) {
            return;
        }
        UseApprovalFlowTemplateDTO useParam = new UseApprovalFlowTemplateDTO();
        useParam.setApprovalTemplateId(approvalTemplateId);
        useParam.setSubjectGid(taskDO.getSubjectGid());
        CreateContractApprovalGroupDTO groupParam = new CreateContractApprovalGroupDTO();
        groupParam.setAccountId(taskDO.getAccountOid());
        groupParam.setAccountGid(taskDO.getAccountGid());
        groupParam.setAccountName(taskDO.getAccountName());
        groupParam.setSubjectId(taskDO.getSubjectOid());
        groupParam.setSubjectGid(taskDO.getSubjectGid());
        groupParam.setBizGroupId(taskDO.getShareBizId());
        groupParam.setBizGroupName(taskDO.getTaskName());
        groupParam.setTotalCount(taskDO.getShareTotal());
        contractApprovalService.createApprovalGroup(groupParam);
    }
    /**
     * 生成扫码签临时模板
     * @param startRequest
     * @return
     */
    private ProcessStartBO copyProcessStartTemplate(ProcessStartContext context,
                                                    ProcessStartBO startRequest) {

        UserAccountDetail tenantUserAccount = context.getTenant();
        UserAccountDetail initiatorUserAccount = context.getAccount();

        ImportFlowTemplateRequest importRequest = new ImportFlowTemplateRequest();
        importRequest.setFlowTemplateId(startRequest.getFlowTemplateId());
        importRequest.setNewFlowTemplateName(startRequest.getTaskName());
        // 1.复制模版
        CopyFlowTemplateResult importResult =
                flowTemplateCopyHelper.doImport(
                        importRequest,
                        tenantUserAccount,
                        tenantUserAccount,
                        initiatorUserAccount,
                        FlowTemplateCopyTypeEnum.SHARE_SCAN);

        ProcessStartBO detailResponse;
        try {
            // 2.获取复制的模板详情
            ProcessStartDetailResponse startDetail =
                    flowTemplateService.startDetail(importResult.getFlowTemplateId(), tenantUserAccount);
            // 校验模板状态
            if (startRequest.getScene() == ProcessStartScene.TEMPLATE_START.getScene()
                    && !FlowTemplateStatusEnum.ENABLE.getStatus().equals(startDetail.getStatus())) {
                throw new BizContractManagerException(
                        COOPERATION_FAILED,
                        DocCooperationErrorEnum.FLOW_TEMPLATE_STATUS_ERROR.getMessage());
            }
            detailResponse = startDetail;
            detailResponse.setInitiatorAccountId(initiatorUserAccount.getAccountOid());
            detailResponse.setScene(startRequest.getScene());
            detailResponse.setSkipFill(startRequest.getSkipFill());
            detailResponse.setCcs(startRequest.getCcs());
            detailResponse.setFileEndTime(startRequest.getFileEndTime());
            detailResponse.setSignEndTime(startRequest.getSignEndTime());
            detailResponse.setSignedNoticeUrl(startRequest.getSignedNoticeUrl());
            detailResponse.setRedirectUrl(startRequest.getRedirectUrl());
            detailResponse.setApproveTemplateId(startRequest.getApproveTemplateId());
            detailResponse.setSecretType(startRequest.getSecretType());
            detailResponse.setVisibleAccounts(startRequest.getVisibleAccounts());
            detailResponse.setStartType(startRequest.getStartType());
            detailResponse.setPrivateShare(Optional.ofNullable(startRequest.getPrivateShare()).orElse(false));
            detailResponse.setInitiatorDeptId(startRequest.getInitiatorDeptId());
            detailResponse.setUseWatermarkTemplateId(startRequest.getUseWatermarkTemplateId());
            detailResponse.setUseWatermarkTemplateSnapshotId(startRequest.getUseWatermarkTemplateSnapshotId());
            detailResponse.setRemarks(CollectionUtils.isEmpty(startDetail.getRemarks()) ? startRequest.getRemarks() : startDetail.getRemarks());
            detailResponse.setProcessRemarks(CollectionUtils.isEmpty(startDetail.getProcessRemarks()) ? startRequest.getProcessRemarks() : startDetail.getProcessRemarks());
            // 处理参与方数据， 将入参中的参与方数据填充到模板中
            List<ParticipantBO> participantList = detailResponse.getParticipants();
            // 获取模板中的参与方map
            Map<String, ParticipantBO> participantMap =
                    participantList.stream()
                            .collect(Collectors.toMap(i -> i.getParticipantId(), j -> j));

            // 循环入参中的参与方
            List<ParticipantBO> participants = startRequest.getParticipants();
            for (ParticipantBO participant : participants) {
                // 获取入参中的参与方id在新复制模板中对应的新参与方id
                String participantId = importResult
                        .getCooperationerIdMap()
                        .get(participant.getParticipantId());
                // 如果不存在新参与方id, 跳过， 因为模板的参与方不允许添加
                if (StringUtils.isBlank(participantId)) {
                    continue;
                }
                // 从新模板中获取到参与方对象
                ParticipantBO participantBO = participantMap.get(participantId);
                // 如果模板设置的参与方类型为不限定，重置参与方类型及签署要求，优先获取前端指定的参与方类型及签署要求
                if (ParticipantSubjectType.UNDETERMINED.getType() == participantBO.getParticipantSubjectType()) {
                    participantBO.setParticipantSubjectType(participant.getParticipantSubjectType());
                    participantBO.setSignRequirements(participant.getSignRequirements());
                }
                // 设置扫码签数据
                participantBO.setSharable(participant.isSharable());
                // 如果当前参与方是扫码方，且未指定扫码参与上限， 取请求参数中指定的扫码参与上限
                if (participantBO.isSharable()) {
                    // 校验是否可扫码加入
                    participantBO.checkSharableValid();
                    // 如果参与方中指定了扫码参与上限， 优先取参与方的， 其次取请求参数指定的shareTaskTotal, 否则使用模板默认的。
                    if (null != participant.getShareMax()) {
                        participantBO.setShareMax(participant.getShareMax());
                    } else if (null != startRequest.getShareTaskTotal()) {
                        participantBO.setShareMax(startRequest.getShareTaskTotal());
                    }
                    if (null == participantBO.getShareMax()) {
                        throw new BizContractManagerException(BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM, "未指定扫码参与上限");
                    }
                } else {
                    participantBO.setShareMax(null);
                }
                // 设置入参的参与方实例
                participantBO.setInstances(participant.getInstances());
                // 设置参与模式
                participantBO.setParticipantMode(participant.getParticipantMode());
                participantBO.setAccessToken(participant.getAccessToken());
                participantBO.setParticipantStartType(participant.getParticipantStartType());
                participantBO.setParticipantStartValue(participant.getParticipantStartValue());
                participantBO.setSignOrder(participant.getSignOrder());
            }

            // 处理签署文件和附件数据， 将入参中追加的附件填充到模板中
            List<FileBO> fileList = detailResponse.getFiles();
            updateShareSignContractFileConfig(
                    tenantUserAccount.getAccountOid(), startRequest, fileList);
            // 获取模板中的附件map
            Map<String, FileBO> attachmentMap =
                    fileList.stream().filter(i -> !ProcessFileType.isContract(i.getFileType()))
                            .collect(Collectors.toMap(i -> i.getFileId(), j -> j));
            // 循环入参中的文件列表
            List<FileBO> files = startRequest.getFiles();
            for (FileBO documentsAndAttachment : files) {
                // 如果是签署文件， 跳过， 因为模板的签署文件不允许添加
                if (ProcessFileType.isContract(documentsAndAttachment.getFileType())) {
                    continue;
                }
                // 获取入参中的文件ID在新复制模板中对应的新文件id
                String fileId =
                        importResult.getAttachmentIdMap().get(documentsAndAttachment.getFileId());
                // 如果新文件id在模板附件中已存在， 跳过
                if (StringUtils.isNotBlank(fileId) && attachmentMap.containsKey(fileId)) {
                    continue;
                }
                // 追加新的附件信息
                detailResponse.getFiles().add(documentsAndAttachment);
            }

            // 3.更新模版
            processDoor.innerFlowTemplate(context, ProcessStartScene.CREATE_SET_TEMPLATE, detailResponse);
        } catch (Exception e) {
            deleteTempTemplateId(importResult.getFlowTemplateId());
            throw e;
        }

        return detailResponse;
    }

    @Override
    public void deleteTempTemplateId(String tempTemplateId) {
        if (StringUtils.isBlank(tempTemplateId)) {
            return;
        }
        String tenantId = RequestContextExtUtils.getTenantId();
        deleteTempFlowTemplateProducer.sendMessage(
                new DeleteTempFlowTemplateMsqInput(tempTemplateId, tenantId));
    }

    /**
     * 补充合同文件配置， 包含合同编号、合同类型
     *
     * @param tenantId
     * @param startRequest
     * @param files
     */
    private void updateShareSignContractFileConfig(
            String tenantId, ProcessStartBO startRequest, List<FileBO> files) {
        try {
            // 先按前端传值更新
            freshShareSignContractFileConfig(startRequest, files);
            String flowTemplateId = startRequest.getFlowTemplateId();
            if (StringUtils.isBlank(flowTemplateId) || CollectionUtils.isEmpty(files)) {
                return;
            }
            // 用当前模板规则替代临时模板规则
            UserAccount tenantUserAccount = userCenterService.getUserAccountBaseByOid(tenantId);
            ProcessFlowTemplateDetailResponse response =
                    flowTemplateService.startDetail(flowTemplateId, tenantUserAccount, false, false);
            if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getFiles())) {
                return;
            }
            Map<String, FileDetailBO> contractTypeMap =
                    LambdaUtil.toMap(response.getFiles(), FileDetailBO::getFileId, identity());
            // 用模板编号规则补充
            for (FileBO file : files) {
                FileDetailBO fileDetailBO =
                        contractTypeMap.get(((FileDetailBO) file).getOriginalFileId());
                if (fileDetailBO == null
                        || (file.getContractNoType() != null
                                && SYSTEM.getType().equals(file.getContractNoType()))) {
                    continue;
                }
                file.setContractNoType(fileDetailBO.getContractNoType());
                file.setContractNoRule(fileDetailBO.getContractNoRule());
            }
        } catch (Exception e) {
            log.warn("扫码签补充合同编号类型失败", e);
        }
    }

    /**
     * 使用前端传值更新合同编号
     *
     * @param startRequest
     * @param files
     */
    private void freshShareSignContractFileConfig(ProcessStartBO startRequest, List<FileBO> files) {
        List<FileBO> freshFiles = startRequest.getFiles();
        if (CollectionUtils.isEmpty(freshFiles) || CollectionUtils.isEmpty(files)) {
            return;
        }
        // 前端传来的文件信息
        Map<String, FileBO> freshFileMap =
                LambdaUtil.toMap(freshFiles, FileBO::getFileId, identity());

        for (FileBO file : files) {
            if (!(file instanceof FileDetailBO)) {
                continue;
            }

            FileBO fileBO = freshFileMap.get(((FileDetailBO) file).getOriginalFileId());
            if (Objects.isNull(fileBO)) {
                continue;
            }
            file.setContractNoType(fileBO.getContractNoType());
            file.setContractNoRule(fileBO.getContractNoRule());
            file.setCategoryId(fileBO.getCategoryId());
            if (fileBO instanceof FileDetailBO) {
                ((FileDetailBO) file).setCategoryName(((FileDetailBO)fileBO).getCategoryName());
            }
        }
    }
}

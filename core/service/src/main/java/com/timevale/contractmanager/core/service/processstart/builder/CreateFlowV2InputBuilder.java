package com.timevale.contractmanager.core.service.processstart.builder;

import static com.timevale.contractmanager.core.service.util.IdsUtil.SPLIT_LETTER;
import static com.timevale.doccooperation.service.enums.SignRequirementEnum.*;
import static com.timevale.doccooperation.service.model.CommonStartExtensionKeyConstants.SIGN_CREATE_WAY;
import static com.timevale.doccooperation.service.model.CommonStartExtensionKeyConstants.SIGN_PRE_FLOW_ID;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.dal.bean.ProcessConfigDO;
import com.timevale.contractmanager.common.dal.bean.ProcessDO;
import com.timevale.contractmanager.common.service.bean.ProcessConfigBean;
import com.timevale.contractmanager.common.service.enums.AuthWayEnum;
import com.timevale.contractmanager.common.service.enums.FdaSignatureLangEnum;
import com.timevale.contractmanager.common.service.enums.process.ProcessSignSealSizeTypeEnum;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.common.utils.DateUtil;
import com.timevale.contractmanager.common.utils.config.SystemConfig;
import com.timevale.contractmanager.core.model.bo.fda.FDASignatureConfig;
import com.timevale.contractmanager.core.model.dto.process.config.BillingConfig;
import com.timevale.contractmanager.core.model.dto.user.OrgDeptDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.service.component.ProcessConfigConverter;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.BaseProcessService;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.process.config.ProcessBizRuleConfigService;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessStartSignParam;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.doccooperation.service.enums.*;
import com.timevale.doccooperation.service.enums.SealSpecsTypeEnum;
import com.timevale.doccooperation.service.enums.SealTypeEnum;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.doccooperation.service.model.*;
import com.timevale.doccooperation.service.util.JingBanManUtil;
import com.timevale.footstone.rpc.enums.*;
import com.timevale.footstone.rpc.model.createflow.v2.CreateFlowV2Input;
import com.timevale.footstone.rpc.model.createflow.v2.FdaTypeConfigBean;
import com.timevale.footstone.rpc.model.createflow.v2.FlowInfoBean;
import com.timevale.footstone.rpc.model.createflow.v2.SignerInfoBean;
import com.timevale.footstone.rpc.model.flowmodel.bean.*;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.*;

import com.timevale.saas.common.manage.common.service.model.bean.bill.BillIsolateDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import ma.glasnost.orika.BoundMapperFacade;
import ma.glasnost.orika.MapperFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 签署流程发起V2参数组装专用类
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Slf4j
@Component
public class CreateFlowV2InputBuilder {

    /** 是否跳过合同审批校验， 如果是true, 则签署发起时不校验是否需要审批（只要参数中带上审批模板id就校验，否则不校验） */
    private static final String EXTEND_SKIP_START_APPROVE_PRE_CHECK = "skipStartApprovePreCheck";
    /** 无效的appId */
    private static final String INVALID_APP_ID = "0";
    /** 发起人姓名 */
    private static final String EXTEND_INITIATOR_NAME = "initiatorName";
    /** 发起主体名称 */
    private static final String EXTEND_INITIATOR_SUBJECT_NAME = "initiatorSubjectName";
    /** 发起部门名称 */
    private static final String EXTEND_INITIATOR_DEPT_NAME = "initiatorDeptName";


    private static final String NO_NEED_CHECK_WILL_TYPE_BEFORE_SIGN = "before.start.sign.no.need.intersection.will.types";

    @Autowired SystemConfig systemConfig;
    @Autowired MapperFactory mapperFactory;
    @Autowired PreferencesService preferencesService;
    @Autowired BaseProcessService baseProcessService;
    @Autowired UserCenterService userCenterService;
    @Autowired ProcessBizRuleConfigService processBizRuleConfigService;
    @Autowired SaasCommonClient saasCommonClient;

    /**
     * 获取发起appId
     *
     * @param input
     * @return
     */
    private String obtainAppId(ProcessStartSignParam input) {
        // 标准签默认appId
        String defaultAppId = systemConfig.getEsignProjectId();
        // 如果发起时指定的appId无效，则返回标准签默认appId
        return INVALID_APP_ID.equals(input.getAppId()) ? defaultAppId : input.getAppId();
    }

    /**
     * 直接发起组装直接发起请求参数
     *
     * @param input
     * @return
     */
    public CreateFlowV2Input buildCreateFlowInput(ProcessStartSignParam input) {

        CreateFlowV2Input signFlowInput = new CreateFlowV2Input();
        // 设置发起应用id
        signFlowInput.setAppId(obtainAppId(input));
        // 设置发起端信息
        signFlowInput.setClientId(input.getSource());
        // 设置流程信息
        signFlowInput.setFlowInfo(buildFlowInfoBean(input));

        // 设置附件
        List<ProcessStartSignParam.FlowFile> attachments = input.getAttachments();
        if (CollectionUtils.isNotEmpty(attachments)) {
            signFlowInput.setAttachments(
                    attachments.stream().map(a -> buildAttachment(a)).collect(Collectors.toList()));
        }

        // 设置抄送人
        List<ProcessStartSignParam.FlowAccount> ccs = input.getCcs();
        if (CollectionUtils.isNotEmpty(ccs)) {
            signFlowInput.setRecipients(
                    ccs.stream().map(c -> buildRecipient(c)).collect(Collectors.toList()));
        }

        List<ProcessStartSignParam.FlowFile> signDocs = input.getDocuments();
        // 设置签署文件
        if (CollectionUtils.isNotEmpty(signDocs)) {
            signFlowInput.setDocs(
                    signDocs.stream().map(s -> buildDocument(s)).collect(Collectors.toList()));
        }

        // 设置签署人
        List<ProcessStartSignParam.FlowSigner> signers = input.getSigners();
        if (CollectionUtils.isNotEmpty(signers)) {
            // 钉签发起签署方 存在经办人章（钉签之前不支持个人空间搞出来），钉签支持个人空间后，强制将经办人的签署人处理成个人章类型
            // 2023-11 月  去除这个转换，所有端业务统一。企业参与方盖经办人章，合同还是属于企业空间
            if (Boolean.TRUE.equals(input.getOldTemplateStartData()) &&
                    !com.timevale.saas.common.enums.SignModeEnum.GLOBAL.equalBiz(input.getSignMode())) {
                long time = System.currentTimeMillis();
                log.info(
                        "start handle convert jingban seal, processId:{}, flowTemplateId:{}",
                        input.getProcessId(),
                        input.getFlowTemplateId());
                convertJingBanSignRequirementsAndSignArea(signers);
                log.info(
                        "handle convert jingban seal end times = {} ms, handler after signers : {}",
                        System.currentTimeMillis() - time,
                        JsonUtils.obj2json(signers));
            }
            // 查询全局可用意愿方式 NOTE 20241125修改：发起时已经校验过，此处无需再次校验
            List<String> globalWillTypes =
                    ConfigService.getAppConfig().getBooleanProperty(NO_NEED_CHECK_WILL_TYPE_BEFORE_SIGN, true)
                    ? WillTypeEnum.allWillTypes()
                    : preferencesService.queryPreferenceWillTypes(
                            input.getInitiator().getSubjectOid(), input.getInitiator().getSubjectGid());
            // 组装签署人信息
            List<SignerInfoBean> signerInfoBeans = Lists.newArrayList();
            for (ProcessStartSignParam.FlowSigner flowSigner : signers) {
                for (ProcessStartSignParam.FlowAccount signerAccount : flowSigner.obtainAccounts()) {
                    signerInfoBeans.add(buildSigner(flowSigner, signerAccount, input.getDocuments(), globalWillTypes));
                }
            }
            signFlowInput.setSigners(signerInfoBeans);
        }
        return signFlowInput;
    }

    /**
     * 处理参与方意愿方式
     *
     * @param inputWillTypes 发起时实际指定意愿方式
     * @param validWillTypes 全局可用意愿方式
     * @return
     */
    public List<String> handleWillTypes(List<String> inputWillTypes, List<String> validWillTypes) {

        // 如果可用意愿方式为空，则直接以发起时指定的意愿方式为准
        if (CollectionUtils.isEmpty(validWillTypes)) {
            return inputWillTypes;
        }
        // 如果发起时参与方未指定意愿方式， 直接使用可用意愿方式
        if (CollectionUtils.isEmpty(inputWillTypes)) {
            return validWillTypes;
        }
        // 如果发起时参与方指定意愿方式且可用意愿方式不为空，获取意愿方式交集
        List retainWillTypes = ListUtils.retainAll(inputWillTypes, validWillTypes);
        // 如果发起时参与方指定的意愿方式和可用意愿方式没有交集， 兜底使用可用意愿方式
        if (CollectionUtils.isEmpty(retainWillTypes)) {
            return validWillTypes;
        }
        return retainWillTypes;
    }

    /**
     * 组装流程信息
     *
     * @param input
     * @return
     */
    private FlowInfoBean buildFlowInfoBean(ProcessStartSignParam input) {
        FlowInfoBean flowInfoBean = new FlowInfoBean();
        // 设置流程基本信息
        flowInfoBean.setBusinessScene(input.getFlowName());
        flowInfoBean.setProcessId(input.getProcessId());
        flowInfoBean.setFlowId(input.getExtension(SIGN_PRE_FLOW_ID));
        flowInfoBean.setDedicatedCloudId(input.getFlowInfo().getDedicatedCloudId());
        flowInfoBean.setApprovalTemplateId(input.getFlowInfo().getApproveTemplateId());
        // 获取创建方式
        String createWay = input.getExtension(SIGN_CREATE_WAY);
        // 设置创建方式
        flowInfoBean.setCreateWay(createWay);
        // 直接发起场景下，无须设置模板id和协作流程id, 此时协作流程一定为空，模板id如果存在一定是临时模板，没有必要传给签署
        if (!CreateWayEnum.NORMAL.getType().equalsIgnoreCase(createWay)
                && !CreateWayEnum.NORMAL_BATCH.getType().equalsIgnoreCase(createWay)) {
            flowInfoBean.setTemplateId(input.getFlowTemplateId());
            // 非标流程模板发起的流程不支持流程模板自动签
            if (BooleanUtils.toBoolean(input.getExtension(CommonStartExtensionKeyConstants.UN_STANDARD_TEMPLATE_START))) {
                flowInfoBean.setTemplateAutoSign(false);
            }
            flowInfoBean.setTemplateCooperationId(input.getCooperationId());
        }
        flowInfoBean.setSignValidity(input.getFlowInfo().calculateSignValidity());
        flowInfoBean.setContractValidity(input.getFlowInfo().calculateFileValidity());
        // 设置流程扩展信息
        String signFlowExtData = input.getFlowInfo().getSignFlowExtData();
        flowInfoBean.setExtend(new HashMap<>());
        if (StringUtils.isNoneBlank(signFlowExtData)) {
            flowInfoBean.getExtend().putAll((Map<String, String>) JSON.parse(signFlowExtData));
        }
        // 设置指定付费方账号id, 如果账号为空，则默认扣发起主体，逻辑由footstone控制
        flowInfoBean.setPayerAccountId(input.getFlowInfo().getPayerAccountId());
        // 设置合同审批参数
        Map<String, Object> extraParam = Maps.newHashMap();
        extraParam.put(EXTEND_SKIP_START_APPROVE_PRE_CHECK, true);
        flowInfoBean.setExtraParam(extraParam);
        // 设置自动归档，用于控制所有签署人都签署完成后，是否自动结束流程
        flowInfoBean.setAutoArchive(true);
        // 设置自动发起，用于控制签署流程是否自动开启流程， 如果否，则创建的签署流程为草稿状态
        flowInfoBean.setAutoInitiate(true);
        // 设置发起人
        if (null != input.getInitiator()) {
            // 设置发起人账号
            flowInfoBean.setInitiatorAccountId(input.getInitiator().getAccountOid());
            // 设置发起主体账号
            flowInfoBean.setInitiatorAuthorizedAccountId(input.getInitiator().getSubjectOid());
            // 保存发起人姓名
            flowInfoBean.getExtend().put(EXTEND_INITIATOR_NAME,input.getInitiator().getAccountName());
            // 保存发起主体名称
            flowInfoBean.getExtend().put(EXTEND_INITIATOR_SUBJECT_NAME,input.getInitiator().getSubjectName());
            // 保存发起部门名称
            String deptName =
                    queryDeptName(
                            input.getInitiatorDeptId(),
                            input.getInitiator().getSubjectOid(),
                            input.getInitiator().getAccountOid());
            flowInfoBean.getExtend().put(EXTEND_INITIATOR_DEPT_NAME, deptName);
        }
        // 设置流程配置信息
        flowInfoBean.setConfigInfo(buildFlowConfig(input));
        // 处理是否能解约
        flowInfoBean.setAllowToRescind(input.getFlowInfo().getAllowRescind() != null ?
                input.getFlowInfo().getAllowRescind() : Boolean.TRUE );
        // 处理主流程配置， 部分配置需以主流程的优先
        handleProcessConfig(input, flowInfoBean);
        //fda签署配置
        handleFdaSignatureConfig(input, flowInfoBean);
        // 返回流程信息
        return flowInfoBean;
    }

    /**
     * 处理fda签署配置
     */
    private void handleFdaSignatureConfig(ProcessStartSignParam input, FlowInfoBean flowInfoBean) {
        FDASignatureConfig fdaSignatureConfig = input.getFdaSignatureConfig();
        if (Objects.nonNull(fdaSignatureConfig)) {
            if (Boolean.TRUE.equals(fdaSignatureConfig.getEnable())) {
                flowInfoBean.setSignType(FlowSignStyleEnum.FDAType.getType());
                flowInfoBean.setFdaTypeConfig(
                        Optional.ofNullable(fdaSignatureConfig.getFdaConfigs()).orElse(Lists.newArrayList()).stream()
                                .filter(FDASignatureConfig.FdaConfig::getIsDefault)
                                .map(fdaConfig -> {
                                    FdaTypeConfigBean fdaTypeConfigBean = new FdaTypeConfigBean();
                                    fdaTypeConfigBean.setFdaLanguageMode(
                                            FdaSignatureLangEnum.LANG_CHINESE.getLang().equals(fdaConfig.getLang()) ?
                                                    FDALanguageModeEnum.CHINESE.getType() : FDALanguageModeEnum.ENGLISH.getType());
                                    fdaTypeConfigBean.setFdaSigningReason(fdaConfig.getSignReasons());
                                    return fdaTypeConfigBean;
                                })
                                .collect(Collectors.toList()));
            }
        }
    }
    /**
     * 处理并填充主流程配置
     *
     * @param input
     * @param flowInfoBean
     */
    private void handleProcessConfig(ProcessStartSignParam input, FlowInfoBean flowInfoBean) {
        // 设置默认流程配置对象
        if (null == flowInfoBean.getConfigInfo()) {
            flowInfoBean.setConfigInfo(new FlowConfig());
        }
        // 获取流程配置信息
        FlowConfig configInfo = flowInfoBean.getConfigInfo();
        // 查询主流程基本信息
        ProcessDO process = baseProcessService.getProcess(input.getProcessId());
        // 查询主流程配置信息
        ProcessConfigDO processConfigDO =
                baseProcessService.queryProcessConfig(input.getProcessId());
        ProcessConfigBean processConfigBean =
                ProcessConfigConverter.convertProcessConfig(processConfigDO, true);
        // 填充主流程信息
        configInfo.setRedirectDelayTime(processConfigBean.getRedirectDelay());
        configInfo.setBackToRedirectUrl(true);
        // 优先以主流程的签订截止时间和合同到期时间为准， 有效期类型设置为指定到期时间
        if (null != process.getProcessEndTime()) {
            flowInfoBean.setSignValidity(DateUtil.dateToLong(process.getProcessEndTime()));
        }
        if (null != process.getContractEndTime()) {
            flowInfoBean.setContractValidity(DateUtil.dateToLong(process.getContractEndTime()));
        }
    }

    /**
     * 组装流程配置信息
     *
     * @param input
     * @return
     */
    private FlowConfig buildFlowConfig(ProcessStartSignParam input) {
        BillingConfig billingConfig = processBizRuleConfigService.billingConfig(input.getSignMode());
        // 初始化流程配置信息
        FlowConfig flowConfig = new FlowConfig();
        String billGid;
        if(StringUtils.isBlank(input.getFlowInfo().getPayerAccountId())){
            billGid=input.getInitiator().getSubjectGid();
        }else {
            UserAccount userAccount= userCenterService.getUserAccountBaseByOid(input.getFlowInfo().getPayerAccountId());
            billGid=userAccount!=null?userAccount.getAccountGid():null;
        }
        BillStrategy billStrategy =
                getFlowBillingStrategy(input.getSource(),billGid,billingConfig.getNeedIsolate(),input.getProcessId());
        if(billStrategy!=null){
            flowConfig.setBillStrategy(billStrategy.getBillStrategy());
            flowConfig.setBillSceneValue(billStrategy.getBillSceneValue());
        }
        // 设置签署通知回调地址
        if (StringUtils.isNotBlank(input.getFlowInfo().getSignedNoticeUrl())) {
            flowConfig.setNoticeDeveloperUrl(input.getFlowInfo().getSignedNoticeUrl());
        }
        // 设置一键落章配置
        if (null != input.getFlowInfo().getBatchDropSeal()) {
            flowConfig.setBatchDropSeal(input.getFlowInfo().getBatchDropSeal());
        }
        flowConfig.setSignMode(input.getSignMode());
        flowConfig.setNoticeType(input.getNoticeType());
        return flowConfig;
    }

    /**
     * 组装抄送人信息
     *
     * @param c
     * @return
     */
    private Recipient buildRecipient(ProcessStartSignParam.FlowAccount c) {
        Recipient recipient = new Recipient();
        recipient.setRecipientAccount(c.getAccount());
        recipient.setRecipientAccountId(c.getAccountOid());
        recipient.setRecipientIdentityAccountId(c.getSubjectOid());
        recipient.setRecipientIdentityAccountName(c.getSubjectName());
        recipient.setRecipientIdentityAccountType(c.getSubjectType());
        recipient.setRecipientName(c.getAccountName());
        recipient.setRecipientNickname(c.getAccountNickname());
        return recipient;
    }

    /**
     * 组装签署文档信息
     *
     * @param file
     * @return
     */
    private Document buildDocument(ProcessStartSignParam.FlowFile file) {
        Document document = new Document();
        document.setFileId(file.getFileId());
        document.setFileName(file.getFileName());
        return document;
    }

    /**
     * 组装附件信息
     *
     * @param file
     * @return
     */
    private Attachment buildAttachment(ProcessStartSignParam.FlowFile file) {
        Attachment attachment = new Attachment();
        attachment.setAttachmentName(file.getFileName());
        attachment.setFileId(file.getFileId());
        return attachment;
    }

    /**
     * 组装签署人信息
     *
     * @param flowSigner
     * @return
     */
    private SignerInfoBean buildSigner(
            ProcessStartSignParam.FlowSigner flowSigner,
            ProcessStartSignParam.FlowAccount instance,
            List<ProcessStartSignParam.FlowFile> signFiles,
            List<String> globalWillTypes) {
        SignerInfoBean signer = new SignerInfoBean();
        // 设置签署人账号信息
        signer.setSignerAccount(buildSignerAccount(flowSigner, instance, globalWillTypes));
        // 设置签署顺序
        if (flowSigner.getSignOrder() != null) {
            signer.setSignOrder(flowSigner.getSignOrder());
        }
        // 设置签署人签章签署区信息
        signer.setSignFields(buildSignerSignFields(flowSigner, signFiles));
        // 设置签署人备注签署区信息
        if (CollectionUtils.isNotEmpty(flowSigner.getRemarkSignAreas())) {
            signer.setRemarkFields(
                    flowSigner.getRemarkSignAreas().stream()
                            .map(CreateFlowV2InputBuilder::buildRemarkSignerSignfield)
                            .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(flowSigner.getDateSignAreas())) {
            signer.setDateSignFieldConfig(
                    flowSigner.getDateSignAreas().stream()
                            .map(CreateFlowV2InputBuilder::buildDateSignFieldConfigBean)
                            .collect(Collectors.toList()));
        }
        ProcessStartSignParam.FlowParticipant.Ext ext = flowSigner.getExt();
        if (ext != null) {
            signer.setForceRead(ext.getForceReadEnd() == null ? false : ext.getForceReadEnd());
            signer.setForcedReadingTime(ext.getForceReadTime());
            // 设置是否或签方
            if (ParticipantModeEnum.isOrSign(ext.getParticipantMode())) {
                signer.setSignTaskType(SignTaskTypeEnum.OR_SIGN.getType());
            } else {
                signer.setSignTaskType(SignTaskTypeEnum.CO_SIGN.getType());
            }
            signer.setParticipantStartType(ext.getParticipantStartType());
            signer.setParticipantStartValue(ext.getParticipantStartValue());
            signer.setLicenseStart(ext.getForceLicense());
            signer.setShowCrossEnterpriseSeal(ext.getSupportCrossSignSeal());
        }
        return signer;
    }

    /**
     * 组装签署人账号信息
     *
     * @param instance
     * @param globalWillTypes
     * @return
     */
    private SignerInfoBean.SignerAccountBean buildSignerAccount(
            ProcessStartSignParam.FlowSigner flowSigner,
            ProcessStartSignParam.FlowAccount instance,
            List<String> globalWillTypes) {
        SignerInfoBean.SignerAccountBean signerAccountBean = new SignerInfoBean.SignerAccountBean();
        signerAccountBean.setSignerAuthorizedAccountName(instance.getSubjectName());
        signerAccountBean.setSignerAuthorizedAccountId(instance.getSubjectOid());
        signerAccountBean.setSignerAccountName(instance.getAccountName());
        signerAccountBean.setSignerAccountId(instance.getAccountOid());
        signerAccountBean.setSignerNickname(instance.getAccountNickname());
        signerAccountBean.setSignerLicense(instance.getAccountLicense());
        signerAccountBean.setSignerLicenseType(instance.getAccountLicenseType());
        signerAccountBean.setSignerRoleLabel(flowSigner.getSignerLabel());
        ProcessStartSignParam.FlowSigner.Ext ext = flowSigner.getExt();
        if (null != ext) {
            // 设置意愿方式
            if (null == ext.getNeedWill() || Boolean.TRUE.equals(ext.getNeedWill())) {
                List<String> willTypes = handleWillTypes(ext.getWillTypes(), globalWillTypes);
                // // 如果意愿认证方式为空或者包含所有，默认为不指定， 仅指定场景流程才需要固化意愿方式
                if (CollectionUtils.isNotEmpty(willTypes)
                        && !willTypes.containsAll(WillTypeEnum.allWillTypes())) {
                    // 固化意愿认证方式
                    signerAccountBean.setWillTypes(willTypes);
                } else {
                    signerAccountBean.setWillTypes(Lists.newArrayList());
                }
                signerAccountBean.setRequiredWilling(true);
            } else {
                signerAccountBean.setRequiredWilling(false);
            }

            if(CollectionUtils.isNotEmpty(ext.getAttachmentConfigs())){
                List<SignerInfoBean.AttachmentConfig> attachmentConfigs = ext.getAttachmentConfigs().stream().map(p->{
                    SignerInfoBean.AttachmentConfig config = new SignerInfoBean.AttachmentConfig();
                    config.setAttachmentName(p.getName());
                    config.setCheckSignerInfo(p.getCheckInfo());
                    config.setMultiple(p.getMultiple());
                    config.setRequired(p.getRequired());
                    config.setVerify(p.getType() != null);
                    config.setVerificationType(p.getType());
                    return config;
                }).collect(Collectors.toList());
                signerAccountBean.setAttachmentConfigs(attachmentConfigs);
            }
            // 签署声明
            signerAccountBean.setSignTipsTitle(ext.getSignTipsTitle());
            signerAccountBean.setSignTipsContent(ext.getSignTipsContent());
            signerAccountBean.setAuthWay(ext.getAuthWay());
            signerAccountBean.setAccessToken(ext.getAccessToken());
            // 通知方式，非空即设值， 空数组表示不通知
            List<Integer> noticeTypes = null != instance.getSignNoticeTypes() ? instance.getSignNoticeTypes() : ext.getSignNoticeTypes();
            signerAccountBean.setNoticeType(null != noticeTypes ? StringUtils.join(noticeTypes, SPLIT_LETTER) : null);
            if (StringUtils.isNotBlank(ext.getAuthWay())) {
                // 不是登录 + 实名的，不需要进行实名
                signerAccountBean.setRequiredRealName(AuthWayEnum.REAL_NAME_AUTH.getCode().equals(ext.getAuthWay()));
            }
        }

        return signerAccountBean;
    }

    /**
     * 组装签署人签署区列表
     *
     * @param instance
     * @param signFiles
     * @return
     */
    private List<SignerInfoBean.SignFieldInfo> buildSignerSignFields(
            ProcessStartSignParam.FlowSigner instance,
            List<ProcessStartSignParam.FlowFile> signFiles) {
        // 指定位置场景,  注意！！！ 解约也会指定签署区
        if (CollectionUtils.isNotEmpty(instance.getSignAreas())) {
            // 个人章区分手绘章和AI手绘章 特殊逻辑处理
            return instance.getSignAreas().stream()
                    .map(sf -> buildSignerSignfield(sf, instance))
                    .filter(sf -> null != sf)
                    .collect(Collectors.toList());
        }
        // 非指定位置场景
        List<SignerInfoBean.SignFieldInfo> signFieldInfos = Lists.newArrayList();
        for (ProcessStartSignParam.FlowFile signFile : signFiles) {
            signFieldInfos.addAll(buildUnlimitSignfields(instance, signFile));
        }
        return signFieldInfos;
    }

    /**
     * 组装自由签署区
     *
     * @param instance
     * @param signFile
     * @return
     */
    private List<SignerInfoBean.SignFieldInfo> buildUnlimitSignfields(
            ProcessStartSignParam.FlowSigner instance, ProcessStartSignParam.FlowFile signFile) {
        String sealType = instance.obtainSealType();
        String signRequirements = instance.obtainSignRequirements();
        // 兜底场景补充签署要求
        if (StringUtils.isBlank(signRequirements)) {
            // 个人主体设置为个人， 企业主体设置为企业
            signRequirements =
                    SubjectTypeEnum.ORG.getType().equals(instance.getSubjectType())
                            ? SignerRoleEnum.ORGAN.getType()
                            : SignerRoleEnum.PERSON.getType();
        } // 根据signRequirement 拆签署区
        List<SignerInfoBean.SignFieldInfo> signFieldInfos = Lists.newArrayList();
        for (String signRequirement : IdsUtil.getIdList(signRequirements)) {
            // buildSignFieldInfo 这个方法可能返回null, 但是在当前场景自由签署区不会发生为null的情况，只有指定签署区才会有null的可能
            SignerInfoBean.SignFieldInfo signField =
                    buildSignFieldInfo(signFile.getFileId(), sealType, signRequirement, instance);
            signField.setSignType(SignTypeEnum.Unspecified.getType());
            if(ProcessSignSealSizeTypeEnum.AUTO_ADAPTABLE.getCode().equals(instance.getSignSealSizeType())){
                signField.setPosBean(buildFreeSignPosSetUp(instance));
            }
            signFieldInfos.add(signField);
        }

        return signFieldInfos;
    }

    /**
     * 组装指定位置签署区
     *
     * @param s
     * @param signer
     * @return
     */
    private SignerInfoBean.SignFieldInfo buildSignerSignfield(
            SignArea s, ProcessStartSignParam.FlowSigner signer) {

        // 组装签署区基本信息
        SignerInfoBean.SignFieldInfo signerSignfield =
                buildSignFieldInfo(s.getFileId(), s.getSealType(), s.getSignRequirements(), signer);
        if (null == signerSignfield) {
            return null;
        }
        // 设置印章id, 如果签署方签署设置中已指定印章id, 则不修改
        if (StringUtils.isBlank(signerSignfield.getSealId())) {
            signerSignfield.setSealId(s.getSealId());
        }
        // 设置签署区是否骑缝签
        if (s.isQiFeng()) {
            signerSignfield.setSignType(SignTypeEnum.Perforation.getType());
        }
        // 设置签署区是否必签
        signerSignfield.setMustSign(!s.isOptional());
        // 设置签署区坐标
        signerSignfield.setPosBean(buildSignPos(s));
        // 返回签署区
        return signerSignfield;
    }

    /**
     * 组装签署区基本信息
     *
     * @param fileId
     * @param sealType
     * @param signRequirement  自由签署区这里传的是 单个 signRequirement， 指定签署区这里传的是 signRequirements, 但是只有一个值，因为外部进行了拆分
     * @param signer
     * @return
     */
    private SignerInfoBean.SignFieldInfo buildSignFieldInfo(
            String fileId,
            String sealType,
            String signRequirement,
            ProcessStartSignParam.FlowSigner signer) {
        // 获取人维度设置的签章类型有哪几种,逗号隔开
        String extSignRequirements = signer.obtainSignRequirements();
        // 获取人维度设置的印章类型，手绘 or 模板
        String extSealType = signer.obtainSealType();
        // 企业主体下，如果签署区指定的签署要求不在签署人的签署要求范围内， 则过滤签署区
        // 指定签署区才会触发下面这个case
        // 比如企业参与方 签章要求勾选了 法人 和 企业 点击下一步设置好签署区这个时候有2个签署区，这时在一次编辑模版把签署方 印章-法人 去掉点击下一步。这个时候就会出现
        // 数据库ext列 signRequirement 没有法人章 但是又有法人签署区控件
        if (!SubjectTypeEnum.PERSON.getType().equals(signer.getSubjectType())
                && StringUtils.isNoneBlank(extSignRequirements, signRequirement)
                && !extSignRequirements.contains(signRequirement)) {
            return null;
        }
        // 如果签署区指定了印章类型，则以签署区指定的优先， 否则默认获取人维度的印章类型
        sealType = StringUtils.isBlank(sealType) ? extSealType : sealType;
        // 根据印章类型转化为签署需要的印章类型值
        //优化后的逻辑
        String signSealType = (StringUtils.isNotBlank(sealType) ||
                    SubjectTypeEnum.PERSON.getType().equals(signer.getSubjectType()) ||
                    (null != signer.getExt() && null != signer.getExt().getSignRequirements() &&  signer.getExt().getSignRequirements().contains(SignRequirementEnum.AGENT_SEAL.getType().toString()))) ?
                    SealTypeEnum.getSealType(sealType) : "";

        // 根据印章类型获取手绘类型
        String handDrawnWay = SealTypeEnum.getHandDrawnWay(sealType);
        SignerInfoBean.SignFieldInfo signerSignfield = new SignerInfoBean.SignFieldInfo();
        signerSignfield.setFileId(fileId);
        // 根据人来获取sealType, 0-手绘，1-模板
        signerSignfield.setSealType(signSealType);
        signerSignfield.setAutoExecute(false);
        // 个人主体
        if (SubjectTypeEnum.PERSON.getType().equals(signer.getSubjectType())) {
            // 设置手绘方式
            signerSignfield.setHandDrawnWay(handDrawnWay);
            // 设置签署要求为个人
            signerSignfield.setSignerRoleType(SignerRoleEnum.PERSON.getType());
            return signerSignfield;
        }
        // 企业主体
        // 默认签署角色信息和签署区的签署要求一致
        String signerRoleType = signRequirement;
        // 如果签署角色信息为空， 则默认根据签署方的签署要求获取默认签署角色信息
        if (StringUtils.isBlank(signerRoleType)) {
            signerRoleType =
                    buildDefaultSignerRoleType(signer.getSubjectType(), extSignRequirements)
                            .getType();
        }
        signerSignfield.setSignerRoleType(signerRoleType);
        // 经办人设置手绘方式
        if (StringUtils.isNotBlank(signRequirement) && signRequirement.contains(SignerRoleEnum.ORGAN_AGENT.getType())) {
            signerSignfield.setHandDrawnWay(handDrawnWay);
        }
        // 设置指定的印章id或者是印章类型
        ProcessStartSignParam.FlowParticipant.Ext ext = signer.getExt();
        if (ext != null
                && ext.getSignSealType() != null
                && SignerRoleEnum.ORGAN.getType().equals(signerRoleType)) {
            // 企业章 可指定印章类型 或者 某个印章
            if (ext.getSignSealType().equals(StartSignSpecifySealTypeEnum.SEAL_ID.getType())) {
                signerSignfield.setSealId(ext.getSignSeal());
            } else if (ext.getSignSealType()
                    .equals(StartSignSpecifySealTypeEnum.BIZ_TYPE.getType())) {
                signerSignfield.setSealBizTypes(ext.getSignSeal());
            }
        }
        return signerSignfield;
    }

    private SignPos buildFreeSignPosSetUp(ProcessStartSignParam.FlowSigner signer) {
        SignPos signPos = new SignPos();
        signPos.setSealSpecs(ProcessSignSealSizeTypeEnum.AUTO_ADAPTABLE.getCode().equals(signer.getSignSealSizeType())? SealSpecsTypeEnum.ADAPTER_SEAL_SIZE.getType() :null);
        return signPos;
    }

    /**
     * 组装签署区签署角色信息
     *
     * @param subjectType 签署方主体类型
     * @param signRequirements 签署方签署要求
     * @return
     */
    private SignerRoleEnum buildDefaultSignerRoleType(
            Integer subjectType, String signRequirements) {
        // 如果签署方为个人主体， 默认返回个人签署角色
        if (SubjectTypeEnum.PERSON.getType().equals(subjectType)) {
            return SignerRoleEnum.PERSON;
        }
        // 签署方为企业主体
        // 获取签署方的签署要求
        List<String> signerSignRequirements = IdsUtil.getIdList(signRequirements);
        // 如果签署方的签署要求为空，兜底补充企业主体签署要求
        if (CollectionUtils.isEmpty(signerSignRequirements)) {
            signerSignRequirements.add(ENTERPRISE_SEAL.getType().toString());
        }
        // 如果签署方的签署要求包含企业章，则缺省签署要求的签署区默认为企业章签署区
        if (signerSignRequirements.contains(ENTERPRISE_SEAL.getType().toString())) {
            return SignerRoleEnum.ORGAN;
        }
        // 如果签署方的签署要求包含法人章，则缺省签署要求的签署区默认为法人章签署区
        if (signerSignRequirements.contains(CORPORATE_SEAL.getType().toString())) {
            return SignerRoleEnum.ORGAN_LEGAL;
        }
        // 如果签署方的签署要求包含经办人章，则缺省签署要求的签署区默认为经办人章签署区
        if (signerSignRequirements.contains(AGENT_SEAL.getType().toString())) {
            return SignerRoleEnum.ORGAN_AGENT;
        }
        // 兜底默认企业章
        return SignerRoleEnum.ORGAN;
    }

    /**
     * 组装签署区坐标
     *
     * @param signArea
     * @return
     */
    private SignPos buildSignPos(SignArea signArea) {
        SignPos signPos = new SignPos();
        boolean signDateForbidden=BooleanUtils.isTrue(signArea.getSignDateForbidden());
        if(signDateForbidden){
            signPos.setSignDateBeanType(SignDateBeanTypeEnum.FORBID.getType());
        }
        // 如果是骑缝章， 仅需要设置签署区Y坐标，忽略签署日期位置
        if (signArea.isQiFeng()) {
            signPos.setPosPage(signArea.getPage());
            signPos.setPosY(signArea.getSignPos().getY());
        } else {
            // 如果是非骑缝章，设置签署区X,Y坐标
            signPos.setPosX(signArea.getSignPos().getX());
            signPos.setPosY(signArea.getSignPos().getY());
            signPos.setPosPage(String.valueOf(signArea.getSignPos().getPage()));
            // 如果需要显示日期， 设置签署日期
            if (Objects.nonNull(signArea.getSignDatePos())) {
                Pos p = signArea.getSignDatePos();
                SignDate signDate = new SignDate();
                signDate.setPosX(p.getX());
                signDate.setPosY(p.getY());
                signDate.setPosPage(p.getPage());
                signDate.setFormat(signArea.getDateFormat());
                signPos.setSignDateBean(signDate);
                // 签署时间戳格式类型,0-禁止 1-必须 2-不限制
                if(!signDateForbidden){
                    signPos.setSignDateBeanType(SignDateBeanTypeEnum.MUST.getType());
                }
            }
            if (signArea.isShowDate()) {
                if(!signDateForbidden){
                    signPos.setSignDateBeanType(SignDateBeanTypeEnum.MUST.getType());
                }
            }
            // 如果是自定义签署区大小，设置大小
            if (signArea.getSignPos() != null
                    && SealSpecsTypeEnum.isCustomSizeSealSpecs(signArea.getSignPos().getSealSpecs())) {
                signPos.setHeight(signArea.getSignPos().getHeight());
                signPos.setWidth(signArea.getSignPos().getWidth());
                signPos.setSealSpecs(SealSpecsTypeEnum.CUSTOM_SIZE.getType());

                log.info("use_seal_specs fileId: {}, structId: {}", signArea.getFileId(), signArea.getStructId());
            }
        }
        return signPos;
    }

    /**
     * 组装备注签署区信息
     *
     * @param remarkSignArea
     * @return
     */
    public static SignerInfoBean.RemarkField buildRemarkSignerSignfield(
            RemarkSignArea remarkSignArea) {
        SignerInfoBean.RemarkField remarkField = new SignerInfoBean.RemarkField();
        remarkField.setFileId(remarkSignArea.getFileId());
        remarkField.setInputType(remarkSignArea.getInputType());
        remarkField.setRemarkContent(remarkSignArea.getRemarkContent());
        remarkField.setAiCheck(remarkSignArea.getAiCheck());
        // 位置信息
        remarkField.setPosX(remarkSignArea.getSignPos().getX());
        remarkField.setPosY(remarkSignArea.getSignPos().getY());
        remarkField.setPosPage(String.valueOf(remarkSignArea.getSignPos().getPage()));
        if (null != remarkSignArea.getSignPos().getWidth()) {
            remarkField.setRemarkFieldWidth(remarkSignArea.getSignPos().getWidth().intValue());
        }
        if (null != remarkSignArea.getSignPos().getHeight()) {
            remarkField.setRemarkFieldHeight(remarkSignArea.getSignPos().getHeight().intValue());
        }
        if (null != remarkSignArea.getFontSize()) {
            remarkField.setRemarkFontSize(remarkSignArea.getFontSize().intValue());
        }
        remarkField.setMustSign(!remarkSignArea.isOptional());
        if (StringUtils.isNotBlank(remarkSignArea.getSignerRoleType())) {
            remarkField.setSignerRoleType(remarkSignArea.getSignerRoleType());
        }
        return remarkField;
    }

    public static SignerInfoBean.DateSignFieldConfigBean buildDateSignFieldConfigBean(DateSignArea dateSignArea) {
        SignerInfoBean.DateSignFieldConfigBean dateSignFieldConfigBean = new SignerInfoBean.DateSignFieldConfigBean();
        dateSignFieldConfigBean.setFileId(dateSignArea.getFileId());
        // 位置信息
        dateSignFieldConfigBean.setSignDatePositionX(dateSignArea.getSignPos().getX());
        dateSignFieldConfigBean.setSignDatePositionY(dateSignArea.getSignPos().getY());
        dateSignFieldConfigBean.setSignDatePositionPage(dateSignArea.getSignPos().getPage());
        if (null != dateSignArea.getFontSize()) {
            dateSignFieldConfigBean.setFontSize(dateSignArea.getFontSize().intValue());
        }
        dateSignFieldConfigBean.setDateFormat(dateSignArea.getDateFormat());
        dateSignFieldConfigBean.setSignerRoleType(dateSignArea.getSignerRoleType());
        return dateSignFieldConfigBean;
    }

    /**
     * 钉签发起签署方 存在经办人章（钉签之前不支持个人空间搞出来），钉签支持个人空间后，强制将经办人的签署人处理成个人章类型
     *
     * @param flowSigners 签署人列表
     *
     */
    private void convertJingBanSignRequirementsAndSignArea(
            List<ProcessStartSignParam.FlowSigner> flowSigners) {

        if (CollectionUtils.isEmpty(flowSigners)) {
            return;
        }

        BoundMapperFacade<ProcessStartSignParam.FlowAccount, ProcessStartSignParam.FlowAccount>
                mapperFacade =
                        mapperFactory.getMapperFacade(
                                ProcessStartSignParam.FlowAccount.class,
                                ProcessStartSignParam.FlowAccount.class);
        // 用来装载从带有经办人的企业主体的签署方拆分出来的个人签署方信息
        List<ProcessStartSignParam.FlowSigner> psnSignerInfos = new ArrayList<>();

        for (ProcessStartSignParam.FlowSigner flowSigner : flowSigners) {

            String signRequirements = flowSigner.obtainSignRequirements();

            // 签署方 签署要求 为空或者不包含经办人章 不处理
            if (!JingBanManUtil.isNeedHandle(signRequirements)) {
                continue;
            }
            log.info("agentSign only jingban : {} oid : {} subjectOid : {}",
                    flowSigner.getSignerLabel(), flowSigner.getAccountOid(), flowSigner.getSubjectOid());
            // 签署要求只有经办人章  这种case 代表只是个人
            if (JingBanManUtil.isOnlyPsnJingBan(signRequirements)) {
                // 当前singerInfo 保持不变
                flowSigner.getExt().setSignRequirements(null);
                flowSigner.setSubjectType(SubjectTypeEnum.PERSON.getType());
                if (CollectionUtils.isNotEmpty(flowSigner.getAccounts())) {
                    List<ProcessStartSignParam.FlowAccount> signerAccounts = new ArrayList<>();
                    for (ProcessStartSignParam.FlowAccount account : flowSigner.obtainAccounts()) {
                        ProcessStartSignParam.FlowAccount signerAccount = mapperFacade.map(account);
                        signerAccount.setSubjectType(SubjectTypeEnum.PERSON.getType());
                        signerAccount.setSubjectOid(signerAccount.getAccountOid());
                        signerAccount.setSubjectName(signerAccount.getAccountName());
                        signerAccounts.add(signerAccount);
                    }
                    flowSigner.setAccounts(signerAccounts);
                }

                // 将签名域签署要求清除 就表示为普通个人
                if (CollectionUtils.isEmpty(flowSigner.getSignAreas())) {
                    continue;
                }

                for (SignArea signArea : flowSigner.getSignAreas()) {
                    signArea.setSignRequirements(null);
                }
                continue;
            }
            // 签署要求不仅仅只有经办人章 可能还有企业章或者法定代表人章
            // 把经办人的签署要求删除掉
            signRequirements = JingBanManUtil.removeJingBanSignRequirements(signRequirements);
            flowSigner.getExt().setSignRequirements(signRequirements);

            // 需要把企业章/法定代表人章 和 经办人章 一分为二， 分出 企业主体签署人 和 普通个人签署人
            ProcessStartSignParam.FlowSigner psnSignerInfo =
                    mapperFactory
                            .getMapperFacade()
                            .map(flowSigner, ProcessStartSignParam.FlowSigner.class);
            // 设置成普通个人签署人
            psnSignerInfo.getExt().setSignRequirements(null);
            psnSignerInfo.getExt().setAttachmentConfigs(null);
            psnSignerInfo.setSubjectType(SubjectTypeEnum.PERSON.getType());
            if (CollectionUtils.isNotEmpty(psnSignerInfo.getAccounts())) {
                List<ProcessStartSignParam.FlowAccount> signerAccounts = new ArrayList<>();
                for (ProcessStartSignParam.FlowAccount account : psnSignerInfo.obtainAccounts()) {
                    ProcessStartSignParam.FlowAccount signerAccount = mapperFacade.map(account);
                    signerAccount.setSubjectType(SubjectTypeEnum.PERSON.getType());
                    signerAccount.setSubjectOid(signerAccount.getAccountOid());
                    signerAccount.setSubjectName(signerAccount.getAccountName());
                    signerAccounts.add(signerAccount);
                }
                psnSignerInfo.setAccounts(signerAccounts);
            }
            psnSignerInfo.setSignAreas(null);
            // 追加个人签署方信息
            psnSignerInfos.add(psnSignerInfo);

            if (CollectionUtils.isEmpty(flowSigner.getSignAreas())) {
                continue;
            }
            // 设置默认签署区列表
            psnSignerInfo.setSignAreas(new ArrayList<>());

            Iterator<SignArea> iterator = flowSigner.getSignAreas().iterator();
            while (iterator.hasNext()) {
                SignArea signArea = iterator.next();
                // 为空 或者 为 企业章/法人章 不处理
                if (!JingBanManUtil.isOnlyPsnJingBan(signArea.getSignRequirements())) {
                    continue;
                }
                // 经办人章 转成普通个人
                SignArea psnSignArea =
                        mapperFactory.getMapperFacade().map(signArea, SignArea.class);
                psnSignArea.setSignRequirements(null);
                // 追加个人签署区
                psnSignerInfo.getSignAreas().add(psnSignArea);
                // 将当前这个签署区 从 signerInfo 的签署区集合中移除
                iterator.remove();
            }
        }

        flowSigners.addAll(psnSignerInfos);
    }

    private String queryDeptName(String deptId, String subjectId, String accountId) {
        try {
            if (StringUtils.isNotBlank(deptId)) {
                OrgDeptDTO dept = userCenterService.getDeptInfosByDeptId(subjectId, deptId);
                return dept != null ? dept.getDeptName() : "";
            }
            List<OrgDeptDTO> deptInfoList =
                    userCenterService.getDeptInfosByMemberId(subjectId, accountId);
            if (CollectionUtils.isNotEmpty(deptInfoList)) {
                return deptInfoList.stream()
                        .map(OrgDeptDTO::getDeptName)
                        .collect(Collectors.joining(","));
            }
        } catch (Exception e) {
            log.warn("start sign flow query deptName failed ", e);
        }
        return "";
    }

  /**
   * 获取签署的计费策略
   *
   * @param clientId  发起签署的端
   * @param processId
   * @return
   */
  private BillStrategy getFlowBillingStrategy(String clientId, String gid,Boolean needIsolate, String processId) {
    //优先取入参中的计费策略与隔离码
    if(StringUtils.isNotBlank(processId)){
      ProcessConfigBean processConfigBean = baseProcessService.queryProcessConfigExt(processId);
      if(!StringUtils.isAnyBlank(processConfigBean.getBillStrategy(), processConfigBean.getBillSceneValue())){
          return BillStrategy.builder()
                  .billStrategy(processConfigBean.getBillStrategy())
                  .billSceneValue(processConfigBean.getBillSceneValue())
                  .build();
      }
    }
    if (Boolean.FALSE.equals(needIsolate)) {
      return null;
    }
    BillIsolateDTO billIsolateDTO = saasCommonClient.getBillIsolateInfo(clientId,gid);
    // 强隔离现在默认是生态融合隔离计费
    return billIsolateDTO != null && billIsolateDTO.getIsIsolate()
        ? new BillStrategy(
            billIsolateDTO.getSceneValue(), BillingStrategyEnum.FUSION_BILLING.getType())
        : null;
  }

  @Data
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  public static class BillStrategy{
      /**
       * 计费隔离码
       */
      private String billSceneValue;
      /**
       * 计费策略
       */
      private String billStrategy;
  }
}

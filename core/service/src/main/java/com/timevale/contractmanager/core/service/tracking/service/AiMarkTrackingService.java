package com.timevale.contractmanager.core.service.tracking.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.timevale.cat.toolkit.datacollect.DataCollector;
import com.timevale.contractmanager.common.service.constant.SystemConstant;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.tracking.bean.TrackingCollectBean;
import com.timevale.saas.tracking.service.custom.ICustomTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

/**
 * ai手绘埋点
 *
 * <AUTHOR>
 * @since 2022-12-09
 */
@Component
@Slf4j
public class AiMarkTrackingService implements ICustomTrackingService {
    @Override
    public String trackingName() {
        return SystemConstant.TRACKING_KEY_AI_MARK;
    }

    @Override
    public void tracking(TrackingCollectBean trackingCollectBean) {
        String distinctId = UUID.randomUUID().toString();

        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("appId", trackingCollectBean.getAppId());
        dataMap.put("PlatformType", trackingCollectBean.getPlatform() == null ?  "" : trackingCollectBean.getPlatform());
        dataMap.put("success", StringUtils.isBlank(trackingCollectBean.getErrorMsg()));
        dataMap.put("errorMsg", trackingCollectBean.getErrorMsg() == null ? "" : trackingCollectBean.getErrorMsg());
        log.info("埋点数据，distinctId:{}, event:{}, data:{}", distinctId, trackingCollectBean.getTrackingKey(),
                JSON.toJSONString(dataMap));
        DataCollector.sensorsCollect(distinctId, false, trackingCollectBean.getTrackingKey(), dataMap);
    }
}

package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessArchiveMsgEntity;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessGroupingDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessGroupingParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by tianlei on 2022/5/16
 */
@Slf4j
@Component
public class ProcessArchiveDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessReadClient contractProcessReadClient;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processManageTopicName, ProcessChangeTagEnum.PROCESS_ARCHIVE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessArchiveMsgEntity entity = JsonUtils.json2pojo(data, ProcessArchiveMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        ProcessArchiveMsgEntity entity = (ProcessArchiveMsgEntity) collectContext.getData();

        String menuId = entity.getMenuId();
        String tenantId = entity.getTenantId();
//        Integer archiveType = entity.getArchiveType();
//        String contractNo = entity.getContractNo();
//        Account archiverPerson = entity.getArchiverPerson();
//        Long archiveTime = entity.getArchiveTime();

        if (StringUtils.isEmpty(menuId) || StringUtils.isBlank(tenantId)) {
            log.info(LOG_PREFIX + "archive param miss");
            return;
        }

        List<ContractProcessGroupingDTO> existGroupingInfoList =
                Optional.ofNullable(contractProcessReadClient.getGroupingInfo(processId)).orElse(new ArrayList<>());


        Integer existIndex = null;
        ContractProcessGroupingDTO findProcessGroupingDTO = null;
        for (int i = 0; i < existGroupingInfoList.size(); i++) {
            ContractProcessGroupingDTO existProcessGroupingDTO = existGroupingInfoList.get(i);
            // 以 tenantId 作为 唯一标准
            if (Objects.equals(existProcessGroupingDTO.getSubjectId(), entity.getTenantId())) {
                findProcessGroupingDTO = existProcessGroupingDTO;
                existIndex = i;
            }
        }

        if (null != findProcessGroupingDTO) {
            List<String> menuIdList = Optional.ofNullable(findProcessGroupingDTO.getMenuIdList()).orElse(new ArrayList<>());
            if (menuIdList.contains(menuId)) {
                // 已经存在这个 menuId
                return;
            }
            menuIdList.add(menuId);

            // 获取要更新的参数
            ContractProcessGroupingParam updateParam = getParam(entity);
            updateParam.setMenuIdList(menuIdList);

            // 参数转换
            List<ContractProcessGroupingParam> paramList =
                    ProcessDataCollectConverter.processGroupingDTO2ParamList(existGroupingInfoList);

            // 数据替换
            paramList.set(existIndex, updateParam);

            ContractProcessUpdateParam param = new ContractProcessUpdateParam();
            param.setProcessId(processId);
            param.setGroupingInfo(paramList);
            param.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_ARCHIVE);
            contractProcessWriteClient.updateByProcessId(param);
            return;
        }

        // 获取要新增的参数
        ContractProcessGroupingParam addParam = getParam(entity);
        addParam.setMenuIdList(Lists.newArrayList(menuId));

        // 参数转换
        List<ContractProcessGroupingParam> paramList =
                ProcessDataCollectConverter.processGroupingDTO2ParamList(existGroupingInfoList);
        // 新增数据
        paramList.add(addParam);

        ContractProcessUpdateParam param = new ContractProcessUpdateParam();
        param.setProcessId(processId);
        param.setGroupingInfo(paramList);
        param.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_ARCHIVE);
        contractProcessWriteClient.updateByProcessId(param);
    }

    private static ContractProcessGroupingParam getParam(ProcessArchiveMsgEntity entity) {
        ContractProcessGroupingParam param = new ContractProcessGroupingParam();
        param.setSubjectId(entity.getTenantId());
        param.setContractNo(entity.getContractNo());
        param.setArchiverPerson(ProcessDataCollectConverter.account2Param(entity.getArchiverPerson()));
        param.setArchiveTime(entity.getArchiveTime());
        param.setArchiveType(entity.getArchiveType());
        return param;
    }


}

package com.timevale.contractmanager.core.service.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.CustomeFieldSysDO;
import com.timevale.contractmanager.core.model.dto.request.grouping.standingbook.SetCustomeFieldRequest;
import com.timevale.contractmanager.core.model.dto.request.grouping.standingbook.SetFieldRequest;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.CustomListDTO;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.FieldDataSourceDTO;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.FieldSysDataSourceDTO;

import java.util.List;


/**
 * 自定义列表服务
 *
 * @author: xuanzhu
 * @since: 2019-10-30 16:43
 */
public interface CustomListService {

    /**
     * 获取字段下拉框数据
     *
     * @param tenantId 空间Oid
     * @param accountId 当前操作人Oid
     * @param fieldCode 字段code
     * @return 值列表
     */
    public FieldDataSourceDTO getFieldData(
            String tenantId, String accountId, String fieldCode);

    /**
     * 获取系统字段下拉数据
     *
     * @param tenantId 空间Oid
     * @param accountId 当前操作人Oid
     * @param type 字段类型
     * @return 值列表
     */
    public FieldSysDataSourceDTO getSysData(String tenantId, String accountId, Integer type);

    /**
     * 获取自定义表格配置列表
     *
     * @param tenantId 空间Oid
     * @param accountId 当前操作人Oid
     * @param menuId 菜单id，待归档的统一为-1，其他的为实际菜单id（目前统一为空即可）获取自定义表格配置列表menuId=-4表示智能合同
     * @return
     */
    public CustomListDTO query(String tenantId, String accountId, String menuId, String clientId, String formId, String operateId);

    /**
     * 设置自定义列表字段配置
     * @param tenantId 空间oid
     * @param request
     * @param operatorId
     * @return
     */
    public boolean setConfig(String tenantId,SetCustomeFieldRequest request, String operatorId,String clientId);

    /**
     * 设置字段
     * @param request
     * @param operateId
     * @param tenantId
     * @return
     */
    public boolean setField(SetFieldRequest request, String operateId, String tenantId, String clientId);

    /**
     * 增加字段下拉框数据及ES中流程的AI字段数据
     *
     * @param tenantId 空间Oid
     * @param processId 流程id
     * @param dataMap 需要增加的数据
     * @param ruleId 规则id
     * @param ruleTemplateId 台账规则模板id
     */
    //public void addFieldData(String tenantId, String processId, Map<String, Object> dataMap,String ruleId,String ruleTemplateId);

    /**
     * 获取查询类型
     * @param fieldType 字段词性类型
     * @return 查询类型
     */
    public Integer getSearchType(String fieldType);

    /**
     * 获取自定义表格配置列表
     *
     * @param tenantId 空间Oid
     * @param accountId 当前操作人Oid
     * @param menuId 菜单id，待归档的统一为-1，其他的为实际菜单id（目前统一为空即可）
     * @param ruleId 台账规则id
     * @return 自定义表格配置列表
     */
     CustomListDTO queryByRuleId(String tenantId, String accountId, String menuId,String ruleId, Integer ruleVersion);

    List<CustomeFieldSysDO> getCustomFieldSysListV2(Integer type);

    Boolean existFileLedgerCustomField(String tenantId, String formId,String operateId);

}

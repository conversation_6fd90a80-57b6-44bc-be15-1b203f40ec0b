package com.timevale.contractmanager.core.service.transfer;

import com.timevale.contractmanager.core.model.bo.transfer.SealTransferNodeCountBO;
import com.timevale.contractmanager.core.model.dto.response.transfer.TransferSealNodeCountResponse;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
public interface TransferSealApproveBizService{

    /**
     * 用印审批中间节点以及最终节点数据 1 用户维度 2 用印审批维度
     *
     * @param request 请求参数
     * @return 返回总数
     */
    TransferSealNodeCountResponse sealNodeCount(SealTransferNodeCountBO request);


}

package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @Author:jianyang
 * @since 2022-02-22 14:51
 */
@Data
public class GroupListSensorBean extends BaseAttributeSensorBean {
	private String listName;
	private Long numberOfProcess;
	private Long returnTime;
	private String processingResult;
	private Integer numberOfField;
	private String field;

	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData = super.sensorData();
		sensorData.put(LIST_NAME,sensorString(listName));
		sensorData.put(PROCESSING_RESULT, sensorString(processingResult));
		sensorData.put(RETURN_TIME, sensorString(returnTime));
		sensorData.put(NUMBER_OF_PROCESS,sensorString(numberOfProcess));
		sensorData.put(FIELD,sensorString(field));
		sensorData.put(NUMBER_OF_FIELD,sensorString(numberOfField));
		return sensorData;
	}
}

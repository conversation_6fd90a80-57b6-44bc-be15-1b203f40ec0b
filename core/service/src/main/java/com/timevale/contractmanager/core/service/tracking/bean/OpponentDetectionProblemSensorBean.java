package com.timevale.contractmanager.core.service.tracking.bean;

import com.google.common.collect.Maps;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Map;

import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.*;
import static com.timevale.contractmanager.core.service.tracking.bean.SensorConstants.sensorString;

/**
 * @Author:jianyang
 * @since 2021-09-27 10:40
 */
@Data
public class OpponentDetectionProblemSensorBean extends ToString {
	/**
	 * 企业名称
	 */
	private String orgName;

	/**
	 * 租户oid
	 */
	private String tenantId;

	/**
	 * 问题类型
	 */
	private Integer problemType;

	public Map<String, Object> sensorData() {
		Map<String, Object> sensorData = Maps.newHashMap();
		sensorData.put(ORG_NAME,sensorString(orgName));
		sensorData.put(TENANT_ID,sensorString(tenantId));
		sensorData.put(PROBLEM_TYPE,sensorString(problemType));
		return sensorData;
	}
}

package com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.impl;

import com.timevale.contractmanager.common.service.enums.opponent.detection.OpponentDetectionProblemEnum;
import com.timevale.contractmanager.common.service.integration.client.ContractAnalysisClient;
import com.timevale.contractmanager.core.model.bo.opponent.detection.DetectionChainBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionChainResultBO;
import com.timevale.contractmanager.core.service.component.opponent.detection.chain.handler.DetectionHandler;
import com.timevale.contractmanager.core.service.tracking.SensorService;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-12-30 11:32
 */
@Component
@Slf4j
public class LegalPersonDetectionHandler implements DetectionHandler {

	@Autowired
	private SensorService sensorService;

	public static final Integer DETECTION_NO = 7;
	public static final String DESC = "最新的工商信息显示法人为:%s，而在e签宝登记的法人为:%s";

	@Override
	public List<OpponentDetectionChainResultBO> handler(DetectionChainBO chainBO, List<OpponentDetectionChainResultBO> chainResults) {
		log.info("开始相对方检测(法人一致性检测) taskId:{} taskType:{} tenantGid:{} orgName:{}",
				chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
		if(StringUtils.isNotBlank(chainBO.getEntity().getLegalRepresentativeName())
				&& !chainBO.getEntity().getLegalRepresentativeName().equals("-")
				&& !chainBO.getEntity().getLegalRepresentativeName().equals(chainBO.getDetail().getLegalPersonName())){
			OpponentDetectionChainResultBO chainResultBO = new OpponentDetectionChainResultBO();
			chainResultBO.setOrgName(chainResultBO.getOrgName());
			String legalPersonName = String.format("%s%s%s","“", chainBO.getDetail().getLegalPersonName(),"”");
			String legalRepresentativeName = String.format("%s%s%s","“", chainBO.getEntity().getLegalRepresentativeName(),"”");
			chainResultBO.setProblemDesc(String.format(DESC, legalPersonName, legalRepresentativeName));
			chainResultBO.setRiskLevel(OpponentDetectionProblemEnum.LEGAL_PERSON_DISCORDANCE.getProblemRiskLevel());
			chainResultBO.setProblemNo(OpponentDetectionProblemEnum.LEGAL_PERSON_DISCORDANCE.getProblemNo());
			chainResultBO.setProblemNo(DETECTION_NO);
			chainResultBO.setSuggestDesc(OpponentDetectionProblemEnum.LEGAL_PERSON_DISCORDANCE.getSuggestDesc());
			chainResults.add(chainResultBO);
			log.info("相对方检测企业工商的法人和在e签宝的法人姓名不一致 taskId:{} taskType:{} tenantGid:{} orgName:{}",
					chainBO.getTaskId(), chainBO.getTaskType(), chainBO.getTenantGid(), chainBO.getOrgName());
			sensorService.opponentDetectionProblemReport(
					chainBO.getTenantGid(), chainBO.getOrgName(),
					OpponentDetectionProblemEnum.LEGAL_PERSON_DISCORDANCE.getProblemNo());
		}
		return chainResults;
	}

	@Override
	public boolean filter(List<Integer> items) {
		if(items.contains(DETECTION_NO)){
			return true;
		}
		return false;
	}
}

package com.timevale.contractmanager.core.service.contractapproval.param;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 校验是否合同流程关联的历史合同审批对应的审批人请求参数
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
public class CheckIsApproverOfHistoryApprovalsDTO extends ToString {
    /** 合同流程id */
    private String processId;
    /** 用户oid */
    private String accountId;
    /** 用户gid */
    private String accountGid;
}

package com.timevale.contractmanager.core.service.contractapproval;

import com.timevale.contractapproval.facade.dto.biz.contractprocess.BizContractProcessDTO;
import com.timevale.contractapproval.facade.output.BatchQueryApprovalBizFilesOutput;
import com.timevale.contractmanager.core.model.bo.process.ProcessTerminateDetailBO;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalFlowDetail;
import com.timevale.contractmanager.core.service.contractapproval.bean.ApprovalFlowInfo;
import com.timevale.contractmanager.core.service.contractapproval.param.*;
import com.timevale.contractmanager.core.service.contractapproval.result.StartContractApprovalCheckResult;
import com.timevale.contractmanager.core.service.contractapproval.result.VirtualStartApprovalFlowResult;
import com.timevale.contractmanager.core.service.processstart.bean.ProcessStartSignParam;

import java.util.List;

/**
 * 合同审批流程接口
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
public interface ContractApprovalService {

    /**
     * 判断是否可使用新合同审批模板
     *
     * @param param
     * @return
     */
    boolean useApprovalFlowTemplate(UseApprovalFlowTemplateDTO param);

    /**
     * 创建合同审批组
     * @param param
     */
    void createApprovalGroup(CreateContractApprovalGroupDTO param);

    /**
     * 发起合同审批
     *
     * @param param
     * @return
     */
    String startApprovalFlow(ProcessStartSignParam param);

    /**
     * 预发起合同审批
     *
     * @param param
     * @return
     */
    VirtualStartApprovalFlowResult virtualStartApprovalFlow(VirtualStartApprovalFlowDTO param);

    /**
     * 发起合同审批时审批模板校验, 并返回正确的合同审批模板id
     *
     * @param param
     * @return
     */
    StartContractApprovalCheckResult startApprovalFlowCheck(StartContractApprovalCheckDTO param);

    /**
     * 查询合同审批流程对应的发起签署参数， 有可能为空
     *
     * @param processId
     * @param approvalFlowId
     * @return
     */
    ProcessStartSignParam queryApprovalStartSignParam(String processId, String approvalFlowId);

    /**
     * 更新合同审批流程对应的发起签署参数
     *
     * @param param
     * @return
     */
    void updateApprovalStartSignParam(String approvalFlowId, ProcessStartSignParam param);

    /**
     * 删除合同审批流程对应的发起签署参数
     *
     * @param processId
     * @return
     */
    void removeApprovalStartSignParam(String processId, String approvalFlowId);

    /**
     * 获取合同审批业务数据
     * @param approvalFlowId
     * @return
     */
    BizContractProcessDTO getApprovalBizProcessData(String approvalFlowId);

    /**
     * 批量查询审批业务文件
     * @param approvalCodes
     * @param approvalIds
     * @return
     */
    BatchQueryApprovalBizFilesOutput batchQueryApprovalBizFiles(List<String> approvalCodes, List<Long> approvalIds);

    /**
     * 新版合同审批流的审批节点查询
     *
     * @param approvalFlowId
     * @return
     */
    ApprovalFlowInfo queryApprovalInfoByApprovalId(String approvalFlowId);

    /**
     * 新版合同审批流的审批节点查询， 只包含已流转到的审批节点信息
     *
     * @param approvalFlowId
     * @return
     */
    ApprovalFlowDetail queryApprovalDetailByApprovalId(String approvalFlowId);

    /**
     * 获取审批终止详情
     * @param approvalCode
     * @return
     */
    ProcessTerminateDetailBO getApprovalTerminateDetail(String approvalCode);

    /**
     * 新版合同审批流的所有审批节点查询， 包含未流转到的审批节点信息
     *
     * @param approvalFlowId
     * @return
     */
    ApprovalFlowDetail queryApprovalDetailWithLogByApprovalId(String approvalFlowId);

    /**
     * 校验是否合同流程关联的历史合同审批对应的审批人
     * @param param
     * @return
     */
    boolean checkIsApproverOfHistoryApprovals(CheckIsApproverOfHistoryApprovalsDTO param);

    /**
     * 获取合同审批详情地址
     *
     * @param param
     * @return
     */
    String getApprovalUrl(GetContractApprovalUrlDTO param);

    /**
     * 终止合同审批
     *
     * @param param
     * @return
     */
    void terminateApproval(TerminateContractApprovalDTO param);

    /**
     * 暂停合同审批
     *
     * @param param
     * @return
     */
    void pauseApproval(PauseContractApprovalDTO param);

    /**
     * 取消暂停合同审批
     *
     * @param param
     * @return
     */
    void cancelPauseApproval(CancelPauseContractApprovalDTO param);

    /**
     * 催办合同审批
     *
     * @param param
     * @return
     */
    void remindApproval(RemindContractApprovalDTO param);
}

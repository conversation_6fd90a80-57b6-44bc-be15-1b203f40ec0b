package com.timevale.contractmanager.core.service.contractprocess.processor.process;

import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.ProcessChangeTagEnum;
import com.timevale.contractmanager.core.service.mq.model.ProcessArchiveMsgEntity;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessGroupingDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessGroupingParam;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by tianlei on 2022/5/25
 */
@Slf4j
@Component
public class ProcessArchiveRemoveDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ContractProcessWriteClient contractProcessWriteClient;
    @Autowired
    private ContractProcessReadClient contractProcessReadClient;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.processManageTopicName, ProcessChangeTagEnum.PROCESS_ARCHIVE_REMOVE.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        ProcessArchiveMsgEntity entity = JsonUtils.json2pojo(data, ProcessArchiveMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();
        ProcessArchiveMsgEntity entity = (ProcessArchiveMsgEntity) collectContext.getData();

        String menuId = entity.getMenuId();
        String tenantId = entity.getTenantId();

        if (StringUtils.isEmpty(menuId) || StringUtils.isBlank(tenantId)) {
            log.info(LOG_PREFIX + "archive param miss");
            return;
        }

        List<ContractProcessGroupingDTO> existGroupingInfoList =
                Optional.ofNullable(contractProcessReadClient.getGroupingInfo(processId)).orElse(new ArrayList<>());

        Integer existIndex = null;
        ContractProcessGroupingDTO findProcessGroupingDTO = null;
        for (int i = 0; i < existGroupingInfoList.size(); i++) {
            ContractProcessGroupingDTO existProcessGroupingDTO = existGroupingInfoList.get(i);
            // 以 tenantId 作为 唯一标准
            if (Objects.equals(existProcessGroupingDTO.getSubjectId(), entity.getTenantId())) {
                findProcessGroupingDTO = existProcessGroupingDTO;
                existIndex = i;
            }
        }

        if (null == findProcessGroupingDTO || CollectionUtils.isEmpty(findProcessGroupingDTO.getMenuIdList())) {
            log.info(LOG_PREFIX + "cannot find exist");
            return;
        }

        if (findProcessGroupingDTO.getMenuIdList().removeIf(elm -> Objects.equals(elm, menuId))) {

            ContractProcessUpdateParam param = new ContractProcessUpdateParam();
            param.setProcessId(processId);

            List<ContractProcessGroupingParam> paramList =
                    ProcessDataCollectConverter.processGroupingDTO2ParamList(existGroupingInfoList);
            if (CollectionUtils.isNotEmpty(findProcessGroupingDTO.getMenuIdList())) {
                // 更新
                ContractProcessGroupingParam updateParam =
                        ProcessDataCollectConverter.processGroupingDTO2Param(findProcessGroupingDTO);

                // 替换数据
                paramList.set(existIndex, updateParam);
                param.setGroupingInfo(paramList);

            } else {
                // 移除
                paramList.remove(existIndex.intValue());
                param.setGroupingInfo(paramList);
            }

            // 更新数据
            param.setBizScene(ProcessDataCollectBizSceneConstants.PROCESS_ARCHIVE_REMOVE);
            contractProcessWriteClient.updateByProcessId(param);
        } else {
            log.info(LOG_PREFIX + "cannot find this menuId");
        }

    }

}

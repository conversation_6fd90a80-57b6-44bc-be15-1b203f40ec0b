package com.timevale.contractmanager.core.service.infocollect.impl;

import com.timevale.contractanalysis.facade.api.dto.infocollect.*;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfoCollectFormQueryRequest;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfoCollectQueryStartDataRequest;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfoCollectSaveRecordRequest;
import com.timevale.contractmanager.common.service.api.RpcProcessExcelService;
import com.timevale.contractmanager.common.service.enums.ParticipantStartType;
import com.timevale.contractmanager.common.service.enums.SourceEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.InfoCollectClient;
import com.timevale.contractmanager.common.service.model.FormDataModel;
import com.timevale.contractmanager.common.service.model.FormImportModel;
import com.timevale.contractmanager.common.service.model.ParticipantDataImportModel;
import com.timevale.contractmanager.common.service.result.FormImportResult;
import com.timevale.contractmanager.core.model.dto.request.infocollect.InfoCollectStartRequest;
import com.timevale.contractmanager.core.model.dto.request.infocollect.InfoCollectRecordRequest;
import com.timevale.contractmanager.core.model.dto.response.infocollect.InfoCollectStartResponse;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.enums.InfoCollectStartTypeEnum;
import com.timevale.contractmanager.core.service.infocollect.InfoCollectFormDataService;
import com.timevale.contractmanager.core.service.other.UserCenterService;
import com.timevale.contractmanager.core.service.process.FlowTemplateService;
import com.timevale.contractmanager.spi.enums.ExcelParticipantTypeEnum;
import com.timevale.doccooperation.service.model.Cooperationer;
import com.timevale.doccooperation.service.model.CooperationerInstance;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * InfoCollectFormDataServiceImpl
 *
 * <AUTHOR>
 * @since 2022/9/21 2:47 下午
 */
@Slf4j
@Service
public class InfoCollectFormDataServiceImpl implements InfoCollectFormDataService {

    @Autowired
    private InfoCollectClient infoCollectClient;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private RpcProcessExcelService rpcProcessExcelService;

    @Autowired
    private FlowTemplateService flowTemplateService;

    @Override
    public InfoCollectStartResponse start(String tenantOid, String operatorOid, InfoCollectStartRequest request) {
        List<InfoCollectSaveRecordRequest.Data> dataList = new ArrayList<>();
        List<InfoCollectDataIds> dataIdsList = new ArrayList<>();
        List<String> recordIdList = new ArrayList<>();
        for (InfoCollectRecordRequest recordRequest : request.getRecordList()) {
            InfoCollectSaveRecordRequest.Data data = new InfoCollectSaveRecordRequest.Data();
            data.setInfoCollectTaskOuterId(recordRequest.getInfoCollectTaskOuterId());
            data.setInfoCollectRecordOuterId(recordRequest.getInfoCollectRecordOuterId());
            dataList.add(data);
            recordIdList.add(recordRequest.getInfoCollectRecordOuterId());

            InfoCollectDataIds dataIds = new InfoCollectDataIds();
            dataIds.setInfoCollectRecordOuterId(recordRequest.getInfoCollectRecordOuterId());
            dataIds.setInfoCollectTaskOuterId(recordRequest.getInfoCollectTaskOuterId());
            dataIdsList.add(dataIds);
        }

        UserAccount tenant = userCenterService.getUserAccountBaseByOid(tenantOid);
        // 保存要发起的采集数据
        InfoCollectSaveRecordRequest saveRecordRequest = new InfoCollectSaveRecordRequest();
        saveRecordRequest.setFormId(request.getFormId());
        saveRecordRequest.setTenantGid(tenant.getAccountGid());
        saveRecordRequest.setTenantOid(tenant.getAccountOid());
        saveRecordRequest.setDataList(dataList);
        infoCollectClient.saveRecordWhenStartProcess(saveRecordRequest);

        // 查询关联数据 —— 参与方关联 + 其他关联
        InfoCollectQueryStartDataRequest startDataRequest = new InfoCollectQueryStartDataRequest();
        startDataRequest.setFormId(request.getFormId());
        startDataRequest.setTemplateId(request.getTemplateId());
        startDataRequest.setTenantOid(tenantOid);
        startDataRequest.setRecordIds(dataIdsList);
        startDataRequest.setOperatorOid(operatorOid);
        startDataRequest.setUpgrade(request.getUpgrade());
        InfoCollectQueryStartDataResultDTO startDataResultDTO = infoCollectClient.queryStartData(startDataRequest);
        if (!startDataResultDTO.isSuccess()) {
            InfoCollectStartResponse response = new InfoCollectStartResponse();
            response.setSuccess(false);
            response.setErrorMsg(startDataResultDTO.getErrorMsg());
            return response;
        }

        if(StringUtils.isNotBlank(request.getTemplateId())){
            GetFlowTemplateResult flowTemplateDetail = flowTemplateService.getFlowTemplateDetail(request.getTemplateId());
            checkTemplateExistNotSupportSet(flowTemplateDetail);
        }

        // 转换参与方为键：信息采集记录id，值：参与方信息导入列表
        List<InfoCollectParticipantWithDataDTO> participantData = Optional.ofNullable(startDataResultDTO.getParticipantData())
                .orElse(new ArrayList<>());

        Map<String, List<ParticipantDataImportModel>> recordOuterIdDataMap = convertParticipantData(participantData);

        List<FormDataModel> formDataList = new ArrayList<>();
        for (InfoCollectFormResultDTO resultDTO : startDataResultDTO.getTemplateStructData()) {
            FormDataModel formDataModel = new FormDataModel();
            formDataList.add(formDataModel);

            // 采集数据Id
            formDataModel.setFormDataId(resultDTO.getRecordId());
            List<FormDataModel.StructData> structList = new ArrayList<>();
            for (InfoCollectFormStructDTO structDTO : resultDTO.getStructDataList()) {
                FormDataModel.StructData structData = new FormDataModel.StructData();
                structData.setId(structDTO.getStructId());
                structData.setData(structDTO.getData());
                structList.add(structData);
            }
            formDataModel.setStructList(structList);

            // 参与方数据
            List<ParticipantDataImportModel> oneRecordParticipantData = recordOuterIdDataMap.get(resultDTO.getRecordId());
            if (CollectionUtils.isNotEmpty(oneRecordParticipantData)) {
                formDataModel.setParticipantDataList(oneRecordParticipantData);
                Map<String, ParticipantDataImportModel> participantLabelDataMap = oneRecordParticipantData.stream()
                        .collect(Collectors.toMap(ParticipantDataImportModel::getParticipantLabel, Function.identity(), (v1, v2) -> v2));
                formDataModel.setParticipantLabelDataMap(participantLabelDataMap);
            }
        }
        FormImportModel model = new FormImportModel();
        model.setClientId(SourceEnum.WEB.getCode());
        model.setFlowTemplateId(request.getTemplateId());
        model.setTenantId(tenantOid);
        Integer participantSubjectType = getParticipantSubjectType(startDataResultDTO.getParticipantData());
        String participanttId = null;
        if (ExcelParticipantTypeEnum.MULTIAGENT.getType() != participantSubjectType) {
            // 不是多对多
            model.setParticipantLabel(participantData.get(0).getParticipantLabel());
            participanttId = participantData.get(0).getParticipantId();
        }
        model.setParticipantSubjectType(participantSubjectType);
        model.setFormDataList(formDataList);
        FormImportResult importResult = rpcProcessExcelService.importFormData(model);

        InfoCollectStartResponse response = new InfoCollectStartResponse();
        response.setRequireId(importResult.getRequireId());
        response.setStartType(ExcelParticipantTypeEnum.MULTIAGENT.getType() == participantSubjectType ?
                InfoCollectStartTypeEnum.MORE_2_MORE.getCode() : InfoCollectStartTypeEnum.ONE_2_MORE.getCode());
        response.setParticipantSubjectType(participantSubjectType);
        response.setSuccess(true);
        response.setParticipantLabel(model.getParticipantLabel());
        response.setParticipantId(participanttId);
        return response;
    }


    private void checkTemplateExistNotSupportSet(GetFlowTemplateResult flowTemplateDetail) {
        List<Cooperationer> cooperationers = flowTemplateDetail.getCooperationers();
        if (CollectionUtils.isEmpty(cooperationers)) {
            return;
        }
        for (Cooperationer cooperationer : cooperationers) {
            if (cooperationer.getExt() != null && (ParticipantStartType.PARTICIPANT_ASSIGN_ORG.getType().equals(cooperationer.getExt().getParticipantStartType()) ||
                    ParticipantStartType.PARTICIPANT_ASSIGN_ROLE.getType().equals(cooperationer.getExt().getParticipantStartType()))) {
                throw new BizContractManagerException(BizContractManagerResultCodeEnum.FLOW_TEMPLATE_INFO_COLLECT_FAIL_BY_NOT_SUPPORT_TEMPLATE);
            }
            if (CollectionUtils.isEmpty(cooperationer.getCooperationerInstances())) {
                continue;
            }
            for (CooperationerInstance instance : cooperationer.getCooperationerInstances()) {
                if (StringUtils.isNotBlank(instance.getAccountLicense())) {
                    throw new BizContractManagerException(BizContractManagerResultCodeEnum.FLOW_TEMPLATE_INFO_COLLECT_FAIL_BY_NOT_SUPPORT_TEMPLATE);
                }
            }
        }
    }

    /**
     * 获取参与方类型
     */
    private Integer getParticipantSubjectType(List<InfoCollectParticipantWithDataDTO> participantData) {
        if (participantData.size() >= 2) {
            return ExcelParticipantTypeEnum.MULTIAGENT.getType();
        }
        return ExcelParticipantTypeEnum.ORGANIZATION.getType() == Integer.parseInt(participantData.get(0).getParticipantSubjectType()) ?
                ExcelParticipantTypeEnum.ORGANIZATION.getType() : ExcelParticipantTypeEnum.PERSON.getType();
    }

    private Map<String, List<ParticipantDataImportModel>> convertParticipantData(List<InfoCollectParticipantWithDataDTO> participantData) {
        Map<String, List<ParticipantDataImportModel>> recordOuterIdDataMap = new HashMap<>();
        for (InfoCollectParticipantWithDataDTO participantWithDataDTO : participantData) {
            if (CollectionUtils.isEmpty(participantWithDataDTO.getData())) {
                continue;
            }
            for (InfoCollectParticipantWithDataDTO.Data data : participantWithDataDTO.getData()) {
                ParticipantDataImportModel participantDataModel = new ParticipantDataImportModel();
                participantDataModel.setParticipantId(participantWithDataDTO.getParticipantId());
                participantDataModel.setParticipantLabel(participantWithDataDTO.getParticipantLabel());
                participantDataModel.setParticipantSubjectType(participantWithDataDTO.getParticipantSubjectType());
                participantDataModel.setName(data.getName());
                participantDataModel.setContract(data.getContract());
                participantDataModel.setSubjectName(data.getSubjectName());
                recordOuterIdDataMap.computeIfAbsent(data.getInfoCollectRecordOuterId(), key -> new ArrayList<>())
                        .add(participantDataModel);
            }
        }
        return recordOuterIdDataMap;
    }
}

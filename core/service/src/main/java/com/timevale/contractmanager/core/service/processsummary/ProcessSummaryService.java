package com.timevale.contractmanager.core.service.processsummary;

import com.timevale.contractmanager.common.service.enums.ProcessSummaryOverAllJobStatusEnum;
import com.timevale.contractmanager.core.model.bo.processsummary.ProcessSummaryDetailBO;
import com.timevale.contractmanager.common.service.enums.ProcessSummaryDataTypeEnum;
import com.timevale.contractmanager.core.model.bo.processsummary.UpdateProcessSummaryBO;

/**
 * 合同摘要服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface ProcessSummaryService {

    /**
     * 给合同内指定文件生成合同摘要
     * @param processId
     * @param subjectOid
     * @param subjectGid
     */
    void createProcessSummary(String processId, String fileId, String subjectOid, String subjectGid);

    /**
     * 给合同内所有文件生成合同摘要
     * @param processId
     * @param subjectOid
     * @param subjectGid
     */
    void createProcessAllFileSummary(String processId, String subjectOid, String subjectGid);

    /**
     * 查询合同摘要详情, 先从subjectGid获取数据，如果为空，则从defaultSubjectGid获取数据
     *
     * @param processId
     * @param fileId
     * @param subjectGid
     * @return
     */
    ProcessSummaryDetailBO queryProcessSummary(String processId, String fileId, String subjectGid);

    /**
     * 重新生成合同摘要
     *
     * @param processId
     * @param fileId
     * @param subjectGid
     */
    void refreshProcessSummary(
            String processId,
            String fileId,
            String subjectOid,
            String subjectGid,
            ProcessSummaryDataTypeEnum dataType);

    /**
     * 获取合同下摘要整体状态
     * @param processId
     * @param subjectGid
     * @return
     */
    ProcessSummaryOverAllJobStatusEnum queryProcessSummaryOverAllStatus(
            String processId, String subjectGid);

    /**
     * 更新合同摘要
     * @param bo
     */
    void updateProcessSummary(UpdateProcessSummaryBO bo);

    /**
     * 更新新增合同摘要结果
     * @param id
     * @param jobStatus
     * @param result
     */
    boolean updateProcessSummaryResultWithCreate(Long id, String jobStatus, Object result);

    /**
     * 更新重新发起合同摘要结果
     * @param id
     * @param jobStatus
     * @param result
     */
    boolean updateProcessSummaryResultWithRefresh(Long id, String jobStatus, Object result);
}

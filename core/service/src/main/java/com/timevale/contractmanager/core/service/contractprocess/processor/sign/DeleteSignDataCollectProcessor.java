package com.timevale.contractmanager.core.service.contractprocess.processor.sign;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessReadClient;
import com.timevale.contractmanager.common.service.integration.client.ContractProcessWriteClient;
import com.timevale.contractmanager.core.service.contractprocess.DataAnalysisResult;
import com.timevale.contractmanager.core.service.contractprocess.HbaseProcessDataAsyncCollectException;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectBizSceneConstants;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectConfigCenter;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectContext;
import com.timevale.contractmanager.core.service.contractprocess.ProcessDataCollectSupport;
import com.timevale.contractmanager.core.service.contractprocess.Route;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataBuilder;
import com.timevale.contractmanager.core.service.contractprocess.builddata.ProcessDataCollectConverter;
import com.timevale.contractmanager.core.service.contractprocess.processor.ProcessDataCollectProcessor;
import com.timevale.contractmanager.core.service.enums.SignChangeTagEnum;
import com.timevale.contractmanager.core.service.lock.Lock;
import com.timevale.contractmanager.core.service.lock.LockService;
import com.timevale.contractmanager.core.service.mq.model.SignChangeMsgEntity;
import com.timevale.contractmanager.core.service.util.TaskIdUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.signflow.search.service.request.datacollect.ContractProcessUpdateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.SUB_PROCESS_NOT_EXIST;

/**
 * Created by tianlei on 2022/5/11
 */
@Slf4j
@Component
public class DeleteSignDataCollectProcessor implements ProcessDataCollectProcessor {

    @Autowired
    private ProcessDataCollectConfigCenter dataCollectConfigCenter;
    @Autowired
    private ProcessDataBuilder processDataBuilder;
    @Autowired
    private ContractProcessWriteClient processWriteClient;
    @Autowired
    private ContractProcessReadClient processQueryClient;
    @Autowired
    private LockService lockService;

    @Override
    public Route route() {
        return Route.of(dataCollectConfigCenter.signTopicName(), SignChangeTagEnum.DELETE_SIGN.getTag());
    }

    @Override
    public DataAnalysisResult dataAnalysis(String data) {
        SignChangeMsgEntity entity =
                JsonUtils.json2pojo(data, SignChangeMsgEntity.class);
        return new DataAnalysisResult(entity.getProcessId(), entity);
    }

    @Override
    public boolean processDataExistContinueProcess() {
        return true;
    }

    @Override
    public boolean initProcessDataAfterContinueProcess() {
        return true;
    }

    @Override
    public void process(ProcessDataCollectContext collectContext) {
        Lock lock = lockService.getLock(ProcessDataCollectSupport.taskInfoChangeLockKey(collectContext.getProcessId()));
        if (lock.tryLock(200, TimeUnit.MILLISECONDS)) {
            try {
                doProcess(collectContext);
            } finally {
                lock.unlock();
            }
        } else {
            // 抛异常重试
            throw new HbaseProcessDataAsyncCollectException();
        }
    }


    private void doProcess(ProcessDataCollectContext collectContext) {
        String processId = collectContext.getProcessId();

        ContractProcessDTO contractProcessDTO = processQueryClient.getByProcessId(processId);

        List<ContractProcessSignTaskDTO> oldSignTaskDTOList = contractProcessDTO.getSignTasks();
        if (CollectionUtils.isEmpty(oldSignTaskDTOList)) {
            // 消息乱序 该消息先与 sign_start 到
            log.info(ProcessDataCollectSupport.LOG_PREFIX + "signTasks is null skip");
            return;
        }
        SignChangeMsgEntity signChangeMsgEntity = (SignChangeMsgEntity) collectContext.getData();

        // 看下是否是构造的假数据
        boolean falseData = StringUtils.isBlank(oldSignTaskDTOList.get(0).getTaskId());
        if (falseData) {
            // 说明是提前构造的假签署任务，消息先与 signStart 到了，丢弃等signStart来到在进行构造
            log.info(ProcessDataCollectSupport.LOG_PREFIX + "signTaskChange before signStart");
            return;
        }

        ContractProcessSignTaskDTO newSignTaskDTO = null;
        try {
            newSignTaskDTO = processDataBuilder.buildUpdateSignTask(signChangeMsgEntity);
        } catch ( BizContractManagerException bizContractManagerException) {
            if (!bizContractManagerException.getCode().equals(SUB_PROCESS_NOT_EXIST.getCode())) {
                throw bizContractManagerException;
            }
        }

        if (null != newSignTaskDTO) {
            // 删除了还存在， 是因为1个人有2个签署区，只是删除了其中一个签署区

            // 更新状态
            log.info(LOG_PREFIX + "data not real delete processId : {}", processId);

            boolean findExistData = false;
            Integer existIndex = null;
            for (int i = 0; i < oldSignTaskDTOList.size(); i++) {
                if (Objects.equals(oldSignTaskDTOList.get(i).getTaskId(), newSignTaskDTO.getTaskId())) {
                    // id相同记性替换
                    findExistData = true;
                    existIndex = i;
                    break;
                }
            }

            if (findExistData) {
                // 保留转交
                newSignTaskDTO.setTransfer(oldSignTaskDTOList.get(existIndex).getTransfer());
                oldSignTaskDTOList.set(existIndex, newSignTaskDTO);

                ContractProcessUpdateParam input = new ContractProcessUpdateParam();
                input.setProcessId(processId);
                input.setSignTasks(ProcessDataCollectConverter.signTaskDTO2ParamList(oldSignTaskDTOList));
                input.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_DELETE_SIGN);
                processWriteClient.updateByProcessId(input);

            } else {
                log.info(LOG_PREFIX + "deleteSign data not exist processId : {}", processId);
            }
            return;
        }

        // 数据彻底删除了
        String oldTaskId = TaskIdUtil.generateTaskId(signChangeMsgEntity.getFlowId(), signChangeMsgEntity.getSignerAccountId(),
                signChangeMsgEntity.getSignerAuthorizedAccountId(), signChangeMsgEntity.getOrder());


        boolean findExistData = false;
        Integer existIndex = null;
        for (int i = 0; i < oldSignTaskDTOList.size(); i++) {
            if (Objects.equals(oldSignTaskDTOList.get(i).getTaskId(), oldTaskId)) {
                // id相同记性替换
                findExistData = true;
                existIndex = i;
                break;
            }
        }

        if (!findExistData) {
            // 数据不存在
            log.info(ProcessDataCollectSupport.LOG_PREFIX + "deleteSign data task not exist processId : {} taskId : {}",
                    processId, oldTaskId);
            return;
        }

        // 根据下标移除数据， 下标必须是int  不能是Integer
        oldSignTaskDTOList.remove(existIndex.intValue());

        // 数据更新
        ContractProcessUpdateParam input = new ContractProcessUpdateParam();
        input.setProcessId(processId);
        input.setBizScene(ProcessDataCollectBizSceneConstants.SIGN_DELETE_SIGN);
        input.setSignTasks(ProcessDataCollectConverter.signTaskDTO2ParamList(oldSignTaskDTOList));
        processWriteClient.updateByProcessId(input);


    }
}

package com.timevale.contractmanager.core.service.watermark.impl;

import com.timevale.contractmanager.common.service.enums.WatermarkBizTypeEnum;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.service.watermark.WatermarkStrategy;
import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkModel;
import com.timevale.contractmanager.core.service.watermark.bean.AddWatermarkResult;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootModel;
import com.timevale.contractmanager.core.service.watermark.bean.GenerateWatermarkSnapShootResult;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkContentTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.watermark.WatermarkTypeEnum;
import com.timevale.saas.common.manage.common.service.model.input.watermark.GenerateWatermarkSnapShootInput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: contract-manager-project
 * @Description: 屏幕水印策略类
 * @date Date : 2023年04月23日 11:45
 */
@Slf4j
@Service
public class ScreenWatermarkStrategy implements WatermarkStrategy {

    @Autowired SaasCommonClient saasCommonClient;

    @Override
    public String getType() {
        return WatermarkStrategy.assembleServiceId(WatermarkTypeEnum.SCREEN_WATERMARK, WatermarkContentTypeEnum.NORMAL_WORD);
    }

    @Override
    public GenerateWatermarkSnapShootResult generateWatermarkSnapShoot(GenerateWatermarkSnapShootModel model) {

        GenerateWatermarkSnapShootInput snapShootInput = new GenerateWatermarkSnapShootInput();
        snapShootInput.setWatermarkTemplateId(model.getWatermarkId());
        snapShootInput.setBizType(WatermarkBizTypeEnum.CONTRACT_PROCESS.getBizType());
        snapShootInput.setBizId(model.getProcessId());
        snapShootInput.setOid(model.getTenantId());
        snapShootInput.setOperatorOid(model.getOperatorId());
        String watermarkSnapShootId = saasCommonClient.generateWatermarkSnapShoot(snapShootInput);
        Map<String, String> fileIdAndWatermarkSSIdMap = model.getFiles().stream().collect(Collectors.toMap(FileBO::getFileId, fileBO -> watermarkSnapShootId));

        GenerateWatermarkSnapShootResult result = new GenerateWatermarkSnapShootResult();
        result.setFileIdAndWatermarkSSIdMap(fileIdAndWatermarkSSIdMap);
        return result;
    }

    @Override
    public AddWatermarkResult addWatermark(AddWatermarkModel model) {
        //屏幕水印不添加到文件上
        AddWatermarkResult result = new AddWatermarkResult();
        result.setScreenWatermark(true);
        result.setScreenWatermarkId(model.getFiles().get(0).getWatermarkId());
        return result;
    }
}

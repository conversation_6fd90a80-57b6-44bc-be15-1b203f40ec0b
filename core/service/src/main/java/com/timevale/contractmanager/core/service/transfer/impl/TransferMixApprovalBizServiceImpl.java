package com.timevale.contractmanager.core.service.transfer.impl;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.TRANSFER_BIZ_SERVICE_NON_EXIST;

import com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum;
import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.model.bo.transfer.SingleTransferResultBO;
import com.timevale.contractmanager.core.model.bo.transfer.TransferUserListBO;
import com.timevale.contractmanager.core.model.dto.response.saasorg.OrgDeptListResponse;
import com.timevale.contractmanager.core.model.dto.transfer.TransferResultDTO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.transfer.TransferAbstractBizService;
import com.timevale.contractmanager.core.service.transfer.TransferBizService;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.doccooperation.service.util.LambdaUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 审批流程转交，包含用印和合同流程的转交
 *
 * <AUTHOR>
 * @since 2023-04-12 13:39
 */
@Slf4j
@Service
public class TransferMixApprovalBizServiceImpl extends TransferAbstractBizService
        implements TransferBizService {
    @Autowired private TransferSealApproveBizServiceImpl transferSealApproveBizService;

    @Autowired private TransferContractApproveBizServiceImpl transferContractApproveBizService;

    @Override
    public Boolean isSupport(Integer transferScene) {
        return Objects.equals(TransferSceneEnum.MIX_APPROVE.getCode(), transferScene);
    }

    @Override
    public TransferResultDTO transfer(TransferBizContext transferBizContext) {
        TransferBizContext.MixTransferApprovalInfo mixInfo =
                transferBizContext.getMixTransferApprovalInfo();
        if (Objects.isNull(mixInfo) || CollectionUtils.isEmpty(mixInfo.getTransferApprovalList())) {
            return new TransferResultDTO(false);
        }
        // 按用印、合同审批分组
        Map<String, List<TransferBizContext.TransferApproval>> approvalMap =
                LambdaUtil.groupingToMap(
                        mixInfo.getTransferApprovalList(),
                        TransferBizContext.TransferApproval::getApprovalType);
        List<TransferBizContext.TransferApproval> sealApprovals =
                approvalMap.getOrDefault(
                        ApprovalTemplateTypeEnum.SEAL.getCode(), Collections.emptyList());
        List<TransferBizContext.TransferApproval> processApprovals =
                approvalMap.getOrDefault(
                        ApprovalTemplateTypeEnum.CONTRACT.getCode(), Collections.emptyList());
        // 如果多个审批流程批量转交， 则使用任务中心
        if (mixInfo.getTransferApprovalList().size() > 1) {
            transferBizContext.setUseTaskCenter(true);
        }

        // 按审批类型分别转交
        TransferResultDTO sealTransferResult = transferSealApproval(transferBizContext, sealApprovals);
        TransferResultDTO contractTransferResult =partTransferProcessApproval(transferBizContext, processApprovals);
        return new TransferResultDTO(sealTransferResult.isGoToTaskCenter() || contractTransferResult.isGoToTaskCenter());
    }

    @Override
    public Map<String, Long> transferCount(
            UserAccountDetail tenantAccount, List<Account> userAccounts) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }

    @Override
    public OrgDeptListResponse transferUserList(TransferUserListBO transferUserListBO) {
        return transferSealApproveBizService.transferUserList(transferUserListBO);
    }

    @Override
    public Integer transferToUserCount(String tenantId, String accountOid) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }

    @Override
    public Long addTransferToUserCount(String tenantId, String accountOid) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }

    @Override
    public void singleTransferResultCallback(SingleTransferResultBO transferResultBO) {
        throw new BizContractManagerException(TRANSFER_BIZ_SERVICE_NON_EXIST);
    }

    /** 用印转交，调用原用印转交 */
    private TransferResultDTO transferSealApproval(
            TransferBizContext transferBizContext,
            List<TransferBizContext.TransferApproval> sealApprovals) {
        if (CollectionUtils.isEmpty(sealApprovals)) {
            return new TransferResultDTO(false);
        }

        // 构建用印审批转交参数
        TransferBizContext sealTransferBizContext =
                JsonUtils.json2pojo(
                        JsonUtils.obj2json(transferBizContext), TransferBizContext.class);
        TransferBizContext.TransferProcessListInfo sealTransInfo =
                new TransferBizContext.TransferProcessListInfo();
        sealTransInfo.setTransferAccountOid(
                transferBizContext.getMixTransferApprovalInfo().getTransferAccountOid());
        sealTransInfo.setTransferProcessList(
                sealApprovals.stream()
                        .map(TransferBizContext.TransferApproval::getApprovalId)
                        .collect(Collectors.toList()));
        sealTransferBizContext.setTransferProcessListInfo(sealTransInfo);
        // 用印转交
        return transferSealApproveBizService.transfer(sealTransferBizContext);
    }

    /** 批量合同转交 */
    private TransferResultDTO partTransferProcessApproval(
            TransferBizContext transferBizContext,
            List<TransferBizContext.TransferApproval> processApprovals) {
        if (CollectionUtils.isEmpty(processApprovals)) {
            return new TransferResultDTO(false);
        }
        transferBizContext.setTransferContractApprovalInfo(
                new TransferBizContext.TransferApprovalInfo(
                        transferBizContext.getMixTransferApprovalInfo().getTransferAccountOid(),
                        processApprovals.stream()
                                .map(TransferBizContext.TransferApproval::getApprovalId)
                                .collect(Collectors.toList())));
        return transferContractApproveBizService.transfer(transferBizContext);
    }
}

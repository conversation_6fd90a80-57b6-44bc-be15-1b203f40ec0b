package com.timevale.contractmanager.core.service.grouping;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.contractmanager.common.service.integration.client.DocCooperationClient;
import com.timevale.contractmanager.common.service.integration.client.SaasCommonClient;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.common.service.enums.ParticipantSubjectType;
import com.timevale.contractmanager.core.service.enums.SealTypeEnum;
import com.timevale.contractmanager.core.service.util.IdsUtil;
import com.timevale.contractmanager.core.service.util.RequestContextExtUtils;
import com.timevale.doccooperation.service.enums.CooperationerRoleEnum;
import com.timevale.doccooperation.service.input.BaseFlowTemplateInput;
import com.timevale.doccooperation.service.model.CooperationerStruct;
import com.timevale.doccooperation.service.result.FlowTemplateStructResult;
import com.timevale.docmanager.service.model.StructComponent;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.manage.common.service.model.output.VipFunctionQueryOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 模板相关服务
 *
 * <AUTHOR>
 * @since 2020-09-02 16:27
 **/
@Slf4j
@Service
public class TemplateServiceImpl implements TemplateService {

    @Autowired private SaasCommonClient saasCommonClient;
    @Autowired private DocCooperationClient docCooperationClient;

    @Override
    public FlowTemplateStructResult getSelectedStructList(
            UserAccount tenantAccount,
            String appId,
            String flowTemplateId,
            Set<Integer> selectedStructTypeSet) {
        BaseFlowTemplateInput baseFlowTemplateInput = new BaseFlowTemplateInput();
        baseFlowTemplateInput.setFlowTemplateId(flowTemplateId);
        baseFlowTemplateInput.setAppId(appId);
        baseFlowTemplateInput.setGid(tenantAccount.getAccountGid());
        baseFlowTemplateInput.setOid(tenantAccount.getAccountOid());
        baseFlowTemplateInput.setUid(tenantAccount.getAccountUid());
        FlowTemplateStructResult flowTemplateStructResult =
                docCooperationClient.getFlowTemplateStructs(baseFlowTemplateInput);

        if (flowTemplateStructResult == null) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.FLOW_TEMPLATE_NOT_EXIST);
        }
        // 没有指定控件类型则返回所有控件
        if (CollectionUtils.isEmpty(selectedStructTypeSet)) {
            return flowTemplateStructResult;
        }

        // 处理公共控件
        Map<String, List<StructComponent>> commonStructMap = flowTemplateStructResult.getCoomonStructs();
        if (MapUtils.isNotEmpty(commonStructMap)) {
            commonStructMap = filterStructMap(commonStructMap, selectedStructTypeSet);
            flowTemplateStructResult.setCoomonStructs(commonStructMap);
        }

        // 处理协作方控件
        List<CooperationerStruct> cooperationerStructList =
                flowTemplateStructResult.getCooperationerStructs();
        if (CollectionUtils.isNotEmpty(cooperationerStructList)) {
            for (CooperationerStruct cooperationerStruct : cooperationerStructList) {
                if (cooperationerStruct == null
                        || MapUtils.isEmpty(cooperationerStruct.getStructs())) {
                    continue;
                }
                Map<String, List<StructComponent>> cooperationerStructMap = cooperationerStruct.getStructs();
                if (MapUtils.isNotEmpty(cooperationerStructMap)) {
                    cooperationerStructMap =
                            filterStructMap(cooperationerStructMap, selectedStructTypeSet);
                    cooperationerStruct.setStructs(cooperationerStructMap);
                }
            }
        }

        return flowTemplateStructResult;
    }

    /**
     * 根据控件类型过滤控件map
     * @param structMap 要过滤的控件map(key:模板id, value:控件list)
     * @param selectedStructTypeSet 保留的控件类型
     * @return 过滤后的控件map
     */
    private Map<String, List<StructComponent>> filterStructMap(
            Map<String, List<StructComponent>> structMap, Set<Integer> selectedStructTypeSet) {
        Iterator<String> keyIterator = structMap.keySet().iterator();
        Iterator<StructComponent> iterator = null;
        StructComponent struct = null;
        String key = null;
        List<StructComponent> structList = null;
        while (keyIterator.hasNext()) {
            key = keyIterator.next();
            // 控件列表
            structList = structMap.get(key);
            if (CollectionUtils.isEmpty(structList)) {
                continue;
            }
            iterator = structList.iterator();
            while (iterator.hasNext()) {
                struct = iterator.next();
                // 控件类型不在范围之内则移除
                if (!selectedStructTypeSet.contains(struct.getType())) {
                    iterator.remove();
                }
            }
            // 如果过滤后控件列表为空, 则同时移除该模板
            if (CollectionUtils.isEmpty(structList)) {
                keyIterator.remove();
            }
        }
        return structMap;
    }

    @Override
    public boolean isAIDrawUsed(List<ParticipantBO> participants) {
        boolean flag = false;
        if (CollectionUtils.isEmpty(participants)) {
            return flag;
        }
        Set<String> idSet = null;
        for (ParticipantBO participant : participants) {
            // 参与方角色类型
            idSet = IdsUtil.getIdSet(participant.getRole());
            // 参与方操作类型是签署, 且角色是个人才需要校验权限
            if (!idSet.contains(CooperationerRoleEnum.SIGNER.getRole().toString())
                    || participant.getParticipantSubjectType() == null
                    || participant.getParticipantSubjectType()
                            != ParticipantSubjectType.PSN.getType()) {
                continue;
            }

            // 手绘章类型id
            idSet = IdsUtil.getIdSet(participant.getSealType());
            if (idSet.contains(SealTypeEnum.AI_HAND.getType())) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    @Override
    public boolean isAdvancedComponentUsed(
            UserAccount tenantUserAccount, String appId, String flowTemplateId, String clientId) {

        VipFunctionQueryOutput functionInfo =
                saasCommonClient.queryVipFunctionInfo(
                        tenantUserAccount.getAccountOid(), FunctionCodeConstant.TEMPLATE_ADVANCED_COMPONENT, clientId);
        if (null == functionInfo || MapUtils.isEmpty(functionInfo.getLimit())) {
            return false;
        }
        // 获取配置的高级控件
        String advancedComponents =
                (String) functionInfo.getLimit().getOrDefault("advanced_component_types", "");
        Set<Integer> structTypeSet = IdsUtil.getIntegerSet(advancedComponents);

        FlowTemplateStructResult flowTemplateStructResult =
                getSelectedStructList(
                        tenantUserAccount,
                        RequestContextExtUtils.getAppId(),
                        flowTemplateId,
                        structTypeSet);

        boolean useAdvancedComponentFlag = false;
        // 判断公共控件中是否包含高级控件
        if (MapUtils.isNotEmpty(flowTemplateStructResult.getCoomonStructs())){
            useAdvancedComponentFlag = true;
        } else {
            // 协作方控件是否包含高级控件
            List<CooperationerStruct> cooperationerStructList =
                    flowTemplateStructResult.getCooperationerStructs();
            if (CollectionUtils.isNotEmpty(cooperationerStructList)) {
                for (CooperationerStruct cooperationerStruct : cooperationerStructList) {
                    if (MapUtils.isNotEmpty(cooperationerStruct.getStructs())) {
                        useAdvancedComponentFlag = true;
                        break;
                    }
                }
            }
        }
        return useAdvancedComponentFlag;
    }
}

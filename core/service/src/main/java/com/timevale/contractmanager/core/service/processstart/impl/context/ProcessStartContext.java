package com.timevale.contractmanager.core.service.processstart.impl.context;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.dto.process.config.ProcessStartBizRuleConfig;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.process.rule.modifier.ProcessStartDataModifier;
import com.timevale.doccooperation.service.result.GetFlowTemplateResult;
import com.timevale.saas.common.manage.common.service.model.output.bean.VipFunction;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.PROCESS_ILLEGAL_PARAM;

@Getter
public class ProcessStartContext {

    @Setter
    private String clientId;

    @Setter
    private String appId;

    @Setter
    private String appName;

    /** 操作人账号信息 */
    private UserAccountDetail operator;
    /** 发起人账号信息 */
    private UserAccountDetail account;
    /** 发起主体账号信息 */
    private UserAccountDetail tenant;

    private UserAccountDetail spaceTenant;
    /** 会员功能上下文， 用于会员功能校验 */
    private VipFunctionBizContext functionContext;

    /**
     * 合同偏好设置
     */
    private Map<String, String> preferenceData = new HashMap<>();

    /**
     * 模版详情，未来如果出现初始加载的数据过多，可使用惰性查询加载
     */
    @Setter
    @Getter
    private GetFlowTemplateResult flowTemplateDetail = null;

    /**
     * 业务规则
     */
    @Getter
    @Setter
    private ProcessStartBizRuleConfig startBizRuleConfig;

    /**
     * 数据修改器，实现该接口适配不同场景
     */
    @Setter
    @Getter
    private ProcessStartDataModifier startDataModifier;

    @Setter
    @Getter
    private List<ParticipantBO> participants;

    public ProcessStartContext(
            UserAccountDetail account, UserAccountDetail tenant, UserAccountDetail spaceTenant, Map<String, VipFunction> vipFunctionMap, Map<String, String> preferenceData) {
        if (null == account) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "发起人账号信息不能为空");
        }
        // 设置发起人账号信息
        this.account = account;
        if (null == tenant) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "发起主体账号信息不能为空");
        }
        // 设置发起主体账号信息
        this.tenant = tenant;
        if (null == spaceTenant) {
            this.spaceTenant = tenant;
        } else {
            this.spaceTenant = spaceTenant;
        }

        if (null == vipFunctionMap) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "会员功能获取参数缺失");
        }
        // 设置会员功能上下文
        this.functionContext = new VipFunctionBizContext(vipFunctionMap);
        this.preferenceData = preferenceData;
    }

    public UserAccountDetail getInitiatorAccount() {
        return account;
    }

    public UserAccountDetail getSubjectAccount() {
        return tenant;
    }

    public void setOperatorAccount(UserAccountDetail operator) {
        this.operator = operator;
    }

    public UserAccountDetail getOperatorAccount() {
        return null == operator ? account : operator;
    }
}

package com.timevale.contractmanager.core.service.transfer;

import static com.timevale.contractmanager.common.utils.config.Constants.*;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.core.model.dto.transfer.SystemTransferEvent;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.service.process.PreferencesService;
import com.timevale.contractmanager.core.service.transfer.factory.TransferBizServiceFactory;
import com.timevale.contractmanager.core.service.transfer.impl.context.TransferBizContext;
import com.timevale.contractmanager.core.service.util.TransferUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/26 系统转交事件处理
 */
@Slf4j
@Service
public class SystemTransferEventHandler {
    @Autowired private TransferBizServiceFactory transferBizServiceFactory;
    @Autowired private PreferencesService preferencesService;

    @Order(1)
    @EventListener(classes = {SystemTransferEvent.class})
    public void processTransfer(SystemTransferEvent transferEvent) {
        systemTransfer(TransferSceneEnum.PROCESS, transferEvent);
    }

    @Order(2)
    @EventListener(classes = {SystemTransferEvent.class})
    public void sealApprovalTransfer(SystemTransferEvent transferEvent) {
        systemTransfer(TransferSceneEnum.SEAL_APPROVE, transferEvent);
    }

    @Order(3)
    @EventListener(classes = {SystemTransferEvent.class})
    public void contractApprovalTransfer(SystemTransferEvent transferEvent) {
        systemTransfer(TransferSceneEnum.CONTRACT_APPROVE, transferEvent);
    }

    @Order(4)
    @EventListener(classes = {SystemTransferEvent.class})
    public void receiverTransfer(SystemTransferEvent transferEvent) {
        UserAccountDetail orgAccount =
                transferEvent
                        .getAccountDetailMap()
                        .get(transferEvent.getSystemTransferBO().getTenantId());
        UserAccountDetail originalAccount =
                transferEvent
                        .getAccountDetailMap()
                        .get(transferEvent.getSystemTransferBO().getTransferAccountOid());
        UserAccountDetail transferToAccount =
                transferEvent.getAccountDetailMap().get(transferEvent.getDefaultAdminOid());
        if (orgAccount == null || originalAccount == null || transferToAccount == null) {
            log.warn(
                    "systemTransfer UserAccountDetail is null orgAccount {} originalAccount {} transferToAccount{}",
                    orgAccount,
                    originalAccount,
                    transferToAccount);
            return;
        }
        // 接收人的转交
        preferencesService.transferReceiver(
                orgAccount.getAccountGid(), originalAccount, transferToAccount);
    }

    private void systemTransfer(TransferSceneEnum scene, SystemTransferEvent transferEvent) {
        TransferBizContext transferBizContext = new TransferBizContext();
        transferBizContext.setTaskId(TransferUtils.generateSystemTaskId());
        TransferBizContext.TransferUserInfo transferUserInfo =
                generateSystemTransferUserInfo(scene, transferEvent);
        transferBizContext.setTransferUserInfo(transferUserInfo);

        if (transferUserInfo == null
                || transferUserInfo.checkAnyBaseAccountIsNull()
                || CollectionUtils.isEmpty(transferUserInfo.getTransferAccount())) {
            log.warn(
                    "systemTransfer UserAccountDetail is null userInfo {}",
                    JSON.toJSONString(transferUserInfo));
            return;
        }
        transferBizServiceFactory.getService(scene.getCode()).transfer(transferBizContext);
    }

    private TransferBizContext.TransferUserInfo generateSystemTransferUserInfo(
            TransferSceneEnum transferScene, SystemTransferEvent systemTransferEvent) {
        TransferBizContext.TransferUserInfo transferUserInfo =
                new TransferBizContext.TransferUserInfo();
        transferUserInfo.setTenantAccount(
                systemTransferEvent
                        .getAccountDetailMap()
                        .get(systemTransferEvent.getSystemTransferBO().getTenantId()));
        transferUserInfo.setTransferAccount(
                Lists.newArrayList(
                        systemTransferEvent
                                .getAccountDetailMap()
                                .get(
                                        systemTransferEvent
                                                .getSystemTransferBO()
                                                .getTransferAccountOid())));
        UserAccountDetail transferToAccount =
                getUserAccountDetailFromScene(
                        transferScene,
                        systemTransferEvent.getSystemTransferToUserOidMap(),
                        systemTransferEvent.getAccountDetailMap());
        transferToAccount =
                transferToAccount == null
                        ? systemTransferEvent
                                .getAccountDetailMap()
                                .get(systemTransferEvent.getDefaultAdminOid())
                        : transferToAccount;
        transferUserInfo.setTransferToAccount(transferToAccount);
        // 系统转交默认没有转交人，目前兼容一些后续流程，设置操作人为被转交人
        transferUserInfo.setOperatorAccount(transferToAccount);
        return transferUserInfo;
    }

    /**
     * 根据转交场景获取用户详情
     *
     * @return
     */
    private UserAccountDetail getUserAccountDetailFromScene(
            TransferSceneEnum transferScene,
            Map<String, String> systemTransferToUserOidMap,
            Map<String, UserAccountDetail> accountDetailMap) {
        String accountKey = null;
        switch (transferScene) {
            case PROCESS:
                accountKey = SYSTEM_PROCESS_TRANSFER_CONFIG_KEY;
                break;
            case SEAL_APPROVE:
                accountKey = SYSTEM_SEAL_APPROVAL_TRANSFER_CONFIG_KEY;
                break;
            case CONTRACT_APPROVE:
                accountKey = SYSTEM_CONTRACT_APPROVAL_TRANSFER_CONFIG_KEY;
                break;
        }
        if (accountKey == null) {
            return null;
        }
        String transferToOid = systemTransferToUserOidMap.get(accountKey);
        if (StringUtils.isBlank(transferToOid)) {
            return null;
        }
        return accountDetailMap.get(transferToOid);
    }
}

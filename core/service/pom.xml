<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.timevale.contractmanager</groupId>
        <artifactId>contractmanager-parent</artifactId>
        <version>1.0.1</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>contractmanager-core-service</artifactId>
    <name>contractmanager/service</name>
    <packaging>jar</packaging>

    <dependencies>
        <!--对接异步下载文件-->
        <dependency>
            <groupId>com.timevale.threadPoolManager</groupId>
            <artifactId>threadPoolManager-facade</artifactId>
            <version>1.1.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>contractmanager-core-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.esign</groupId>
            <artifactId>component-simple-encrypt</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.10</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
            <version>1.13.10.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.6</version>
        </dependency>
        <!--对象映射转换-->
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>1.5.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.timevale.notificationmanager</groupId>
            <artifactId>notificationmanager-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>third-platform-integrate-facade</artifactId>
                    <groupId>com.timevale.tpi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 插件化 -->
        <dependency>
            <groupId>com.timevale.framework.pluginify</groupId>
            <artifactId>launcher-support-spring</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.saas-common-manage</groupId>
            <artifactId>saas-common-manage-spi-extender</artifactId>
            <version>1.0.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.framework.pluginify</groupId>
            <artifactId>galaxy-integration-mandarin</artifactId>
            <version>1.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>cat-toolkit-trace</artifactId>
        </dependency>

        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>5.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.user.resource</groupId>
            <artifactId>user-resource-facade</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.account</groupId>
                    <artifactId>account-facade</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>contractmanager-spi-extender-invoker</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.23.4</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.billing.conan</groupId>
            <artifactId>conan-common-service-facade</artifactId>
            <version>${conan-common-service-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.billing.common</groupId>
            <artifactId>basicbs-billing-common-util</artifactId>
            <version>1.3.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.timevale.saas.common</groupId>
            <artifactId>all</artifactId>
            <version>1.0.13</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis.spring.boot</groupId>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.timevale.spring.boot</groupId>
                    <artifactId>sands-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.timevale.saas-auth-api</groupId>
            <artifactId>ding-spring-boot-starter</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.timevale.saas</groupId>
            <artifactId>common-exception-util</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.saas</groupId>
            <artifactId>common-util</artifactId>
            <version>1.0.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.saas</groupId>
            <artifactId>tracking-util</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>2.4.1</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.dayu</groupId>
            <artifactId>sdk</artifactId>
            <version>1.0.25-SNAPSHOT</version>
        </dependency>

        <!--  调用线程池传递traceId用  -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>8.0.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.saas-auth-api</groupId>
            <artifactId>ding-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.flash</groupId>
            <artifactId>flash-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.besp.lowcode.tripartite</groupId>
            <artifactId>tripartite-rpc-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.sterna.doctemplate</groupId>
            <artifactId>doctemplate-integrate-client</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.eapr</groupId>
                    <artifactId>eapr-app-springboot2-starter</artifactId>
                </exclusion>
                <!--   框架兼容性有问题，升级到2.12之后可以不排除             -->
                <exclusion>
                    <groupId>com.timevale.middleware</groupId>
                    <artifactId>unified-rpc-copy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.timevale.footstone-doc</groupId>
            <artifactId>footstone-doc-common-service-facade</artifactId>
            <version>2.0.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.saas</groupId>
            <artifactId>common-privilege-util</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.saas-common-manage</groupId>
                    <artifactId>saas-common-manage-common-service-facade</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>

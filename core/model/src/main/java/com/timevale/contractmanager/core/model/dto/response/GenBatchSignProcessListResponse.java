package com.timevale.contractmanager.core.model.dto.response;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.ProcessInfoDisplayInfo;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 获取批量签署可执行列表
 *
 * <AUTHOR>
 * @since 2019-08-29
 */
@Data
public class GenBatchSignProcessListResponse extends ToString {

    @ApiModelProperty("批量序列号id")
    private String batchSerialId;
    @ApiModelProperty("可指定位置批量签署列表数据")
    private List<ProcessInfoDisplayInfo> assignedBatchSignList = Lists.newArrayList();
    @ApiModelProperty("可非指定位置批量签署列表数据")
    private List<ProcessInfoDisplayInfo> unassignedBatchSignList = Lists.newArrayList();
    @ApiModelProperty("不可批量签署列表数据")
    private List<ProcessInfoDisplayInfo> notSupportBatchSignList = Lists.newArrayList();
}

package com.timevale.contractmanager.core.model.dto.request.grouping.menu;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 更新菜单操作请求
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class ModifyMenuRequest extends CommonRequest {

    @ApiModelProperty(value = "目录名称", required = true)
    @NotBlank(message = "name不能为空")
    @Length(max = 50, message = "名字长度不能大于50")
    @Pattern(regexp = "[^\\*\\:\\\"\\*\\?\\|\\\\\\/\\>\\<]+",message = "不能包含特殊字符:* : \" ? | \\ / > <")
    private String name;
}

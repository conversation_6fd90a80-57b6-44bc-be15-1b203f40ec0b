package com.timevale.contractmanager.core.model.dto.response.sharedownload;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/2/10 流程文档
 */
@Data
public class FlowDocumentVO {
    @ApiModelProperty(value = "文件id")
    private String fileId;

    @ApiModelProperty(value = "文件fileKey")
    private String fileKey;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件下载地址")
    private String fileUrl;
}

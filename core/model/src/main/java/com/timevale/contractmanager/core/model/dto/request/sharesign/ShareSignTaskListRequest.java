package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.contractmanager.common.service.enums.FlowQueryStatusEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 扫码签任务列表请求入参
 *
 * <AUTHOR>
 * @since 2021/02/05
 */
@ApiModel(description = "扫码签任务列表参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class ShareSignTaskListRequest extends ToString {

    @ApiModelProperty("当前登录账号id")
    @NotBlank(message = "用户账号id不能为空")
    private String accountId;

    @ApiModelProperty("页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @ApiModelProperty("页行数，最小1， 最大20")
    @Min(value = 1, message = "页行数不能小于1")
    @Max(value = 20, message = "页行数不能大于20")
    private Integer pageSize;

    @ApiModelProperty(value = "流程名称全模糊搜索", position = 1)
    private String taskName;

    @ApiModelProperty(value = "签署方名称/手机号/邮箱全模糊搜索", position = 1)
    private String signerKeyWord;

    /** @see FlowQueryStatusEnum */
    @ApiModelProperty(
            value =
                    "扫码流程状态过滤, 多个状态以逗号分隔, 1-待我签,2-待他人签,3-签署完成, 5-拒签,6-撤回,7-审批中,9-过期,18-待我填写, 19-待他人填写")
    private String processStatusList;
}

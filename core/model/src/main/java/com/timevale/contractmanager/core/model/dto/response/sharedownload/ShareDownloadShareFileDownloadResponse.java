package com.timevale.contractmanager.core.model.dto.response.sharedownload;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/10 分享下载合同的单个文件下载信息
 */
@ApiModel("分享下载合同的单个文件下载信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareDownloadShareFileDownloadResponse extends ToString {
    @ApiModelProperty(value = "文件的filKey")
    private String fileKey;

    @ApiModelProperty(value = "文件的下载地址")
    private String downloadUrl;

    @ApiModelProperty(value = "文件的名称")
    private String fileName;
}

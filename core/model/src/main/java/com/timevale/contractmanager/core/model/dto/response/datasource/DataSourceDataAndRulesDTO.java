package com.timevale.contractmanager.core.model.dto.response.datasource;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceDataAndRulesDTO {

    /** 数据源字段 注意，这里的字段类型是经过特殊处理的 */
    private List<DataSourceDataDTO> dataSourceDataList = Lists.newArrayList();
}

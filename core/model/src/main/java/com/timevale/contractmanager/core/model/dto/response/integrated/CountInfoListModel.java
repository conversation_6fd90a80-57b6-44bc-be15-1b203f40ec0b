package com.timevale.contractmanager.core.model.dto.response.integrated;

import com.timevale.signflow.search.docSearchService.bean.CountDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 融合统计信息
 *
 * <AUTHOR>
 * @since 2020-10-29 17:37
 */
@Data
public class CountInfoListModel {

    @ApiModelProperty(value = "统计列表")
    private List<CountDetail> countList;
}

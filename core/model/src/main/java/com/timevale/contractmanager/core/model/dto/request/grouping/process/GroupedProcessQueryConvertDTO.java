package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/6/21 10:06
 */
@Data
public class GroupedProcessQueryConvertDTO {

    private String tenantOid;

    private String operatorOid;

    /**
     * 搜索条件
     */
    private String matching;

    /**
     * @see com.timevale.contractmanager.common.service.enums.grouping.MenuIdEnum
     */
    private String menuId;

    /**
     * 是否查询指定分类的子分类，默认查询。为false是不查询
     */
    private Boolean querySubMenuProcess;

    private Integer pageSize;

    private Integer pageNum;
}

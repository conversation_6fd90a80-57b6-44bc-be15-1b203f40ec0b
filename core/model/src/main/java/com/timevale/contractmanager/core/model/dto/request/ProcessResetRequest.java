package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("合同流程重置请求参数")
public class ProcessResetRequest extends ToString {

    @ApiModelProperty("账号id")
    @NotNull
    private String accountId;

    @ApiModelProperty("主体/空间id")
    private String authorizedAccountId;
}

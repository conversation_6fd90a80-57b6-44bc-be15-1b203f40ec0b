package com.timevale.contractmanager.core.model.dto.request.autoArchive;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2021-05-14 21:05
 */
@Data
public class ArchiveRuleAccountIdRequest extends ToString {
	@ApiModelProperty(value = "accountOid集合")
	@NotEmpty
	private List<String> accountId;
}

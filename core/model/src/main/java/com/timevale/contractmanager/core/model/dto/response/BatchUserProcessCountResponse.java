package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.dto.user.UserProcessCountDTO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("查询批量用户合同数量响应")
public class BatchUserProcessCountResponse extends ToString {
    @ApiModelProperty("批量用户的流程数量")
    private List<UserProcessCountDTO> usersProcessCount;
}

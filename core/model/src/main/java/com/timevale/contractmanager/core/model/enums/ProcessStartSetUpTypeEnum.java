package com.timevale.contractmanager.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/1/29 10:10
 */
@Getter
public enum ProcessStartSetUpTypeEnum {

    PROCESS_COMPARE("processCompare", "合同比对", 1020),

    FEI_SHU_DATA_SOURCE("feiShuDataSource", "飞书数据源", 1030),

    KINGDEE_DATA_SOURCE("kingDeeDataSource", "金蝶数据源", 1040),


    DEDICATED_CLOUD("dedicatedCloud", "专属云", 1010),

    SIGN_MODE("signMode", "签署模式", 1000),

    // 签署文件更多设置 -  
    SIGN_FILE_SETTING("signFileSetting", "更多设置", 990),

    // 允许发起时添加文件 -  
    FLOW_TEMPLATE_START_ADD_FILE("flowTemplateStartAddFile", "允许发起时添加文件", 980),

    // 模版关联审批模版 -
    FLOW_TEMPLATE_RELATION_APPROVAL_TEMPLATE("flowTemplateRelationApprovalTemplate", "审批流程", 960),

    // 发起时选择审批模版 -  
    START_SELECT_APPROVAL_TEMPLATE("startSelectApprovalTemplate", "审批流程", 970),


    // 关联合同 -  
    RELATED_CONTRACT("relatedContract", "关联合同", 950),

    // 添加水印  -  
    ADD_WATERMARK("addWatermark", "添加水印", 940),

    // 合同保密  -  
    PROCESS_SECRET("processSecret", "合同保密", 930),

    //签订截止日期 -  
    SIGNING_DEADLINE("signingDeadline", "签订截止日期", 920),

    //合同到期日期 -  
    CONTRACT_EXPIRATION("contractExpiration", "合同到期日期", 910),

    //合同抄送 -  
    CONTRACT_COPY("contractCopy", "合同抄送", 900),

    //合同附件 -  
    ATTACHMENT("attachment", "合同附件", 890),

    //添加个人签署方 -
    ADD_PERSON_PARTICIPANT("addPersonParticipant", "添加个人签署方", 880),

    EPAAS_TEMPLATE_USER_GRAY("saas_graft_epaas", "epaas流程模板用户灰度", 870),

    //模版数据源
    FLOW_TEMPLATE_DATA_SOURCE("dataSource", "模板自动填写", 860),

    //模版数据源
    SHARE_SCAN_SIGN("shareScanSign", "参与方扫码加入", 850),

    // 设置是否允许解约
    ALLOW_INITIATOR_RESCIND("allowInitiatorRescind","发起签署时可设置是否允许解约",840),

    // 定时发起
    SCHEDULED_INITIATE("scheduledInitiate","定时发起", 830),

    FORCE_LICENSE("forceLicense", "指定证件号发起签署", 820),

    PARTICIPANT_START_TYPE("participantStartType", "签署方设置方式", 810),

    PROCESS_REMARK("process_remark", "合同备注", 800),
    DIRECT_START_CONTRACT_NO_RULES("directStartContractNoRules", "直接发起合同编号规则", 790),

    //fda签署
    FDA_SIGNATURE("fda_signature", " fda签署", 780),
    ;

    private String code;
    private String description;
    private Integer order;

    ProcessStartSetUpTypeEnum(String code, String description, Integer order) {
        this.code = code;
        this.description = description;
        this.order = order;
    }
}

package com.timevale.contractmanager.core.model.dto.response.processdownload;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/14
 */
@Data
public class ProcessDownloadCheckCanDownloadResponse extends ToString {

    private List<ProcessCanDownload> resultList;

    /**
     * 合同下载带章偏好设置
     */
    private String signingDownloadType;

    public ProcessDownloadCheckCanDownloadResponse(List<ProcessCanDownload> resultList) {
        this.resultList = resultList;
    }

    public ProcessDownloadCheckCanDownloadResponse(List<ProcessCanDownload> resultList,String signingDownloadType) {
        this.resultList = resultList;
        this.signingDownloadType = signingDownloadType;
    }

    @Data
    public static class ProcessCanDownload {

        private String processId;


        /**
         * true 可下载  false不可下载
         */
        private Boolean canDownload;

        /**
         * 不能下载的原因
         */
        private Integer cannotDownloadReason;

        /**
         * true 已签署完后，false 未签署完成
         */
        private Boolean done;

    }
}

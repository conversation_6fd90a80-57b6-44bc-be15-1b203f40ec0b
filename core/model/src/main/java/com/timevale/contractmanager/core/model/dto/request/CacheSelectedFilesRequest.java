package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("缓存选择的文件列表请求参数")
public class CacheSelectedFilesRequest extends ToString {
    @ApiModelProperty(value = "文件id列表")
    @NotEmpty(message = "文件id列表不能为空")
    @Size(max = 50, message = "单次选择不能超过50个文件")
    private List<String> fileIds;
}

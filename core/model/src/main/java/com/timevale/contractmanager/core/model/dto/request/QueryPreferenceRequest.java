package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.annotation.EnumValidList;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonIgnore;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-19 19:25
 **/
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class QueryPreferenceRequest extends ToString {
    /**
     * 组织oid
     */
    @ApiModelProperty(value = "oID")
    @NotBlank(message = "OID不能为空")
    private String orgId;

    /** 组织gid */
    @ApiModelProperty(value = "GID", hidden = true)
    @JsonIgnore
    private String orgGid;

    /**
     * 偏好keys(用于查询)
     */
    @ApiModelProperty(value = "偏好项Key键列表")
    @EnumValidList(message = "存在无法识别的配置类型！", target = ProcessPreferenceEnum.class)
    private List<String> preferenceKeys;

    public QueryPreferenceRequest(String orgId, List<String> preferenceKeys) {
        this.orgId = orgId;
        this.preferenceKeys = preferenceKeys;
    }

    public QueryPreferenceRequest(String orgId, String orgGid, List<String> preferenceKeys) {
        this.orgId = orgId;
        this.orgGid = orgGid;
        this.preferenceKeys = preferenceKeys;
    }

    private String clientId;

    private String appName;
}

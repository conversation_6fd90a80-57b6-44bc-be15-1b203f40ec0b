package com.timevale.contractmanager.core.model.dto.response.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-23 10:00
 */
@Data
public class OpponentEnterpriseInfoResponse extends ToString {

	@ApiModelProperty("企业名称")
	private String name;


	@ApiModelProperty("社会信用统一代码")
	private String creditCode;

	@ApiModelProperty("企业注册号")
	private String regNo;


	@ApiModelProperty("经营状态,1:存续,2:在业,3:停业,4:迁入,5:迁出,6:清算,7:注销,8:吊销,9:其他,99:未知")
	private String status;


	@ApiModelProperty("注册资本")
	private String registerCapital;


	@ApiModelProperty("法定代表人姓名")
	private String legalPersonName;


	@ApiModelProperty("经营范围")
	private String businessScope;


	@ApiModelProperty("经营开始日期")
	private String businessStartDate;


	@ApiModelProperty("经营结束日期")
	private String businessEndDate;


	@ApiModelProperty("历史名称")
	private List<String> historyNameList;


	@ApiModelProperty("注册地址")
	private String address;


	@ApiModelProperty("登记机关")
	private String regInstitute;


	@ApiModelProperty("成立日期")
	private String establishDate;


	@ApiModelProperty("注销日期")
	private String cancellationDate;


	@ApiModelProperty("核准日期")
	private String checkDate;


	@ApiModelProperty("吊销原因")
	private String revokeReason;


	@ApiModelProperty("吊销日期")
	private String revokeDate;


	@ApiModelProperty("1:大陆企业,2:社会组织,3:机关及事业单位,4:港澳台及国外企业,5:律所及其他组织机构,6:基金会,9:新机构,99:未知")
	private String entityType;


	@ApiModelProperty("区域编码")
	private String areaCode;
}

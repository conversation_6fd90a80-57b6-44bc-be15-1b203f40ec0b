package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import lombok.Data;

import java.util.List;

/**
 * 批量流程归档内部请求参数
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BatchProcessGroupingInnerRequest {
    /** 操作人oid */
    private String operatorOid;
    /** 操作人gid */
    private String operatorGid;
    /** 主体oid */
    private String subjectOid;
    /** 主体gid */
    private String subjectGid;
    /** 菜单id */
    private String menuId;
    /** 流程id列表 */
    private List<String> processIdList;
}

package com.timevale.contractmanager.core.model.dto.response.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Author:jiany<PERSON>
 * @since 2021-03-10 15:01
 */
@Data
@Builder
public class OpponentBatchGetBlackListResponse extends ToString {
	@ApiModelProperty("企业名称或个人账号")
	private String entityName;
	@ApiModelProperty("实体类型(1-企业,0-个人)")
	private Integer entityType;
}

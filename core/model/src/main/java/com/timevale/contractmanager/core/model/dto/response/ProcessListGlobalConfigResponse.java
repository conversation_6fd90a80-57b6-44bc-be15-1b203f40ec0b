package com.timevale.contractmanager.core.model.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取合同流程列表全局操作权限响应数据
 *
 * <AUTHOR>
 * @date 2021/11/02
 **/
@Data
public class ProcessListGlobalConfigResponse {

    @ApiModelProperty(value = "是否拥有设置合同保密权限")
    private boolean canConfigSecret = false;

    @ApiModelProperty(value = "跳转链接box 如钉钉智能合同跳转链接需要用到这个box")
    private String thirdJumpBox;

    @ApiModelProperty(value = "是否可选带表单的流程明细导出")
    private boolean canExportWithForm = false;
}

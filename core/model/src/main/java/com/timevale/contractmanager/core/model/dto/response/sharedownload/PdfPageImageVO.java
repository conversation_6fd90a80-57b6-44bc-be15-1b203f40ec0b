package com.timevale.contractmanager.core.model.dto.response.sharedownload;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/2/11 pdf转图片页面信息
 */
@Data
public class PdfPageImageVO {
    @ApiModelProperty(value = "pdf转图片的下载地址")
    private String imageUrl;

    @ApiModelProperty(value = "转换结果对应文档页")
    private int pageNo;

    @ApiModelProperty(value = "文档宽度")
    private float pageWidth;

    @ApiModelProperty(value = "文档高度")
    private float pageHeight;
}

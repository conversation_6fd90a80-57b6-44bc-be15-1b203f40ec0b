package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 校验流程模板是否可用于续签
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
@Data
@ApiModel
public class CheckTemplateRenewableRequest extends ToString {

    @ApiModelProperty("流程模板id")
    @NotBlank(message = "流程模板id不能为空")
    private String flowTemplateId;
}

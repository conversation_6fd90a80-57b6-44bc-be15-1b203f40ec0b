package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.bean.ValidityConfigBean;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 修改到期时间请求参数
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
@Data
@ApiModel
public class UpdateValidityRequest extends ToString {

    @ApiModelProperty("流程id列表")
    @NotEmpty(message = "流程id列表不能为空")
    @Size(max = 100, message = "流程id列表不能查过100")
    private List<String> processIds;

    @ApiModelProperty("指定到期时间")
    private Long validityDate;

    @ApiModelProperty("到期时间配置")
    @NotNull(message = "到期时间配置不能为空")
    private ValidityConfigBean validityConfig;

    @ApiModelProperty("菜单id，null-经办合同, -1-企业合同待归档, MENU_ALL-企业合同已归档全部, {uuid}-企业合同具体分类")
    private String menuId;
}

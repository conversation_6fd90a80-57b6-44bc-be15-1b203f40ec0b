package com.timevale.contractmanager.core.model.dto.response.grouping.standingbook;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 文件解析控件属性
 * @author: jinhuan
 * @since: 2020-04-17 11:54
 **/
@Data
public class StructDTO extends ToString {

    @ApiModelProperty(value = "字段的属性：姓名，电话等")
    private String type;

    @ApiModelProperty(value = "所选内容,提取的字段内容")
    private String content;

    @ApiModelProperty(value = "词性")
    private String property;

    @ApiModelProperty(value = "需要高亮的文字坐标")
    private List<HighLightCoordinate> highLightCoordinateList;

    @ApiModelProperty(value = "控件id")
    private String structId;

    @ApiModelProperty(value = "控件类型 1为模板控件 2为ai识别控件")
    private Integer structType;

    @ApiModelProperty(value = "扩展字段类型 1-文本 2-数字 3-时间")
    private Integer fieldType;

    /** 需要高亮显示的目标坐标 */
    @Data
    public static class HighLightCoordinate {
        @ApiModelProperty(value = "和左边界距离，单位point")
        private double x;

        @ApiModelProperty(value = "和下边界的距离，单位point")
        private double y;

        @ApiModelProperty(value = "向上的高度，单位point")
        private double h;

        @ApiModelProperty(value = "向右的宽度，单位point")
        private double w;

        @ApiModelProperty(value = "当前页码")
        private int pageNum;
    }

}

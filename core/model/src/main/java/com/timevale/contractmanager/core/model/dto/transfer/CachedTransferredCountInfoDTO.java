package com.timevale.contractmanager.core.model.dto.transfer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/5/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("被转交人收到的缓存合同数量信息")
public class CachedTransferredCountInfoDTO {
    @ApiModelProperty("企业oid")
    private String orgOid;

    @ApiModelProperty("转交合同数据")
    private Integer transferContractCount = 0;

    @ApiModelProperty("待处理的合同数量--已经废弃")
    @Deprecated
    private Integer untreatedContractCount = 0;

    @ApiModelProperty("用印转交数")
    private Integer transferSealApproveCount = 0;
}

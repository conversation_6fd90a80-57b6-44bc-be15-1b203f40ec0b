package com.timevale.contractmanager.core.model.dto.request.grouping.menu;

import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 *
 * <AUTHOR>
 * @since 2023-09-26 11:18
 */
@Data
@ApiModel(value = "子分类排序请求对象")
public class ChildMenuSortRequest extends ToString {

    @ApiModelProperty(value = "子分类排序后的id列表")
    @NotEmpty(message = "排序后的子分类id列表不能为空")
    private List<String> menuIds;

    @ApiModelProperty(value = "该子分类对应的父分类id")
    private String parentMenuId;

    @ApiModelProperty(value = "用户oid", hidden = true)
    private String accountOid;

    @ApiModelProperty(value = "企业oid", hidden = true)
    private String subjectOid;
}

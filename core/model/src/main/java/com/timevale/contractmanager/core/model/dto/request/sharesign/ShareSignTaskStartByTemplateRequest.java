package com.timevale.contractmanager.core.model.dto.request.sharesign;


import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import javax.validation.constraints.NotNull;

/**
 * 扫码签通过模板发起任务
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@ApiModel(description = "扫码签模板发起入参")
public class ShareSignTaskStartByTemplateRequest extends ToString {

    @ApiModelProperty(value = "流程模板id")
    @NotNull(message = "流程模板id不能为空")
    private String flowTemplateId;

    /** 请参考{@link com.timevale.contractmanager.core.model.enums.ProcessStartScene } */
    @NotNull(message = "scene不能为空")
    private Integer scene;

    @ApiModelProperty("是否需要进行印章授权校验")
    private boolean needSealAuthCheck;

}

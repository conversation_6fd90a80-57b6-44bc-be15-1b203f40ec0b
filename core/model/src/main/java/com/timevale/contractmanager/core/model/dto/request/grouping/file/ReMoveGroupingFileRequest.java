package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 移出合同文件操作请求
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class ReMoveGroupingFileRequest extends CommonRequest {

    /** 移出后需要将该菜单ID在processId下移除-ES操作 */
    @ApiModelProperty(value = "被移出的菜单ID", required = true)
    @NotNull(message = "targetMenuId不能为空")
    private String targetMenuId;

    @ApiModelProperty(value = "流程列表")
    @Size(max = 100, message = "最多100个流程")
    private List<String> processIds;
}

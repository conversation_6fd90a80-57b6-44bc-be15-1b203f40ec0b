package com.timevale.contractmanager.core.model.dto.response.sharedownload;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/10 分享下载的分享信息
 */
@ApiModel("分享下载的分享信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareDownloadShareInfoResponse extends ToString {
    @ApiModelProperty(value = "新分享地址 newShareEnable为true并且shareDownloadPrivilege为true时返回")
    private String newShareUrl;

    @ApiModelProperty(value = "是否启用新分享功能 合同状态非签署完成也返回false")
    private Boolean newShareEnable;

    @ApiModelProperty(
            value =
                    "是否有分享下载权限 newShareEnable=true时返回true或false，newShareEnable=false时为null，合同状态非签署完成返回null")
    private Boolean shareDownloadPrivilege;
}

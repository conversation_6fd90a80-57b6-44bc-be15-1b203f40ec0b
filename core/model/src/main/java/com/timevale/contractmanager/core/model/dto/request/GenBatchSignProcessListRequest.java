package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 获取批量签署地址
 *
 * <AUTHOR>
 * @since 2019/7/31
 */
@Data
public class GenBatchSignProcessListRequest extends ToString {

    @ApiModelProperty(value = "流程id集合")
    @Size(max = 100, message = "批量处理最大支持选择100个流程")
    private List<String> processIds;

    @ApiModelProperty(value = "操作人账号id", required = true)
    @NotBlank(message = "accountId不能为空")
    private String accountId;

    @ApiModelProperty(value = "操作人所属主体id", required = true)
    @NotBlank(message = "subjectId不能为空")
    private String subjectId;

    @ApiModelProperty("批量签署序列id")
    private String batchSerialId;

    @ApiModelProperty("批量签署版本，1-老版， 2-新版， 默认老版")
    @Min(value = 1, message = "批量签署版本最小为1")
    @Max(value = 2, message = "批量签署版本目前最大为2")
    private Integer version;
    
    @ApiModelProperty("流程组id，换取batchSerialId时 processIds与processGroupId至少一个非空")
    private String processGroupId;
}

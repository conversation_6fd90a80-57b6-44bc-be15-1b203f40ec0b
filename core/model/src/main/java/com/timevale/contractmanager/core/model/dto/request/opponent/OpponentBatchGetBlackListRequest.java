package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.contractmanager.common.service.model.opponent.data.OrgNameContactData;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-03-09 15:15
 */
@Data
public class OpponentBatchGetBlackListRequest extends ToString {
	@ApiModelProperty("entityUniqueIds")
	@NotEmpty(message = "entityUniqueIds不能为空")
	@Valid
	private List<OrgNameContactData> entityUniqueIds;
}

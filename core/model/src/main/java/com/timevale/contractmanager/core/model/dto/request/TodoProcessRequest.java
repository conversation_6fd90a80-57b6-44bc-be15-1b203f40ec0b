package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.core.model.enums.TodoTypeEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 合同待办列表请求条件
 *
 * <AUTHOR>
 * @since 2021-11-24 16:27
 */
@Data
public class TodoProcessRequest extends ToString {
    @NotNull(message = "subjectId不能为空")
    @ApiModelProperty(value = "查询的主体", required = true)
    private String subjectId;

    @ApiModelProperty(value = "流程开始时间,单位毫秒")
    private Long beginTimeInMillSec;

    @ApiModelProperty(value = "流程完成时间,单位毫秒")
    private Long endTimeInMillSec;

    @ApiModelProperty(value = "fuzzyMatching")
    private String fuzzyMatching;
    /** @see TodoTypeEnum */
    @NotNull(message = "type不能为空")
    @ApiModelProperty(value = "待办类型: 1.待我签署 2.待我填写", required = true)
    private Integer type;

    @Min(value = 1, message = "页码不能小于1")
    @ApiModelProperty(value = "页码", required = true)
    private Integer pageNum;

    @Max(value = 100, message = "每页数据不能超过100行")
    @ApiModelProperty(value = "每页行数", required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;
}

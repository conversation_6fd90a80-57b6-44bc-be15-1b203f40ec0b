package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.contractmanager.core.model.dto.request.ProcessStartRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫码签请求入参
 *
 * <AUTHOR>
 * @since 2021/04/15
 */
@ApiModel(description = "扫码签发起入参")
@EqualsAndHashCode(callSuper = true)
@Data
public class ShareSignTaskStartRequest extends ProcessStartRequest {

    /**
     * 是否扫码签模板发起流程接口
     */
    private Boolean shareSignStartByFlowTemplateId;

    /**
     * 是否扫码签指定位置直接发起
     */
    private Boolean shareSignDirectStartByFlowTemplateId;

    /** 是否允许多次签署 */
    private Boolean repeatSign = false;

    @ApiModelProperty(value = "clientId")
    private String clientId;

    private String subjectOid;

    private String operatorOid;

    private String appId;

    private String appName;

}

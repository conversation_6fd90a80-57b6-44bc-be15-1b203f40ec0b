package com.timevale.contractmanager.core.model.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/1 15:54
 */
@Data
public class MultiCloudDownloadHaveDataResponse {


    @ApiModelProperty("专属云是否有可下载流程")
    private Boolean dedicatedCloudHaveDownloadData;

    @ApiModelProperty("公有云是否有可下载流程")
    private Boolean publicCloudHaveDownloadData;
    
    @ApiModelProperty("合同下载带章偏好设置")
    private String signingDownloadType;

}

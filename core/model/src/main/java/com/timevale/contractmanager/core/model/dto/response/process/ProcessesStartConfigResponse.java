package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.contractmanager.core.model.bo.StartSuccessCacheBO;
import com.timevale.contractmanager.core.model.dto.process.start.BaseStartDisplayRuleDTO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/26 10:19
 */
@Data
public class ProcessesStartConfigResponse extends ToString {

    private List<BaseStartDisplayRuleDTO> displayRules;

    @ApiModelProperty(value = "上次发起的数据")
    private StartSuccessCacheBO lastStartData;

    @ApiModelProperty(value = "发起序列号")
    private String startSeqNo;

}


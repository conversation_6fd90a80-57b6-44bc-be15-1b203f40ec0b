package com.timevale.contractmanager.core.model.dto.response.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: hui<PERSON>
 * @since: 2021-08-25 14:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OpponentDetectionTaskReportExportResponse extends ToString {
    @ApiModelProperty("后续操作类型 1-跳转到{operationUrl} 2-下载{operationUrl}")
    private Integer operationType;

    @ApiModelProperty("operationType为1时:跳转地址url;为1时检测结果下载链接")
    private String operationUrl;
}

package com.timevale.contractmanager.core.model.dto.response.saasorg;

import com.timevale.contractmanager.core.model.dto.user.OrgMemberBaseDTO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-11-28 11:34
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgDeptListResponse extends ToString {

    @ApiModelProperty(value = "成员列表")
    private List<OrgMemberBaseDTO> memberList;

    @ApiModelProperty(value = "成员总数量")
    private Integer memberCount;

}

package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.Draft;
import com.timevale.mandarin.common.result.BaseResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <h1>功能说明:草稿操作入参 </h1> <br/>
 * <h2><AUTHOR> <h2><br/>
 * <h2>@date 2019/8/1 2:29 PM <h2><br/>
 * <h2>修改记录 2019/8/1 2:29 PM : initial </h2> <br/>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("草稿查询结果")
public class DraftQueryResponse extends BaseResult {

    @ApiModelProperty("统计")
    private long total;

    @ApiModelProperty("草稿列表")
    private List<Draft> draftList;

}

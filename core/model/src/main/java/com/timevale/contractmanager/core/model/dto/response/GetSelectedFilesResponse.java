package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("获取选择的文件列表响应参数")
@NoArgsConstructor
@AllArgsConstructor
public class GetSelectedFilesResponse extends ToString {
    @ApiModelProperty(value = "原文件id列表")
    private List<String> fileIds;
}

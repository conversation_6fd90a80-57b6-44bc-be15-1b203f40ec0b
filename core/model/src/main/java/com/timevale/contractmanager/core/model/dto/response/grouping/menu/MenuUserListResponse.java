package com.timevale.contractmanager.core.model.dto.response.grouping.menu;

import com.timevale.contractmanager.common.service.result.grouping.RoleResult;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 菜单下的授权用户列表
 *
 * @author: xuanzhu
 * @since: 2019-09-09 17:05
 */
@Data
public class MenuUserListResponse extends ToString {

    @ApiModelProperty("菜单id")
    private String menuId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("用户oid或者部门deptId")
    private String oid;

    @ApiModelProperty("手机号码")
    private String mobileNo;

    @ApiModelProperty("邮箱地址")
    private String email;

    @ApiModelProperty("角色id")
    private String roleId;

    @ApiModelProperty(value = "被授权人类型 1-个人;2-部门。为1时oid字段给的是oid，为2时为deptId", required = false)
    private Integer authorizeType;

    @ApiModelProperty(value = "可选择的roleId列表")
    private List<RoleResult> availableRoles;

    @ApiModelProperty(value = "是否可编辑 true-可编辑  false 不可编辑")
    private Boolean canEdit;

    @ApiModelProperty(value = "0 - 法人  1-管理员  2-普通人")
    private Integer menuAuthUserDisplayType;

}

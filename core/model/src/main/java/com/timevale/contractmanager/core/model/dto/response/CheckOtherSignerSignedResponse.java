package com.timevale.contractmanager.core.model.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 校验流程是否存在其他签署人已完成签署
 * <AUTHOR>
 * @since 2022-10-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckOtherSignerSignedResponse {

    @ApiModelProperty("是否存在其他签署人已完成签署")
    private Boolean existOtherSignerSigned;
}

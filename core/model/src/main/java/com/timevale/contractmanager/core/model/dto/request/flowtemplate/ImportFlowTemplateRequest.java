package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import com.timevale.contractmanager.core.model.bo.UnStandardFlowFileChangeInfoBO;
import com.timevale.doccooperation.service.model.DocTemplate;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 导入流程模板入参
 *
 * <AUTHOR>
 * @since 2020/11/6
 */
@Getter
@Setter
@ImportFlowTemplateRequest.ValidImportFlowTemplateAnnotation
public class ImportFlowTemplateRequest extends ToString {

    @ApiModelProperty(value = "导入到当前空间可用")
    private String flowTemplateId;

    @ApiModelProperty(value = "新的流程模板名称")
    private String newFlowTemplateName;

    @ApiModelProperty(value = "共享码,跨空间复制时可用")
    private String shareCode;

    @ApiModelProperty(value = "文件列表")
    private List<DocTemplate> replaceDocTemplates;
    
    @ApiModelProperty("非标模板文件变化记录")
    private UnStandardFlowFileChangeInfoBO unStandardFlowFileChangeInfoBO;

    public static final class ImportFlowTemplateValidor
            implements ConstraintValidator<
                    ValidImportFlowTemplateAnnotation, ImportFlowTemplateRequest> {
        @Override
        public void initialize(ValidImportFlowTemplateAnnotation constraintAnnotation) {}

        @Override
        public boolean isValid(
                ImportFlowTemplateRequest request,
                ConstraintValidatorContext constraintValidatorContext) {
            return StringUtils.isNotBlank(request.getFlowTemplateId())
                    || StringUtils.isNotBlank(request.getShareCode());
        }
    }

    @Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @Constraint(validatedBy = {ImportFlowTemplateValidor.class})
    public @interface ValidImportFlowTemplateAnnotation {

        String message() default "flowTemplateId或者shareCode不能为空";

        Class<?>[] groups() default {};

        Class<? extends Payload>[] payload() default {};
    }
}

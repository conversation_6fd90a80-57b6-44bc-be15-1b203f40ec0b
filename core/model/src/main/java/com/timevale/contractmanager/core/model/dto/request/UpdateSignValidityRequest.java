package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.enums.ValidityUpdateTypeEnum;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.EnumCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 更新签订截止时间请求参数
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
@Data
@ApiModel("更新签订截止时间请求参数")
public class UpdateSignValidityRequest extends ToString {

    @ApiModelProperty(value = "操作人账号id, 不支持前端指定", hidden = true)
    private String accountId;

    @ApiModelProperty(value = "操作主体账号id， 不支持前端指定", hidden = true)
    private String subjectId;

    @ApiModelProperty("指定签订截止日志，当更新类型为2时有效，其他场景设置无效")
    private Long validityDate;

    @ApiModelProperty("天数， 最大365，当更新类型为1有效，其他场景设置无效")
    @Min(value = 1, message = "天数只支持1-365天")
    @Max(value = 365, message = "天数只支持1-365天")
    private Integer validityDay;

    @NotNull(message = "更新类型不能为空")
    @ApiModelProperty("更新类型， 1-在现有截止日期上增加，2-指定到期日")
    @EnumCheck(target = ValidityUpdateTypeEnum.class, enumField = "type")
    private Integer updateType;
}

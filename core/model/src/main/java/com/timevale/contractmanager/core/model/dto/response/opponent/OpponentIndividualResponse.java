package com.timevale.contractmanager.core.model.dto.response.opponent;

import com.timevale.contractmanager.core.model.bo.opponent.OrganizationInfoBO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: huifeng
 * @since: 2021-01-22 13:25
 */
@Builder
@Data
public class OpponentIndividualResponse extends ToString {
    @ApiModelProperty("相对方个人id")
    private String individualId;

    @ApiModelProperty("相对方个人账号oid")
    private String individualAccountId;

    @ApiModelProperty("相对方个人名")
    private String individualName;

    @ApiModelProperty("联系方式，根据contactType决定是手机号还是邮箱")
    private String contact;

    @ApiModelProperty("认证状态 0-未实名；1-实名中；2-已实名；3-已注销")
    private Integer authorizeType;

    @ApiModelProperty("备注")
    private String desc;

    @ApiModelProperty("和相对方个人进行中的合同数")
    private Integer processingContractCount;

    @ApiModelProperty("创建来源合同流程id")
    private String createProcessId;

    @ApiModelProperty("创建来源名称")
    private String createName;

    @ApiModelProperty("创建来源联系人")
    private String createContact;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("黑名单状态")
    private Integer riskLevel;

    @ApiModelProperty("关联企业信息")
    private List<OrganizationInfoBO> organizationList;

}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.enums.ProcessBusinessType;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.core.model.dto.process.StartDataSource;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 通过流程模板id发起
 *
 * <AUTHOR>
 * @since 2019/11/7
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StartByFlowTemplateRequest extends ToString {

    @ApiModelProperty("请求编号，用于幂等处理")
    private String requestNo;

    @ApiModelProperty(value = "流程模板id", required = true)
    @NotBlank(message = "flowTemplateId不能为空")
    private String flowTemplateId;

    @ApiModelProperty(value = "发起人oid", required = true)
    private String initiatorAccountOid;

    @ApiModelProperty(value = "使用场景, 1-直接发起 2-模板发起")
    @NotNull(message = "scene不能为空")
    @Min(value = 1, message = "scene值不正确")
    @Max(value = 2, message = "scene值不正确")
    private Integer scene;

    @ApiModelProperty(value = "如果是模板发起，并且是批量，需要传此字段。标识如果第一个填写人是发起人，如果需要他必填的内容已完成填写，选择“仍要填写”或“确认跳过”，批量合同任务")
    private Boolean skipFill = false;

    /** 合同发起业务类型 参考{@link ProcessBusinessType } */
    @ApiModelProperty(value = "合同发起业务类型, 0-普通流程 1-解约流程 2-重新发起流程 3-续签流程， 默认普通流程")
    private Integer businessType;

    /** 原合同id 重新发起/解约/续签流程时使用 */
    @ApiModelProperty(value = "原合同id, 重新发起/解约/续签流程时使用")
    private String originProcessId;

    /** 原合同解约/续签等文件id列表 */
    @ApiModelProperty(value = "原合同解约/续签等文件id列表")
    private List<String> originFileIds;

    /** 一键落章开关（仅支持直接发起） */
    @ApiModelProperty(value = "一键落章开关（仅支持直接发起）")
    private Boolean batchDropSeal;

    /** 具体发起场景 {@link ProcessStartType} */
    @ApiModelProperty(value = "具体发起场景")
    private String specificScene;

    @ApiModelProperty(value = "钉钉审批流id，非必传")
    private String instanceId;

    @ApiModelProperty("发起数据源")
    private StartDataSource dataSource;

    @ApiModelProperty("是否需要进行印章授权校验")
    private boolean needSealAuthCheck;
}

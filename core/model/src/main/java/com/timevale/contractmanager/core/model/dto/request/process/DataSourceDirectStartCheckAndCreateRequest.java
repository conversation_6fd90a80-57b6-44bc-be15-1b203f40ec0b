package com.timevale.contractmanager.core.model.dto.request.process;

import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/23 14:49
 */
@Data
public class DataSourceDirectStartCheckAndCreateRequest {

    private String dataSourceDataId;
    private UserAccountDetail subjectAccount;
    private UserAccountDetail operatorAccount;
    private List<FileBO> contractFiles;
    private List<FileBO> attachmentFiles;
}

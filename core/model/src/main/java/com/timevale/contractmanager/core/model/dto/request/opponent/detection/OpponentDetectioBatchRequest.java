package com.timevale.contractmanager.core.model.dto.request.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-04 18:20
 */
@Data
public class OpponentDetectioBatchRequest extends ToString {

	@ApiModelProperty("开始时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startTime;

	@ApiModelProperty("结束时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endTime;
}

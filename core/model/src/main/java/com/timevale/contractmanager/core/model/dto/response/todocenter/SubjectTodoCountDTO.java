package com.timevale.contractmanager.core.model.dto.response.todocenter;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 主体待办统计
 *
 * <AUTHOR>
 * @since 2021-11-24 10:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubjectTodoCountDTO extends ToString {

    @ApiModelProperty("主体名称")
    @NeedTranslateField
    private String subjectName;

    @ApiModelProperty("主体oid")
    private String subjectId;

    @ApiModelProperty("待办数量")
    private Long count;
}

package com.timevale.contractmanager.core.model.dto.response.transfer;

import com.timevale.contractmanager.core.model.dto.transfer.CachedTransferredCountInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取缓存中被转交人接收到的转交合同数量响应")
public class GetCachedTransferredCountResponse {
    @ApiModelProperty("企业下被转交的合同数量列表")
    private List<CachedTransferredCountInfoDTO> orgTransferredCountList;
}

package com.timevale.contractmanager.core.model.dto.response.grouping.process;

import com.timevale.contractmanager.common.service.result.grouping.PermissionInfo;
import com.timevale.contractmanager.core.model.dto.response.grouping.standingbook.CustomListDTO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: hei pao
 * @since: 2019-09-17 16:15
 */
@Data
public class GroupingProcessListResponse extends ToString {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("列表信息")
    private List<ProcessInfo> groupingProcessList;

    /**
     * 目前单独抽出来，同一目录下的所有文件都具备相同的操作权限，后面如果有单独对文件设置权限的再放到列表信息里面
     *
     * <p>该权限需要使用目录的权限+用户中心配置【已归档文件管理】的全局权限做合并
     */
    @ApiModelProperty(value = "可操作的权限列表")
    private List<PermissionInfo> permissionInfoList;

    @ApiModelProperty(value = "表格自定义字段列表")
    private CustomListDTO customList;

    private String scrollId;
}

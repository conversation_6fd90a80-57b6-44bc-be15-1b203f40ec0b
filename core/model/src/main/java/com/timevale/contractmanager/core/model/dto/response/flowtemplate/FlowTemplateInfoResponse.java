package com.timevale.contractmanager.core.model.dto.response.flowtemplate;

import com.timevale.contractmanager.common.service.result.grouping.PermissionInfo;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 流程模板数据
 * @author: jinhuan
 * @since: 2020-08-11 16:43
 **/
@Data
public class FlowTemplateInfoResponse extends ToString {
    /**
     * 流程模板id
     */
    @ApiModelProperty(value = "流程模板id")
    private String flowTemplateId;

    /**
     * 流程模板名称
     */
    @ApiModelProperty(value = "流程模板名称")
    private String flowTemplateName;

    /** 流程模板类型 请参考{@link com.timevale.doccooperation.service.enums.FlowTemplateTypeEnum } */
    @ApiModelProperty(value = "流程模板类型 1-普通 2-草稿 3-临时 4-第三方")
    private Integer type;


    /** 流程模板schema类型 请参考{@link com.timevale.doccooperation.service.enums.FlowTemplateSchemaTypeEnum } */
    @ApiModelProperty(value = "流程模板schema类型 0-普通静态模板 1-动态模板 2-混合模板,暂未启用")
    private Integer schemaType;

    /**
     * 流程木版本号 请参考{@link com.timevale.doccooperation.service.enums.FlowTemplateVersionEnum }
     */
    @ApiModelProperty(value = "流程模板版本号")
    private Integer version;

    /**
     * 流程模板状态
     */
    @ApiModelProperty(value = "流程模板状态")
    private Integer status;

    @ApiModelProperty(value = "可操作的权限列表")
    private List<PermissionInfo> permissionInfoList;

    @ApiModelProperty(value = "流程模板标签")
    private String label;

    /**
     * 是否是共享模板（外部授权）
     */
    @ApiModelProperty(value = "是否是外部共享流程模板")
    private boolean shared;

    @ApiModelProperty(value = "专属云项目Id")
    private String dedicatedCloudId;

    /**
     * 流程模板归属oid
     */
    @ApiModelProperty(value = "流程模板真实归属方oid")
    private String ownerOid;

    /**
     * 流程模板归属gid
     */
    @ApiModelProperty(value = "流程模板真实归属方gid")
    private String ownerGid;

    /**
     * 流程模板归属空间名称
     */
    @ApiModelProperty(value = "流程模板真实归属方name")
    private String ownerName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String initiatorName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateName;

    @ApiModelProperty(value = "所属分类")
    private List<CategoryInfo> categories;

    /**
     * 是否置顶
     */
    @ApiModelProperty(value = "是否置顶")
    private Boolean topped;

    /**
     * 置顶时间
     */
    @ApiModelProperty(value = "置顶时间")
    private Date topTime;

    @Data
    public static class CategoryInfo {
        private String oid;
        private String categoryId;
        private String categoryName;
    }
}

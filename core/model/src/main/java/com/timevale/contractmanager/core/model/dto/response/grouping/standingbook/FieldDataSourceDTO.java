package com.timevale.contractmanager.core.model.dto.response.grouping.standingbook;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 字段数据源,用于填充表格某个查询字段的内容
 *
 * @author: xuanzhu
 * @since: 2019-10-27 21:43
 */
@Data
public class FieldDataSourceDTO extends ToString {

    @ApiModelProperty(value = "值列表,最多50个")
    private Set<String> valueList;
}

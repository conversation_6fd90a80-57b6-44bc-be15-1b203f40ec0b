package com.timevale.contractmanager.core.model.dto.request.contractNo;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;

/**
 * 规则保存请求
 * 模板关联关系不传则只保存规则
 *
 * <AUTHOR>
 * @since 2022/10/19 4:34 下午
 */
@Data
public class ContractNoRuleAddRequest extends ToString {

    @NotBlank(message = "ruleName不能为空")
    @Length(max = 50, message = "规则名称最长不可超过50字符")
    private String ruleName;

    @NotBlank(message = "prefix不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5][a-zA-Z0-9\\u4e00-\\u9fa5\\-]{0,19}$", message = "前缀首位仅支持文字、数字或字母，不支持-，最长不可超过20字符")
    private String prefix;

    @NotNull(message = "timeType不能为空")
    private Integer timeType;

    @NotNull(message = "tailType不能为空")
    private Integer tailType;

    @NotNull(message = "tailNumber不能为空")
    private Integer tailNumber;

    @ApiModelProperty("初始流水值")
    private Integer initNumber;
    
    @ApiModelProperty("合同编号间隔符(key：ContractNoPartEnum.order,vlaue：ContractNoIntervalEnum.symbol)")
    private Map<String,String> contractNoInterval;

    @ApiModelProperty("模板关联关系")
    private List<ContractNoTemplateMappingRequest> templates;

    @ApiModelProperty("企业oid")
    private String subjectId;

    @ApiModelProperty("操作人oid")
    private String accountId;
}

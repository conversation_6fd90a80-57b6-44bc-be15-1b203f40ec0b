package com.timevale.contractmanager.core.model.dto.request.process;

import com.timevale.contractmanager.common.service.enums.ParticipantSubjectType;
import com.timevale.contractmanager.core.model.bo.AttachmentConfigBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/5 17:32
 */
@Data
public class ProcessesCheckParticipantSetUpRequest {

    @ApiModelProperty(value = "参与方id")
    private String participantId;

    @ApiModelProperty(value = "参与方id")
    private String participantLabel;

    @ApiModelProperty(value = "参与方实例化")
    private List<ParticipantInstanceBO> instances;

    /** 请参考 {@link ParticipantSubjectType} */
    @ApiModelProperty(value = "参与方主体类型 0-个人 1-企业 2-待定")
    private Integer participantSubjectType = ParticipantSubjectType.UNDETERMINED.getType();

    @ApiModelProperty(value = "参与模式 NORMAL-普通模式 OR_SIGN-或签模式")
    private String participantMode = ParticipantModeEnum.NORMAL.getMode();

    @ApiModelProperty(value = "协作方角色设置 0-指定用户 1-发起人指定 2-发起人自己", required = true)
    private Integer roleSet;

    @ApiModelProperty(value = "是否扫码签参与方")
    private Boolean sharable;

    @ApiModelProperty(value = "固定企业oid")
    private String assignedSubjectId;

    @ApiModelProperty(value = "固定企业名称")
    private String assignedSubjectName;

    @ApiModelProperty(value = "阅读到底")
    private Boolean forceReadEnd;

    @ApiModelProperty(value = "阅读时长")
    private Integer forceReadTime;

    /**
     * 身份验证方式
     * @see com.timevale.contractmanager.common.service.enums.AuthWayEnum
     */
    private String authWay;

    /**
     * authWay = ACCESS_TOKEN 存在
     */
    private String accessToken;

    /**
     * 固定口令 {@link com.timevale.doccooperation.service.enums}
     */
    private Integer accessTokenType;

    @ApiModelProperty(value = "签署要求，逗号分隔 1-企业章 2-经办人 3-法定代表人章")
    private String signRequirements;

    @ApiModelProperty(value = "印章类型，逗号分割，0-手绘印章，1-模版印章，2-ai手绘，为空或0,1不限制")
    private String sealType;

    @ApiModelProperty(value = "意愿方式")
    private List<String> willTypes;

    @ApiModelProperty(value = "通知方式")
    private List<Integer> noticeTypes;

    @Valid
    @ApiModelProperty(value = "参与方附件配置")
    private List<AttachmentConfigBO> attachmentConfigs;

    @ApiModelProperty(value = "是否需要校验, 默认是")
    private boolean participantNeedCheck = true;
}

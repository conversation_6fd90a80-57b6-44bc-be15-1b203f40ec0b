package com.timevale.contractmanager.core.model.dto.request.grouping;

import com.timevale.mandarin.common.result.ToString;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公共入参
 *
 * @author: xuanzhu
 * @since: 2019-09-18 16:32
 */
@Data
public class CommonRequest extends ToString {

    /** 多用于权限校验使用 */
    @ApiModelProperty(value = "当前操作用户oid", required = true)
    @NotBlank(message = "accountId不能为空")
    private String accountId;
}

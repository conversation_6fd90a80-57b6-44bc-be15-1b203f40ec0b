package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.alibaba.fastjson.JSONArray;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI 动态入参
 *
 * @author: xuanzhu
 * @since: 2019-10-29 09:18
 */
@Data
public class AiParamInput extends ToString {

    @ApiModelProperty(value = "查询条件字段，对应fieldCode")
    private String key;

    @ApiModelProperty(value = "查询值")
    private JSONArray value;

    @ApiModelProperty(value = "排序")
    private String sort;

    @ApiModelProperty(value = "是否部门")
    private Boolean isPublic;
}

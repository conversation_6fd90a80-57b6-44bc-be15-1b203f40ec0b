package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: huifeng
 * @since: 2021-02-02 13:42
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessStartedEvent extends ToString {

    private String processId;

    private UserAccountDetail operatorAccount;

    private UserAccountDetail tenantAccount;

    /** 当前使用的流程模板id */
    private String flowTemplateId;
}

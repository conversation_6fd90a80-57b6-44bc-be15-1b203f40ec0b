package com.timevale.contractmanager.core.model.dto.response.autoArchive;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-05-07 20:20
 */
@Data
public class AutoArchiveMenuDTO extends ToString {
	@ApiModelProperty("菜单id")
	private String menuId;

	@ApiModelProperty("菜单名称")
	private String menuName;

	@ApiModelProperty("排序从0开始，升序，越小的排越前面")
	private Integer order;

	@ApiModelProperty("是否只读，true-是（无权归档），false-否（可归档）")
	private boolean isReadOnly = true;

	@ApiModelProperty("自动归档提取次数")
	private Integer autoArchiveNum;

	@ApiModelProperty("绑定的台账名称")
	private String bindingFormName;

	@ApiModelProperty("自动归档状态:0-开启;1-关闭;2-已失效;3-运行中")
	private Integer ruleStatus;

	@ApiModelProperty("规则id")
	private String ruleId;

	@ApiModelProperty(value = "是否统一台账")
	private Integer unityForm;

	@ApiModelProperty("子菜单信息")
	private List<AutoArchiveMenuDTO> childNode;

	@ApiModelProperty("条件名称")
	private List<String> fieldNames;

	@ApiModelProperty("创建时间")
	private Date createTime;

	@ApiModelProperty("修改时间")
	private Date updateTime;

	@ApiModelProperty("置顶时间")
	private Date topTime;

	@ApiModelProperty("菜单路径")
	private String path;
}

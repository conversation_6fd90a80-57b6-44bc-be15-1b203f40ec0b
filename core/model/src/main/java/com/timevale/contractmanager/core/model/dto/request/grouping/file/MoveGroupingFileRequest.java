package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 移动合同文件操作请求
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class MoveGroupingFileRequest extends CommonRequest {

    /** 移动后需要将该菜单ID在processId下移除-ES操作 */
    @ApiModelProperty(value = "移动前的父菜单ID", required = true)
    @NotNull(message = "sourceMenuId不能为空")
    private String sourceMenuId;

    @ApiModelProperty(value = "移动后的父菜单ID集合", required = true)
    @NotNull(message = "targetMenuIdList不能为空")
    @Size(min = 1, message = "targetMenuIdList不能为空")
    @Size(max = 10, message = "最大支持10个分类")
    private List<String> targetMenuIdList;

    @ApiModelProperty(value = "流程列表")
    @Size(max = 100, message = "最多100个流程")
    private List<String> processIds;
}

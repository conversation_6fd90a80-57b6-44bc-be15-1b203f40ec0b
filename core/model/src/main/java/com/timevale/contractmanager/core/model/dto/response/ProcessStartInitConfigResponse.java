package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.bo.AttachmentConfigTypeBO;
import com.timevale.contractmanager.core.model.bo.StartSuccessCacheBO;
import com.timevale.contractmanager.core.model.bo.WaterMarkBO;
import com.timevale.contractmanager.core.model.dto.process.start.AddWatermarkGlobalWatermarkConfigDTO;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 获取合同流程发起初始化配置响应数据
 *
 * <AUTHOR>
 * @date 2021/10/21
 **/
@Data
public class ProcessStartInitConfigResponse {

    @ApiModelProperty(value = "指定意愿方式列表")
    private List<String> willTypes;

    @ApiModelProperty(value = "参与方数据来源， 1-经办人联系人，2-企业相对方")
    private String participantSource;

    @ApiModelProperty(value = "是否拥有直接发起权限")
    private boolean canRedirectStart = true;


    @ApiModelProperty(value = "是否拥有发起页所有文件下载权限")
    private boolean canDownloadAllFile = true;

    @ApiModelProperty(value = "部分拥有发起页下载权限的文件id列表")
    private Set<String> partDownloadFiles;

    @ApiModelProperty(value = "发起序列号")
    private String startSeqNo;


    @ApiModelProperty(value = "上次发起的数据")
    private StartSuccessCacheBO lastStartData;

    @ApiModelProperty(value = "参与方附件配置类型列表")
    @HasTranslateField
    private List<AttachmentConfigTypeBO> attachmentConfigTypeList;

    @ApiModelProperty(value = "支持的签署模式")
    private List<String> signMode;

    /**
     * 水印全局偏好设置
     * 0:不使用水印
     * 1：全局屏幕水印
     * 2：全局文件水印
     */
    // todo tianlei 专属云上线后删除掉这个 后面删除掉
    @Deprecated
    @ApiModelProperty(value = "水印全局偏好设置")
    private AddWatermarkGlobalWatermarkConfigDTO globalWatermarkSetting;

    @Data
    public static class GlobalWatermarkConfig {
        /**
         * 全局水印类型
         * 0:不使用水印
         * 1:全局屏幕水印
         * 2:全局文件水印
         */
        private Integer type = 0;

        /**
         * 文件水印的模式(目前仅针对全局文件水印,即type=2时生效):
         *
         * 1:固定水印ID
         *
         * 2:自选模式（发起时从启用中的水印模板列表选一个）
         */
        private Integer fileWatermarkModel = 1;

        /**
         * 指定水印模板ID集合，目前最大10个;
         *
         * 当type为2时有效
         *
         * model为1时有且有多个ID
         *
         * model为2时此值无效
         */
        private List<WaterMarkBO> fileWatermarkInfos = new ArrayList<>();

        /**
         * 全局屏幕水印时生效
         * 屏幕水印ID
         * 仅当type==1时有效
         */
        private WaterMarkBO screenWatermarkInfo;
    }
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: shaobo
 * @since: 2020-07-22 14:21
 **/
@Data
@ApiModel
public class ProcessExportUrlRequest extends ToString {
    @ApiModelProperty("导出任务Id")
    private String exportTaskId;

    @ApiModelProperty("用户id")
    private String accountId;
}

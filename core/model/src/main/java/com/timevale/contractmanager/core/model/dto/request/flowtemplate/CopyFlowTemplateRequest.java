package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import com.timevale.mandarin.common.result.ToString;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 复制流程模板请求参数
 *
 * <AUTHOR>
 * @since 2020/11/6
 */
@ApiModel(value = "复制流程模板请求参数")
@Getter
@Setter
public class CopyFlowTemplateRequest extends ToString {

    @ApiModelProperty(value = "被复制的流程模板id", required = true)
    @NotBlank(message = "flowTemplateId不能为空")
    private String flowTemplateId;

    @ApiModelProperty(value = "复制目标对象集合，为空表示复制到当前空间下", required = true)
    @NotNull(message = "copyTargets不能为空")
    @Size(min = 1, max = 20, message = "copyTargets数量最小1最大20")
    @Valid
    private List<CopyTarget> copyTargets;

    @Getter
    @Setter
    public static final class CopyTarget {

        @ApiModelProperty(value = "复制目标对象账号,如企业名称/个人账号", required = true)
        @NotBlank(message = "targetAccount不能为空")
        private String targetAccount;

        @ApiModelProperty(value = "复制目标对象账号类型, 0-个人 1-企业 暂时只支持复制到企业", required = true)
        @NotNull(message = "targetAccountType不能为空")
        @Min(value = 0, message = "targetAccountType值不正确")
        @Max(value = 1, message = "targetAccountType值不正确")
        private Integer targetAccountType;
    }
}

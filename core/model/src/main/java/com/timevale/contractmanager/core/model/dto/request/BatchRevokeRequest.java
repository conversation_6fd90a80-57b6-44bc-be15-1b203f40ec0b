package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.docmanager.service.aop.annotation.StringCheckValid;
import com.timevale.docmanager.service.enums.StringCheckTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量撤回入参
 *
 * <AUTHOR> on 2019/7/30
 */
@Data
public class BatchRevokeRequest extends BaseBatchRequest {

    @ApiModelProperty(value = "撤回理由", required = true)
    @NotBlank(message = "revokeReason不能为空")
    @StringCheckValid(message = "撤回理由不允许包含特殊字符", types = {StringCheckTypeEnum.EMOJI})
    private String revokeReason;
}

package com.timevale.contractmanager.core.model.dto.response.process;

import com.google.common.collect.Lists;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2023-09-25 11:00
 */
@Data
@ApiModel("合同参与方状态结果")
public class ProcessParticipantStatusResponse extends ToString {
    @ApiModelProperty(value = "填写参与方状态列表")
    private List<ParticipantStatusVO> writeParticipants = Lists.newArrayList();
    @ApiModelProperty(value = "签署参与方状态列表")
    private List<ParticipantStatusVO> signParticipants = Lists.newArrayList();
    @ApiModelProperty(value = "是否顺序签")
    private boolean orderSign;

    /**
     * 参与方状态信息对象
     *
     */
    @Data
    public static class ParticipantStatusVO {
        @ApiModelProperty(value = "参与方状态")
        private Integer status;
        @ApiModelProperty(value = "参与方是否为或签：true-是，false-否")
        private boolean isMultiOr;
        @ApiModelProperty(value = "参与方实体参与人列表")
        private List<ParticipantInstanceVO> instances = Lists.newArrayList();
    }

    /**
     * 参与方实体参与人对象
     * （兼容或签模式参与方有多个参与人）
     */
    @Data
    public static class ParticipantInstanceVO {

        @ApiModelProperty("或签：是否任务执行人")
        private boolean executorOwner;

        @ApiModelProperty("个人信息")
        private AccountVO person;

        @ApiModelProperty("主体信息")
        private AccountVO subject;
    }

    /**
     * 参与方实体所需的账户信息
     *
     */
    @Data
    public static class AccountVO {
        /** gid */
        private String gid;
        /** oid */
        private String oid;
        /** 名字 */
        private String name;
        /** 手机号 */
        private String mobile;
        /** 邮箱 */
        private String email;

        private String licenseType;
        private String license;
        /** 是否是企业 */
        private Boolean organ;
        /** 昵称 */
        private String nickname;
        /** 是否删除 */
        private Boolean deleted;
    }
}

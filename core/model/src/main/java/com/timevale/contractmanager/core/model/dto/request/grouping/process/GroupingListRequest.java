package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.core.model.dto.request.grouping.RequestContextRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;

/**
 * @author: jinhuan
 * @since: 2020-04-20 11:31
 **/
@Data
public class GroupingListRequest extends RequestContextRequest {
    @Max(100)
    @ApiModelProperty(value = "分页参数", required = true)
    private int pageSize =100;

    @Max(value = 10000, message = "最多支持查看20000条数据，如需查看更早之前的数据，可设置发起时间作为筛选条件或导出明细查看全部数据")
    @ApiModelProperty(value = "分页参数", required = true)
    private int pageNum =1;

    @ApiModelProperty(value = "分类id")
    private String menuId;

    @ApiModelProperty(value = "字段查询入参-json格式")
    private String matching;

    @ApiModelProperty("是否可包含审批状态的流程, 默认不包含")
    Boolean withApproving;

//    @ApiModelProperty("是否企业总部")
//    private Boolean parentSubject;

    @ApiModelProperty("是否查询子菜单的合同，默认查询")
    private Boolean querySubMenuProcess;

    @ApiModelProperty("下载映射状态 0-全部 1-已完成 2-未完成")
    private Integer downloadMapStatus;

    public void valid() {
        ValidationUtil.validateBean(this);
    }
}

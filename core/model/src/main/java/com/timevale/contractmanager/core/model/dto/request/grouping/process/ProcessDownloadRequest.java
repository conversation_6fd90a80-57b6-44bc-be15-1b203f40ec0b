package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.alibaba.fastjson.JSONObject;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 合同流程下载请求参数
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
public class ProcessDownloadRequest extends ToString {

    @ApiModelProperty(value = "产品端id, 不对外透出参数", hidden = true)
    private String clientId;

    @NotBlank(message = "下载场景不能为空")
    private String downloadScene;

    @NotNull(message = "下载条件参数不能为空")
    private JSONObject downloadParam;

    @ApiModelProperty(value = "下载映射状态 0-全部 1-已完成 2-未完成")
    private Integer downloadMapStatus;

    @ApiModelProperty(value = "菜单id")
    private String menuId;
}

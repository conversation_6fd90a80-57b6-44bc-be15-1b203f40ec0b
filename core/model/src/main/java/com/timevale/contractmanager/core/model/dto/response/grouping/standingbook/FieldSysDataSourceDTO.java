package com.timevale.contractmanager.core.model.dto.response.grouping.standingbook;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 系统字段
 *
 * @author: jinhuan
 * @since: 2019-11-05 20:54
 **/
@Data
public class FieldSysDataSourceDTO extends ToString {

    @ApiModelProperty(value = "字段列表")
    private List<FieldSysData> fieldList;

    @Data
    public static class FieldSysData {
        @ApiModelProperty(value = "key")
        private String key;

        @ApiModelProperty(value = "value")
        private String value;
    }
}

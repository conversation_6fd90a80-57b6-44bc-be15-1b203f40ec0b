package com.timevale.contractmanager.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/11/14
 */
public enum ProcessCannotDownloadReasonEnum {

    DATA_NOT_EXIST(0, "数据不存在"),
    STATUS_ERROR(1, "状态错误"),
    NO_SIG_TASK(2, "没有签署任务"),
    NOT_PARTICIPANT(3, "不是参与方"),
    NOT_MATCH_SECRET(4, "无法下载保密合同"),
    NOT_CLOUD(5, "非指定云的合同"),

    NOT_PARTICIPANT_OR_ADMIN(5, "不是参与方或管理员"),

    NOT_MATCH_FILE_AUTH(6, "无法下载设置过文件权限的合同"),
    NOT_APPROVAL_PARTICIPANT(7, "非审批参与方"),
    SIGNING_FORBIDDEN_DOWNLOAD(8, "签署中的合同禁止下载"),
    APPID_STETTING_SIGNING_FORBIDDEN_DOWNLOAD(9, "appId发起方设置了签署中的合同禁止下载"),
    INITIATOR_PREFERENCE_SIGNING_FORBIDDEN_DOWNLOAD(10, "发起方合同偏好设置了签署中的合同禁止下载"),
    ;

    @Getter private Integer code;
    @Getter private String desc;

    ProcessCannotDownloadReasonEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProcessCannotDownloadReasonEnum from(Integer code) {
        if (null == code) {
            return null;
        }

        for (ProcessCannotDownloadReasonEnum statusEnum :
                ProcessCannotDownloadReasonEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }
}

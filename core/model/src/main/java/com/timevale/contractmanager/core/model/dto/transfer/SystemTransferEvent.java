package com.timevale.contractmanager.core.model.dto.transfer;

import com.timevale.contractmanager.core.model.bo.transfer.SystemTransferBO;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/26 转交事件
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SystemTransferEvent {
    private SystemTransferBO systemTransferBO;
    // 查询转交配置的用户oid信息，key为对应的系统转交配置常量
    private Map<String, String> systemTransferToUserOidMap;
    private Map<String, UserAccountDetail> accountDetailMap;
    private String defaultAdminOid;
}

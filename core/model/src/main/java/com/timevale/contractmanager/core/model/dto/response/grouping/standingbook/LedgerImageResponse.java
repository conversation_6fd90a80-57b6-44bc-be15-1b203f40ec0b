package com.timevale.contractmanager.core.model.dto.response.grouping.standingbook;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: huifeng
 * @since: 2021-05-17 19:48
 **/
@Data
public class LedgerImageResponse extends ToString {
    @ApiModelProperty("图片下载地址")
    private String downloadUrl;

    @ApiModelProperty("文件id")
    private String fileId;
}

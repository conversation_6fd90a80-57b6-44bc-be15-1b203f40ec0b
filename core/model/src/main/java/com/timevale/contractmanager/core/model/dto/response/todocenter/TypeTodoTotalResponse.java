package com.timevale.contractmanager.core.model.dto.response.todocenter;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 按照流程状态统计待办数量
 *
 * <AUTHOR>
 * @since 2021-11-24 13:57
 */
@Data
@NoArgsConstructor
public class TypeTodoTotalResponse extends ToString {

    /** 统计列表 */
    @HasTranslateField
    private List<TypeTodoCountDTO> todos;

    /** 总待办数 */
    private Long total;

    public TypeTodoTotalResponse(List<TypeTodoCountDTO> todos) {
        this.todos = todos;

        if (CollectionUtils.isEmpty(todos)) {
            this.total = 0L;
        } else {
            this.total =
                    todos.stream()
                            .filter(Objects::nonNull)
                            .filter(todoCountDTO -> Objects.nonNull(todoCountDTO.getCount()))
                            .mapToLong(TypeTodoCountDTO::getCount)
                            .sum();
        }
    }
}

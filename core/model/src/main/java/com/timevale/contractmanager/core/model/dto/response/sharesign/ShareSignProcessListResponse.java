package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.contractmanager.common.service.bean.ProcessSimpleListResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021-02-05
 */
@Data
public class ShareSignProcessListResponse extends ProcessSimpleListResponse {
    @ApiModelProperty("完成数")
    private long done;

    @ApiModelProperty("未完成数")
    private long inProgress;

    @ApiModelProperty("任务上限数")
    private Long upperLimit;

    @ApiModelProperty("是否私密分享，true: 是，false: 否")
    private Boolean privateShare;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;

import lombok.Data;

import java.util.Map;

/**
 * 获取流程跳转地址
 *
 * <AUTHOR> on 2019/7/30
 */
@Data
public class GetProcessUrlRequest extends ToString {

    /** 流程id */
    private String processId;

    /** 标准签appId */
    private String appId;

    /** 当前产品端信息 */
    private String client;

    /** 操作人账号id */
    private String accountId;

    /** 操作人所属主体id */
    private String subjectId;

    /** 获取哪个端的地址 请参考{@link com.timevale.footstone.rpc.enums.SignPlatformEnum } */
    private Integer platform;

    /** 回调地址 */
    private String redirectUrl;

    /** 归档后的菜单目录id */
    private String menuId;

    /** 客户自身应用appId */
    private String dnsAppId;

    /*一些端上的登录token*/
    private String token;

    /*资源分享id*/
    private String resourceShareId;

    /** 是否从指定空间下获取填写详情地址 */
    private boolean fromTenant;

    /**
     * api接口获取填写链接时传入的业务参数，需要转成长码使用，不需要AES解密
     */
    private String openId;

    /**
     * true需要登录，false 不需要登录
     */
    private Boolean needSaasLogin;

    private Long expireTime;

    /**
     * 入口
     */
    private String entrance;

    /**
     * 扩展参数
     */
    private Map<String, String> extraParams;

    /**
     * 发起人Oid
     */
    private String initiatorOid;
}

package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程发起合并配置项
 *
 * <AUTHOR>
 * @since 2019/12/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProcessStartConfigResponse extends ToString {

    @ApiModelProperty(value = "草稿发起页面地址")
    private String draftStartUrl;

    @ApiModelProperty(value = "编辑流程模板页面地址")
    private String editFlowTemplateUrl;

    @ApiModelProperty(value = "使用流程模板地址")
    private String useFlowTemplateUrl;

    @ApiModelProperty(value = "流程模板重新发起地址")
    private String flowTemplateRestartUrl;

    @ApiModelProperty(value = "当前主体是否是企业")
    private boolean org;

    @ApiModelProperty(value = "是否拥有发起权限")
    private Boolean isStart;

    @ApiModelProperty(value = "体验签署文件")
    private String experienceSignFile;

    public void fortmat(String domainUrl) {
        draftStartUrl = domainUrl + draftStartUrl;
        editFlowTemplateUrl = domainUrl + editFlowTemplateUrl;
        useFlowTemplateUrl = domainUrl + useFlowTemplateUrl;
        flowTemplateRestartUrl = domainUrl + flowTemplateRestartUrl;
    }
}

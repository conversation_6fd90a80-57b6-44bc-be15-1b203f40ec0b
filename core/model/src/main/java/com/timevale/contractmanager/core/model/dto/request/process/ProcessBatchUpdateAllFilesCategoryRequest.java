package com.timevale.contractmanager.core.model.dto.request.process;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 修改合同类型请求参数
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
public class ProcessBatchUpdateAllFilesCategoryRequest extends ToString {

    /** 合同流程id */
    @NotEmpty(message = "合同流程id列表不能为空")
    private List<String> processIds;
    /** 菜单id */
    private String menuId;
    /** 合同类型id */
    private String categoryId;
}

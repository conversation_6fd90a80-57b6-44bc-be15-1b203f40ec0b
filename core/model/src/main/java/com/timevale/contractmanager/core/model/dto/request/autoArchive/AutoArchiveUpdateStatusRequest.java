package com.timevale.contractmanager.core.model.dto.request.autoArchive;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:jiany<PERSON>
 * @since 2021-05-17 20:36
 */
@Data
public class AutoArchiveUpdateStatusRequest extends ToString {
	@ApiModelProperty("规则状态0:开启,1:关闭,2:失效,3:运行中")
	@NotNull(message = "规则状态不能为空")
	private Integer status;

	@ApiModelProperty()
	@NotBlank(message = "分类规则id不能为空")
	private String ruleId;
}

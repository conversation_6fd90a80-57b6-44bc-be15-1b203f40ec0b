package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务数量统计
 *
 * <AUTHOR>
 * @since 2022-12-20 17:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShareSignTaskCountResponse extends ToString {
    /** 处理中任务数量 */
    private Long inProcessNum;

    /** 已结束数量 */
    private Long finishNum;
}

package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/8/13 11:09
 */
@Data
public class FlowTemplateDataSource {

    /**
     * 数据源id
     */
    private String dataSourceId;

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 数据源渠道
     */
    private String dataSourceChannel;

    /**
     * 关联状态
     */
    private String relationStatus;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人姓名
     */
    private String creatorName;

}

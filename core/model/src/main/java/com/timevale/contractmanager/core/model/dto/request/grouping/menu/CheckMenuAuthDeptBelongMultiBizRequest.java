package com.timevale.contractmanager.core.model.dto.request.grouping.menu;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/26 16:01
 */
@Data
public class CheckMenuAuthDeptBelongMultiBizRequest extends ToString {

    @NotEmpty
    @ApiModelProperty(value = "部门ids")
    private List<String> deptIds;

}

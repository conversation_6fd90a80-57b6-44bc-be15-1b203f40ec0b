package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.core.model.dto.request.grouping.RequestContextRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author:jiany<PERSON>
 * @since 2022-06-14 14:29
 */
@Data
public class OneClickGroupFileRequest extends RequestContextRequest {

	@ApiModelProperty(value = "分类id")
	private String toMenuId;

	@ApiModelProperty(value = "字段查询入参-json格式")
	private String matching;

	@ApiModelProperty("是否可包含审批状态的流程, 默认不包含")
	Boolean withApproving;

	public void valid() {
		ValidationUtil.validateBean(this);
	}

}

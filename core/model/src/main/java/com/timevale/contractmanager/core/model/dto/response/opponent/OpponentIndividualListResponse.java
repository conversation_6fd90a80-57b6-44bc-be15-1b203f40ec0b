package com.timevale.contractmanager.core.model.dto.response.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.List;

/**
 * @author: huifeng
 * @since: 2021-01-22 13:25
 **/
@Data
@Builder
public class OpponentIndividualListResponse extends ToString {

    @ApiModelProperty("相对方个人总数")
    private Long totalSize;

    @ApiModelProperty("相对方个人列表")
    private List<OpponentIndividualResponse> individuals;

    @ApiModelProperty("相对方个人已用额度")
    private int opponentEntityNums;

    @JsonIgnore
    private String scrollId;
}

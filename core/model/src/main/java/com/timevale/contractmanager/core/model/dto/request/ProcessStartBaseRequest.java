package com.timevale.contractmanager.core.model.dto.request;

import com.google.common.collect.Maps;
import com.timevale.contractmanager.common.service.bean.ValidityConfigBean;
import com.timevale.contractmanager.common.service.constant.SystemConstant;
import com.timevale.contractmanager.common.service.enums.ProcessBusinessType;
import com.timevale.contractmanager.common.service.enums.ProcessStartType;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.contractmanager.core.model.enums.BizProcessTypeEnum;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.saas.common.enums.SignModeEnum;
import com.timevale.doccooperation.service.enums.CooperationerRoleEnum;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.doccooperation.service.enums.ValidityTypeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.result.ToString;

import lombok.Data;

import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
public class ProcessStartBaseRequest extends ToString {

    /** 应用id，区分不同开发者账号 */
    @NotBlank(message = "应用id不能为空")
    private String appId;

    /** 钉钉corpId */
    private String corpId;

    /** 指定的签署流程id */
    private String assignFlowId;

    /** 指定的合同流程id */
    private String assignProcessId;

    /** 流程组id */
    private String processGroupId;

    /** 流程组名称 */
    private String processGroupName;

    /** 流程名称 */
    @NotBlank(message = "流程名称不能为空")
    private String processName;

    /** 流程备注 */
    private String processComment;

    /** 参与方信息 */
    @Valid
    @NotEmpty(message = "参与方信息不能为空")
    private List<ParticipantBO> participants;

    /** 抄送方信息 */
    private List<UserBO> ccs;

    /** 合同文件 */
    private List<FileBO> contracts;

    /** 附件 */
    private List<FileBO> attachments;

    /**
     * 发起流程使用的flowTemplate,根据processStartScene不同，使用的flowTemplate临时或者永久的 直接发起-指定签署位置 临时模版
     * 直接发起-不指定签署位置 临时模版 模版发起-无填写区域 临时模版 模版发起-有填写区域 永久模版
     */
    private String flowTemplateId;

    /** 原始模板id, 目前只有扫码发起场景才会用到 */
    private String originFlowTemplateId;

    /** 审批流template id */
    private String approveTemplateId;
    /** 合同审批模板条件类型, 不由外部传入，审批流程模板校验完成后，服务层内部设值 {@link com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum} */
    private Integer approvalTemplateConditionType;
    /** 合同审批模板条件值, 不由外部传入，审批流程模板校验完成后，服务层内部设值 */
    private String approvalTemplateConditionValue;

    /**
     * 从哪个平台发起的流程
     *
     * @see com.timevale.footstone.rpc.enums.SignPlatformEnum
     */
    private Integer signPlatform;

    /** 签署截止时间 */
    private Long signEndTime;

    /** 签署截止时间配置 */
    private ValidityConfigBean signValidityConfig;

    /** 文件到期时间 */
    private Long fileEndTime;

    /** 文件到期时间配置 */
    private ValidityConfigBean fileValidityConfig;

    /** 重定向地址 */
    private String redirectUrl;

    /** 签署完成通知地址 */
    private String signedNoticeUrl;

    /** 流程状态变更通知地址 */
    private String processNotifyUrl;

    /** 通知方式，1-短信，2-邮件，空字符串不发送，空值不限 */
    private String noticeType;

    /** 标识如果第一个填写人是发起人，如果需要他必填的内容已完成填写，选择“仍要填写”或“确认跳过”，批量合同任务 */
    private boolean skipFill;

    /** 跳过填写类型：0.不跳过填写 1. 跳过自己的填写流程 2. 跳过全部填写流程" */
    private Integer skipFillType;

    private String token;

    private String clientId;

    private String appName;

    /** 分为轩辕API、轩辕WEB两种from来源 @XuanYuanBizSceneEnum */
    private String bizScene;

    /** 流程发起场景，1-直接发起，2-模板发起， 发起场景和发起模式关系如下
     * 直接发起场景 -- 直接发起（基于合同文件直接发起）、指定位置发起（对应基于流程模板发起）
     * 模板发起场景 -- 基于流程模板发起模式
     * */
    private Integer startScene;

    /**
     * 专属云项目id
     */
    private String dedicatedCloudId;

    /**
     * 签署模式
     */
    private String signMode = SignModeEnum.NORMAL.getCode();

    /**
     * 流程发起模式，1-基于合同文件直接发起，2-基于流程模板发起
     * @see com.timevale.contractmanager.core.model.enums.ProcessStartMode
     */
    private Integer startMode;

    /** 一键落章开关 */
    private Boolean batchDropSeal;

    /**
     * 第三方批量组id（第三方批量发起通知用）
     */
    private String bizGroupId;

    /** 后续扩展使用 */
    private Map<String, String> startSignExtendMap = Maps.newHashMap();

    /**
     * 发起类型
     *
     * @see ProcessStartType
     */
    private Integer startType;

    /**
     * 业务类型
     *
     * @see ProcessBusinessType
     */
    private Integer businessType = ProcessBusinessType.NORMAL.getBusinessType();

    /** 业务说明，限制50字 */
    private String businessRemark;

    /** 三方业务流程id */
    private String bizProcessId;

    /** {@link BizProcessTypeEnum#getType() 三方业务流程类型} */
    private Integer bizProcessType;

    /** 是否走异步发起 */
    private boolean asyncStart;

    /** 是否带epaas标签的模板发起 */
    private boolean epaasTemplateTag;

    /** 发起人所属部门id */
    private String initiatorDeptId;


    /** 发起时指定的水印模板ID */
    private String useWatermarkTemplateId;

    /** 发起时指定的水印模板快照ID */
    private String useWatermarkTemplateSnapshotId;
    
    /** 是否能解约 */
    private Boolean allowRescind;

    /**
     * 计费策略
     */
    private String billStrategy;

    /**
     * 计费隔离场景码
     */
    private String billSceneValue;

    /**
     * 不限制指定的意愿方式，用户指定什么方式就用什么方式（前提是在支持范围内）
     */
    private boolean noLimitWillTypes;


    public ProcessStartBaseRequest() {}

    /**
     * 获取通知方式
     * @return
     */
    public String getNoticeType() {
        if (null == noticeType) {
            return SystemConstant.DEFAULT_CONTRACT_NOTICE_WAYS;
        }
        return noticeType;
    }

    /**
     * 获取发起类型
     *
     * @return
     */
    public Integer getStartType() {
        // 如果未指定发起类型， 默认根据发起场景获取发起类型
        if (null == startType) {
            startType =
                    null != startScene && ProcessStartScene.TEMPLATE_START.getScene() == startScene
                            ? ProcessStartType.NORMAL_TEMPLATE_START.getType()
                            : ProcessStartType.NORMAL_DIRECT_START.getType();
        }
        // 返回发起类型
        return startType;
    }

    /**
     * 判断是否需要进行填写，从而判断走 填写发起 还是 签署发起
     *
     * @return true 需要进行填写 false 不需要进行填写
     */
    public Boolean needFill() {
        return StringUtils.isNotBlank(flowTemplateId)
                && getParticipants().stream()
                        .anyMatch(
                                participantBO -> {
                                    List<String> roles =
                                            Arrays.asList(participantBO.getRole().split(","));
                                    return roles.contains(
                                            CooperationerRoleEnum.FORMULATER.getRole().toString());
                                });
    }

    public void convertParticipantId(List<ParticipantBO> templateParticipants) {
        Map<String, String> useTemplateParticipantLabels =
                templateParticipants.stream()
                        .collect(
                                Collectors.toMap(
                                        ParticipantBO::getParticipantLabel,
                                        ParticipantBO::getParticipantId));
        // 重新设置下参与方的id
        for (ParticipantBO participant : participants) {
            if (useTemplateParticipantLabels.containsKey(participant.getParticipantLabel())) {
                participant.setParticipantId(
                        useTemplateParticipantLabels.get(participant.getParticipantLabel()));
            }
        }
    }

    public void addExtension(String key, String value) {
        if (startSignExtendMap == null) startSignExtendMap = Maps.newHashMap();
        startSignExtendMap.put(key, value);
    }

    public String getExtension(String key) {
        return startSignExtendMap == null ? null : startSignExtendMap.get(key);
    }

    // 统计有多少个流程（批量发起的流程数）
    public int processCount() {
        return participants.stream()
                .filter(i -> !ParticipantModeEnum.isOrSign(i.getParticipantMode()))
                .mapToInt(participants -> participants.getInstances().size())
                .max()
                .orElse(0);
    }

    /**
     * 是否多方批量发起
     *
     * @return
     */
    public Boolean isMultiBatch() {
        return this.getParticipants().size() > 1
                && this.getParticipants().stream().allMatch(
                        i -> !ParticipantModeEnum.isOrSign(i.getParticipantMode())
                                && (CollectionUtils.isNotEmpty(i.getInstances()) && i.getInstances().size() > 1));
    }

    /**
     * 获取签署截止时间配置， 如果未设置，返回默认配置
     *
     * @return
     */
    public ValidityConfigBean getSignValidityConfig() {
        // 获取签署截止时间配置， 如果未设置，返回默认配置
        return signValidityConfig = getOrDefaultValidityConfig(signValidityConfig);
    }

    /**
     * 获取合同到期时间配置， 如果未设置，返回默认配置
     *
     * @return
     */
    public ValidityConfigBean getFileValidityConfig() {
        // 获取合同到期时间配置， 如果未设置，返回默认配置
        return fileValidityConfig = getOrDefaultValidityConfig(fileValidityConfig);
    }

    /**
     * 获取有效期配置，如果有效期配置为空，则返回默认配置
     *
     * @param validityConfig
     * @return
     */
    private ValidityConfigBean getOrDefaultValidityConfig(ValidityConfigBean validityConfig) {
        if (null == validityConfig) {
            validityConfig = ValidityConfigBean.defaultConfig();
        }
        // 如果未指定有效期类型，设置默认有效期类型
        if (null == validityConfig.getValidityType()) {
            validityConfig.setValidityType(ValidityTypeEnum.USE_ASSIGNED.getType());
        }
        return validityConfig;
    }

    public Long getSignEndTime() {
        if (signEndTime != null) {
            return this.signEndTime;
        }
        ValidityConfigBean validityConfig = getSignValidityConfig();
        if (ValidityTypeEnum.USE_ASSIGNED.getType() == validityConfig.getValidityType()) {
            return DateUtils.getDayEnd(DateUtils.addDays(new Date(), 90)).getTime();
        }
        return null;
    }

    /**
     * 判断是否指定了合同到期时间
     * @return
     */
    public boolean assignFileValidity() {
        // 获取合同到期时间有效期类型
        Integer validityType = getFileValidityConfig().getValidityType();
        // 无需设置 或 使用模板时不指定合同到期时间， 返回false
        if (ValidityTypeEnum.NO_NEED.getType() == validityType
                || (ValidityTypeEnum.USE_ASSIGNED.getType() == validityType
                && null == fileEndTime)) {
            return false;
        }
        // 其他场景默认指定了合同到期时间
        return true;
    }

    public List<UserBO> obtainCcs() {
        List<UserBO> ccs = getInstantCcs(this.getParticipants());
        // 7.设置抄送人
        if (CollectionUtils.isNotEmpty(ccs)) {
            return ccs;
        }
        return getCcs();
    }

    public List<UserBO> getInstantCcs(List<ParticipantBO> participantBOS) {
        List<UserBO> ccs = new ArrayList<>();
        if(CollectionUtils.isEmpty(participantBOS)){
            return ccs;
        }
        for(ParticipantBO participantBO : participantBOS){
            if(CollectionUtils.isEmpty(participantBO.getInstances())){
                continue;
            }
            for(ParticipantInstanceBO participantInstanceBO : participantBO.getInstances()){
                if(CollectionUtils.isEmpty(participantInstanceBO.getCcs())){
                    continue;
                }
                for (UserBO userBO : participantInstanceBO.getCcs()){
                    //防止前端传空的数据过滤
                    if(StringUtils.isEmpty(userBO.getAccountOid()) && StringUtils.isEmpty(userBO.getAccount())
                            && StringUtils.isEmpty(userBO.getAccountNick()) && StringUtils.isEmpty(userBO.getAccountName())){
                        continue;
                    }
                    ccs.add(userBO);
                }
            }
        }
        return ccs;
    }
}

package com.timevale.contractmanager.core.model.dto.request.grouping.menu;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 移动菜单操作请求
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class MoveMenuRequest extends CommonRequest {

    @ApiModelProperty(value = "移动前的父菜单ID", required = true)
    @NotNull(message = "sourceMenuId不能为空")
    private String sourceMenuId;

    @ApiModelProperty(value = "移动后的父菜单ID", required = true)
    @NotNull(message = "targetMenuId不能为空")
    private String targetMenuId;

    @ApiModelProperty(value = "移动后排序，同一目录移动时需要传入排序")
    private Integer targetOrder = -1;
}

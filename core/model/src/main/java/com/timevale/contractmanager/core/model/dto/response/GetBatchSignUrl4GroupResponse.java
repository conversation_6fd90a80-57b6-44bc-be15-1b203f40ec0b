package com.timevale.contractmanager.core.model.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: huifeng
 * @since: 2020-11-05 14:19
 **/
@Data
public class GetBatchSignUrl4GroupResponse extends GetBatchSignUrlResponse{

    @ApiModelProperty("后续操作类型 1-跳转后续批量签页面 3-提示其他任务进行中")
    private Integer operationType;

    @ApiModelProperty("当前进行中任务类型 1-批量发起合同 3-合同下载 4-导出合同任务明细 5-批量撤回 6-批量催办")
    private Integer existTaskType;
}

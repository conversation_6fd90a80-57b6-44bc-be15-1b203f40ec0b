package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("缓存选择的文件列表响应参数")
@NoArgsConstructor
@AllArgsConstructor
public class CacheSelectedFilesResponse extends ToString {
    @ApiModelProperty(value = "缓存序列号")
    private String serialKey;
}

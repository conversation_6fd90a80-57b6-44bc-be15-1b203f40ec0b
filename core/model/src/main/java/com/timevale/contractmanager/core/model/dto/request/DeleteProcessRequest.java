package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 企业合同-删除合同请求参数
 *
 * <AUTHOR>
 * @since 2022-06-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class DeleteProcessRequest extends ToString {

    @ApiModelProperty("流程id列表")
    @NotEmpty(message = "流程id列表不能为空")
    @Size(max = 100, message = "流程id列表不能超过100")
    private List<String> processIds;

    @ApiModelProperty("来源。ORG_ARCHIVE/ORG_WAITING_ARCHIVE")
    @NotEmpty(message = "来源不能为空")
    private String resource;
}

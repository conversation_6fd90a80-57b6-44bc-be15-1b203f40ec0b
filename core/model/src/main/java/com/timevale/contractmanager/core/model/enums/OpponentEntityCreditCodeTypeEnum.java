package com.timevale.contractmanager.core.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023-08-24 10:46
 */
@Getter
@AllArgsConstructor
public enum OpponentEntityCreditCodeTypeEnum {

    /** 统一社会信息代码 */
    SOCIAL_CREDIT_CODE(1, "统一社会信用代码"),
    /** 工商注册号 */
    BUSINESS_REGISTRATION_NUMBER(2, "工商注册号"),
    /** 其他（国外企业证件代码）*/
    OTHER(3, "其他")
    ;
    private Integer code;
    private String name;

    /**
     * 依据证件类型名称获取证件类型枚举
     *
     * @param name 证件类型名称
     * @return 证件类型枚举
     */
    public static OpponentEntityCreditCodeTypeEnum getByName(String name) {
        for (OpponentEntityCreditCodeTypeEnum value : OpponentEntityCreditCodeTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 依据证件类型标识获取证件类型枚举
     *
     * @param code
     * @return
     */
    public static OpponentEntityCreditCodeTypeEnum getByCode(Integer code) {
        for (OpponentEntityCreditCodeTypeEnum value : OpponentEntityCreditCodeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}

package com.timevale.contractmanager.core.model.dto.response.datasource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceDataDTO {
    private String thirdApproveInstanceId;
    private String dataSourceId;
    private List<GroupFieldDataDTO> groupFieldDataList;
}

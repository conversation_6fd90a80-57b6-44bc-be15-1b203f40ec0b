package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 查询流程文件列表响应对象
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
public class QueryProcessFilesResponse extends ToString {
    /** 合同文件列表 */
    private List<ProcessFileBean> contractFiles;

    @Data
    public static class ProcessFileBean {
        /** 文件id */
        private String fileId;
        /** 文件fileKey */
        private String fileKey;
        /** 文件名称 */
        private String fileName;
        /** 合同类型id */
        private String categoryId;
        /** 合同类型名称 */
        private String categoryName;
        /** 是否附属文件 */
        private boolean attachment;
    }
}

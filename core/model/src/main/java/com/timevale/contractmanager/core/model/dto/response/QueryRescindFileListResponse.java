package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.DocRescindInfo;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 查询解约流程返回对象
 *
 * <AUTHOR>
 * @since 2021-03-05 00:05
 **/
@Data
public class QueryRescindFileListResponse extends ToString {

    /**
     * 原流程文件信息
     */
    private List<DocRescindInfo> files;

    /**
     * 原流程状态
     */
    private Integer originProcessStatus;

}

package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.google.common.collect.Lists;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-05
 */
@Data
public class ShareSignTaskUrlResponse extends ToString {
    @ApiModelProperty("扫码签任务Id")
    private String shareSignTaskId;

    @ApiModelProperty("扫码签任务链接地址")
    private List<ShareSignTaskUrl> shareUrls = Lists.newArrayList();
}

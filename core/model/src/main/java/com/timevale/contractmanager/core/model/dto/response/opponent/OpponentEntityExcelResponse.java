package com.timevale.contractmanager.core.model.dto.response.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 表格解析返回
 *
 * <AUTHOR>
 * @since 2021-02-02 11:21
 */
@Data
public class OpponentEntityExcelResponse extends ToString {

    @ApiModelProperty(value = "导入成功数量")
    private int successCount;

    @ApiModelProperty(value = "导入失败数量")
    private int errorCount;

    @ApiModelProperty(value = "错误数据excel表格下载地址，为空表示无错误数据")
    private String errorDataExcelDownloadUrl;

    private String fileKey;


}

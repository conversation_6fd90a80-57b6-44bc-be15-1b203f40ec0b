package com.timevale.contractmanager.core.model.dto.cooperation;

import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @classname ParticipantInstance
 * @description
 * @date 2020/1/16 18:30
 */
@Data
public class ParticipantInstance extends UserBO {

    /**
     * 参与主体gid
     */
    private String subjectGid;

    /**
     * 参与主体uid
     */
    private String subjectUid;

    public void refreshAccount(UserAccount userAccount) {
        if (StringUtils.isBlank(getAccountOid())) {
            setAccountOid(userAccount.getAccountOid());
        }
        if (StringUtils.isBlank(getAccountGid())) {
            setAccountGid(userAccount.getAccountGid());
        }
        if (StringUtils.isBlank(getAccountUid())) {
            setAccountUid(userAccount.getAccountUid());
        }
    }

    public void refreshSubject(UserAccount userAccount) {
        if (StringUtils.isBlank(getSubjectId())) {
            setSubjectId(userAccount.getAccountOid());
        }
        if (StringUtils.isBlank(getSubjectGid())) {
            setSubjectGid(userAccount.getAccountGid());
        }
        if (StringUtils.isBlank(getSubjectUid())) {
            setSubjectUid(userAccount.getAccountUid());
        }
    }
}

package com.timevale.contractmanager.core.model.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BatchSignRequest extends BatchSignBaseRequest {
    /**
     * @see com.timevale.contractmanager.core.service.process.batchsign.BatchSignSource
     */
    @ApiModelProperty(value = "来源 1-我的合同 2-批量签署 3-待办中心, 默认 1")
    private Integer source = 1;
    /**
     * @see com.timevale.footstone.rpc.enums.SignPlatformEnum
     */
    @ApiModelProperty(value = "客户端类型 4-移动端H5 5-标准签WEB")
    private Integer clientType;
    @ApiModelProperty(value = "scheme")
    private String appScheme;
    @ApiModelProperty(value = "token")
    private String token;
}

package com.timevale.contractmanager.core.model.dto.request.transfer;

import com.timevale.contractmanager.common.service.enums.TransferSceneEnum;
import com.timevale.contractmanager.core.model.dto.transfer.TransferProcessInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/9 合同转交请求
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("合同转交请求")
public class ProcessTransferRequest {
    @ApiModelProperty("被转交人oid")
    private String transferToAccountId;

    @ApiModelProperty("转交人，全部转交需要此参数")
    private List<String> transferUserList;

    @ApiModelProperty("转交流程信息，分批转交需要此参数")
    private TransferProcessInfoDTO transferProcessInfo;

    @ApiModelProperty("转交审批流程信息，分批转交需要此参数")
    private MixTransferApprovalInfoDTO mixTransferApprovalInfo;

    /** {@link com.timevale.contractmanager.common.service.enums.TransferSceneEnum} */
    @ApiModelProperty("转交场景值")
    private Integer transferScene = TransferSceneEnum.PROCESS.getCode();

    @ApiModelProperty("合同转交原因")
    private String contractApprovalTransferReason;

    @ApiModelProperty("用印转交原因")
    private String sealApprovalTransferReason;

    @Data
    public static class MixTransferApprovalInfoDTO {
        @ApiModelProperty("转交人oid")
        private String transferAccountOid;

        @ApiModelProperty("转交审批流程列表")
        private List<TransferApprovalDTO> transferApprovalList;
    }

    @Data
    public static class TransferApprovalDTO {
        @ApiModelProperty("审批流程id")
        private String approvalId;

        /** {@link com.timevale.account.flow.service.enums.ApprovalFlowTemplateTypeEnum} */
        @ApiModelProperty("审批流程类型")
        private String approvalType;
    }
}

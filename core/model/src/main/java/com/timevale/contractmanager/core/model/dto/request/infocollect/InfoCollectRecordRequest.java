package com.timevale.contractmanager.core.model.dto.request.infocollect;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * InfoCollecRecordDataRequest
 *
 * <AUTHOR>
 * @since 2022/9/21 2:50 下午
 */
@Data
public class InfoCollectRecordRequest extends ToString {

    @NotBlank(message = "任务id不能为空")
    private String infoCollectTaskOuterId;

    @NotBlank(message = "记录id不能为空")
    private String infoCollectRecordOuterId;
}

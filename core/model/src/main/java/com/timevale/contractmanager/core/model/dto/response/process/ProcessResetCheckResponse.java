package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-04-12
 */
@Data
public class ProcessResetCheckResponse extends ToString {

    @ApiModelProperty(value = "是否支持重置合同流程")
    private boolean canReset;

    @ApiModelProperty(value = "不支持重置合同流程原因")
    private String canNotResetReason;

}
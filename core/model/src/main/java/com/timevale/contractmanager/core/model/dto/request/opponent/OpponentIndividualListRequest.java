package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * @author: huifeng
 * @since: 2021-01-22 13:32
 **/
@Data
public class OpponentIndividualListRequest extends ToString {
    @Max(100)
    @ApiModelProperty(value = "分页参数", required = true)
    private Integer pageSize = 20;

    @ApiModelProperty(value = "分页参数", required = true)
    private Integer pageNum = 1;

    @Min(0)
    @Max(3)
    @ApiModelProperty("认证状态 0-未实名；1-实名中；2-已实名；3-已注销")
    private Integer authorizeType;

    @Min(1)
    @Max(2)
    @ApiModelProperty("风险等级 1-白名单; 2-黑名单")
    private Integer riskLevel;

    @ApiModelProperty("备注信息，模糊匹配")
    @Length(max = 200, message = "备注最长不可超过200字符")
    private String fuzzyDesc;

    @ApiModelProperty("个人名，模糊匹配")
    @Length(max = 50, message = "姓名最长不可超过50字符")
    private String fuzzyIndividualName;

    @ApiModelProperty("手机号/邮箱，模糊匹配")
    @Length(max = 50, message = "手机号/邮箱最长不可超过50字符")
    private String fuzzyEntityUniqueId;

    @ApiModelProperty("所属企业 1-有; 2-没有")
    private Integer attachedEntityType;

    private String scrollId;

    private Boolean useScroll;
}

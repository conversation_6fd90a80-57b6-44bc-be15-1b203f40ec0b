package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.AccountBean;
import com.timevale.contractmanager.common.service.bean.ProcessButtonBean;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-08 20:41
 */
@Data
public class ProcessNodeUsersResponse {
    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("发起时间")
    private Date processCreateTime;

    @ApiModelProperty("合同到期时间")
    private Date contractValidity;

    @ApiModelProperty("签订截止时间")
    private Date signValidity;


    @ApiModelProperty("流程状态")
    private Integer processStatus;

    @ApiModelProperty("流程状态描述")
    private String processStatusDesc;

    @ApiModelProperty("发起人")
    ProcessAccountInfo startAccount;

    @ApiModelProperty("流程填写人列表 - 有序")
    private List<ProcessNodeAccount> cooperatorAccounts;

    @ApiModelProperty("流程审批人列表 - 混序")
    private List<ProcessNodeAccount> approverAccounts;

    @ApiModelProperty("流程签署人列表 - 混序")
    private List<ProcessNodeAccount> signerAccounts;

    @ApiModelProperty("当前待处理的人 - 无序")
    private List<ProcessNodeAccount> currentOperators;

    @ApiModelProperty("是否支持单个节点多个用户模式，默认支持")
    private boolean processNodeMultiAccount = true;

    @ApiModelProperty(
            value = "当前登录人是否能对此流程操作-签署or填写等",
            example = "true"
    )
    private Boolean operable;


    @ApiModelProperty("填写流程ID")
    private String cooperationid;

    @ApiModelProperty("签署流程ID")
    private String flowId;


    @ApiModelProperty("合同审批模板version ID")
    private String approveTemplateId;

    @ApiModelProperty("合同审批ID")
    private String approveId;


    @ApiModelProperty(value = "包含填写流程")
    private boolean withFillProcess;

    @ApiModelProperty(value = "包含填写流程")
    private List<ProcessButtonInfo> buttons;

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
        ProcessStatusEnum statusEnum = ProcessStatusEnum.parse(processStatus);
        if (null != statusEnum) {
            this.processStatusDesc = statusEnum.getStatusDesc();
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProcessButtonInfo extends ToString {
        @ApiModelProperty("按钮code")
        private String code;

        @ApiModelProperty("按钮描述")
        private String desc;
    }

    @Data
    public static class ProcessAccountInfo extends AccountInfo {

        @ApiModelProperty("人类型")
        private Integer type;

        @ApiModelProperty("是否当前操作人")
        private Boolean currentUser = Boolean.FALSE;

        @ApiModelProperty("当前节点中的顺序")
        private Integer order;

        @ApiModelProperty(value = "是否扫码签参与方")
        private boolean sharable = false;
    }

    @Data
    public static class ProcessNodeAccount extends ToString {

        @ApiModelProperty("当前节点的用户列表")
        private List<AccountInfo> accounts;

        @ApiModelProperty("人类型")
        private Integer type;

        @ApiModelProperty("角色类型")
        private String role;

        @ApiModelProperty("是否当前操作人")
        private Boolean currentUser = Boolean.FALSE;

        @ApiModelProperty("当前节点中的顺序")
        private Integer order;

        @ApiModelProperty(value = "是否扫码签参与方")
        private boolean sharable = false;

        @ApiModelProperty(value = "状态")
        private Integer status;
    }

    @Data
    public static class AccountInfo extends ToString {

        @ApiModelProperty("个人信息")
        private AccountBean person;

        @ApiModelProperty("主体信息")
        private AccountBean subject;
    }

    @Getter
    public enum NodeRole {
        // 填写节点
        COOPERATOR("COOPERATOR"),
        // 审批节点
        APPROVER("APPROVER"),
        // 审批抄送节点
        APPROVER_CC("APPROVER_CC"),
        // 签署节点
        SIGNER("SIGNER"),
        ;

        private String role;

        NodeRole(String role) {
            this.role = role;
        }
    }
}

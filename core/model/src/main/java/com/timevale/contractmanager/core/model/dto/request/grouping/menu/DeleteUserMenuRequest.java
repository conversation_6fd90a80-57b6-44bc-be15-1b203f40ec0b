package com.timevale.contractmanager.core.model.dto.request.grouping.menu;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 删除用户的菜单操作权限
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class DeleteUserMenuRequest extends CommonRequest {

    @ApiModelProperty(value = "被删除的授权用户 oid或者部门id", required = true)
    @NotNull(message = "authorizer不能为空")
    @Size(max = 100, message = "oidList最长为100")
    private List<String> oidList;

    @ApiModelProperty(value = "被删除授权的类型，1-oid；2-deptId")
    private List<Integer> authorizeTypes;
}

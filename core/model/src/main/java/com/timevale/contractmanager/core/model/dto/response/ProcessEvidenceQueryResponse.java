package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.ProcessEvidenceListBean;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-14
 */
@Data
@ApiModel("可出证合同流程列表查询响应数据")
public class ProcessEvidenceQueryResponse extends ToString {
    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("流程列表")
    private List<ProcessEvidenceListBean> processes;

}

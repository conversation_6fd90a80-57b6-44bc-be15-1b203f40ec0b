package com.timevale.contractmanager.core.model.dto.response.contractNo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 规则信息
 *
 * <AUTHOR>
 * @since 2022/10/19 4:59 下午
 */
@Data
public class ContractNoRuleVO {

    @ApiModelProperty(value = "规则Id")
    private String ruleId;

    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "前缀")
    private String prefix;

    @ApiModelProperty(value = "时间类型")
    private Integer timeType;

    @ApiModelProperty(value = "尾号类型")
    private Integer tailType;

    @ApiModelProperty(value = "尾号数量")
    private Integer tailNumber;

    @ApiModelProperty(value = "初始值")
    private Integer initNumber;

    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    @ApiModelProperty(value = "创建人oid")
    private String createByOid;

    @ApiModelProperty(value = "修改人姓名")
    private String modifiedName;

    @ApiModelProperty(value = "修改人oid")
    private String modifiedByOid;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "修改时间")
    private Long modifyTime;

    @ApiModelProperty(value = "模板列表")
    private List<ContractNoTemplateMappingVO> templates;
    
    @ApiModelProperty(value = "间隔符")
    private String contractNoInterval;
}

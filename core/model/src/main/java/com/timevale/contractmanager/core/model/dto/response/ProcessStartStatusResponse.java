package com.timevale.contractmanager.core.model.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 * @date 2022/2/21
 */
@Data
public class ProcessStartStatusResponse {

    /** 主流程状态 请参考{@link com.timevale.contractmanager.common.service.enums.ProcessStatusEnum} */
    @ApiModelProperty("合同状态 1填写 2签署 3合同审批 0未发起")
    private Integer status = 0;
    @ApiModelProperty("当前发起方是否为操作人")
    private boolean hasOperator = false;
}

package com.timevale.contractmanager.core.model.dto.response.todocenter;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 主体统计信息类
 *
 * <AUTHOR>
 * @since 2021-12-07 19:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubjectTodoCountDTOHolder extends ToString {

    @ApiModelProperty("主体名称")
    private String subjectName;

    @ApiModelProperty("主体gid")
    private String subjectGid;

    @ApiModelProperty("主体oid")
    private String subjectOid;

    @ApiModelProperty("待办数量")
    private Long count;

    public SubjectTodoCountDTO get() {
        return new SubjectTodoCountDTO(subjectName, subjectOid, count);
    }
}

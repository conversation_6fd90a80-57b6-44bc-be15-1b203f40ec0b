package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取扫码签任务管理地址请求参数
 *
 * <AUTHOR>
 * @since 2019/11/7
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShareSignTaskManageUrlRequest extends ToString {

    @ApiModelProperty(value = "登录token")
    private String token;
}

package com.timevale.contractmanager.core.model.dto.response.process;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同签署方口令
 * 
 * <AUTHOR>
 * @date 2023-11-28
 */
@Data
public class ProcessParticipantAuthWayResponse {

    @ApiModelProperty(value = "签署方列表")
    private List<ProcessParticipantInfo> list;

    @Data
    public static class ProcessParticipantInfo {
        @ApiModelProperty(value = "签署方标签")
        private String label;
        @ApiModelProperty(value = "签署模式")
        private String participantMode;
        @ApiModelProperty(value = "签署口令信息列表")
        private List<ProcessAuthInfo> participant;
    }

    @Data
    public static class ProcessAuthInfo{
        @ApiModelProperty(value = "是否为企业")
        private Boolean organ;
        @ApiModelProperty(value = "企业oid")
        private String subjectOid;
        @ApiModelProperty(value = "主体名称")
        private String subjectName;
        @ApiModelProperty(value = "个人oid")
        private String personOid;
        @ApiModelProperty(value = "个人名称")
        private String personName;
        @ApiModelProperty(value = "手机号或者邮箱")
        private String contract;
        @ApiModelProperty(value = "NONE无、ACCESS_TOKEN访问口令、REAL_NAME_AUTH 登录+实名 本期只有签署口令")
        private String authWay;
        @ApiModelProperty(value = "访问口令")
        private String accessToken;
    }
}

package com.timevale.contractmanager.core.model.dto.transfer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/9 转交流程信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("转交流程信息")
public class TransferProcessInfoDTO {
    @ApiModelProperty("转交人oid")
    private String transferAccountOid;
    @ApiModelProperty("转交流程列表")
    private List<String> transferProcessList;
}

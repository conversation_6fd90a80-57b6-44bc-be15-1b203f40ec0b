package com.timevale.contractmanager.core.model.dto.response.todocenter;

import com.timevale.contractmanager.core.model.enums.TodoTypeEnum;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 按照待办类型统计列表
 *
 * <AUTHOR>
 * @since 2021-11-24 14:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TypeTodoCountDTO extends ToString {

    @ApiModelProperty("类型名称")
    @NeedTranslateField
    private String label;

    /** @see TodoTypeEnum */
    @ApiModelProperty("待办类型")
    private Integer type;

    @ApiModelProperty("待办数量")
    private Long count;

    @ApiModelProperty("子级待办统计")
    @HasTranslateField
    private List<TypeTodoCountDTO> subList;

    public TypeTodoCountDTO(String label, Integer type, Long count) {
        this.label = label;
        this.type = type;
        this.count = count;
    }

    public TypeTodoCountDTO(String label, Integer type, List<TypeTodoCountDTO> subList) {
        this.label = label;
        this.type = type;
        this.subList = subList;

        if (CollectionUtils.isEmpty(subList)) {
            this.count = 0L;
        } else {
            this.count =
                    subList.stream()
                            .filter(Objects::nonNull)
                            .filter(todoCountDTO -> Objects.nonNull(todoCountDTO.getCount()))
                            .mapToLong(TypeTodoCountDTO::getCount)
                            .sum();
        }
    }
}

package com.timevale.contractmanager.core.model.dto.request.infocollect;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * InfoCollectImportRequest
 *
 * <AUTHOR>
 * @since 2022/9/21 2:41 下午
 */
@Data
public class InfoCollectStartRequest extends ToString {

    @NotBlank(message = "台账id不能为空")
    private String formId;

    @NotBlank(message = "模板id不能为空")
    private String templateId;

    // 二期后这个字段废弃
    @Deprecated
    private InfoCollectCooperationRequest cooperation;

    @NotEmpty(message = "发起记录不能为空")
    @Valid
    private List<InfoCollectRecordRequest> recordList;

    private Boolean upgrade;
}

package com.timevale.contractmanager.core.model.dto.request.datasource;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 数据源发起消息服务请求体
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Setter
@Getter
public class DataSourceStartNotifyRequest {

	/**
	 * 三方表单数据id
	 */
	private String dataId;


	/**
	 * 流程所属人oid
	 */
	private String ownerOid;
	/**
	 * 流程模板id
	 */
	private String flowTemplateId;

	/**
	 * 通知模板名称
	 */
	private String notifyTemplateName;

	/**
	 * 通知渠道
	 */
	private List<String> notifyChannels;

	/**
	 * 通知内容：用户名称
	 */
	private String userName;
	/**
	 * 通知内容：表单名称
	 */
	private String formName;

	/**
	 * 通知内容：模板名称
	 */
	private String flowTemplateName;


	/**
	 * 开始时间
	 */
	private Long startTime;

	/**
	 * 结束时间
	 */
	private Long endTime;

	/**
	 * 错误原因
	 */
	private String failReason;


}

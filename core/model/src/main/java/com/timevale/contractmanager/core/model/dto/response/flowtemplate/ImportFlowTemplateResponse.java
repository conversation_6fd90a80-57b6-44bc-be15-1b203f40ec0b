package com.timevale.contractmanager.core.model.dto.response.flowtemplate;

import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 导入流程模板结果
 *
 * <AUTHOR>
 * @since 2020/11/6
 */
@Getter
@Setter
public class ImportFlowTemplateResponse extends ToString {

    @ApiModelProperty(value = "复制后新流程模板id")
    private String flowTemplateId;

    @ApiModelProperty(value = "复制后新流程模板中参与方的映射关系")
    private Map<String, String> cooperationerIdMap;
}

package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("合同审批流程组数据")
public class ProcessApprovalGroupResponse extends ToString {

    @ApiModelProperty("合同审批流程组id")
    private String approvalGroupId;

    @ApiModelProperty("合同审批流程组名称")
    private String approvalGroupName;

    @ApiModelProperty("流程组创建时间")
    private Long createTime;

    @ApiModelProperty("流程组创建者账户oid")
    private String creatorAccountOid;

    @ApiModelProperty("流程组创建者账户gid")
    private String creatorAccountGid;

    @ApiModelProperty("流程组创建者账号名称")
    private String creatorAccountName;

    @ApiModelProperty("合同流程总数")
    private long totalProcesses;

    @ApiModelProperty("已完成合同流程数")
    private long finishApproval;

    @ApiModelProperty("合同流程状态")
    private Integer approvalGroupStatus;

}

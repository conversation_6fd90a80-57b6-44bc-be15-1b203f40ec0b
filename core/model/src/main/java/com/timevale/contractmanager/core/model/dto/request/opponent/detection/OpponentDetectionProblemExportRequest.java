package com.timevale.contractmanager.core.model.dto.request.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: huifeng
 * @since: 2021-08-25 10:54
 **/
@Data
public class OpponentDetectionProblemExportRequest extends ToString {

    @ApiModelProperty("企业名称")
    private String orgName;

    @ApiModelProperty("风险等级类型,低,高,极高")
    private List<Integer> riskLevel;

    @ApiModelProperty(value = "问题级别")
    private List<Integer> problemLevel;

    @ApiModelProperty(value = "开始日期")
    private Long startDate;

    @ApiModelProperty(value = "结束日期")
    private Long endDate;
}

package com.timevale.contractmanager.core.model.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取批量签署地址
 *
 * <AUTHOR>
 * @since 2019/7/31
 */
@Data
public class GetBatchSignUrlRequest extends BaseBatchRequest {

    @ApiModelProperty("批量批次id")
    private String batchSerialId;

    @ApiModelProperty("签署完成重定向地址")
    private String redirectUrl;

    @ApiModelProperty("签署端信息, 默认PC端")
    private boolean h5;

    @ApiModelProperty("指定token")
    private String token;
}

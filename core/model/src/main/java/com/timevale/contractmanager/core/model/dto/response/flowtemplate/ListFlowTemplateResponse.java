package com.timevale.contractmanager.core.model.dto.response.flowtemplate;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 流程模板列表数据
 * @author: jinhuan
 * @since: 2020-08-11 16:40
 **/
@Data
public class ListFlowTemplateResponse extends ToString {
    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Long total;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    private Integer currPage;

    /**
     * 页数大小
     */
    @ApiModelProperty(value = "页数大小")
    private Integer totalPage;

    /**
     * 共享模板数
     */
    @ApiModelProperty(value = "共享模板数")
    private Long sharedCount;

    /**
     * 流程模板列表
     */
    @ApiModelProperty(value = "流程模板列表")
    private List<FlowTemplateInfoResponse> list;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("草稿列表查询参数")
@Valid
public class ProcessDraftQueryRequest extends ToString {

    @Min(value = 0, message = "page >=0")
    @NotNull(message = "page 不能为空")
    @ApiModelProperty(value = "页码", required = true, position = 1)
    private Integer pageNum;

    @NotNull(message = "pageSize 不能为空")
    @Min(value = 0, message = "pageSize >=0")
    @ApiModelProperty(value = "每页大小", required = true, position = 1)
    private Integer pageSize;

    @ApiModelProperty(value = "查询类型", required = true)
    @NotNull(message = "查询类型不能为空")
    private Integer docQueryType;

    @ApiModelProperty(value = "查询用户账号id")
    @NotBlank(message = "查询用户账号id不能为空")
    private String accountId;

    @ApiModelProperty(value = "查询主体id")
    @NotBlank(message = "查询主体id不能为空")
    private String subjectId;

    @ApiModelProperty("模糊查询关键字")
    private String fuzzyMatching;

    @ApiModelProperty(value = "查询时间类型：1）发起时间；2）完成时间")
    private Integer timeRangeType;

    @ApiModelProperty(value = "开始时间,单位毫秒")
    private Long beginTimeInMillSec;

    @ApiModelProperty(value = "截止时间,单位毫秒")
    private Long endTimeInMillSec;

    @ApiModelProperty(value = "是否提交定时发起")
    private Boolean startScheduledInitiate;

    @ApiModelProperty(value = "参与人姓名")
    private String personName;

    @ApiModelProperty(value = "参与主体名称")
    private String subjectName;
}

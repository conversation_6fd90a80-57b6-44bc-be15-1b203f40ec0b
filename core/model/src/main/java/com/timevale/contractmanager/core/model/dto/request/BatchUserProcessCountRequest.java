package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/7
 */
@Data
@ApiModel("查询批量用户合同数量请求")
public class BatchUserProcessCountRequest extends ToString {
	@ApiModelProperty("查询的用户的oid列表")
	@NotEmpty(message = "查询的用户的oid列表不能为空")
	private List<String> queryUserOids;
}
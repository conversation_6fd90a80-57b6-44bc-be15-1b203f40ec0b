package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-02-01 14:27
 */
@Data
public class OpponentEntityBatchDeleteRequest extends ToString {
	@ApiModelProperty("实体的uuid")
	private List<String> uuid;
	@ApiModelProperty("是否删除企业下的个人联系人(true:删除)")
	private boolean deleteIndividuals;
}

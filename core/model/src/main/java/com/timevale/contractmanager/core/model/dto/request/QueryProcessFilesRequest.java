package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * 获取流程文件列表请求参数
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
public class QueryProcessFilesRequest extends ToString {
    /** 用户id */
    private String accountId;
    /** 主体gid */
    private String subjectId;
    /** 流程id */
    private String processId;
    /** 菜单id */
    private String menuId;
    /** 资源分享id */
    private String resourceShareId;
    /** 是否返回文件fileKey */
    private boolean withFileKey;
    /** 是否返回附属文件 */
    private boolean withAttachment;
    /** 是否返回合同类型 */
    private boolean withCategory;
}

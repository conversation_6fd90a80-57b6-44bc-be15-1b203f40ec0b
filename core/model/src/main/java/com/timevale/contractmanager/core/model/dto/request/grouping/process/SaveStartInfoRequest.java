package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 保存发起流程的请求入参数据
 *
 * @author: akun
 * @since: 2020-05-21 16:13
 */
@Getter
@Setter
public class SaveStartInfoRequest extends ToString {

    @ApiModelProperty(value = "文件id列表", required = true)
    @NotEmpty(message = "文件id列表不能为空")
    @Valid
    private List<String> fileIds;
}

package com.timevale.contractmanager.core.model.enums;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;

import java.util.Objects;

/**
 * 签署要求枚举
 *
 * <AUTHOR>
 * @since 2021-03-16 00:20
 **/
@Getter
public enum SignRequirementEnum {

    /** 1-企业章 */
    ENTERPRISE_SEAL(1, 1, "企业章"),

    /** 2-经办人章 */
    AGENT_SEAL(2, 2, "经办人章"),

    /** 3-法定代表人章 */
    CORPORATE_SEAL(3, 3, "法定代表人章");

    private Integer type;
    private Integer signerRoleType;
    private String desc;

    SignRequirementEnum(int type, int signerRoleType, String desc) {
        this.type = type;
        this.signerRoleType = signerRoleType;
        this.desc = desc;
    }

    public boolean bizEquals(String type) {
        return this.getType().toString().equals(type);
    }

    public String getStringValue() {
        return this.getType().toString();
    }

    public static String getName(String type) {
        for (SignRequirementEnum signRequirementEnum : SignRequirementEnum.values()) {
            if (Objects.equals(String.valueOf(signRequirementEnum.getType()), type)) {
                return signRequirementEnum.getDesc();
            }
        }
        return "";
    }
}

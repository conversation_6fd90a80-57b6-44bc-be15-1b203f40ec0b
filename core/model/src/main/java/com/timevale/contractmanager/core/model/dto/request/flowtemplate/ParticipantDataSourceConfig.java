package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/13 11:20
 */
@Data
public class ParticipantDataSourceConfig {

    /**
     * 数据源id, 表单id
     */
    private String dataSourceId;

    /**
     * 名称配置
     */
    private ParticipantOneInfoDataSourceConfig accountNameConfig;

    /**
     * 联系方式
     */
    private ParticipantOneInfoDataSourceConfig accountConfig;


    /**
     * 企业名称
     */
    private ParticipantOneInfoDataSourceConfig subjectNameConfig;

    /**
     * 发起方配置
     */
    private ParticipantOneInfoDataSourceConfig initiatorConfig;

}

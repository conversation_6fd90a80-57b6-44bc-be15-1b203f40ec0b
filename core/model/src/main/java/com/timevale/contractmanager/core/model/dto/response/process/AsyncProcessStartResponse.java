package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.contractmanager.core.model.dto.response.BaseProcessStartResponse;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 各start 响应结果父类
 * <AUTHOR>
 * @since 2020/3/23
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AsyncProcessStartResponse extends BaseProcessStartResponse {
    @ApiModelProperty(value = "结果地址，loading页长链")
    protected String longResultUrl;
}

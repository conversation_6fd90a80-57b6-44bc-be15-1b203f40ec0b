package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/3/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetProcessTerminateDetailResponse extends ToString {
    @ApiModelProperty("合同操作类型")
    private Integer processOptType;
    @ApiModelProperty("终止原因")
    private String terminateReason;
    @ApiModelProperty("操作时间")
    private Long operateTime;
    @ApiModelProperty("操作人")
    private String operatorOid;
    @ApiModelProperty("操作手机号码")
    private String operatorMobile;
    @ApiModelProperty("操作人姓名")
    private String operatorName;




}

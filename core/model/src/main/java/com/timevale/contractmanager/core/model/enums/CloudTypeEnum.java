package com.timevale.contractmanager.core.model.enums;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/1/30 15:59
 */
@Getter
public enum CloudTypeEnum {


    PUBLIC_CLOUD("PUBLIC_CLOUD", "公有云"),
    DEDICATED_CLOUD("DEDICATED_CLOUD", "专属云")
    ;

    public static List<String> ALL = Arrays.asList(PUBLIC_CLOUD.getCode(), DEDICATED_CLOUD.getCode());


    private String code;

    private String description;

    CloudTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static CloudTypeEnum get(String dedicateCloudId) {
        return StringUtils.isNotBlank(dedicateCloudId) ? DEDICATED_CLOUD : PUBLIC_CLOUD;
    }

    public static String getName(String dedicateCloudId) {
        return Optional.ofNullable(get(dedicateCloudId)).map(CloudTypeEnum::getDescription).orElse("");
    }
}

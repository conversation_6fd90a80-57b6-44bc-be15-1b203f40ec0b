package com.timevale.contractmanager.core.model.dto.request;

import com.alibaba.fastjson.JSONObject;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/1 15:56
 */
@Data
public class MultiCloudDownloadHaveDataRequest extends ToString {

    /**
     * @see com.timevale.contractmanager.common.service.enums.grouping.DownloadSourceEnum
     */
    @ApiModelProperty("下载来源")
    private Integer source;

    @ApiModelProperty("请求")
    private JSONObject request;

    @ApiModelProperty("是否支持设置了附件权限的合同， 默认不支持")
    private boolean supportFileAuth;

}

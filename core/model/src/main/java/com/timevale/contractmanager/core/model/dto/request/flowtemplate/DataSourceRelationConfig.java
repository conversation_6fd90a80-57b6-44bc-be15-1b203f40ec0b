package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceRelationConfig {
    /** 关联的数据源id */
    private String dataSourceId;

    /** 允许签署完成文件上传评论区 */
    private Boolean signedReturnComment;

    /** 允许签署文件来自评论区 */
    private Boolean signFileFromComment;

    /** 允许合同附件来自评论区 */
    private Boolean attachmentFromComment;

    /** 允许修改来自数据源的签署人 */
    private Boolean allowModifyParticipantFromDataSource;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@Data
@ApiModel("合同流程撤回参数")
@Valid
public class ProcessRevokeRequest extends ToString {

    @NotBlank(message = "撤回原因不能为空")
    @ApiModelProperty(value = "撤回原因")
    private String revokeReason;

    @ApiModelProperty(value = "操作人账号id", hidden = true)
    private String accountId;

    @ApiModelProperty(value = "操作人所属主体id")
    private String subjectId;
}

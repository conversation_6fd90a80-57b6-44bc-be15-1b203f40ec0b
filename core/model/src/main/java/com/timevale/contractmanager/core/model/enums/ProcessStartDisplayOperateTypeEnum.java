package com.timevale.contractmanager.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/1/26 10:36
 */
public enum ProcessStartDisplayOperateTypeEnum {

    NORMAL("NORMAL", "正常"),
    NO_VIP("NO_VIP", "vip不支持"),
    NO_PRIVILEGE("NO_PRIVILEGE", "权限不支持"),
            ;
    ProcessStartDisplayOperateTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Getter
    private String code;
    private String desc;
}

package com.timevale.contractmanager.core.model.enums;

import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.RELATE_PROCESS_TODO_TYPE_NOT_SUPPORT;

/**
 * 待办类型枚举
 *
 * <AUTHOR>
 * @since 2021-11-24 14:35
 */
@Getter
@AllArgsConstructor
public enum TodoTypeEnum {
    WAIT_SIGN(1, ProcessStatusEnum.SIGNING.getStatus(), "待我签署"),
    WAIT_FILL(2, ProcessStatusEnum.WRITING.getStatus(), "待我填写"),
    WAIT_APPROVAL(30, -1, "待我审批"),
    WAIT_APPROVAL_NEW(3, 3, "新版审批"),
    WAIT_APPROVAL_OLD(4, 3, "旧版审批"),

    //用印审批 + 合同审批
    WAIT_APPROVAL_ALL(5, 0, "待我审批"),

    WAIT_TA(6, -1, "待他人操作"),

    WAIT_FORM(7, -1, "待处理表单"),

    FULFILLMENT(20, -1, "履约提醒"),

    ;

    /** 待办类型 */
    private Integer type;

    /** 对应底层服务的待办类型编码 */
    private Integer bizType;

    /** 待办类型文案 */
    private String label;

    public static TodoTypeEnum getByType(Integer type) {
        return Arrays.stream(TodoTypeEnum.values())
                .filter(todoTypeEnum -> todoTypeEnum.getType().equals(type))
                .findFirst()
                .orElseThrow(
                        () ->
                                new BizContractManagerException(
                                        RELATE_PROCESS_TODO_TYPE_NOT_SUPPORT, type));
    }

    public static Integer getBizType(Integer type) {
        return getByType(type).getBizType();
    }

    /**
     * 是否在合同处理流程中
     *
     * @param type
     * @return
     */
    public static boolean isProcess(Integer type) {
        return WAIT_SIGN.getType().equals(type) || WAIT_FILL.getType().equals(type) || WAIT_TA.getType().equals(type);
    }
}

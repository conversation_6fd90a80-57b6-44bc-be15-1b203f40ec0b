package com.timevale.contractmanager.core.model.dto.request.process;

import com.timevale.contractmanager.core.model.bo.fda.FDASignatureConfig;
import com.timevale.footstone.rpc.enums.SignPlatformEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/5 17:33
 */
@Data
public class ProcessesCheckSignSetUpRequest extends ToString {


    /********** 流程维度参数 ************/

    /**
     * @see  com.timevale.contractmanager.core.model.enums.ProcessStartScene
     */
    @NotNull
    private Integer scene;

    @ApiModelProperty(value = "专属云项目Id")
    private String dedicatedCloudId;

    /**
     * @see com.timevale.contractmanager.common.service.enums.ProcessBusinessType
     */
    private Integer businessType;

    /**
     * 签署模式
     */
    private String signMode;

    @Deprecated
    @ApiModelProperty(value = "平台类型")
    private Integer platform = SignPlatformEnum.STANDARD_WEB.getPlatform();

    /**
     * 流程模版id
     */
    private String flowTemplateId;

    @ApiModelProperty(value = "true多对多")
    private Boolean manyToMany;



    /**
     * 参与方数据
     */
    @NotEmpty
    private List<ProcessesCheckParticipantSetUpRequest> participants;


    /**
     * @see com.timevale.contractmanager.common.service.enums.ProcessDataTagEnum
     */
    private List<String> tags;

    /**
     * 签署设置
     */
    private List<String> setUpTypes;

    /**
     * fda签名配置
     */
    private FDASignatureConfig fdaSignatureConfig;
}

package com.timevale.contractmanager.core.model.dto.request.grouping.menu;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 创建菜单操作请求
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class CreateMenuRequest extends ToString {

    @ApiModelProperty(value = "目录名称", required = true)
    @NotBlank(message = "name不能为空")
    @Length(max = 50, message = "名字长度不能大于50")
    @Pattern(regexp = "[^\\*\\:\\\"\\*\\?\\|\\\\\\/\\>\\<]+", message = "不能包含特殊字符:* : \" ? | \\ / > <")
    private String name;

    @ApiModelProperty(value = "父节点ID，对应父目录的menuId,根目录为空")
    private String parentMenuId;

    @ApiModelProperty(value = "绑定的台账id")
    private String bindingFormId;

    @ApiModelProperty(value = "是否统一台账")
    private Integer unityForm;

    @ApiModelProperty(value = "当前操作用户oid", hidden = true)
    private String accountId;
}

package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.Preference;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-17 17:33
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "偏好设置响应数据")
public class PreferenceResponse extends ToString {

    /**
     * 偏好设置
     */
    @ApiModelProperty(value = "偏好设置")
    private List<Preference> preferences;

}


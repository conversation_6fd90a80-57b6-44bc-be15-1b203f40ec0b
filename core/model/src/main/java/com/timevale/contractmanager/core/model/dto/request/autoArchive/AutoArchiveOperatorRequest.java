package com.timevale.contractmanager.core.model.dto.request.autoArchive;

import com.timevale.contractmanager.common.service.enums.autoarchive.OperationConditionStrategyEnum;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveOperator;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;

/**
 * @Author:jianyang
 * @since 2021-05-06 16:59
 */
@Data
public class AutoArchiveOperatorRequest extends ToString {
	@ApiModelProperty(value = "目录名称", required = true)
	@NotBlank(message = "name不能为空")
	@Length(max = 50, message = "名字长度不能大于50")
	private String name;

	@ApiModelProperty(value = "父节点ID，对应父目录的menuId,根目录为空")
	private String parentMenuId;

	@ApiModelProperty(value = "绑定的台账id")
	private String bindingFormId;

	@ApiModelProperty(value = "规则状态0:开启,1:关闭,2:失效,3:运行中")
	private int status = 1;

	@ApiModelProperty(value = "自动归档条件")
	private List<AutoArchiveOperator> conditions;

	@ApiModelProperty(value = "移除不符合规则合同:0开启,1:关闭")
	private Integer revomeContract;

	@ApiModelProperty(value = "已存在的分类Id")
	private String oldMenuId;

	@ApiModelProperty(value = "解绑旧台账")
	private Boolean unBindOldAiRule;

	@ApiModelProperty(value = "是否统一台账")
	private Integer unityForm;


	/**
	 * 校验自动归档条件标题的长度大小
	 */
	public void checkValidityParam(){
		if(CollectionUtils.isEmpty(conditions)){
			return;
		}

		for (AutoArchiveOperator condition : conditions) {
			OperationConditionStrategyEnum strategyEnum = OperationConditionStrategyEnum.convert(condition.getFieldId());
			if(OperationConditionStrategyEnum.CONTRACT_TITLE==strategyEnum
					&&CollectionUtils.isNotEmpty(condition.getChildOperators())&&condition.getChildOperators().size()>50){
				throw new BizContractManagerException(CONTRACT_TITLE_EXCEED);
			}
		}

	}
}

package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.StringRegex;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Set;

import static com.timevale.contractmanager.common.utils.StringValidUtil.REG_GENERAL_LIMIT;
import static com.timevale.contractmanager.common.utils.StringValidUtil.REG_GENERAL_LIMIT_MESSAGE;

/**
 * <AUTHOR>
 * @since 2022/11/23
 */
@Data
public class OpponentIndividualCreateOuterRequest extends ToString {

    @ApiModelProperty("联系方式，手机号或邮箱")
    @NotBlank(message = "手机号/邮箱不能为空")
    private String contact;

    @ApiModelProperty("个人名")
    @NotBlank(message = "姓名不能为空")
    @Length(max = 50, message = "姓名不能超过50个字")
    private String individualName;

    @ApiModelProperty("关联企业相对方uuid列表")
    @Size(max = 10,message = "最多关联10个企业")
    private Set<String> organizationIds;

    @ApiModelProperty("备注")
    @Length(max = 200, message = "备注不能超过200个字")
    @StringRegex(regex = REG_GENERAL_LIMIT, message = "备注" + REG_GENERAL_LIMIT_MESSAGE)
    private String desc;
}

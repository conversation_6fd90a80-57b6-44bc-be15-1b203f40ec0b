package com.timevale.contractmanager.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020-05-25
 */
@Getter
public enum ProcessExcelEnum {
    PSN_NAME("姓名", "name", "签署人姓名", 0),
    PSN_ACCOUNT("手机/邮箱", "account", "签署人手机/邮箱", 1),
    ORG_NAME("企业名称", "orgName", "企业名称", 2),
    TASK_NAME("合同名称（选填）", "taskName", "合同名称（选填）",3),
    ;

    private String name;
    private String value;
    private String desc;
    private int index;

    ProcessExcelEnum(String name, String value, String desc, int index) {
        this.name = name;
        this.value = value;
        this.desc = desc;
        this.index = index;
    }
}

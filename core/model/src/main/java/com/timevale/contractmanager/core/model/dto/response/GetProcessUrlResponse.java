package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.enums.ProcessCurrentStatusEnum;
import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> on 2019/7/30
 */
@Data
public class GetProcessUrlResponse extends ToString {

    @ApiModelProperty(value = "流程跳转地址", required = true)
    private String url;

    @ApiModelProperty(value = "流程跳转长链地址")
    private String longUrl;

    /**
     * 请参考{@link ProcessCurrentStatusEnum }
     */
    @ApiModelProperty(value = "流程当前所处状态 1-协作填写 2-签署 3-合同审批", required = true)
    private Integer status;

    @ApiModelProperty(value = "子流程id")
    private String subProcessId;
}

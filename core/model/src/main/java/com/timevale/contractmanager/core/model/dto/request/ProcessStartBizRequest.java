package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.enums.ProcessBusinessType;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.fda.FDASignatureConfig;
import com.timevale.contractmanager.core.model.dto.process.ProcessRemark;
import com.timevale.contractmanager.core.model.dto.process.StartDataSource;
import com.timevale.contractmanager.core.model.dto.process.config.ProcessParticipantSetUpBizRuleConfig;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
public class ProcessStartBizRequest extends ProcessStartBaseRequest {
    /** 操作人账号id， 默认操作人和发起人一致 */
    private String operatorId;
    /** 发起人账号id */
    private String accountId;
    /** 发起主体账号id */
    private String tenantId;
    /** 付费主体账号 */
    private UserAccount payerAccount;
    /** 关联模板id */
    private String refFlowTemplateId;
    /** 临时模板id */
    private String tempFlowTemplateId;
    /** 是否重置合同流程 */
    private boolean resetProcess = false;
    /** 原合同id 重新发起/解约/续签流程时使用 */
    private String originProcessId;
    /** 原合同文件id, 解约/续签场景使用 */
    private List<String> originFileIds;
    /** 关联合同列表 */
    private List<String> relationProcessIds;
    /** 合同保密类型 */
    private Integer secretType;
    /** 是否允许解约，默认允许 */
    private Boolean allowRescind = true;
    /** 保密可见人类型 */
    private Set<String> visibleAccounts;
    /** 跳过发起权限校验, 默认为否 */
    private Boolean skipStartValid = false;
    /** 发起成功后是否删除临时流程, 默认删除 */
    private boolean deleteTempFlowTemplate = true;
    /** 三方审批id */
    private String thirdApprovalInstanceId;
    /**
     * 是否走异步发起
     */
    private Boolean asyncStart = false;

    /** 是否需要进行印章授权校验 */
    private boolean needSealAuthCheck;

    /**
     * @see com.timevale.contractmanager.common.service.enums.ProcessDataTagEnum
     * 承接一些后端无法识别后端的场景
     */
    private List<String> tags;

    private ProcessParticipantSetUpBizRuleConfig bizRuleConfig = new ProcessParticipantSetUpBizRuleConfig();

    /** 钉钉审批ID，中间页使用 */
    private String instanceId;

    /**
     * 发起时的数据源
     */
    private StartDataSource startDataSource;

    /**
     * 获取操作人账号id
     * @return
     */
    public String getOperatorId() {
        return StringUtils.isBlank(operatorId) ? accountId : operatorId;
    }
    /**
     * 合同备注
     */
    private List<String> remarks;
    /**
     * 新版合同备注
     */
    private List<ProcessRemark> processRemarks;

    /**
     * fda配置
     */
    private FDASignatureConfig fdaSignatureConfig;

    /**
     * 判断是否批量发起
     *
     * @return
     */
    public boolean checkBatchStart() {
        if (CollectionUtils.isEmpty(getParticipants())) {
            return false;
        }
        for (ParticipantBO participant : getParticipants()) {
            if (CollectionUtils.isEmpty(participant.getInstances())) {
                continue;
            }
            // 如果非或签方有多个实例，说明是批量发起
            if (!ParticipantModeEnum.isOrSign(participant.getParticipantMode())
                    && participant.getInstances().size() > 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验是否重置合同流程场景的重新发起
     * @return
     */
    public boolean checkResetProcess() {
        return ProcessBusinessType.RESTART.getBusinessType().equals(getBusinessType())
                && isResetProcess() && StringUtils.isNotBlank(getOriginProcessId());
    }


}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.core.model.dto.request.process.ProcessListRequest;
import io.swagger.annotations.ApiModel;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 流程列表查询参数
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@ApiModel(value = "流程列表查询参数")
@Data
public class ProcessListSearchRequest extends ProcessListRequest {
    /** 用户id */
    private String accountId;
    /** 主体id */
    private String subjectId;

    @NotNull(message = "查询类型不能为空")
    public Integer getDocQueryType() {
        return super.getDocQueryType();
    }
}

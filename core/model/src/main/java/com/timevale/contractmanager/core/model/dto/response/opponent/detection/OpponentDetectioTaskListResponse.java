package com.timevale.contractmanager.core.model.dto.response.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-04 19:54
 */
@Data
public class OpponentDetectioTaskListResponse extends ToString {
	@ApiModelProperty("总数")
	private long total;

	@ApiModelProperty("任务")
	private List<OpponentDetectioTaskResponse> taskResponses;
}

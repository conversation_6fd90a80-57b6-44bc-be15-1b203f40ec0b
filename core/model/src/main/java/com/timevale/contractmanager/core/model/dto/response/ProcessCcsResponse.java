package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 * @date 2022/1/10
 */
@ApiModel("抄送人列表结果")
@Data
public class ProcessCcsResponse extends ToString {

    private List<UserBO> ccs;

    public List<UserBO> getCcs() {
        return Optional.ofNullable(ccs).orElse(Lists.newArrayList());
    }
}

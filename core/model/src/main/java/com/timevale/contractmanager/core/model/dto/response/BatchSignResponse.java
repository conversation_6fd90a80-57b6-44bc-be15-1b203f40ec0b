package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class BatchSignResponse extends ToString {
    @ApiModelProperty(value = "跳转地址")
    private String redirectUrl;
}

package com.timevale.contractmanager.core.model.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.timevale.contractmanager.core.model.bo.FileDetailBO;
import com.timevale.contractmanager.core.model.bo.ProcessStartBO;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.assertj.core.util.Lists;

import java.util.Collections;
import java.util.List;

/**
 * 流程发起详情信息
 *
 * <AUTHOR>
 * @since 2019/11/6
 */
@Getter
@Setter
public class ProcessStartDetailResponse extends ProcessStartBO<FileDetailBO> {

    /** 流程模板的状态 */
    @JsonIgnore private Integer status;

    /** 创建时间 */
    @JsonIgnore private Long createTime;
    /** 更新时间 */
    @JsonIgnore private Long updateTime;

    /** 流程模板类型 请参考{@link com.timevale.doccooperation.service.enums.FlowTemplateTypeEnum } */
    @ApiModelProperty(value = "流程模板类型 1-普通 2-草稿 3-临时")
    private Integer flowTemplateType;

    /** 流程模板版本号 请参考 {@link com.timevale.doccooperation.service.enums.FlowTemplateVersionEnum } */
    @ApiModelProperty(value = "流程模板版本号")
    private Integer flowTemplateVersion;

    @ApiModelProperty(value = "是否使用ai手绘 0未使用 1使用")
    private Integer aiDrawFlag;

    @ApiModelProperty(value = "是否使用高级控件 0未使用 1使用")
    private Integer advancedComponentFlag;

    @ApiModelProperty(value = "流程模板标签")
    private String label;

    @ApiModelProperty(value = "是否外部授权共享模板")
    private Boolean shared;


    @ApiModelProperty(value = "模板schema类型")
    private Integer schemaType;
    @ApiModelProperty(value = "epaas模板标记")
    private boolean epaasTag;

    @ApiModelProperty(value = "合同审批模板")
    @Deprecated
    private ProcessApprovalTemplateVO processApprovalTemplate;

    @ApiModelProperty(value = "合同审批模板列表")
    private List<ProcessApprovalTemplateVO> processApprovalTemplateList;

    @ApiModelProperty("发起类型 0直接发起 1模版发起 2使用模版扫码发起 3本地文件扫码发起 4钉钉附件审批发起 5钉钉模板关联审批单发起 6钉钉ISV直接发起")
    private Integer startType;

    @ApiModelProperty(value = "签署模式")
    private String signMode;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProcessApprovalTemplateVO extends ToString {
        @ApiModelProperty(value = "审批模板id")
        private String approvalTemplateId;

        @ApiModelProperty(value = "审批模板名称")
        private String approvalTemplateName;
    }
    
    public List<ProcessApprovalTemplateVO> getProcessApprovalTemplateList() {
        if (CollectionUtils.isNotEmpty(this.processApprovalTemplateList)) {
            return this.processApprovalTemplateList;
        }
        
        if (this.processApprovalTemplate == null) {
            return Lists.newArrayList();
        }
        
        return Collections.singletonList(this.processApprovalTemplate);
    }
}

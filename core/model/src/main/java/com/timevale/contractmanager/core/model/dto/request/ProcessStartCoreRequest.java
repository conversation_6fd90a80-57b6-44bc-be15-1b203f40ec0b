package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.bean.ProcessSecretConfigBean;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.fda.FDASignatureConfig;
import com.timevale.contractmanager.core.model.dto.process.ProcessRemark;
import com.timevale.contractmanager.core.model.dto.process.StartCoreDataSource;
import com.timevale.contractmanager.core.model.dto.process.config.ProcessStartCoreBizRuleConfig;
import com.timevale.contractmanager.core.model.dto.user.UserAccount;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.mandarin.base.util.CollectionUtils;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
public class ProcessStartCoreRequest extends ProcessStartBaseRequest {

    @NotNull(message = "操作人账号信息不能为空")
    private UserAccountDetail operatorAccount;

    /** 发起人 */
    @NotNull(message = "发起人账号信息不能为空")
    private UserAccountDetail initiatorAccount;

    /** 租户 */
    @NotNull(message = "发起主体账号信息不能为空")
    private UserAccountDetail tenantAccount;

    /** 付费主体 */
    @NotNull(message = "付费主体账号信息不能为空")
    private UserAccount payerAccount;

    /** 是否可以进行流程核验 */
    private boolean canProcessCheck;

    /** 是否需要进行发起埋点， 默认不需要 */
    private boolean needStartSensor;

    /** 是否rpc发起流程 */
    private boolean rpcStart;

    /** 是否重置发起流程 */
    private boolean resetStart;

    /** 是否需要进行相对方风控 */
    private boolean checkOpponentRisk;

    /**签署区大小是否自适应*/
    private String signSealSizeType;

    /** 合同保密配置 */
    private ProcessSecretConfigBean processSecretConfig;

    /**
     * 核心配置，从业务层带过来
     * 需要从上层业务传递过来
     */
    private ProcessStartCoreBizRuleConfig startCoreConfig = new ProcessStartCoreBizRuleConfig();

    /**
     * 发起时的数据源
     */
    private StartCoreDataSource startDataSource;

    /**
     * 合同备注
     */
    private List<String> remarks;

    /**
     * 新版备注
     */
    private List<ProcessRemark> processRemarks;

    /**
     * fda配置
     */
    private FDASignatureConfig fdaSignatureConfig;

    /** 获取操作人账号 */
    public UserAccountDetail getOperatorAccount() {
        return null == operatorAccount ? initiatorAccount : operatorAccount;
    }

    /**
     * 判断参与方是否指定通知方式
     * @return
     */
    public boolean assignedParticipantNoticeType() {
        List<ParticipantBO> participants = getParticipants();
        if (CollectionUtils.isEmpty(participants)) {
            return false;
        }
        return participants.stream().anyMatch(i -> null != i.getNoticeTypes());
    }
}

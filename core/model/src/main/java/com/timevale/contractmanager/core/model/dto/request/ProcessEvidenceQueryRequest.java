package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("合同流程出证列表查询参数")
@Valid
public class ProcessEvidenceQueryRequest extends ToString {

    @ApiModelProperty("应用id")
    private String appId;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty(value = "查询用户账号id")
    private String accountId;

    @ApiModelProperty(value = "流程名称全模糊搜索", position = 1)
    private String title;

    @ApiModelProperty(value = "发起方名称/手机号/邮箱全模糊搜索", position = 1)
    private String initiatorKeyWord;

    @ApiModelProperty(value = "签署方名称/手机号/邮箱全模糊搜索", position = 1)
    private String signerKeyWord;

    @ApiModelProperty("流程id")
    private String flowId;

    @ApiModelProperty("合同流程id")
    private String processId;

    @ApiModelProperty("发起时间段的起始时间")
    private Long initiateTimeFrom;

    @ApiModelProperty("发起时间段的结束时间")
    private Long initiateTimeTo;

    @Min(value = 0, message = "page >=0")
    @NotNull(message = "page 不能为空")
    @ApiModelProperty(value = "页码", required = true, position = 1)
    private Integer page;

    @NotNull(message = "pageSize 不能为空")
    @Min(value = 0, message = "pageSize >=0")
    @ApiModelProperty(value = "每页大小", required = true, position = 1)
    private Integer pageSize;

    /** @see com.timevale.signflow.search.docSearchService.enums.ProcessStatusEnum */
    @ApiModelProperty(value = "合同流程状态, 多个以逗号分隔, 8-已完成, 11-解约中, 12-部分解约, 13-已解约, 14-续签中, 15-部分续签, 16-已续签")
    private String processStatus;
}

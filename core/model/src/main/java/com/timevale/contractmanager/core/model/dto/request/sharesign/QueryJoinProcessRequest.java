package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023-10-18 17:20
 */
@Data
public class QueryJoinProcessRequest extends ToString {
    @ApiModelProperty(value = "扫码签任务id", required = true)
    @NotBlank(message = "扫码签任务id不能为空")
    private String shareSignTaskId;
    @ApiModelProperty(value = "扫码签扫码id", required = true)
    @NotBlank(message = "扫码签扫码id不能为空")
    private String shareScanId;
    @ApiModelProperty(value = "企业名称")
    private String orgName;
    @ApiModelProperty(value = "签署人oid", hidden = true)
    private String accountOid;
}

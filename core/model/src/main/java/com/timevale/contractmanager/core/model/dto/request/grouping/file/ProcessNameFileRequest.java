package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: duhui
 * @since: 2021/6/30 3:49 下午
 **/
@Data
public class ProcessNameFileRequest {
    @ApiModelProperty(value = "合同名称", required = true)
    @NotBlank(message = "合同名称不能为空")
    private String processName;
    @ApiModelProperty(value = "合同id", required = true)
    @NotBlank(message = "合同id不能为空")
    private String processId;
    @ApiModelProperty(value = "文件id", required = true)
    @NotBlank(message = "文件id不能为空")
    private String fileId;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.footstone.rpc.enums.SignPlatformEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取流程详情地址（支持子流程自动调度流转）
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@ApiModel("获取自动调度流转的流程详情地址请求参数")
public class GetProcessDispatchUrlRequest extends ToString {

    /** 获取哪个端的地址 请参考{@link com.timevale.footstone.rpc.enums.SignPlatformEnum } */
    @ApiModelProperty(
            "详情地址对应的端，1-开放服务h5 2-支付宝小程序 3-微信小程序 4-标准签H5 5-标准签WEB 6-开放服务WEB 7-IOS标准签APP 8-Android标准签APP，默认标准签WEB")
    private Integer platform;

    @ApiModelProperty("回调地址")
    private String redirectUrl;

    @ApiModelProperty("三方端上的登录token, 用于获取地址后可以免登打开")
    private String token;

    public Integer getPlatform() {
        return null == platform ? SignPlatformEnum.STANDARD_WEB.getPlatform() : platform;
    }
}

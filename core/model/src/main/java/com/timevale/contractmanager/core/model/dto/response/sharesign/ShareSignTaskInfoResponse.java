package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.contractmanager.core.model.dto.response.ProcessNodeUsersResponse;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-05
 */
@Data
public class ShareSignTaskInfoResponse extends ToString {
    @ApiModelProperty("扫码签任务Id")
    private String taskId;
    @ApiModelProperty("扫码签任务名称")
    private String taskName;
    @ApiModelProperty("扫码签任务类型，1-单方扫码，2-多方扫码")
    private Integer taskType;
    @ApiModelProperty("任务完成数")
    private int taskDone;
    @ApiModelProperty("任务参与数")
    private int taskNum;
    @ApiModelProperty("任务上限数")
    private Integer taskTotal;
    @ApiModelProperty("任务状态，0-关闭，1-开启")
    private Integer taskStatus;
    @ApiModelProperty("发起人姓名")
    private String accountName;
    @ApiModelProperty("发起主体名称")
    private String subjectName;
    @ApiModelProperty("发起主体id")
    private String subjectId;
    @ApiModelProperty("任务发起时间")
    private Date startTime;
    @ApiModelProperty("任务截止时间")
    private Date endTime;
    @ApiModelProperty("分享类型为批量扫码签时，share_biz_id和批量合同列表里的groupId维持一致, 单个扫码签时，share_biz_id为processId")
    private String shareBizId;
    @ApiModelProperty("扫码签H5详情页:无二维码的页面")
    private String h5DetailUrl;

    @ApiModelProperty(value = "是否私密分享")
    private Boolean privateShare;

    @ApiModelProperty("任务节点信息")
    private TaskNode taskNode;

    @ApiModelProperty("是否允许用户多次签署")
    private Boolean repeatSign;

    @Data
    public static class TaskNode {

        @ApiModelProperty("发起人")
        ProcessNodeUsersResponse.ProcessAccountInfo startAccount;

        @Deprecated
        @ApiModelProperty("所有填写人-有序")
        List<ProcessNodeUsersResponse.ProcessAccountInfo> fillAccountList;

        @Deprecated
        @ApiModelProperty("所有签署人-可能无序")
        List<ProcessNodeUsersResponse.ProcessAccountInfo> signAccountList;

        @ApiModelProperty("所有填写人-有序")
        List<ProcessNodeUsersResponse.ProcessNodeAccount> cooperatorAccounts;

        @ApiModelProperty("所有签署人-可能无序")
        List<ProcessNodeUsersResponse.ProcessNodeAccount> signerAccounts;

        @ApiModelProperty("所有合同审批人")
        List<ProcessNodeUsersResponse.ProcessNodeAccount> approverAccounts;
    }
}

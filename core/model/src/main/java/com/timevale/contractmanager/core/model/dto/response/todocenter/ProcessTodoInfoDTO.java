package com.timevale.contractmanager.core.model.dto.response.todocenter;

import com.timevale.contractmanager.common.service.bean.ProcessAccount;
import com.timevale.contractmanager.common.service.bean.ProcessNodeAccount;
import com.timevale.contractmanager.common.service.bean.process.ContractFileBean;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 待办流程信息
 *
 * <AUTHOR>
 * @since 2021-11-24 14:57
 */
@Data
public class ProcessTodoInfoDTO extends ToString {
    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("发起时间")
    private Long processCreateTime;

    @ApiModelProperty("流程发起人")
    private ProcessNodeAccount initiatorAccount;

    @ApiModelProperty("所有参与人")
    List<ProcessNodeAccount> participantAccountList;

    @ApiModelProperty("需要处理的人")
    List<ProcessAccount> currentOperatorList;

    @ApiModelProperty(value = "流程类型")
    private Integer processType;

    @ApiModelProperty(value = "是否转交合同")
    private boolean transfer;

    @ApiModelProperty(value = "合同文件")
    List<ContractFileBean> contractFiles;

    @ApiModelProperty(value = "签署模式")
    private String signMode;

    @ApiModelProperty("专属云项目Id")
    private String dedicatedCloudId;

    @ApiModelProperty("流程状态")
    private Integer processStatus;
}

package com.timevale.contractmanager.core.model.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * 查询合同保密配置信息返回参数
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Data
@ApiModel("查询合同保密配置信息返回参数")
public class QueryProcessSecretConfigResponse {

    @ApiModelProperty("合同保密类型")
    private Integer secretType;

    @ApiModelProperty("保密文件id列表")
    private Set<String> secretFileIds;

    @ApiModelProperty("保密文件可见人Oid列表")
    private Set<String> visibleAccounts;
}

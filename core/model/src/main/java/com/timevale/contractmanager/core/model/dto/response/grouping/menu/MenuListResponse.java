package com.timevale.contractmanager.core.model.dto.response.grouping.menu;

import com.timevale.contractmanager.common.service.result.grouping.PermissionInfo;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 菜单列表返回参数
 *
 * @author: xuanzhu
 * @since: 2019-09-26 20:49
 */
@Data
public class MenuListResponse extends ToString {

    @ApiModelProperty("具有权限的菜单列表")
    @HasTranslateField
    private List<MenuListDTO> menuList;

    @ApiModelProperty("是否具备菜单操作权限(CRUD菜单使用)，true-有，false-无")
    private boolean isOperatorPermission;

    @ApiModelProperty("企业合同-归档合同操作列表")
    @HasTranslateField
    private List<PermissionInfo> archivedPermissionCodes;


}

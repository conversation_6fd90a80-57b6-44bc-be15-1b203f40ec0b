package com.timevale.contractmanager.core.model.dto.datasource;

import lombok.Data;

/**
 * 三方数据源文件模型
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
public class FileDataSourceBO {
	/**
	 * 文件key
	 */
	private String fileKey;

	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 文件id
	 */
	private String fileId;

	/**
	 * 数据源渠道
	 */
	private String dataSourceChannel;

	/**
	 * 字段id
	 */
	private String field;

	/**
	 * 字段名称
	 */
	private String fieldName;



}

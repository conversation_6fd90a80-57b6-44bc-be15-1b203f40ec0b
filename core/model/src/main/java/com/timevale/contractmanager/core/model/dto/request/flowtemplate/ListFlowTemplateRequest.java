package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import com.timevale.saas.common.validator.StringCheck;
import com.timevale.saas.common.validator.enums.StringCheckType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 流程模板列表入参
 *
 * <AUTHOR>
 * @since 2020-12-18 14:58
 */
@Data
@ToString
public class ListFlowTemplateRequest {
    @ApiModelProperty(value = "页码,默认1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小，默认20")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "流程模板名称")
    @StringCheck(types = {StringCheckType.EMOJI, StringCheckType.UTF8MB4}, message = "请勿使用表情符或异形字符搜索！")
    private String flowTemplateName;

    @ApiModelProperty(value = "是否仅查询可使用的模板 false-查询有查看权限的模板 true-查询有使用权限的模板")
    private boolean queryUse;

    @ApiModelProperty(value = "状态,多个以逗号分隔,默认0,1 0-停用 1-启用")
    private String status;

    @ApiModelProperty(value = "是否查询带有label标签的流程模板 默认false true-查询有label标签的模板 false-不查询有label标签的模板")
    private boolean queryLabel;

    @ApiModelProperty(value = "流程模板标签 仅queryLabel为true时生效 查询对应标签的流程模板")
    private String label;

    @ApiModelProperty(value = "是否包含共享模板")
    private boolean containShared = false;


    @ApiModelProperty(value = "是否需要展示动态模板")
    private boolean containsDynamic = false;

    /**
     * 废弃，现钉签和PC已统一，后续找个迭代下掉
     */
    @Deprecated
    @ApiModelProperty(value = "要排除的流程模板列表,多个用逗号隔开")
    private String excludeLabels;


    @ApiModelProperty(value = "模板分类")
    private String categoryId;

    /**
     * 流程模板所属分类ID
     */
    @ApiModelProperty(value = "是否需要展示动态模板")
    private List<String> categoryIds;

    @ApiModelProperty(value = "是否过滤带参与人附件配置模版")
    private boolean filterAttachment = false;

    @ApiModelProperty(value = "是否需要展示参与方设置了固定企业的模板")
    private boolean containsDesignatedOrg = false;
}

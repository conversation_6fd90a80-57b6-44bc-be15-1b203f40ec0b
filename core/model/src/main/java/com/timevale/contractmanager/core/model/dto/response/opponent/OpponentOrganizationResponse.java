package com.timevale.contractmanager.core.model.dto.response.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @author: huifeng
 * @since: 2021-01-22 11:41
 **/
@Data
@Builder
public class OpponentOrganizationResponse extends ToString {

    @ApiModelProperty("相对方企业id")
    private String organizationId;

    @ApiModelProperty("相对方企业名")
    private String organizationName;

    @ApiModelProperty("认证状态 0-未实名；1-实名中；2-已实名；3-已注销")
    private Integer authorizeType;

    @ApiModelProperty("备注")
    private String desc;

    @ApiModelProperty("相对方成员人数")
    private Integer memberCount;

    @ApiModelProperty("和相对方企业进行中的合同数")
    private Integer processingContractCount;

    @ApiModelProperty("创建来源合同流程id")
    private String createProcessId;

    @ApiModelProperty("创建来源名称")
    private String createName;

    @ApiModelProperty("创建来源联系人")
    private String createContact;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("黑名单状态")
    private Integer riskLevel;

    @ApiModelProperty("相对方企业账号oid")
    private String organizationAccountId;

    @ApiModelProperty("相对方企业账号gid")
    private String organizationAccountGid;

    @ApiModelProperty("证件号")
    private String socialCreditCode;

    @ApiModelProperty("法定代表人姓名")
    private String legalPersonName;

    /** {@link com.timevale.contractmanager.core.model.enums.OpponentEntityCreditCodeTypeEnum}*/
    @ApiModelProperty("证件类型 1-统一社会信用代码、2-工商注册号、3-其他")
    private Integer creditCodeType;
}

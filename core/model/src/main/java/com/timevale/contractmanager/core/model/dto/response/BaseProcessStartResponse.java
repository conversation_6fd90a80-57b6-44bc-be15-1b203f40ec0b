package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 各start 响应结果父类
 * <AUTHOR>
 * @since 2020/3/23
 */
@Getter
@Setter
@NoArgsConstructor
public class BaseProcessStartResponse extends ToString {

    @ApiModelProperty(value = "流程id")
    protected String processId;

    @ApiModelProperty(value = "结果地址，签署地址/填写地址短链")
    protected String resultUrl;

    public BaseProcessStartResponse(String processId, String resultUrl) {
        this.processId = processId;
        this.resultUrl = resultUrl;
    }
}

package com.timevale.contractmanager.core.model.dto.response.base.process;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by t<PERSON>lei on 2022/9/20
 */
@Data
public class BaseBizAccountVO {

    @ApiModelProperty("gid")
    private String gid;

    @ApiModelProperty("oid")
    private String oid;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("nickname")
    private String nickname;

    @ApiModelProperty("true 是企业")
    private Boolean organ;

    @ApiModelProperty("mobile")
    private String mobile;

    @ApiModelProperty("email")
    private String email;

    private Boolean deleted;
}

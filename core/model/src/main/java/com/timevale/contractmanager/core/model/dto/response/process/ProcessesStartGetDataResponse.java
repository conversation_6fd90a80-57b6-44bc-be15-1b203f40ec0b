package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.contractmanager.core.model.bo.FileBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/23 10:06
 */
@Data
public class ProcessesStartGetDataResponse {

    @ApiModelProperty(value = "文件列表,包括合同文件和附件")
    @Valid
    private List<FileBO> files;
}

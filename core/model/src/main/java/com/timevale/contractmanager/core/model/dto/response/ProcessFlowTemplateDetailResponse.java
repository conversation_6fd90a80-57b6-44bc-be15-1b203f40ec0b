package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.bo.CategoryInfoBO;
import com.timevale.contractmanager.core.model.bo.WaterMarkBO;
import com.timevale.contractmanager.core.model.bo.fda.FDASignatureConfig;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.DataSourceRelationConfig;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.FlowTemplateDataSourceConfig;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.InitiatorDataSourceConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程模板详情信息
 *
 * <AUTHOR>
 * @since 2021/06/03
 */
@Getter
@Setter
@ApiModel("流程模板详情信息")
public class ProcessFlowTemplateDetailResponse extends ProcessStartDetailResponse {
    @ApiModelProperty(value = "是否支持发起时添加文件")
    private Boolean supportAddDoc =false;

    @ApiModelProperty(value = "流程模板中配置的的水印模板列表")
    private List<WaterMarkBO> watermarkConfigs = new ArrayList<>();

    @ApiModelProperty(value = "草稿中记录的发起时使用的水印模板ID")
    private WaterMarkBO draftWatermarkInfo;

    @ApiModelProperty(value = "是否置顶")
    private Boolean topped;

    @ApiModelProperty(value = "所属分类")
    private List<CategoryInfoBO> categories;

    @ApiModelProperty(value = "模版数据源")
    private FlowTemplateDataSourceConfig dataSourceConfig;

    @ApiModelProperty(value = "模板数据源关联")
    private DataSourceRelationConfig dataSourceRelationConfig;

    @ApiModelProperty(value = "定时发起时间")
    private Long scheduledInitiateTime;

    @ApiModelProperty(value = "是否已提交定时发起时间")
    private Boolean startScheduledInitiate;

    @ApiModelProperty(value = "发起方数据源")
    private InitiatorDataSourceConfig initiatorDataSourceConfig;

    @ApiModelProperty(value = "fda配置")
    private FDASignatureConfig fdaSignatureConfig;
}

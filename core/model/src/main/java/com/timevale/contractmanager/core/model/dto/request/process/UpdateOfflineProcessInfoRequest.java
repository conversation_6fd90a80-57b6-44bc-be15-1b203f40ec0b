package com.timevale.contractmanager.core.model.dto.request.process;

import com.timevale.contractmanager.core.model.dto.process.OfflineProcessAccount;
import com.timevale.contractmanager.core.model.dto.process.OfflineProcessFile;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 更新线下流程信息请求参数
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Data
public class UpdateOfflineProcessInfoRequest extends ToString {

    /** 合同到期时间 */
    private Long contractValidity;

    /** 合同文件列表 */
    private List<OfflineProcessFile> contractFiles;

    /** 流程用户信息 */
    private List<OfflineProcessAccount> processAccounts;
}

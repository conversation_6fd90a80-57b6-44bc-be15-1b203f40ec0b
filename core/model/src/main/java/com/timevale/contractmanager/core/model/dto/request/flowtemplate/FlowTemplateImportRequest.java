package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.StringRegex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2021/9/8
 */
@Data
@ApiModel("导入合同流程模板请求类")
public class FlowTemplateImportRequest extends ToString {
    @NotBlank(message = "downloadUrl不能为空")
    @ApiModelProperty(value = "合同流程模板数据下载地址")
    @StringRegex(regex = "(http|https)://[a-zA-Z0-9-.]+\\.(tsign|esign)\\.cn(|:[0-9]+)(|/.*)", message = "下载地址不符合域名要求规范")
    private String downloadUrl;

    @ApiModelProperty(value = "新合同流程模板名")
    private String flowTemplateName;
}

package com.timevale.contractmanager.core.model.dto.response.flowtemplate;

import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程模板分享信息
 *
 * <AUTHOR>
 * @since 2020/11/6
 */
@ApiModel(value = "流程模板分享信息")
@Getter
@Setter
public class FlowTemplateShareInfoResponse extends ToString {

    @ApiModelProperty(value = "流程模板名称")
    private String flowTemplateName;

    @ApiModelProperty(value = "来源")
    private String source;
}

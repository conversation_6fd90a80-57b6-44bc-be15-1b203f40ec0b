package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.google.common.collect.Lists;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-05
 */
@Data
public class ShareSignTaskListResponse extends ToString {
    @ApiModelProperty("总数")
    private long total;

    /** 当前页码 */
    @ApiModelProperty(value = "当前页码")
    private Integer currPage;

    /** 页数大小 */
    @ApiModelProperty(value = "页数大小")
    private Integer pages;

    @ApiModelProperty("扫码签任务列表")
    private List<ShareSignTaskList> tasks = Lists.newArrayList();
}

package com.timevale.contractmanager.core.model.dto.request.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Value;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-04 18:06
 */
@Data
public class OpponentDetectionSaveSettingRequest extends ToString {
	@ApiModelProperty("经营范围")
	private List<String> scope;

	@ApiModelProperty("是否开启实时检测,1:开启,2:不开启")
	private Integer realTimeDetection;

	@ApiModelProperty("是否开启推送,1:开启,2:不开启")
	private Integer push;

	@ApiModelProperty("推送对象")
	private String pushObject;

	@ApiModelProperty("是否开启立即推送,1:开启,2:不开启")
	private Integer immediatelyPush;

	@ApiModelProperty("是否开启经营范围检测,1:开启,2:不开启")
	private Integer businessScopeDetection;

	@ApiModelProperty("是否系统自动保存相对方,1:是,2:否")
	private Integer autoSaveOpponent;
}

package com.timevale.contractmanager.core.model.dto.response.contractNo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 规则列表
 *
 * <AUTHOR>
 * @since 2022/10/19 4:32 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractNoRuleListResponse {

    @ApiModelProperty(value = "总数")
    private Integer total;

    @ApiModelProperty(value = "规则列表")
    private List<ContractNoRuleVO> ruleList;
}

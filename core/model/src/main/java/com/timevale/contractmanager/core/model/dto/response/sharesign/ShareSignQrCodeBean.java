package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021-02-18
 */
@Data
public class ShareSignQrCodeBean extends ToString {

    @ApiModelProperty("扫码签二维码id")
    private String shareScanId;

    @ApiModelProperty("扫码签落地页地址")
    private String shareUrl;

    @ApiModelProperty("扫码签二维码fileKey")
    private String qrCodeFileKey;

    @ApiModelProperty("扫码签二维码地址")
    private String qrCodeUrl;
}

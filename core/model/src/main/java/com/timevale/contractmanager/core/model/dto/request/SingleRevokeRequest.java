package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 单个撤销请求
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Data
public class SingleRevokeRequest extends ToString {

    @ApiModelProperty(value = "撤回理由", required = true)
    @NotBlank(message = "revokeReason不能为空")
    private String revokeReason;

    @ApiModelProperty(value = "流程id", required = true)
    @NotBlank(message = "processId不能为空")
    private String processId;

    @ApiModelProperty(value = "操作人账号id", required = true)
    @NotBlank(message = "accountId不能为空")
    private String accountId;

    @ApiModelProperty(value = "操作人所属主体id", required = true)
    @NotBlank(message = "subjectId不能为空")
    private String subjectId;

}

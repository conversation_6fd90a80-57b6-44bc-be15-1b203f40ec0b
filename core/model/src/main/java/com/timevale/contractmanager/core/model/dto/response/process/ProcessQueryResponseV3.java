package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.mandarin.common.result.BaseResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessQueryResponseV3 extends BaseResult {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("列表数据")
    private List<ProcessInfoDisplayInfoV3> processInfoList;
}

package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.contractmanager.common.service.bean.process.SealInfoBean;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * ProcessAggregationInfo
 *
 * <AUTHOR>
 * @since 2022/8/25 6:53 下午
 */
@Data
public class ProcessAggregationInfo extends ToString {

    private Long operateTime;

    private List<SealInfoBean> sealInfo;

    private String describe;
}

package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.contractmanager.core.model.dto.request.process.ProcessListRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:ji<PERSON><PERSON>
 * @since 2022-04-06 11:05
 */
@Data
public class ManageListRequest extends ProcessListRequest {
	@ApiModelProperty(value = "下载映射状态 0-全部 1-已完成 2-未完成")
	private Integer downloadMapStatus;
}

package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.dto.response.sharedownload.FlowDocumentVO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/25
 */
@Setter
@Getter
public class ProcessSignInfoResponse extends ToString {

    @ApiModelProperty(value = "签署文件信息：当前流程保密查看权限时为空")
    private List<FlowDocumentVO> contractFiles;

    @ApiModelProperty(value = "合同附件信息：当前流程保密查看权限时为空")
    private List<FlowDocumentVO> attachmentFiles;

    @ApiModelProperty(value = "签署流程id")
    private String signFlowId;

    @ApiModelProperty("合同保密类型，1-不保密， 2-全部保密，3-部分保密")
    private Integer secretType;

    @ApiModelProperty("是否可以下载")
    private Boolean canDownload;

    @ApiModelProperty("专属云项目Id")
    private String dedicatedCloudId;

}

package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.StringRegex;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

import static com.timevale.contractmanager.common.utils.StringValidUtil.REG_GENERAL_LIMIT;
import static com.timevale.contractmanager.common.utils.StringValidUtil.REG_GENERAL_LIMIT_MESSAGE;

/**
 * <AUTHOR>
 * @since 2022/11/24
 */
@Data
public class OpponentOrganizationCreateOuterRequest extends ToString {

    @NotBlank(message = "企业名称不能为空")
    @ApiModelProperty("企业名称")
    private String organizationName;

    /** {@link com.timevale.contractmanager.core.model.enums.OpponentEntityCreditCodeTypeEnum}*/
    @ApiModelProperty("证件类型")
    private Integer creditCodeType;

    @NotBlank(message = "证件代码不能为空")
    @ApiModelProperty("证件代码")
    @Length(min = 1, max = 18, message = "请检查证件代码长度")
    private String creditCode;

    @ApiModelProperty("法定代表人姓名")
    @Length(max = 100, message = "法人姓名最长100个字符")
    private String legalPersonName;

    @ApiModelProperty("备注")
    @Length(max = 200, message = "备注不能超过200个字")
    @StringRegex(regex = REG_GENERAL_LIMIT, message = "备注" + REG_GENERAL_LIMIT_MESSAGE)
    private String desc;
}

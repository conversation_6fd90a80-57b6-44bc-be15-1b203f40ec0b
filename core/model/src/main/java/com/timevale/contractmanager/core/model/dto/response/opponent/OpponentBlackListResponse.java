package com.timevale.contractmanager.core.model.dto.response.opponent;

import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-03-03 14:11
 */
@Data
public class OpponentBlackListResponse extends ToString {
	@ApiModelProperty("发起人黑名单")
	private List<OpponentEntityDO> initiator;
	@ApiModelProperty("参与人黑名单")
	private List<OpponentEntityDO> participant;
	@ApiModelProperty("抄送人黑名单")
	private List<OpponentEntityDO> cc;
}

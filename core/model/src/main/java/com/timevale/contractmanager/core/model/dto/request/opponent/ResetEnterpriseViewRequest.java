package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:jiany<PERSON>
 * @since 2021-03-22 22:14
 */
@Data
public class ResetEnterpriseViewRequest extends ToString {

	@ApiModelProperty("赠送的租户oid")
	private String tenantOid;

	@ApiModelProperty("赠送次数")
	private Integer num;

	@ApiModelProperty("过期时间:1616651674000")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date expiratTime;
}

package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.doccooperation.service.result.flow.AsyncStartFlowResult;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: doc-cooperation-project
 * @Description: 异步发起返回结果
 * @date Date : 2022年10月09日 14:30
 */
@Data
public class AsyncStartFlowResponse extends ToString {

    /**
     * 流程ID
     */
    private String processId;

    /**
     * 任务是否已结束
     */
    private boolean isDone;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String errReason;

    /**
     * 任务执行结果（isDone=true）
     */
    private ProcessStartResponse resultData;

    /**
     * 任务执行进度(isDone=false)
     */
    private AsyncStartFlowResult.ProcessStatus processStatus;

}

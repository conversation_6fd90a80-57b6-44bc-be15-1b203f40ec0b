package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.contractmanager.core.model.dto.request.process.ProcessListRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BatchSignAllRequest extends ProcessListRequest {
    @ApiModelProperty(value = "来源 1-我的合同 2-批量签署 3-待办中心, 默认 1")
    private Integer source = 1;
    @ApiModelProperty(value = "客户端类型 4-移动端H5 5-标准签WEB")
    private Integer clientType = 4;
    @ApiModelProperty(value = "scheme")
    private String appScheme;
    @ApiModelProperty(value = "token")
    private String token;
}

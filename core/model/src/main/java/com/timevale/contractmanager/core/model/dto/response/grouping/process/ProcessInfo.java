package com.timevale.contractmanager.core.model.dto.response.grouping.process;

import com.timevale.contractmanager.common.service.bean.ProcessAccount;
import com.timevale.contractmanager.common.service.bean.SupportOperate;
import com.timevale.contractmanager.common.service.bean.process.ContractFileBean;
import com.timevale.contractmanager.common.service.enums.ProcessFromEnum;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.v2.ProcessSealInfo;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.v2.ProcessTemplateInfo;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.ProcessApproval;
import com.timevale.signflow.search.docSearchService.bean.TaskInfoTotalInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: heipao
 * @since: 2019-09-017 11:12
 */
@Data
public class ProcessInfo {
    @ApiModelProperty("流程id")
    private String processId;
    @ApiModelProperty("应用id")
    private String appId;
    @ApiModelProperty("应用名")
    private String appName;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("发起时间")
    private Long processCreateTime;
    @ApiModelProperty("更新时间")
    private Long processUpdateTime;
    @ApiModelProperty("签署截止时间")
    private Long signValidity;
    @ApiModelProperty("来源")
    private Integer source;
    @ApiModelProperty("来源type")
    private String
            sourceType;
    @ApiModelProperty("流程描述")
    private String processDesc;
    @ApiModelProperty("流程状态")
    private Integer processStatus;
    @ApiModelProperty("发起人")
    List<ProcessAccount> processAccountList;
    @ApiModelProperty("发起人")
    ProcessAccount initiatorAccount;
    @ApiModelProperty("所有参与人")
    List<ProcessAccount> participantAccountList;
    @ApiModelProperty("需要处理的人")
    List<ProcessAccount> currentOperatorList;

    @ApiModelProperty("抄送人")
    List<ProcessAccount> ccAccountList;

    @ApiModelProperty(
            value = "当前登录人是否能对此流程操作-签署or填写等",
            example = "true"
    )
    private Boolean operable;
    @ApiModelProperty("flowId")
    private String flowId;

    @ApiModelProperty(value = "流程类型")
    private Integer processType;

    @ApiModelProperty(value = "合同有效期")
    private Long contractValidity;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "合同文件名列表")
    private List<String> contractFileNames;

    @ApiModelProperty(value = "附属文件名列表")
    private List<String> attachmentFileNames;

    @ApiModelProperty(value = "AI动态出参")
    private List<AiParamOut> dynamicOutParamList;

    @ApiModelProperty("合同完成时间")
    private Long completeTime;

    private ProcessApproval processApproval;

    private List<TaskInfoTotalInfo> taskInfo;

    private Map<String, Object> currentSubjectAiData;

    @ApiModelProperty("合同文件数")
    private Integer contractFileCount;
    @ApiModelProperty("附件文件数")
    private Integer attachmentFileCount;

    @ApiModelProperty("续签状态, 0-无需续签，1-可续签，2-续签中，3-部分续签，4-已续签")
    private Integer renewalStatus;

    /**
     * 流程业务类型，0-普通流程，1-解约流程，2-重新发起流程，3-续签流程
     */
    private Integer processBizType;

    @ApiModelProperty("续签配置-是否续签")
    private Boolean renewable;

    private ProcessTemplateInfo template;

    private List<ProcessSealInfo> sealInfoList;

    /**
     * 是否保密
     *
     * @see com.timevale.signflow.search.docSearchService.enums.ProcessSecretEnum
     */
    @ApiModelProperty("合同保密")
    private Integer processSecret;

    private ProcessAccount affiliatedEnterprise;

    private List<String> rescindRemark;

    private List<ContractFileBean> contractFiles;

    @ApiModelProperty("是否可解约")
    private Boolean canRescind;

    @ApiModelProperty("不能解约原因")
    private String cannotRescindReason;

    @ApiModelProperty(value = "流程来源，OFFLINE-线下导入流程，ONLINE-线上流程，为空默认线上流程")
    private String processFrom;

    @ApiModelProperty(value = "流程发起来源")
    private String initiateFrom;

    @ApiModelProperty("合同类型列表")
    private List<String> contractCategoryList;

    /**
     * 签署模式
     */
    private String signMode;

    @ApiModelProperty("专属云项目Id")
    private String dedicatedCloudId;

    /**
     * 支持的操作
     */
    private List<SupportOperate> operations;

    public String getProcessFrom() {
        if (StringUtils.isBlank(processFrom)) {
            processFrom = ProcessFromEnum.ONLINE.getType();
        }
        return processFrom;
    }
}

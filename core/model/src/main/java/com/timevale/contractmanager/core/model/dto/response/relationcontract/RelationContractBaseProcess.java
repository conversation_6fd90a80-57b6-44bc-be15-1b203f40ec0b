package com.timevale.contractmanager.core.model.dto.response.relationcontract;

import com.timevale.contractmanager.common.service.bean.process.ContractFileBean;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by tianlei on 2022/7/6
 */
@Data
public class RelationContractBaseProcess {

    @ApiModelProperty("processId")
    private String processId;

    @ApiModelProperty("合同名称")
    private String title;

    @ApiModelProperty("合同完成时间")
    private Long completeTime;

    @ApiModelProperty("合同状态")
    @NeedTranslateField
    private String processStatusDesc;

    @ApiModelProperty("合同状态")
    private Integer processStatus;

    @ApiModelProperty("发起人")
    private RelationContractProcessAccount initiator;

    @ApiModelProperty("参与人")
    private List<RelationContractProcessParticipantAccountVO> participants;

    @ApiModelProperty("合同编号")
    private List<ContractFileBean> contractFiles;
}

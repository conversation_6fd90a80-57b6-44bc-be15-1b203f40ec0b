package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.mandarin.common.result.ToString;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 合同流程下载地址获取请求参数
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
public class ProcessDownloadUrlRequest extends ToString {

    @NotBlank(message = "下载码不能为空")
    private String downloadCode;

    @NotBlank(message = "下载场景")
    private String downloadScene;
}

package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/8/14 14:40
 */
@Setter
@Getter
public class FlowTemplateGetStartDetailRequest {

    /**
     * 主体oid
     */
    private String subjectOid;

    /**
     * 模版Id
     */
    private String flowTemplateId;

    /**
     * 数据Id
     */
    private String dataId;

    private String clientId;

    private String appName;

    /**
     * 是否进行授权校验
     */
    private boolean queryAuth = true;
}

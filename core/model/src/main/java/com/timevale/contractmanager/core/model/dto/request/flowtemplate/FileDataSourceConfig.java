package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileDataSourceConfig {

    /** 数据源id, 表单id */
    private String dataSourceId;

    /** 数据来源渠道 */
    private String dataSourceChannel;

    /**
     * 文件来源（评论区 or 控件名称）
     */
    private String fileSourceFieldName;

    @ApiModelProperty(value = "文件类型，1-合并文件 2-附件", required = true)
    @NotNull(message = "fileType不能为空")
    @Min(value = 1, message = "fileType值不正确")
    @Max(value = 2, message = "fileType值不正确")
    private Integer fileType;

    private FieldConfig fieldConfig;
}

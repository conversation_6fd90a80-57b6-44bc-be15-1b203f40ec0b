package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.enums.ProcessFromEnum;
import com.timevale.contractmanager.core.model.bo.WaterMarkBO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 流程回填信息
 *
 * <AUTHOR>
 * @since 2021/06/02
 */
@Getter
@Setter
public class ProcessBackFillResponse extends ProcessStartDetailResponse {

    /** 回填流程对应的流程来源, 默认线上合同 */
    private String backFillProcessFrom = ProcessFromEnum.ONLINE.getType();
    /** 合同审批模板查询条件类型 */
    private Integer approvalTemplateConditionType;
    /** 合同神婆模板查询条件值 */
    private String approvalTemplateConditionValue;
    /** 当前模板配置的文件水印模板ID列表 */
    private List<WaterMarkBO> watermarkConfigs;
    /** 续签回填执行的动作 */
    private String renewBackFillActionType;
    /** 流程模板名称 */
    private String flowTemplateName;
}

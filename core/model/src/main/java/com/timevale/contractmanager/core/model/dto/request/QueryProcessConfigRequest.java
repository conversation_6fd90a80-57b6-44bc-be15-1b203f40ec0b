package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/8
 **/
@Data
@ApiModel("查询流程配置信息")
@AllArgsConstructor
@NoArgsConstructor
public class QueryProcessConfigRequest extends ToString {

    private List<String> appIdKeys;
}

package com.timevale.contractmanager.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/11/16
 */
public enum ProcessDownloadMapStatsEnum {

    // 下载映射状态，会映射到 ProcessStatusEnum
    ALL(0, "全部"),
    DONE(1, "已完成"),
    DOING(2, "未完成"),
    ;

    @Getter
    private Integer code;
    @Getter
    private String desc;


    ProcessDownloadMapStatsEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProcessDownloadMapStatsEnum from(Integer code) {
        if (null == code) {
            return null;
        }

        for (ProcessDownloadMapStatsEnum statusEnum : ProcessDownloadMapStatsEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }
}

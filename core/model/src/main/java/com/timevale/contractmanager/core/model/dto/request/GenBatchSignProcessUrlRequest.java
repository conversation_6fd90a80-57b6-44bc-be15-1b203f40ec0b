package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 获取批量签署地址
 *
 * <AUTHOR>
 * @since 2023/4/4
 */
@Data
public class GenBatchSignProcessUrlRequest extends ToString {

    @ApiModelProperty(value = "流程id集合")
    @Size(max = 100, message = "批量处理最大支持选择100个流程")
    private List<String> processIds;

    @ApiModelProperty(value = "空间的id")
    private String subjectId;

}

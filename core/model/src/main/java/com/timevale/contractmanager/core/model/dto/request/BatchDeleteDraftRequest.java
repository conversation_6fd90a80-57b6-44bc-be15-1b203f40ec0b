package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量删除草稿请求参数
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Data
public class BatchDeleteDraftRequest extends ToString {

    @ApiModelProperty(value = "流程id集合", required = true)
    @NotNull(message = "processIds不能为空")
    @Size(min = 1, message = "processIds不能为空")
    @Size(max = 100, message = "最大上限100")
    private List<String> processIds;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private String accountId;

    @ApiModelProperty(value = "操作人所属主体id", required = true)
    @NotBlank(message = "subjectId不能为空")
    private String subjectId;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.docmanager.service.aop.annotation.StringCheckValid;
import com.timevale.docmanager.service.enums.StringCheckTypeEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 生成解约文件请求参数
 *
 * <AUTHOR>
 * @since 2021-03-11 04:36
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class GenerateRescindDocRequest extends ToString {

    @ApiModelProperty("流程id")
    @NotBlank(message = "流程iId不能为空")
    private String processId;

    @ApiModelProperty("解约文件")
    @NotEmpty(message = "解约文件不能为空")
    private List<String> rescindFileIds;

    @ApiModelProperty("解约原因")
    @StringCheckValid(message = "解约原因不允许包含特殊字符", types = {StringCheckTypeEnum.EMOJI})
    private String rescindRemark;
}

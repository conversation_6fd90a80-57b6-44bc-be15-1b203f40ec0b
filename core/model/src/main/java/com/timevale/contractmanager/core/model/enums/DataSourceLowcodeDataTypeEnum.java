package com.timevale.contractmanager.core.model.enums;

import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/12/23 10:53
 */

public enum DataSourceLowcodeDataTypeEnum {

    FILE_FORM_INSTANCE("FILE_FORM_INSTANCE", "数据源表单内文件"),
    FILE_INSTANCE("NO_FILE_FORM_INSTANCE", "saas上传文件");

    @Getter
    private String code;

    private String description;

    DataSourceLowcodeDataTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }


    public static DataSourceLowcodeDataTypeEnum from(String code) {
        for (DataSourceLowcodeDataTypeEnum value : DataSourceLowcodeDataTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}

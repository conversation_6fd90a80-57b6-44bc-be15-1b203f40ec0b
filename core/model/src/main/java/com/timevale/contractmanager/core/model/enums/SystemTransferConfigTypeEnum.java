package com.timevale.contractmanager.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/7/26 系统转交配置类型
 */
@Getter
public enum SystemTransferConfigTypeEnum {
    ADMIN("admin", "管理员"),
    DEPT_HEAD("dept_head", "部门负责人");

    private final String type;
    private final String desc;

    SystemTransferConfigTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class BatchSignBaseRequest extends ToString {
    private String tenantId;
    private String operatorId;
    @ApiModelProperty(value = "流程实例id列表")
    private List<String> processIds;
    @ApiModelProperty(value = "流程分组id")
    private String processGroupId;
}

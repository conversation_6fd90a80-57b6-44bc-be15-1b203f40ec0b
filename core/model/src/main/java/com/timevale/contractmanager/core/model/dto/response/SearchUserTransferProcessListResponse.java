package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.dto.user.UserProcessDTO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户合同列表查询相应")
public class SearchUserTransferProcessListResponse extends ToString {
    @ApiModelProperty("用户转交合同列表")
    private List<UserProcessDTO> userProcessList;
    @ApiModelProperty("总数")
    private Long total;
}

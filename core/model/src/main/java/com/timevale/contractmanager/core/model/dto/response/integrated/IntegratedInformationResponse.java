package com.timevale.contractmanager.core.model.dto.response.integrated;

import com.timevale.billing.manager.facade.model.order.AggreEffectiveOrderMoel;
import com.timevale.signflow.search.docSearchService.result.ProcessInfoResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 融合信息返回
 *
 * <AUTHOR>
 * @since 2020-10-29 17:36
 */
@Data
public class IntegratedInformationResponse {

    /** 套餐信息 */
    @ApiModelProperty("套餐信息")
    Map<Long, AggreEffectiveOrderMoel> orderInfo;

    /** 统计信息 */
    @ApiModelProperty("统计信息")
    CountInfoListModel countInfo;

    @ApiModelProperty("流程信息")
    List<ProcessInfoResult> processInfoList;

    @ApiModelProperty("待我用印审批统计信息")
    int sealApprovingCount;

    @ApiModelProperty("待我合同审批统计信息")
    int contractApprovingCount;
}

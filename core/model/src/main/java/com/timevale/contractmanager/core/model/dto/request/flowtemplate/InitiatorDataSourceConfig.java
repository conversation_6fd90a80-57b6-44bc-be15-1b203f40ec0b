package com.timevale.contractmanager.core.model.dto.request.flowtemplate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InitiatorDataSourceConfig {

    /** 数据源id, 表单id */
    private String dataSourceId;

    /**
     * false，需要传dataSourceId和fileConfig
     *
     * <p>true，需要传
     *
     * <p>assignedUserOid
     */
    private Boolean isAssigned;

    /** 指定的发起人 */
    private String assignedUserOid;

    /** 返回时给前端的指定的发起人姓名 */
    private String assignedUserName;

    private FieldConfig fieldConfig;
}

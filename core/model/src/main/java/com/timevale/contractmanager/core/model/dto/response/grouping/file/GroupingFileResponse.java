package com.timevale.contractmanager.core.model.dto.response.grouping.file;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同归档返回
 *
 * @author: xuanzhu
 * @since: 2019-09-08 21:41
 */
@Data
public class GroupingFileResponse extends ToString {

    @ApiModelProperty("操作结果,true-成功,false-失败")
    private boolean result;

    @ApiModelProperty("业务信息")
    private String bizMessage;
}

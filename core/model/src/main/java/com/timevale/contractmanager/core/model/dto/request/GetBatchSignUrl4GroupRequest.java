package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: huifeng
 * @since: 2020-11-05 14:00
 **/
@Data
public class GetBatchSignUrl4GroupRequest extends ToString {
    @ApiModelProperty("签署完成重定向地址")
    private String redirectUrl;

    @ApiModelProperty("签署端信息, 默认PC端")
    private boolean h5;

    @ApiModelProperty(value = "菜单id，归档场景下使用")
    private String menuId;

    @ApiModelProperty("流程组id")
    @NotNull(message = "processGroupId不能为空")
    private String processGroupId;
}

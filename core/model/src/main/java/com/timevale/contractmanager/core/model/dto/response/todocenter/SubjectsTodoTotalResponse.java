package com.timevale.contractmanager.core.model.dto.response.todocenter;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 主体待办数量
 *
 * <AUTHOR>
 * @since 2021-11-24 10:37
 */
@Data
@NoArgsConstructor
public class SubjectsTodoTotalResponse extends ToString {

    /** 主体维度待办统计 */
    @HasTranslateField
    private List<SubjectTodoCountDTO> subjects;

    /** 待办总数 */
    private Long total;

    /**
     * 只传入列表时自动计算总数，避免手动设置出错
     *
     * @param subjects
     */
    public SubjectsTodoTotalResponse(List<SubjectTodoCountDTO> subjects) {
        this.subjects = subjects;
        if (CollectionUtils.isEmpty(subjects)) {
            this.total = 0L;
        } else {
            this.total =
                    subjects.stream()
                            .filter(Objects::nonNull)
                            .filter(subject -> Objects.nonNull(subject.getCount()))
                            .mapToLong(SubjectTodoCountDTO::getCount)
                            .sum();
        }
    }
}

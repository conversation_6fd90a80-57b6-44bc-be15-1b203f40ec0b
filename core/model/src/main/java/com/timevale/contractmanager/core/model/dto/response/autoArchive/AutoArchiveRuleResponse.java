package com.timevale.contractmanager.core.model.dto.response.autoArchive;

import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveOperator;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-05-08 14:28
 */
@Data
public class AutoArchiveRuleResponse extends ToString {

	@ApiModelProperty(value = "自动分类规则id")
	private String uuid;

	@ApiModelProperty(value = "归档状态0:开启,1:关闭")
	private Integer ruleStatus;

	@ApiModelProperty(value = "移除不符合规则合同:0开启,1:关闭")
	private Integer revomeContract;

	@ApiModelProperty(value = "目录名称", required = true)
	private String name;

	@ApiModelProperty(value = "目录id")
	private String menuId;

	@ApiModelProperty(value = "绑定的台账id")
	private String bindingFormId;

	@ApiModelProperty(value = "父节点ID，对应父目录的menuId,根目录为空")
	private String parentMenuId;

	@ApiModelProperty(value = "自动归档条件")
	private List<AutoArchiveOperator> conditions;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * 更新合同保密配置信息
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("更新合同保密配置信息")
public class ProcessSecretConfigUpdateRequest extends ToString {

    @ApiModelProperty(value = "合同流程id列表", required = true)
    @NotEmpty(message = "合同流程id列表不能为空")
    @Size(min = 1, max = 100, message = "单次更新不能超过100个流程")
    private List<String> processIds;

    @ApiModelProperty(value = "合同保密类型，1-不保密， 2-全部保密，3-部分保密", required = true)
    @NotNull(message = "合同保密类型不能为空")
    private Integer secretType;

    @ApiModelProperty("保密文件id列表")
    private Set<String> secretFileIds;

    @ApiModelProperty("保密文件可见人Oid列表")
    @Size(max = 10, message = "可见人不能超过10个")
    private Set<String> visibleAccounts;
}

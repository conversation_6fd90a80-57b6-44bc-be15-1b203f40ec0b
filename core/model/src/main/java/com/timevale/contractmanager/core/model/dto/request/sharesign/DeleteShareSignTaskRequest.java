package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class DeleteShareSignTaskRequest extends ToString {

    /**
     * 操作人id
     */
    private String operatorId;

    @ApiModelProperty("扫码签任务id")
    @NotBlank(message = "扫码签任务id不能为空")
    private String shareSignTaskId;

}

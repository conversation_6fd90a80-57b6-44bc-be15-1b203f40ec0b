package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.contractmanager.core.model.dto.process.signsetup.base.SignSetUpDisplayUnitDTO;
import com.timevale.contractmanager.core.model.dto.process.signsetup.display.SignSetUpPopoverDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/28 14:44
 */
@Data
public class ParticipantSetUpDisplayRuleVO {

    private String key;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("true 禁用，false 可用")
    private Boolean disabled;

    @ApiModelProperty("true 打开，false 关闭")
    private Boolean open = true;

    @ApiModelProperty("true 展示，false 不展示")
    private Boolean show = true;

    @ApiModelProperty("提示文案")
    private SignSetUpPopoverDTO popover;

    @ApiModelProperty("每个不同项特有的规则")
    private Object rules;

    @ApiModelProperty("选项")
    private List<ParticipantSetUpDisplayUnitVO> optionals;

    private List<String> tooltip;

    @ApiModelProperty("试用状态")
    private String trialStatus;

    @ApiModelProperty("会员版本是否支持")
    private Boolean supportVipFunction;


    @Data
    public static class ParticipantSetUpDisplayUnitVO {

        private String type;

        private String name;

        /**
         * 是否展示
         */
        private Boolean show = true;

        /**
         * 是否禁止操作
         */
        private Boolean disabled;

        /**
         * 后端字段是否可选, 相同于可存在这个值
         */
        private Boolean canChoose;

        // "tooltip": "个人空间不支持AI手绘签名"
        private List<String> tooltip;

        private SignSetUpPopoverDTO popover;

    }

}

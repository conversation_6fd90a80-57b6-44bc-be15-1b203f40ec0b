package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 批量删除合同流程组
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
public class DeleteProcessGroupRequest extends ToString {

    @ApiModelProperty(value = "流程组Id", required = true)
    @NotBlank(message = "流程组Id不能为空")
    private String processGroupId;

    @ApiModelProperty(value = "操作类型", required = true)
    @NotBlank(message = "操作类型不能为空")
    private String operateType;
    
}

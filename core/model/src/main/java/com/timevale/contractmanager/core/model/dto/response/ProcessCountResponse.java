package com.timevale.contractmanager.core.model.dto.response;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.CountDetail;
import com.timevale.mandarin.common.result.BaseResult;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.signflow.search.docSearchService.result.DocCountResult;
import com.timevale.signflow.search.docSearchService.result.ProcessInfoResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 功能说明: count api
 *
 * <AUTHOR>
 * @date 2019/8/6 12:52 PM
 * 修改记录 2019/8/6 12:52 PM : initial
 */
@Data
@ApiModel("process count api")
public class ProcessCountResponse extends ToString {

    @ApiModelProperty("统计结果")
    List<CountDetail> countList = Lists.newArrayList();


}

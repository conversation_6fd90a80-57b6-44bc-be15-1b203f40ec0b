package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 扫码签加入信息
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
@ApiModel("扫码签加入信息")
public class ShareSignTaskJoinInfoResponse extends ToString {

    @ApiModelProperty("是否发起人")
    private Boolean taskInitiator;

    @ApiModelProperty("已签署次数")
    private Integer signTimes;
}

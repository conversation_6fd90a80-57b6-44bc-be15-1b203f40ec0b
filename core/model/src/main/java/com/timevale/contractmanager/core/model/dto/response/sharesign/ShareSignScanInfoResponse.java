package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.contractmanager.core.model.dto.response.process.ProcessParticipantStatusResponse;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021-08-09
 */
@Data
public class ShareSignScanInfoResponse extends ShareSignTaskInfoResponse {

    @ApiModelProperty("扫码签参与方类型，0-个人，1-企业")
    private Integer participantType;


    @ApiModelProperty("扫码发起是否需要走异步发起")
    private boolean asyncStart = false;

    @ApiModelProperty("是否是epaas模板")
    private boolean epaasTemplateTag;

    @ApiModelProperty("签署方信息")
    private ProcessParticipantStatusResponse participants;
}

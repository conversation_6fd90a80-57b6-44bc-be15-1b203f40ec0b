package com.timevale.contractmanager.core.model.enums;

import com.google.common.collect.Maps;

import java.util.Map;

public enum SensorEventEnum {
    PROCESS_START("saas_process_start", "process流程发起"),
    ONE_PARTICIPATE_PROCESS_START("one_initiate_signature_click", "单方签署发起"),
    TWO_PARTICIPATE_PROCESS_START("two_initiate_signature_click", "双方签署发起"),
    MORE_PARTICIPATE_PROCESS_START("multiple_initiate_signature_click", "多方签署发起"),
    PROCESS_END("saas_process_end", "process流程结束"),
    PROCESS_TRANSFER("saas_process_transfer", "process流程转交"),
    SHARE_SIGN_TASK_START("scan_code_initiate_success", "扫码任务发起成功"),
    TEMPLATEMATCH_ARCHIVE("standard_contracts_autofiled","模板合同自动归档"),
    UN_TEMPLATEMATCH_ARCHIVE("nonstandard_contracts_autofiled","非模板合同自动归档"),
    TEMPLATEMATCH_STANDINGBOOK("standard_contracts_information_acquired","模板合同提取信息"),
    UN_TEMPLATEMATCH_STANDINGBOOK("nonstandard_contracts_information_acquired","非模板合同提取信息"),
    AUTO_ARCHIVE("fuwuduan_xintaizhangzidongguidang","新台账自动归档成功"),
    CLOSE_INITIATE_CONTRACT_ALL("close_initiate_contract_all","关闭全局直接发起开关"),
    OPEN_INITIATE_CONTRACT("open_initiate_contract","开启全局直接发起开关"),
    PROCESS_DOWNLOAD_ALL_DONE("fuwuduan_xiazaiquanbuchenggong", "下载全部合同成功"),
    OPPONENT_DETECTION_PROBLEM_REPORT("fuwuduan_faxianwenti","每次发现问题时，不论是批量检测还是新增检测"),
    PROCESS_LIST("handle_contract_list_load_sever","经办合同列表加载"),
    PROCESS_DELETE("delete_contract_sever","删除合同"),
    PROCESS_DOWNLOAD_CONTRACT("download_contract_sever","下载合同"),
    EXPORT_CONTRACT_INFORMATION_SEVER("export_contract_information_sever","导出合同明细"),
    EDIT_DUE_DATE_SEVER("edit_due_date_sever","修改到期日期"),
    EDIT_RENEWAL_SEVER("edit_renewal_sever","修改续签"),
    ENTERPRISE_CONTRACT_LIST_LOAD_SEVER("enterprise_contract_list_load_sever","企业合同列表加载"),
    SET_FOLDER_SEVER("set_folder_sever","设置分类"),
    CLASSIFY_CONTRACTS_SEVER("classify_contracts_sever","企业合同移动分类"),
    AUTHORIZE_CONTRACT_FOLDER_SEVER("authorize_contract_folder_sever","设置分类授权"),
    OPPONENT_DETECTION("fuwuduan_jianceqiye","每次检测企业时，不论是批量检测还是新增检测"),

    VIEW_CONTRACT_SEVER("view_contract_sever","查看合同请求鉴权处理完成时"),
    URGE_CONTRACT_SEVER("urge_contract_sever","催办的消息发送完成时"),
    WITHDRAW_CONTRACT_SEVER("withdraw_contract_sever","撤回完成时"),
    SHARE_CONTRACT_SEVER("share_contract_sever", "分享的链接生成时"),
    EDIT_SECRECY_SEVER("edit_secrecy_sever", "修改保密性完成时"),
    EDIT_CC_SEVER("edit_cc_sever", "修改抄送人完成时"),
    SET_CONTRACT_TEMPLATE_SEVER("set_contract_template_sever", "设置合同模板完成时"),
    CREATE_TERMINATION_AGREEMENT("create_termination_agreement", "生成解约协议书"),

    GET_PERSON_FROM_OPPONENT("congxiangduifangxuanren", "从相对方选人"),

    RELATION_CONTRACT_QUERY("query_relation_contract", "关联合同查询"),

    SAVE_CONTRACT_NO_RULE_SEVER("save_contractNoRule_sever", "保存合同编号规则"),

    CREATE_CONTRACT_NO_SEVER("createContractNo_sever", "创建合同编号"),

    SAVE_GLOBAL_WATERMARK_SETTING("PC_shuiyinpeizhixiangqing", "水印全局配置事件"),

    INTERNAL_MEMBER_AUTO_TRANSFER("internal_member_auto_transfer","外部合同发送给企业成员自动转交统计"),

    EXTERNAL_MEMBER_AUTO_TRANSFER("external_member_auto_transfer","非企业成员的合同自动转交统计"),

    ORG_NAME_AUTO_TRANSFER("org_name_auto_transfer","未指定经办人合同自动转交")
    ;

    /**
     * 埋点事件
     */
    private String event;

    /**
     * 描述
     */
    private String des;

    private static Map<String, SensorEventEnum> items;

    static {
        items = Maps.newHashMap();
        for (SensorEventEnum item : values()) {
            items.put(item.getEvent(), item);
        }
    }

    SensorEventEnum(String event, String des) {
        this.event = event;
        this.des = des;
    }

    public String getEvent() {
        return event;
    }

    public String getDes() {
        return des;
    }

    /**
     * 获取埋点事件
     *
     * @param event
     * @return
     */
    public static SensorEventEnum valueOfEvent(String event) {
        return items.get(event);
    }
}

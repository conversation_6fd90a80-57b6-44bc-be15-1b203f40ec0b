package com.timevale.contractmanager.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/11/17 14:44
 */
public enum BizRuleStartTypeEnum {

    // 主模式   海外签只是主干道下面一个分支
    /**
     * 直接发起
     */
    START("START"),
    /**
     * 直接发起-解约
     */
    START_RESCIND("START_RESCIND"),
    /**
     * 直接发起-重新发起
     */
    START_RESTART("START_RESTART"),
    /**
     * 直接发起-续签
     */
    START_RENEW("START_RENEW"),

    /**
     * 创建编辑模版
     */
    TEMPLATE_EDIT("TEMPLATE_EDIT"),
    /**
     * 模版发起
     */
    TEMPLATE("TEMPLATE"),

    /**
     * 模版发起 - 解约
     */
    TEMPLATE_RESCIND("TEMPLATE_RESCIND"),
    /**
     * 模版发起 - 重新发起
     */
    TEMPLATE_RESTART("TEMPLATE_RESTART"),

    /**
     * 模版发起 - 续签
     */
    TEMPLATE_RENEW("TEMPLATE_RENEW"),
    ;

    @Getter
    private String code;

    BizRuleStartTypeEnum(String code) {
        this.code = code;
    }





}

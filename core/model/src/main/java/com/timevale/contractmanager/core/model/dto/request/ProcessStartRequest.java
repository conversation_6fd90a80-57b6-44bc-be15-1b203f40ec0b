package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.bean.ValidityConfigBean;
import com.timevale.contractmanager.common.service.enums.ParticipantStartType;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.integration.util.ValidationUtil;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.contractmanager.core.model.bo.ProcessStartBO;
import com.timevale.contractmanager.core.model.enums.ProcessStartScene;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.doccooperation.service.enums.ValidityTypeEnum;

import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;

/**
 * 流程发起请求入参
 *
 * <AUTHOR>
 * @since 2019/11/5
 */
@ApiModel(description = "流程发起入参")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProcessStartRequest extends ProcessStartBO<FileBO> {

    @ApiModelProperty("请求编号，用于幂等处理")
    private String requestNo;

    @ApiModelProperty(value = "流程模板id, scene=2时需要传入")
    @Override
    public String getFlowTemplateId() {
        return super.getFlowTemplateId();
    }

    @ApiModelProperty(value = "使用场景, 1-直接发起 2-模板发起")
    @NotNull(message = "scene不能为空")
    @Valid
    @Override
    public Integer getScene() {
        if (ProcessStartScene.DIRECT_START.getScene() != super.getScene()
                && ProcessStartScene.TEMPLATE_START.getScene() != super.getScene()) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "scene值不正确");
        }
        return super.getScene();
    }

    /** 校验有效期参数是否正确, 无需设置场景可设置日期， 但后续逻辑会忽略，不生效 */
    public void checkValidityParam() {
        ValidityConfigBean signValidityConfig = getSignValidityConfig();
        // 如果是有效时长类型， 且未指定时长， 报错
        if (durationValidityType(signValidityConfig.getValidityType())
                && !signValidityConfig.assignedDuration()) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "未指定签署截止日期");
        }
        //指定截止日期校验是否小于当前时间
        if (ValidityTypeEnum.USE_ASSIGNED.getType() == signValidityConfig.getValidityType()
                && getSignEndTime() != null
                && getSignEndTime() < new Date().getTime()) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "签署截止日期不能小于当前日期");
        }
        // 签署截止日期参数有效性校验
        ValidationUtil.validateBean(signValidityConfig);

        ValidityConfigBean fileValidityConfig = getFileValidityConfig();
        // 如果是有效时长类型， 且未指定时长， 报错
        if (durationValidityType(fileValidityConfig.getValidityType())
                && !fileValidityConfig.assignedDuration()) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "未指定合同到期日期");
        }
        //指定截止日期校验是否小于当前时间
        if (ValidityTypeEnum.USE_ASSIGNED.getType() == signValidityConfig.getValidityType()
                && getFileEndTime() != null
                && getFileEndTime() < new Date().getTime()) {
            throw new BizContractManagerException(PROCESS_ILLEGAL_PARAM, "合同到期日期不能小于当前日期");
        }
        // 合同到期日期参数有效性校验
        ValidationUtil.validateBean(fileValidityConfig);
    }

    private boolean durationValidityType(Integer validityType) {
        if (null == validityType) {
            return false;
        }
        return ValidityTypeEnum.FROM_SIGN_DONE.getType() == validityType
                || ValidityTypeEnum.FROM_INITIATE.getType() == validityType;
    }

    // 检查参数
    public void checkParams() {
        // 检查参与方信息
        checkParticipants(this::processParticipantValidation);
    }

    /** 合同流程的参与方校验 */
    private void processParticipantValidation(ParticipantBO participant) {
        List<ParticipantInstanceBO> instances =
                Optional.ofNullable(participant.getInstances()).orElse(Collections.emptyList());
        boolean isOrSignParticipant =
                ParticipantModeEnum.isOrSign(participant.getParticipantMode());

        // 或签方
        if (isOrSignParticipant) {
            //指定角色不校验，后续会设置人员在Instances
            if(StringUtils.isNotBlank(participant.getParticipantStartType()) && ParticipantStartType.PARTICIPANT_ASSIGN_ROLE.getType().equals(participant.getParticipantStartType())){
                return;
            }
            // 至少2个人
            if (instances.size() < 2) {
                throw new BizContractManagerException(OR_SIGN_PARTICIPANT_LESS_THAN_2);
            }

            // 签章方式只能选1个
            if (StringUtils.isNotBlank(participant.getSignRequirements())
                    && participant.getSignRequirements().split(",").length > 1) {
                throw new BizContractManagerException(OR_SIGN_REQUIREMENTS_TOO_MORE);
            }
        }
    }
}

package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 编辑合同归档操作请求
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class ModifyGroupingFileRequest extends CommonRequest {

    @ApiModelProperty(value = "合同主题")
    private String title;

    @ApiModelProperty(value = "合同有效时间-时间戳")
    private Long validTime;

    @ApiModelProperty(value = "菜单ID", required = true)
    @NotNull(message = "menuId不能为空")
    private String menuId;
}

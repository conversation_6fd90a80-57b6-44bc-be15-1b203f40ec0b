package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 修改续签配置请求参数
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
@Data
@ApiModel
public class UpdateRenewableRequest extends ToString {

    @ApiModelProperty("流程id列表")
    @NotEmpty(message = "流程id列表不能为空")
    @Size(max = 100, message = "流程id列表不能查过100")
    private List<String> processIds;

    @ApiModelProperty("是否需要续签")
    private boolean renewable;

    @ApiModelProperty("通知范围，0-无需通知，1-全部参与人，2-当前企业参与人")
    private Integer noticeRange;
}

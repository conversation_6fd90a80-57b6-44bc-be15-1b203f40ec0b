package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022-08-24 15:04
 */
@Data
@ApiModel("重新发起信息")
@AllArgsConstructor
@NoArgsConstructor
public class ProcessStartInfoResponse extends ToString {

    private Integer startType;
}

package com.timevale.contractmanager.core.model.dto.request.datasource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetDataSourceDataRequest {
    private String dataSourceDataId;
    private String processId;
    private String primaryOid;
    private List<String> fieldTypes;
}

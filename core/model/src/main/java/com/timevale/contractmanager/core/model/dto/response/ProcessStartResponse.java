package com.timevale.contractmanager.core.model.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2019/11/6
 */
@Getter
@Setter
@NoArgsConstructor
public class ProcessStartResponse extends BaseProcessStartResponse {

    @ApiModelProperty(value = "结果地址，签署地址/填写地址长链")
    private String longResultUrl;

    @ApiModelProperty(value = "签署流程id")
    private String flowId;

    @ApiModelProperty(value = "合同审批流程id")
    private String approvalId;

    @ApiModelProperty(value = "填写流程id")
    private String cooperationId;

    @ApiModelProperty(value = "主流程id 在processId不是主流程id时有值")
    private String realProcessId;

    @ApiModelProperty(value = "流程组id")
    private String groupId;

    @ApiModelProperty(value = "流程模板ID")
    private String flowTemplateId;

    @ApiModelProperty(value = "中间页地址")
    private String middleResultUrl;

    public ProcessStartResponse(String processId, String longResultUrl, String resultUrl) {
        super(processId, resultUrl);
        this.longResultUrl = longResultUrl;
    }

    public ProcessStartResponse(String processId, String longResultUrl, String resultUrl,String groupId) {
        this(processId,longResultUrl, resultUrl);
        this.groupId = groupId;
    }

    public ProcessStartResponse(
            String processId, String longResultUrl, String resultUrl, String flowId,String realProcessId) {
        super(processId, resultUrl);
        this.longResultUrl = longResultUrl;
        this.flowId = flowId;
        this.realProcessId = realProcessId;
    }

    public ProcessStartResponse(String flowTemplateId,
            String processId, String longResultUrl, String resultUrl, String flowId,String realProcessId) {
        super(processId, resultUrl);
        this.longResultUrl = longResultUrl;
        this.flowId = flowId;
        this.realProcessId = realProcessId;
        this.flowTemplateId = flowTemplateId;
    }

}

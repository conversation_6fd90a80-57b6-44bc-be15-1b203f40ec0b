package com.timevale.contractmanager.core.model.dto.response.process;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("合同审批流程组列表")
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalGroupListQueryResponse {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("流程列表")
    private List<ProcessApprovalGroupResponse> approvalGroupList;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.ValidityConfigBean;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.core.model.bo.ApproveBO;
import com.timevale.contractmanager.core.model.bo.FileDetailBO;
import com.timevale.contractmanager.core.model.bo.ParticipantBO;
import com.timevale.contractmanager.core.model.bo.ParticipantInstanceBO;
import com.timevale.contractmanager.core.model.bo.ProcessStartBO;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.DataSourceRelationConfig;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.FlowTemplateDataSourceConfig;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.FlowTemplateWatermarkTemplate;
import com.timevale.contractmanager.core.model.dto.request.flowtemplate.InitiatorDataSourceConfig;
import com.timevale.doccooperation.service.enums.CooperationerRoleSetEnum;
import com.timevale.doccooperation.service.enums.ParticipantModeEnum;
import com.timevale.doccooperation.service.enums.ValidityTypeEnum;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import static com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum.*;

/**
 * 保存流程模板
 *
 * <AUTHOR>
 * @since 2019/11/6
 */
@ApiModel(value = "保存流程模板")
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveFlowTemplateRequest extends ProcessStartBO<FileDetailBO> {

    @ApiModelProperty(value = "流程模板标签")
    private String label;

    @ApiModelProperty(value = "是为空表示新建,不为空表示更新")
    @Override
    public String getFlowTemplateId() {
        return super.getFlowTemplateId();
    }

    /** 请参考{@link com.timevale.contractmanager.core.model.enums.ProcessStartScene } */
    @ApiModelProperty(
            value =
                    "使用场景, 3-创建/设置模板 4-保存草稿 5-本地直接发起（指定位置签署，保存临时）6-预览, 7-第三方模板, 8-钉签附件审批指定位置发起（构建临时模板）")
    @NotNull(message = "scene不能为空")
    @Min(value = 3, message = "scene值不正确")
    @Max(value = 8, message = "scene值不正确")
    @Override
    public Integer getScene() {
        return super.getScene();
    }

    /** 请参考{@link com.timevale.contractmanager.core.model.enums.PreviewTypeEnum } */
    @ApiModelProperty(value = "预览场景")
    private Integer previewType;

    /** 预览场景重定向地址 */
    // 续签场景使用，上一步地址
    @ApiModelProperty(value = "预览场景重定向地址")
    private String previewRedirectUrl;

    @ApiModelProperty(value = "是否支持发起时添加文件")
    private Boolean supportAddDoc = false;

    /** 下一步跳转地址 */
    @ApiModelProperty(value = "非标模板发起跳转地址")
    private String forwardUrl;

    @ApiModelProperty(value = "重新发起时, 上一次流程id")
    private String originProcessId;

    @Deprecated
    @ApiModelProperty(value = "要取消关联的审批模板id")
    private String offApproveTemplateId;

    @ApiModelProperty(value = "水印模板ID集合")
    private List<String> watermarkTemplateIds;

    @ApiModelProperty(value = "水印模板集合")
    private List<FlowTemplateWatermarkTemplate> watermarkTemplates;

    @ApiModelProperty(value = "模板关联的审批列表")
    private List<ApproveBO> approveTemplateList;

    @ApiModelProperty(value = "clientId")
    private String clientId;

    private String subjectOid;

    private String operatorOid;

    private String spaceOid;

    private String appId;

    private String appName;

    @ApiModelProperty(value = "定时发起时间")
    private Long scheduledInitiateTime;

    @ApiModelProperty(value = "是否提交定时任务")
    private Boolean startScheduledInitiate;

    @ApiModelProperty(value = "模版数据源")
    private FlowTemplateDataSourceConfig dataSourceConfig;

    @ApiModelProperty(value = "发起方数据源")
    private InitiatorDataSourceConfig initiatorDataSourceConfig;

    @ApiModelProperty(value = "关联数据源配置")
    private DataSourceRelationConfig dataSourceRelationConfig;

    private String relationParentOid;

    public void preHandleRequest() {
        Integer defaultValidityType = ValidityTypeEnum.USE_ASSIGNED.getType();
        // 预处理签署截止时间配置, 默认有效期类型为使用模板时设置
        if (null == getSignValidityConfig()) {
            setSignValidityConfig(new ValidityConfigBean(defaultValidityType));
        } else if (null == getSignValidityConfig().getValidityType()) {
            getSignValidityConfig().setValidityType(defaultValidityType);
        }
        // 预处理合同到期日期配置, 默认有效期类型为使用模板时设置
        if (null == getFileValidityConfig()) {
            setFileValidityConfig(new ValidityConfigBean(defaultValidityType));
        } else if (null == getFileValidityConfig().getValidityType()) {
            getFileValidityConfig().setValidityType(defaultValidityType);
        }
    }

    public void checkParams() {
        // 参与方检查
        checkParticipants(this::flowTemplateParticipantValidation);
    }

    /** 流程模板的参与方校验 */
    private void flowTemplateParticipantValidation(ParticipantBO participant) {
        List<ParticipantInstanceBO> instances =
                Optional.ofNullable(participant.getInstances()).orElse(Collections.emptyList());
        boolean isOrSignParticipant =
                ParticipantModeEnum.isOrSign(participant.getParticipantMode());
        boolean designatedUser =
                CooperationerRoleSetEnum.DESIGNATED_USER.getType() == participant.getRoleSet();
        // 参与方角色设置校验
        participant.checkRoleSetValid();
        // 是否可扫码加入校验
        participant.checkSharableValid();

        // 或签方校验
        if (isOrSignParticipant) {
            // 或签方固定成员至少填2个人
            if (designatedUser && instances.size() < 2) {
                throw new BizContractManagerException(OR_SIGN_PARTICIPANT_LESS_THAN_2);
            }

            // 或签方的人员账号不能重复(同一人代表多个企业也不行)
            if (instances.size() >= 2) {
                checkDuplicateParticipantAccounts(instances);
            }
        }
    }

    /** 检查或签人的账号是否重复 */
    private void checkDuplicateParticipantAccounts(List<ParticipantInstanceBO> instances) {
        Set<String> oidSet = new HashSet<>();
        Set<String> gidSet = new HashSet<>();
        Set<String> nameAppendAccountSet = new HashSet<>();

        for (ParticipantInstanceBO instance : instances) {
            // oid
            if (StringUtils.isNotBlank(instance.getAccountOid())
                    && !oidSet.add(instance.getAccountOid())) {
                throw new BizContractManagerException(OR_SIGN_ACCOUNT_DUPLICATE);
            }

            // gid
            if (StringUtils.isNotBlank(instance.getAccountGid())
                    && !gidSet.add(instance.getAccountGid())) {
                throw new BizContractManagerException(OR_SIGN_ACCOUNT_DUPLICATE);
            }

            // name_account
            String nameAppendAccount = joinNameAndAccount(instance);
            if (StringUtils.isNotBlank(nameAppendAccount)
                    && !nameAppendAccountSet.add(nameAppendAccount)) {
                throw new BizContractManagerException(OR_SIGN_ACCOUNT_DUPLICATE);
            }
        }
    }

    private String joinNameAndAccount(ParticipantInstanceBO instance) {
        String accountName = StringUtils.trim(instance.getAccountName());
        String account = StringUtils.trim(instance.getAccount());

        if (StringUtils.isBlank(accountName) && StringUtils.isBlank(account)) {
            return null;
        }

        return Optional.ofNullable(accountName).orElse("")
                + "_"
                + Optional.ofNullable(account).orElse("");
    }
    
    public List<ApproveBO> getApproveTemplateList() {
        if (CollectionUtils.isNotEmpty(this.approveTemplateList)) {
            return this.approveTemplateList;
        }
        
        if (StringUtils.isBlank(this.getApproveTemplateId())) {
            return Lists.newArrayList();
        }
        
        return Collections.singletonList(new ApproveBO(this.getApproveTemplateId()));
    }
}

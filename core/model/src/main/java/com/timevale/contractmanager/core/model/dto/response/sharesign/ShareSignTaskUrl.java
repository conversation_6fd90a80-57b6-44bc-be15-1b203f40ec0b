package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021-02-05
 */
@Data
public class ShareSignTaskUrl extends ToString {
    @ApiModelProperty("扫码签参与方id")
    private String participantId;
    @ApiModelProperty("扫码签参与方类型，0-个人，1-企业")
    private Integer participantType;
    @ApiModelProperty("扫码签参与方名称")
    private String participantLabel;
    @ApiModelProperty("扫码签任务链接地址, 格式：http://xxxxxx?shareSignTaskId=ssss&participantId=aaaaa")
    private String shareUrl;
    @ApiModelProperty("扫码签任务二维码地址")
    private String qrcodeUrl;
    @ApiModelProperty("扫码签二维码id")
    private String shareScanId;
}

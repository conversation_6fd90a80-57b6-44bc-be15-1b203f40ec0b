package com.timevale.contractmanager.core.model.dto.request.datasource;

import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectTemplateFormRelationDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/19 15:38
 */
@Setter
@Getter
public class DataSourceStartInnerRequest {

    private String subjectOid;


    /**
     * 关联关系
     */
    private InfoCollectTemplateFormRelationDTO relation;

    /**
     * outerDataId
     */
    private String outerDataId;

    /**
     * 三方主体key
     */
    private String tenantKey;

    /**
     * 数据源平台信息
     */
    private String platform;

    /**
     * 采集数据
     */
    private Map<String, Object> data;
}

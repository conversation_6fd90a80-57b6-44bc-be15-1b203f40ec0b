package com.timevale.contractmanager.core.model.enums;

/**
 * 流程发起场景
 *
 * <AUTHOR>
 * @since 2019/11/6
 */
public enum ProcessStartScene {

    /** 本地直接发起 */
    DIRECT_START(1),
    /** 模板发起 */
    TEMPLATE_START(2),
    /** 创建/设置模板流程模板相关信息 */
    CREATE_SET_TEMPLATE(3),
    /** 保存草稿 */
    SAVE_DRAFT(4),
    /** 本地直接发起（指定位置签署，保存临时） */
    SIGN_ASSIGN_POS(5),
    /*模板预览*/
    PREVIEW(6),
    /*创建系统三方模板*/
    CREATE_SET_THIRD_TEMPLATE(7),
    /** 附件审批指定位置发起，保存临时数据 */
    DING_ATTACHMENT_ASSIGN_POS(8),
    /*轩辕流程模板开放发起,内部使用，后面会替换成 TEMPLATE_START*/
    NEW_TEMPLATE_START(100);

    ProcessStartScene(int scene) {
        this.scene = scene;
    }

    private int scene;

    public int getScene() {
        return scene;
    }

    public static ProcessStartScene getScene(Integer scene){
        for(ProcessStartScene value : ProcessStartScene.values()){
            if(value.getScene() == scene)   return value;
        }
        throw new IllegalArgumentException();
    }

    /**
     * 判断是否指定位置场景保存模板
     *
     * @param scene
     * @return
     */
    public static boolean isAssignPos(Integer scene) {
        return null != scene
                && (SIGN_ASSIGN_POS.getScene() == scene
                        || DING_ATTACHMENT_ASSIGN_POS.getScene() == scene);
    }
    /**
     * 判断是否指定位置场景保存模板
     *
     * @param scene
     * @return
     */
    public static boolean isSaveFlowTemplate(Integer scene) {
        return null != scene
                && (CREATE_SET_TEMPLATE.getScene() == scene
                || CREATE_SET_THIRD_TEMPLATE.getScene() == scene);
    }

    /**
     * FDA 场景
     */
    public static boolean isFdaSupportScene(Integer scene) {
        return null != scene
                && (TEMPLATE_START.getScene() == scene
                || PREVIEW.getScene() == scene);
    }

    /**
     * FDA签署以下场景关闭
     */
    public static boolean isFdaCloseScene(Integer scene) {
        return null != scene
                && (DIRECT_START.getScene() == scene
                || TEMPLATE_START.getScene() == scene
                || CREATE_SET_TEMPLATE.getScene() == scene);
    }

}

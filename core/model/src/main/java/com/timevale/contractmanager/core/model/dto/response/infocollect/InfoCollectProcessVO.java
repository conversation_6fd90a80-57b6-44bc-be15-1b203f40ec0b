package com.timevale.contractmanager.core.model.dto.response.infocollect;

import com.timevale.contractmanager.core.model.dto.response.base.process.BaseBizProcessAccountVO;
import com.timevale.contractmanager.core.model.dto.response.base.process.BaseBizProcessParticipantAccountVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by tianlei on 2022/9/19
 */
@Data
public class InfoCollectProcessVO {


    @ApiModelProperty("是否可下载")
    private Boolean canDownload;

    @ApiModelProperty("是否可查看")
    private Boolean canLook;

    @ApiModelProperty("processId")
    private String processId;

    @ApiModelProperty("合同名称")
    private String title;

    @ApiModelProperty("合同完成时间")
    private Long completeTime;

    @ApiModelProperty("合同状态")
    private String processStatusDesc;

    @ApiModelProperty("合同状态")
    private Integer processStatus;

    @ApiModelProperty("发起人")
    private BaseBizProcessAccountVO initiator;

    @ApiModelProperty("参与人")
    private List<BaseBizProcessParticipantAccountVO> participants;


}

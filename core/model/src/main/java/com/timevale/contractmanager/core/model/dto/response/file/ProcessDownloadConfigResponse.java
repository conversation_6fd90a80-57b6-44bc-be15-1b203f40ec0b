package com.timevale.contractmanager.core.model.dto.response.file;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 合同下载配置结果
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
public class ProcessDownloadConfigResponse extends ToString {

    @ApiModelProperty(value = "下载配置, 1 系统默认 2 自定义")
    private Integer downloadConfigType;

    @ApiModelProperty(value = "文件名称, 1-合同名称，2-签署方，3-合同编号")
    private List<Integer> namingFormat;

    @ApiModelProperty(value = "参与方名称, 1-企业名称，2-签署人姓名")
    private List<Integer> participantsFormat;

    @ApiModelProperty(value = "是否适用于流程内部文件，默认否")
    private boolean suitableForFile;
}

package com.timevale.contractmanager.core.model.dto.response.grouping.standingbook;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义表格列表
 *
 * @author: xuanzhu
 * @since: 2019-10-27 16:29
 */
@Data
public class CustomListDTO extends ToString {

    @ApiModelProperty(value = "菜单id，空为所有菜单生效，默认空")
    private String menuId;

    @ApiModelProperty(value = "需要显示的字段列表", required = true)
    @HasTranslateField
    private List<CustomField> displayList = new ArrayList<>();

    @ApiModelProperty(value = "需要隐藏的字段列表")
    @HasTranslateField
    private List<CustomField> hiddenList = new ArrayList<>();

    @Data
    public static class CustomField {
        @ApiModelProperty(value = "字段id")
        private String fieldId;

        @ApiModelProperty(value = "字段code")
        private String fieldCode;

        @ApiModelProperty(value = "字段显示名称")
        @NeedTranslateField
        private String fieldName;

        /** @see com.timevale.contractmanager.common.service.enums.grouping.FieldTypeEnum */
        @ApiModelProperty(value = "类型，1-系统自动，2-AI解析 3-文件")
        private int fieldType;

        /** @see com.timevale.contractanalysis.facade.api.enums.LedgerFieldTypeEnum */
        @ApiModelProperty(value = "自定义字段类型 ")
        private Integer ledgerFieldType;

        @ApiModelProperty(value = "是否默认，0-否，1-是")
        private int isDefault;

        /** @see com.timevale.contractmanager.common.service.enums.grouping.FieldSearchTypeEnum */
        @ApiModelProperty(value = "搜索类型，如：1-文本，2-日期，3-固定下拉框不支持编辑,4-固定下拉框支持编辑,5-区间")
        private int searchType;

        @ApiModelProperty(value = "字段宽度")
        private Integer width;

        @ApiModelProperty(value = "是否冻结")
        private int isFrozen;
    }
}

package com.timevale.contractmanager.core.model.dto.response.grouping.process;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI 动态出参
 *
 * @author: xuanzhu
 * @since: 2019-10-29 09:18
 */
@Data
public class AiParamOut extends ToString {

    @ApiModelProperty(value = "AI解析的字段，对应fieldCode")
    private String key;

    @ApiModelProperty(value = "对应值")
    private Object value;

    @ApiModelProperty(value = "类型：1 在线模板控件 2 AI ")
    private Integer type;

    @ApiModelProperty(value = "是否已修改,true已修改")
    private Boolean fixed;
}

package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.contractmanager.common.service.enums.ParticipantSubjectType;
import com.timevale.contractmanager.common.utils.StringValidUtil;
import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.validator.constraints.Length;

import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @since 2021/10/18
 */
@Setter
@Getter
public class ExcelParseRequest extends ToString {

  @ApiModelProperty(value = "参与方名称", required = true)
  private String participantLabel;

  @ApiModelProperty(value = "fileKey", required = true)
  @NotBlank(message = "未获取到文件信息")
  private String fileKey;

  /** {@link ParticipantSubjectType} */
  @ApiModelProperty(value = "参与方主体类型：0甲方，1：企业，2：待定")
  private Integer participantSubjectType = 2;

  /** 流程模板id：为空的话使用本地发起模板 */
  @ApiModelProperty(value = "流程模板id")
  private String flowTemplateId;

  /** 流程模板类型 */
  @ApiModelProperty(value = "流程模板类型,1:通用模板,2:第三方用户模板(钉钉，微信，飞书)下载的文档")
  private Integer excelTemplateType = 1;

  @ApiModelProperty(value = "文件列表")
  private List<FileBO> files;

  /** 任务名称，不支持特殊字符，如 \ / * : < > | ? " */
  @ApiModelProperty(value = "任务名称，不支持特殊字符", required = true)
  @Length(max = 100, message = "taskName最多100个字符")
  @Pattern(
          regexp = StringValidUtil.REG_NONE_ILLEGAL_CHAR_NULLABLE,
          message = "任务主题不支持换行、缩进及特殊字符 < > / \\ | : \" * ?")
  private String taskName;

  @ApiModelProperty(value = "企业抄送方个数")
  @Max(value = 5, message = "最多设置5个抄送方")
  private Integer subjectCcNum;

  @ApiModelProperty(value = "个人抄送方个数")
  @Max(value = 5, message = "最多设置5个抄送方")
  private Integer personCcNum;

  @ApiModelProperty(value = "参与方id")
  private String participantId;

  @ApiModelProperty(value = "签署模式")
  private String signMode;
}

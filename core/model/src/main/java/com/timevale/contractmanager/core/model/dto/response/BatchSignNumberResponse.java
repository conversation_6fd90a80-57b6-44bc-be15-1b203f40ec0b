package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BatchSignNumberResponse extends ToString {
    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "是否异步")
    private boolean async;
}

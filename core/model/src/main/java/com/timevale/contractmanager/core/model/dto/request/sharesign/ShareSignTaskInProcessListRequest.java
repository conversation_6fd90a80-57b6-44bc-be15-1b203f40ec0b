package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @since 2021/12/10
 */
@Setter
@Getter
public class ShareSignTaskInProcessListRequest extends ToString {
    @ApiModelProperty("页码")
    @Min(value = 1, message = "页行数不能小于1")
    @Max(value = 20, message = "页行数不能大于20")
    private Integer pageNum;

    @ApiModelProperty("页行数，最小1， 最大20")
    @Min(value = 1, message = "页行数不能小于1")
    @Max(value = 20, message = "页行数不能大于20")
    private Integer pageSize;

    @ApiModelProperty("0:结束，1：任务中")
    private Integer status;

    @ApiModelProperty("是否过滤主体，如果是，默认只获取当前空间数据")
    private boolean filterSubject;

    public Integer getPageNum() {
        return null == this.pageNum || this.pageNum <= 0 ? 1 : pageNum;
    }

    public Integer getPageSize() {
        return null == this.pageSize || this.pageSize <= 0 || this.pageSize > 20
                ? 20
                : this.pageSize;
    }
}

package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 解析批量导入表格返回值
 *
 * <AUTHOR>
 * @since 2021/12/29
 */
@ApiModel(value = "解析批量导入表格")
@EqualsAndHashCode(callSuper = true)
@Data
public class ParseMultiBatchExcelResponse extends ToString {

    @ApiModelProperty(value = "正确数据个数")
    private Integer correctCount = 0;

    @ApiModelProperty(value = "错误数据个数")
    private Integer errorCount = 0;

    @ApiModelProperty(value = "正确的表格流程信息")
    private List<SignleProcessInfo> processInstances;

    @ApiModelProperty(value = "错误数据excel表格下载地址，为空表示没用错误数据")
    private String errorDataExcelDownloadUrl;

    /**
     * 单流程信息
     * 其下有多个参与方
     * <AUTHOR>
     * @since 2021/12/10
     */
    @Data
    public static class SignleProcessInfo {
        @ApiModelProperty(value = "子任务名称，批量时传入")
        private String subTaskName;

        @ApiModelProperty(value = "预填内容")
        private Map<String, Object> preFillValues;

        @ApiModelProperty(value = "实际参与人集合(该方多个流程的实际参与人)")
        private List<ParticipantInfo> participants;

        @ApiModelProperty(value= "三方业务id")
        private String subTaskBizId;

        @ApiModelProperty(value= "三方业务类型")
        private Integer subTaskBizType;

        @ApiModelProperty(value= "抄送方")
        private List<UserBO> ccs;

        /**
         * 参与方信息
         *
         * <AUTHOR>
         * @since 2021/12/18
         */
        @Data
        public static class ParticipantInfo{

            @ApiModelProperty(value = "参与方ID")
            private String participantId;

            @ApiModelProperty(value = "参与方标签,如:甲方")
            private String participantLabel;

            @ApiModelProperty(value= "参与方类型如: 1:个人,1:企业")
            private Integer participantSubjectType;

            @ApiModelProperty(value= "参与方实例,具体的实际参与人信息")
            private UserBO participantInstance = new UserBO();

        }
    }
}

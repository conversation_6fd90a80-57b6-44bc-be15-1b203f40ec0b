package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2021/12/9
 */
@Setter
@Getter
public class ShareSignTaskStartFlowTemplateRequest extends ToString {
    @Valid
    @NotBlank(message = "流程模板id")
    @ApiModelProperty(value = "流程模板id")
    private String flowTemplateId;

    @ApiModelProperty(value = "流程模板id,0:个人，1企业")
    Integer subjectType;

    @ApiModelProperty(value = "clientId")
    private String clientId;

    private String subjectOid;

    private String operatorOid;

    private String appId;

    private String appName;

    @ApiModelProperty("是否需要进行印章授权校验")
    private boolean needSealAuthCheck;
}

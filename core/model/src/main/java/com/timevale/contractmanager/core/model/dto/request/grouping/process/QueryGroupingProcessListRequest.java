package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.service.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: hei pao
 * @since: 2019-09-17 16:19
 */
@Data
public class QueryGroupingProcessListRequest extends BaseModel {

    @Max(100)
    @ApiModelProperty(value = "分页参数", required = true)
    private int pageSize;

    @Max(10000)
    @ApiModelProperty(value = "分页参数", required = true)
    private int pageNum;

    /**
     * clientId
     */
    @ApiModelProperty(hidden = true)
    private String clientId;

    @ApiModelProperty("全字段模糊查询")
    private String fuzzyMatching;

    @ApiModelProperty("全字段列表模糊查询")
    private List<String> fuzzyMatchingList;

    @ApiModelProperty("模糊查询-标题")
    private String title;

    @ApiModelProperty("模糊查询-多标题")
    private List<String> titles;

    @ApiModelProperty("模糊查询=发起人")
    private String initiator;

    @ApiModelProperty("模糊查询=发起人主体")
    private String initiatorSubject;

    @ApiModelProperty("发起时间匹配")
    private Long createFrom;

    @ApiModelProperty("发起时间匹配")
    private Long createEnd;

    @ApiModelProperty("合同有效期匹配")
    private Long validFrom;

    @ApiModelProperty("合同有效期匹配")
    private Long validEnd;

    @ApiModelProperty(value = "文件夹id匹配", required = true)
    private String menuId;

    @ApiModelProperty("主体oid")
    private String subject;

    @ApiModelProperty("主体gid")
    private String subjectGid;

    @ApiModelProperty(value = "个人oid", required = true)
    @NotBlank(message = "个人oid不能为空")
    private String accountId;

    @ApiModelProperty("contractNo")
    private String contractNo;

    @ApiModelProperty("processStatus")
    private String processStatus;
    @ApiModelProperty("来源")
    private String source;

    @ApiModelProperty(value = "AI动态入参")
    private String dynamicInputParamList;

    @ApiModelProperty(value = "签订截止日,开始")
    private Long signValidityFrom;

    @ApiModelProperty(value = "签订截止日,结束")
    private Long signValidityEnd;

    @ApiModelProperty(value = "合同完成时间开始，智能台账2.0新增")
    private Long completeTimeFrom;

    @ApiModelProperty(value = "合同完成时间结束，智能台账2.0新增")
    private Long completeTimeEnd;

    @ApiModelProperty(value = "合同修改时间开始")
    private Long updateTimeFrom;

    @ApiModelProperty(value = "合同修改时间结束")
    private Long updateTimeEnd;

    @ApiModelProperty(value = "发起人和主体融合，智能台账2.0新增")
    private String initiatorMix;

    @ApiModelProperty(value = "发起人和主体融合，智能台账2.0新增")
    private List<String> initiatorMixList;

    @ApiModelProperty(value = "抄送人模糊查询")
    private String ccMatching;

    @ApiModelProperty(value = "抄送人列表模糊查询")
    private List<String> ccMatchingList;

    @ApiModelProperty("是否可包含审批状态的流程, 默认不包含")
    Boolean withApproving;

    @ApiModelProperty("合同文件名")
    private String contractFileName;

    @ApiModelProperty("附属文件名")
    private String attachmentFileName;
    private List<String> initiatorOids;
    private List<String> participantOids;
    private List<String> ccOids;
    private List<String> initiatorGids;
    private List<String> participantGids;
    private List<String> ccGids;

    /** 未续签 */
    private Boolean unRenewable = false;

    /** 无需续签*/
    private Boolean noNeedRenewable = false;

    private String renewableSubjectOid;

    private String renewableSubjectGid;

    @ApiModelProperty("续签状态")
    private List<Integer> renewalStatusList;

    /**
     * 流程业务类型列表
     */
    private List<Integer> processBizTypeList;

    private List<String> deptList;

    private List<String> templateList;

    private List<String> sealList;

    private List<String> subSubjectGidList;

    private String deptMatching;

    private String templateMatching;

    private String sealMatching;

    /**
     * 查询保密合同，1-查询未保密，2-查询保密
     */
    private Integer findSecret;

    /**
     * 解约原因
     */
    private String rescindRemark;

    private String processId;

    /**
     * 批量查询processId条件
     */
    private List<String> processIds;

    private String flowId;

    private String personName;

    private String personAccount;

    /** 签署主体名称 */
    private String subjectName;

    /** 流程来源列表 */
    private List<String> processFromList;

    /** 合同类型id列表 */
    private List<String> contractCategoryIds;

    private String signMode;

    private Boolean searchSignModeEmpty;

    private String cloudType;

    /** 和流程都相关的用户列表 */
    private List<Account> relatedPersonList;

    @Override
    public void valid() {

        super.valid();

        if (StringUtils.isNotEmpty(fuzzyMatching)) {
            fuzzyMatching = fuzzyMatching.replaceAll(" ", "");
            if (StringUtils.isNotEmpty(fuzzyMatching) && fuzzyMatching.length() > 50) {
                fuzzyMatching = fuzzyMatching.substring(0, 50);
            }
        }

        if (StringUtils.isNotEmpty(title)) {
            title = title.replaceAll(" ", "");
            if (StringUtils.isNotEmpty(title) && title.length() > 50) {
                title = title.substring(0, 50);
            }
        }

        if (StringUtils.isNotEmpty(initiator)) {
            initiator = initiator.replaceAll(" ", "");
            if (StringUtils.isNotEmpty(initiator) && initiator.length() > 50) {
                initiator = initiator.substring(0, 50);
            }
        }

        if (StringUtils.isNotEmpty(initiatorSubject)) {
            initiatorSubject = initiatorSubject.replaceAll(" ", "");
            if (StringUtils.isNotEmpty(initiatorSubject) && initiatorSubject.length() > 50) {
                initiatorSubject = initiatorSubject.substring(0, 50);
            }
        }
    }
}

package com.timevale.contractmanager.core.model.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/08
 */
@Data
@ApiModel("批量更新抄送人请求参数")
public class BatchUpdateCcsRequest extends UpdateCcRequest{

    @NotEmpty(message = "合同流程id列表不能为空")
    @Size(max = 100, message = "合同流程id列表单次不能超过100个")
    @ApiModelProperty(value = "合同流程id列表")
    private List<String> processIds;

}

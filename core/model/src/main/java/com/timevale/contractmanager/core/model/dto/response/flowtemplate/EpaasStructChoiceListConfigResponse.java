package com.timevale.contractmanager.core.model.dto.response.flowtemplate;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * epaas下拉组建配置
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EpaasStructChoiceListConfigResponse extends ToString {
    
    private List<StructConfig> structsConfig;

    @Getter
    @Setter
    public static final class StructConfig {
        @ApiModelProperty(value = "组建类型")
        private String type;

        @ApiModelProperty(value = "可选项")
        private List<option> options;
    }

    @Getter
    @Setter
    public static final class option {
        @ApiModelProperty(value = "类型 ALL-全部可选、TENANT-企业可选、 PERSON-个人可选")
        private String type;

        @ApiModelProperty(value = "key")
        private String key;

        @ApiModelProperty(value = "描述")
        private String desc;
    }
    
}

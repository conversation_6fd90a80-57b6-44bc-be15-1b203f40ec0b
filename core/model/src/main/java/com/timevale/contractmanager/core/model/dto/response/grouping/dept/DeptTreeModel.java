package com.timevale.contractmanager.core.model.dto.response.grouping.dept;

import com.timevale.easun.service.model.organization.output.BizEasunDeptTreeOutput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:ji<PERSON><PERSON>
 * @since 2021-04-01 13:28
 */
@Data
public class DeptTreeModel extends BizEasunDeptTreeOutput  {
	@ApiModelProperty(value = "true:部门负责人,false:非部门负责人")
	private Boolean memberMain;
}

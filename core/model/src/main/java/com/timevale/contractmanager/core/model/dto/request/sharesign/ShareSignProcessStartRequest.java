package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 通过流程模板id发起
 *
 * <AUTHOR>
 * @since 2019/11/7
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShareSignProcessStartRequest extends ToString {

    @ApiModelProperty(value = "扫码签任务id")
    private String shareSignTaskId;

    @ApiModelProperty(value = "扫码签参与方id")
    private String participantId;

    @ApiModelProperty(value = "扫码签扫码id")
    private String shareScanId;

    @ApiModelProperty(value = "签署人oid", required = true)
    @NotBlank(message = "accountId不能为空")
    private String accountId;

    @ApiModelProperty(value = "签署人手机号或邮箱")
    private String account;

    @ApiModelProperty(value = "签署企业名称")
    private String orgName;

    @ApiModelProperty(value = "登录token", required = true)
    private String token;

    /**
     * 是否走异步发起
     */
    @ApiModelProperty(value = "是否走异步发起",hidden = true)
    private boolean asyncStart = false;
}

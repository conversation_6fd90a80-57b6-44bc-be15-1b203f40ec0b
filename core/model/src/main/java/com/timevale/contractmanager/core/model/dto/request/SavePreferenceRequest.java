package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.annotation.EnumValidList;
import com.timevale.contractmanager.common.service.bean.Preference;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-17 15:58
 **/
@Data
@ApiModel
public class SavePreferenceRequest extends ToString {

    /**
     * oid
     */
    @ApiModelProperty(value = "oid")
    @NotBlank(message = "oid不能为空")
    private String orgId;

    /**
     * 偏好设置
     */
    @ApiModelProperty(value = "偏好值设置")
    @EnumValidList(message = "偏好设置key不合法,请检查!", target = ProcessPreferenceEnum.class, ignoreEmpty = false, srcField = "preferenceKey")
    private List<Preference> preferences;

}

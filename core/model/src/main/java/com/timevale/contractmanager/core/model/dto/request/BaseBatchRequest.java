package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量操作父类
 *
 * <AUTHOR> on 2019/7/30
 */
@Data
public class BaseBatchRequest extends ToString {

    @ApiModelProperty(value = "流程id集合", required = true)
    @NotNull(message = "processIds不能为空")
    @Size(min = 1, message = "processIds不能为空")
    @Size(max = 100, message = "批量处理最大支持选择100个流程")
    private List<String> processIds;

    @ApiModelProperty(value = "操作人账号id", required = true)
    @NotBlank(message = "accountId不能为空")
    private String accountId;

    @ApiModelProperty(value = "操作人所属主体id", required = true)
    @NotBlank(message = "subjectId不能为空")
    private String subjectId;

    @ApiModelProperty(value = "菜单id，归档场景下使用")
    private String menuId;
}

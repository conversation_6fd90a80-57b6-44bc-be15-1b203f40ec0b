package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.DocRelateRecord;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 合同文件解约记录
 *
 * <AUTHOR>
 * @since 2021-03-05 00:36
 **/
@Data
public class QueryRelationRecordResponse extends ToString {

    @ApiModelProperty(value = "文件关联记录")
    private List<DocRelateRecord> records;

}

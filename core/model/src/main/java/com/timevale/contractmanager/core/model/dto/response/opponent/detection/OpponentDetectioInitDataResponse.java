package com.timevale.contractmanager.core.model.dto.response.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:ji<PERSON><PERSON>
 * @since 2021-08-05 19:08
 */
@Data
public class OpponentDetectioInitDataResponse extends ToString {

	@ApiModelProperty("经营范围")
	private String socp;

	@ApiModelProperty("最新检测任务Id")
	private String detectionTaskId;

	@ApiModelProperty("检测任务类型:1:批量检测，2:新增检测")
	private Integer taskType;

	@ApiModelProperty("是否开启实时检测,1:开启,2:不开启")
	private Integer realTimeDetection;

	@ApiModelProperty("任务状态1:运行中，2:完成，3:异常终止")
	private Integer taskStatus;

	@ApiModelProperty("可用检测次数")
	private Integer usableDetectionNum=0;
}

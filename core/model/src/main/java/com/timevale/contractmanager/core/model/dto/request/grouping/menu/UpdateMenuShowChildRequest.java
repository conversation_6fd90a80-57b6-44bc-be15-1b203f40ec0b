package com.timevale.contractmanager.core.model.dto.request.grouping.menu;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: duhui
 * @since: 2021/12/13 5:05 下午
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateMenuShowChildRequest extends ToString {

    @ApiModelProperty(value = "分类id", required = true)
    @NotBlank
    private String menuId;

    @ApiModelProperty(value = "是否展示子分类合同", required = true)
    @NotNull
    private Integer showChild;
}

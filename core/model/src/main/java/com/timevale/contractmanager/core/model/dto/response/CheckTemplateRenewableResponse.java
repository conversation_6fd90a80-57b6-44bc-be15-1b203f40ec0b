package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 校验流程模板是否可用于续签响应数据
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class CheckTemplateRenewableResponse extends ToString {

    @ApiModelProperty("是否可用于续签")
    private boolean renewable;

    @ApiModelProperty("不可续签原因")
    private String reason;

    public static CheckTemplateRenewableResponse enable() {
        return new CheckTemplateRenewableResponse(true, null);
    }

    public static CheckTemplateRenewableResponse disable(String reason) {
        return new CheckTemplateRenewableResponse(false, reason);
    }
}

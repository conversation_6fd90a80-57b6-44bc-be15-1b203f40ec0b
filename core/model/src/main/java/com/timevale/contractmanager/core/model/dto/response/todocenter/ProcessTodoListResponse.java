package com.timevale.contractmanager.core.model.dto.response.todocenter;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 待办合同列表
 *
 * <AUTHOR>
 * @since 2021-11-24 16:02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessTodoListResponse extends ToString {

    @ApiModelProperty("待办合同列表")
    List<ProcessTodoInfoDTO> processInfoList;

    @ApiModelProperty("待办合同总数")
    Long total;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/** <AUTHOR> */
@Data
@ApiModel("合同流程催办参数")
@Valid
public class ProcessRushRequest extends ToString {

    @NotBlank(message = "催办人账号id不能为空")
    @ApiModelProperty(value = "催办人账号id", required = true)
    private String accountId;

    @ApiModelProperty(value = "催办主体id")
    private String subjectId;

    @ApiModelProperty(value = "被催办人账号id")
    private String rushAccountId;

    @ApiModelProperty("通知方式，逗号分割，1-短信，2-邮件 3-支付宝 4-钉钉，默认按照走流程设置")
    private String noticeTypes;
}

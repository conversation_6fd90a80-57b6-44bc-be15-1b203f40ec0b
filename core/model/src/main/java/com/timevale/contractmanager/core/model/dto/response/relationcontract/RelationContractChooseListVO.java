package com.timevale.contractmanager.core.model.dto.response.relationcontract;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by tianlei on 2022/7/1
 */
@Data
public class RelationContractChooseListVO extends ToString {


    @ApiModelProperty("总数")
    private Long total;

    @ApiModelProperty("列表数据")
    @HasTranslateField
    private List<RelationContractChooseProcessVO> list;

}

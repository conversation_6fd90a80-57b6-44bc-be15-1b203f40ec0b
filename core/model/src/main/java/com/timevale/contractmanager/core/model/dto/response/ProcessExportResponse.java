package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 异步批量导出excel响应数据
 *
 * @author: qianyi
 * @since: 2020-08-18
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessExportResponse extends ToString {
    /**
     * 没有实际意义，用于串联流程和给测试人员写集测断言
     */
    @ApiModelProperty("导出任务id， 后续可用于获取导出结果")
    private String exportTaskId;

    @ApiModelProperty("待导出记录条数")
    private Long totalSize;

    @ApiModelProperty(value = "是否跳转任务中心")
    private boolean goTask;
    @ApiModelProperty(value = "导出地址")
    private String url;

    public ProcessExportResponse(String exportTaskId){
        this.exportTaskId = exportTaskId;
    }

    public ProcessExportResponse(Long totalSize){
        this.totalSize = totalSize;
    }
}

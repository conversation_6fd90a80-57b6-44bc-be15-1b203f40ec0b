package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.ProcessInfoDisplayInfo;
import com.timevale.mandarin.common.result.BaseResult;
import com.timevale.signflow.search.docSearchService.result.ProcessInfoResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by ma<PERSON>,
 * on 06/Aug.
 */
@Data
public class ProcessQueryResponse extends BaseResult {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("列表数据")
    private List<ProcessInfoDisplayInfo> processInfoList;
}

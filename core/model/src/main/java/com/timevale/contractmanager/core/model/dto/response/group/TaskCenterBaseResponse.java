package com.timevale.contractmanager.core.model.dto.response.group;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: huifeng
 * @since: 2020-11-06 14:50
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskCenterBaseResponse extends ToString {
    @ApiModelProperty("后续操作类型 1-根据操作不同正常逻辑 2-跳转到operationData 3-提示其他任务进行中")
    private Integer operationType;

    @ApiModelProperty("当前进行中任务类型 1-批量发起合同 3-合同下载 4-导出合同任务明细 5-批量撤回 6-批量催办")
    private Integer existTaskType;

    @ApiModelProperty("operationType为2时:跳转地址url，operationType为1时且前次操作为批量下载时为合同下载链接")
    private String operationData;
}

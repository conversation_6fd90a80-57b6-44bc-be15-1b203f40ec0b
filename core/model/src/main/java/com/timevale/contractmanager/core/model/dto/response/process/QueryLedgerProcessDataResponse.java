package com.timevale.contractmanager.core.model.dto.response.process;

import com.alibaba.hbase.thrift2.generated.THBaseService.AsyncProcessor.put;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-10-15 19:08
 */
@Data
public class QueryLedgerProcessDataResponse extends ToString {
    @ApiModelProperty("多级表头")
    private List<LedgerTree> tableHead;
    @ApiModelProperty("表数据")
    private List<Map<String, Object>> tableData;

    @ApiModelProperty("总条数")
    private long total;

    @Data
    public static class LedgerTree {
        @ApiModelProperty("树名称")
        private String name;
        @ApiModelProperty("字段码")
        private String fieldCode;
        @ApiModelProperty("树属性名")
        private String prop;
        @ApiModelProperty("树节点子树列表")
        private List<LedgerTree> children;
        @ApiModelProperty("树节点路径")
        @JsonIgnore
        private String path;
        public static List<LedgerTree> getAllLeaves(LedgerTree node) {
            List<LedgerTree> result = new ArrayList<>();
            if (CollectionUtils.isEmpty(node.children)) {
                // 当前节点为叶子节点，直接返回
                result.add(node);
            } else {
                // 当前节点不是叶子节点，递归获取所有子节点的叶子节点
                for (LedgerTree child : node.children) {
                    result.addAll(getAllLeaves(child));
                }
            }
            return result;
        }
    }
}

package com.timevale.contractmanager.core.model.dto.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量移动文件夹
 *
 * <AUTHOR> on 2019/7/30
 */
@Data
public class BatchMoveRequest extends BaseBatchRequest {

    @ApiModelProperty(value = "文件夹id", required = true)
    @NotBlank(message = "folderId不能为空")
    private String folderId;

    @ApiModelProperty(value = " 移动类型， 0-移入， 1-移出", required = true)
    @NotNull(message = "moveType不能为空")
    private Integer moveType;
}

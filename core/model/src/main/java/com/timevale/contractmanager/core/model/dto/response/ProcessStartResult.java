package com.timevale.contractmanager.core.model.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessStartResult {

    @ApiModelProperty("合同id")
    private String processId;

    @ApiModelProperty(value = "结果地址，签署地址/填写地址短链")
    private String resultUrl;

    @ApiModelProperty(value = "结果地址，签署地址/填写地址长链")
    private String longResultUrl;

    @ApiModelProperty(value = "流程id")
    private String flowId;

    @ApiModelProperty(value = "流程类型")
    private Integer flowType;

    @ApiModelProperty(value = "批量组ID")
    private String groupId;

    @ApiModelProperty(value = "中间页地址")
    private String middleResultUrl;

    public ProcessStartResult(String processId, String flowId, Integer flowType) {
        this.processId = processId;
        this.flowId = flowId;
        this.flowType = flowType;
    }
}

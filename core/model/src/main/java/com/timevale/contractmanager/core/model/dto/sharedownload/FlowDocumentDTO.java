package com.timevale.contractmanager.core.model.dto.sharedownload;

import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/2/10
 * 流程文档
 */
@Data
public class FlowDocumentDTO {
	private String fileId;
	private String fileKey;
	private String fileName;
	/** 流程文件类型 请参考{@link ProcessFileType } */
	@ApiModelProperty(value = "文件类型，1-合并文件 2-附件", required = true)
	private Integer fileType;
	private String fileUrl;

}
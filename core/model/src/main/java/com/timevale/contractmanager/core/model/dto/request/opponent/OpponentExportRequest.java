package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 相对方导出
 *
 * <AUTHOR>
 * @since 2021-08-11 11:51
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class OpponentExportRequest extends ToString {

    @ApiModelProperty("导出类型 0-个人；1-企业")
    @NotNull(message = "导出类型不能为空")
    private Integer exportType;

    @Min(0)
    @Max(3)
    @ApiModelProperty("认证状态 0-未实名；1-实名中；2-已实名；3-已注销")
    private Integer authorizeType;

    @Min(1)
    @Max(2)
    @ApiModelProperty("风险等级 1-白名单; 2-黑名单")
    private Integer riskLevel;

    @ApiModelProperty("备注信息，模糊匹配")
    private String fuzzyDesc;

    @ApiModelProperty("企业名称/个人名，模糊匹配")
    private String name;

    @ApiModelProperty("手机号/邮箱，模糊匹配")
    private String fuzzyEntityUniqueId;

    @ApiModelProperty("所属企业 1-有; 2-没有")
    private Integer attachedEntityType;

    @ApiModelProperty("企业实体uuid")
    private String organizationId;

    @ApiModelProperty(value = "企业统一社会信用代码", required = false)
    private String creditCode;

    @ApiModelProperty(value = "法定代表人姓名", required = false)
    private String legalPersonName;
}

package com.timevale.contractmanager.core.model.dto.response.grouping.process;

import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 获取流程发起数据的接口响应（文件列表）
 *
 * @author: akun
 * @since: 2020-05-21 16:24
 */
@Data
public class GetStartInfoResponse extends ToString {

    @ApiModelProperty(value = "文件列表", required = true)
    @NotEmpty(message = "文件列表不能为空")
    private List<FileBO> files;
}

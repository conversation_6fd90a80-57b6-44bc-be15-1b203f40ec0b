package com.timevale.contractmanager.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/11/14 15:20
 */
@Getter
public enum ParticipantSetUpTypeEnum {

    AUTH_WAY("authWay", "身份验证方式", 100),
    SEAL("seal", "签章方式", 90),
    WIL<PERSON>("will", "意愿认证方式", 80),
    NOTICE_TYPE("noticeType", "通知方式", 75),
    ATTACHMENT_CONFIG("attachmentConfig", "指定签署附件", 50),
    FORCE_READ_END("forceReadEnd", "必须阅读到底", 60),
    FORCE_READ_TIME("forceReadTime", "最低阅读时长", 70),
    SIGN_TIPS("signTips", "展示签署声明", 40),
    ;

    private String code;
    private String description;
    private Integer order;


    ParticipantSetUpTypeEnum(String code, String description, Integer order) {
        this.code = code;
        this.description = description;
        this.order = order;
    }
}

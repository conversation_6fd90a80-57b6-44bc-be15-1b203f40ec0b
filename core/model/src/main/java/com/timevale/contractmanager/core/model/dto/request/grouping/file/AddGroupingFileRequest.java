package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 添加合同归档操作请求
 *
 * <p>将合同文件挂载到指定的目录下
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class AddGroupingFileRequest extends CommonRequest {

    @ApiModelProperty(value = "流程列表", required = true)
    @NotNull(message = "processIdList不能为空")
    @Size(min = 1, message = "流程不能为空")
    @Size(max = 50, message = "最大支持50个流程")
    private List<String> processIdList;

    @ApiModelProperty(value = "合同主题")
    private String title;

    @ApiModelProperty(value = "合同有效时间yyyy-MM-dd")
    private String validTime;

    @ApiModelProperty(value = "合同归属的菜单id集合", required = true)
    @NotNull(message = "menuIdList不能为空")
    @Size(min = 1, message = "分类不能为空")
    @Size(max = 10, message = "最大支持10个分类")
    private List<String> menuIdList;
}

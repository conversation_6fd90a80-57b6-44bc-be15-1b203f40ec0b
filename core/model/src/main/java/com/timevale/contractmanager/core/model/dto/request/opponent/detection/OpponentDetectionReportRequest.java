package com.timevale.contractmanager.core.model.dto.request.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-08-22 14:14
 */
@Data
public class OpponentDetectionReportRequest extends ToString {
	@ApiModelProperty("检测任务id")
	private String detectionTaskId;

	@ApiModelProperty("企业名称")
	private String orgName;

	@ApiModelProperty("风险等级类型,低:10、20,高:70,极高:100")
	private List<Integer> riskLevel;

	@ApiModelProperty(value = "问题编号,1:该企业未完成在e签宝企业实名认证,2:未查询到名称," +
			"3:经营状态为停业、吊销、清算或注销,4:经营状态为迁入、迁出,5:经营范围不符合要求")
	private List<Integer> problemLevel;

	@ApiModelProperty(value = "1:升序(asc),2:降序(desc)")
	private Integer sort;

	@Max(100)
	@ApiModelProperty(value = "分页参数", required = true)
	@NotNull
	private Integer pageSize = 20;

	@ApiModelProperty(value = "分页参数", required = true)
	@NotNull
	private Integer pageNum = 1;

	@ApiModelProperty(value = "开始日期")
	private Long startDate;

	@ApiModelProperty(value = "结束日期")
	private Long endDate;
}

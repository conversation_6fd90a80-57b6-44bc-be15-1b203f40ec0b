package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.contractmanager.common.service.bean.ProcessAccount;
import com.timevale.contractmanager.common.service.bean.SupportOperate;
import com.timevale.contractmanager.common.service.bean.process.ContractFileBean;
import com.timevale.contractmanager.common.service.bean.process.ProcessApprovalBean;
import com.timevale.contractmanager.common.service.bean.process.ProcessOperatorBean;
import com.timevale.contractmanager.common.service.bean.process.ProcessParticipantBean;
import com.timevale.contractmanager.common.service.bean.process.ProcessRelationFileBean;
import com.timevale.contractmanager.common.service.bean.process.SealApprovalBean;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.common.service.enums.ProcessSecretEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProcessInfoDisplayInfoV3 {

    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("流程业务类型，0-普通流程，1-解约流程，2-重新发起流程，3-续签流程")
    private Integer processBizType;

    @ApiModelProperty("流程合同保密类型，1-不保密，2-全部保密，3-部分保密, 默认不保密")
    private Integer processSecretType = ProcessSecretEnum.NONE_SECRET.getType();

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("发起时间")
    private Long processCreateTime;

    @ApiModelProperty("更新时间")
    private Long processUpdateTime;

    @ApiModelProperty("签署完成时间(非撤回、到期时间)")
    private Long completeTime;

    @ApiModelProperty("合同到期时间")
    private Long contractValidity;

    @ApiModelProperty("签订截止时间")
    private Long signValidity;

    @ApiModelProperty("创建方式")
    private Integer createWay;

    @ApiModelProperty("来源")
    private Integer source;

    @ApiModelProperty("流程描述")
    private String processDesc;

    @ApiModelProperty("流程状态")
    private Integer processStatus;

    @ApiModelProperty("流程状态描述")
    private String processStatusDesc;

    @ApiModelProperty("发起人")
    private ProcessAccount initiatorAccount;

    @ApiModelProperty("所有参与人")
    private List<ProcessParticipantBean> participantAccountList;

    @ApiModelProperty("需要处理的人")
    List<ProcessOperatorBean> currentOperatorList;

    @ApiModelProperty(
            value = "当前登录人是否能对此流程操作-签署or填写等",
            example = "true"
    )
    private Boolean operable;

    @ApiModelProperty(value = "包含填写流程")
    private Integer withFillProcess;

    @ApiModelProperty(value = "是否顺序填")
    private boolean orderWrite;

    @ApiModelProperty(value = "是否顺序签")
    private boolean orderSign;

    private ProcessApprovalBean processApproval;

    private List<SealApprovalBean> sealApproval;

    @ApiModelProperty(value = "节点事件的聚合信息")
    private ProcessAggregationInfo aggregationInfo;

    @ApiModelProperty(value = "流程类型")
    private Integer processType;

    @ApiModelProperty("是否需要续签")
    private boolean renewable;

    @ApiModelProperty("续签状态, 0-无需续签，1-可续签，2-续签中，3-部分续签，4-已续签")
    private Integer renewStatus;

    @ApiModelProperty("是否转交流程")
    private boolean transfer;

    private List<ContractFileBean> contractFiles;

    private List<ProcessRelationFileBean> rescindList;

    private List<ProcessRelationFileBean> renewList;

    @ApiModelProperty("是否可解约")
    private Boolean canRescind;

    @ApiModelProperty("发起来源")
    private String initiateFrom;

    @ApiModelProperty("不能解约原因")
    private String cannotRescindReason;

    @ApiModelProperty("专属云项目Id")
    private String dedicatedCloudId;

    @ApiModelProperty("签署模式")
    private String signMode;


    /**
     * 支持的操作
     */
    private List<SupportOperate> supportOperate;

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
        ProcessStatusEnum statusEnum = ProcessStatusEnum.parse(processStatus);
        if (null != statusEnum) {
            this.processStatusDesc = statusEnum.getStatusDesc();
        }
    }
}

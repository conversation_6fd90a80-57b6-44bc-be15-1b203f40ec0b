package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021-02-05
 */
@Data
public class ShareSignTaskList extends ToString {
    @ApiModelProperty("扫码签任务Id")
    private String taskId;
    @ApiModelProperty("扫码签任务名称")
    private String taskName;
    @ApiModelProperty("扫码签任务类型，1-单方扫码，2-多方扫码")
    private Integer taskType;
    @ApiModelProperty("扫码签任务业务Id")
    private String taskBizId;
    @ApiModelProperty("任务完成数")
    private int taskDone;
    @ApiModelProperty("任务参与数")
    private int taskNum;
    @ApiModelProperty("任务上限数")
    private Integer taskTotal;
    @ApiModelProperty("任务状态，0-关闭，1-开启")
    private Integer taskStatus;
    @ApiModelProperty("任务发起时间")
    private Date startTime;
    @ApiModelProperty("任务截止时间")
    private Date endTime;
    @ApiModelProperty("是否私密分享，true: 是，false: 否")
    private Boolean privateShare;
}

package com.timevale.contractmanager.core.model.dto.request.autoArchive;

import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveOperator;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-05-08 15:46
 */
@Data
public class AutoArchiveUpdateOperatorRequest extends ToString {
	@ApiModelProperty(value = "目录名称", required = true)
	@NotBlank(message = "name不能为空")
	@Length(max = 50, message = "名字长度不能大于50")
	private String name;

	@ApiModelProperty(value = "需要绑定的分类Id")
	private String bindingMenuId;

	@ApiModelProperty(value = "父节点ID，对应父目录的menuId,根目录为空")
	private String parentMenuId;

	@ApiModelProperty(value = "移动后排序，同一目录移动时需要传入排序")
	private Integer targetOrder;

	@ApiModelProperty(value = "原始的目录名称", required = true)
	@NotBlank(message = "sourceName不能为空")
	@Length(max = 50, message = "名字长度不能大于50")
	private String sourceName;

	@ApiModelProperty(value = "原始的父节点ID，对应父目录的menuId,根目录为空")
	private String sourceParentMenuId;


	@ApiModelProperty(value = "绑定的台账id")
	private String bindingFormId;

	@ApiModelProperty(value = "原始绑定的台账id")
	private String sourceBindingFormId;

	@ApiModelProperty(value = "规则状态0:开启,1:关闭,2:失效,3:运行中")
	private int status = 1;

	@ApiModelProperty(value = "自动归档条件")
	private List<AutoArchiveOperator> conditions;

	@ApiModelProperty(value = "移除不符合规则合同:0开启,1:关闭")
	private Integer revomeContract = 1;

	@ApiModelProperty(value = "是否修改归档条件 false:未修改,true:修改")
	private Boolean modifyMark;

	@ApiModelProperty(value = "是否统一子分类台账")
	private Integer unityForm;
}

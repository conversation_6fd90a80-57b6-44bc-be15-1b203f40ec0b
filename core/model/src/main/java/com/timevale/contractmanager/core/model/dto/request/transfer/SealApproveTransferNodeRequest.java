package com.timevale.contractmanager.core.model.dto.request.transfer;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/28
 */
@Setter
@Getter
public class SealApproveTransferNodeRequest extends ToString {

    @ApiModelProperty("转交人oid，目前只支持单个人查询")
    private String transferUser;

    @ApiModelProperty("用印审批id")
    private List<String> sealApproveIds;
}

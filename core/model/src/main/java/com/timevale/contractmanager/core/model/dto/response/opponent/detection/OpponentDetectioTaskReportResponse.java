package com.timevale.contractmanager.core.model.dto.response.opponent.detection;

import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionProblemInfoDO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionProblemInfoBO;
import com.timevale.contractmanager.core.model.bo.opponent.detection.OpponentDetectionReportBO;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-08-04 19:50
 */
@Data
@Builder
public class OpponentDetectioTaskReportResponse extends ToString {
	@ApiModelProperty("企业名称")
	private String orgName;

	@ApiModelProperty("问题")
	@HasTranslateField
	private List<OpponentDetectionProblemInfoBO> problemInfoDOS;

	@ApiModelProperty("黑名单,1:白名单,2:黑名单")
	private Integer blackLevel;

	@ApiModelProperty("检测时间")
	private Date detectionTime;

	@ApiModelProperty("相对方企业uuid")
	private String organizationId;

	@ApiModelProperty("企业oid")
	private String entityOid;

	@ApiModelProperty("统一社会信用代码")
	private String socialCreditCode;
}

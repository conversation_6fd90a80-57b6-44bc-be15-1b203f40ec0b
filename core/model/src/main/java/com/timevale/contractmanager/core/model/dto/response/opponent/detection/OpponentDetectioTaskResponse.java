package com.timevale.contractmanager.core.model.dto.response.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-04 19:40
 */
@Data
public class OpponentDetectioTaskResponse extends ToString {
	@ApiModelProperty("检测总数")
	private Integer detectionTotality;

	@ApiModelProperty("检测完成数量")
	private Integer detectionQuantityCompletion;

	@ApiModelProperty("运行时间")
	private Long elapsedTime;

	@ApiModelProperty("发现的问题数量")
	private Long reportProblems;

	@ApiModelProperty("检测时间")
	private Date detectionTime;

	@ApiModelProperty("风险企业数量")
	private Integer ventureBusinessNum;

	@ApiModelProperty("检测任务类型:1:批量检测，2:新增检测")
	private Integer taskType;

	@ApiModelProperty("任务状态1:运行中，2:完成，3:异常终止")
	private Integer taskStatus;

	@ApiModelProperty("检测任务id")
	private String detectionTaskId;
}

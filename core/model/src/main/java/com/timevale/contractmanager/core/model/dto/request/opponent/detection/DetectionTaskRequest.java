package com.timevale.contractmanager.core.model.dto.request.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-31 10:58
 */
@Data
public class DetectionTaskRequest extends ToString {
	@ApiModelProperty(value = "分页参数", required = true)
	@NotNull(message = "每页大小不能为空")
	private Integer pageSize;

	@ApiModelProperty(value = "分页参数", required = true)
	@NotNull(message = "页码不能为空")
	private Integer pageNum;

	@ApiModelProperty(value = "开始日期")
	private Long startDate;
	@ApiModelProperty(value = "结束日期")
	private Long endDate;
}

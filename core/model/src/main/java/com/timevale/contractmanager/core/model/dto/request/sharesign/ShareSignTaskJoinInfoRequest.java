package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询扫码签加入信息入参
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
public class ShareSignTaskJoinInfoRequest extends ToString {
    @ApiModelProperty("扫码签任务id")
    @NotBlank(message = "扫码签任务id不能为空")
    private String taskId;

    @ApiModelProperty("企业名称")
    private String subjectName;
}

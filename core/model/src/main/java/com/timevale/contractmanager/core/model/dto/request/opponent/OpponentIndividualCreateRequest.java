package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @author: huifeng
 * @since: 2021-01-22 13:42
 */
@Data
public class OpponentIndividualCreateRequest extends ToString {
    @ApiModelProperty("联系方式，手机号或邮箱")
    @NotBlank(message = "手机号/邮箱不能为空")
    private String contact;

    @ApiModelProperty("个人名")
    @NotBlank(message = "姓名不能为空")
    @Length(max = 50, message = "姓名不能超过50个字")
    private String individualName;

    @ApiModelProperty("关联企业相对方uuid列表")
    private Set<String> organizationIds;

    @ApiModelProperty("备注")
    @Length(max = 200, message = "备注不能超过200个字")
    private String desc;
}

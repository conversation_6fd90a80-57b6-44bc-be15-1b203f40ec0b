package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("合同流程续签管理列表查询参数")
@Valid
public class ProcessRenewalQueryRequest extends ToString {

    @ApiModelProperty(value = "查询用户账号id")
    private String accountId;

    @ApiModelProperty("合同到期时间段的起始时间")
    private Long contractValidityFrom;

    @ApiModelProperty("合同到期时间段的结束时间")
    private Long contractValidityTo;

    @ApiModelProperty("签署完成时间段的起始时间")
    private Long completeTimeFrom;

    @ApiModelProperty("签署完成时间段的结束时间")
    private Long completeTimeTo;

    @Min(value = 0, message = "pageNum >=0")
    @NotNull(message = "pageNum 不能为空")
    @ApiModelProperty(value = "页码", required = true, position = 1)
    private Integer pageNum;

    @NotNull(message = "pageSize 不能为空")
    @Min(value = 0, message = "pageSize >=0")
    @ApiModelProperty(value = "每页大小", required = true, position = 1)
    private Integer pageSize;

    @ApiModelProperty(value = "续签状态, 多个以逗号分隔, 0-无需续签 1-未续签 2-续签中，3-部分续签, 4-已续签")
    private String renewStatus;

    @ApiModelProperty(value = "模糊查询字段, 支持合同主体、发起人/参与人姓名或账号")
    private String fuzzyMatching;

    @ApiModelProperty(value = "合同主题模糊查询")
    private String title;

    @ApiModelProperty(value = "人名模糊查询")
    private String personName;

    @ApiModelProperty(value = "企业名称模糊查询")
    private String subjectName;

    @ApiModelProperty(value = "手机号/邮箱查询")
    private String account;

    @ApiModelProperty(value = "合同编号模糊查询")
    private String contractNo;

    @ApiModelProperty(value = "合同类型id")
    private String contractCategoryId;

    @ApiModelProperty(value = "签署模式")
    private String signMode;

    @ApiModelProperty(value = "工号")
    private String jobNumber;

    @ApiModelProperty(value = "用户昵称")
    private String personNickName;

    @ApiModelProperty(value = "用户证件号")
    private String personIdentity;

    @ApiModelProperty(value = "转交标识")
    private Boolean transferFlag;

}

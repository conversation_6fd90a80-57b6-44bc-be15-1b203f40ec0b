package com.timevale.contractmanager.core.model.dto.response.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-04 19:53
 */
@Data
public class OpponentDetectioTaskReportListResponse extends ToString {

	@ApiModelProperty("总数")
	private long total;

	@ApiModelProperty("检测问题报告")
	@HasTranslateField
	private List<OpponentDetectioTaskReportResponse> reportResponses;
}

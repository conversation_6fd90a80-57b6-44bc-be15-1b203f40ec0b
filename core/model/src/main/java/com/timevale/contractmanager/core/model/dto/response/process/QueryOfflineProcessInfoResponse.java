package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.contractmanager.core.model.dto.process.OfflineProcessAccount;
import com.timevale.contractmanager.core.model.dto.process.OfflineProcessFile;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 查询线下流程信息响应对象
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
public class QueryOfflineProcessInfoResponse extends ToString {

    /** 流程id */
    private String processId;

    /** 流程名称 */
    private String title;

    /** 合同到期时间 */
    private Long contractValidity;

    /** 合同文件列表 */
    private List<OfflineProcessFile> contractFiles;

    /** 流程用户信息 */
    private List<OfflineProcessAccount> processAccounts;
}

package com.timevale.contractmanager.core.model.dto.response.relationcontract;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by tianlei on 2022/7/1
 */
@Data
public class RelationContractAccount {

    @ApiModelProperty("gid")
    private String gid;

    @ApiModelProperty("oid")
    private String oid;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("nickname")
    private String nickname;

    @ApiModelProperty("true 是企业")
    private Boolean organ;

    @ApiModelProperty("mobile")
    private String mobile;

    @ApiModelProperty("email")
    private String email;

    private Boolean deleted;


}

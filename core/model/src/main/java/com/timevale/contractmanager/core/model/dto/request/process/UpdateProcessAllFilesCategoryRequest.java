package com.timevale.contractmanager.core.model.dto.request.process;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 修改流程所有文件合同类型请求参数
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
public class UpdateProcessAllFilesCategoryRequest extends ToString {

    /** 合同流程id */
    @NotBlank(message = "合同流程id不能为空")
    private String processId;

    @NotBlank(message = "主体oid不能为空")
    private String subjectOid;

    @NotBlank(message = "主体gid不能为空")
    private String subjectGid;

    @NotBlank(message = "合同类型id不能为空")
    private String categoryId;
}

package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.contractmanager.core.model.dto.process.signsetup.base.BaseSetUpDisplayRuleDTO;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/19 10:18
 */
@Data
public class ProcessesParticipantInitConfigResponse extends ToString {

    private List<ParticipantSetUpDisplayRuleVO> displayRule;
}

package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 按钮配置结果
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
public class ProcessButtonConfigResponse extends ToString {

    @ApiModelProperty("是否显示按钮")
    private Boolean show;

    @ApiModelProperty("跳转地址")
    private String url;

    @ApiModelProperty("按钮类型")
    private String buttonType;

    @ApiModelProperty("按钮场景")
    private String buttonScene;
    
    @ApiModelProperty("提示信息")
    private String tips;
    
}

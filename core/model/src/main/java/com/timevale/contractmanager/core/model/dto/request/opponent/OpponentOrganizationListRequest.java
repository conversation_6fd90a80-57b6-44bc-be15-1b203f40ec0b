package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * @author: huifeng
 * @since: 2021-01-22 11:51
 **/
@Data
public class OpponentOrganizationListRequest extends ToString {
    @Max(100)
    @ApiModelProperty(value = "分页参数", required = true)
    private Integer pageSize = 20;

    @ApiModelProperty(value = "分页参数", required = true)
    private Integer pageNum = 1;

    @Min(0)
    @Max(3)
    @ApiModelProperty("认证状态 0-未实名；1-实名中；2-已实名；3-已注销")
    private Integer authorizeType;

    @Min(1)
    @Max(2)
    @ApiModelProperty("风险等级 1-白名单; 2-黑名单")
    private Integer riskLevel;

    @ApiModelProperty("备注信息，模糊匹配")
    @Length(max = 200, message = "备注最长不可超过200字符")
    private String fuzzyDesc;

    @ApiModelProperty("企业名称，模糊匹配")
    @Length(max = 200, message = "企业名称最长不可超过200字符")
    private String fuzzyOrganizationName;

    @ApiModelProperty(value = "企业统一社会信用代码", required = false)
    private String creditCode;

    @ApiModelProperty(value = "法定代表人姓名", required = false)
    private String legalPersonName;

    private String scrollId;

    private Boolean useScroll;
}

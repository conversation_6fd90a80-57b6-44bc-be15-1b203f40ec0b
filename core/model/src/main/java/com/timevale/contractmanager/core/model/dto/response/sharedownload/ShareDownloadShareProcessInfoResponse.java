package com.timevale.contractmanager.core.model.dto.response.sharedownload;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/10 分享下载的分享信息
 */
@ApiModel("分享下载的合同信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareDownloadShareProcessInfoResponse extends ToString {
    @ApiModelProperty(value = "文件信息")
    private List<FlowDocumentVO> files;

    @ApiModelProperty(value = "分享的主体oid")
    private String shareSubjectOid;
}

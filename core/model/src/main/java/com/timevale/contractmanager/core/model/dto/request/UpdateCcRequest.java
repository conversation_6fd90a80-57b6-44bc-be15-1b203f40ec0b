package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.common.service.constant.SystemConstant;
import com.timevale.contractmanager.core.model.bo.UserBO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2022/1/5
 */
@Data
@ApiModel("修改抄送人入参")
public class UpdateCcRequest extends ToString {

    @ApiModelProperty(value = "菜单id")
    private String menuId;

    @ApiModelProperty(value = "当前抄送人列表")
    @Size(max = 30, message = "抄送人最大上限30人")
    private List<UserBO> ccs;

    @ApiModelProperty(value = "修改类型 新增：ADD,替换：REPLACE 默认为替换")
    @Pattern(regexp = "ADD|REPLACE", message = "修改类型不支持")
    private String operationType = SystemConstant.PROCESS_UPDATE_CC_REPLACE;
}

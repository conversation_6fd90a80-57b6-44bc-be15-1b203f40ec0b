package com.timevale.contractmanager.core.model.enums;

/**
 * <AUTHOR>
 *
 * @date 2022/2/22
 */
public enum SensorEnum {
    PLATFORM_TYPE(
            "微信小程序：WE_CHAT,支付宝小程序：ALI_PAY_MINI,安卓app：APP_ANDROID,ios app：APP_IOS,H5：H5,WEB：WEB,钉钉微应用：DING_TALK,开放服务：OPEN_SERVICE",
            "PlatformType",
            "platformType"),

    OID("操作人oid","oid", ""),

    OPERATOR_OID("操作人oid","operatorOid", "operatorOid" ),

    OPERATOR_GID("操作人gid","operatorGid", "operatorGid" ),

    AUTHORIZED_OID("操作主体oid","authorizedOid", "authorizedOid"),

    AUTHORIZED_GID("操作人主体gid","authorizedGid", "authorizedGid" ),

    DURATION("合同持续时长","duration", "" ),

    VIP_VERSION("会员版本","vipVersion", "" ),

    PROCESS_ID("流程id","processId", ""),

    PROCESS_TYPE("流程类型， 普通流程、复制重发流程、解约流程","processType", ""),

    APP_ID("应用id","appId", "appId"),

    PROCESS_START_TYPE("流程发起方式,直接发起、使用模板发起","processStartType", ""),

    PROCESS_START_WAY("流程发起模式, 单任务普通发起、单任务扫码发起、批量普通发起、批量扫码签发起","processStartWay", "" ),

    FILE_AMOUNT("签署文件数","fileamount", ""),

    ATTACHMENT_AMOUNT("附属材料数","attachmentAmount", "" ),

    FILL_AMOUNT("填写方数量","fillAmount", ""),

    PERSON_SIGNATORY_AMOUNT("个人主体签署方数量","personSignatoryAmount", ""),

    ORG_SIGNATORY_AMOUNT("企业主体签署方数量","orgSignatoryAmount", "" ),

    AUTHORIZED_OID_SIGN("当前空间主体是否是签署方","authorizedOidSign", ""),

    CC_AMOUNT("抄送方数量","ccAmount", "" ),

    SIGN_DATE_LIMIT("是否设置签署截止日期","signdatelimit", "" ),

    CONTRACT_DATE_LIMIT("是否设置合同到期日期","contractdatelimit", ""),

    ORDER_SIGN("是否指定了顺序","ordersign", ""),

    TEMPLATE_SEAL_AMOUNT("指定模板章数","templateSealAmount", ""),

    HAND_DRAW_AMOUNT("指定AI手绘章数","handdrawAmount", ""),

    AI_HAND_DRAW_AMOUNT("指定普通手绘章数","aiHanddrawAmount", "" ),

    CONTRACT_APPROVAL_ID("合同审批流程id","contractApprovalId", ""),

    FILLING_ID("填写流程id","fillingId", ""),

    FLOW_ID("签署流程id","flowId", ""),

    PROCESS_ID_STATUS("合同流程状态","processIdStatus", ""),

    ORG_NAME("","orgName", ""),

    TENANT_ID("","tenantId", ""),

    FLOW_TYPE("","flowType", ""),

    TRANSFER_TYPE("转交类型， 自动转交 or 手动转交","transferType", ""),

    TRANSFER_WAY("转交模式， 待办任务转交 or 全流程转交","transferWay", "" ),

    SCAN_INITIATE_SUBJECT("","scan_initiate_subject", ""),

    SCAN_CODE_NUMBER_MAXIMUM("","scan_code_number_maximum", ""),

    MENU_ID("","menu_id", "" ),

    RECEIVER_ID("","receiver_id", "" ),

    PROBLEM_TYPE("","problem_type", "" ),

    NUMBER_OF_PROCESS("合同流程数量","number_of_process", ""),

    RETURN_TIME("处理耗时","return_time", ""),

    WITHDRAW_REASON("撤回原因","withdraw_reason", ""),

    PROCESSING_RESULT("处理结果","processing_result", ""),

    PROCESS_STATUS("流程状态","process_status", ""),

    SHARE_TYPE("分享类型","share_type", ""),

    AUTHENTICATION_RESULT("鉴权结果","authentication_result", ""),

    AUTHENTICATION_FAILURE_REASON("鉴权失败原因","authentication_failure_reason", ""),

    IS_SECRECY("是否保密","is_secrecy", ""),

    NUMBER_OF_PEOPLE("人数","number_of_people", ""),

    SET_TYPE("设置类型","set_type", ""),

    VERSION("版本号","version", ""),

    CREATE_TYPE("创建模板方式","create_type", ""),

    TEMPLATE_NAME("模板名称","template_name", ""),

    NUMBER_OF_CONTRACT_FILE("合同文件数量","number_of_contract_file", ""),

    NUMBER_OF_ATTACHED_FILE("附属文件数量","number_of_attached_file", ""),

    NUMBER_OF_FILL_ENTERPRISE_FILE("企业填写方数量","number_of_fill_enterprise_file", ""),

    NUMBER_OF_SIGN_ENTERPRISE_FILE("企业签署方数量","number_of_sign_enterprise_file", ""),

    NUMBER_OF_CC_ENTERPRISE_FILE("企业抄送方数量","number_of_cc_enterprise_file", ""),

    NUMBER_OF_FILL_PERSON_FILE("个人填写方数量","number_of_fill_person_file", ""),

    NUMBER_OF_SIGN_PERSON_FILE("个人签署方数量","number_of_sign_person_file", ""),

    NUMBER_OF_CC_PERSON_FILE("个人抄送方数量","number_of_cc_person_file", ""),

    NUMBER_OF_FIXED_PARTICIPANTS("参与方为固定成员的数量","number_of_fixed_participants", ""),

    NUMBER_OF_INITIATOR("参与方为发起人本人的数量","number_of_initiator", ""),

    NUMBER_OF_FAQISHIZHIDING("参与方为使用模板时指定的数量","number_of_faqishizhiding", ""),

    NUMBER_OF_HAND_SEAL("手绘章的签署方数量","number_of_hand_seal", ""),

    NUMBER_OF_AI_SEAL("AI章的签署方数量","number_of_ai_seal", ""),

    NUMBER_OF_TEMPLATE_SEAL("模板章的签署方数量","number_of_template_seal", ""),

    NUMBER_OF_FACE_AUTHENTICATION("人脸认证的参与方数量","number_of_face_authentication", ""),

    NUMBER_OF_MESSAGE_AUTHENTICATION("短信认证的参与方数量","number_of_message_authentication", ""),

    NUMBER_OF_PASSWORD_AUTHENTICATION("密码认证的参与方数量","number_of_password_authentication", ""),

    NUMBER_OF_EMAIL_AUTHENTICATION("邮箱认证的参与方数量","number_of_email_authentication", ""),

    NUMBER_OF_VEDIO_AUTHENTICATION("视频认证的参与方数量","number_of_vedio_authentication", ""),

    IS_SIGN_ORDER("是否设置了顺序签署","is_sign_order", ""),

    SIGN_DEADLINE_TYPE("签订截止设置类型","sign_deadline_type", ""),

    NUMBER_OF_SIGN_DEADLINE_DAYS("签订截止设置时长","number_of_sign_deadline_days", ""),

    VALIDITY_DEADLINE_TYPE("合同到期设置类型","validity_deadline_type", ""),

    NUMBER_OF_VALIDITY_DEADLINE_DAYS("合同到期设置时长","number_of_validity_deadline_days", ""),

    NUMBER_OF_SEAL("签署区数量","number_of_seal", ""),

    NUMBER_OF_PAGING_SEAL("骑缝章数量","number_of_paging_seal", ""),

    NUMBER_OF_PICTURE("图片控件数量","number_of_picture", ""),

    NUMBER_OF_WIDGET("总控件数量","number_of_widget", ""),

    NUMBER_OF_SUB_ENTERPRISE("关联企业数量","number_of_sub_enterprise", ""),

    NUMBER_OF_USE_ABLE_DEPARTMENT("可使用部门数量","number_of_use_able_department", ""),

    NUMBER_OF_USE_ABLE_STAFF("可使用员工数量","number_of_use_able_staff", ""),

    NUMBER_OF_EDIT_ABLE_DEPARTMENT("可编辑部门数量","number_of_edit_able_department", ""),

    NUMBER_OF_EDIT_ABLE_STAFF("可编辑员工数量","number_of_edit_able_staff", ""),

    REASON("原因", "reason", ""),

    NUMBER_OF_ORIGINAL_FILE("原合同数量", "number_of_original_file", ""),
    ;

    /** 埋点key （产品提供） */
    private String key;
    /** 埋点映射字段 */
    private String field;
    /** 字段备注 */
    private String desc;

    SensorEnum(String desc, String key, String field) {
        this.key = key;
        this.field = field;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getField() {
        return field;
    }
}

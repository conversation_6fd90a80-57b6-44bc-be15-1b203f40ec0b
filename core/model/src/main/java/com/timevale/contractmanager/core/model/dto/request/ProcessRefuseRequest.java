package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.docmanager.service.aop.annotation.StringCheckValid;
import com.timevale.docmanager.service.enums.StringCheckTypeEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.minidev.json.annotate.JsonIgnore;

/**
 * @author: qianyi
 * @since: 2020-11-30
 **/
@Data
@ApiModel
public class ProcessRefuseRequest extends ToString {

    @ApiModelProperty(value = "用户id", hidden = true)
    private String accountId;

    @ApiModelProperty("拒绝原因")
    @StringCheckValid(message = "拒填理由不允许包含特殊字符", types = {StringCheckTypeEnum.EMOJI})
    private String reason;
    @ApiModelProperty("平台信息")
    @JsonIgnore
    private String clientId;
}

package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 移出合同文件操作请求
 * @author: huifeng
 * @since: 2020-12-21 11:51
 **/
@Data
public class ReMoveGroupingFileRequestV2 extends ToString {
    /** 移出后需要将该菜单ID在processId下移除-ES操作 */
    @ApiModelProperty(value = "被移出的菜单ID", required = true)
    @NotNull(message = "targetMenuId不能为空")
    private String targetMenuId;

    @ApiModelProperty(value = "流程列表")
    @Size(max = 100, message = "最多100个流程")
    private List<String> processIds;

    @ApiModelProperty(value = "所在分类")
    private List<String> menuIdList;
}



package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/10
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ExcelInitDataDTO extends ToString {

    @ApiModelProperty(value = "用户初始化数据", required = false)
    private List<String> values;

    @ApiModelProperty(value = "用户初始化数据位于excel列下标，找对应开发拿", required = false)
    private Integer index;
}

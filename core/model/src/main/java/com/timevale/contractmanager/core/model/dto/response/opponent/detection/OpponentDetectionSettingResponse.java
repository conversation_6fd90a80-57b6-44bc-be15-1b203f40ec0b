package com.timevale.contractmanager.core.model.dto.response.opponent.detection;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Author:ji<PERSON><PERSON>
 * @since 2021-08-11 19:23
 */
@Data
@Builder
public class OpponentDetectionSettingResponse extends ToString {
	@ApiModelProperty("经营范围")
	private String scope;

	@ApiModelProperty("是否开启实时检测,1:开启,2:不开启")
	private Integer realTimeDetection;

	@ApiModelProperty("推送对象")
	private String pushObject;

	@ApiModelProperty("是否开启立即推送,1:开启,2:不开启")
	private Integer immediatelyPush;

	@ApiModelProperty("是否开启推送,1:开启,2:不开启")
	private Integer push;

	@ApiModelProperty("是否开启经营范围检测,1:开启,2:不开启")
	private Integer businessScopeDetection;

	@ApiModelProperty("是否系统自动保存相对方,1:是,2:否")
	private Integer autoSaveOpponent;
}

package com.timevale.contractmanager.core.model.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取合同流程发起初始化配置请求参数
 *
 * <AUTHOR>
 * @date 2024/05/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessStartInitConfigRequest {

    @ApiModelProperty("流程模板id")
    private String flowTemplateId;

    private String clientId;

    private String tenantId;

    private String resourceTenantId;

    private String operatorId;
    
}

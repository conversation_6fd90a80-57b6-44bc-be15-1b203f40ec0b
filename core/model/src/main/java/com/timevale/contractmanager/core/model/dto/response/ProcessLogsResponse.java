package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.ProcessLog;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 获取流程操作日志响应数据
 *
 * @author: qianyi
 * @since: 2020-11-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessLogsResponse extends ToString {
    @ApiModelProperty("操作日志列表")
    private List<ProcessLog> logs;
}

package com.timevale.contractmanager.core.model.dto.request.transfer;

import com.timevale.contractmanager.core.model.dto.transfer.TransferProcessInfoDTO;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/28
 */
@Setter
@Getter
public class TransferUserListRequest extends ToString {
    @ApiModelProperty("转交人，全部转交需要此参数")
    private List<String> transferUserList;

    @ApiModelProperty("转交流程信息，分批转交需要此参数")
    private TransferProcessInfoDTO transferProcessInfo;

    @ApiModelProperty("转交场景值")
    /** {@link com.timevale.contractmanager.common.service.enums.TransferSceneEnum} */
    @Min(value = 1, message = "转交场景值不存在")
    @Max(value = 4, message = "转交场景值不存在")
    @NotNull(message = "转交场景值不能为空")
    private Integer transferScene;

    @ApiModelProperty(value = "当前主体id", hidden = true)
    private String tenantId;

    @ApiModelProperty(value = "数据下标", hidden = true)
    @Max(value = 2000, message = "暂不支持2000以上数据查询")
    private Integer offset;

    @ApiModelProperty(value = "每次查询数量", hidden = true)
    @Max(value = 20, message = "每次最多查询20条")
    private Integer size;

    @ApiModelProperty(value = "搜索关键字", hidden = true)
    private String keyword;

}

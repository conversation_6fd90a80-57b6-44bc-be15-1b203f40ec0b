package com.timevale.contractmanager.core.model.dto.response.grouping.file;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:jiany<PERSON>
 * @since 2022-06-13 16:36
 */
@Data
public class OneClickGroupingFileNumberResponse extends ToString {
	@ApiModelProperty("一键归档的数量")
	private Long groupingNum;
	@ApiModelProperty("是否跳转任务中心")
	private boolean goToTaskCenter;
}

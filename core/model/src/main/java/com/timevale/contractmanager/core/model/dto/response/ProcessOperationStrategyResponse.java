package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/9/8
 */
@Setter
@Getter
public class ProcessOperationStrategyResponse extends ToString {
    @ApiModelProperty("是否是法人或者管理员")
    private Boolean legalOrAdmin;

    @ApiModelProperty("是否关注过企业服务号")
    private Boolean followedWechatOfficial;

    @ApiModelProperty("是否添加企业微信内部员工")
    private Boolean addedWecomInnerUser;
}

package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.enums.SubjectExperienceStatusEnum;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Setter
@Getter
public class ProcessSubjectExperienceResponse extends ToString {

    /** {@link SubjectExperienceStatusEnum} */
    @ApiModelProperty("体验签署状态 1:已体验 2：未体验 3:未知")
    private Integer status  ;

    @ApiModelProperty("实名成功后企业主体id：可空")
    private String subjectId;
}

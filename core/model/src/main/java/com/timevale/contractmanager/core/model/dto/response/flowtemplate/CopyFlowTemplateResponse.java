package com.timevale.contractmanager.core.model.dto.response.flowtemplate;

import com.timevale.mandarin.common.result.ToString;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 复制流程模板结果
 *
 * <AUTHOR>
 * @since 2020/11/6
 */
@Getter
@Setter
public class CopyFlowTemplateResponse extends ToString {

    @ApiModelProperty(value = "共享码")
    private String shareCode;

    @ApiModelProperty(value = "共享码失效，单位天数")
    private int shareDays;

    @ApiModelProperty(value = "复制目标对象状态列表, shareCode为空时, 此字段可用")
    private List<CopyTargetAccountStatus> copyTargetStatus;

    @Getter
    @Setter
    public static final class CopyTargetAccountStatus {
        @ApiModelProperty(value = "复制目标对象名称")
        private String targetAccount;

        @ApiModelProperty(value = "复制目标对象是否实名")
        private boolean realname;
    }
}

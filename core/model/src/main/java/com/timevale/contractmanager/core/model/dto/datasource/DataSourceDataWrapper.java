package com.timevale.contractmanager.core.model.dto.datasource;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Sets;
import com.timevale.besp.lowcode.integration.response.FormDataResponse;
import com.timevale.contractmanager.common.service.enums.DataSourceBizConvertFiledTypeEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStartChannelEnum;
import com.timevale.contractmanager.core.model.dto.user.UserAccountDetail;
import com.timevale.contractmanager.core.model.enums.DataSourceLowcodeDataTypeEnum;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/19 16:26
 */
public class DataSourceDataWrapper {

//    TEXT(1, "文本"),   String
//    NUM(2, "数字"),  Double
//    DATE(3, "日期"),  Long
//    MULTILINE_TEXT(8, "多行文本"), String
//    CHECK_BOX(9, "复选"), List<String>
//    RADIO_BOX(10, "单选"), String
//    IMAGE(11, "图片"), 示例：[{"fileKey":"","fileName":""},{"fileKey":"","fileName":""}]
//    PHONE_NUM(19, "手机号"), String

    private static final String CHANNEL_DATA_ID = "";

    //采集任务创建人
    private static final String DATA_OWNER = DataSourceBizConvertFiledTypeEnum.TASK_CREATOR.getFieldId();
    // 采集任务创建人企业
    private static final String TASK_CREATOR_SUBJECT = DataSourceBizConvertFiledTypeEnum.TASK_CREATOR_SUBJECT.getFieldId();

    // 填写人的oid
    private static final String DATA_CREATOR = DataSourceBizConvertFiledTypeEnum.DATA_CREATOR.getFieldId();

    // 这些字段对应的值为oid 或者 三方Id
    private static final List<String> ACCOUNT_FIELDS = Arrays.asList(DATA_OWNER, TASK_CREATOR_SUBJECT, DATA_CREATOR);

    /**
     * @see com.timevale.contractmanager.core.model.enums.DataSourceLowcodeDataTypeEnum
     * 1.模版数据发起  2.基于数据源文件   3.关联数据源发起，数据源无任何值只有发起记录
     */
    private static final String DATA_TYPE_KEY = "formType";

    /**
     * 合同文件key
     */
    private static final String CONTRACT_FILE_KEY = "contractFile";

    /**
     * 附件key
     */
    private static final String ATTACHMENT_FILE_KEY = "attachmentFile";

    private static final String FORM_ID_KEY = "formKey";
    private static final String SUBJECT_OID_KEY = "oid";
    private static final String DATA_SOURCE_CHANNEL = "platform";

    /**
     * 评论区文件key
     */
    public static final String LOWCODE_COMMENT_FILES_KEY = "lowcodeCommentFiles";

    /**
     * 表单定义fileKey的key
     */
    private static final String FILE_KEY = "file_key";
    /**
     * 表单定义文件id的key
     */
    private static final String FILE_ID = "file_id";

    /**
     * 表单定义fileName的key
     */
    private static final String FILE_NAME = "file_name";


    /**
     * 数据采集开始时间
     */
    private static final String START_TIME = "startTime";

    /**
     * 数据采集技术实践
     */
    private static final String END_TIME = "endTime";

    /**
     * 支持json数据的渠道
     */
    private static final String SUPPORT_JSON_DATA_CHANNELS = "support.json.data.channels";


    private Map<String, String> dataMap = new HashMap<>();

    private Map<String, UserAccountDetail> accountFieldAccountMap = new HashMap<>();

    public DataSourceDataWrapper(Map<String, Object> outerDataMap) {
        if (null == outerDataMap) {
            throw new RuntimeException("数据map不能为空");
        }
        Map<String, String> dataMap = new HashMap<>();
        outerDataMap.forEach((fieldId, value) -> {
            if (value == null) {
                return;
            }
            dataMap.put(fieldId, value instanceof List ? JSON.toJSONString(value) : value.toString());
        });
        this.dataMap = dataMap;
    }

    public DataSourceDataWrapper(FormDataResponse response) {
        this(response.getData());
        dataMap.putIfAbsent(FORM_ID_KEY, response.getFormKey());
        dataMap.putIfAbsent(DATA_SOURCE_CHANNEL, response.getPlatform());
    }


    /**
     * 数据填写人
     * oid 或者3方平台Id
     */
    public String getDataCreatorId() {
        return dataMap.get(DATA_CREATOR);
    }

    /**
     * 数据拥有人
     * oid 或者3方平台Id
     */
    public String getDataOwnerId() {
        String ownerId = dataMap.get(DATA_OWNER);
        return StringUtils.isNotBlank(ownerId) ? ownerId : dataMap.get(DATA_CREATOR);
    }

    /**
     * formId
     */
    public String getFormId() {
        return dataMap.get(FORM_ID_KEY);
    }

    /**
     * subjectOid
     */
    public String getSubjectOid() {
        return dataMap.get(SUBJECT_OID_KEY);
    }

    /**
     * 来源平台
     */
    public String getDataSourceChannel() {
        return dataMap.get(DATA_SOURCE_CHANNEL);
    }

    public String get(String fieldId) {
        if (ACCOUNT_FIELDS.contains(fieldId)) {
            return Optional.ofNullable(accountFieldAccountMap.get(fieldId)).map(UserAccountDetail::getAccountName).orElse("");
        }
        return dataMap.get(fieldId);
    }

    public Optional<DataSourceLowcodeDataTypeEnum> getType() {
        String type =  dataMap.get(DATA_TYPE_KEY);
        if (StringUtils.isBlank(type)) {
            return null;
        }
        return Optional.ofNullable(DataSourceLowcodeDataTypeEnum.from(type));
    }

    public LinkedHashSet<String> getContractFiles() {
        return getFile(CONTRACT_FILE_KEY);
    }

    private LinkedHashSet<String> getFileIdsFromMap(String fileType) {

        List<FileDataSourceBO> fileDataByType = getFileDataByType(fileType);
        return  fileDataByType.stream().map(FileDataSourceBO::getFileId).collect(Collectors.toCollection(LinkedHashSet::new));
    }

    /**
     * 获取评论区文件
     *
     * @return 数据源文件对象列表
     */
    public List<FileDataSourceBO> getCommentFiles() {
        return getFileDataByType(LOWCODE_COMMENT_FILES_KEY);
    }

    /**
     * 获取文件数据
     *
     * @param type 文件类型
     * @return 数据源文件对象列表
     */
    private List<FileDataSourceBO> getFileDataByType(String type) {
        String filesMaps = dataMap.get(type);
        List<FileDataSourceBO> commentFiles = new ArrayList<>();
        if (StringUtils.isBlank(filesMaps)) {
            return commentFiles;
        }
        LinkedHashSet<Map<String, String>> commentFilesMap = JSONObject.parseObject(filesMaps, new TypeReference<LinkedHashSet<Map<String, String>>>() {
        });
        if (commentFilesMap.isEmpty()) {
            return commentFiles;
        }
        // 遍历map 根据key获取信息
        for (Map<String, String> map : commentFilesMap) {
            if (MapUtils.isEmpty(map)) {
                continue;
            }
            FileDataSourceBO fileDetailBO = new FileDataSourceBO();
            fileDetailBO.setFileId(map.get(FILE_ID));
            fileDetailBO.setFileName(map.get(FILE_NAME));
            fileDetailBO.setFileKey(map.get(FILE_KEY));
            commentFiles.add(fileDetailBO);
        }
        return commentFiles;
    }


    public LinkedHashSet<String> getAttachmentFiles() {
        return getFile(ATTACHMENT_FILE_KEY);
    }

    public String getStartTime() {
        return dataMap.get(START_TIME);
    }

    public String getEndTime() {
        return dataMap.get(END_TIME);
    }

    public LinkedHashSet<String> getFile(String key) {
        String[] jsonDataChannels = ConfigService.getAppConfig().getArrayProperty(
                SUPPORT_JSON_DATA_CHANNELS, // 添加有意义的配置key
                ",",
                new String[]{ProcessStartChannelEnum.CONTRACT_DRAFT.getCode()}
        );
        if(Sets.newHashSet(jsonDataChannels).contains(getDataSourceChannel())){
            return getFileIdsFromMap(key);
        }
        String fileIds = dataMap.get(key);
        return StringUtils.isBlank(fileIds) ? new LinkedHashSet<>() :
                        JSONObject.parseObject(fileIds, new TypeReference<LinkedHashSet<String>>() {});
    }


    public String channelDataId() {
        return dataMap.getOrDefault(CHANNEL_DATA_ID, "");
    }

    // 账户这里优化
    public void populateAccount(Function<Collection<String>, Map<String, UserAccountDetail>> accountFunction) {
        Map<String, String> accountFieldDataMap = new HashMap<>();
        for (String accountField : ACCOUNT_FIELDS) {
            String accountId = null; // e签宝为oid
            if (StringUtils.isBlank(accountId = dataMap.get(accountField))) {
                continue;
            }
            accountFieldDataMap.put(accountField, accountId);
        }

        Map<String, UserAccountDetail> oidAccountMap = accountFunction.apply(new HashSet<>(accountFieldDataMap.values()));
        Map<String, UserAccountDetail> accountFieldAccountMap = new HashMap<>();
        accountFieldDataMap.forEach((accountField, oid) -> {
            accountFieldAccountMap.put(accountField, oidAccountMap.get(oid));
        });
        this.accountFieldAccountMap = accountFieldAccountMap;
    }
}

package com.timevale.contractmanager.core.model.dto.request.grouping;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请求头中获取的参数
 * @author: jinhuan
 * @since: 2020-04-10 16:44
 **/
@Data
public class RequestContextRequest extends ToString {

    /**
     * 空间id
     */
    @ApiModelProperty(hidden = true)
    private String tenantId;

    /**
     * 当前操作人id
     */
    @ApiModelProperty(hidden = true)
    private String operatorId;

    /**
     * appId
     */
    @ApiModelProperty(hidden = true)
    private String appId;

    /**
     * clientId
     */
    @ApiModelProperty(hidden = true)
    private String clientId;
}

package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 更新任务信息参数
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Data
public class ShareSignTaskInfoRequest extends ToString {

    @ApiModelProperty("扫码签任务id")
    @NotBlank(message = "扫码签任务id不能为空")
    private String shareSignTaskId;

    @ApiModelProperty("是否私密分享，true: 是，false: 否")
    private Boolean privateShare;
}

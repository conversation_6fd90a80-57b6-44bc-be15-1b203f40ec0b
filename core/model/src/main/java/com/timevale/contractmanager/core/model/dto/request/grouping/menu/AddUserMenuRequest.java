package com.timevale.contractmanager.core.model.dto.request.grouping.menu;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 添加用户的菜单操作权限
 *
 * @author: xuanzhu
 * @since: 2019-09-08 15:50
 */
@Data
public class AddUserMenuRequest extends CommonRequest {

    @ApiModelProperty(value = "被添加人的账户信息列表", required = true)
    @NotNull(message = "accountList不能为空")
    @Size(min = 1, message = "accountList不能为空")
    List<AccountInfo> accountList;

    @Data
    public static class AccountInfo {

        @ApiModelProperty(value = "被添加的用户oid或者部门deptId", required = true)
        @NotBlank(message = "oid不能为空")
        private String oid;

        @ApiModelProperty(value = "被添加的用户名称", required = true)
        @NotBlank(message = "name不能为空")
        private String name;

        @ApiModelProperty(value = "被添加的用户手机号码")
        private String mobileNo;

        @ApiModelProperty(value = "被添加的用户邮箱地址")
        private String email;

        @ApiModelProperty(value = "角色id", required = true)
        @NotBlank(message = "roleId不能为空")
        private String roleId;

        @ApiModelProperty(value = "被授权人类型 1-个人;2-部门。为1时oid字段给的是oid，为2时为deptId", required = false)
        private Integer authorizeType;

        @ApiModelProperty(value = "操作类型 0-添加；1-删除")
        private Integer operateType;
    }
}

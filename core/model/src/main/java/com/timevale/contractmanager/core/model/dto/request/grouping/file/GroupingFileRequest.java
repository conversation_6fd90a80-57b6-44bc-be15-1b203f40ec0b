package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import com.timevale.contractmanager.core.model.dto.request.grouping.RequestContextRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 流程归档入参
 * @author: jinhuan
 * @since: 2020-04-30 11:28
 **/
@Data
public class GroupingFileRequest extends RequestContextRequest {

    @ApiModelProperty(value = "流程列表", required = true)
    @NotNull(message = "processIdList不能为空")
    @Size(min = 1, message = "流程不能为空")
    @Size(max = 100, message = "最大支持100个流程")
    private List<String> processIdList;

    @ApiModelProperty(value = "合同归属的菜单id集合", required = true)
    @NotNull(message = "menuIdList不能为空")
    @Size(min = 1, message = "分类不能为空")
    @Size(max = 1, message = "最大支持1个分类")
    private List<String> menuIdList;

}

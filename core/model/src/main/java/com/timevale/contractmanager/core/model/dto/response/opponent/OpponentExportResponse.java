package com.timevale.contractmanager.core.model.dto.response.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/***
 * 相对方导出结果
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpponentExportResponse extends ToString {

    @ApiModelProperty("导出任务id， 后续可用于获取导出结果")
    private String exportTaskId;

    @ApiModelProperty("待导出记录条数")
    private Long totalSize;
}

package com.timevale.contractmanager.core.model.dto.request.transfer;

import com.timevale.contractmanager.common.service.annotation.EnumValidList;
import com.timevale.contractmanager.common.service.enums.ProcessTransferTypeEnum;
import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 手动转交入参
 *
 * @author: qianyi
 * @since: 2021-07-07
 */
@Data
@ApiModel("手动转交入参")
public class ManualTransferRequest extends CommonRequest {

    @ApiModelProperty(value = "转交主体oid，未指定时默认取当前空间id")
    private String subjectId;

    @ApiModelProperty(value = "转交人oid", required = true)
    @NotBlank(message = "转交人不能为空")
    private String originalAccountId;

    @ApiModelProperty(value = "被转交人姓名", required = true)
    @NotBlank(message = "被转交人姓名不能为空")
    private String transferToAccountName;

    @ApiModelProperty(value = "被转交人账号")
    private String transferToAccount;

    @ApiModelProperty(value = "被转交人账号id")
    private String transferToAccountId;

    @ApiModelProperty(value = "合同流程id列表", required = true)
    @NotEmpty(message = "合同流程id列表不能为空")
    @Size(max = 100, message = "最大支持100个流程")
    private List<String> processIds;

    /** 目前接口文档暂时隐藏当前参数 */
    @ApiModelProperty(
            value =
                    "转交类型，INITIATOR_TRANSFER-发起人转交，EXECUTING_TASK_TRANSFER-待办任务转交，COMPLETE_TASK_TRANSFER-已办任务转交，CC_TRANSFER-抄送人转交",
            hidden = true)
    @EnumValidList(message = "存在无法识别的转交类型！", target = ProcessTransferTypeEnum.class)
    private List<String> transferTypes;
}

package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021-05-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShareSignTaskManageUrlResponse extends ToString {

    @ApiModelProperty("扫码签任务管理地址")
    private String url;
}

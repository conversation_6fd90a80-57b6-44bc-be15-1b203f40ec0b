package com.timevale.contractmanager.core.model.dto.response.contractNo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-10-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractNoIntervalResultVO {

    @ApiModelProperty(value = "可选间隔符")
    private List<String> intervalList;
    
}

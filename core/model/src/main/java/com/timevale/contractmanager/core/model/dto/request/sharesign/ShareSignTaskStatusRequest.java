package com.timevale.contractmanager.core.model.dto.request.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2021-02-05
 */
@Data
public class ShareSignTaskStatusRequest extends ToString {

    @ApiModelProperty("扫码签任务id")
    @NotBlank(message = "扫码签任务id不能为空")
    private String shareSignTaskId;

    @ApiModelProperty("当前登录账号oid")
    private String accountId;

    @ApiModelProperty("任务状态，0-关闭， 1-开启")
    @Min(value = 0, message = "任务状态无效, 仅支持0，1")
    @Max(value = 1, message = "任务状态无效, 仅支持0，1")
    private Integer taskStatus;
}

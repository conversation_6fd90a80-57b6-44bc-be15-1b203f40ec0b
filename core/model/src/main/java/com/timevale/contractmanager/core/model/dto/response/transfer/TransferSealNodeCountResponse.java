package com.timevale.contractmanager.core.model.dto.response.transfer;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/11/30
 */
@Setter
@Getter
public class TransferSealNodeCountResponse extends ToString {

    @ApiModelProperty("中间节点总数")
    private Integer personalNodeCount = 0;

    @ApiModelProperty("审批末节点总数")
    private Integer finalNodeCount = 0;
}

package com.timevale.contractmanager.core.model.dto.response.grouping.process.v2;

import com.timevale.contractmanager.common.service.bean.ProcessAccount;
import com.timevale.contractmanager.common.service.bean.SupportOperate;
import com.timevale.contractmanager.common.service.bean.process.ContractFileBean;
import com.timevale.contractmanager.common.service.enums.ProcessFromEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.contractmanager.core.model.dto.response.grouping.process.AiParamOut;
import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 归档流程信息，修改了ProcessInfo的发起人，list->object
 * @author: TaiWu
 * @since: 2020-05-09 13:13
 */
@Data
public class GroupingProcessInfo {
    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("应用名")
    private String appName;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("发起时间")
    private Long processCreateTime;

    @ApiModelProperty("更新时间")
    private Long processUpdateTime;

    @ApiModelProperty("来源")
    private Integer source;

    @ApiModelProperty("来源type")
    private String sourceType;

    @ApiModelProperty("流程描述")
    private String processDesc;

    @ApiModelProperty("流程状态")
    private Integer processStatus;

    @ApiModelProperty("流程状态描述")
    private String processStatusDesc;

    @ApiModelProperty("发起人")
    ProcessAccount initiatorAccount;

    @ApiModelProperty("所有参与人")
    List<ProcessAccount> participantAccountList;

    @ApiModelProperty("抄送人")
    List<ProcessAccount> ccAccountList;

    @ApiModelProperty("需要处理的人")
    List<ProcessAccount> currentOperatorList;

    @ApiModelProperty(value = "当前登录人是否能对此流程操作-签署or填写等", example = "true")
    private Boolean operable;

    @ApiModelProperty("flowId")
    private String flowId;

    @ApiModelProperty(value = "当前子流程类型")
    private Integer processType;

    @ApiModelProperty(value = "签署模式")
    private String signMode;

    @ApiModelProperty("专属云项目Id")
    private String dedicatedCloudId;

    @ApiModelProperty(value = "合同有效期")
    private Long contractValidity;

    @ApiModelProperty(value = "签署文件份数")
    private Integer contractFileCount;

    @ApiModelProperty(value = "附属文件份数")
    private Integer attachmentFileCount;

    /**
     * 废弃，用archiveNo
     */
    @Deprecated
    @ApiModelProperty(value = "归档编号")
    private String contractNo;

    @ApiModelProperty(value = "归档编号")
    private String archiveNo;

    @ApiModelProperty(value = "归档人名称")
    private String archivedPersonName;

    @ApiModelProperty(value = "归档时间")
    private long archivedTime;

    @ApiModelProperty(value = "合同文件名列表")
    private List<String> contractFileNames;

    @ApiModelProperty(value = "附属文件名列表")
    private List<String> attachmentFileNames;

    @ApiModelProperty(value = "AI动态出参")
    private List<AiParamOut> dynamicOutParamList;

    @ApiModelProperty("合同完成时间")
    private Long completeTime;

    @ApiModelProperty("分类列表")
    private List<String> menuIdList;

    @ApiModelProperty("续签状态, 0-无需续签，1-可续签，2-续签中，3-部分续签，4-已续签")
    private Integer renewalStatus;

    @ApiModelProperty(value = "流程业务类型，0-普通流程，1-解约流程，2-重新发起流程，3-续签流程")
    private Integer processBizType;

    @ApiModelProperty("续签配置-是否续签")
    private Boolean renewable;

    @ApiModelProperty("签署截止时间")
    private Long signValidity;

    private ProcessTemplateInfo template;

    private List<ProcessSealInfo> sealInfoList;

    private ProcessAccount affiliatedEnterprise;

    @ApiModelProperty(value = "是否顺序填")
    private boolean orderWrite;

    @ApiModelProperty(value = "是否顺序签")
    private boolean orderSign;

    /**
     * 是否保密
     *
     * @see com.timevale.signflow.search.docSearchService.enums.ProcessSecretEnum
     */
    @ApiModelProperty("合同保密")
    private Integer processSecret;

    private String rescindRemark;

    @ApiModelProperty("合同编号")
    private List<ContractFileBean> contractFiles;

    @ApiModelProperty("是否可解约")
    private Boolean canRescind;

    @ApiModelProperty("不能解约原因")
    private String cannotRescindReason;

    @ApiModelProperty("最后一条备注")
    private String lastProcessRemark;

    @ApiModelProperty(value = "流程来源，OFFLINE-线下导入流程，ONLINE-线上流程，为空默认线上流程")
    private String processFrom;

    @ApiModelProperty(value = "发起来源")
    private String initiateFrom;

    @ApiModelProperty("合同类型列表")
    private List<String> contractCategoryList;

    @ApiModelProperty("是否展示查看访问口令")
    private Boolean showAuthToken;

    /**
     * 支持的操作
     */
    private List<SupportOperate> operations;


    public String getProcessFrom() {
        if (StringUtils.isBlank(processFrom)) {
            processFrom = ProcessFromEnum.ONLINE.getType();
        }
        return processFrom;
    }

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
        ProcessStatusEnum statusEnum = ProcessStatusEnum.parse(processStatus);
        if (null != statusEnum) {
            this.processStatusDesc = statusEnum.getStatusDesc();
        }
    }
}

package com.timevale.contractmanager.core.model.dto.request.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author:jiany<PERSON>
 * @since 2021-03-08 10:48
 */
@Data
public class OpponentRiskLevelRequest extends ToString {

	@ApiModelProperty("uuis")
	@NotBlank(message = "uuid不能为空")
	private String uuid;
}

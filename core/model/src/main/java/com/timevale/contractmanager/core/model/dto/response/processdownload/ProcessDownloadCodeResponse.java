package com.timevale.contractmanager.core.model.dto.response.processdownload;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 下载返回结果
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
public class ProcessDownloadCodeResponse extends ToString {

    @ApiModelProperty("下载码")
    private String downloadCode;

    @ApiModelProperty("是否前往任务中心")
    private boolean goToJobCenter;
}

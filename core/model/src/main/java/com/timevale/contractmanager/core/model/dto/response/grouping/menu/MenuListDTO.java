package com.timevale.contractmanager.core.model.dto.response.grouping.menu;

import com.timevale.contractmanager.common.service.result.grouping.PermissionInfo;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 菜单列表
 *
 * @author: xuanzhu
 * @since: 2019-09-09 17:05
 */
@Data
public class MenuListDTO extends ToString {

    @ApiModelProperty("菜单id")
    private String menuId;

    @ApiModelProperty("菜单名称")
    private String menuName;

    @ApiModelProperty("排序从0开始，升序，越小的排越前面")
    private Integer order;

    @ApiModelProperty("是否只读，true-是（无权归档），false-否（可归档）")
    private boolean isReadOnly = true;

    @ApiModelProperty("操作列表 CREATE/DELETE等")
    @HasTranslateField
    private List<PermissionInfo> permissionCodes;

    @ApiModelProperty("菜单下的合同数")
    private Long processCount = 0L;

    @ApiModelProperty("是否展示子分类合同设置")
    private Integer showChild;

    @ApiModelProperty("子菜单信息")
    @HasTranslateField
    private List<MenuListDTO> childNode;

    @ApiModelProperty("是否统一子分类台账")
    private Integer unityForm;

    @ApiModelProperty("绑定的台账id")
    private String bindingFormId;

    @ApiModelProperty("菜单更新时间")
    private Long updateTime;
}

package com.timevale.contractmanager.core.model.dto.request;

import com.timevale.contractmanager.core.model.dto.request.process.ProcessListRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@ApiModel
public class ProcessExportRequest extends ProcessListRequest {

    @ApiModelProperty(value = "个人id")
    String accountId;
    @ApiModelProperty(value = "主体id")
    String subjectId;
    @ApiModelProperty(value = "processIds")
    List<String> processIds = Lists.emptyList();
    //QueryChannelEnum
    @ApiModelProperty(value = "查询模式")
    @NotNull
    Integer queryChannel;

    @ApiModelProperty(value = "分类id")
    private String menuId;

    @ApiModelProperty(value = "字段查询入参-json格式")
    private String matching;

    /**
     * 表单/台账id
     */
    @ApiModelProperty(value = "表单id")
    private String formId;

    /**
     * 是否包含数据源表单--非台账
     */
    @ApiModelProperty(value = "是否包含表单")
    private boolean withForm;

    @ApiModelProperty(value = "是否导出文件")
    private boolean exportFile;

    @ApiModelProperty(value = "操作人id")
    private String  operatorId;

    @ApiModelProperty(value = "appId")
    private String appId;
}

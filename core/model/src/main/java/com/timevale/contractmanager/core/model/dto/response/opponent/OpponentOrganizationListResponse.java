package com.timevale.contractmanager.core.model.dto.response.opponent;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.List;

/**
 * @author: huifeng
 * @since: 2021-01-22 11:48
 */
@Data
@Builder
public class OpponentOrganizationListResponse extends ToString {

    @ApiModelProperty("相对方企业总数")
    private Long totalSize;

    @ApiModelProperty("相对方企业列表")
    private List<OpponentOrganizationResponse> organizations;

    @ApiModelProperty("相对方企业已用额度")
    private int opponentEntityNums;

    @JsonIgnore
    private String scrollId;
}

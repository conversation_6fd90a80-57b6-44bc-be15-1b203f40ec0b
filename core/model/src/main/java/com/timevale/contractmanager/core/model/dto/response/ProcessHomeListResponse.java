package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.common.service.bean.process.ProcessOperatorBean;
import com.timevale.flow.facade.service.model.output.ApprovalFlowListOutput;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/29
 */
@Data
@ApiModel("首页合同列表")
public class ProcessHomeListResponse extends ToString {

    private List<ProcessHomeInfo> data = new ArrayList<>();

    private Long total = 0L;

    @Data
    public static class ProcessHomeInfo {
        @ApiModelProperty("标题")
        private String title;

        @ApiModelProperty("流程编号")
        private String processId;

        @ApiModelProperty("发起人")
        private ProcessAccount processAccount;

        @ApiModelProperty("发起人- 分享的时候用到")
        private ProcessAccount initiatorAccount;

        @ApiModelProperty("流程发起时间")
        private Long processCreateTime;

        @ApiModelProperty("所有参与人")
        private List<ProcessAccount> participantAccountList;

        @ApiModelProperty("全部待审批人")
        private List<ApprovalFlowListOutput.Account> approvalAccountList;

        @ApiModelProperty("审批批次id")
        private String batchId;

        @ApiModelProperty("签署流程id")
        private String flowId;

        @ApiModelProperty("数据类型 1老 2新")
        private Integer dataType = 2;

        @ApiModelProperty("是否转交合同")
        private boolean transfer = false;

        @ApiModelProperty("审批id")
        private String approvalId;

        @ApiModelProperty("审批状态")
        private Integer approvalStatus;

        /** {@link com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum 审批类型} */
        @ApiModelProperty("审批类型")
        private String approvalType;

        @ApiModelProperty("履约记录id")
        private String recordId;

        @ApiModelProperty("是否续约")
        private Boolean renewable;

        @ApiModelProperty("流程状态")
        private Integer processStatus;

        @ApiModelProperty("流程状态描述")
        private String processStatusDesc;

        @ApiModelProperty("需要处理的人")
        List<ProcessOperatorBean> currentOperatorList;

        @ApiModelProperty("更新时间")
        private Long processUpdateTime;

        @ApiModelProperty("类型名称")
        private String typeName;
    }
}

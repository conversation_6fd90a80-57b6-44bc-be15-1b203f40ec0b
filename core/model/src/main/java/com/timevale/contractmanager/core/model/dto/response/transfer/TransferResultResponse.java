package com.timevale.contractmanager.core.model.dto.response.transfer;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 转交结果
 * <AUTHOR>
 * @since 2025-04-02
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransferResultResponse extends ToString {

    @ApiModelProperty("是否跳转任务中心")
    private boolean goToJobCenter;
}

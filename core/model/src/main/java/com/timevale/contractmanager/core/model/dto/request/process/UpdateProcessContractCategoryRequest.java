package com.timevale.contractmanager.core.model.dto.request.process;

import com.timevale.contractmanager.common.service.bean.FileContractCategoryBean;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 修改合同类型请求参数
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
public class UpdateProcessContractCategoryRequest extends ToString {

    /** 合同流程id */
    @NotBlank(message = "合同流程id不能为空")
    private String processId;
    /** 菜单id */
    private String menuId;
    /** 文件合同类型列表 */
    private List<FileContractCategoryBean> fileContractCategories;
}

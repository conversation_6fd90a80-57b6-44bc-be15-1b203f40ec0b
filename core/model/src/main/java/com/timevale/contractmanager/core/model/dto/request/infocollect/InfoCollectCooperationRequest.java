package com.timevale.contractmanager.core.model.dto.request.infocollect;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * InfoCollectCooperationRequest
 *
 * <AUTHOR>
 * @since 2022/9/21 2:45 下午
 */
@Data
public class InfoCollectCooperationRequest extends ToString {

    private String cooperationId;

    @NotBlank(message = "签署方名称不能为空")
    private String cooperationName;

    @NotBlank(message = "签署方类型不能为空")
    private String cooperationType;
}

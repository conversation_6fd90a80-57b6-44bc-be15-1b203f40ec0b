package com.timevale.contractmanager.core.model.dto.response;

import com.timevale.contractmanager.core.model.bo.bizlog.ProcessOperatePhaseLog;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * 获取流程操作日志响应数据
 *
 * @author: qianyi
 * @since: 2024-02-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessBizLogsResponse extends ToString {

    @ApiModelProperty("操作阶段日志列表")
    private List<ProcessOperatePhaseLog> phaseLogs = Lists.newArrayList();
}

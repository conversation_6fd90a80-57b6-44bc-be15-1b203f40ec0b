package com.timevale.contractmanager.core.model.dto.response.infocollect;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/30 14:23
 */
@Data
public class InfoCollectStartResponse {

    /**
     * true 取 dataList 结果，false 取 errorMsg 报错信息
     */
    private boolean success;

    private String errorMsg;

    @ApiModelProperty("发起页需要")
    private String requireId;

    @ApiModelProperty("发起类型 0-1v多，1-多v多")
    private Integer startType;

    /**
     *
     */
    private Integer participantSubjectType;

    private String participantLabel;

    private String participantId;


}

package com.timevale.contractmanager.core.model.dto.request.grouping.standingbook;

import com.timevale.contractmanager.core.model.dto.request.grouping.CommonRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 设置自定义列表字段请求
 *
 * @author: xuanzhu
 * @since: 2019-10-27 16:07
 */
@Data
public class SetCustomeFieldRequest extends CommonRequest {

    @ApiModelProperty(value = "菜单id")
    private String menuId;

    @ApiModelProperty(value = "需要显示的字段id列表 - fieldId列表", required = true)
    @NotNull(message = "displayList不能为空")
    @Size(min = 1, message = "displayList不能为空")
    private List<String> displayList;

    @ApiModelProperty(value = "需要冻结的字段id列表 - fieldId列表")
    @Size(max = 3, message = "冻结的字段不能超过三个")
    private List<String> freezeList;

    @ApiModelProperty(value = "需要隐藏的字段id列表 - fieldId列表")
    private List<String> hiddenList;

    @ApiModelProperty(value = "台账id")
    private String formId;
}

package com.timevale.contractmanager.core.model.enums;

/**
 * <AUTHOR>
 * @since 2021-02-02 11:26
 **/
public enum PreviewTypeEnum {

    /**
     * 普通预览
     */
    NORMAL_PREVIEW(0),

    /**
     * 重新发起预览
     */
    RESTART_PREVIEW(1),

    /**
     * 续签/解约预览
     */
    RELATE_PREVIEW(2),
    ;

    PreviewTypeEnum(int type) {
        this.type = type;
    }

    private Integer type;

    public Integer getType() {
        return type;
    }

}

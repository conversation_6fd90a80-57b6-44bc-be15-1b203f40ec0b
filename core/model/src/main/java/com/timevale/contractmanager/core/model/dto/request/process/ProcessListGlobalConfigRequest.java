package com.timevale.contractmanager.core.model.dto.request.process;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/4/7 11:31
 */
@Data
public class ProcessListGlobalConfigRequest extends ToString {

    private String clientId;

    private String accountId;

    private String tenantId;

    @ApiModelProperty("钉钉 corpId")
    private String corpId;

    @ApiModelProperty("钉钉 isvAppId")
    private String isvAppId;
}

package com.timevale.contractmanager.core.model.dto.request.grouping.process;

import com.timevale.contractmanager.core.model.bo.FileBO;
import com.timevale.contractmanager.common.service.enums.ParticipantSubjectType;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/18
 */
@Setter
@Getter
public class ExcelDownloadRequest extends ToString {

  @ApiModelProperty(value = "参与方id")
  private String participantId;

  @ApiModelProperty(value = "参与方名称")
  private String participantLabel;

  /** {@link ParticipantSubjectType} */
  @ApiModelProperty(value = "参与方主体类型：0:个人，1：企业，2：待定")
  private Integer participantSubjectType = 2;

  /** 流程模板id：为空的话使用本地发起模板 */
  @ApiModelProperty(value = "流程模板id")
  private String flowTemplateId;

  /** 流程模板类型 */
  @ApiModelProperty(value = "流程模板类型,1:通用模板,2:第三方用户模板(钉钉，微信，飞书)下载的文档")
  private Integer excelTemplateType = 1;

  @ApiModelProperty(value = "excel初始化数据，目前仅支持初始化一列的数据")
  private List<ExcelInitDataDTO> excelInitDataList;

  @ApiModelProperty(value = "额外表头列，用于指定模板以外列的表头", hidden = true)
  private List<List<String>> extraHeaderList;

  @ApiModelProperty(value = "额外列的数据", hidden = true)
  private List<ExcelInitDataDTO> extraDataList;

  @ApiModelProperty(value = "文件列表")
  private List<FileBO> files;

  @ApiModelProperty(value = "是否锁定预填数据", hidden = true)
  private Boolean lockPreFill = false;

  @ApiModelProperty(value = "企业抄送方个数")
  @Max(value = 5, message = "最多设置5个抄送方")
  private Integer subjectCcNum;

  @ApiModelProperty(value = "个人抄送方个数")
  @Max(value = 5, message = "最多设置5个抄送方")
  private Integer personCcNum;

  @ApiModelProperty(value = "签署模式")
  private String signMode;
}

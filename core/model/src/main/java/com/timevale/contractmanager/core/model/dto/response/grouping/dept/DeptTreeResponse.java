package com.timevale.contractmanager.core.model.dto.response.grouping.dept;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2021-04-03 22:34
 */
@Data
public class DeptTreeResponse extends ToString {
	@ApiModelProperty(value = "部门id")
	private String deptId;

	@ApiModelProperty(value = "部门名称")
	private String deptName;

	@ApiModelProperty(value = "上级部门id")
	private String parentDeptId;

	@ApiModelProperty(value = "序号")
	private Long seq;

	@ApiModelProperty(value = "部门人数")
	private Integer count;
	@ApiModelProperty()
	private Boolean memberMain;

	@ApiModelProperty(value = "下级部门信息")
	private List<DeptTreeResponse> children;
}

package com.timevale.contractmanager.core.model.dto.response.process;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取流程预览地址响应数据
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessPreviewUrlResponse extends ToString {

    /** 流程预览地址 */
    private String previewUrl;
}

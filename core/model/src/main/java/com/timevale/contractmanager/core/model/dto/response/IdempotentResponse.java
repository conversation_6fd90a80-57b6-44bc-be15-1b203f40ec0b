package com.timevale.contractmanager.core.model.dto.response;

import com.alibaba.fastjson.JSONObject;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: contract-manager-project
 * @Description: 幂等结果类
 * @date Date : 2022年08月11日 17:41
 */
@Data
public class IdempotentResponse extends ToString {

    /**
     * 请求状态
     * 0:无效请求
     * 1：请求进行中
     * 2：请求完成
     */
    private int status;

    /**
     * 请求结果
     * 仅状态为2时有值
     */
    private JSONObject result;


}

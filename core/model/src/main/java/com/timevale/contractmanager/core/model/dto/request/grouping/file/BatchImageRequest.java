package com.timevale.contractmanager.core.model.dto.request.grouping.file;

import com.timevale.contractmanager.core.model.dto.request.grouping.RequestContextRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: duhui
 * @since: 2021/6/30 3:43 下午
 **/
@Data
public class BatchImageRequest extends RequestContextRequest {
    @ApiModelProperty(name = "菜单id", required = true)
    @NotBlank(message = "菜单id不能为空")
    private String menuId;
    @ApiModelProperty(value = "字段id", required = true)
    @NotBlank(message = "字段id不能为空")
    private String fieldId;
    @ApiModelProperty(value = "文件信息列表", required = true)
    @NotEmpty(message = "文件信息列表不能为空")
    private List<ProcessNameFileRequest> fileInfoList;
}

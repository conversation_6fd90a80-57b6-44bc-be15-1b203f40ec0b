package com.timevale.contractmanager.core.model.dto.response.grouping.menu;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: duhui
 * @since: 2021/7/2 3:27 下午
 **/
@Data
public class MenuPathDTO extends ToString {

    @ApiModelProperty("菜单id")
    private String menuId;

    @ApiModelProperty("菜单名称")
    private String menuName;

    @ApiModelProperty("菜单路径")
    private String menuPath;
}

package com.timevale.contractmanager.core.model.dto.request.grouping.standingbook;

import com.timevale.contractmanager.core.model.dto.request.grouping.RequestContextRequest;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设置自定义字段入参
 * @author: jinhuan
 * @since: 2020-04-17 12:05
 **/
@Data
public class SetFieldRequest extends RequestContextRequest {

    @ApiModelProperty(value = "菜单id")
    @NotBlank(message = "菜单id不能为空")
    private String menuId;

    @ApiModelProperty(value = "字段id")
    @NotBlank(message = "字段id不能为空")
    private String fieldId;

    @ApiModelProperty(value = "宽度")
    @NotNull(message = "宽度不能为空")
    private Integer width;

}

package com.timevale.contractmanager.core.model.dto.response.sharesign;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2021/12/20
 */
@Setter
@Getter
public class ShareSignTaskDetailCountResponse extends ToString {

    @ApiModelProperty("待签署数量")
    private Long waitingApproveNum;

    @ApiModelProperty("待我签署数量")
    private Long myWaitingApproveNum;

    @ApiModelProperty("合同审批中数量")
    private Long approvalNum;

    @ApiModelProperty("填写中数量")
    private Long cooperationNum;

    @ApiModelProperty("任务完成数")
    private int taskDone;

    @ApiModelProperty("任务参与数")
    private int taskNum;
}

package com.timevale.contractmanager.core.model.dto.response.sharedownload;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/10 分享下载合同的单个文件预览信息
 */
@ApiModel("分享下载合同的单个文件预览信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareDownloadShareFilePreviewResponse extends ToString {
    @ApiModelProperty(value = "文档转换总页数")
    private Integer totalPages;

    /**
     * 转换状态
     * 转换中：converting
     * 转换完成：complete
     * 转换失败：fail
     */
    @ApiModelProperty(value = "文档转换状态")
    private String convertStatus;

    @ApiModelProperty(value = "文件转换图片的页面信息")
    private List<PdfPageImageVO> pages;
}

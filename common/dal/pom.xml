<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.timevale.contractmanager</groupId>
		<artifactId>contractmanager-parent</artifactId>
		<version>1.0.1</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

	<artifactId>contractmanager-common-dal</artifactId>
	<name>contractmanager/dal</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.timevale</groupId>
			<artifactId>mandarin-common</artifactId>
		</dependency>

		<dependency>
			<groupId>com.timevale</groupId>
			<artifactId>mandarin-microservice</artifactId>
		</dependency>
		<dependency>
		<groupId>com.timevale.saas</groupId>
		<artifactId>hbase-util</artifactId>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>cn.dalgen.plugins</groupId>
				<artifactId>mybatis-maven-plugin</artifactId>
				<version>1.0.0-SNAPSHOT</version>
				<configuration>
					<copyTemplate>false</copyTemplate>
					<outputDirectory>.</outputDirectory>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>

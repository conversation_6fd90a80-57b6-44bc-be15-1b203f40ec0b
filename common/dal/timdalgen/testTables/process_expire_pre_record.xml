<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="process_expire_pre_record" physicalName="process_expire_pre_record" remark="合同过期预处理记录">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,PROCESS_ID,EXPIRE_TYPE,CREATE_TIME,EXPIRE_TIME,MODIFY_TIME,NOTIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.PROCESS_ID,sf.EXPIRE_TYPE,sf.CREATE_TIME,sf.EXPIRE_TIME,sf.MODIFY_TIME,sf.NOTIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:process_expire_pre_record">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO process_expire_pre_record(
            ID
            ,PROCESS_ID
            ,EXPIRE_TYPE
            ,CREATE_TIME
            ,EXPIRE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{processId,jdbcType=VARCHAR}
            , #{expireType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{expireTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:process_expire_pre_record">
        INSERT INTO process_expire_pre_record(
            ID
            ,PROCESS_ID
            ,EXPIRE_TYPE
            ,CREATE_TIME
            ,EXPIRE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.expireType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.expireTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:process_expire_pre_record">
        <![CDATA[
        UPDATE process_expire_pre_record
        SET
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,EXPIRE_TYPE     = #{expireType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,EXPIRE_TIME     = #{expireTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
            ,NOTIFY_TIME     = #{notifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:process_expire_pre_record">
        <![CDATA[
        DELETE FROM process_expire_pre_record
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:process_expire_pre_record">
        SELECT *
        FROM process_expire_pre_record
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxExpireTime" multiplicity="many" remark="根据普通索引IdxExpireTime获取数据:process_expire_pre_record">
        SELECT *
        FROM process_expire_pre_record
        WHERE
        <![CDATA[
            EXPIRE_TIME     = #{expireTime,jdbcType=TIMESTAMP}
            AND EXPIRE_TYPE     = #{expireType,jdbcType=TINYINT}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxNotifyTime" multiplicity="many" remark="根据普通索引IdxNotifyTime获取数据:process_expire_pre_record">
        SELECT *
        FROM process_expire_pre_record
        WHERE
        <![CDATA[
            NOTIFY_TIME     = #{notifyTime,jdbcType=TIMESTAMP}
            AND EXPIRE_TYPE     = #{expireType,jdbcType=TINYINT}

        ]]>
    </operation>
</table>

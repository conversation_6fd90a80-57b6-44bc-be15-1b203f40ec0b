<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="custome_field_ai" physicalName="custome_field_ai" remark="自定义字段配置-AI解析生成，以企业维度记录">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,GID,OID,FIELD_ID,FIELD_CODE,FIELD_NAME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.GID,sf.OID,sf.FIELD_ID,sf.FIELD_CODE,sf.FIELD_NAME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:custome_field_ai">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO custome_field_ai(
            ID
            ,GID
            ,OID
            ,FIELD_ID
            ,FIELD_CODE
            ,FIELD_NAME
        )VALUES(
             null
            , #{gid,jdbcType=VARCHAR}
            , #{oid,jdbcType=VARCHAR}
            , #{fieldId,jdbcType=VARCHAR}
            , #{fieldCode,jdbcType=VARCHAR}
            , #{fieldName,jdbcType=VARCHAR}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:custome_field_ai">
        INSERT INTO custome_field_ai(
            ID
            ,GID
            ,OID
            ,FIELD_ID
            ,FIELD_CODE
            ,FIELD_NAME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.gid,jdbcType=VARCHAR}
                , #{item.oid,jdbcType=VARCHAR}
                , #{item.fieldId,jdbcType=VARCHAR}
                , #{item.fieldCode,jdbcType=VARCHAR}
                , #{item.fieldName,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:custome_field_ai">
        <![CDATA[
        UPDATE custome_field_ai
        SET
            GID             = #{gid,jdbcType=VARCHAR}
            ,OID             = #{oid,jdbcType=VARCHAR}
            ,FIELD_ID        = #{fieldId,jdbcType=VARCHAR}
            ,FIELD_CODE      = #{fieldCode,jdbcType=VARCHAR}
            ,FIELD_NAME      = #{fieldName,jdbcType=VARCHAR}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:custome_field_ai">
        <![CDATA[
        DELETE FROM custome_field_ai
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:custome_field_ai">
        SELECT *
        FROM custome_field_ai
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>

<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="field_data_ai" physicalName="field_data_ai" remark="存储AI解析后的字段内容-企业维度">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,V,GID,OID,FIELD_CODE
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.V,sf.GID,sf.OID,sf.FIELD_CODE
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:field_data_ai">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO field_data_ai(
            ID
            ,V
            ,GID
            ,OID
            ,FIELD_CODE
        )VALUES(
             null
            , #{v,jdbcType=VARCHAR}
            , #{gid,jdbcType=VARCHAR}
            , #{oid,jdbcType=VARCHAR}
            , #{fieldCode,jdbcType=VARCHAR}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:field_data_ai">
        INSERT INTO field_data_ai(
            ID
            ,V
            ,GID
            ,OID
            ,FIELD_CODE
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.v,jdbcType=VARCHAR}
                , #{item.gid,jdbcType=VARCHAR}
                , #{item.oid,jdbcType=VARCHAR}
                , #{item.fieldCode,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:field_data_ai">
        <![CDATA[
        UPDATE field_data_ai
        SET
            V               = #{v,jdbcType=VARCHAR}
            ,GID             = #{gid,jdbcType=VARCHAR}
            ,OID             = #{oid,jdbcType=VARCHAR}
            ,FIELD_CODE      = #{fieldCode,jdbcType=VARCHAR}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:field_data_ai">
        <![CDATA[
        DELETE FROM field_data_ai
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:field_data_ai">
        SELECT *
        FROM field_data_ai
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxOidK" multiplicity="many" remark="根据普通索引IdxOidK获取数据:field_data_ai">
        SELECT *
        FROM field_data_ai
        WHERE
        <![CDATA[
            OID             = #{oid,jdbcType=VARCHAR}
            AND FIELD_CODE      = #{fieldCode,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="custome_field_sys" physicalName="custome_field_sys" remark="自定义字段配置-系统默认（所有企业共用）">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,FIELD_ID,FIELD_CODE,FIELD_NAME,DEFAULT_SHOW
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.FIELD_ID,sf.FIELD_CODE,sf.FIELD_NAME,sf.DEFAULT_SHOW
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:custome_field_sys">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO custome_field_sys(
            ID
            ,FIELD_ID
            ,FIELD_CODE
            ,FIELD_NAME
            ,DEFAULT_SHOW
        )VALUES(
             null
            , #{fieldId,jdbcType=VARCHAR}
            , #{fieldCode,jdbcType=VARCHAR}
            , #{fieldName,jdbcType=VARCHAR}
            , #{defaultShow,jdbcType=TINYINT}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:custome_field_sys">
        INSERT INTO custome_field_sys(
            ID
            ,FIELD_ID
            ,FIELD_CODE
            ,FIELD_NAME
            ,DEFAULT_SHOW
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.fieldId,jdbcType=VARCHAR}
                , #{item.fieldCode,jdbcType=VARCHAR}
                , #{item.fieldName,jdbcType=VARCHAR}
                , #{item.defaultShow,jdbcType=TINYINT}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:custome_field_sys">
        <![CDATA[
        UPDATE custome_field_sys
        SET
            FIELD_ID        = #{fieldId,jdbcType=VARCHAR}
            ,FIELD_CODE      = #{fieldCode,jdbcType=VARCHAR}
            ,FIELD_NAME      = #{fieldName,jdbcType=VARCHAR}
            ,DEFAULT_SHOW    = #{defaultShow,jdbcType=TINYINT}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:custome_field_sys">
        <![CDATA[
        DELETE FROM custome_field_sys
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:custome_field_sys">
        SELECT *
        FROM custome_field_sys
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>

<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="custome_table_info" physicalName="custome_table_info" remark="表格字段自定义信息">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,GID,OID,MENU_ID,FIELD_ID,DELETED,ENABLED
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.GID,sf.OID,sf.MENU_ID,sf.FIELD_ID,sf.DELETED,sf.ENABLED
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:custome_table_info">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO custome_table_info(
            ID
            ,GID
            ,OID
            ,MENU_ID
            ,FIELD_ID
            ,DELETED
            ,ENABLED
        )VALUES(
             null
            , #{gid,jdbcType=VARCHAR}
            , #{oid,jdbcType=VARCHAR}
            , #{menuId,jdbcType=VARCHAR}
            , #{fieldId,jdbcType=VARCHAR}
            , #{deleted,jdbcType=TINYINT}
            , #{enabled,jdbcType=TINYINT}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:custome_table_info">
        INSERT INTO custome_table_info(
            ID
            ,GID
            ,OID
            ,MENU_ID
            ,FIELD_ID
            ,DELETED
            ,ENABLED
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.gid,jdbcType=VARCHAR}
                , #{item.oid,jdbcType=VARCHAR}
                , #{item.menuId,jdbcType=VARCHAR}
                , #{item.fieldId,jdbcType=VARCHAR}
                , #{item.deleted,jdbcType=TINYINT}
                , #{item.enabled,jdbcType=TINYINT}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:custome_table_info">
        <![CDATA[
        UPDATE custome_table_info
        SET
            GID             = #{gid,jdbcType=VARCHAR}
            ,OID             = #{oid,jdbcType=VARCHAR}
            ,MENU_ID         = #{menuId,jdbcType=VARCHAR}
            ,FIELD_ID        = #{fieldId,jdbcType=VARCHAR}
            ,DELETED         = #{deleted,jdbcType=TINYINT}
            ,ENABLED         = #{enabled,jdbcType=TINYINT}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:custome_table_info">
        <![CDATA[
        DELETE FROM custome_table_info
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:custome_table_info">
        SELECT *
        FROM custome_table_info
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>

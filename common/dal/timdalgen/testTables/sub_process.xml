<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="sub_process" physicalName="sub_process" remark="合同管理主流程与子流程关系">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,PROCESS_ID,SUB_PROCESS_ID,SUB_PROCESS_TYPE,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.PROCESS_ID,sf.SUB_PROCESS_ID,sf.SUB_PROCESS_TYPE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:sub_process">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO sub_process(
            ID
            ,PROCESS_ID
            ,SUB_PROCESS_ID
            ,SUB_PROCESS_TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{processId,jdbcType=VARCHAR}
            , #{subProcessId,jdbcType=VARCHAR}
            , #{subProcessType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:sub_process">
        INSERT INTO sub_process(
            ID
            ,PROCESS_ID
            ,SUB_PROCESS_ID
            ,SUB_PROCESS_TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.subProcessId,jdbcType=VARCHAR}
                , #{item.subProcessType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:sub_process">
        <![CDATA[
        UPDATE sub_process
        SET
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,SUB_PROCESS_ID  = #{subProcessId,jdbcType=VARCHAR}
            ,SUB_PROCESS_TYPE = #{subProcessType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:sub_process">
        <![CDATA[
        DELETE FROM sub_process
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:sub_process">
        SELECT *
        FROM sub_process
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByIdxProcessId" paramtype="object" remark="根据唯一约束IdxProcessId更新表:sub_process">
        <![CDATA[
        UPDATE sub_process
        SET
            SUB_PROCESS_ID  = #{subProcessId,jdbcType=VARCHAR}
            ,SUB_PROCESS_TYPE = #{subProcessType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByIdxProcessId" remark="根据唯一约束IdxProcessId删除数据:sub_process">
        <![CDATA[
        DELETE FROM sub_process
        WHERE
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByIdxProcessId" multiplicity="one" remark="根据唯一约束IdxProcessId获取数据:sub_process">
        SELECT *
        FROM sub_process
        WHERE
        <![CDATA[
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}

        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByIdxSubprocessId" paramtype="object" remark="根据唯一约束IdxSubprocessId更新表:sub_process">
        <![CDATA[
        UPDATE sub_process
        SET
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,SUB_PROCESS_TYPE = #{subProcessType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            SUB_PROCESS_ID  = #{subProcessId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByIdxSubprocessId" remark="根据唯一约束IdxSubprocessId删除数据:sub_process">
        <![CDATA[
        DELETE FROM sub_process
        WHERE
            SUB_PROCESS_ID  = #{subProcessId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByIdxSubprocessId" multiplicity="one" remark="根据唯一约束IdxSubprocessId获取数据:sub_process">
        SELECT *
        FROM sub_process
        WHERE
        <![CDATA[
            SUB_PROCESS_ID  = #{subProcessId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="ai_rule_menu" physicalName="ai_rule_menu" remark="台账规则配置-分类">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,MENU_ID,RULE_ID,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.MENU_ID,sf.RULE_ID,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:ai_rule_menu">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO ai_rule_menu(
            ID
            ,MENU_ID
            ,RULE_ID
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{menuId,jdbcType=VARCHAR}
            , #{ruleId,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:ai_rule_menu">
        INSERT INTO ai_rule_menu(
            ID
            ,MENU_ID
            ,RULE_ID
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.menuId,jdbcType=VARCHAR}
                , #{item.ruleId,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:ai_rule_menu">
        <![CDATA[
        UPDATE ai_rule_menu
        SET
            MENU_ID         = #{menuId,jdbcType=VARCHAR}
            ,RULE_ID         = #{ruleId,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:ai_rule_menu">
        <![CDATA[
        DELETE FROM ai_rule_menu
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:ai_rule_menu">
        SELECT *
        FROM ai_rule_menu
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>

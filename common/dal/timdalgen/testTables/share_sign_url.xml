<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="share_sign_url" physicalName="share_sign_url" remark="分享签分享地址表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,SHARE_URL,PARTICIPANT_ID,SHARE_SIGN_TASK_ID,SHARE_QRCODE_FILEKEY,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.SHARE_URL,sf.PARTICIPANT_ID,sf.SHARE_SIGN_TASK_ID,sf.SHARE_QRCODE_FILEKEY,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:share_sign_url">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO share_sign_url(
            ID
            ,SHARE_URL
            ,PARTICIPANT_ID
            ,PARTICIPANT_LABEL
            ,SHARE_SIGN_TASK_ID
            ,SHARE_QRCODE_FILEKEY
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{shareUrl,jdbcType=VARCHAR}
            , #{participantId,jdbcType=VARCHAR}
            , #{participantLabel,jdbcType=VARCHAR}
            , #{shareSignTaskId,jdbcType=VARCHAR}
            , #{shareQrcodeFilekey,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:share_sign_url">
        INSERT INTO share_sign_url(
            ID
            ,SHARE_URL
            ,PARTICIPANT_ID
            ,PARTICIPANT_LABEL
            ,SHARE_SIGN_TASK_ID
            ,SHARE_QRCODE_FILEKEY
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.shareUrl,jdbcType=VARCHAR}
                , #{item.participantId,jdbcType=VARCHAR}
                , #{item.participantLabel,jdbcType=VARCHAR}
                , #{item.shareSignTaskId,jdbcType=VARCHAR}
                , #{item.shareQrcodeFilekey,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:share_sign_url">
        <![CDATA[
        UPDATE share_sign_url
        SET
            SHARE_URL       = #{shareUrl,jdbcType=VARCHAR}
            ,PARTICIPANT_ID  = #{participantId,jdbcType=VARCHAR}
            ,SHARE_SIGN_TASK_ID = #{shareSignTaskId,jdbcType=VARCHAR}
            ,SHARE_QRCODE_FILEKEY = #{shareQrcodeFilekey,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:share_sign_url">
        <![CDATA[
        DELETE FROM share_sign_url
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:share_sign_url">
        SELECT *
        FROM share_sign_url
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByUniqShareSignTaskId" paramtype="object" remark="根据唯一约束UniqShareSignTaskId更新表:share_sign_url">
        <![CDATA[
        UPDATE share_sign_url
        SET
            SHARE_URL       = #{shareUrl,jdbcType=VARCHAR}
            ,SHARE_QRCODE_FILEKEY = #{shareQrcodeFilekey,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            SHARE_SIGN_TASK_ID = #{shareSignTaskId,jdbcType=VARCHAR}
            AND PARTICIPANT_ID  = #{participantId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByUniqShareSignTaskId" remark="根据唯一约束UniqShareSignTaskId删除数据:share_sign_url">
        <![CDATA[
        DELETE FROM share_sign_url
        WHERE
            SHARE_SIGN_TASK_ID = #{shareSignTaskId,jdbcType=VARCHAR}
            AND PARTICIPANT_ID  = #{participantId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByUniqShareSignTaskId" multiplicity="one" remark="根据唯一约束UniqShareSignTaskId获取数据:share_sign_url">
        SELECT *
        FROM share_sign_url
        WHERE
        <![CDATA[
            SHARE_SIGN_TASK_ID = #{shareSignTaskId,jdbcType=VARCHAR}
            AND PARTICIPANT_ID  = #{participantId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

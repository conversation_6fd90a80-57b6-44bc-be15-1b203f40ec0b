<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="menu" physicalName="menu" remark="菜单记录表（企业空间+个人空间）-以空间维度记录">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,GID,OID,NAME,MENU_ID,PARENT_ID,ORDER,DELETED,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.GID,sf.OID,sf.NAME,sf.MENU_ID,sf.PARENT_ID,sf.ORDER,sf.DELETED,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:menu">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO menu(
            ID
            ,GID
            ,OID
            ,NAME
            ,MENU_ID
            ,PARENT_ID
            ,ORDER
            ,DELETED
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{gid,jdbcType=VARCHAR}
            , #{oid,jdbcType=VARCHAR}
            , #{name,jdbcType=VARCHAR}
            , #{menuId,jdbcType=VARCHAR}
            , #{parentId,jdbcType=VARCHAR}
            , #{order,jdbcType=INTEGER}
            , #{deleted,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:menu">
        INSERT INTO menu(
            ID
            ,GID
            ,OID
            ,NAME
            ,MENU_ID
            ,PARENT_ID
            ,ORDER
            ,DELETED
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.gid,jdbcType=VARCHAR}
                , #{item.oid,jdbcType=VARCHAR}
                , #{item.name,jdbcType=VARCHAR}
                , #{item.menuId,jdbcType=VARCHAR}
                , #{item.parentId,jdbcType=VARCHAR}
                , #{item.order,jdbcType=INTEGER}
                , #{item.deleted,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:menu">
        <![CDATA[
        UPDATE menu
        SET
            GID             = #{gid,jdbcType=VARCHAR}
            ,OID             = #{oid,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,MENU_ID         = #{menuId,jdbcType=VARCHAR}
            ,PARENT_ID       = #{parentId,jdbcType=VARCHAR}
            ,ORDER           = #{order,jdbcType=INTEGER}
            ,DELETED         = #{deleted,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:menu">
        <![CDATA[
        DELETE FROM menu
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:menu">
        SELECT *
        FROM menu
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxMenuId" multiplicity="many" remark="根据普通索引IdxMenuId获取数据:menu">
        SELECT *
        FROM menu
        WHERE
        <![CDATA[
            MENU_ID         = #{menuId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxOid" multiplicity="many" remark="根据普通索引IdxOid获取数据:menu">
        SELECT *
        FROM menu
        WHERE
        <![CDATA[
            OID             = #{oid,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxParentId" multiplicity="many" remark="根据普通索引IdxParentId获取数据:menu">
        SELECT *
        FROM menu
        WHERE
        <![CDATA[
            PARENT_ID       = #{parentId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

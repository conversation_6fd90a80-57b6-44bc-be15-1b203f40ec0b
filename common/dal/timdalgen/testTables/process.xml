<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="process" physicalName="process" remark="合同管理主流程表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,APP_ID,PROCESS_ID,SUBJECT_GID,SUBJECT_OID,SUBJECT_UID,INITIATOR_GID,INITIATOR_OID,INITIATOR_UID,PROCESS_TITLE,PROCESS_INSTANCE_ID,STATUS,DELETED,CREATE_TYPE,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.APP_ID,sf.PROCESS_ID,sf.SUBJECT_GID,sf.SUBJECT_OID,sf.SUBJECT_UID,sf.INITIATOR_GID,sf.INITIATOR_OID,sf.INITIATOR_UID,sf.PROCESS_TITLE,sf.PROCESS_INSTANCE_ID,sf.STATUS,sf.DELETED,sf.CREATE_TYPE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:process">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO process(
            ID
            ,APP_ID
            ,PROCESS_ID
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,SUBJECT_UID
            ,INITIATOR_GID
            ,INITIATOR_OID
            ,INITIATOR_UID
            ,PROCESS_TITLE
            ,PROCESS_INSTANCE_ID
            ,STATUS
            ,DELETED
            ,CREATE_TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{appId,jdbcType=VARCHAR}
            , #{processId,jdbcType=VARCHAR}
            , #{subjectGid,jdbcType=VARCHAR}
            , #{subjectOid,jdbcType=VARCHAR}
            , #{subjectUid,jdbcType=VARCHAR}
            , #{initiatorGid,jdbcType=VARCHAR}
            , #{initiatorOid,jdbcType=VARCHAR}
            , #{initiatorUid,jdbcType=VARCHAR}
            , #{processTitle,jdbcType=VARCHAR}
            , #{processInstanceId,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
            , #{deleted,jdbcType=TINYINT}
            , #{createType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:process">
        INSERT INTO process(
            ID
            ,APP_ID
            ,PROCESS_ID
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,SUBJECT_UID
            ,INITIATOR_GID
            ,INITIATOR_OID
            ,INITIATOR_UID
            ,PROCESS_TITLE
            ,PROCESS_INSTANCE_ID
            ,STATUS
            ,DELETED
            ,CREATE_TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.appId,jdbcType=VARCHAR}
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.subjectGid,jdbcType=VARCHAR}
                , #{item.subjectOid,jdbcType=VARCHAR}
                , #{item.subjectUid,jdbcType=VARCHAR}
                , #{item.initiatorGid,jdbcType=VARCHAR}
                , #{item.initiatorOid,jdbcType=VARCHAR}
                , #{item.initiatorUid,jdbcType=VARCHAR}
                , #{item.processTitle,jdbcType=VARCHAR}
                , #{item.processInstanceId,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
                , #{item.deleted,jdbcType=TINYINT}
                , #{item.createType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:process">
        <![CDATA[
        UPDATE process
        SET
            APP_ID          = #{appId,jdbcType=VARCHAR}
            ,PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            ,SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}
            ,SUBJECT_UID     = #{subjectUid,jdbcType=VARCHAR}
            ,INITIATOR_GID   = #{initiatorGid,jdbcType=VARCHAR}
            ,INITIATOR_OID   = #{initiatorOid,jdbcType=VARCHAR}
            ,INITIATOR_UID   = #{initiatorUid,jdbcType=VARCHAR}
            ,PROCESS_TITLE   = #{processTitle,jdbcType=VARCHAR}
            ,PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,DELETED         = #{deleted,jdbcType=TINYINT}
            ,CREATE_TYPE     = #{createType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:process">
        <![CDATA[
        DELETE FROM process
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:process">
        SELECT *
        FROM process
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByIdxProcessId" paramtype="object" remark="根据唯一约束IdxProcessId更新表:process">
        <![CDATA[
        UPDATE process
        SET
            APP_ID          = #{appId,jdbcType=VARCHAR}
            ,SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            ,SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}
            ,SUBJECT_UID     = #{subjectUid,jdbcType=VARCHAR}
            ,INITIATOR_GID   = #{initiatorGid,jdbcType=VARCHAR}
            ,INITIATOR_OID   = #{initiatorOid,jdbcType=VARCHAR}
            ,INITIATOR_UID   = #{initiatorUid,jdbcType=VARCHAR}
            ,PROCESS_TITLE   = #{processTitle,jdbcType=VARCHAR}
            ,PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,DELETED         = #{deleted,jdbcType=TINYINT}
            ,CREATE_TYPE     = #{createType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByIdxProcessId" remark="根据唯一约束IdxProcessId删除数据:process">
        <![CDATA[
        DELETE FROM process
        WHERE
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByIdxProcessId" multiplicity="one" remark="根据唯一约束IdxProcessId获取数据:process">
        SELECT *
        FROM process
        WHERE
        <![CDATA[
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

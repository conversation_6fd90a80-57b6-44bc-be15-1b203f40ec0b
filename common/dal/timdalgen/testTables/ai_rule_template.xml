<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="ai_rule_template" physicalName="ai_rule_template" remark="台账规则模板表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,GID,OID,UUID,FILEID,RULE_ID,FILE_KEY,TEMPLATE_ID,TYPE,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.GID,sf.OID,sf.UUID,sf.FILEID,sf.RULE_ID,sf.FILE_KEY,sf.TEMPLATE_ID,sf.TYPE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:ai_rule_template">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO ai_rule_template(
            ID
            ,GID
            ,OID
            ,UUID
            ,FILEID
            ,RULE_ID
            ,FILE_KEY
            ,TEMPLATE_ID
            ,TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{gid,jdbcType=VARCHAR}
            , #{oid,jdbcType=VARCHAR}
            , #{uuid,jdbcType=VARCHAR}
            , #{fileid,jdbcType=VARCHAR}
            , #{ruleId,jdbcType=VARCHAR}
            , #{fileKey,jdbcType=VARCHAR}
            , #{templateId,jdbcType=VARCHAR}
            , #{type,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:ai_rule_template">
        INSERT INTO ai_rule_template(
            ID
            ,GID
            ,OID
            ,UUID
            ,FILEID
            ,RULE_ID
            ,FILE_KEY
            ,TEMPLATE_ID
            ,TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.gid,jdbcType=VARCHAR}
                , #{item.oid,jdbcType=VARCHAR}
                , #{item.uuid,jdbcType=VARCHAR}
                , #{item.fileid,jdbcType=VARCHAR}
                , #{item.ruleId,jdbcType=VARCHAR}
                , #{item.fileKey,jdbcType=VARCHAR}
                , #{item.templateId,jdbcType=VARCHAR}
                , #{item.type,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:ai_rule_template">
        <![CDATA[
        UPDATE ai_rule_template
        SET
            GID             = #{gid,jdbcType=VARCHAR}
            ,OID             = #{oid,jdbcType=VARCHAR}
            ,UUID            = #{uuid,jdbcType=VARCHAR}
            ,FILEID          = #{fileid,jdbcType=VARCHAR}
            ,RULE_ID         = #{ruleId,jdbcType=VARCHAR}
            ,FILE_KEY        = #{fileKey,jdbcType=VARCHAR}
            ,TEMPLATE_ID     = #{templateId,jdbcType=VARCHAR}
            ,TYPE            = #{type,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:ai_rule_template">
        <![CDATA[
        DELETE FROM ai_rule_template
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:ai_rule_template">
        SELECT *
        FROM ai_rule_template
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>

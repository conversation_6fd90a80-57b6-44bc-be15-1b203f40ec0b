<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="offline_process_account" physicalName="offline_process_account" remark="纸质合同流程用户信息">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,ROLE,ACCOUNT,PROCESS_ID,ACCOUNT_NAME,SUBJECT_NAME,SUBJECT_TYPE,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.ROLE,sf.ACCOUNT,sf.PROCESS_ID,sf.ACCOUNT_NAME,sf.SUBJECT_NAME,sf.SUBJECT_TYPE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:offline_process_account">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO offline_process_account(
            ID
            ,ROLE
            ,ACCOUNT
            ,PROCESS_ID
            ,ACCOUNT_NAME
            ,SUBJECT_NAME
            ,SUBJECT_TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{role,jdbcType=VARCHAR}
            , #{account,jdbcType=VARCHAR}
            , #{processId,jdbcType=VARCHAR}
            , #{accountName,jdbcType=VARCHAR}
            , #{subjectName,jdbcType=VARCHAR}
            , #{subjectType,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:offline_process_account">
        INSERT INTO offline_process_account(
            ID
            ,ROLE
            ,ACCOUNT
            ,PROCESS_ID
            ,ACCOUNT_NAME
            ,SUBJECT_NAME
            ,SUBJECT_TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.role,jdbcType=VARCHAR}
                , #{item.account,jdbcType=VARCHAR}
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.accountName,jdbcType=VARCHAR}
                , #{item.subjectName,jdbcType=VARCHAR}
                , #{item.subjectType,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:offline_process_account">
        <![CDATA[
        UPDATE offline_process_account
        SET
            ROLE            = #{role,jdbcType=VARCHAR}
            ,ACCOUNT         = #{account,jdbcType=VARCHAR}
            ,PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,ACCOUNT_NAME    = #{accountName,jdbcType=VARCHAR}
            ,SUBJECT_NAME    = #{subjectName,jdbcType=VARCHAR}
            ,SUBJECT_TYPE    = #{subjectType,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:offline_process_account">
        <![CDATA[
        DELETE FROM offline_process_account
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:offline_process_account">
        SELECT *
        FROM offline_process_account
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxOfflineProcessAccountProcessId" multiplicity="many" remark="根据普通索引IdxOfflineProcessAccountProcessId获取数据:offline_process_account">
        SELECT *
        FROM offline_process_account
        WHERE
        <![CDATA[
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

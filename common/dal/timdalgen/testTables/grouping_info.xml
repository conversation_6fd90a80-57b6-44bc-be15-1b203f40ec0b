<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="grouping_info" physicalName="grouping_info" remark="合同归档记录表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,MENU_ID,PROCESS_ID,CONTRACT_NO,SUBJECT_GID,SUBJECT_OID,OPERATOR_GID,OPERATOR_OID,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.MENU_ID,sf.PROCESS_ID,sf.CONTRACT_NO,sf.SUBJECT_GID,sf.SUBJECT_OID,sf.OPERATOR_GID,sf.OPERATOR_OID,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:grouping_info">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO grouping_info(
            ID
            ,MENU_ID
            ,PROCESS_ID
            ,CONTRACT_NO
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,OPERATOR_GID
            ,OPERATOR_OID
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{menuId,jdbcType=VARCHAR}
            , #{processId,jdbcType=VARCHAR}
            , #{contractNo,jdbcType=VARCHAR}
            , #{subjectGid,jdbcType=VARCHAR}
            , #{subjectOid,jdbcType=VARCHAR}
            , #{operatorGid,jdbcType=VARCHAR}
            , #{operatorOid,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:grouping_info">
        INSERT INTO grouping_info(
            ID
            ,MENU_ID
            ,PROCESS_ID
            ,CONTRACT_NO
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,OPERATOR_GID
            ,OPERATOR_OID
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.menuId,jdbcType=VARCHAR}
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.contractNo,jdbcType=VARCHAR}
                , #{item.subjectGid,jdbcType=VARCHAR}
                , #{item.subjectOid,jdbcType=VARCHAR}
                , #{item.operatorGid,jdbcType=VARCHAR}
                , #{item.operatorOid,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:grouping_info">
        <![CDATA[
        UPDATE grouping_info
        SET
            MENU_ID         = #{menuId,jdbcType=VARCHAR}
            ,PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,CONTRACT_NO     = #{contractNo,jdbcType=VARCHAR}
            ,SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            ,SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}
            ,OPERATOR_GID    = #{operatorGid,jdbcType=VARCHAR}
            ,OPERATOR_OID    = #{operatorOid,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:grouping_info">
        <![CDATA[
        DELETE FROM grouping_info
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:grouping_info">
        SELECT *
        FROM grouping_info
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxProcessMenuOid" multiplicity="many" remark="根据普通索引IdxProcessMenuOid获取数据:grouping_info">
        SELECT *
        FROM grouping_info
        WHERE
        <![CDATA[
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            AND MENU_ID         = #{menuId,jdbcType=VARCHAR}
            AND SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

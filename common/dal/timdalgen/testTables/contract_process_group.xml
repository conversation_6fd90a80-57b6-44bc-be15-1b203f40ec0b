<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="contract_process_group" physicalName="contract_process_group" remark="contract_process_group">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,PROCESS_GROUP_ID,PROCESS_GROUP_NAME,PROCESS_GROUP_CREATOR,PROCESS_GROUP_TYPE,GMT_CREATE,GMT_MODIFIED
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.PROCESS_GROUP_ID,sf.PROCESS_GROUP_NAME,sf.PROCESS_GROUP_CREATOR,sf.PROCESS_GROUP_TYPE,sf.GMT_CREATE,sf.GMT_MODIFIED
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:contract_process_group">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO contract_process_group(
            ID
            ,PROCESS_GROUP_ID
            ,PROCESS_GROUP_NAME
            ,PROCESS_GROUP_CREATOR
            ,PROCESS_GROUP_TYPE
            ,GMT_CREATE
            ,GMT_MODIFIED
        )VALUES(
             null
            , #{processGroupId,jdbcType=VARCHAR}
            , #{processGroupName,jdbcType=VARCHAR}
            , #{processGroupCreator,jdbcType=VARCHAR}
            , #{processGroupType,jdbcType=TINYINT}
            , now()
            , now()
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:contract_process_group">
        INSERT INTO contract_process_group(
            ID
            ,PROCESS_GROUP_ID
            ,PROCESS_GROUP_NAME
            ,PROCESS_GROUP_CREATOR
            ,PROCESS_GROUP_TYPE
            ,GMT_CREATE
            ,GMT_MODIFIED
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.processGroupId,jdbcType=VARCHAR}
                , #{item.processGroupName,jdbcType=VARCHAR}
                , #{item.processGroupCreator,jdbcType=VARCHAR}
                , #{item.processGroupType,jdbcType=TINYINT}
                , now()
                , now()
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:contract_process_group">
        <![CDATA[
        UPDATE contract_process_group
        SET
            PROCESS_GROUP_ID = #{processGroupId,jdbcType=VARCHAR}
            ,PROCESS_GROUP_NAME = #{processGroupName,jdbcType=VARCHAR}
            ,PROCESS_GROUP_CREATOR = #{processGroupCreator,jdbcType=VARCHAR}
            ,PROCESS_GROUP_TYPE = #{processGroupType,jdbcType=TINYINT}
            ,GMT_MODIFIED    = now()
        WHERE
            ID              = #{id,jdbcType=INTEGER}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:contract_process_group">
        <![CDATA[
        DELETE FROM contract_process_group
        WHERE
            ID = #{id,jdbcType=INTEGER}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:contract_process_group">
        SELECT *
        FROM contract_process_group
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=INTEGER}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxProcessGroupId" multiplicity="many" remark="根据普通索引IdxProcessGroupId获取数据:contract_process_group">
        SELECT *
        FROM contract_process_group
        WHERE
        <![CDATA[
            PROCESS_GROUP_ID = #{processGroupId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxProcessGroupName" multiplicity="many" remark="根据普通索引IdxProcessGroupName获取数据:contract_process_group">
        SELECT *
        FROM contract_process_group
        WHERE
        <![CDATA[
            PROCESS_GROUP_CREATOR = #{processGroupCreator,jdbcType=VARCHAR}
            AND PROCESS_GROUP_NAME = #{processGroupName,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxProcessGroupType" multiplicity="many" remark="根据普通索引IdxProcessGroupType获取数据:contract_process_group">
        SELECT *
        FROM contract_process_group
        WHERE
        <![CDATA[
            PROCESS_GROUP_CREATOR = #{processGroupCreator,jdbcType=VARCHAR}
            AND PROCESS_GROUP_TYPE = #{processGroupType,jdbcType=TINYINT}

        ]]>
    </operation>
</table>

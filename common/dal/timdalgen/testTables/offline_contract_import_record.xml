<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="offline_contract_import_record" physicalName="offline_contract_import_record" remark="纸质合同导入记录">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,MENU_ID,STATUS,RECORD_ID,IMPORT_WAY,EXTRACT_WAY,SUBJECT_GID,SUBJECT_OID,IMPORTER_GID,IMPORTER_OID,SUBJECT_NAME,IMPORTER_NAME,EXTRACT_CONFIG,CONTRACT_SIZE,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.MENU_ID,sf.STATUS,sf.RECORD_ID,sf.IMPORT_WAY,sf.EXTRACT_WAY,sf.SUBJECT_GID,sf.SUBJECT_OID,sf.IMPORTER_GID,sf.IMPORTER_OID,sf.SUBJECT_NAME,sf.IMPORTER_NAME,sf.EXTRACT_CONFIG,sf.CONTRACT_SIZE,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:offline_contract_import_record">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO offline_contract_import_record(
            ID
            ,MENU_ID
            ,STATUS
            ,RECORD_ID
            ,IMPORT_WAY
            ,EXTRACT_WAY
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,IMPORTER_GID
            ,IMPORTER_OID
            ,SUBJECT_NAME
            ,IMPORTER_NAME
            ,EXTRACT_CONFIG
            ,CONTRACT_SIZE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{menuId,jdbcType=VARCHAR}
            , #{status,jdbcType=VARCHAR}
            , #{recordId,jdbcType=VARCHAR}
            , #{importWay,jdbcType=VARCHAR}
            , #{extractWay,jdbcType=VARCHAR}
            , #{subjectGid,jdbcType=VARCHAR}
            , #{subjectOid,jdbcType=VARCHAR}
            , #{IMPORTERGid,jdbcType=VARCHAR}
            , #{IMPORTEROid,jdbcType=VARCHAR}
            , #{subjectName,jdbcType=VARCHAR}
            , #{IMPORTERName,jdbcType=VARCHAR}
            , #{extractConfig,jdbcType=LONGVARCHAR}
            , #{contractSize,jdbcType=INTEGER}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:offline_contract_import_record">
        INSERT INTO offline_contract_import_record(
            ID
            ,MENU_ID
            ,STATUS
            ,RECORD_ID
            ,IMPORT_WAY
            ,EXTRACT_WAY
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,IMPORTER_GID
            ,IMPORTER_OID
            ,SUBJECT_NAME
            ,IMPORTER_NAME
            ,EXTRACT_CONFIG
            ,CONTRACT_SIZE
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.menuId,jdbcType=VARCHAR}
                , #{item.status,jdbcType=VARCHAR}
                , #{item.recordId,jdbcType=VARCHAR}
                , #{item.importWay,jdbcType=VARCHAR}
                , #{item.extractWay,jdbcType=VARCHAR}
                , #{item.subjectGid,jdbcType=VARCHAR}
                , #{item.subjectOid,jdbcType=VARCHAR}
                , #{item.IMPORTERGid,jdbcType=VARCHAR}
                , #{item.IMPORTEROid,jdbcType=VARCHAR}
                , #{item.subjectName,jdbcType=VARCHAR}
                , #{item.IMPORTERName,jdbcType=VARCHAR}
                , #{item.extractConfig,jdbcType=LONGVARCHAR}
                , #{item.contractSize,jdbcType=INTEGER}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:offline_contract_import_record">
        <![CDATA[
        UPDATE offline_contract_import_record
        SET
            MENU_ID         = #{menuId,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=VARCHAR}
            ,RECORD_ID       = #{recordId,jdbcType=VARCHAR}
            ,IMPORT_WAY      = #{importWay,jdbcType=VARCHAR}
            ,EXTRACT_WAY     = #{extractWay,jdbcType=VARCHAR}
            ,SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            ,SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}
            ,IMPORTER_GID    = #{IMPORTERGid,jdbcType=VARCHAR}
            ,IMPORTER_OID    = #{IMPORTEROid,jdbcType=VARCHAR}
            ,SUBJECT_NAME    = #{subjectName,jdbcType=VARCHAR}
            ,IMPORTER_NAME   = #{IMPORTERName,jdbcType=VARCHAR}
            ,EXTRACT_CONFIG  = #{extractConfig,jdbcType=LONGVARCHAR}
            ,CONTRACT_SIZE   = #{contractSize,jdbcType=INTEGER}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:offline_contract_import_record">
        <![CDATA[
        DELETE FROM offline_contract_import_record
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:offline_contract_import_record">
        SELECT *
        FROM offline_contract_import_record
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxContractOfflineImportRecordId" multiplicity="many" remark="根据普通索引IdxContractOfflineImportRecordId获取数据:offline_contract_import_record">
        SELECT *
        FROM offline_contract_import_record
        WHERE
        <![CDATA[
            RECORD_ID       = #{recordId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxContractOfflineImportRecordSubjectGid" multiplicity="many" remark="根据普通索引IdxContractOfflineImportRecordSubjectGid获取数据:offline_contract_import_record">
        SELECT *
        FROM offline_contract_import_record
        WHERE
        <![CDATA[
            SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            AND CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}

        ]]>
    </operation>
</table>

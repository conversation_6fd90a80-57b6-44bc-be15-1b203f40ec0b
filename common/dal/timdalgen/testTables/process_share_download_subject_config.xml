<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="process_share_download_subject_config" physicalName="process_share_download_subject_config" remark="主体分享功能配置信息表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,SUBJECT_GID,SUBJECT_OID,STATUS,DELETED,ACCESS_LIMIT,ACCESS_STATUS,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.SUBJECT_GID,sf.SUBJECT_OID,sf.STATUS,sf.DELETED,sf.ACCESS_LIMIT,sf.ACCESS_STATUS,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:process_share_download_subject_config">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO process_share_download_subject_config(
            ID
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,STATUS
            ,DELETED
            ,ACCESS_LIMIT
            ,ACCESS_STATUS
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{subjectGid,jdbcType=VARCHAR}
            , #{subjectOid,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
            , #{deleted,jdbcType=TINYINT}
            , #{accessLimit,jdbcType=INTEGER}
            , #{accessStatus,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:process_share_download_subject_config">
        INSERT INTO process_share_download_subject_config(
            ID
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,STATUS
            ,DELETED
            ,ACCESS_LIMIT
            ,ACCESS_STATUS
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.subjectGid,jdbcType=VARCHAR}
                , #{item.subjectOid,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
                , #{item.deleted,jdbcType=TINYINT}
                , #{item.accessLimit,jdbcType=INTEGER}
                , #{item.accessStatus,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:process_share_download_subject_config">
        <![CDATA[
        UPDATE process_share_download_subject_config
        SET
            SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            ,SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,DELETED         = #{deleted,jdbcType=TINYINT}
            ,ACCESS_LIMIT    = #{accessLimit,jdbcType=INTEGER}
            ,ACCESS_STATUS   = #{accessStatus,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:process_share_download_subject_config">
        <![CDATA[
        DELETE FROM process_share_download_subject_config
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:process_share_download_subject_config">
        SELECT *
        FROM process_share_download_subject_config
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>

<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="offline_contract_import_files" physicalName="offline_contract_import_files" remark="纸质合同导入记录对应的文件列表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,FILE_ID,STATUS,FILE_MODE,FILE_NAME,FILE_TYPE,RECORD_ID,FILE_GROUP_ID,ORIGIN_FILE_ID,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.FILE_ID,sf.STATUS,sf.FILE_MODE,sf.FILE_NAME,sf.FILE_TYPE,sf.RECORD_ID,sf.FILE_GROUP_ID,sf.ORIGIN_FILE_ID,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:offline_contract_import_files">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO offline_contract_import_files(
            ID
            ,FILE_ID
            ,STATUS
            ,FILE_MODE
            ,FILE_NAME
            ,FILE_TYPE
            ,RECORD_ID
            ,FILE_GROUP_ID
            ,ORIGIN_FILE_ID
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{fileId,jdbcType=VARCHAR}
            , #{status,jdbcType=VARCHAR}
            , #{fileMode,jdbcType=VARCHAR}
            , #{fileName,jdbcType=VARCHAR}
            , #{fileType,jdbcType=VARCHAR}
            , #{recordId,jdbcType=VARCHAR}
            , #{fileGroupId,jdbcType=VARCHAR}
            , #{originFileId,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:offline_contract_import_files">
        INSERT INTO offline_contract_import_files(
            ID
            ,FILE_ID
            ,STATUS
            ,FILE_MODE
            ,FILE_NAME
            ,FILE_TYPE
            ,RECORD_ID
            ,FILE_GROUP_ID
            ,ORIGIN_FILE_ID
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.fileId,jdbcType=VARCHAR}
                , #{item.status,jdbcType=VARCHAR}
                , #{item.fileMode,jdbcType=VARCHAR}
                , #{item.fileName,jdbcType=VARCHAR}
                , #{item.fileType,jdbcType=VARCHAR}
                , #{item.recordId,jdbcType=VARCHAR}
                , #{item.fileGroupId,jdbcType=VARCHAR}
                , #{item.originFileId,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:offline_contract_import_files">
        <![CDATA[
        UPDATE offline_contract_import_files
        SET
            FILE_ID         = #{fileId,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=VARCHAR}
            ,FILE_MODE       = #{fileMode,jdbcType=VARCHAR}
            ,FILE_NAME       = #{fileName,jdbcType=VARCHAR}
            ,FILE_TYPE       = #{fileType,jdbcType=VARCHAR}
            ,RECORD_ID       = #{recordId,jdbcType=VARCHAR}
            ,FILE_GROUP_ID   = #{fileGroupId,jdbcType=VARCHAR}
            ,ORIGIN_FILE_ID  = #{originFileId,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:offline_contract_import_files">
        <![CDATA[
        DELETE FROM offline_contract_import_files
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:offline_contract_import_files">
        SELECT *
        FROM offline_contract_import_files
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxContractOfflineImportFilesFileId" multiplicity="many" remark="根据普通索引IdxContractOfflineImportFilesFileId获取数据:offline_contract_import_files">
        SELECT *
        FROM offline_contract_import_files
        WHERE
        <![CDATA[
            FILE_ID         = #{fileId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxContractOfflineImportFilesGroupId" multiplicity="many" remark="根据普通索引IdxContractOfflineImportFilesGroupId获取数据:offline_contract_import_files">
        SELECT *
        FROM offline_contract_import_files
        WHERE
        <![CDATA[
            FILE_GROUP_ID   = #{fileGroupId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxContractOfflineImportFilesRecordId" multiplicity="many" remark="根据普通索引IdxContractOfflineImportFilesRecordId获取数据:offline_contract_import_files">
        SELECT *
        FROM offline_contract_import_files
        WHERE
        <![CDATA[
            RECORD_ID       = #{recordId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

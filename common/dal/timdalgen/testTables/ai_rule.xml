<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="ai_rule" physicalName="ai_rule" remark="ai台账规则信息表-以空间维度记录多少个台账规则">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,GID,OID,FILE_ID,RULE_ID,TYPE,FILE_NO,STATUS,ENABLED,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.GID,sf.OID,sf.FILE_ID,sf.RULE_ID,sf.TYPE,sf.FILE_NO,sf.STATUS,sf.ENABLED,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:ai_rule">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO ai_rule(
            ID
            ,GID
            ,OID
            ,FILE_ID
            ,RULE_ID
            ,TYPE
            ,FILE_NO
            ,STATUS
            ,ENABLED
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{gid,jdbcType=VARCHAR}
            , #{oid,jdbcType=VARCHAR}
            , #{fileId,jdbcType=VARCHAR}
            , #{ruleId,jdbcType=VARCHAR}
            , #{type,jdbcType=TINYINT}
            , #{fileNo,jdbcType=INTEGER}
            , #{status,jdbcType=TINYINT}
            , #{enabled,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:ai_rule">
        INSERT INTO ai_rule(
            ID
            ,GID
            ,OID
            ,FILE_ID
            ,RULE_ID
            ,TYPE
            ,FILE_NO
            ,STATUS
            ,ENABLED
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.gid,jdbcType=VARCHAR}
                , #{item.oid,jdbcType=VARCHAR}
                , #{item.fileId,jdbcType=VARCHAR}
                , #{item.ruleId,jdbcType=VARCHAR}
                , #{item.type,jdbcType=TINYINT}
                , #{item.fileNo,jdbcType=INTEGER}
                , #{item.status,jdbcType=TINYINT}
                , #{item.enabled,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:ai_rule">
        <![CDATA[
        UPDATE ai_rule
        SET
            GID             = #{gid,jdbcType=VARCHAR}
            ,OID             = #{oid,jdbcType=VARCHAR}
            ,FILE_ID         = #{fileId,jdbcType=VARCHAR}
            ,RULE_ID         = #{ruleId,jdbcType=VARCHAR}
            ,TYPE            = #{type,jdbcType=TINYINT}
            ,FILE_NO         = #{fileNo,jdbcType=INTEGER}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,ENABLED         = #{enabled,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:ai_rule">
        <![CDATA[
        DELETE FROM ai_rule
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:ai_rule">
        SELECT *
        FROM ai_rule
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxOidType" multiplicity="many" remark="根据普通索引IdxOidType获取数据:ai_rule">
        SELECT *
        FROM ai_rule
        WHERE
        <![CDATA[
            OID             = #{oid,jdbcType=VARCHAR}
            AND TYPE            = #{type,jdbcType=TINYINT}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxRuleId" multiplicity="many" remark="根据普通索引IdxRuleId获取数据:ai_rule">
        SELECT *
        FROM ai_rule
        WHERE
        <![CDATA[
            RULE_ID         = #{ruleId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

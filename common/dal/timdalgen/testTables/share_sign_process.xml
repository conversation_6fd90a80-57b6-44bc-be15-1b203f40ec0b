<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="share_sign_process" physicalName="share_sign_process" remark="分享签流程记录">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,PROCESS_ID,SHARE_SIGN_TASK_ID,SIGNER_OID,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.PROCESS_ID,sf.SHARE_SIGN_TASK_ID,sf.SIGNER_OID,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:share_sign_process">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO share_sign_process(
            ID
            ,PROCESS_ID
            ,SHARE_SIGN_TASK_ID
            ,PARTICIPANT_ID
            ,ACCOUNT_OID
            ,ACCOUNT_GID
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{processId,jdbcType=VARCHAR}
            , #{shareSignTaskId,jdbcType=VARCHAR}
            , #{participantId,jdbcType=VARCHAR}
            , #{accountOid,jdbcType=VARCHAR}
            , #{accountGid,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:share_sign_process">
        INSERT INTO share_sign_process(
            ID
            ,PROCESS_ID
            ,SHARE_SIGN_TASK_ID
            ,PARTICIPANT_ID
            ,ACCOUNT_OID
            ,ACCOUNT_GID
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.shareSignTaskId,jdbcType=VARCHAR}
                , #{item.participantId,jdbcType=VARCHAR}
                , #{item.accountOid,jdbcType=VARCHAR}
                , #{item.accountGid,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <operation name="deleteById"  remark="根据主键删除数据:share_sign_process">
        <![CDATA[
        DELETE FROM share_sign_process
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:share_sign_process">
        SELECT *
        FROM share_sign_process
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxProcessId" multiplicity="many" remark="根据普通索引IdxProcessId获取数据:share_sign_process">
        SELECT *
        FROM share_sign_process
        WHERE
        <![CDATA[
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxShareSignTaskId" multiplicity="many" remark="根据普通索引IdxShareSignTaskId获取数据:share_sign_process">
        SELECT *
        FROM share_sign_process
        WHERE
        <![CDATA[
            SHARE_SIGN_TASK_ID = #{shareSignTaskId,jdbcType=VARCHAR}
            AND ACCOUNT_OID      = #{accountOid,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

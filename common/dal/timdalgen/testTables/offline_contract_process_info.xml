<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="offline_contract_process_info" physicalName="offline_contract_process_info" remark="纸质合同导入记录对应的合同信息">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,RECORD_ID,PROCESS_ID,FILE_GROUP_ID,PROCESS_INFO,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.RECORD_ID,sf.PROCESS_ID,sf.FILE_GROUP_ID,sf.PROCESS_INFO,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:offline_contract_process_info">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO offline_contract_process_info(
            ID
            ,RECORD_ID
            ,PROCESS_ID
            ,FILE_GROUP_ID
            ,PROCESS_INFO
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{recordId,jdbcType=VARCHAR}
            , #{processId,jdbcType=VARCHAR}
            , #{fileGroupId,jdbcType=VARCHAR}
            , #{processInfo,jdbcType=LONGVARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:offline_contract_process_info">
        INSERT INTO offline_contract_process_info(
            ID
            ,RECORD_ID
            ,PROCESS_ID
            ,FILE_GROUP_ID
            ,PROCESS_INFO
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.recordId,jdbcType=VARCHAR}
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.fileGroupId,jdbcType=VARCHAR}
                , #{item.processInfo,jdbcType=LONGVARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:offline_contract_process_info">
        <![CDATA[
        UPDATE offline_contract_process_info
        SET
            RECORD_ID       = #{recordId,jdbcType=VARCHAR}
            ,PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,FILE_GROUP_ID   = #{fileGroupId,jdbcType=VARCHAR}
            ,PROCESS_INFO    = #{processInfo,jdbcType=LONGVARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:offline_contract_process_info">
        <![CDATA[
        DELETE FROM offline_contract_process_info
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:offline_contract_process_info">
        SELECT *
        FROM offline_contract_process_info
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxContractOfflineImportFilesFileGroupId" multiplicity="many" remark="根据普通索引IdxContractOfflineImportFilesFileGroupId获取数据:offline_contract_process_info">
        SELECT *
        FROM offline_contract_process_info
        WHERE
        <![CDATA[
            FILE_GROUP_ID   = #{fileGroupId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxContractOfflineImportFilesProcessId" multiplicity="many" remark="根据普通索引IdxContractOfflineImportFilesProcessId获取数据:offline_contract_process_info">
        SELECT *
        FROM offline_contract_process_info
        WHERE
        <![CDATA[
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxContractOfflineImportFilesRecordId" multiplicity="many" remark="根据普通索引IdxContractOfflineImportFilesRecordId获取数据:offline_contract_process_info">
        SELECT *
        FROM offline_contract_process_info
        WHERE
        <![CDATA[
            RECORD_ID       = #{recordId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

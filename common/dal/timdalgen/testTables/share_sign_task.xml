<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="share_sign_task" physicalName="share_sign_task" remark="分享签任务表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,TASK_ID,ACCOUNT_GID,ACCOUNT_OID,SHARE_BIZ_ID,SUBJECT_GID,SUBJECT_OID,ACCOUNT_NAME,SUBJECT_NAME,SHARE_TEMPLATE_ID,STATUS,SHARE_NUM,SHARE_TYPE,SHARE_TOTAL,END_TIME,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.TASK_ID,sf.ACCOUNT_GID,sf.ACCOUNT_OID,sf.SHARE_BIZ_ID,sf.SUBJECT_GID,sf.SUBJECT_OID,sf.ACCOUNT_NAME,sf.SUBJECT_NAME,sf.SHARE_TEMPLATE_ID,sf.STATUS,sf.SHARE_NUM,sf.SHARE_TYPE,sf.SHARE_TOTAL,sf.END_TIME,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:share_sign_task">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO share_sign_task(
            ID
            ,TASK_ID
            ,TASK_NAME
            ,ACCOUNT_GID
            ,ACCOUNT_OID
            ,SHARE_BIZ_ID
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,ACCOUNT_NAME
            ,SUBJECT_NAME
            ,SHARE_TEMPLATE_ID
            ,STATUS
            ,SHARE_DONE
            ,SHARE_NUM
            ,SHARE_TYPE
            ,SHARE_TOTAL
            ,END_TIME
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{taskId,jdbcType=VARCHAR}
            , #{taskName,jdbcType=VARCHAR}
            , #{accountGid,jdbcType=VARCHAR}
            , #{accountOid,jdbcType=VARCHAR}
            , #{shareBizId,jdbcType=VARCHAR}
            , #{subjectGid,jdbcType=VARCHAR}
            , #{subjectOid,jdbcType=VARCHAR}
            , #{accountName,jdbcType=VARCHAR}
            , #{subjectName,jdbcType=VARCHAR}
            , #{shareTemplateId,jdbcType=VARCHAR}
            , #{status,jdbcType=TINYINT}
            , #{shareDone,jdbcType=INTEGER}
            , #{shareNum,jdbcType=INTEGER}
            , #{shareType,jdbcType=TINYINT}
            , #{shareTotal,jdbcType=INTEGER}
            , #{endTime,jdbcType=TIMESTAMP}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:share_sign_task">
        INSERT INTO share_sign_task(
            ID
            ,TASK_ID
            ,TASK_NAME
            ,ACCOUNT_GID
            ,ACCOUNT_OID
            ,SHARE_BIZ_ID
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,ACCOUNT_NAME
            ,SUBJECT_NAME
            ,SHARE_TEMPLATE_ID
            ,STATUS
            ,SHARE_NUM
            ,SHARE_TYPE
            ,SHARE_TOTAL
            ,END_TIME
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.taskId,jdbcType=VARCHAR}
                , #{item.taskName,jdbcType=VARCHAR}
                , #{item.accountGid,jdbcType=VARCHAR}
                , #{item.accountOid,jdbcType=VARCHAR}
                , #{item.shareBizId,jdbcType=VARCHAR}
                , #{item.subjectGid,jdbcType=VARCHAR}
                , #{item.subjectOid,jdbcType=VARCHAR}
                , #{item.accountName,jdbcType=VARCHAR}
                , #{item.subjectName,jdbcType=VARCHAR}
                , #{item.shareTemplateId,jdbcType=VARCHAR}
                , #{item.status,jdbcType=TINYINT}
                , #{item.shareNum,jdbcType=INTEGER}
                , #{item.shareType,jdbcType=TINYINT}
                , #{item.shareTotal,jdbcType=INTEGER}
                , #{item.endTime,jdbcType=TIMESTAMP}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:share_sign_task">
        <![CDATA[
        UPDATE share_sign_task
        SET
            TASK_ID         = #{taskId,jdbcType=VARCHAR}
            ,ACCOUNT_GID     = #{accountGid,jdbcType=VARCHAR}
            ,ACCOUNT_OID     = #{accountOid,jdbcType=VARCHAR}
            ,SHARE_BIZ_ID    = #{shareBizId,jdbcType=VARCHAR}
            ,SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            ,SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}
            ,ACCOUNT_NAME    = #{accountName,jdbcType=VARCHAR}
            ,SUBJECT_NAME    = #{subjectName,jdbcType=VARCHAR}
            ,SHARE_TEMPLATE_ID = #{shareTemplateId,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,SHARE_NUM       = #{shareNum,jdbcType=INTEGER}
            ,SHARE_TYPE      = #{shareType,jdbcType=TINYINT}
            ,SHARE_TOTAL     = #{shareTotal,jdbcType=INTEGER}
            ,END_TIME        = #{endTime,jdbcType=TIMESTAMP}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:share_sign_task">
        <![CDATA[
        DELETE FROM share_sign_task
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:share_sign_task">
        SELECT *
        FROM share_sign_task
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByUniqTaskId" paramtype="object" remark="根据唯一约束UniqTaskId更新表:share_sign_task">
        <![CDATA[
        UPDATE share_sign_task
        SET
            ACCOUNT_GID     = #{accountGid,jdbcType=VARCHAR}
            ,ACCOUNT_OID     = #{accountOid,jdbcType=VARCHAR}
            ,SHARE_BIZ_ID    = #{shareBizId,jdbcType=VARCHAR}
            ,SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            ,SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}
            ,ACCOUNT_NAME    = #{accountName,jdbcType=VARCHAR}
            ,SUBJECT_NAME    = #{subjectName,jdbcType=VARCHAR}
            ,SHARE_TEMPLATE_ID = #{shareTemplateId,jdbcType=VARCHAR}
            ,STATUS          = #{status,jdbcType=TINYINT}
            ,SHARE_NUM       = #{shareNum,jdbcType=INTEGER}
            ,SHARE_TYPE      = #{shareType,jdbcType=TINYINT}
            ,SHARE_TOTAL     = #{shareTotal,jdbcType=INTEGER}
            ,END_TIME        = #{endTime,jdbcType=TIMESTAMP}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            TASK_ID         = #{taskId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="deleteByUniqTaskId" remark="根据唯一约束UniqTaskId删除数据:share_sign_task">
        <![CDATA[
        DELETE FROM share_sign_task
        WHERE
            TASK_ID         = #{taskId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByUniqTaskId" multiplicity="one" remark="根据唯一约束UniqTaskId获取数据:share_sign_task">
        SELECT *
        FROM share_sign_task
        WHERE
        <![CDATA[
            TASK_ID         = #{taskId,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxShareBizId" multiplicity="many" remark="根据普通索引IdxShareBizId获取数据:share_sign_task">
        SELECT *
        FROM share_sign_task
        WHERE
        <![CDATA[
            SHARE_BIZ_ID    = #{shareBizId,jdbcType=VARCHAR}
            AND SHARE_TYPE      = #{shareType,jdbcType=TINYINT}

        ]]>
    </operation>
</table>

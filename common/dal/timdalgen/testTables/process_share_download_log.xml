<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="process_share_download_log" physicalName="process_share_download_log" remark="分享合同操作记录表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,PROCESS_ID,SUBJECT_GID,SUBJECT_OID,OPERATING_OID,DELETED,OPERATING_TYPE,CREATE_TIME,UPDATE_TIME,OPERATING_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.PROCESS_ID,sf.SUBJECT_GID,sf.SUBJECT_OID,sf.OPERATING_OID,sf.DELETED,sf.OPERATING_TYPE,sf.CREATE_TIME,sf.UPDATE_TIME,sf.OPERATING_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:process_share_download_log">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO process_share_download_log(
            ID
            ,PROCESS_ID
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,OPERATING_OID
            ,DELETED
            ,OPERATING_TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
            ,OPERATING_TIME
        )VALUES(
             null
            , #{processId,jdbcType=VARCHAR}
            , #{subjectGid,jdbcType=VARCHAR}
            , #{subjectOid,jdbcType=VARCHAR}
            , #{operatingOid,jdbcType=VARCHAR}
            , #{deleted,jdbcType=TINYINT}
            , #{operatingType,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
            , #{operatingTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:process_share_download_log">
        INSERT INTO process_share_download_log(
            ID
            ,PROCESS_ID
            ,SUBJECT_GID
            ,SUBJECT_OID
            ,OPERATING_OID
            ,DELETED
            ,OPERATING_TYPE
            ,CREATE_TIME
            ,UPDATE_TIME
            ,OPERATING_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.subjectGid,jdbcType=VARCHAR}
                , #{item.subjectOid,jdbcType=VARCHAR}
                , #{item.operatingOid,jdbcType=VARCHAR}
                , #{item.deleted,jdbcType=TINYINT}
                , #{item.operatingType,jdbcType=TINYINT}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
                , #{item.operatingTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:process_share_download_log">
        <![CDATA[
        UPDATE process_share_download_log
        SET
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,SUBJECT_GID     = #{subjectGid,jdbcType=VARCHAR}
            ,SUBJECT_OID     = #{subjectOid,jdbcType=VARCHAR}
            ,OPERATING_OID   = #{operatingOid,jdbcType=VARCHAR}
            ,DELETED         = #{deleted,jdbcType=TINYINT}
            ,OPERATING_TYPE  = #{operatingType,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
            ,OPERATING_TIME  = #{operatingTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:process_share_download_log">
        <![CDATA[
        DELETE FROM process_share_download_log
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:process_share_download_log">
        SELECT *
        FROM process_share_download_log
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
</table>

<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="offline_process_file" physicalName="offline_process_file" remark="纸质合同流程文件信息">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,FILE_ID,FILE_KEY,FILE_NAME,FILE_TYPE,PROCESS_ID,OCR_FILE_KEY,CREATE_TIME,UPDATE_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.FILE_ID,sf.FILE_KEY,sf.FILE_NAME,sf.FILE_TYPE,sf.PROCESS_ID,sf.OCR_FILE_KEY,sf.CREATE_TIME,sf.UPDATE_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:offline_process_file">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO offline_process_file(
            ID
            ,FILE_ID
            ,FILE_KEY
            ,FILE_NAME
            ,FILE_TYPE
            ,PROCESS_ID
            ,OCR_FILE_KEY
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES(
             null
            , #{fileId,jdbcType=VARCHAR}
            , #{fileKey,jdbcType=VARCHAR}
            , #{fileName,jdbcType=VARCHAR}
            , #{fileType,jdbcType=VARCHAR}
            , #{processId,jdbcType=VARCHAR}
            , #{ocrFileKey,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:offline_process_file">
        INSERT INTO offline_process_file(
            ID
            ,FILE_ID
            ,FILE_KEY
            ,FILE_NAME
            ,FILE_TYPE
            ,PROCESS_ID
            ,OCR_FILE_KEY
            ,CREATE_TIME
            ,UPDATE_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.fileId,jdbcType=VARCHAR}
                , #{item.fileKey,jdbcType=VARCHAR}
                , #{item.fileName,jdbcType=VARCHAR}
                , #{item.fileType,jdbcType=VARCHAR}
                , #{item.processId,jdbcType=VARCHAR}
                , #{item.ocrFileKey,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:offline_process_file">
        <![CDATA[
        UPDATE offline_process_file
        SET
            FILE_ID         = #{fileId,jdbcType=VARCHAR}
            ,FILE_KEY        = #{fileKey,jdbcType=VARCHAR}
            ,FILE_NAME       = #{fileName,jdbcType=VARCHAR}
            ,FILE_TYPE       = #{fileType,jdbcType=VARCHAR}
            ,PROCESS_ID      = #{processId,jdbcType=VARCHAR}
            ,OCR_FILE_KEY    = #{ocrFileKey,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:offline_process_file">
        <![CDATA[
        DELETE FROM offline_process_file
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:offline_process_file">
        SELECT *
        FROM offline_process_file
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxOfflineProcessFileProcessId" multiplicity="many" remark="根据普通索引IdxOfflineProcessFileProcessId获取数据:offline_process_file">
        SELECT *
        FROM offline_process_file
        WHERE
        <![CDATA[
            PROCESS_ID      = #{processId,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="user_menu_permission" physicalName="user_menu_permission" remark="用户拥有菜单的角色权限-只有企业空间下的用户才记录，个人空间无需记录">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,GID,OID,NAME,EMAIL,MENU_ID,ROLE_ID,MOBILE_NO,CREATE_TIME,MODIFY_TIME
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.GID,sf.OID,sf.NAME,sf.EMAIL,sf.MENU_ID,sf.ROLE_ID,sf.MOBILE_NO,sf.CREATE_TIME,sf.MODIFY_TIME
    </sql>

    <operation name="insert" paramtype="object" remark="插入表:user_menu_permission">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <![CDATA[
        INSERT INTO user_menu_permission(
            ID
            ,GID
            ,OID
            ,NAME
            ,EMAIL
            ,MENU_ID
            ,ROLE_ID
            ,MOBILE_NO
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES(
             null
            , #{gid,jdbcType=VARCHAR}
            , #{oid,jdbcType=VARCHAR}
            , #{name,jdbcType=VARCHAR}
            , #{email,jdbcType=VARCHAR}
            , #{menuId,jdbcType=VARCHAR}
            , #{roleId,jdbcType=VARCHAR}
            , #{mobileNo,jdbcType=VARCHAR}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{modifyTime,jdbcType=TIMESTAMP}
        )
        ]]>
    </operation>

    <!-- foreach 可以自定义类型，paramtype="primitive" foreach->javatype="自己书写的类"  -->
    <!-- 只有一个参数且为List时必须将参数命名为list -->
    <operation name="insertBatch" paramtype="objectList" remark="批量插入表:user_menu_permission">
        INSERT INTO user_menu_permission(
            ID
            ,GID
            ,OID
            ,NAME
            ,EMAIL
            ,MENU_ID
            ,ROLE_ID
            ,MOBILE_NO
            ,CREATE_TIME
            ,MODIFY_TIME
        )VALUES
        <foreach collection="list"  item="item" separator=",">
            (
                 null
                , #{item.gid,jdbcType=VARCHAR}
                , #{item.oid,jdbcType=VARCHAR}
                , #{item.name,jdbcType=VARCHAR}
                , #{item.email,jdbcType=VARCHAR}
                , #{item.menuId,jdbcType=VARCHAR}
                , #{item.roleId,jdbcType=VARCHAR}
                , #{item.mobileNo,jdbcType=VARCHAR}
                , #{item.createTime,jdbcType=TIMESTAMP}
                , #{item.modifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>

    <!-- 不推荐使用全 update 有需要自己打开
    <operation name="update" paramtype="object" remark="更新表:user_menu_permission">
        <![CDATA[
        UPDATE user_menu_permission
        SET
            GID             = #{gid,jdbcType=VARCHAR}
            ,OID             = #{oid,jdbcType=VARCHAR}
            ,NAME            = #{name,jdbcType=VARCHAR}
            ,EMAIL           = #{email,jdbcType=VARCHAR}
            ,MENU_ID         = #{menuId,jdbcType=VARCHAR}
            ,ROLE_ID         = #{roleId,jdbcType=VARCHAR}
            ,MOBILE_NO       = #{mobileNo,jdbcType=VARCHAR}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,MODIFY_TIME     = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    -->
    <operation name="deleteById"  remark="根据主键删除数据:user_menu_permission">
        <![CDATA[
        DELETE FROM user_menu_permission
        WHERE
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>

    <operation name="getById" multiplicity="one" remark="根据主键获取数据:user_menu_permission">
        SELECT *
        FROM user_menu_permission
        WHERE
        <![CDATA[
            ID = #{id,jdbcType=BIGINT}
        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxMenuidOid" multiplicity="many" remark="根据普通索引IdxMenuidOid获取数据:user_menu_permission">
        SELECT *
        FROM user_menu_permission
        WHERE
        <![CDATA[
            MENU_ID         = #{menuId,jdbcType=VARCHAR}
            AND OID             = #{oid,jdbcType=VARCHAR}

        ]]>
    </operation>
    <!-- 根据普通索引查询数据 -->
    <operation name="queryByIdxName" multiplicity="many" remark="根据普通索引IdxName获取数据:user_menu_permission">
        SELECT *
        FROM user_menu_permission
        WHERE
        <![CDATA[
            NAME            = #{name,jdbcType=VARCHAR}

        ]]>
    </operation>
</table>

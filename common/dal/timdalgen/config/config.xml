<?xml version="1.0" encoding="UTF-8"?>

<!-- ============================================================== -->
<!--                   dal代码生成器主配置                                                                           -->
<!-- ============================================================== -->

<config>
	<!-- ============================以下配置无需修改==================  -->
    <!-- “数据库类型”与“java类型”映射配置 -->
    <typemap from="java.sql.Date" to="java.util.Date"/>
    <typemap from="java.sql.Time" to="java.util.Date"/>
    <typemap from="java.sql.Timestamp" to="java.util.Date"/>
    <typemap from="java.math.BigDecimal" to="Long"/>
    <typemap from="byte" to="int"/>
    <typemap from="short" to="int"/>

    <!-- 代码生成路径配置 -->
    <package value="com.timevale.contractmanager.common.dal">
        <subClass name="dal" value="dal"/>
        <subClass name="mapper" value="mapper"/>
        <subClass name="mapper.xml" value="/mapper"/><!-- 绝对路径 -->
        <subClass name="paging" value="paging"/>
        <subClass name="dao" value="dao"/>
        <subClass name="dataobject" value="bean"/>
        <subClass name="resultmap" value="resultmap"/>
    </package>

    <!--分库分表规则  分表后缀 支持多个-->
    <splitTableSuffixs>
        <splitTableSuffix value="_000"/>
    </splitTableSuffixs>

    <!-- 索引省略前缀，支持多个 -->
    <indexPrefixs>
        <!--长的放前面-->
        <indexPrefix value="idx_"/>
        <indexPrefix value="uk_" replace=""/>
    </indexPrefixs>

	<!-- 扩展参数，自定义模板时扩展支持 -->
    <extParams>
        <extParam name="Repository" value="true"/>
    </extParams>
    
    <!-- ====================以下为为需修改配置====================== -->
    <!-- 配置数据源，支持多数据源配置 -->
    <database name="test" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url" value="**************************************************************************************************************************"/>
        <property name="userid" value="docm"/>
        <property name="password" value="docm#123456#"/>
    </database>
    
    <!-- 表省略前缀，支持多个 -->
    <tablePrefixs database="test">
        <!--长的放前面-->
        <tablePrefix value="test_" replace=""/>
        <tablePrefix value="pre_"/>
    </tablePrefixs>
    
</config>

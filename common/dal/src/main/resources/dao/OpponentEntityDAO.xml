<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.timevale.contractmanager.common.dal.dao.opponententity.OpponentEntityDAO">
  <resultMap id="BaseResultMap" type="com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 08 10:43:22 CST 2022.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="tenant_oid" jdbcType="VARCHAR" property="tenantOid" />
    <result column="tenant_gid" jdbcType="VARCHAR" property="tenantGid" />
    <result column="entity_unique_id" jdbcType="VARCHAR" property="entityUniqueId" />
    <result column="entity_type" jdbcType="TINYINT" property="entityType" />
    <result column="entity_oid" jdbcType="VARCHAR" property="entityOid" />
    <result column="entity_gid" jdbcType="VARCHAR" property="entityGid" />
    <result column="entity_name" jdbcType="VARCHAR" property="entityName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="attached_entity_id" jdbcType="BIGINT" property="attachedEntityId" />
    <result column="create_process_id" jdbcType="VARCHAR" property="createProcessId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_by_oid" jdbcType="VARCHAR" property="createByOid" />
    <result column="create_by_gid" jdbcType="VARCHAR" property="createByGid" />
    <result column="modify_by_oid" jdbcType="VARCHAR" property="modifyByOid" />
    <result column="modify_by_gid" jdbcType="VARCHAR" property="modifyByGid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime" />
    <result column="authorize_type" jdbcType="TINYINT" property="authorizeType" />
    <result column="risk_level" jdbcType="INTEGER" property="riskLevel" />
    <result column="detection_org_id" jdbcType="VARCHAR" property="detectionOrgId" />
    <result column="social_credit_code" jdbcType="VARCHAR" property="socialCreditCode" />
    <result column="legal_representative_name" jdbcType="VARCHAR" property="legalRepresentativeName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 08 10:43:22 CST 2022.
    -->
    id, uuid, tenant_oid, tenant_gid, entity_unique_id, entity_type, entity_oid, entity_gid, 
    entity_name, description, attached_entity_id, create_process_id, deleted, create_by_oid, 
    create_by_gid, modify_by_oid, modify_by_gid, create_time, modified_time, authorize_type, 
    risk_level, detection_org_id, social_credit_code, legal_representative_name
  </sql>



  <insert id="insertSelective" parameterType="com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 08 10:43:22 CST 2022.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into opponent_entity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uuid != null">
        uuid,
      </if>
      <if test="tenantOid != null">
        tenant_oid,
      </if>
      <if test="tenantGid != null">
        tenant_gid,
      </if>
      <if test="entityUniqueId != null">
        entity_unique_id,
      </if>
      <if test="entityType != null">
        entity_type,
      </if>
      <if test="entityOid != null">
        entity_oid,
      </if>
      <if test="entityGid != null">
        entity_gid,
      </if>
      <if test="entityName != null">
        entity_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="attachedEntityId != null">
        attached_entity_id,
      </if>
      <if test="createProcessId != null">
        create_process_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createByOid != null">
        create_by_oid,
      </if>
      <if test="createByGid != null">
        create_by_gid,
      </if>
      <if test="modifyByOid != null">
        modify_by_oid,
      </if>
      <if test="modifyByGid != null">
        modify_by_gid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifiedTime != null">
        modified_time,
      </if>
      <if test="authorizeType != null">
        authorize_type,
      </if>
      <if test="riskLevel != null">
        risk_level,
      </if>
      <if test="detectionOrgId != null">
        detection_org_id,
      </if>
      <if test="socialCreditCode != null">
        social_credit_code,
      </if>
      <if test="legalRepresentativeName != null">
        legal_representative_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="tenantOid != null">
        #{tenantOid,jdbcType=VARCHAR},
      </if>
      <if test="tenantGid != null">
        #{tenantGid,jdbcType=VARCHAR},
      </if>
      <if test="entityUniqueId != null">
        #{entityUniqueId,jdbcType=VARCHAR},
      </if>
      <if test="entityType != null">
        #{entityType,jdbcType=TINYINT},
      </if>
      <if test="entityOid != null">
        #{entityOid,jdbcType=VARCHAR},
      </if>
      <if test="entityGid != null">
        #{entityGid,jdbcType=VARCHAR},
      </if>
      <if test="entityName != null">
        #{entityName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="attachedEntityId != null">
        #{attachedEntityId,jdbcType=BIGINT},
      </if>
      <if test="createProcessId != null">
        #{createProcessId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createByOid != null">
        #{createByOid,jdbcType=VARCHAR},
      </if>
      <if test="createByGid != null">
        #{createByGid,jdbcType=VARCHAR},
      </if>
      <if test="modifyByOid != null">
        #{modifyByOid,jdbcType=VARCHAR},
      </if>
      <if test="modifyByGid != null">
        #{modifyByGid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedTime != null">
        #{modifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authorizeType != null">
        #{authorizeType,jdbcType=TINYINT},
      </if>
      <if test="riskLevel != null">
        #{riskLevel,jdbcType=INTEGER},
      </if>
      <if test="detectionOrgId != null">
        #{detectionOrgId,jdbcType=VARCHAR},
      </if>
      <if test="socialCreditCode != null">
        #{socialCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="legalRepresentativeName != null">
        #{legalRepresentativeName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateById" parameterType="com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO">
    update opponent_entity
    <set>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="tenantOid != null">
        tenant_oid = #{tenantOid,jdbcType=VARCHAR},
      </if>
      <if test="tenantGid != null">
        tenant_gid = #{tenantGid,jdbcType=VARCHAR},
      </if>
      <if test="entityUniqueId != null">
        entity_unique_id = #{entityUniqueId,jdbcType=VARCHAR},
      </if>
      <if test="entityType != null">
        entity_type = #{entityType,jdbcType=TINYINT},
      </if>
      <if test="entityOid != null">
        entity_oid = #{entityOid,jdbcType=VARCHAR},
      </if>
      <if test="entityGid != null">
        entity_gid = #{entityGid,jdbcType=VARCHAR},
      </if>
      <if test="entityName != null">
        entity_name = #{entityName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="attachedEntityId != null">
        attached_entity_id = #{attachedEntityId,jdbcType=BIGINT},
      </if>
      <if test="createProcessId != null">
        create_process_id = #{createProcessId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createByOid != null">
        create_by_oid = #{createByOid,jdbcType=VARCHAR},
      </if>
      <if test="createByGid != null">
        create_by_gid = #{createByGid,jdbcType=VARCHAR},
      </if>
      <if test="modifyByOid != null">
        modify_by_oid = #{modifyByOid,jdbcType=VARCHAR},
      </if>
      <if test="modifyByGid != null">
        modify_by_gid = #{modifyByGid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authorizeType != null">
        authorize_type = #{authorizeType,jdbcType=TINYINT},
      </if>
      <if test="riskLevel != null">
        risk_level = #{riskLevel,jdbcType=INTEGER},
      </if>
      <if test="detectionOrgId != null">
        detection_org_id = #{detectionOrgId,jdbcType=VARCHAR},
      </if>
      <if test="socialCreditCode != null">
        social_credit_code = #{socialCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="legalRepresentativeName != null">
        legal_representative_name = #{legalRepresentativeName,jdbcType=VARCHAR},
      </if>
      <if test="modifiedTime != null">
        modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <sql id="fix_data_where_sql">
    <where>
      <if test="ignoreDeleted == null or ignoreDeleted==false">
        and deleted = 0
      </if>
      <if test="tenantGid != null">
        and tenant_gid = #{tenantGid}
      </if>
      <if test="entityType != null">
        and entity_type = #{entityType}
      </if>
      <if test="minCreateTime != null">
        and create_time >= #{minCreateTime}
      </if>
      <if test="maxCreateTime != null">
        and create_time <![CDATA[ <= ]]> #{maxCreateTime}
      </if>
    </where>
  </sql>

  <select id="listFixDataByQuery" resultMap="BaseResultMap"
          parameterType="com.timevale.contractmanager.common.dal.query.opponent.OpponentEntityFixDataQuery">
    select
    <include refid="Base_Column_List" />
    from opponent_entity
    <include refid="fix_data_where_sql"/>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="pageIndex != null and pageSize != null">
      limit #{start},#{pageSize}
    </if>
  </select>

  <select id="listScrollById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from opponent_entity
    <if test="minId != null">
      id >= #{minId}
    </if>
    <include refid="fix_data_where_sql"/>
    order by id asc
    limit #{pageSize}
  </select>

  <select id="listByEntityOidAndEntityType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from opponent_entity
    where
    deleted = 0 and
    entity_oid = #{entityOid}
    <if test="entityType != null">
      and entity_type = #{entityType}
    </if>
  </select>

  <update id="clearAttachedEntityId">
    update opponent_entity set attached_entity_id = null where id = #{id}
  </update>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.timevale.contractmanager.common.dal.dao.ProcessDataCollectErrorMsgDAO">
  <resultMap id="BaseResultMap" type="com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="topic" jdbcType="VARCHAR" property="topic" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="process_id" jdbcType="VARCHAR" property="processId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    id, create_time, topic, tag, remark
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    content
  </sql>

  <select id="listSearchAfter" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from process_data_collect_error_msg
    where
    <if test="minId != null">
      id >= #{minId}
    </if>
    order by id asc limit #{size}
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from process_data_collect_error_msg
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    delete from process_data_collect_error_msg
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="save" parameterType="com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into process_data_collect_error_msg
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="topic != null">
        topic,
      </if>
      <if test="tag != null">
        tag,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="processId != null">
        process_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="topic != null">
        #{topic,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="processId != null">
        #{processId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    update process_data_collect_error_msg
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="topic != null">
        topic = #{topic,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        tag = #{tag,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    update process_data_collect_error_msg
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      topic = #{topic,jdbcType=VARCHAR},
      tag = #{tag,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.timevale.contractmanager.common.dal.bean.ProcessDataCollectErrorMsgDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jun 10 18:12:51 CST 2022.
    -->
    update process_data_collect_error_msg
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      topic = #{topic,jdbcType=VARCHAR},
      tag = #{tag,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
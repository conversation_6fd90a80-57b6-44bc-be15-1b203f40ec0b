<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.timevale.contractmanager.common.dal.dao.ProcessStartDataDAO">
  <resultMap id="BaseResultMap" type="com.timevale.contractmanager.common.dal.bean.ProcessStartDataDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_id" jdbcType="VARCHAR" property="dataId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="subject_oid" jdbcType="VARCHAR" property="subjectOid" />
    <result column="subject_gid" jdbcType="VARCHAR" property="subjectGid" />
    <result column="owner_oid" jdbcType="VARCHAR" property="ownerOid" />
    <result column="owner_gid" jdbcType="VARCHAR" property="ownerGid" />
    <result column="outer_data_id" jdbcType="VARCHAR" property="outerDataId" />
    <result column="flow_template_id" jdbcType="VARCHAR" property="flowTemplateId" />
    <result column="data_source_id" jdbcType="VARCHAR" property="dataSourceId" />
    <result column="data_source_channel" jdbcType="VARCHAR" property="dataSourceChannel" />
    <result column="data_source_name" jdbcType="VARCHAR" property="dataSourceName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="process_title" jdbcType="VARCHAR" property="processTitle" />
    <result column="process_id" jdbcType="VARCHAR" property="processId" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="type" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    id, data_id, create_time, update_time, deleted, subject_oid, subject_gid, owner_oid, 
    owner_gid, outer_data_id, flow_template_id, data_source_id, data_source_channel, 
    data_source_name, status, process_title, process_id, extra, type
  </sql>

  <select id="getByDataId" parameterType="string" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from process_start_data
    where data_id = #{dataId} and deleted = 0
  </select>

  <select id="getByProcessId" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from process_start_data
    where process_id = #{processId}
  </select>

  <select id="listByDataIdList" parameterType="list" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from process_start_data
    where deleted = 0 and data_id in
    <foreach collection="dataIdList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="listByOwnerOidAndDataSourceChannel" parameterType="com.timevale.contractmanager.common.dal.query.processstartdata.ProcessStartDataQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from process_start_data
    where subject_oid= #{subjectOid}
    and    owner_oid = #{ownerOid}
    and data_source_channel = #{dataSourceChannel}
    and deleted = 0
  </select>

  <!-- 查询符合条件的记录总数 -->


  <select id="getByFlowTemplateIdAndOuterDataId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from process_start_data
    where outer_data_id = #{outerDataId} and flow_template_id=#{flowTemplateId}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from process_start_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  

  <insert id="insertSelective" parameterType="com.timevale.contractmanager.common.dal.bean.ProcessStartDataDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into process_start_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        data_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="subjectOid != null">
        subject_oid,
      </if>
      <if test="subjectGid != null">
        subject_gid,
      </if>
      <if test="ownerOid != null">
        owner_oid,
      </if>
      <if test="ownerGid != null">
        owner_gid,
      </if>
      <if test="outerDataId != null">
        outer_data_id,
      </if>
      <if test="flowTemplateId != null">
        flow_template_id,
      </if>
      <if test="dataSourceId != null">
        data_source_id,
      </if>
      <if test="dataSourceChannel != null">
        data_source_channel,
      </if>
      <if test="dataSourceName != null">
        data_source_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="processTitle != null">
        process_title,
      </if>
      <if test="processId != null">
        process_id,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        #{dataId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="subjectOid != null">
        #{subjectOid,jdbcType=VARCHAR},
      </if>
      <if test="subjectGid != null">
        #{subjectGid,jdbcType=VARCHAR},
      </if>
      <if test="ownerOid != null">
        #{ownerOid,jdbcType=VARCHAR},
      </if>
      <if test="ownerGid != null">
        #{ownerGid,jdbcType=VARCHAR},
      </if>
      <if test="outerDataId != null">
        #{outerDataId,jdbcType=VARCHAR},
      </if>
      <if test="flowTemplateId != null">
        #{flowTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="dataSourceId != null">
        #{dataSourceId,jdbcType=VARCHAR},
      </if>
      <if test="dataSourceChannel != null">
        #{dataSourceChannel,jdbcType=VARCHAR},
      </if>
      <if test="dataSourceName != null">
        #{dataSourceName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="processTitle != null">
        #{processTitle,jdbcType=VARCHAR},
      </if>
      <if test="processId != null">
        #{processId,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByDataId" parameterType="com.timevale.contractmanager.common.dal.bean.ProcessStartDataDO">
    update process_start_data
    <set>
      <if test="dataId != null">
        data_id = #{dataId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="subjectOid != null">
        subject_oid = #{subjectOid,jdbcType=VARCHAR},
      </if>
      <if test="subjectGid != null">
        subject_gid = #{subjectGid,jdbcType=VARCHAR},
      </if>
      <if test="ownerOid != null">
        owner_oid = #{ownerOid,jdbcType=VARCHAR},
      </if>
      <if test="ownerGid != null">
        owner_gid = #{ownerGid,jdbcType=VARCHAR},
      </if>
      <if test="outerDataId != null">
        outer_data_id = #{outerDataId,jdbcType=VARCHAR},
      </if>
      <if test="flowTemplateId != null">
        flow_template_id = #{flowTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="dataSourceId != null">
        data_source_id = #{dataSourceId,jdbcType=VARCHAR},
      </if>
      <if test="dataSourceChannel != null">
        data_source_channel = #{dataSourceChannel,jdbcType=VARCHAR},
      </if>
      <if test="dataSourceName != null">
        data_source_name = #{dataSourceName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="processTitle != null">
        process_title = #{processTitle,jdbcType=VARCHAR},
      </if>
      <if test="processId != null">
        process_id = #{processId,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where data_id = #{dataId}
  </update>

  <!-- ... 已有代码 ... -->

  <update id="updateOwnerByDataIds" parameterType="java.util.List">
    UPDATE process_start_data
    <set>
      <if test="records != null and records.size() > 0">
        owner_oid =
        <foreach collection="records" item="record" separator=" " open="CASE data_id" close="END">
          WHEN #{record.dataId} THEN #{record.ownerOid}
        </foreach>,
        owner_gid =
        <foreach collection="records" item="record" separator=" " open="CASE data_id" close="END">
          WHEN #{record.dataId} THEN #{record.ownerGid}
        </foreach>
      </if>
    </set>
    WHERE data_id IN
    <foreach collection="records" item="record" open="(" close=")" separator=",">
      #{record.dataId}
    </foreach>
  </update>

  <!-- ... 已有代码 ... -->




  <update id="revokeByDataId" parameterType="com.timevale.contractmanager.common.dal.bean.ProcessStartDataDO">
    update process_start_data
    <set>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="processId != null">
        process_id = #{processId,jdbcType=VARCHAR},
      </if>
      <if test="processId == null">
        process_id = null,
      </if>
    </set>
    where data_id = #{dataId}
  </update>

  <update id="deleteByDataIds">
    update process_start_data set deleted = 1 where
    data_id in
    <foreach collection="dataIdList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>

  <update id="trueDeleteByDataIds">
    delete from process_start_data where
    data_id in
    <foreach collection="dataIdList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>

  <sql id="where_sql">
    <where>
        and deleted = 0
      <if test="processTitle != null">
        and process_title like concat('%', #{processTitle}, '%')
      </if>
      <if test="statusList != null and statusList.size() > 0">
        and status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
          #{status}
        </foreach>
      </if>

      <if test="typeList != null and typeList.size() > 0">
        and type in
        <foreach collection="typeList" item="type" open="(" close=")" separator=",">
          #{type}
        </foreach>
      </if>

      <if test="dataSourceChannel != null">
        and data_source_channel = #{dataSourceChannel}
      </if>
      <if test="subjectGid != null">
        and subject_gid = #{subjectGid}
      </if>
      <if test="ownerGid != null">
        and owner_gid = #{ownerGid}
      </if>
      <if test="maxCreateTime != null">
        and create_time <![CDATA[ < ]]> #{maxCreateTime}
      </if>
    </where>
  </sql>



  <select id="listByQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from process_start_data
    <include refid="where_sql"/>
    <if test="orderByClause != null">
      ${orderByClause}
    </if>
    <if test="pageNum != null and pageSize != null">
      limit #{start},#{pageSize}
    </if>
  </select>

  <select id="listByProcessIds" resultMap="BaseResultMap">
    select * from process_start_data where
    process_id in
    <foreach collection="processIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    limit 500
  </select>

  <select id="countByQuery" resultType="int">
    select
    count(1)
    from process_start_data
    <include refid="where_sql"/>
  </select>
  
  <select id="listByOuterDataId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from process_start_data
    where outer_data_id = #{outerDataId} and deleted = 0
  </select>

</mapper>

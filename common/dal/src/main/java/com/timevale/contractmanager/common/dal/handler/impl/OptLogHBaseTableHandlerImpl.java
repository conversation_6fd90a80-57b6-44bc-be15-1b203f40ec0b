package com.timevale.contractmanager.common.dal.handler.impl;

import com.timevale.contractmanager.common.dal.bean.ProcessLogDO;
import com.timevale.contractmanager.common.dal.configuration.HBaseConfig;
import com.timevale.contractmanager.common.dal.configuration.HBaseConnectionAutoFactory;
import com.timevale.contractmanager.common.dal.dao.constant.HBaseConstant;
import com.timevale.contractmanager.common.dal.dao.constant.HBaseOptProcessLogTableEnum;
import com.timevale.contractmanager.common.dal.dao.constant.HBaseProcessOptLogColumnEnum;
import com.timevale.framework.sands.Sahara;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.hbase.handler.HBaseColumnHandler;
import com.timevale.saas.hbase.handler.HBaseTableHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Table;

import java.io.IOException;
@Slf4j
public class OptLogHBaseTableHandlerImpl implements HBaseTableHandler<ProcessLogDO> {

    private static HBaseConfig hBaseConfig;

    private static OptLogHBaseTableHandlerImpl instance =new OptLogHBaseTableHandlerImpl();

    public static OptLogHBaseTableHandlerImpl getInstance(HBaseConfig hBaseConfigParam) {
        hBaseConfig=hBaseConfigParam;
        return instance;
    }

    public static void setInstance(OptLogHBaseTableHandlerImpl instance) {
        OptLogHBaseTableHandlerImpl.instance = instance;
    }

    @Override
    public String getRowKey(ProcessLogDO data) {
        if (StringUtils.isNotEmpty(data.getRowKey())) {
            return data.getRowKey();
        }
        return generateRowKey(data.getProcessId(), data.getId());
    }



    public static String generateRowKey(String bizId, Long id) {
        if (null == id) {
            // id默认是rds自增id，只操作hbase时基于雪花算法生成自增id（需要配置sands.enabled=true）
            id = Sahara.instance.getSand();
        }
        StringBuffer idBuffer = new StringBuffer();
        idBuffer.append(bizId).append(generateRowKeySuffix(id));
        log.info("OptLogHBaseTableHandlerImpl generateRowKey {} ",idBuffer.toString());
        return idBuffer.toString();
    }

    /**
     * rowkey生成的规则有两种:
     * 1.当数据双写的时候使用processId+id,组合来生成,processId是32位的,id是mysql自增id,通过16进制转换,如果不足8位在后面用0补足
     * processId本身就是唯一的,id也是唯一的保证rowkey是唯一的
     * 2.当数据单写hbase的时候，使用processId+(snowId后八位),来构成rowkey的唯一性
     * rowkey的长度设计位40个字符的长度
     *
     *
     * @param id
     * @return
     *
     */
    private static String generateRowKeySuffix(Long id) {
        String idStr = Long.toHexString(id);
        //大于8个字符的长度，直接截取8个字符
        if(idStr.length()>HBaseConstant.ROWKEY_SUFFIX_LENGTH){
            // 当id大于8位的时候，需要从后向前截取，而非从前向后截取,来保证截取id的唯一性
            return idStr.substring(idStr.length()-HBaseConstant.ROWKEY_SUFFIX_LENGTH,idStr.length());
        }

        return StringUtils.leftPad(
                idStr,
                HBaseConstant.ROWKEY_SUFFIX_LENGTH,
                HBaseConstant.ROWKEY_SUFFIX_PADDING_CHAR);
    }

    @Override
    public String getFamily() {
        return HBaseOptProcessLogTableEnum.OPT_LOG.getFamily();
    }

    @Override
    public String getTableName() {
        String tableName = HBaseOptProcessLogTableEnum.OPT_LOG.getTable();
        return hBaseConfig.getHBASE_TABLE_PREFIX()+tableName;
    }

    @Override
    public byte[] getFamilyBytes() {
        return HBaseOptProcessLogTableEnum.OPT_LOG.getFamilyBytes();
    }

    @Override
    public HBaseColumnHandler getColumnHandler(String columnName) {
        HBaseProcessOptLogColumnEnum hBaseProcessOptLogColumnEnum = HBaseProcessOptLogColumnEnum.getColumnEnumMap().get(columnName);
        if(hBaseProcessOptLogColumnEnum==null){
            return null;
        }
        return hBaseProcessOptLogColumnEnum.getHandler();
    }

    @Override
    public Table getTable() throws IOException {
        return HBaseConnectionAutoFactory.getConnection()
                .getTable(TableName.valueOf(getTableName()));
    }
}

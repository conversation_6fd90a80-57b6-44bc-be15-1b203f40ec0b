package com.timevale.contractmanager.common.dal.bean;

import java.util.Date;

/**
 * The table.
 * 合同流程自定义配置
 * <AUTHOR> Kunpeng
 */
public class ProcessCustomizeConfigDO {

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * processId 主流程id.
     */
    private String processId;
    /**
     * subjectOid 主体oid
     */
    private String subjectOid;
    /**
     * subjectGid 主体gid
     */
    private String subjectGid;

    /**
     * configInfo 流程自定义配置信息，json字符串
     * 1、renewable-是否需要续签
     */
    private String configInfo;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set processId 主流程id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 主流程id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    public String getSubjectOid() {
        return subjectOid;
    }

    public void setSubjectOid(String subjectOid) {
        this.subjectOid = subjectOid;
    }

    public String getSubjectGid() {
        return subjectGid;
    }

    public void setSubjectGid(String subjectGid) {
        this.subjectGid = subjectGid;
    }

    public String getConfigInfo() {
        return configInfo;
    }

    public void setConfigInfo(String configInfo) {
        this.configInfo = configInfo;
    }
}

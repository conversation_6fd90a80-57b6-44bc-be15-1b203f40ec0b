package com.timevale.contractmanager.common.dal.facade;

import com.timevale.contractmanager.common.dal.bean.ProcessLogDO;
import com.timevale.contractmanager.common.dal.dao.hbase.ProcessOptLogHBaseDAO;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProcessOptLogDAOFacade {

    @Autowired
    ProcessOptLogHBaseDAO processOptLogHBaseDAO;

    @Transactional(rollbackFor = Exception.class)
    public void insert(ProcessLogDO processLogDO){
        // hbase不支持插入数据自动生成时间
        processLogDO.setCreateTime(new Date());

        processOptLogHBaseDAO.insert(processLogDO);
    }

    public List<ProcessLogDO> queryProcessLogs(String processId) {
        return processOptLogHBaseDAO.getByProcessId(processId,null,null);
    }


    public List<ProcessLogDO> queryProcessLogs(String processId, int optType) {
        List<ProcessLogDO> processLogDOS = queryProcessLogs(processId);
        List<ProcessLogDO> collectList = processLogDOS.stream().filter(p -> p.getOperateType().equals(optType)).collect(Collectors.toList());
        return collectList;
    }

}

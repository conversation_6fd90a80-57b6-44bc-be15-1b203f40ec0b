package com.timevale.contractmanager.common.dal.bean.grouping;

import lombok.Data;

import java.util.Date;

/**
 * The table.
 * 用户拥有菜单的角色权限-只有企业空间下的用户才记录，个人空间无需记录
 * <AUTHOR> Kunpeng
 */
@Data
public class UserMenuPermissionDO{

    /**
     * createTime CREATE_TIME.
     */
    private Date createTime;
    /**
     * modifyTime MODIFY_TIME.
     */
    private Date modifyTime;
    /**
     * id ID.
     */
    private Long id;
    /**
     * gid 当前操作人gid.
     */
    private String gid;
    /**
     * oid 当前操作人oid.
     */
    private String oid;
    /**
     * name 姓名.
     */
    private String name;
    /**
     * email 邮箱地址.
     */
    private String email;
    /**
     * menuId 菜单ID，当只有菜单权限时，从ES里面直接搜索该菜单ID，否则查对应菜单下的具体文件ID.
     */
    private String menuId;
    /**
     * roleId 角色ID.
     */
    private String roleId;
    /**
     * mobileNo 手机号码.
     */
    private String mobileNo;

    /**被授权人，可以为用户gid或者部门id*/
    private String authorizer;

    /**授权类型 1-个人；2-部门*/
    private Integer authorizeType;

    private String tenantOid;

    private String tenantGid;
}

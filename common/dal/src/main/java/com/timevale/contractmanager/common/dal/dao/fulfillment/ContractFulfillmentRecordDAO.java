package com.timevale.contractmanager.common.dal.dao.fulfillment;

import com.timevale.contractmanager.common.dal.bean.fulfillment.ContractFulfillmentRecordDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * ContractFulfillmentRecordDAO
 *
 * <AUTHOR>
 * @since 2023/10/11 11:10 上午
 */
public interface ContractFulfillmentRecordDAO {

    @Insert({
            "<script>",
            "INSERT INTO contract_fulfillment_record (tenant_oid,tenant_gid,record_id,title,",
            "process_id,rule_id,notice_time,complete_time,type,type_name,notice_oid,notice_gid,status) values",
            "<foreach collection='list' item='t' index='index' separator=','>",
            "(#{t.tenantOid},#{t.tenantGid},#{t.recordId},#{t.title},#{t.processId},#{t.ruleId},",
            "#{t.noticeTime},#{t.completeTime},#{t.type},#{t.typeName},#{t.noticeOid},#{t.noticeGid},#{t.status})",
            "</foreach>",
            "</script>"
    })
    void batchInsert(@Param("list") List<ContractFulfillmentRecordDO> list);

    @Update({
            "<script>",
            "<foreach collection='list' item='t'>",
            "UPDATE contract_fulfillment_record SET type = #{t.type},type_name = #{t.typeName},notice_oid = #{t.noticeOid},notice_gid = #{t.noticeGid}",
            "where record_id = #{t.recordId};",
            "</foreach>",
            "</script>"
    })
    int batchUpdate(@Param("list") List<ContractFulfillmentRecordDO> params);

    @Delete({
            "<script>",
            "delete from contract_fulfillment_record where record_id in ",
            "<foreach collection='recordIds' item='recordId' index='index' open='(' separator=',' close=')'>",
            "#{recordId}",
            "</foreach>",
            "</script>"
    })
    void batchDelete(@Param("recordIds") List<String> recordIds);

    @Update({
            "<script>",
            "update contract_fulfillment_record set status = #{status}, complete_time = now() where tenant_gid = #{tenantGid} and record_id=#{recordId}",
            "</script>"
    })
    void updateStatus(@Param("tenantGid") String tenantGid, @Param("recordId") String recordId, @Param("status") String status);

    @Update({
            "<script>",
            "update contract_fulfillment_record set status = #{status} where tenant_gid = #{tenantGid} and record_id in ",
            "<foreach collection='recordIds' item='recordId' index='index' open='(' separator=',' close=')'>",
                "#{recordId}",
            "</foreach>",
            "</script>"
    })
    void batchUpdateStatus(@Param("tenantGid") String tenantGid, @Param("recordIds") List<String> recordIds, @Param("status") String status);

    @Select({
            "<script>",
            "select * from contract_fulfillment_record where status = #{status} and process_id in ",
            "<foreach collection='processIds' item='processId' index='index' open='(' separator=',' close=')'>",
                "#{processId}",
            "</foreach>",
            "</script>"
    })
    List<ContractFulfillmentRecordDO> list(@Param("processIds") List<String> processIds, @Param("status") String status);
}

package com.timevale.contractmanager.common.dal.bean;

import java.util.Date;

/**
 * The table.
 * 合同管理主流程与子流程关系
 * <AUTHOR> Kunpeng
 */
public class SubProcessDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * processId 主流程id.
     */
    private String processId;
    /**
     * subProcessId 子流程id.
     */
    private String subProcessId;
    /**
     * subProcessType 子流程类型 1-填写 2-签署.
     */
    private Integer subProcessType;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set processId 主流程id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 主流程id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    /**
     * Set subProcessId 子流程id.
     */
    public void setSubProcessId(String subProcessId){
        this.subProcessId = subProcessId;
    }

    /**
     * Get subProcessId 子流程id.
     *
     * @return the string
     */
    public String getSubProcessId(){
        return subProcessId;
    }

    /**
     * Set subProcessType 子流程类型 1-填写 2-签署.
     */
    public void setSubProcessType(Integer subProcessType){
        this.subProcessType = subProcessType;
    }

    /**
     * Get subProcessType 子流程类型 1-填写 2-签署.
     *
     * @return the string
     */
    public Integer getSubProcessType(){
        return subProcessType;
    }
}

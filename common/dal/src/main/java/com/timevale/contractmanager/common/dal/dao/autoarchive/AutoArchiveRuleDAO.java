package com.timevale.contractmanager.common.dal.dao.autoarchive;

import com.timevale.contractmanager.common.dal.bean.autoarchive.AutoArchiveRuleDO;
import com.timevale.contractmanager.common.dal.bean.grouping.AiRuleDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-05-06 19:29
 */
public interface AutoArchiveRuleDAO {

	@Insert({"INSERT INTO auto_archive_rule (tenant_gid,rule_status,binding_menu_id,uuid,create_by_oid,modify_by_oid)" +
	"VALUES(#{tenantGid},#{ruleStatus},#{bindingMenuId},#{uuid},#{createByOid},#{modifyByOid})"})
	public Integer insert(AutoArchiveRuleDO archiveRuleDO);

	@Select("select * from auto_archive_rule where uuid = #{ruleId} and deleted = 0")
	public AutoArchiveRuleDO queryByRuleId(String ruleId);

	@Select("select * from auto_archive_rule where tenant_gid=#{tenantGid} and uuid = #{ruleId} and deleted = 0")
	AutoArchiveRuleDO queryByRuleIdAndTenantGid(@Param("ruleId") String ruleId, @Param("tenantGid") String tenantGid);

	@Update("update auto_archive_rule set rule_status = #{ruleStatus} where uuid=#{ruleId}")
	public Integer updateStatus(@Param("ruleStatus") Integer ruleStatus,@Param("ruleId") String ruleId);

	@Update("update auto_archive_rule set top_time = #{topTime} where uuid=#{ruleId}")
	public Integer updateTopTime(@Param("topTime") Date topTime,@Param("ruleId") String ruleId);

	@Update("update auto_archive_rule set binding_menu_id = #{bindingMenuId}, rule_status = #{ruleStatus} , revome_contract = #{revomeContract}, modify_by_oid= #{modifyByOid} where uuid=#{uuid}")
	public int update(AutoArchiveRuleDO ruleDO);

	@Update("update auto_archive_rule set deleted = 1 where uuid=#{ruleId}")
	public Integer updateDeleteStatus(@Param("ruleId") String ruleId);

	@Select("select * from auto_archive_rule where tenant_gid=#{tenantGid} and deleted = 0")
	public List<AutoArchiveRuleDO> queryByTenantGid(String tenantGid);

	@Select("select * from auto_archive_rule where tenant_gid=#{tenantGid}")
	List<AutoArchiveRuleDO> queryByTenantGidIgnoreDeleted(String tenantGid);

	@Select({
			"<script>",
			" select * from auto_archive_rule where tenant_gid=#{tenantGid}",
			" and rule_status in",
			" <foreach collection='status' item='status' open='(' separator=',' close=')'>",
			" #{status}",
			" </foreach> and deleted = 0",
			"</script>"
	})
	public List<AutoArchiveRuleDO> queryByTenantGidAndStatus(@Param("tenantGid") String tenantGid, @Param("status") List<Integer> status);

	@Select("select * from auto_archive_rule where tenant_gid=#{tenantGid} and binding_menu_id = #{bindingMenuId} and deleted = 0")
	public AutoArchiveRuleDO queryByMenuId(@Param("tenantGid") String tenantGid,@Param("bindingMenuId") String bindingMenuId);

	@Update("update  auto_archive_rule set auto_archive_num = auto_archive_num+#{archiveNum} where id = #{id}")
	public void updateArchiveNum(@Param("id")long id, @Param("archiveNum") long archiveNum);

	@Update("update auto_archive_rule set auto_archive_num = auto_archive_num + #{archiveNum} where uuid = #{ruleId}")
	public void addArchiveNum(@Param("ruleId") String ruleId, @Param("archiveNum") long archiveNum);

	@Select("select * from auto_archive_rule where tenant_gid=#{tenantGid} and top_time != '' and deleted = 0")
	public List<AutoArchiveRuleDO> queryTopTimeList(@Param("tenantGid") String tenantGid);

}

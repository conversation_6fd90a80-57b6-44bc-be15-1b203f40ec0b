package com.timevale.contractmanager.common.dal.dao.opponententity.detection;

import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionTaskDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-08-12 14:45
 */
public interface OpponentDetectionTaskDAO {

	@Insert({"INSERT INTO opponent_detection_task (tenant_gid, detection_task_id, detection_totality, " +
			"detection_quantity_completion, task_status,task_type, detection_scope, business_scope, create_by_oid) " +
			"VALUES(#{tenantGid},#{detectionTaskId}, #{detectionTotality}, #{detectionQuantityCompletion}, #{taskStatus}," +
			"#{taskType}, #{detectionScope}, #{businessScope}, #{createByOid})"})
	int insert(OpponentDetectionTaskDO taskDO);
	/**
	 * 获取运行中最新的任务信息
	 * @param tenantGid
	 * @param taskType
	 * @return
	 */
	@Select("select * from opponent_detection_task where tenant_gid = #{tenantGid}" +
			"and task_type = #{taskType} order by create_time desc limit 1")
	OpponentDetectionTaskDO getByTaskLast(@Param("tenantGid") String tenantGid, @Param("taskType") Integer taskType);


	@Select("select * from opponent_detection_task where tenant_gid = #{tenantGid} and task_status = #{taskStatus} and task_type = #{taskType}")
	OpponentDetectionTaskDO getByTaskStatus(@Param("tenantGid") String tenantGid,
									 @Param("taskStatus") Integer taskStatus,
									 @Param("taskType") Integer taskType);


	@Select({"<script>",
			"select * from opponent_detection_task where",
			"tenant_gid = #{tenantGid}",
			"<if test='taskType!=null'> and task_type = #{taskType}</if>",
			"<if test='startTime!=null'> and create_time &gt;= #{startTime}</if>",
			"<if test='endTime!=null'> and create_time &lt;= #{endTime}</if>",
			"order by create_time desc limit #{offset},#{pageSize}",
			"</script>"
	})
	List<OpponentDetectionTaskDO> getTaskList(@Param("tenantGid") String tenantGid,
											  @Param("taskType") Integer taskType,
											  @Param("pageSize") Integer pageSize,
											  @Param("offset") Integer offset,
											  @Param("startTime") Date startTime,
											  @Param("endTime") Date endTime);

	@Select({"<script>",
			"select count(tenant_gid) from opponent_detection_task where",
			"tenant_gid = #{tenantGid}",
			"<if test='taskType!=null'> and task_type = #{taskType}</if>",
			"<if test='startTime!=null'> and create_time &gt;= #{startTime}</if>",
			"<if test='endTime!=null'> and create_time &lt;= #{endTime}</if>",
			"</script>"
	})
	long getStatistics(@Param("tenantGid") String tenantGid, @Param("taskType") Integer taskType,
					   @Param("startTime") Date startTime, @Param("endTime") Date endTime);


	@Select("select * from opponent_detection_task where tenant_gid = #{tenantGid} and detection_task_id = #{detectionTaskId}")
	OpponentDetectionTaskDO getTaskByTaskId(@Param("tenantGid") String tenantGid, @Param("detectionTaskId") String detectionTaskId);

	@Update({"<script>",
			"update  opponent_detection_task",
			"<set>",
			"<if test = \"reportProblemNum != null \"> report_problem_num = #{reportProblemNum},</if>",
			"<if test = \"detectionTotality != null \"> detection_totality = #{detectionTotality},</if>",
			"<if test = \"detectionQuantityCompletion != null \"> detection_quantity_completion = #{detectionQuantityCompletion},</if>",
			"<if test = \"elapsedTime != null \"> elapsed_time = #{elapsedTime},</if>",
			"<if test = \"taskStatus != null \"> task_status = #{taskStatus},</if>",
			"<if test = \"ventureBusinessNum != null \"> venture_business_num = #{ventureBusinessNum},</if>",
			"</set>",
			"where id = #{id}",
			"</script>"
	})
	int update(OpponentDetectionTaskDO taskDO);

	@Select("select * from opponent_detection_task where task_status = #{taskStatus}")
	List<OpponentDetectionTaskDO> getTaskListByType(@Param("taskStatus") Integer taskStatus);

}

package com.timevale.contractmanager.common.dal.dao.rule;

import com.timevale.contractmanager.common.dal.bean.autoarchive.AutoArchiveOperatorConfigDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface RuleConditionSysConfigDAO {

	@Select("select * from rule_condition_sys_config")
	List<AutoArchiveOperatorConfigDO> queryList();

	@Select("select * from rule_condition_sys_config where biz_type = #{bizType}")
	List<AutoArchiveOperatorConfigDO> queryListByType(@Param("bizType") Integer bizType);
}

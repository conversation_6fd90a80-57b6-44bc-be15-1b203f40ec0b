package com.timevale.contractmanager.common.dal.bean.sharesign;

import java.util.Date;

/**
 * The table.
 * 分享签任务表
 * <AUTHOR> Kunpeng
 */
public class ShareSignTaskDO{

    /**
     * endTime 任务截止时间.
     */
    private Date endTime;
    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * taskId 分享签署任务id.
     */
    private String taskId;
    /**
     * taskName 分享签署任务名称.
     */
    private String taskName;
    /**
     * taskFrom 分享签署任务发起方式.
     */
    private Integer taskFrom;
    /**
     * accountGid 任务发起人gid.
     */
    private String accountGid;
    /**
     * accountOid 任务发起人oid.
     */
    private String accountOid;
    /**
     * shareBizId 分享类型为批量扫码签时，share_biz_id和批量合同列表里的groupId维持一致, 单个扫码签时，share_biz_id为processId.
     */
    private String shareBizId;
    /**
     * subjectGid 任务发起主体gid.
     */
    private String subjectGid;
    /**
     * subjectOid 任务发起主体oid.
     */
    private String subjectOid;
    /**
     * accountName 任务发起人姓名.
     */
    private String accountName;
    /**
     * subjectName 任务发起主体名称.
     */
    private String subjectName;
    /**
     * shareTemplateId 分享任务流程模板id.
     */
    private String shareTemplateId;

    /**
     * shareTemplateId 分享任务流程模板id.
     */
    private String originTemplateId;
    /**
     * status 状态 0-不可用 1-可用 默认1.
     */
    private Integer status;
    /**
     * shareNum 任务已完成总数.
     */
    private Integer shareDone;
    /**
     * shareNum 任务已参与总数.
     */
    private Integer shareNum;
    /**
     * shareType 分享类型，1-批量扫码签，2-单个扫码签.
     */
    private Integer shareType;
    /**
     * shareTotal 任务参与总数限制， 为空表示不限.
     */
    private Integer shareTotal;
    /**
     * shareNumOccupied 任务名额占用数.
     */
    private Integer shareNumOccupied;
    /**
     * version 任务版本，为空时默认1.0.
     */
    private Integer version;

    /** 是否私密分享任务，0.否 1.是 */
    private Boolean privateShare;

    /** 业务扩展信息 */
    private String bizExtra;

    /**
     * Set endTime 任务截止时间.
     */
    public void setEndTime(Date endTime){
        this.endTime = endTime;
    }

    /**
     * Get endTime 任务截止时间.
     *
     * @return the string
     */
    public Date getEndTime(){
        return endTime;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set taskId 分享签署任务id.
     */
    public void setTaskId(String taskId){
        this.taskId = taskId;
    }

    /**
     * Get taskId 分享签署任务id.
     *
     * @return the string
     */
    public String getTaskId(){
        return taskId;
    }

    /**
     * Set taskName 分享签署任务名称.
     */
    public void setTaskName(String taskName){
        this.taskName = taskName;
    }

    /**
     * Get taskName 分享签署任务名称.
     *
     * @return the string
     */
    public String getTaskName(){
        return taskName;
    }

    /**
     * Set taskFrom 分享签署任务发起方式.
     */
    public void setTaskFrom(Integer taskFrom){
        this.taskFrom = taskFrom;
    }

    /**
     * Get taskFrom 分享签署任务发起方式.
     *
     * @return the string
     */
    public Integer getTaskFrom(){
        return taskFrom;
    }

    /**
     * Set accountGid 任务发起人gid.
     */
    public void setAccountGid(String accountGid){
        this.accountGid = accountGid;
    }

    /**
     * Get accountGid 任务发起人gid.
     *
     * @return the string
     */
    public String getAccountGid(){
        return accountGid;
    }

    /**
     * Set accountOid 任务发起人oid.
     */
    public void setAccountOid(String accountOid){
        this.accountOid = accountOid;
    }

    /**
     * Get accountOid 任务发起人oid.
     *
     * @return the string
     */
    public String getAccountOid(){
        return accountOid;
    }

    /**
     * Set shareBizId 分享类型为批量扫码签时，share_biz_id和批量合同列表里的groupId维持一致, 单个扫码签时，share_biz_id为processId.
     */
    public void setShareBizId(String shareBizId){
        this.shareBizId = shareBizId;
    }

    /**
     * Get shareBizId 分享类型为批量扫码签时，share_biz_id和批量合同列表里的groupId维持一致, 单个扫码签时，share_biz_id为processId.
     *
     * @return the string
     */
    public String getShareBizId(){
        return shareBizId;
    }

    /**
     * Set subjectGid 任务发起主体gid.
     */
    public void setSubjectGid(String subjectGid){
        this.subjectGid = subjectGid;
    }

    /**
     * Get subjectGid 任务发起主体gid.
     *
     * @return the string
     */
    public String getSubjectGid(){
        return subjectGid;
    }

    /**
     * Set subjectOid 任务发起主体oid.
     */
    public void setSubjectOid(String subjectOid){
        this.subjectOid = subjectOid;
    }

    /**
     * Get subjectOid 任务发起主体oid.
     *
     * @return the string
     */
    public String getSubjectOid(){
        return subjectOid;
    }

    /**
     * Set accountName 任务发起人姓名.
     */
    public void setAccountName(String accountName){
        this.accountName = accountName;
    }

    /**
     * Get accountName 任务发起人姓名.
     *
     * @return the string
     */
    public String getAccountName(){
        return accountName;
    }

    /**
     * Set subjectName 任务发起主体名称.
     */
    public void setSubjectName(String subjectName){
        this.subjectName = subjectName;
    }

    /**
     * Get subjectName 任务发起主体名称.
     *
     * @return the string
     */
    public String getSubjectName(){
        return subjectName;
    }

    /**
     * Set shareTemplateId 分享任务流程模板id.
     */
    public void setShareTemplateId(String shareTemplateId){
        this.shareTemplateId = shareTemplateId;
    }

    /**
     * Get shareTemplateId 分享任务流程模板id.
     *
     * @return the string
     */
    public String getShareTemplateId(){
        return shareTemplateId;
    }

    public String getOriginTemplateId() {
        return originTemplateId;
    }

    public void setOriginTemplateId(String originTemplateId) {
        this.originTemplateId = originTemplateId;
    }

    /**
     * Set status 状态 0-不可用 1-可用 默认1.
     */
    public void setStatus(Integer status){
        this.status = status;
    }

    /**
     * Get status 状态 0-不可用 1-可用 默认1.
     *
     * @return the string
     */
    public Integer getStatus(){
        return status;
    }

    /**
     * Set shareDone 任务已完成总数.
     */
    public void setShareDone(Integer shareDone){
        this.shareDone = shareDone;
    }

    /**
     * Get shareDone 任务已完成总数.
     *
     * @return the string
     */
    public Integer getShareDone(){
        return shareDone;
    }

    /**
     * Set shareNum 任务已参与总数.
     */
    public void setShareNum(Integer shareNum){
        this.shareNum = shareNum;
    }

    /**
     * Get shareNum 任务已参与总数.
     *
     * @return the string
     */
    public Integer getShareNum(){
        return shareNum;
    }

    /**
     * Set shareType 分享类型，1-批量扫码签，2-单个扫码签.
     */
    public void setShareType(Integer shareType){
        this.shareType = shareType;
    }

    /**
     * Get shareType 分享类型，1-批量扫码签，2-单个扫码签.
     *
     * @return the string
     */
    public Integer getShareType(){
        return shareType;
    }

    /**
     * Set shareTotal 任务参与总数限制， 为空表示不限.
     */
    public void setShareTotal(Integer shareTotal){
        this.shareTotal = shareTotal;
    }

    /**
     * Get shareTotal 任务参与总数限制， 为空表示不限.
     *
     * @return the string
     */
    public Integer getShareTotal(){
        return shareTotal;
    }

    /**
     * Set shareNumOccupied 任务名额占用数.
     */
    public void setShareNumOccupied(Integer shareNumOccupied){
        this.shareNumOccupied = shareNumOccupied;
    }

    /**
     * Get shareNumOccupied 任务名额占用数.
     *
     * @return the string
     */
    public Integer getShareNumOccupied(){
        return shareNumOccupied;
    }

    /**
     * Set version 任务版本，为空时默认1.0.
     */
    public void setVersion(Integer version){
        this.version = version;
    }

    /**
     * Get version 任务版本，为空时默认1.0.
     *
     * @return the string
     */
    public Integer getVersion(){
        return version;
    }

    public Boolean getPrivateShare() {
        return privateShare;
    }

    public void setPrivateShare(Boolean privateShare) {
        this.privateShare = privateShare;
    }

    public String getBizExtra() {
        return bizExtra;
    }

    public void setBizExtra(String bizExtra) {
        this.bizExtra = bizExtra;
    }
}

package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ContractFileBizRelationDO;
import com.timevale.contractmanager.common.dal.query.ContractBizRelationQuery;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-10-26 17:32
 */
public interface ContractFileBizRelationDAO {

    String BASE_FIELD =
            "process_id, file_id, file_name, origin_file_id, contract_no, contract_no_type, contract_no_rule";

    @Insert({
        "<script>",
        " INSERT INTO contract_file_biz_relation(" + BASE_FIELD + ", deleted, create_time, update_time)",
        " VALUES ",
        " <foreach collection='list' item='item' separator=','>",
        "   (#{item.processId},",
        "   #{item.fileId},",
        "   #{item.fileName},",
        "   #{item.originFileId},",
        "   #{item.contractNo},",
        "   #{item.contractNoType},",
        "   #{item.contractNoRule},",
        "   0, now(), now())",
        " </foreach>",
        "</script>"
    })
    Integer insertBatch(List<ContractFileBizRelationDO> list);

    @Update({
        "<script>",
        " UPDATE contract_file_biz_relation ",
        " <set>",
        "   <if test=\"deleted != null \">deleted = #{deleted}, </if>",
        "   update_time = now() ",
        " </set>",
        " WHERE ",
        "   process_id = #{processId}",
        "</script>"
    })
    Integer updateByProcessId(ContractFileBizRelationDO update);

    @Select({
        "<script>",
        " SELECT id, deleted," + BASE_FIELD,
        " FROM contract_file_biz_relation ",
        " <where>",
        "   process_id = #{processId} ",
        "   <if test=\"deleted != null\"> AND deleted = #{deleted} </if>",
        "   <if test=\"fileIds != null and fileIds.size() > 0\">",
        "       AND file_id IN <foreach collection='fileIds' item='fileId' open='(' separator=',' close=')'> #{fileId} </foreach> </if>",
        " </where>",
        "</script>"
    })
    List<ContractFileBizRelationDO> selectByCondition(ContractBizRelationQuery condition);
}

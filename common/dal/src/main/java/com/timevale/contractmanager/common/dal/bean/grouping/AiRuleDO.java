package com.timevale.contractmanager.common.dal.bean.grouping;

import java.util.Date;

/**
 * The table.
 * ai台账规则信息表-以空间维度记录多少个台账规则
 * <AUTHOR> Kunpeng
 */
public class AiRuleDO{

    /**
     * createTime CREATE_TIME.
     */
    private Date createTime;
    /**
     * updateTime UPDATE_TIME.
     */
    private Date updateTime;
    /**
     * id ID.
     */
    private Long id;
    /**
     * gid 空间gid.
     */
    private String gid;
    /**
     * oid 空间oid.
     */
    private String oid;
    /**
     *
     */
    private String fileKey;
    /**
     * ruleId 规则id.
     */
    private String ruleId;
    /**
     * type 合同类型，1-默认.
     */
    private Integer type;
    /**
     * fileNo 文件编号，后期用于支持多文件规则，目前只有一个文件默认1.
     */
    private Integer fileNo;
    /**
     * status 状态，0：待运行，1：运行中.
     */
    private Integer status;
    /**
     * enabled 是否启用，0：否，1：是.
     */
    private Integer enabled;

    /**
     * ruleId 规则id.
     */
    private String newRuleId;

    private String name;

    private String remark;

    private Integer version;

    private String invalidDesc;

    public String getInvalidDesc() {
        return invalidDesc;
    }

    public void setInvalidDesc(String invalidDesc) {
        this.invalidDesc = invalidDesc;
    }

    /**
     * Set createTime CREATE_TIME.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime CREATE_TIME.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime UPDATE_TIME.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime UPDATE_TIME.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set gid 空间gid.
     */
    public void setGid(String gid){
        this.gid = gid;
    }

    /**
     * Get gid 空间gid.
     *
     * @return the string
     */
    public String getGid(){
        return gid;
    }

    /**
     * Set oid 空间oid.
     */
    public void setOid(String oid){
        this.oid = oid;
    }

    /**
     * Get oid 空间oid.
     *
     * @return the string
     */
    public String getOid(){
        return oid;
    }

    public String getFileKey() {
        return fileKey;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    /**
     * Set ruleId 规则id.
     */
    public void setRuleId(String ruleId){
        this.ruleId = ruleId;
    }

    /**
     * Get ruleId 规则id.
     *
     * @return the string
     */
    public String getRuleId(){
        return ruleId;
    }

    /**
     * Set type 合同类型，1-默认.
     */
    public void setType(Integer type){
        this.type = type;
    }

    /**
     * Get type 合同类型，1-默认.
     *
     * @return the string
     */
    public Integer getType(){
        return type;
    }

    /**
     * Set fileNo 文件编号，后期用于支持多文件规则，目前只有一个文件默认1.
     */
    public void setFileNo(Integer fileNo){
        this.fileNo = fileNo;
    }

    /**
     * Get fileNo 文件编号，后期用于支持多文件规则，目前只有一个文件默认1.
     *
     * @return the string
     */
    public Integer getFileNo(){
        return fileNo;
    }

    /**
     * Set status 状态，0：待运行，1：运行中.
     */
    public void setStatus(Integer status){
        this.status = status;
    }

    /**
     * Get status 状态，0：待运行，1：运行中.
     *
     * @return the string
     */
    public Integer getStatus(){
        return status;
    }

    /**
     * Set enabled 是否启用，0：否，1：是.
     */
    public void setEnabled(Integer enabled){
        this.enabled = enabled;
    }

    /**
     * Get enabled 是否启用，0：否，1：是.
     *
     * @return the string
     */
    public Integer getEnabled(){
        return enabled;
    }

    public String getNewRuleId() {
        return newRuleId;
    }

    public void setNewRuleId(String newRuleId) {
        this.newRuleId = newRuleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}

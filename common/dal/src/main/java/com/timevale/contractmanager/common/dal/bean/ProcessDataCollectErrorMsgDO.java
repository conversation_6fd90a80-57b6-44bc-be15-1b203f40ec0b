package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

@Data
public class ProcessDataCollectErrorMsgDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_data_collect_error_msg.id
     *
     * @mbg.generated Fri Jun 10 18:12:51 CST 2022
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_data_collect_error_msg.create_time
     *
     * @mbg.generated Fri Jun 10 18:12:51 CST 2022
     */
    private Date createTime;

    private String processId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_data_collect_error_msg.topic
     *
     * @mbg.generated Fri Jun 10 18:12:51 CST 2022
     */
    private String topic;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_data_collect_error_msg.tag
     *
     * @mbg.generated Fri Jun 10 18:12:51 CST 2022
     */
    private String tag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_data_collect_error_msg.remark
     *
     * @mbg.generated Fri Jun 10 18:12:51 CST 2022
     */
    private String remark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_data_collect_error_msg.content
     *
     * @mbg.generated Fri Jun 10 18:12:51 CST 2022
     */
    private String content;

}
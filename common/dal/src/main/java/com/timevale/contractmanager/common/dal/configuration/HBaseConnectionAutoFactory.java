package com.timevale.contractmanager.common.dal.configuration;

import org.apache.hadoop.hbase.client.Connection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;

@Component
public class HBaseConnectionAutoFactory {

    private static HBaseConnectionFactory hBaseConnectionFactory;

    @Autowired
    private HBaseConfig hBaseConfig;

    @PostConstruct
    public void init() {
        hBaseConnectionFactory =
                new HBaseConnectionFactory(
                        hBaseConfig.getHBASE_ZK_QUORUM(),
                        hBaseConfig.getHBASE_USERNAME(),
                        hBaseConfig.getHBASE_PASSWORD());
    }

    public static Connection getConnection() throws IOException {
        return hBaseConnectionFactory.getConnection();
    }
}

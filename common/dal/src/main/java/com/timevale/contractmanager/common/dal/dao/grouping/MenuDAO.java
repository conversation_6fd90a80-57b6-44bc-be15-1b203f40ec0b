package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.MenuDO;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

/**
 * The Table menu.
 * 菜单记录表（企业空间+个人空间）
 * <AUTHOR> Kunpeng
 */
public interface MenuDAO{

    /**
     * desc:插入表:menu.<br/>
     * @param entity entity
     * @return int
     */
    @Insert({
            "<script>",
            "INSERT INTO menu (binding_form_id,GID, OID, NAME, MENU_ID , PARENT_ID, `ORDER`, `show_child`, `unity_form`, `path`) VALUES ",
            "(#{bindingFormId,jdbcType=VARCHAR},#{gid,jdbcType=VARCHAR}, #{oid,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},",
            "#{menuId,jdbcType=VARCHAR} , #{parentId,jdbcType=VARCHAR}, #{order,jdbcType=INTEGER}, #{showChild}, #{unityForm}, #{path})",
            "</script>"
    })
    public int insert(MenuDO entity);

    @Update({
            "UPDATE menu SET NAME = #{name,jdbcType=VARCHAR}, update_time = now() ",
            "WHERE MENU_ID =  #{menuId,jdbcType=VARCHAR}"
    })
    public int updateMenuName(MenuDO entity);


    @Update({
            "<script>",
            "UPDATE menu SET binding_form_id = #{bindingFormId,jdbcType=VARCHAR}",
            "<if test='unityForm!=null'>,unity_form = #{unityForm}</if>",
            "WHERE MENU_ID =  #{menuId,jdbcType=VARCHAR}",
            "</script>"
    })
    public Integer updateForm(MenuDO entity);

    @Update({
            "UPDATE menu SET `ORDER` = #{order,jdbcType=INTEGER}, update_time = now() ",
            "WHERE ID = #{id,jdbcType=BIGINT}"
    })
    public int updateMenuOrderById(MenuDO entity);

    @Update({
          "<script>",
          "UPDATE menu",
          "<set>",
          "<if test='order!=null'> `ORDER` = #{order,jdbcType=INTEGER} ,</if>",
          "<if test='parentId!=null'> PARENT_ID = #{parentId,jdbcType=VARCHAR}, path=#{path}</if>",
          "<if test='unityForm!=null'> unity_form = #{unityForm},</if>",
            "<if test='bindingFormId!=null'>binding_form_id = #{bindingFormId,jdbcType=VARCHAR},</if>",
          "update_time = now()",
          "</set>",
          "WHERE MENU_ID =  #{menuId,jdbcType=VARCHAR}",
          "</script>"
     })
    public int updateMenuNotNull(MenuDO entity);

    @Update({
            "<script>",
            "<foreach collection='list' item='t' separator=';'>",
            "UPDATE menu",
            "<set>",
            "<if test='t.order!=null'> `ORDER` = #{t.order,jdbcType=INTEGER} ,</if>",
            "<if test='t.parentId!=null'> PARENT_ID = #{t.parentId,jdbcType=VARCHAR}, path=#{t.path},</if>",
            "<if test='t.unityForm!=null'> unity_form = #{t.unityForm},</if>",
            "<if test='t.bindingFormId!=null'>binding_form_id = #{t.bindingFormId,jdbcType=VARCHAR},</if>",
            "update_time = now()",
            "</set>",
            "WHERE MENU_ID =  #{t.menuId,jdbcType=VARCHAR}",
            "</foreach>",
            "</script>"
    })
    public int batchUpdateMenuNotNull(@Param("list") List<MenuDO> menuDOS);

    @Update({
            "<script>",
            "UPDATE menu SET DELETED = #{deleted,jdbcType=TINYINT} ,update_time = now() ",
            "WHERE ID IN",
            "(<foreach collection='list' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    public int deleteByIds(@Param("list") List<Long> list,@Param("deleted") Integer deleted);

    @Delete("DELETE FROM menu WHERE MENU_ID = #{menuId,jdbcType=VARCHAR}")
    public int deleteByMenuId(String menuId);

    @Select("SELECT * FROM menu WHERE MENU_ID = #{menuId,jdbcType=VARCHAR}")
    public MenuDO getByMenuId(String menuId);

    @Select("SELECT * FROM menu WHERE id = #{id}")
    MenuDO getById(Long id);

    @Select({
            "<script>",
            "SELECT * FROM menu WHERE id in",
            "<foreach collection='list' item='t' open='(' separator=',' close=')'>#{t}</foreach>",
            "</script>"
    })
    List<MenuDO> listByIds(@Param("list") List<Long> ids);

    @Select(
            "SELECT * FROM menu WHERE OID = #{oid,jdbcType=VARCHAR} AND PARENT_ID = #{parentId,jdbcType=VARCHAR} AND NAME = #{name,jdbcType=VARCHAR} AND DELETED = #{deleted,jdbcType=TINYINT} limit 1")
    public MenuDO getByOidAndName(
            @Param("oid") String oid,
            @Param("parentId") String parentId,
            @Param("name") String name,
            @Param("deleted") Integer deleted);

    /**
     * desc:根据普通索引IdxOid获取数据:menu.<br/>
     * @param oid oid
     * @return List<MenuDO>
     */
    @Select("SELECT * FROM menu WHERE OID = #{oid,jdbcType=VARCHAR} AND DELETED = #{deleted,jdbcType=TINYINT}")
    public List<MenuDO> queryByIdxOid(@Param("oid") String oid, @Param("deleted") Integer deleted);

    /**
     * desc:根据普通索引IdxOid获取数据:menu.<br/>
     * @param oid oid
     * @return List<MenuDO>
     */
    @Select("SELECT MENU_ID FROM menu WHERE OID = #{oid,jdbcType=VARCHAR} LIMIT #{offset},#{limit}")
    public List<String> queryByIdxOidLimit(@Param("oid") String oid,@Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("SELECT * FROM menu WHERE PARENT_ID = #{parentId,jdbcType=VARCHAR}")
    public List<MenuDO> queryByParentId(String parentId);

    @Select("SELECT MENU_ID FROM menu WHERE PARENT_ID = #{parentId,jdbcType=VARCHAR}")
    public List<String> queryByParentIdString(String parentId);

    @Select("SELECT MENU_ID FROM menu WHERE DELETED = #{deleted,jdbcType=TINYINT} limit 100")
    public List<String> queryDeletedMenuModList(Integer deleted);

    @Select({
            "<script>",
            "SELECT * FROM menu WHERE PARENT_ID IN ",
            "(<foreach collection='list' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    public List<MenuDO> queryByParentIds(List<String> list);

    @Select("SELECT max(`ORDER`) FROM menu WHERE PARENT_ID =  #{parentId,jdbcType=VARCHAR}")
    public Integer getOrderByParentId(String parentId);

    @Select("SELECT max(`ORDER`) FROM menu WHERE PARENT_ID =  #{parentId} and oid = #{oid}")
    Integer getMaxOrderByParentId(@Param("oid") String oid, @Param("parentId") String parentId);

    @Select({
            "<script>",
            "SELECT GID, OID, NAME, MENU_ID , PARENT_ID, `ORDER`, PATH FROM menu WHERE MENU_ID IN ",
            "(<foreach collection='list' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    public List<MenuDO> queryByMenuIds(@Param("list") Collection<String> list);

    @Update({
            "update menu set show_child=#{showChild} where menu_id = #{menuId}"
    })
    int updateShowChild(@Param("menuId") String menuId, @Param("showChild") Integer showChild);

    @Select({
            "select * from menu where OID = #{oid,jdbcType=VARCHAR} and path like concat(#{path}, '%')"
    })
    List<MenuDO> listAllChildrenByPath(@Param("oid") String oid, @Param("path") String path);

    @Update({
            "UPDATE menu SET binding_form_id = null, binding_form_name = null, unity_form = 0 ",
            "WHERE id = #{id,jdbcType=BIGINT}"
    })
    void unbindMenu(@Param("id") Long id);

    /**
     * 依据父分类id获取一级子分类列表
     *
     * @param parentId
     * @return
     */
    @Select("select * from menu where oid=#{subjectOid} and parent_id=#{parentId} and deleted=0")
    List<MenuDO> listTopChildMenuByParentId(@Param("parentId") String parentId, @Param("subjectOid") String subjectOid);

    @Select("select count(*) from menu where oid=#{subjectOid} and parent_id=#{parentId} and deleted=0")
    int countChildMenuByParentId(@Param("parentId") String parentId, @Param("subjectOid") String subjectOid);

    @Update({
            "<script>",
            "<foreach collection='menuDOS' item='item' separator=';'>",
            "update menu set `order`=#{item.order}, update_time=now() where id=#{item.id} and oid=#{subjectOid}",
            "</foreach>",
            "</script>"
    })
    int batchUpdateMenuOrderById(@Param("menuDOS") List<MenuDO> menuDOS, @Param("subjectOid") String subjectOid);

    @Select({
            "<script>",
            "SELECT MENU_ID FROM menu WHERE OID = #{oid,jdbcType=VARCHAR} and menu_id in",
            "<foreach collection='list' item='t' open='(' separator=',' close=')'>#{t}</foreach>",
            "AND DELETED = #{deleted,jdbcType=TINYINT}",
            "</script>"
    })
    List<String> listMenuIdByOidAndMenuIdList(@Param("oid") String oid, @Param("list") List<String> menuIdList,  @Param("deleted") Integer deleted);
}

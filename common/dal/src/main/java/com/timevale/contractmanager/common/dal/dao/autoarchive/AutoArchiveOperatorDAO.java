package com.timevale.contractmanager.common.dal.dao.autoarchive;

import com.timevale.contractmanager.common.dal.bean.autoarchive.AutoArchiveOperatorDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-05-06 19:30
 */
public interface AutoArchiveOperatorDAO {

	@Insert({"INSERT INTO auto_archive_operator (field_type,archive_rule_id,operator_id,parent_operator_id," +
	"condition_params,operator_type,field_name,field_id,operator_weight,match_type)" +
	"VALUES(#{fieldType},#{archiveRuleId},#{operatorId},#{parentOperatorId},#{conditionParams},#{operatorType}," +
	"#{fieldName},#{fieldId},#{operatorWeight},#{matchType})"})
	public void insert(AutoArchiveOperatorDO operatorDOS);

	@Select("select * from auto_archive_operator where archive_rule_id=#{archiveRuleId} and deleted=0")
	public List<AutoArchiveOperatorDO> listByRuleId(String archive_rule_id);

	@Select({
			"<script>",
			"select * from auto_archive_operator  where  deleted = 0 and archive_rule_id in ",
			"<foreach item='item' collection='ruleIds' open='(' separator=',' close=')'> #{item} </foreach>",
			"</script>"
	})
	public List<AutoArchiveOperatorDO>  listByRuleIds(@Param("ruleIds") List<String> ruleIds);


	@Delete("delete from auto_archive_operator where archive_rule_id=#{archiveRuleId}")
	public void delete(String archive_rule_id);
}

package com.timevale.contractmanager.common.dal.bean.grouping;

import lombok.Data;

import java.util.Date;

/**
 * The table.
 * 台账规则配置-分类
 * <AUTHOR> Kunpeng
 */
@Data
public class AiRuleMenuDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id ID.
     */
    private Long id;
    /**
     * menuId 菜单id.
     */
    private String menuId;
    /**
     * ruleId 台账规则id.
     */
    private String ruleId;

    /**
     * 规则版本 台账3.0开始台账设置及数据不在contract-manager写入，所以ai_rule表没有台账信息，只有分类关联台账表有
     */
    private Integer ruleVersion;

    private String oid;

    private String gid;

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set menuId 菜单id.
     */
    public void setMenuId(String menuId){
        this.menuId = menuId;
    }

    /**
     * Get menuId 菜单id.
     *
     * @return the string
     */
    public String getMenuId(){
        return menuId;
    }

    /**
     * Set ruleId 台账规则id.
     */
    public void setRuleId(String ruleId){
        this.ruleId = ruleId;
    }

    /**
     * Get ruleId 台账规则id.
     *
     * @return the string
     */
    public String getRuleId(){
        return ruleId;
    }
}

package com.timevale.contractmanager.common.dal.dao.opponententity;

import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentOrgInfoOrder;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-03-21 15:07
 */
public interface OpponentOrgInfoOrderDAO {

	@Insert({"INSERT INTO opponent_org_info_order (expirat_time,deleted,order_type,tenant_oid,tenant_gid,check_info_num,used_check_info_num) " +
			"VALUES(#{expiratTime},#{deleted},#{orderType},#{tenantOid},#{tenantGid},#{checkInfoNum},#{usedCheckInfoNum})"})
	public Integer insert(OpponentOrgInfoOrder opponentCheckInfo);

	@Select("select * from opponent_org_info_order where tenant_gid = #{tenantGid} and order_type = 1")
	public OpponentOrgInfoOrder queryInitByTenantGid(String tenantGid);

	@Select("select * from opponent_org_info_order where tenant_gid = #{tenantGid} and deleted = 0")
	public List<OpponentOrgInfoOrder> queryByTenantGid(String tenantGid);

	@Select("select * from opponent_org_info_order where tenant_gid = #{tenantGid} " +
			"and deleted = 0 and order_type= 2 order by expirat_time limit 1")
	public OpponentOrgInfoOrder queryPresentOrderByTenantGid(String tenantGid);

	@Select("select * from opponent_org_info_order where tenant_gid = #{tenantGid} " +
			"and deleted = 0 and expirat_time < CURRENT_DATE")
	public List<OpponentOrgInfoOrder> queryExpiraByTenantGid(String tenantGid);

	@Select("select * from opponent_org_info_order where tenant_gid = #{tenantGid} " +
			"and deleted = 0 and check_info_num = used_check_info_num")
	public List<OpponentOrgInfoOrder> queryFinishedByTenantGid(String tenantGid);
	@Update({
			"<script>",
			"UPDATE opponent_org_info_order SET deleted = 1 WHERE id IN",
			"<foreach item='item' collection='idList' open='(' separator=',' close=')'> #{item} </foreach>",
			"</script>"
	})
	public void batchUpdate(@Param("idList") List<Long> idList);


	@Update("UPDATE opponent_org_info_order SET deleted = 1 WHERE id = #{id}")
	public Integer updateInitOrder(long id);

	@Update("UPDATE opponent_org_info_order SET used_check_info_num = #{usedCheckInfoNum} WHERE id = #{id}")
	public Integer updateCheckInfo(@Param("usedCheckInfoNum") Integer usedCheckInfoNum,@Param("id") Long id);
}

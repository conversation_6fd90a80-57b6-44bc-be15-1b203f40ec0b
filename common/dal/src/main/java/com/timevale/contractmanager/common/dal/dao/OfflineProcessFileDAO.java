package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.OfflineProcessFileDO;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

/**
 * The Table offline_process_file.
 * 纸质合同流程文件信息
 * <AUTHOR> Kunpeng
 */
public interface OfflineProcessFileDAO{

    @Insert({
        "<script>",
        "INSERT INTO offline_process_file(",
        " PROCESS_ID,FILE_ID,FILE_KEY,FILE_NAME,FILE_TYPE,OCR_FILE_KEY) VALUES",
        " <foreach collection='list' item='item' separator=','>",
        "(#{item.processId,jdbcType=VARCHAR}",
        " , #{item.fileId,jdbcType=VARCHAR}",
        " , #{item.fileKey,jdbcType=VARCHAR}",
        " , #{item.fileName,jdbcType=VARCHAR}",
        " , #{item.fileType,jdbcType=VARCHAR}",
        " , #{item.ocrFileKey,jdbcType=VARCHAR}",
        " )",
        "</foreach>",
        "</script>",
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int batchInsert(@Param("list") List<OfflineProcessFileDO> list);

    @Update("update offline_process_file set ocr_file_key = #{ocrFileKey} where file_key = #{fileKey}")
    int updateOcrFileKey(@Param("fileKey") String fileKey, @Param("ocrFileKey") String ocrFileKey);

    @Select("select * from offline_process_file where file_key = #{fileKey}")
    List<OfflineProcessFileDO> queryByFileKey(String fileKey);

    @Select("select * from offline_process_file where process_id = #{processId}")
    List<OfflineProcessFileDO> queryByProcessId(String processId);

    @Select({
        "<script>",
        "select * from offline_process_file where process_id in",
        "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
        "#{item}",
        "</foreach>",
        "</script>"
    })
    List<OfflineProcessFileDO> queryByProcessIds(@Param("list") Collection<String> processIds);
}

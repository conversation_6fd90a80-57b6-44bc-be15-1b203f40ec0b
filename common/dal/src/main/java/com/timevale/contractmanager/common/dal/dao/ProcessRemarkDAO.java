package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessRelationDO;
import com.timevale.contractmanager.common.dal.bean.ProcessRemarkDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 合同备注
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
public interface ProcessRemarkDAO {

    @Insert("INSERT INTO process_remark (process_id, remark, account_name, account_login_mobile, account_login_email," +
            "account_oid, account_gid, subject_oid, subject_gid, type, ext) " +
            "VALUES (#{processId}, #{remark}, #{accountName}, #{accountLoginMobile}, #{accountLoginEmail}, #{accountOid}," +
            "#{accountGid}, #{subjectOid}, #{subjectGid}, #{type}, #{ext})")
    void insert(ProcessRemarkDO processRemark);

    @Insert({
            "<script>",
            "INSERT INTO process_remark (process_id, remark, account_name, account_login_mobile, account_login_email, ",
            " account_oid, account_gid, subject_oid, subject_gid, type, ext) VALUES",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.processId,jdbcType=VARCHAR},#{item.remark,jdbcType=VARCHAR},#{item.accountName,jdbcType=VARCHAR},#{item.accountLoginMobile,jdbcType=VARCHAR}, ",
            "#{item.accountLoginEmail,jdbcType=TINYINT},#{item.accountOid,jdbcType=TINYINT},#{item.accountGid,jdbcType=VARCHAR},#{item.subjectOid,jdbcType=VARCHAR},#{item.subjectGid,jdbcType=VARCHAR},#{item.type,jdbcType=VARCHAR},#{item.ext,jdbcType=VARCHAR})",
            "</foreach>",
            "</script>"
    })
    int insertBatch(@Param("list") List<ProcessRemarkDO> list);

    @Select("SELECT * FROM process_remark WHERE process_id = #{processId} ORDER BY create_time ASC")
    List<ProcessRemarkDO> listByProcessId(String processId);

    @Select("<script>" +
            "SELECT * FROM process_remark WHERE process_id IN " +
            "<foreach item='processId' collection='processIds' open='(' separator=',' close=')'>" +
            "#{processId}" +
            "</foreach>" +
            " ORDER BY create_time ASC" +
            "</script>")
    List<ProcessRemarkDO> listByProcessIds(@Param("processIds") List<String> processIds);
}

package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.UserDownloadConfigDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * @author: duhui
 * @since: 2021/11/15 11:27 上午
 **/
public interface UserDownloadConfigDAO {

    @Insert({
            "insert into user_download_config(oid, type, config_detail)",
            "values(#{oid}, #{type}, #{configDetail})",
            "ON DUPLICATE KEY UPDATE type=#{type}, config_detail=#{configDetail}"
    })
    int insertOrUpdate(UserDownloadConfigDO userDownloadConfigDO);

    @Update({
            "update user_download_config set type=#{type}",
            "where oid = #{oid}"
    })
    int updateTypeByOid(@Param("type") Integer type, @Param("oid") String oid);

    @Select({
            "select * from user_download_config where oid = #{oid}"
    })
    UserDownloadConfigDO getByOid(@Param("oid") String oid);

    @Select({
            "select * from user_download_config where oid = #{oid} and type = 2"
    })
    UserDownloadConfigDO getCustomizeByOid(@Param("oid") String oid);
}

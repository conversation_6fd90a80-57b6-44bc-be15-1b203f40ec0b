package com.timevale.contractmanager.common.dal.bean.opponententity;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-03-21 15:00
 */
@Data
@Builder
public class OpponentOrgInfoOrder {
	/**
	 * id 自增主键.
	 */
	@JsonIgnore
	private Long id;
	/**
	 * deleted 是否删除 0-否；1-是
	 */
	private Integer deleted;
	/**
	 * createTime 创建时间.
	 */
	private Date createTime;
	/**
	 * modifyTime 更新时间.
	 */
	private Date modifiedTime;
	/**
	 * 过期时间
	 */
	private Date expiratTime;
	/**
	 * 订单类型
	 */
	private Integer orderType;
	/**
	 * 可用次数
	 */
	private Integer checkInfoNum;
	/**
	 * 已用次数
	 */
	private Integer usedCheckInfoNum;
	/**
	 *tenantGid 租户gid
	 */
	private String tenantGid;
	/**
	 *tenantGid 租户oid
	 */
	private String tenantOid;
	@Tolerate
	public OpponentOrgInfoOrder(){}
}

package com.timevale.contractmanager.common.dal.dao.limit;

import com.timevale.contractmanager.common.dal.bean.limit.AiExtractLimitDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * AiExtractLimitDAO
 *
 * <AUTHOR>
 * @since 2023/11/3 10:32 上午
 */
public interface AiExtractLimitDAO {

    @Insert({"INSERT INTO ai_extract_limit (tenant_oid, tenant_gid, biz_id, total, margin, type)" +
            "VALUES(#{tenantOid}, #{tenantGid}, #{bizId}, #{total}, #{margin}, #{type})"})
    public Integer insert(AiExtractLimitDO aiExtractLimitDO);

    @Select("select * from ai_extract_limit where tenant_gid = #{tenantGid} and type = #{type} limit 1")
    public AiExtractLimitDO queryByTenantGidAndType(@Param("tenantGid") String tenantGid, @Param("type") String type);

    @Update("update ai_extract_limit set margin = #{margin} where tenant_gid = #{tenantGid} and type = #{type}")
    public Integer updateMargin(@Param("margin") Integer margin, @Param("tenantGid") String tenantGid, @Param("type") String type);

    @Update("update ai_extract_limit set total = #{total}, margin = #{margin} where tenant_gid = #{tenantGid} and type = #{type}")
    public Integer updateTotalAndMargin(@Param("total") Integer total, @Param("margin") Integer margin, @Param("tenantGid") String tenantGid, @Param("type") String type);

    @Update("update ai_extract_limit set total = total + #{total}, margin = margin + #{margin} where tenant_gid = #{tenantGid} and type = #{type}")
    public Integer addNum(@Param("total") Integer total, @Param("margin") Integer margin, @Param("tenantGid") String tenantGid, @Param("type") String type);

}

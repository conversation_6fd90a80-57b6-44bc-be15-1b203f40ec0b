package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessPreferenceDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 系统偏好设置表
 * <AUTHOR>
 * @since 2021-05-17 17:50
 **/
public interface ProcessPreferenceDAO {
    /**
     * 批量插入/更新process_preference表
     * @param entities
     * @return
     */
    @Insert({
            "<script>",
                "INSERT INTO process_preference( account_oid, account_gid, account_type, preference_type, preference_value, status)",
                "VALUES" +
                "<foreach collection='entities' item='item' separator=','>",
                    "(#{item.accountOid,jdbcType=VARCHAR},",
                    "#{item.accountGid,jdbcType=VARCHAR},",
                    "#{item.accountType,jdbcType=TINYINT},",
                    "#{item.preferenceType,jdbcType=VARCHAR},",
                    "#{item.preferenceValue,jdbcType=VARCHAR},",
                    "#{item.status,jdbcType=TINYINT})",
                "</foreach>",
                " ON DUPLICATE KEY UPDATE" +
                " preference_value = VALUES(preference_value)" +
                ", status = values(status)" +
            "</script>"})
    int saveOrUpdateBatch(@Param("entities") List<ProcessPreferenceDO> entities);


    @Select({
            "<script>",
            "select * from process_preference where account_gid=#{gid} ",
            "and preference_type in",
                "<foreach item='pType' index='index' collection='types'",
                    "open='(' separator=',' close=')'>",
                    "#{pType}",
                "</foreach>",
            " and status = 1" +
            "</script>"
    })
    List<ProcessPreferenceDO> queryByGIdAndTypes(@Param("gid") String gid, @Param("types") List<String> types);



    @Select({
            "<script>",
            "select * from process_preference where account_gid in",
            "<foreach item='gid' index='index' collection='gids'",
                "open='(' separator=',' close=')'>",
                "#{gid}",
            "</foreach>",
            "<if test='types != null and types.size() > 0'>",
                "and preference_type in",
                "<foreach item='pType' index='index' collection='types'",
                    "open='(' separator=',' close=')'>",
                    "#{pType}",
                "</foreach>",
            "</if>",
            " and status = 1 limit 500",
            "</script>"
    })
    List<ProcessPreferenceDO> listByGidAndType(@Param("gids") List<String> gids, @Param("types") List<String> types);


    @Update({"update process_preference ",
            "set status = #{currentStatus}, ",
            "modify_time = now() ",
            "where account_gid = #{gid} and status = #{originalStatus}",})
    int updateStatus(
            @Param("gid") String gid,
            @Param("originalStatus") Integer originalStatus,
            @Param("currentStatus") Integer currentStatus);


    @Update({"update process_preference set preference_value = #{preferenceValue}, modify_time = now() where id = #{id}"})
    int updateValue(ProcessPreferenceDO entity);

    @Delete("delete from process_preference where account_gid = #{accountGid} and preference_type=#{preferenceType}")
    int delete(@Param("accountGid") String accountGid, @Param("preferenceType") String preferenceType);
}

package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.FieldDataAiDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * The Table field_data_ai.
 * 存储AI解析后的字段内容-企业维度
 * <AUTHOR> Kunpeng
 */
public interface FieldDataAiDAO{

    /**
     * desc:批量插入表:field_data_ai.<br/>
     * @param list list
     * @return int
     */
    @Insert({
            "<script>",
            "INSERT INTO field_data_ai (V, GID, OID, FIELD_CODE,RULE_ID) VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.v,jdbcType=VARCHAR}, #{item.gid,jdbcType=VARCHAR}, #{item.oid,jdbcType=VARCHAR}, #{item.fieldCode,jdbcType=VARCHAR}, #{item.ruleId,jdbcType=VARCHAR})",
            "</foreach>",
            "</script>"
    })
    public int insertBatch(List<FieldDataAiDO> list);

    @Select("SELECT V FROM field_data_ai WHERE OID = #{oid,jdbcType=VARCHAR} AND RULE_ID = #{ruleId,jdbcType=VARCHAR} AND FIELD_CODE = #{fieldCode,jdbcType=VARCHAR}")
    public Set<String> queryValueListByIdxOidRuleIdK(@Param("oid") String oid, @Param("ruleId") String ruleId, @Param("fieldCode") String fieldCode);
}

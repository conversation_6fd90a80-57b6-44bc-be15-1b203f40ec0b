package com.timevale.contractmanager.common.dal.dao.sharesign;

import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignUrlDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * The Table share_sign_url.
 * 分享签分享地址表
 * <AUTHOR> Kunpeng
 */
public interface ShareSignUrlDAO{

    @Insert(
            "INSERT INTO share_sign_url(\n"
                    + "            UUID\n"
                    + "            ,SHARE_URL\n"
                    + "            ,PARTICIPANT_ID\n"
                    + "            ,PARTICIPANT_TYPE\n"
                    + "            ,PARTICIPANT_LABEL\n"
                    + "            ,SHARE_SIGN_TASK_ID\n"
                    + "            ,SHARE_QRCODE_FILEKEY\n"
                    + "        )VALUES(\n"
                    + "             #{uuid,jdbcType=VARCHAR}\n"
                    + "            , #{shareUrl,jdbcType=VARCHAR}\n"
                    + "            , #{participantId,jdbcType=VARCHAR}\n"
                    + "            , #{participantType,jdbcType=TINYINT}\n"
                    + "            , #{participantLabel,jdbcType=VARCHAR}\n"
                    + "            , #{shareSignTaskId,jdbcType=VARCHAR}\n"
                    + "            , #{shareQrcodeFilekey,jdbcType=VARCHAR}\n"
                    + "        )")
    @Options(useGeneratedKeys = true, keyColumn = "id")
    public int insert(ShareSignUrlDO entity);

    @Insert({
        "<script>",
        "INSERT INTO share_sign_url("
                + "            UUID"
                + "            ,SHARE_URL"
                + "            ,PARTICIPANT_ID"
                + "            ,PARTICIPANT_TYPE"
                + "            ,PARTICIPANT_LABEL"
                + "            ,SHARE_SIGN_TASK_ID"
                + "            ,SHARE_QRCODE_FILEKEY"
                + "        ) VALUES"
                + "       <foreach collection='list' item='item' separator=','>"
                + "            (#{item.uuid,jdbcType=VARCHAR}"
                + "            , #{item.shareUrl,jdbcType=VARCHAR}"
                + "            , #{item.participantId,jdbcType=VARCHAR}"
                + "            , #{item.participantType,jdbcType=TINYINT}"
                + "            , #{item.participantLabel,jdbcType=VARCHAR}"
                + "            , #{item.shareSignTaskId,jdbcType=VARCHAR}"
                + "            , #{item.shareQrcodeFilekey,jdbcType=VARCHAR})"
                + "       </foreach>"
                + "</script>"
    })
    @Options(useGeneratedKeys = true, keyColumn = "id")
    public int batchInsert(List<ShareSignUrlDO> list);

    @Update("update share_sign_url set uuid = #{uuid}, share_url = #{shareUrl}, share_qrcode_filekey = #{shareQrcodeFilekey} where id = #{id}")
    public int update(ShareSignUrlDO entity);

    @Select("select * from share_sign_url where uuid = #{uuid} limit 1")
    public ShareSignUrlDO getByUuid(String uuid);

    @Select("select * from share_sign_url where share_sign_task_id = #{shareSignTaskId}")
    public List<ShareSignUrlDO> getByShareSignTaskId(String shareSignTaskId);

    @Select("select * from share_sign_url where share_sign_task_id = #{shareSignTaskId} and participant_id = #{participantId}")
    public List<ShareSignUrlDO> getByParticipantId(@Param("shareSignTaskId") String shareSignTaskId, @Param("participantId") String participantId);
}

package com.timevale.contractmanager.common.dal.bean.opponententity;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-03-08 15:42
 */
@Data
@Builder
public class OpponentOrgSupplierMappingDO {
	private Long id;
	/**
	 * createTime 创建时间
	 */
	private Date createTime;
	/**
	 * modifyTime 更新时间.
	 */
	private Date modifiedTime;

	/**
	 * 企业名称
	 */
	private String orgName;
	private String entityOid;
	private String entityGid;
	/**
	 * 企业编号
	 */
	private String orgCode;
	/**
	 * supplierType 供应商类型
	 */
	private Integer	supplierType;

	@Tolerate
	public OpponentOrgSupplierMappingDO(){}
}

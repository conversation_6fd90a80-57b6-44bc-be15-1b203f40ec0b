package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessCustomizeConfigDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table process_customize_config. 合同流程自定义配置表
 *
 * <AUTHOR> Kunpeng
 */
public interface ProcessCustomizeConfigDAO {

    /**
     * desc:插入表:process_customize_config.<br>
     *
     * @param entity entity
     * @return int
     */
    @Insert({
        "<script>",
        "insert into process_customize_config(process_id,subject_oid,subject_gid,config_info) values",
        "(#{processId},#{subjectOid},#{subjectGid},#{configInfo})",
        "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(ProcessCustomizeConfigDO entity);

    @Insert({
        "<script>",
        "insert into process_customize_config(process_id,subject_oid,subject_gid,config_info) values",
        "<foreach collection='list' item='item' separator=','>",
        "(#{item.processId},#{item.subjectOid},#{item.subjectGid},#{item.configInfo})",
        "</foreach>",
        "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertBatch(@Param("list") List<ProcessCustomizeConfigDO> list);
    /**
     * desc:批量根据id更新表:process_customize_config.<br>
     *
     * @param list
     * @return int
     */
    @Update({
        "<script>",
        "update process_customize_config a join (",
        "<foreach collection='list' item='item' separator='UNION'>",
        " SELECT #{item.id} AS id, #{item.configInfo} AS config_info ",
        "</foreach>",
        ") b USING(id)",
        "set a.config_info = b.config_info",
        "</script>"
    })
    int updateBatchById(@Param("list") List<ProcessCustomizeConfigDO> list);

    /**
     * desc:根据processId获取数据:process_customize_config.<br>
     *
     * @param processId processId
     * @return ProcessConfigDO
     */
    @Select({
        "<script>",
        "select * from process_customize_config where process_id = #{processId}",
        "</script>"
    })
    List<ProcessCustomizeConfigDO> getByIdxProcessId(String processId);

    @Select({
            "<script>",
            "select * from process_customize_config where process_id = #{processId}",
            " and subject_gid = #{subjectGid} limit 1",
            "</script>"
    })
    ProcessCustomizeConfigDO getByProcessIdAndGid(
            @Param("processId") String processId, @Param("subjectGid") String subjectGid);

    @Select({
            "<script>",
            "select * from process_customize_config where process_id in",
            "<foreach item='item' index='index' collection='processIds' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            " and subject_gid = #{subjectGid}",
            "</script>"
    })
    List<ProcessCustomizeConfigDO> getByIdxProcessIdsAndGid(
            @Param("processIds") List<String> processIds, @Param("subjectGid") String subjectGid);

    @Select({
            "<script>",
            "select * from process_customize_config where process_id in",
            "<foreach item='item' index='index' collection='processIds' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<ProcessCustomizeConfigDO> getByIdxProcessIds(
            @Param("processIds") List<String> processIds);

    @Update({
            "update process_customize_config ",
            "set config_info = #{configInfo} ",
            "where id = #{id}",
    })
    int updateConfigInfoById(@Param("id") Long id,@Param("configInfo") String configInfo);

}

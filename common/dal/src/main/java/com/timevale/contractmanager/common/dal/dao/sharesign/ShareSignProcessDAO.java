package com.timevale.contractmanager.common.dal.dao.sharesign;

import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignProcessDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table share_sign_process.
 * 分享签流程记录
 * <AUTHOR> Kunpeng
 */
public interface ShareSignProcessDAO{

    @Insert(
            "INSERT INTO share_sign_process(\n"
                    + "            PROCESS_ID\n"
                    + "            ,SHARE_SIGN_TASK_ID\n"
                    + "            ,PARTICIPANT_ID\n"
                    + "            ,PARTICIPANT_TYPE\n"
                    + "            ,ACCOUNT_OID\n"
                    + "            ,ACCOUNT_GID\n"
                    + "            ,SUBJECT_OID\n"
                    + "            ,SUBJECT_NAME\n"
                    + "            ,DELETED\n"
                    + "            ,STATUS\n"
                    + "        )VALUES(\n"
                    + "              #{processId,jdbcType=VARCHAR}\n"
                    + "            , #{shareSignTaskId,jdbcType=VARCHAR}\n"
                    + "            , #{participantId,jdbcType=VARCHAR}\n"
                    + "            , #{participantType,jdbcType=TINYINT}\n"
                    + "            , #{accountOid,jdbcType=VARCHAR}\n"
                    + "            , #{accountGid,jdbcType=VARCHAR}\n"
                    + "            , #{subjectOid,jdbcType=VARCHAR}\n"
                    + "            , #{subjectName,jdbcType=VARCHAR}\n"
                    + "            , #{deleted,jdbcType=TINYINT}\n"
                    + "            , #{status,jdbcType=TINYINT}\n"
                    + "        )")
    @Options(useGeneratedKeys = true, keyColumn = "id")
    public int insert(ShareSignProcessDO entity);

    @Select("select * from share_sign_process where process_id = #{processId} and deleted = 0 limit 1")
    public List<ShareSignProcessDO> queryByProcessId(String processId);

    @Select("select * from share_sign_process where share_sign_task_id = #{shareSignTaskId} and account_gid = #{accountGid} and deleted = 0 limit #{limit}")
    public List<ShareSignProcessDO> queryByShareSignTaskIdAndGid(
            @Param("shareSignTaskId") String shareSignTaskId,
            @Param("accountGid") String accountGid,
            @Param("limit") Integer limit);

    @Select({
        "<script>",
        " SELECT * ",
        " FROM share_sign_process ",
        " WHERE ",
        "   process_id IN <foreach collection='list' item='item' open='(' separator=',' close=')'> #{item} </foreach>",
        "</script>"
    })
    List<ShareSignProcessDO> queryByProcessIds(@Param("list") List<String> processIds);

    @Select("select count(*) from share_sign_process where share_sign_task_id = #{shareSignTaskId} and account_gid = #{accountGid} and deleted = 0")
    public int countByShareSignTaskIdAndGid(@Param("shareSignTaskId") String shareSignTaskId, @Param("accountGid") String accountGid);

    @Select({
            "select count(*) from share_sign_process where share_sign_task_id = #{shareSignTaskId} and account_gid = #{accountGid}",
            "and participant_type = 0 and deleted = 0"
    })
    int countByShareSignTaskIdAndGidWithPerson(@Param("shareSignTaskId") String shareSignTaskId, @Param("accountGid") String accountGid);

    @Update("update share_sign_process set deleted = 1 where process_id = #{processId} and deleted = 0")
    public int deleteByProcessId(String processId);

    @Update("update share_sign_process set status = #{status} where process_id = #{processId} and deleted = 0")
    public int updateStatusByProcessId(@Param("processId") String processId, @Param("status") Integer status);
}

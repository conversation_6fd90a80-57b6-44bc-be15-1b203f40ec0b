package com.timevale.contractmanager.common.dal.bean.fulfillment;

import lombok.Data;

import java.util.Date;

/**
 * ContractFulfillmentRuleDO
 *
 * <AUTHOR>
 * @since 2023/10/11 11:10 上午
 */
@Data
public class ContractFulfillmentRuleDO {

    private Long id;

    private String tenantOid;

    private String tenantGid;

    private String name;

    private String ruleId;

    private String type;

    private String typeName;

    private String status;

    private String formId;

    private String scopeType;

    private String noticeChannel;

    private String noticeRule;

    private String noticeQueryScript;

    private String createWay;

    private String createByOid;

    private String modifiedByOid;

    private Date createTime;

    /**
     * modifyTime 更新时间.
     */
    private Date modifiedTime;

}

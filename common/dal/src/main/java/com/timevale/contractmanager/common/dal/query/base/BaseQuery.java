package com.timevale.contractmanager.common.dal.query.base;

import java.io.Serializable;

/**
 * Created by t<PERSON><PERSON><PERSON> on 2019/3/2.
 */
public class BaseQuery implements Serializable {


    /**
     * 第几页
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 排序 例：created desc
     */
    private String orderByClause;

    public BaseQuery() {
    }

    public BaseQuery(Integer pageIndex, Integer pageSize, String orderByClause) {
        this.pageNum = pageIndex;
        this.pageSize = pageSize;
        this.orderByClause = orderByClause;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


    /**
     * 获取从第几条开始
     * @return
     */
    public int getStart() {
        if(this.pageNum <= 1)
            return 0;
        else
            return (this.pageNum - 1) * this.pageSize;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }
}

package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * The table. 纸质合同导入记录
 *
 * <AUTHOR> Kunpeng
 */
@Data
public class OfflineContractImportRecordDO {

    /** id 自增id. */
    private Long id;
    /** menuId 归档菜单id. */
    private String menuId;
    /** status 导入状态. */
    private String status;
    /** recordId 导入记录id. */
    private String recordId;
    /** importWay 导入方式. */
    private String importWay;
    /** extractWay 合同信息提取方式. */
    private String extractWay;
    /** subjectGid 导入主体gid. */
    private String subjectGid;
    /** subjectOid 导入主体oid. */
    private String subjectOid;
    /** importerGid 导入人gid. */
    private String importerGid;
    /** importerOid 导入人oid. */
    private String importerOid;
    /** subjectName 导入主体名称. */
    private String subjectName;
    /** importerName 导入人姓名. */
    private String importerName;
    /** extractConfig 提取方式配置. */
    private String extractConfig;
    /** contractSize 导入数量. */
    private Integer contractSize;
    /** successSize 导入成功数量. */
    private Integer successSize;
    /** failedSize 导入失败数量. */
    private Integer failedSize;
    /** importClient 导入端. */
    private String importClient;
    /** createTime 创建时间. */
    private Date createTime;
    /** updateTime 更新时间. */
    private Date updateTime;
}

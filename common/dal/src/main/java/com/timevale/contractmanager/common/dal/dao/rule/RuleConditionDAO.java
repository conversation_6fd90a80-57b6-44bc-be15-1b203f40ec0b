package com.timevale.contractmanager.common.dal.dao.rule;

import com.timevale.contractmanager.common.dal.bean.rule.RuleConditionDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface RuleConditionDAO {

	@Insert({"INSERT INTO rule_condition (field_type,rule_id,operator_id,parent_operator_id," +
	"condition_params,operator_type,field_name,field_id,operator_weight,match_type,biz_type)" +
	"VALUES(#{fieldType},#{ruleId},#{operatorId},#{parentOperatorId},#{conditionParams},#{operatorType}," +
	"#{fieldName},#{fieldId},#{operatorWeight},#{matchType},#{bizType})"})
	public void insert(RuleConditionDO ruleConditionDO);

	@Insert({
			"<script>",
			"INSERT INTO rule_condition (field_type,rule_id,operator_id,parent_operator_id,",
			"condition_params,operator_type,field_name,field_id,operator_weight,match_type,biz_type) values",
			"<foreach collection='list' item='t' index='index' separator=','>",
			"(#{t.fieldType},#{t.ruleId},#{t.operatorId},#{t.parentOperatorId},#{t.conditionParams},#{t.operatorType},",
			"#{t.fieldName},#{t.fieldId},#{t.operatorWeight},#{t.matchType},#{t.bizType})",
			"</foreach>",
			"</script>"
	})
	public void batchInsert(List<RuleConditionDO> list);

	@Select("select * from rule_condition where rule_id=#{ruleId} and deleted=0")
	public List<RuleConditionDO> listByRuleId(String ruleId);

	@Select({
			"<script>",
			"select * from rule_condition  where  deleted = 0 and rule_id in ",
			"<foreach item='item' collection='ruleIds' open='(' separator=',' close=')'> #{item} </foreach>",
			"</script>"
	})
	public List<RuleConditionDO> listByRuleIds(@Param("ruleIds") List<String> ruleIds);

	@Delete("delete from rule_condition where rule_id=#{archiveRuleId}")
	public void delete(String rule_id);
}

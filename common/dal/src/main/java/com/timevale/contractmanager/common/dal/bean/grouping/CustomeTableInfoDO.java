package com.timevale.contractmanager.common.dal.bean.grouping;


import lombok.Data;

/**
 * The table.
 * 表格字段自定义信息
 * <AUTHOR> Kunpeng
 */
@Data
public class CustomeTableInfoDO{

    /**
     * id ID.
     */
    private Long id;
    /**
     * gid 空间gid.
     */
    private String gid;
    /**
     * oid OID.
     */
    private String oid;
    /**
     * menuId MENU_ID.
     */
    private String menuId;
    /**
     * fieldId 字段id.
     */
    private String fieldId;
    /**
     * deleted 是否删除，0：否，1：是.
     */
    private Integer deleted;
    /**
     * enabled 是否显示，0：否，1：是.
     */
    private Integer enabled;
    /**
     * freezed 是否冻结，0：否，1：是.
     */
    private Integer freezed;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 操作人gid
     */
    private String operateGid;

    /**
     * 操作人oid
     */
    private String operateOid;
}

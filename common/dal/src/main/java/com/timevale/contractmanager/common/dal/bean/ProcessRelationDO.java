package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * 流程关联关系对象
 *
 * <AUTHOR>
 * @since 2021-06-08
 **/
@Data
public class ProcessRelationDO {

    /**
     * id 主键id.
     */
    private Long id;

    /**
     * 关联类型，1-解约，2-续签
     */
    private Integer type;

    /**
     * 原流程id
     */
    private String originProcessId;

    /**
     * 关联流程id
     */
    private String processId;

    /**
     * 关联文档id
     */
    private String fileId;

    /**
     * 关联状态
     */
    private Integer status;

    /**
     * 原流程状态
     */
    private Integer originStatus;

    /**
     * 关联原因/说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModified;

}

package com.timevale.contractmanager.common.dal.bean.fulfillment;

import lombok.Data;

import java.util.Date;

/**
 * ContractFulfillmentRecordDO
 *
 * <AUTHOR>
 * @since 2023/10/11 11:11 上午
 */
@Data
public class ContractFulfillmentRecordDO {

    private Long id;

    private String tenantOid;

    private String tenantGid;

    private String recordId;

    private String title;

    private String processId;

    private String ruleId;

    private Date noticeTime;

    private Date completeTime;

    private String noticeOid;

    private String noticeGid;

    private String type;

    private String typeName;

    private String status;

    private Date createTime;

    /**
     * modifyTime 更新时间.
     */
    private Date modifiedTime;

}

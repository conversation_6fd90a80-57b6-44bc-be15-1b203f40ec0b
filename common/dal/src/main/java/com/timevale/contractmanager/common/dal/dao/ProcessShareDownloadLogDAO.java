package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessShareDownloadLogDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * The Table process_share_download_log.
 * 分享合同操作记录表
 * <AUTHOR> Kunpeng
 */
public interface ProcessShareDownloadLogDAO{
    String ALL_COLUMNS = "create_time,update_time,operating_time,id,process_id,subject_gid,subject_oid,operating_oid,deleted,operating_type,share_download_id";

    /**
     * desc:插入表:process_share_download_log.<br>
     *
     * @param entity entity
     * @return int
     */
    @Insert({
        "<script>",
        "insert into process_share_download_log(process_id,",
            " <if test=\"subjectGid!=null and subjectGid!=''\"> subject_gid, </if> ",
            " <if test=\"subjectOid!=null and subjectOid!=''\"> subject_oid, </if> ",
            " operating_oid,operating_type,share_download_id) values ",
            " (#{processId}, ",
            " <if test=\"subjectGid!=null and subjectGid!=''\"> #{subjectGid}, </if> ",
            " <if test=\"subjectOid!=null and subjectOid!=''\"> #{subjectOid}, </if> ",
            " #{operatingOid},#{operatingType},#{shareDownloadId}) ",
            "</script>"
    })
    int insert(ProcessShareDownloadLogDO entity);

    @Select({"select ",ALL_COLUMNS," from process_share_download_log where share_download_id=#{shareDownloadId} and  operating_type=#{operatingType} and deleted=0"})
    List<ProcessShareDownloadLogDO> getByShareDownloadId(@Param("shareDownloadId") String shareDownloadId,@Param("operatingType")Integer operatingType);
}

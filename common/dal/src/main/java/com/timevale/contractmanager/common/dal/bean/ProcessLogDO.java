package com.timevale.contractmanager.common.dal.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;

/**
 * The table.
 * 合同管理主流程操作日志
 * <AUTHOR> Kunpeng
 */
public class ProcessLogDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    /** hbase RowKey */
    private String rowKey;

    /**
     * processId 主流程id.
     */
    private String processId;
    /**
     * subProcessId 子流程id.
     */
    private String subProcessId;

    /**
     * serialId 操作序列号， 用于关联子流程日志和分支流程日志
     */
    private String serialId;
    /**
     * subProcessType 子流程类型 1-填写 2-签署.
     */
    private Integer subProcessType;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作主体id
     */
    private String subjectId;

    /**
     * 操作主体名称
     */
    private String subjectName;

    /**
     * 操作主体类型
     */
    private Integer subjectType;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作数据
     */
    private String operateData;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set processId 主流程id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 主流程id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    /**
     * Set subProcessId 子流程id.
     */
    public void setSubProcessId(String subProcessId){
        this.subProcessId = subProcessId;
    }

    /**
     * Get subProcessId 子流程id.
     *
     * @return the string
     */
    public String getSubProcessId(){
        return subProcessId;
    }

    /**
     * Set subProcessType 子流程类型 1-填写 2-签署.
     */
    public void setSubProcessType(Integer subProcessType){
        this.subProcessType = subProcessType;
    }

    /**
     * Get subProcessType 子流程类型 1-填写 2-签署.
     *
     * @return the string
     */
    public Integer getSubProcessType(){
        return subProcessType;
    }

    public String getSerialId() {
        return serialId;
    }

    public void setSerialId(String serialId) {
        this.serialId = serialId;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public Integer getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(Integer subjectType) {
        this.subjectType = subjectType;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getOperateData() {
        return operateData;
    }

    public void setOperateData(String operateData) {
        this.operateData = operateData;
    }

    public String getRowKey() {
        return rowKey;
    }

    public void setRowKey(String rowKey) {
        this.rowKey = rowKey;
    }


}

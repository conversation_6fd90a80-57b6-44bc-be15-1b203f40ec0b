package com.timevale.contractmanager.common.dal.bean.sharesign;

import java.util.Date;

/**
 * The table.
 * 分享签分享地址表
 * <AUTHOR> Kunpeng
 */
public class ShareSignUrlDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * uuid 对外唯一id.
     */
    private String uuid;
    /**
     * shareUrl 分享地址，固定参数：shareSignTaskId, participanterId.
     */
    private String shareUrl;
    /**
     * participantId 任务参与方id.
     */
    private String participantId;
    /**
     * participantType 任务参与方类型.
     */
    private Integer participantType;
    /**
     * participantLabel 任务参与方名称.
     */
    private String participantLabel;
    /**
     * shareSignTaskId 分享签署任务id.
     */
    private String shareSignTaskId;
    /**
     * shareQrcodeFilekey 分享二维码filekey.
     */
    private String shareQrcodeFilekey;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set uuid 对外唯一id.
     */
    public void setUuid(String uuid){
        this.uuid = uuid;
    }

    /**
     * Get uuid 对外唯一id.
     *
     * @return the string
     */
    public String getUuid(){
        return uuid;
    }

    /**
     * Set shareUrl 分享地址，固定参数：shareSignTaskId, participanterId.
     */
    public void setShareUrl(String shareUrl){
        this.shareUrl = shareUrl;
    }

    /**
     * Get shareUrl 分享地址，固定参数：shareSignTaskId, participanterId.
     *
     * @return the string
     */
    public String getShareUrl(){
        return shareUrl;
    }

    /**
     * Set participantId 任务参与方id.
     */
    public void setParticipantId(String participantId){
        this.participantId = participantId;
    }

    /**
     * Get participantId 任务参与方id.
     *
     * @return the string
     */
    public String getParticipantId(){
        return participantId;
    }

    /**
     * Set participantType 任务参与方类型.
     */
    public void setParticipantType(Integer participantType){
        this.participantType = participantType;
    }

    /**
     * Get participantType 任务参与方类型.
     *
     * @return the string
     */
    public Integer getParticipantType(){
        return participantType;
    }

    /**
     * Set participantLabel 任务参与方名称.
     */
    public void setParticipantLabel(String participantLabel){
        this.participantLabel = participantLabel;
    }

    /**
     * Get participantLabel 任务参与方名称.
     *
     * @return the string
     */
    public String getParticipantLabel(){
        return participantLabel;
    }

    /**
     * Set shareSignTaskId 分享签署任务id.
     */
    public void setShareSignTaskId(String shareSignTaskId){
        this.shareSignTaskId = shareSignTaskId;
    }

    /**
     * Get shareSignTaskId 分享签署任务id.
     *
     * @return the string
     */
    public String getShareSignTaskId(){
        return shareSignTaskId;
    }

    /**
     * Set shareQrcodeFilekey 分享二维码filekey.
     */
    public void setShareQrcodeFilekey(String shareQrcodeFilekey){
        this.shareQrcodeFilekey = shareQrcodeFilekey;
    }

    /**
     * Get shareQrcodeFilekey 分享二维码filekey.
     *
     * @return the string
     */
    public String getShareQrcodeFilekey(){
        return shareQrcodeFilekey;
    }
}

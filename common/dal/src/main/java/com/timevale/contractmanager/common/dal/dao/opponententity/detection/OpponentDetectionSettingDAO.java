package com.timevale.contractmanager.common.dal.dao.opponententity.detection;

import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionSettingDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-08-11 11:17
 */
public interface OpponentDetectionSettingDAO {

	@Insert({"INSERT INTO opponent_detection_setting (tenant_gid, business_scope, real_time_detection, push, push_object" +
			", immediately_push, used_detection_num, detection_num, business_scope_detection, auto_save_opponent) " +
			"VALUES(#{tenantGid},#{businessScope},#{realTimeDetection}, #{push}, #{pushObject}, #{immediatelyPush}, " +
			"#{usedDetectionNum}, #{detectionNum}, #{businessScopeDetection}, #{autoSaveOpponent})"})
	int insert(OpponentDetectionSettingDO settingDO);

	/**
	 * 根据租户gid查询配置
	 * @param tenantGid
	 * @param deleted
	 * @return
	 */
	@Select("select * from opponent_detection_setting where tenant_gid = #{tenantGid} and deleted = #{deleted}")
	OpponentDetectionSettingDO getByTenantGid(@Param("tenantGid") String tenantGid, @Param("deleted") Integer deleted);


	/**
	 * 根据租户gid查询配置
	 * @param tenantGid
	 * @return
	 */
	@Select("select * from opponent_detection_setting where tenant_gid = #{tenantGid}")
	OpponentDetectionSettingDO getByTenantGidNoDel(@Param("tenantGid") String tenantGid);

	@Update({"<script>",
			"update  opponent_detection_setting",
			"<set>",
			"<if test = \"businessScope != null \"> business_scope = #{businessScope},</if>",
			"<if test = \"realTimeDetection != null \"> real_time_detection = #{realTimeDetection},</if>",
			"<if test = \"push != null \"> push = #{push},</if>",
			"<if test = \"pushObject != null \"> push_object = #{pushObject},</if>",
			"<if test = \"pushObject != null \"> push_object = #{pushObject},</if>",
			"<if test = \"immediatelyPush != null \"> immediately_push = #{immediatelyPush},</if>",
			"<if test = \"businessScopeDetection != null \"> business_scope_detection = #{businessScopeDetection},</if>",
			"<if test = \"usedDetectionNum != null \"> used_detection_num = #{usedDetectionNum},</if>",
			"<if test = \"autoSaveOpponent != null \"> auto_save_opponent = #{autoSaveOpponent},</if>",
			"</set>",
			"where id = #{id}",
			"</script>"
	})
	Integer update(OpponentDetectionSettingDO settingDO);


	@Select("select * from opponent_detection_setting where deleted = #{deleted} order by create_time asc limit #{offset},#{pageSize}")
	List<OpponentDetectionSettingDO> getList(@Param("offset") long offset,
											 @Param("pageSize") long pageSize,
											 @Param("deleted") Integer deleted);


	@Select("select * from opponent_detection_setting where push = #{push} and deleted = #{deleted} " +
			"order by create_time asc limit #{offset},#{pageSize}")
	List<OpponentDetectionSettingDO> getNeedSendList(@Param("offset") long offset,
													 @Param("pageSize") long pageSize,
													 @Param("deleted") Integer deleted,
													 @Param("push") Integer push);


	@Update({"<script>",
			"update opponent_detection_setting set used_detection_num = 0  where tenant_gid in",
			"(<foreach collection='tenantGids' item='item' separator=','> #{item} </foreach>)",
			"</script>"})
	void updateUsedDetectionNum(@Param("tenantGids") List<String> tenantGids);
}

package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.CustomeTableInfoDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * The Table custome_table_info.
 * 表格字段自定义信息
 * <AUTHOR> Kunpeng
 */
public interface CustomeTableInfoDAO{
    /**
     * desc:批量插入表:custome_table_info.<br/>
     * @param list list
     * @return int
     */
    @Insert({
            "<script>",
            "INSERT INTO custome_table_info (GID, OID, MENU_ID, FIELD_ID , ENABLED, FREEZED, operate_oid,operate_gid) VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.gid,jdbcType=VARCHAR}, #{item.oid,jdbcType=VARCHAR}, " +
                    "#{item.menuId,jdbcType=VARCHAR}, #{item.fieldId,jdbcType=VARCHAR} ," +
                    "#{item.enabled,jdbcType=TINYINT} , #{item.freezed,jdbcType=TINYINT} , #{item.operateOid,jdbcType=VARCHAR} , #{item.operateGid,jdbcType=VARCHAR})",
            "</foreach>",
            "</script>"
    })
    public int insertBatch(List<CustomeTableInfoDO> list);

    @Update("UPDATE custome_table_info SET DELETED = 1 WHERE OID = #{oid,jdbcType=VARCHAR} " +
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR}")
    public int deleteByOidMenuId(@Param("oid") String oid,@Param("menuId") String menuId);


    @Update("UPDATE custome_table_info SET DELETED = 1 WHERE " +
            "operate_gid = #{operateGid,jdbcType=VARCHAR} " +
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR} and oid = #{oid,jdbcType=VARCHAR}")
    public int deleteByOperateGidMenuId(@Param("operateGid") String operateGid,
                                        @Param("menuId") String menuId,
                                        @Param("oid") String oid);

    @Delete("delete from custome_table_info WHERE " +
            "operate_gid = #{operateGid,jdbcType=VARCHAR} " +
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR} and oid = #{oid,jdbcType=VARCHAR}")
    int deleteByOperateGidMenuIdTenantOid(@Param("operateGid") String operateGid,
                                          @Param("menuId") String menuId,
                                          @Param("oid") String oid);

    @Update("UPDATE custome_table_info SET DELETED = 1 " +
            "WHERE operate_oid = #{operateOid,jdbcType=VARCHAR} " +
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR} and oid = #{oid,jdbcType=VARCHAR}")
    public int deleteByOperateOidMenuId(@Param("operateOid") String operateOid,
                                        @Param("menuId") String menuId,
                                        @Param("oid") String oid);

    @Delete("delete from custome_table_info" +
            "WHERE operate_oid = #{operateOid,jdbcType=VARCHAR} " +
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR} and oid = #{oid,jdbcType=VARCHAR}")
    public int deleteByOperateOidMenuIdTenantOid(@Param("operateOid") String operateOid,
                                        @Param("menuId") String menuId,
                                        @Param("oid") String oid);

    @Select("SELECT FIELD_ID,ID,WIDTH,ENABLED,FREEZED FROM custome_table_info WHERE OID = #{oid,jdbcType=VARCHAR} AND MENU_ID = #{menuId,jdbcType=VARCHAR} AND ENABLED = 1 AND DELETED = 0 ORDER BY ID")
    public List<CustomeTableInfoDO> queryShowListByOidMenuId(@Param("oid") String oid,@Param("menuId") String menuId);


    @Select("SELECT FIELD_ID,ID,WIDTH,ENABLED,FREEZED FROM custome_table_info WHERE " +
            "operate_oid = #{operateOid,jdbcType=VARCHAR} AND " +
            "MENU_ID = #{menuId,jdbcType=VARCHAR} AND DELETED = 0 and oid = #{oid,jdbcType=VARCHAR}")
    public List<CustomeTableInfoDO> queryListByOperateOidMenuId(@Param("operateOid") String operateOid,
                                                                @Param("menuId") String menuId,
                                                                @Param("oid") String oid);

    @Select("SELECT FIELD_ID,ID,WIDTH,ENABLED,FREEZED FROM custome_table_info WHERE " +
            "operate_gid = #{operateGid,jdbcType=VARCHAR} " +
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR} AND DELETED = 0 and oid = #{oid,jdbcType=VARCHAR}")
    public List<CustomeTableInfoDO> queryListByOperateGidMenuId(@Param("operateGid") String operateGid,
                                                                @Param("menuId") String menuId,
                                                                @Param("oid") String oid);

    @Select("SELECT FIELD_ID,ID,WIDTH,ENABLED,FREEZED FROM custome_table_info WHERE OID = #{oid,jdbcType=VARCHAR} " +
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR} AND DELETED = 0 and operate_oid = '' ")
    public List<CustomeTableInfoDO> queryListByOidMenuId(@Param("oid") String oid,@Param("menuId") String menuId);

    @Update("UPDATE custome_table_info SET width = #{width} WHERE ID = #{id}")
    public int updateWidth(@Param("id") Long id,@Param("width") int width);
}

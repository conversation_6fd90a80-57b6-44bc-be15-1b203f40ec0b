package com.timevale.contractmanager.common.dal.query.processstartdata;

import com.timevale.contractmanager.common.dal.query.base.BaseQuery;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/23 10:45
 */
@Getter
@Setter
public class ProcessStartDataQuery extends BaseQuery {

    /**
     * 企业oid
     */
    private String subjectOid;

    /**
     * 企业gid
     */
    private String subjectGid;

    /**
     * 企业Oid
     */
    private String ownerOid;

    /**
     * 当前人Oid
     */
    private String ownerGid;

    /**
     * 数据源渠道
     */
    private String dataSourceChannel;

    /**
     * 筛选状态列表
     */
    private List<String> statusList;

    /**
     * 筛选类型列表
     */
    private List<String> typeList;

    /**
     * 合同名称
     */
    private String processTitle;


    /**
     * createTime < maxCreateTime
     */
    private Date maxCreateTime;
}

package com.timevale.contractmanager.common.dal.dao.hbase;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.dal.bean.ProcessLogDO;
import com.timevale.contractmanager.common.dal.configuration.HBaseConfig;
import com.timevale.contractmanager.common.dal.dao.constant.HBaseConstant;
import com.timevale.contractmanager.common.dal.handler.impl.OptLogHBaseTableHandlerImpl;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saas.hbase.model.HBaseTimeStamp;
import com.timevale.saas.hbase.processor.HBaseBasicTableProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ProcessOptLogHBaseDAO {

    @Autowired
    private HBaseConfig hBaseConfig;

    public void insert(ProcessLogDO processLogDO) {
        try {
            HBaseBasicTableProcessor.putAll(OptLogHBaseTableHandlerImpl.getInstance(hBaseConfig), processLogDO);
        } catch (Exception e) {
            log.error("insert hbase processId{ }",processLogDO.getProcessId());
            throw new BaseRuntimeException(HBaseConstant.OPT_EXCEPTION, e);
        }
    }

    public List<ProcessLogDO> getByProcessId(String processId, Date startDate, Date endDate ) {
        try {
            HBaseTimeStamp stamp = getStamp(startDate,endDate);
            List<ProcessLogDO> processLogDOList =
                    HBaseBasicTableProcessor.getList(OptLogHBaseTableHandlerImpl.getInstance(hBaseConfig), processId, stamp, ProcessLogDO.class,HBaseConstant.DEFAULT_CACHING);
            return Lists.reverse(processLogDOList);
        } catch (Exception e) {
            log.error("getByProcessId hbase processId{ }",processId);
            throw new BaseRuntimeException(HBaseConstant.OPT_EXCEPTION, e);
        }
    }

    public HBaseTimeStamp getStamp(Date startDate, Date endDate) {
        if (null == startDate || null == endDate) {
            return null;
        }
        HBaseTimeStamp stamp = new HBaseTimeStamp();
        stamp.setStartStamp(startDate.getTime());
        stamp.setEndStamp(endDate.getTime());
        return stamp;
    }

    public void deleteByProcessId(String processId, Date startDate, Date endDate ){
        try {
            HBaseTimeStamp stamp = getStamp(startDate,endDate);
            HBaseBasicTableProcessor.deleteAll(OptLogHBaseTableHandlerImpl.getInstance(hBaseConfig),processId,stamp,HBaseConstant.DEFAULT_CACHING);
        } catch (Exception e) {
            throw new BaseRuntimeException(HBaseConstant.OPT_EXCEPTION, e);
        }

    }




}

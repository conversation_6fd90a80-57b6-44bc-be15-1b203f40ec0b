package com.timevale.contractmanager.common.dal.bean.opponententity.detection;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-13 15:18
 */
@Data
@Builder
public class OpponentDetectionProblemInfoDO {

	/**
	 * id 自增主键.
	 */
	private Long id;
	/**
	 * createTime 创建时间.
	 */
	private Date createTime;
	/**
	 * modifyTime 更新时间.
	 */
	private Date modifyTime;
	/**
	 *tenantGid 租户gid
	 */
	private String tenantGid;
	/**
	 * 检测企业uuid
	 */
	private String detectionOrgId;

	/**
	 * 风险级别,10、20:低,50:中,70:高,80:高,100:极高
	 */
	private Integer riskLevel;

	/**
	 * 问题描述
	 */
	private String problemDesc;

	/**
	 * 问题编号
	 */
	private Integer problemNo;

	/**
	 * 处理建议
	 */
	private String suggestDesc;

	private String detectionTaskId;

	@Tolerate
	public OpponentDetectionProblemInfoDO(){}
}

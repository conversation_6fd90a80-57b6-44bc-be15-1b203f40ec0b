package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * The table.
 * 分享合同操作记录表
 * <AUTHOR> Kunpeng
 */
@Data
public class ProcessShareDownloadLogDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * operatingTime 操作时间.
     */
    private Date operatingTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * processId 合同id.
     */
    private String processId;
    /**
     * subjectGid 主体gid.
     */
    private String subjectGid;
    /**
     * subjectOid 主体oid.
     */
    private String subjectOid;
    /**
     * operatingOid 操作人oid.
     */
    private String operatingOid;
    /**
     * deleted 删除状态：1-删除，0-正常.
     */
    private Integer deleted;
    /**
     * operatingType 操作类型：1-分享，2-下载.
     */
    private Integer operatingType;

    private String shareDownloadId;
    }

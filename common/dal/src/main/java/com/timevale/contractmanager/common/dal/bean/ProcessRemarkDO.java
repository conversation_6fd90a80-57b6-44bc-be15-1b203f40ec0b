package com.timevale.contractmanager.common.dal.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 合同备注
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Data
public class ProcessRemarkDO {
    /** 主键ID */
    private Long id;

    /** 合同ID */
    private String processId;

    /** 备注内容 */
    private String remark;

    /** 操作人姓名 */
    private String accountName;

    /** 操作人姓名登录手机号 */
    private String accountLoginMobile;

    /** 操作人姓名登录邮箱 */
    private String accountLoginEmail;

    /** 操作人OID */
    private String accountOid;

    /** 操作人GID */
    private String accountGid;

    /** 空间OID */
    private String subjectOid;

    /** 空间GID */
    private String subjectGid;

    /**
     * 添加备注时的阶段类型
     */
    private String type;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date modifyTime;
    
    /** 扩展字段 */
    private String ext;
}

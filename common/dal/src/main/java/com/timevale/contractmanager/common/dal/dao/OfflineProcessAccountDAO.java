package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.OfflineProcessAccountDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table offline_process_account.
 * 纸质合同流程用户信息
 * <AUTHOR> Kunpeng
 */
public interface OfflineProcessAccountDAO{

    @Insert({
        "<script>",
        "INSERT INTO offline_process_account(",
        " PROCESS_ID,ACCOUNT,ACCOUNT_NAME,SUBJECT_NAME,SUBJECT_TYPE,ROLE) VALUES",
        " <foreach collection='list' item='item' separator=','>",
        "(#{item.processId,jdbcType=VARCHAR}",
        " , #{item.account,jdbcType=VARCHAR}",
        " , #{item.accountName,jdbcType=VARCHAR}",
        " , #{item.subjectName,jdbcType=VARCHAR}",
        " , #{item.subjectType,jdbcType=VARCHAR}",
        " , #{item.role,jdbcType=VARCHAR}",
        " )",
        "</foreach>",
        "</script>",
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int batchInsert(@Param("list") List<OfflineProcessAccountDO> list);

    @Delete("delete from offline_process_account where process_id = #{processId}")
    int deleteByProcessId(String processId);

    @Select("select * from offline_process_account where process_id = #{processId}")
    List<OfflineProcessAccountDO> queryByProcessId(String processId);
}

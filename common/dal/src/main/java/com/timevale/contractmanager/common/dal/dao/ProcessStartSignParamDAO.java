package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessStartSignParamDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table process_start_sign_param. 流程发起签署参数临时存储表
 *
 * <AUTHOR> Kunpeng
 */
public interface ProcessStartSignParamDAO {

    /**
     * desc:插入表:process_start_sign_param.<br>
     *
     * @param entity entity
     * @return int
     */
    @Insert(
            "INSERT INTO process_start_sign_param(PROCESS_ID, START_SIGN_PARAM) "
                    + "VALUES(#{processId,jdbcType=VARCHAR}, #{startSignParam,jdbcType=LONGVARCHAR})")
    int insert(ProcessStartSignParamDO entity);
    /**
     * desc:根据唯一约束UniqProcessId更新表:process_start_sign_param.<br>
     *
     * @param entity entity
     * @return int
     */
    @Update("update process_start_sign_param set start_sign_param = #{startSignParam} where process_id = #{processId}")
    int updateByUniqProcessId(ProcessStartSignParamDO entity);
    /**
     * desc:根据唯一约束UniqProcessId删除数据:process_start_sign_param.<br>
     *
     * @param processId processId
     * @return int
     */
    @Delete("delete from process_start_sign_param where process_id = #{processId}")
    int deleteByUniqProcessId(@Param("processId") String processId);
    /**
     * desc:根据唯一约束UniqProcessId获取数据:process_start_sign_param.<br>
     *
     * @param processId processId
     * @return ProcessStartSignParamDO
     */
    @Select("select * from process_start_sign_param where process_id = #{processId}")
    ProcessStartSignParamDO getByUniqProcessId(@Param("processId") String processId);
}

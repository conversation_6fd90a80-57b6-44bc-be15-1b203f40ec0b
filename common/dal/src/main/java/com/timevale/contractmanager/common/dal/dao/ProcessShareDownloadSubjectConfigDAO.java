package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessShareDownloadSubjectConfigDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * The Table process_share_download_subject_config.
 * 主体分享功能配置信息表
 * <AUTHOR> Kunpeng
 */
public interface ProcessShareDownloadSubjectConfigDAO{

    String ALL_COLUMNS = "create_time,update_time,id,subject_gid,subject_oid,status,deleted,access_limit,access_status";

    @Insert({
            "<script>",
            "insert ignore into  process_share_download_subject_config(subject_gid,subject_oid,access_limit) values",
            "(#{subjectGid},#{subjectOid},#{accessLimit})",
            "</script>"
    })
    int insertIgnore(ProcessShareDownloadSubjectConfigDO entity);

    @Select({"select ",ALL_COLUMNS," from process_share_download_subject_config where subject_gid=#{subjectGid} and deleted=0 order by id desc limit 1"})
     ProcessShareDownloadSubjectConfigDO getBySubjectGid(@Param("subjectGid") String  subjectGid);


}

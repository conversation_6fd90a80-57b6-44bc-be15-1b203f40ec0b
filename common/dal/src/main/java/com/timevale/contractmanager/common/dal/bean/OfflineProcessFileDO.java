package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * The table. 纸质合同流程文件信息
 *
 * <AUTHOR> Kunpeng
 */
@Data
public class OfflineProcessFileDO {

    /** id 自增id. */
    private Long id;
    /** processId 合同流程id. */
    private String processId;
    /** fileId 文件id. */
    private String fileId;
    /** fileKey 文件filekey. */
    private String fileKey;
    /** fileName 文件名称. */
    private String fileName;
    /** fileType 文件类型，CONTRACT-合同文件，ATTACHMENT-附件. */
    private String fileType;
    /** ocrFileKey 文件ocr提取内容的filekey. */
    private String ocrFileKey;
    /** createTime 创建时间. */
    private Date createTime;
    /** updateTime 更新时间. */
    private Date updateTime;
}

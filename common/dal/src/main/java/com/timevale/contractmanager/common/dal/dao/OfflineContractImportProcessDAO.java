package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.OfflineContractImportProcessDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table offline_contract_import_process.
 * 纸质合同导入记录对应的合同信息
 * <AUTHOR> Kunpeng
 */
public interface OfflineContractImportProcessDAO {

    @Insert({
        "<script>",
        "INSERT INTO offline_contract_import_process(",
        " RECORD_ID,RECORD_PROCESS_ID,PROCESS_ID,PROCESS_INFO,STATUS) VALUES",
        " <foreach collection='list' item='item' separator=','>",
        "(#{item.recordId,jdbcType=VARCHAR}",
        " , #{item.recordProcessId,jdbcType=VARCHAR}",
        " , #{item.processId,jdbcType=VARCHAR}",
        " , #{item.processInfo,jdbcType=VARCHAR}",
        " , #{item.status,jdbcType=VARCHAR}",
        " )",
        "</foreach>",
        "</script>",
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int batchInsert(List<OfflineContractImportProcessDO> list);

    @Update("update offline_contract_import_process set process_info = #{processInfo} where record_process_id = #{recordProcessId}")
    int updateProcessInfo(OfflineContractImportProcessDO processDO);

    @Update("update offline_contract_import_process set status = 'IMPORTED', process_id = #{processId} where record_process_id = #{recordProcessId}")
    int updateProcessId(@Param("recordProcessId") String recordProcessId, @Param("processId") String processId);

    @Update("update offline_contract_import_process set status = 'IMPORT_FAILED', fail_reason = #{failReason}, fail_code=#{failCode} where record_process_id = #{recordProcessId}")
    int updateFailReason(@Param("recordProcessId") String recordProcessId, @Param("failReason") String failReason, @Param("failCode") String failCode);

    @Update({
        "<script>",
        "update offline_contract_import_process",
        " set status = #{newStatus}",
        " where record_id in",
        "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
        "#{item}",
        "</foreach>",
        " and status = #{originStatus}",
        "</script>",
    })
    int updateStatusByRecordIds(
            @Param("list") List<String> recordIds,
            @Param("originStatus") String originStatus,
            @Param("newStatus") String newStatus);

    @Delete({
            "<script>",
            "delete from offline_contract_import_process where record_id in",
            "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    int deleteByRecordIds(@Param("list") List<String> recordIds);

    @Select({
        "<script>",
        "select * from offline_contract_import_process where record_id in",
        "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
        "#{item}",
        "</foreach>",
        "<if test='statusList != null and statusList.size()'> ",
        " and status in",
        " <foreach collection='statusList' item='item' separator=',' open='(' close=')'>",
        "  #{item}",
        " </foreach>",
        "</if>",
        "</script>"
    })
    List<OfflineContractImportProcessDO> selectByRecordIds(
            @Param("list") List<String> recordIds, @Param("statusList") List<String> statusList);

    @Select("select * from offline_contract_import_process where record_process_id = #{recordProcessId} limit 1")
    OfflineContractImportProcessDO queryByRecordProcessId(String recordProcessId);

    @Select("select * from offline_contract_import_process where record_id = #{recordProcessId} and status = 'IMPORT_FAILED' and fail_code=#{failCode} limit 1")
    OfflineContractImportProcessDO queryCanRetryByRecordProcessId(@Param("recordProcessId") String recordProcessId, @Param("failCode") String failCode);

    @Select("select * from offline_contract_import_process where process_id = #{processId} limit 1")
    OfflineContractImportProcessDO queryByProcessId(String processId);

    @Select({
        "<script>",
        "select * from offline_contract_import_process where record_id = #{recordId} ",
        "<if test='statusList != null and statusList.size()'> ",
        " and status in",
        " <foreach collection='statusList' item='item' separator=',' open='(' close=')'>",
        "  #{item}",
        " </foreach>",
        "</if>",
        "</script>"
    })
    List<OfflineContractImportProcessDO> queryByRecordId(
            @Param("recordId") String recordId, @Param("statusList") List<String> statusList);
}

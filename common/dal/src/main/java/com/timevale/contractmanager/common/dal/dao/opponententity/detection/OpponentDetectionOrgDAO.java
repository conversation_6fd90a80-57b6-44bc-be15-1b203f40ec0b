package com.timevale.contractmanager.common.dal.dao.opponententity.detection;

import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionOrgDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-08-13 15:28
 */
public interface OpponentDetectionOrgDAO {

	@Select({"<script>",
			"SELECT count(org.tenant_gid) FROM",
			// 查询企业名称需要表连接
			"<if test= \"orgName!=null and orgName != ''\" >",
			"(select * from opponent_detection_org where tenant_gid = #{tenantGid} and detection_status = #{detectionStatus}) as org",
			"left join opponent_entity en on org.opponent_entity_id = en.uuid where ",
			" en.entity_name  LIKE CONCAT('%',#{orgName} ,'%')",
			"</if>",
			// 不查询企业名称需要表连接
			"<if test= \"orgName ==null or orgName == ''\">",
			"opponent_detection_org as org",
			"where tenant_gid = #{tenantGid} and detection_status = #{detectionStatus}",
			"</if>",
			"<if test='detectionTaskId!=null'> AND org.detection_task_id = #{detectionTaskId} </if>",
			"<if test='startDate!=null'> and org.create_time &gt;= #{startDate}</if>",
			"<if test='endDate!=null'> and org.create_time &lt;= #{endDate}</if>",
			"<if test='problemMapping!=null and problemMapping.size > 0 '> " +
					"AND (<foreach collection='problemMapping' item='item' separator='or'> org.problem_mapping &amp; #{item} </foreach>) </if>",
			"<if test='riskLevelMapping!=null and riskLevelMapping.size > 0 '> " +
					"AND (<foreach collection='riskLevelMapping' item='item' separator='or'> org.risk_level_mapping &amp; #{item} </foreach>) </if>",
			"</script>"
	})
	long countList(@Param("tenantGid") String tenantGid, @Param("problemMapping") List<Integer> problemMapping,
				   @Param("riskLevelMapping") List<Integer> riskLevelMapping, @Param("detectionTaskId") String detectionTaskId,
				   @Param("orgName") String orgName, @Param("detectionStatus") Integer detectionStatus,
				   @Param("startDate") Date startDate, @Param("endDate") Date endDate);


	@Select({"<script>",
			"SELECT org.detection_task_id as detectionTaskId, ",
			"org.detection_org_id as detectionOrgId , org.create_time , org.opponent_entity_id FROM",
//			查询企业名称需要表关联
			"<if test= \"orgName!=null and orgName != ''\">",
			"(select * from opponent_detection_org where tenant_gid = #{tenantGid} and detection_status = #{detectionStatus}) as org",
			"left join opponent_entity en on org.opponent_entity_id = en.uuid where ",
			" en.entity_name  LIKE CONCAT('%',#{orgName} ,'%')",
			"</if>",
//          不查企业名称不需要表关联
			"<if test= \"orgName ==null or orgName == ''\">",
			"opponent_detection_org as org",
			"where tenant_gid = #{tenantGid} and detection_status = #{detectionStatus}",
			"</if>",

			"<if test='detectionTaskId!=null'> AND org.detection_task_id = #{detectionTaskId} </if>",
			"<if test='startDate!=null'> and org.create_time &gt;= #{startDate}</if>",
			"<if test='endDate!=null'> and org.create_time &lt;= #{endDate}</if>",
			"<if test='problemMapping!=null and problemMapping.size > 0 '> " +
					"AND (<foreach collection='problemMapping' item='item' separator='or'> org.problem_mapping &amp; #{item} </foreach>) </if>",
			"<if test='riskLevelMapping!=null and riskLevelMapping.size > 0 '> " +
					"AND (<foreach collection='riskLevelMapping' item='item' separator='or'> org.risk_level_mapping &amp; #{item} </foreach>) </if>",
			"<if test='sort!=null and sort == 2 '> order by  org.create_time desc</if>",
			"<if test='sort!=null and sort == 1 '> order by  org.create_time asc</if>",
			"<if test='sort == null '>order by org.create_time desc, org.risk_Level_mapping desc</if> ",
			"limit #{offset},#{pageSize}",
			"</script>"
	})
	List<OpponentDetectionOrgDO> getList(
			@Param("tenantGid") String tenantGid, @Param("problemMapping") List<Integer> problemMapping,
			@Param("riskLevelMapping") List<Integer> riskLevelMapping, @Param("detectionTaskId") String detectionTaskId,
			@Param("orgName") String orgName, @Param("pageSize") Integer pageSize, @Param("offset") Integer offset,
			@Param("sort") Integer sort, @Param("detectionStatus") Integer detectionStatus,
			@Param("startDate") Date startDate, @Param("endDate") Date endDate);

	@Insert({"INSERT INTO opponent_detection_org (tenant_gid, detection_task_id, opponent_entity_id, detection_org_id," +
			"problem_mapping, risk_level_mapping, detection_status, elapsed_time, uni_code) " +
			"VALUES(#{tenantGid}, #{detectionTaskId}, #{opponentEntityId}, #{detectionOrgId}," +
			"#{problemMapping}, #{riskLevelMapping}, #{detectionStatus}, #{elapsedTime}, #{uniCode})"})
	int insert(OpponentDetectionOrgDO orgDO);

	/**
	 * 分组统计检测企业
	 * @param detectionTaskId
	 * @return
	 */
	@Select("select id as id, count(tenant_gid) as orgNum, detection_status as detectionStatus from opponent_detection_org " +
			"where detection_task_id = #{detectionTaskId} GROUP BY detection_status")
	List<OpponentDetectionOrgDO> countOrgs(@Param("detectionTaskId") String detectionTaskId);


	@Select("select * from opponent_detection_org where uni_code = #{uniCode} " +
			"and detection_task_id = #{taskId} and tenant_gid = #{tenantGid}")
	OpponentDetectionOrgDO getOrgByUniCode(@Param("uniCode") String uniCode,
										   @Param("taskId") String taskId,
										   @Param("tenantGid") String tenantGid);


	/**
	 *统计时间内的检测数量
	 * @param tenantGid
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@Select({"<script>",
			"select count(tenant_gid) from opponent_detection_org where tenant_gid=#{tenantGid} ",
			"and (detection_status =1 or detection_status = 3)",
			"<if test='startTime!=null'> and create_time &gt;= #{startTime}</if>",
			"<if test='endTime!=null'> and create_time &lt;= #{endTime}</if>",
			"</script>"
	})
	Integer sumOrgByTenantGid(@Param("tenantGid") String tenantGid,
							   @Param("startTime")Date startTime,
							   @Param("endTime") Date endTime);

	/**
	 * 统计时间段内检测企业数量
	 * @param tenantGid
	 * @param startTime
	 * @param endTime
	 * @param detectionStatus
	 * @param detectionTaskId
	 * @return
	 */
	@Select({"<script>",
			"select count(tenant_gid) from opponent_detection_org where tenant_gid=#{tenantGid} ",
			"<if test='startTime!=null'> and create_time &gt;= #{startTime}</if>",
			"<if test='endTime!=null'> and create_time &lt;= #{endTime}</if>",
			"<if test='detectionStatus!=null'> and detection_status = #{detectionStatus}</if>",
			"<if test='detectionTaskId!=null'> and detection_task_id = #{detectionTaskId}</if>",
			"</script>"
	})
	int sumOrgByDetectionStatus(@Param("tenantGid") String tenantGid,
									@Param("startTime")Date startTime,
									@Param("endTime") Date endTime,
									@Param("detectionStatus") Integer detectionStatus,
									@Param("detectionTaskId") String detectionTaskId);
}

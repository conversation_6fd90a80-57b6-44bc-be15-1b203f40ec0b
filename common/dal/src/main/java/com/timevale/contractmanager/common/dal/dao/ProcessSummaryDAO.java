package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessSummaryBaseDO;
import com.timevale.contractmanager.common.dal.bean.ProcessSummaryDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 合同摘要
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Mapper
public interface ProcessSummaryDAO {

    @Insert({
        "insert into process_summary(process_id, file_id, ai_job_id, ai_job_status, last_job_success, data_type, data, extend_data, subject_oid, subject_gid)",
        "values(#{processId}, #{fileId}, #{aiJobId}, #{aiJobStatus}, #{lastJobSuccess}, #{dataType}, #{data}, #{extendData}, #{subjectOid}, #{subjectGid})"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ProcessSummaryDO processSummaryDO);

    @Update({"update process_summary set `data` = #{data}, last_job_success=#{lastJobSuccess} where id = #{id}"})
    int updateJobDataById(@Param("id") Long id, @Param("data") String data, @Param("lastJobSuccess") Integer lastJobSuccess);

    @Update({
        "update process_summary set data = #{data}, ai_job_status = #{aiJobStatus}, last_job_success = #{lastJobSuccess}, extend_data=#{extendData}, retry_times=retry_times+1 where id = #{id}"
    })
    int updateDataAndAiJobStatusById(
            @Param("id") Long id,
            @Param("data") String data,
            @Param("aiJobStatus") String aiJobStatus,
            @Param("lastJobSuccess") Integer lastJobSuccess,
            @Param("extendData") String extendData);

    @Update({"update process_summary set last_job_success = #{lastJobSuccess}, ai_job_status = #{aiJobStatus} where id = #{id}"})
    int updateLastJobSuccessById(
            @Param("id") Long id, @Param("lastJobSuccess") Integer lastJobSuccess, @Param("aiJobStatus") String aiJobStatus);

    @Update({
        "<script>",
        "update process_summary set ai_job_status = #{aiJobStatus}",
        "<if test=\"aiJobId != null and aiJobId != ''\">,ai_job_id = #{aiJobId}</if>",
        "<if test=\"extendData != null and extendData != ''\">,extend_data = #{extendData}</if>",
        "where id = #{id}",
        "</script>"
    })
    int updateAiJobInfoById(ProcessSummaryDO processSummaryDO);

    @Select({
        "select * from process_summary where process_id = #{processId} and file_id = #{fileId} and subject_gid = #{subjectGid} and data_type = #{dataType}"
    })
    ProcessSummaryDO getByProcessIdFileIdSubjectGIdDataType(
            @Param("processId") String processId,
            @Param("fileId") String fileId,
            @Param("subjectGid") String subjectGid,
            @Param("dataType") String dataType);

    @Select({
        "select * from process_summary where process_id = #{processId} and file_id = #{fileId} and subject_gid = #{subjectGid}",
    })
    List<ProcessSummaryDO> listByProcessIdFileIdAndGid(
            @Param("processId") String processId,
            @Param("fileId") String fileId,
            @Param("subjectGid") String subjectGid);

    @Select({
            "<script>",
            "select * from process_summary where process_id = #{processId} and subject_gid = #{subjectGid}",
            "</script>"
    })
    List<ProcessSummaryDO> listByProcessIdAndGid(
            @Param("processId") String processId,
            @Param("subjectGid") String subjectGid
    );

    @Select({"select * from process_summary where id=#{id}"})
    ProcessSummaryDO getById(@Param("id") Long id);

    @Delete({
            "delete from process_summary where process_id = #{processId} and file_id = #{fileId}"
    })
    int deleteByProcessIdAndFileId(@Param("processId") String processId, @Param("fileId") String fileId);
}

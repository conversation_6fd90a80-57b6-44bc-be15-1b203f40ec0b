package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessDO;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * The Table process. 合同管理主流程表
 *
 * <AUTHOR> Kunpeng
 */
public interface ProcessDAO {

    /**
     * desc:插入表:process.<br>
     *
     * @param entity entity
     * @return int
     */
    @Insert({
        "<script>",
        "insert into process(process_id,process_title,process_version,process_instance_id,create_type,app_id,status,initiator_oid,initiator_gid,initiator_uid,subject_oid,subject_gid,subject_uid,process_group_id,third_process_no,process_end_time,contract_end_time) values",
        "(#{processId},#{processTitle},#{processVersion},#{processInstanceId},#{createType},#{appId},#{status},#{initiatorOid},#{initiatorGid},#{initiatorUid},#{subjectOid},#{subjectGid},#{subjectUid},#{processGroupId},#{thirdProcessNo},#{processEndTime},#{contractEndTime})",
        "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(ProcessDO entity);

    /**
     * desc:根据唯一约束IdxProcessId更新表:process.<br>
     *
     * @param entity entity
     * @return int
     */
    @Update({
        "<script>",
        "update process",
        "<trim prefix=\"set\" suffixOverrides=\",\">",
        "<if test=\"status != null\"> status = #{status} ,</if>",
        "<if test=\"deleted != null\"> deleted = #{deleted}, </if>",
        "<if test=\"processTitle != null\"> process_title = #{processTitle}, </if>",
        "<if test=\"processEndTime != null\"> process_end_time = #{processEndTime}, </if>",
        "<if test=\"contractEndTime != null\"> contract_end_time = #{contractEndTime}, </if>",
        "update_time = now()",
        "where process_id = #{processId}",
        "</trim>",
        "</script>"
    })
    int updateByIdxProcessId(ProcessDO entity);

    @Update({
            "<script>",
            "update process",
            "<trim prefix=\"set\" suffixOverrides=\",\">",
            "<if test=\"status != null\"> status = #{status} ,</if>",
            "process_group_id = #{processGroupId}, ",
            "update_time = now() ",
            "where process_id = #{processId}",
            "</trim>",
            "</script>"
    })
    int updateGroupByIdxProcessId(ProcessDO entity);

    @Update({
            "<script>",
            "update process a join (",
            "<foreach collection='list' item='item' separator='UNION'>",
            " SELECT #{item.id} AS id, #{item.contractEndTime} AS contract_end_time ",
            "</foreach>",
            ") b USING(id)",
            "set a.contract_end_time = b.contract_end_time",
            "</script>"
    })
    int updateContractEndTime(@Param("list") List<ProcessDO> list);

    /**
     * desc:根据唯一约束IdxProcessId获取数据:process.<br>
     *
     * @param processId processId
     * @return ProcessDO
     */
    @Select({
        "<script>",
        "select * from process where process_id=#{processId} and deleted = 0",
        "</script>"
    })
    ProcessDO getByIdxProcessId(String processId);

    @Select({
        "<script>",
        "select * from process where process_id in ",
        "<foreach item='item' index='index' collection='list' open='(' separator=',' close=')'>",
        "#{item}",
        "</foreach>",
        "</script>"
    })
    List<ProcessDO> getBatchProcess(@Param("list") List<String> list);

    @Update({
            "<script>",
            "update process ",
            "set initiator_oid = #{initiatorOid} ,",
            "initiator_gid = #{initiatorGid}, ",
            "initiator_uid = #{initiatorUid}, ",
            "update_time = now() ",
            "where id = #{id} and subject_oid = #{subjectOid}",
            "</script>"
    })
    int updateInitiator(ProcessDO processDO);

    @Delete("delete from process where process_id=#{processId}")
    int deleteProcess(String processId);
}

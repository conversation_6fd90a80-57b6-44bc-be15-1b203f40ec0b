package com.timevale.contractmanager.common.dal.dao.fulfillment;

import com.timevale.contractmanager.common.dal.bean.fulfillment.ContractFulfillmentRuleDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * ContractFulfillmentRuleDAO
 *
 * <AUTHOR>
 * @since 2023/10/11 11:10 上午
 */
public interface ContractFulfillmentRuleDAO {

    @Insert({"INSERT INTO contract_fulfillment_rule (rule_id,tenant_oid,tenant_gid,name,type,",
            "status,type_name,notice_rule,form_id,scope_type,notice_channel,create_way,create_by_oid, modified_by_oid)",
            "VALUES(#{ruleId},#{tenantOid},#{tenantGid},#{name},#{type},#{status},",
            "#{typeName},#{noticeRule},#{formId},#{scopeType},#{noticeChannel},#{createWay},#{createByOid},#{modifiedByOid})"})
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(ContractFulfillmentRuleDO ruleDO);

    @Update({"update contract_fulfillment_rule set name = #{name}, type = #{type}, type_name = #{typeName}, status = #{status}, notice_rule = #{noticeRule}, ",
            "form_id = #{formId}, scope_type = #{scopeType}, notice_channel = #{noticeChannel}, modified_by_oid = #{modifiedByOid} ",
            "where rule_id = #{ruleId}"})
    void update(ContractFulfillmentRuleDO ruleDO);

    @Update("update contract_fulfillment_rule set deleted = 1, modified_by_oid = #{modifiedByOid} where rule_id = #{ruleId}")
    void delete(@Param("ruleId") String ruleId, @Param("modifiedByOid") String modifiedByOid);

    @Delete("update contract_fulfillment_rule set status = #{status}, modified_by_oid = #{modifiedByOid}, modified_time = modified_time  where rule_id=#{ruleId}")
    void updateStatus(@Param("ruleId") String ruleId, @Param("status") String status, @Param("modifiedByOid") String modifiedByOid);

    @Select({
            "<script>",
            "select * from contract_fulfillment_rule where tenant_gid = #{tenantGid} and rule_id = #{ruleId} and deleted = 0",
            "</script>"
    })
    ContractFulfillmentRuleDO getByRuleId(@Param("tenantGid") String tenantGid, @Param("ruleId") String ruleId);

    @Select({
            "<script>",
            "select * from contract_fulfillment_rule where tenant_gid = #{tenantGid} and deleted = 0",
            "<if test='typeName != null and typeName != \"\" '> and type_name = #{typeName} </if>",
            "<if test='status != null and status != \"\" '> and status = #{status} </if>",
            "<if test='name != null and name != \"\" '> and name = #{name} </if>",
            "ORDER BY create_time DESC LIMIT #{offset},#{pageSize}",
            "</script>"
    })
    List<ContractFulfillmentRuleDO> list(@Param("tenantGid") String tenantGid, @Param("status") String status, @Param("typeName") String typeName, @Param("name") String name,
            @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    @Select({
            "<script>",
            "select count(*) from contract_fulfillment_rule where tenant_gid = #{tenantGid} and deleted = 0",
            "<if test='typeName != null and typeName != \"\" '> and type_name = #{typeName} </if>",
            "<if test='status != null and status != \"\" '> and status = #{status} </if>",
            "<if test='name != null and name != \"\" '> and name = #{name} </if>",
            "</script>"
    })
    Long count(@Param("tenantGid") String tenantGid, @Param("status") String status, @Param("typeName") String typeName, @Param("name") String name);

    @Select({
            "<script>",
            "select count(*) from contract_fulfillment_rule where status = #{status} and deleted = 0",
            "<if test='startId != null'> and id <![CDATA[ >= ]]> #{startId} </if>",
            "<if test='endId != null'> and id <![CDATA[ < ]]> #{endId} </if>",
            "</script>"
    })
    Long shardingCount(@Param("startId") Long startId, @Param("endId") Long endId, @Param("status") String status);

    @Select({
            "<script>",
            "select * from contract_fulfillment_rule where status = #{status} and deleted = 0",
            "<if test='startId != null'> and id <![CDATA[ >= ]]> #{startId} </if>",
            "<if test='endId != null'> and id <![CDATA[ < ]]> #{endId} </if>",
            "ORDER BY id DESC LIMIT #{offset},#{pageSize}",
            "</script>"
    })
    List<ContractFulfillmentRuleDO> shardingList(@Param("startId") Long startId, @Param("endId") Long endId, @Param("status") String status,
            @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    @Select({
            "<script>",
            "select distinct(type_name) from contract_fulfillment_rule where tenant_gid = #{tenantGid} and type_name != '' and  deleted = 0",
            "</script>"
    })
    List<String> typeList(@Param("tenantGid") String tenantGid);

    @Select({
            "<script>",
            "select * from contract_fulfillment_rule where tenant_gid = #{tenantGid} and create_way = 'system'",
            "</script>"
    })
    ContractFulfillmentRuleDO getSystemRule(@Param("tenantGid") String tenantGid);

    /**
     * 历史数据刷新使用
     * @return
     */
    @Select({"<script> select count(*) from contract_fulfillment_rule where deleted = 0 </script>"})
    Long countAllRules();

    /**
     * 历史数据刷新使用
     * @return
     */
    @Select({
        "<script> select * from contract_fulfillment_rule where deleted = 0 LIMIT #{offset},#{pageSize} </script>"
    })
    List<ContractFulfillmentRuleDO> queryAllRules(@Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    /**
     * 更新履约规则查询语句
     * @param ruleId
     * @param noticeQueryScript
     */
    @Update({"update contract_fulfillment_rule set notice_query_script = #{noticeQueryScript} where rule_id = #{ruleId}"})
    void updateQueryScript(@Param("ruleId") String ruleId, @Param("noticeQueryScript") String noticeQueryScript);

}

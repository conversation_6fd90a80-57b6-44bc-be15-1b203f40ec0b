package com.timevale.contractmanager.common.dal.bean.autoarchive;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * @Author:jianyang
 * @since 2021-05-06 19:20
 */
@Data
@Builder
public class AutoArchiveOperatorDO {
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 自动分类规则表Id
	 */
	private String archiveRuleId;
	/**
	 * 算子Id
	 */
	private String operatorId;
	/**
	 * 父算子id
	 */
	private String parentOperatorId;
	/**
	 * 算子参数
	 */
	private String conditionParams;
	/**
	 * 算子类型
	 */
	private Integer operatorType;
	private String fieldId;
	private String fieldName;
	private Integer fieldType;
	private Integer deleted;
	private Integer matchType;
	/**
	 * 排序值
	 */
	private Integer operatorWeight;
	@Tolerate
	public AutoArchiveOperatorDO(){}
}

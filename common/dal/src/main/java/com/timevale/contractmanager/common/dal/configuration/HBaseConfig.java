package com.timevale.contractmanager.common.dal.configuration;

import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class HBaseConfig {

    private static final String HBASE_ZK_QUORUM_KEY = "hbase.zk-quorum";
    private static final String HBASE_USERNAME_KEY = "hbase.username";
    private static final String HBASE_PASSWORD_KEY = "hbase.password";

    private static final String HBASE_TABLE_PREFIX_KEY = "hbase.table-prefix";

    /** hbase集群地址 */
    private   String HBASE_ZK_QUORUM;

    /** hbase用户名 */
    private  String HBASE_USERNAME;

    /** hbase密码 */
    private  String HBASE_PASSWORD;

    /** hbase表名前缀，该配置不能修改 */
    private  String HBASE_TABLE_PREFIX;

    @Value("${" + HBASE_ZK_QUORUM_KEY + "}")
    public void setHbaseZkQuorum(String hbaseZkQuorum) {
        HBASE_ZK_QUORUM = hbaseZkQuorum;
    }

    @Value("${" + HBASE_USERNAME_KEY + "}")
    public void setHbaseUsername(String hbaseUsername) {
        HBASE_USERNAME = hbaseUsername;
    }

    @Value("${" + HBASE_PASSWORD_KEY + "}")
    public void setHbasePassword(String hbasePassword) {
        HBASE_PASSWORD = hbasePassword;
    }

    @Value("${" + HBASE_TABLE_PREFIX_KEY + ":}")
    public void setHbaseTablePrefix(String hbaseTablePrefix) {
        HBASE_TABLE_PREFIX = hbaseTablePrefix;
    }

    @PuppeteerConfigChangeListener
    private void configChangeListener(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged(HBASE_ZK_QUORUM_KEY)) {
            HBASE_ZK_QUORUM = changeEvent.getChange(HBASE_ZK_QUORUM_KEY).getNewValue();
        }
        if (changeEvent.isChanged(HBASE_USERNAME_KEY)) {
            HBASE_USERNAME = changeEvent.getChange(HBASE_USERNAME_KEY).getNewValue();
        }
        if (changeEvent.isChanged(HBASE_PASSWORD_KEY)) {
            HBASE_PASSWORD = changeEvent.getChange(HBASE_PASSWORD_KEY).getNewValue();
        }
    }

    public String getHBASE_ZK_QUORUM() {
        return HBASE_ZK_QUORUM;
    }

    public String getHBASE_USERNAME() {
        return HBASE_USERNAME;
    }

    public String getHBASE_PASSWORD() {
        return HBASE_PASSWORD;
    }

    public String getHBASE_TABLE_PREFIX() {
        return HBASE_TABLE_PREFIX;
    }
}

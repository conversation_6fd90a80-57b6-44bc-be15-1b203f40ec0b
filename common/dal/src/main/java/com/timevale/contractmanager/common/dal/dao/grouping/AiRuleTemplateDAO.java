package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.AiRuleTemplateDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * The Table ai_rule_template.
 * 台账规则模板表
 * <AUTHOR> Kunpeng
 */
public interface AiRuleTemplateDAO{

    @Select("SELECT ID,UUID,OID,FILE_ID,FILE_NAME,TYPE,TEMPLATE_ID,MENU_ID,RULE_ID,analysis_flag FROM ai_rule_template WHERE RULE_ID = #{ruleId}")
    public List<AiRuleTemplateDO> queryByRuleId(String ruleId);

    @Update("UPDATE ai_rule_template SET MENU_ID = #{menuId} WHERE UUID = #{ruleTemplateId}")
    public int updateMenuId(@Param("ruleTemplateId") String ruleTemplateId,@Param("menuId") String menuId);
}

package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * @author: du<PERSON>
 * @since: 2021/11/15 11:23 上午
 **/
@Data
public class UserDownloadConfigDO {
    private Long id;
    /**
     * 用户oid
     */
    private String oid;
    /**
     * 类型，1 系统默认 2 自定义
     */
    private Integer type;
    private String configDetail;
    private Date createTime;
    private Date updateTime;
}

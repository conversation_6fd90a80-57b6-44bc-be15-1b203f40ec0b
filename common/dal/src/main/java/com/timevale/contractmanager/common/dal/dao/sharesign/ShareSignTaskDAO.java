package com.timevale.contractmanager.common.dal.dao.sharesign;

import com.timevale.contractmanager.common.dal.bean.sharesign.ShareSignTaskDO;
import com.timevale.contractmanager.common.dal.query.shareSign.ShareSignTaskListParam;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * The Table share_sign_task.
 * 分享签任务表
 * <AUTHOR> Kunpeng
 */
public interface ShareSignTaskDAO{

    @Insert(
            "INSERT INTO share_sign_task(\n"
                    + "            TASK_ID\n"
                    + "            ,TASK_NAME\n"
                    + "            ,TASK_FROM\n"
                    + "            ,ACCOUNT_GID\n"
                    + "            ,ACCOUNT_OID\n"
                    + "            ,SHARE_BIZ_ID\n"
                    + "            ,SUBJECT_GID\n"
                    + "            ,SUBJECT_OID\n"
                    + "            ,ACCOUNT_NAME\n"
                    + "            ,SUBJECT_NAME\n"
                    + "            ,SHARE_TEMPLATE_ID\n"
                    + "            ,ORIGIN_TEMPLATE_ID\n"
                    + "            ,SHARE_TYPE\n"
                    + "            ,SHARE_TOTAL\n"
                    + "            ,END_TIME\n"
                    + "            ,VERSION\n"
                    + "            ,PRIVATE_SHARE\n"
                    + "            ,BIZ_EXTRA\n"
                    + "        )VALUES(\n"
                    + "             #{taskId,jdbcType=VARCHAR}\n"
                    + "            , #{taskName,jdbcType=VARCHAR}\n"
                    + "            , #{taskFrom,jdbcType=TINYINT}\n"
                    + "            , #{accountGid,jdbcType=VARCHAR}\n"
                    + "            , #{accountOid,jdbcType=VARCHAR}\n"
                    + "            , #{shareBizId,jdbcType=VARCHAR}\n"
                    + "            , #{subjectGid,jdbcType=VARCHAR}\n"
                    + "            , #{subjectOid,jdbcType=VARCHAR}\n"
                    + "            , #{accountName,jdbcType=VARCHAR}\n"
                    + "            , #{subjectName,jdbcType=VARCHAR}\n"
                    + "            , #{shareTemplateId,jdbcType=VARCHAR}\n"
                    + "            , #{originTemplateId,jdbcType=VARCHAR}\n"
                    + "            , #{shareType,jdbcType=TINYINT}\n"
                    + "            , #{shareTotal,jdbcType=INTEGER}\n"
                    + "            , #{endTime,jdbcType=TIMESTAMP}\n"
                    + "            , #{version,jdbcType=TINYINT}\n"
                    + "            , #{privateShare,jdbcType=TINYINT}\n"
                    + "            , #{bizExtra}\n"
                    + "        )")
    @Options(useGeneratedKeys = true, keyColumn = "id")
    public int insert(ShareSignTaskDO entity);

    @Update("update share_sign_task set status = #{status} where task_id = #{taskId}")
    public int updateStatus(@Param("taskId") String taskId, @Param("status") Integer status);

    @Update({
        "<script>",
        " UPDATE ",
        " share_sign_task ",
        " <set> ",
        "   update_time = now(),",
        "   <if test=\"privateShare != null\"> private_share = #{privateShare} </if>",
        " </set> ",
        " WHERE ",
        "   task_id = #{taskId}",
        "</script>",
    })
    int updateByTaskId(ShareSignTaskDO updateData);

    @Update("update share_sign_task set share_total = #{total} where task_id = #{taskId}")
    public int updateTotal(@Param("taskId") String taskId, @Param("total") Integer total);

    @Update("update share_sign_task set endTime = #{endTime} where task_id = #{taskId}")
    public int updateEndTime(@Param("taskId") String taskId, @Param("endTime") Date endTime);

    @Update("update share_sign_task set share_num = share_num + 1 where task_id = #{taskId} and share_num < share_total")
    public int upgradeNum(@Param("taskId") String taskId);

    @Update("update share_sign_task set share_num = share_num - 1 where task_id = #{taskId} and share_num > 0")
    public int degradeNum(@Param("taskId") String taskId);

    @Update("update share_sign_task set share_num_occupied = share_num_occupied + 1 where task_id = #{taskId} and share_num_occupied < share_total")
    public int upgradeNumOccupied(@Param("taskId") String taskId);

    @Update("update share_sign_task set share_num_occupied = share_num_occupied - 1 where task_id = #{taskId} and share_num_occupied > 0")
    public int degradeNumOccupied(@Param("taskId") String taskId);

    @Update("update share_sign_task set share_done = share_done + 1 where task_id = #{taskId} and share_done < share_num")
    public int upgradeDone(@Param("taskId") String taskId);

    @Select("select * from share_sign_task where task_id = #{taskId}")
    public ShareSignTaskDO getByTaskId(String taskId);

    @Select("select * from share_sign_task where share_biz_id = #{shareBizId} and share_type = #{shareType} limit 1")
    public ShareSignTaskDO queryByShareBizId(@Param("shareBizId") String shareBizId, @Param("shareType") Integer shareType);

    @Select({
            "<script>",
            "select * from share_sign_task where share_biz_id in ",
            "(<foreach collection='shareBizIds' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "<if test=\"shareType != null\"> and share_type = #{shareType}</if>",
            " order by id desc",
            "</script>"})
    public List<ShareSignTaskDO> queryByShareBizIds(@Param("shareBizIds") List<String> shareBizIds, @Param("shareType") Integer shareType);

    @Select("select * from share_sign_task where account_gid = #{accountGid} and subject_gid = #{subjectGid} order by id desc")
    public List<ShareSignTaskDO> queryByAccountGid(@Param("accountGid") String accountGid, @Param("subjectGid") String subjectGid);

    @Select("select count(*) from share_sign_task where account_gid = #{accountGid} and share_type = #{shareType} and status = #{status}")
    int countTaskByAccountGid(@Param("accountGid") String accountGid, @Param("status") Integer status, @Param("shareType")Integer shareType);

    @Select("<script>" +
            "select * from share_sign_task <where>" +
            "<if test=\"accountGid != null and accountGid != ''\"> and account_gid = #{accountGid} </if>" +
            "<if test=\"subjectGid != null and subjectGid != ''\"> and subject_gid = #{subjectGid} </if>" +
            "<if test=\"shareType != null\"> and share_type =#{shareType} </if>" +
            "<if test=\"status != null \"> and status =#{status} </if>" +
            "</where> order by create_time desc" +
            "</script>")
    List<ShareSignTaskDO> getList(ShareSignTaskListParam param);

    @Select("<script>" +
            "select count(1) from share_sign_task where share_template_id = #{shareTemplateId}" +
            "</script>")
    int countByShareTemplateId(String shareTemplateId);

    @Select({
        "<script>",
        " SELECT status, count(*) AS count ",
        " FROM share_sign_task ",
        " WHERE account_gid = #{accountGid} AND subject_gid = #{subjectGid} ",
        " GROUP BY status",
        "</script>"
    })
    List<Map<String, Object>> countByStatus(
            @Param("accountGid") String accountGid, @Param("subjectGid") String subjectGid);
}

package com.timevale.contractmanager.common.dal.bean.opponententity.detection;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-08-12 11:40
 */
@Data
@Builder
public class OpponentDetectionTaskDO {
	/**
	 * id 自增主键.
	 */
	private Long id;
	/**
	 * deleted 是否删除 0-否；1-是
	 */
	private Integer deleted;
	/**
	 * createTime 创建时间.
	 */
	private Date createTime;
	/**
	 * modifyTime 更新时间.
	 */
	private Date modifyTime;
	/**
	 *tenantGid 租户gid
	 */
	private String tenantGid;

	/**
	 * 报告的问题数量
	 */
	private Long reportProblemNum;

	/**
	 * 任务id
	 */
	private String detectionTaskId;
	/**
	 * 检测总数
	 */
	private Integer detectionTotality;
	/**
	 * 检测完成数量
	 */
	private Integer detectionQuantityCompletion;

	/**
	 * 运行时间
	 */
	private Long elapsedTime;
	/**
	 * 任务状态
	 */
	private Integer taskStatus;

	/**
	 * 检测范围
	 */
	private String detectionScope;

	/**
	 * 风险企业数量
	 */
	private Integer ventureBusinessNum;

	/**
	 * 任务类型
	 */
	private Integer taskType;

	/**
	 * 经营范围
	 */
	private String businessScope;

	/**
	 * 发起人oid
	 */
	private String createByOid;

	@Tolerate
	public OpponentDetectionTaskDO(){}
}

package com.timevale.contractmanager.common.dal.bean.grouping;

import java.util.Date;

/**
 * The table.
 * 合同归档记录表
 * <AUTHOR> Kunpeng
 */
public class GroupingInfoDO{

    /**
     * createTime CREATE_TIME.
     */
    private Date createTime;
    /**
     * updateTime UPDATE_TIME.
     */
    private Date updateTime;
    /**
     * id ID.
     */
    private Long id;
    /**
     * menuId 归档到对应的菜单ID.
     */
    private String menuId;
    /**
     * processId PROCESS_ID.
     */
    private String processId;
    /**
     * contractNo 合同编号.
     */
    private String contractNo;
    /**
     * subjectGid SUBJECT_GID.
     */
    private String subjectGid;
    /**
     * subjectOid 主体Oid.
     */
    private String subjectOid;
    /**
     * operatorGid OPERATOR_GID.
     */
    private String operatorGid;
    /**
     * operatorOid 操作人Oid.
     */
    private String operatorOid;

    /**
     * Set createTime CREATE_TIME.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime CREATE_TIME.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime UPDATE_TIME.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime UPDATE_TIME.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set menuId 归档到对应的菜单ID.
     */
    public void setMenuId(String menuId){
        this.menuId = menuId;
    }

    /**
     * Get menuId 归档到对应的菜单ID.
     *
     * @return the string
     */
    public String getMenuId(){
        return menuId;
    }

    /**
     * Set processId PROCESS_ID.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId PROCESS_ID.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    /**
     * Set contractNo 合同编号.
     */
    public void setContractNo(String contractNo){
        this.contractNo = contractNo;
    }

    /**
     * Get contractNo 合同编号.
     *
     * @return the string
     */
    public String getContractNo(){
        return contractNo;
    }

    /**
     * Set subjectGid SUBJECT_GID.
     */
    public void setSubjectGid(String subjectGid){
        this.subjectGid = subjectGid;
    }

    /**
     * Get subjectGid SUBJECT_GID.
     *
     * @return the string
     */
    public String getSubjectGid(){
        return subjectGid;
    }

    /**
     * Set subjectOid 主体Oid.
     */
    public void setSubjectOid(String subjectOid){
        this.subjectOid = subjectOid;
    }

    /**
     * Get subjectOid 主体Oid.
     *
     * @return the string
     */
    public String getSubjectOid(){
        return subjectOid;
    }

    /**
     * Set operatorGid OPERATOR_GID.
     */
    public void setOperatorGid(String operatorGid){
        this.operatorGid = operatorGid;
    }

    /**
     * Get operatorGid OPERATOR_GID.
     *
     * @return the string
     */
    public String getOperatorGid(){
        return operatorGid;
    }

    /**
     * Set operatorOid 操作人Oid.
     */
    public void setOperatorOid(String operatorOid){
        this.operatorOid = operatorOid;
    }

    /**
     * Get operatorOid 操作人Oid.
     *
     * @return the string
     */
    public String getOperatorOid(){
        return operatorOid;
    }
}

package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessRelationDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 流程关联关系表
 *
 * <AUTHOR>
 * @since 2021-06-08
 */
public interface ProcessRelationDAO {

    /**
     * 批量插入
     *
     * @param list list
     * @return int
     */
    @Insert({
        "<script>",
        "INSERT INTO process_relation (type,origin_process_id,process_id,file_id,status,origin_status,remark,"
                + "gmt_create,"
                + "gmt_modified) VALUES",
        "<foreach collection='list' item='item' separator=','>",
        "(#{item.type,jdbcType=TINYINT},#{item.originProcessId,jdbcType=VARCHAR},#{item.processId,jdbcType=VARCHAR},#{item.fileId,jdbcType=VARCHAR}, ",
        "#{item.status,jdbcType=TINYINT},#{item.originStatus,jdbcType=TINYINT},#{item.remark,jdbcType=VARCHAR},now(),now())",
        "</foreach>",
        "</script>"
    })
    int insertBatch(@Param("list") List<ProcessRelationDO> list);

    /**
     * 根据原流程id及文件id查询
     *
     * @param originProcessId 原流程id
     * @param fileIdList 关联文档id
     * @return ProcessRelationDO
     */
    @Select({
        "<script>",
        "select * from process_relation where origin_process_id=#{originProcessId}",
        " <if test='type != null'> ",
        "   and type = #{type}",
        " </if>",
        " <if test='fileIdList != null and fileIdList.size() > 0'> ",
        "   and file_id in ",
        "    <foreach collection='fileIdList' item='item' open='(' separator=',' close=')'>",
        "        #{item} ",
        "    </foreach>",
        " </if>",
        "</script>"
    })
    List<ProcessRelationDO> queryByOriginProcessIdAndFileId(
            @Param("originProcessId") String originProcessId,
            @Param("type") Integer type,
            @Param("fileIdList") List<String> fileIdList);

    /**
     * 根据原流程id及文件id查询
     *
     * @param originProcessId 原流程id
     * @param fileIdList 关联文档id
     * @return ProcessRelationDO
     */
    @Select({
        "<script>",
        "select * from process_relation where origin_process_id=#{originProcessId}",
        " <if test='type != null'> ",
        "   and type = #{type}",
        " </if>",
        " <if test='fileIdList != null and fileIdList.size() > 0'> ",
        "   and file_id in ",
        "    <foreach collection='fileIdList' item='item' open='(' separator=',' close=')'>",
        "        #{item} ",
        "    </foreach>",
        " </if>",
        " and status in(1,2)",
        "</script>"
    })
    List<ProcessRelationDO> queryValidByOriginProcessIdAndFileId(
            @Param("originProcessId") String originProcessId,
            @Param("type") Integer type,
            @Param("fileIdList") List<String> fileIdList);

    @Select({
            "<script>",
            "select COUNT(DISTINCT(process_id)) from process_relation where origin_process_id=#{originProcessId}",
            " <if test='type != null'> ",
            "   and type = #{type}",
            " </if>",
            "</script>"
    })
    int countByOriginProcessIdAndType(
            @Param("originProcessId") String originProcessId,
            @Param("type") Integer type);

    /**
     * 根据原流程id及状态查询
     *
     * @param originProcessId 原流程id
     * @param statusList 状态
     * @return ProcessRelationDO
     */
    @Select({
        "<script>",
        "select * from process_relation where origin_process_id=#{originProcessId}",
        " <if test='type != null'> ",
        "   and type = #{type}",
        " </if>",
        " <if test='statusList != null and statusList.size() > 0'> ",
        "   and status in ",
        "    <foreach collection='statusList' item='item' open='(' separator=',' close=')'>",
        "        #{item} ",
        "    </foreach>",
        " </if>",
        "</script>"
    })
    List<ProcessRelationDO> queryByOriginProcessIdAndStatus(
            @Param("originProcessId") String originProcessId,
            @Param("type") Integer type,
            @Param("statusList") List<Integer> statusList);

    /**
     * 根据原流程id及状态查询
     *
     * @param originProcessId 原流程id
     * @param type 关联类型
     * @return ProcessRelationDO
     */
    @Select({
        "<script>",
        "select * from process_relation where origin_process_id=#{originProcessId}",
        " <if test='type != null'> ",
        "   and type = #{type}",
        " </if>",
        " and status in(1,2)",
        "</script>"
    })
    List<ProcessRelationDO> queryValidByOriginProcessId(
            @Param("originProcessId") String originProcessId, @Param("type") Integer type);

    /**
     * 根据原流程id及状态查询
     *
     * @param originProcessId 原流程id
     * @param type 关联类型
     * @return ProcessRelationDO
     */
    @Select({
        "<script>",
        "select * from process_relation where origin_process_id=#{originProcessId}",
        " <if test='type != null'> ",
        "   and type = #{type}",
        " </if>",
        "</script>"
    })
    List<ProcessRelationDO> queryAllByOriginProcessId(
            @Param("originProcessId") String originProcessId, @Param("type") Integer type);

    /**
     * 根据原流程id及状态查询
     *
     * @param originProcessIds 原流程ids
     * @return ProcessRelationDO
     */
    @Select({
            "<script>",
            "select id,type,origin_process_id,process_id,file_id,status,origin_status,gmt_create,remark from process_relation where type = #{type} ",
            " <if test='originProcessIds != null and originProcessIds.size() > 0'> ",
            "   and origin_process_id in ",
            "    <foreach collection='originProcessIds' item='item' open='(' separator=',' close=')'>",
            "        #{item} ",
            "    </foreach>",
            " </if> " ,
            " <if test='statusList != null and statusList.size() > 0'> ",
            "   and status in ",
            "    <foreach collection='statusList' item='status' open='(' separator=',' close=')'>",
            "        #{status} ",
            "    </foreach>",
            " </if> " ,
            "limit 1000 ",
            "</script>"
    })
    List<ProcessRelationDO> queryAllByOriginProcessIdsAndType(
            @Param("originProcessIds") List<String> originProcessIds,@Param("type") Integer type,@Param("statusList") List<Integer> statusList);

    /**
     * 根据关联流程id查询
     *
     * @param processId 关联流程id
     * @return ProcessRelationDO 结果
     */
    @Select({
        "<script>",
        "select * from process_relation where process_id=#{processId}",
        " and status in ",
        "  <foreach collection='statusList' item='item' open='(' separator=',' close=')'>",
        "     #{item} ",
        "  </foreach>",
        "</script>"
    })
    List<ProcessRelationDO> queryByProcessIdAndStatus(
            @Param("processId") String processId, @Param("statusList") List<Integer> statusList);

    /**
     * 根据关联流程id查询原流程
     *
     * @param processId 关联流程id
     * @return ProcessRelationDO 结果
     */
    @Select({
        "<script>",
        "select * from process_relation where process_id=#{processId}",
        "</script>"
    })
    List<ProcessRelationDO> queryByProcessId(@Param("processId") String processId);

    /**
     * 根据关联流程id更新状态
     *
     * @param processId 关联流程id
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @return int
     */
    @Update({
        "update process_relation ",
        "set status = #{newStatus}, ",
        "gmt_modified = now() ",
        "where process_id = #{processId} and status = #{oldStatus}",
    })
    int updateProcessRelationStatus(
            @Param("processId") String processId,
            @Param("oldStatus") Integer oldStatus,
            @Param("newStatus") Integer newStatus);
}

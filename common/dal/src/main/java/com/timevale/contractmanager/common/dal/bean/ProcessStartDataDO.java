package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

@Data
public class ProcessStartDataDO {

    private Long id;

    /**
     * 数据Id
     */
    private String dataId;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Integer deleted;

    /**
     * 企业oid
     */
    private String subjectOid;

    /**
     * 企业gid
     */
    private String subjectGid;

    /**
     * 拥有者oid
     */
    private String ownerOid;

    /**
     * 拥有者gid
     */
    private String ownerGid;


    /**
     * 外部数据源Id
     */
    private String outerDataId;

    /**
     * 流程模版id
     */
    private String flowTemplateId;

    /**
     * 数据源id, 如表单id
     */
    private String dataSourceId;

    /**
     * 数据源渠道
     */
    private String dataSourceChannel;

    /**
     *  数据源名称，如表单名称
     */
    private String dataSourceName;

    /**
     * 状态
     */
    private String status;

    /**
     * 流程名称
     */
    private String processTitle;

    /**
     * 流程id
     */
    private String processId;

    /**
     * @see com.timevale.contractmanager.common.service.enums.ProcessStartDataTypeEnum
     */
    private String type;

    /**
     * 扩展字段
     */
    private String extra;

}
package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.SubProcessDO;

import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table sub_process. 合同管理主流程与子流程关系
 *
 * <AUTHOR> Kunpeng
 */
public interface SubProcessDAO {

    /**
     * desc:插入表:sub_process.<br>
     *
     * @param entity entity
     * @return int
     */
    @Insert({
        "<script>",
        "insert into sub_process(process_id,sub_process_id,sub_process_type) values",
        "(#{processId},#{subProcessId},#{subProcessType})",
        "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(SubProcessDO entity);

    @Update({
        "<script>",
        "update sub_process set sub_process_id = #{newSubProcessId} ",
        "where process_id = #{processId} and sub_process_id = #{originSubProcessId}",
        "</script>"
    })
    int updateSubProcessId(@Param("processId") String processId, @Param("originSubProcessId")String originSubProcessId, @Param("newSubProcessId") String newSubProcessId);

    /**
     * desc:根据唯一约束IdxProcessId获取数据:sub_process.<br>
     *
     * @param processId processId
     * @return SubProcessDO
     */
    @Select({"<script>", "select * from sub_process where process_id=#{processId}", "</script>"})
    List<SubProcessDO> getByIdxProcessId(String processId);



    @Select({
        "<script>",
        "select * from sub_process where process_id=#{processId} and sub_process_type=#{subProcessType}",
        "</script>"
    })
    SubProcessDO getByIdxProcessIdAndType(
            @Param("processId") String processId, @Param("subProcessType") Integer subProcessType);

    /**
     * desc:根据唯一约束IdxSubprocessId获取数据:sub_process.<br>
     *
     * @param subProcessId subProcessId
     * @return SubProcessDO
     */
    @Select({
        "<script>",
        "select * from sub_process where sub_process_id=#{subProcessId}",
        "</script>"
    })
    SubProcessDO getByIdxSubprocessId(String subProcessId);

    @Select({
        "<script>",
        "select process_id,sub_process_type,sub_process_id from sub_process where process_id in ",
        "<foreach item='item' index='index' collection='list' open='(' separator=',' close=')'>",
        "#{item}",
        "</foreach>",
        "</script>"
    })
    List<SubProcessDO> getBatchSubProcessByProcessIds(@Param("list") List<String> list);

    @Select({
            "<script>",
            "select process_id,sub_process_type,sub_process_id from sub_process where sub_process_id in ",
            "<foreach item='item' index='index' collection='list' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<SubProcessDO> getBatchSubProcessBySubProcessIds(@Param("list") List<String> list);

}

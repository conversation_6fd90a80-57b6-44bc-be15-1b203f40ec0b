package com.timevale.contractmanager.common.dal.query.opponent;

import com.timevale.contractmanager.common.dal.query.base.BaseQuery;
import lombok.Data;

import java.util.Date;

/**
 * Created by tianlei on 2022/8/8
 */
@Data
public class OpponentEntityFixDataQuery extends BaseQuery {

    private String tenantGid;
    private Date minCreateTime;
    private Date maxCreateTime;
    private Integer entityType;
    private Boolean ignoreDeleted;

}

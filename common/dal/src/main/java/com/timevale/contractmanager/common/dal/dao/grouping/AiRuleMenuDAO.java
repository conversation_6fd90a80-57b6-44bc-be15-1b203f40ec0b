package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.AiRuleMenuDO;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

/**
 * The Table ai_rule_menu.
 * 台账规则配置-分类
 * <AUTHOR> Kunpeng
 */
public interface AiRuleMenuDAO{

    /**
     * desc:插入表:ai_rule_menu.<br/>
     * @param entity entity
     * @return int
     */
    //public int insert(AiRuleMenuDO entity);
    /**
     * desc:批量插入表:ai_rule_menu.<br/>
     * @param list list
     * @return int
     */
    @Insert({
            "<script>",
            "INSERT INTO ai_rule_menu (MENU_ID, RULE_ID, OID, GID,RULE_VERSION ) VALUES",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.menuId,jdbcType=VARCHAR},#{item.ruleId,jdbcType=VARCHAR},#{item.oid,jdbcType=VARCHAR}, ",
            "#{item.gid,jdbcType=VARCHAR},#{item.ruleVersion,jdbcType=INTEGER})",
            "</foreach>",
            "</script>"
    })
    public int insertBatch(List<AiRuleMenuDO> list);
    /**
     * desc:根据主键删除数据:ai_rule_menu.<br/>
     * @param id id
     * @return int
     */
    @Delete("DELETE FROM ai_rule_menu WHERE ID = #{id}")
    public int deleteById(Long id);

    @Delete({
            "<script>",
            "DELETE FROM ai_rule_menu WHERE ID IN ",
            "(<foreach collection='list' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    int deleteByIds(List<Long> list);

    /*@Delete("DELETE FROM ai_rule_menu WHERE OID = #{oid} AND RULE_ID = #{ruleId}")
    public int deleteByOidRuleId(@Param("oid") String oid,@Param("ruleId") String ruleId);*/

    /**
     * desc:根据主键获取数据:ai_rule_menu.<br/>
     * @param id id
     * @return AiRuleMenuDO
     */
    //public AiRuleMenuDO getById(Long id);

    /*@Select("SELECT ID,MENU_ID FROM ai_rule_menu WHERE OID = #{oid} AND RULE_ID = #{ruleId}")
    public List<AiRuleMenuDO> queryMenuListByRuleId(@Param("oid") String oid,@Param("ruleId") String ruleId);

    @Select("SELECT MENU_ID FROM ai_rule_menu WHERE OID = #{oid} AND RULE_ID = #{ruleId}")
    public List<String> queryMenuIdListByRuleId(@Param("oid") String oid,@Param("ruleId") String ruleId);

    @Select("SELECT MENU_ID FROM ai_rule_menu WHERE OID = #{oid}")
    public List<String> queryMenuIdListByOid(@Param("oid") String oid);*/

    @Select("SELECT ID,RULE_ID,MENU_ID FROM ai_rule_menu WHERE OID = #{oid}")
    public List<AiRuleMenuDO> queryMenuListByOid(@Param("oid") String oid);

    @Select("SELECT ID,RULE_ID,RULE_VERSION,MENU_ID FROM ai_rule_menu WHERE MENU_ID = #{menuId}")
    public AiRuleMenuDO getRuleByMenuId(@Param("menuId") String menuId);

    @Select("SELECT ID,RULE_ID,RULE_VERSION,MENU_ID FROM ai_rule_menu WHERE MENU_ID = #{menuId} AND GID = #{gid}")
    public AiRuleMenuDO getRuleByMenuIdAndGid(@Param("menuId") String menuId, @Param("gid") String gid);

    @Select({
            "<script>",
            "SELECT ID,RULE_ID,RULE_VERSION,MENU_ID FROM ai_rule_menu WHERE MENU_ID in",
            "<foreach collection='menuIds' item='t' open='(' separator=',' close=')'>#{t}</foreach>",
            "</script>"
    })
    List<AiRuleMenuDO> queryRuleByMenuIds(@Param("menuIds") Collection<String> menuIds);

    @Update("update ai_rule_menu set rule_id =#{ruleId} where menu_id=#{menuId} ")
    public Integer updateRuleId(AiRuleMenuDO aiRuleMenuDO);

    @Update("update ai_rule_menu set rule_id =#{ruleId}, menu_id = #{menuId}  where id=#{id} ")
    public Integer updateRuleIdAndMenuId(@Param("ruleId") String ruleId, @Param("menuId") String menuId, @Param("id") Long id);

    @Select("SELECT * FROM ai_rule_menu WHERE rule_id = #{ruleId}")
    public List<AiRuleMenuDO> listByRuleId(@Param("ruleId")String ruleId);
}

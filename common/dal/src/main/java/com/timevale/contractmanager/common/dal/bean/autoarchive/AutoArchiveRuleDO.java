package com.timevale.contractmanager.common.dal.bean.autoarchive;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-05-06 19:26
 */
@Data
@Builder
public class AutoArchiveRuleDO {
	private Long id;
	private String uuid;
	private String tenantGid;
	private Integer ruleStatus;
	private String bindingMenuId;
	private Integer autoArchiveNum;
	private Integer revomeContract;
	private Integer deleted;
	private String createByOid;
	private String modifyByOid;
	private Date createTime;
	private Date modifiedTime;
	private Date topTime;

	@Tolerate
	public  AutoArchiveRuleDO(){};
}

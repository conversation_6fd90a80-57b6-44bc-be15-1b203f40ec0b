package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * The table.
 * contract_process_group
 * <AUTHOR> Kunpeng
 */
@Data
public class ContractProcessGroupDO{

    /**
     * gmtCreate 记录创建时间.
     */
    private Date gmtCreate;
    /**
     * gmtModified 记录更新时间.
     */
    private Date gmtModified;
    /**
     * processGroupId 合同群组id.
     */
    private String processGroupId;
    /**
     * processGroupName 合同群组名称.
     */
    private String processGroupName;
    /**
     * processGroupAppId PROCESS_GROUP_APP_ID.
     */
    private String processGroupAppId;
    /**
     * processGroupOwner PROCESS_GROUP_OWNER.
     */
    private String processGroupOwner;
    /**
     * processGroupCreator 合同群组创建人账号的uuid.
     */
    private String processGroupCreator;
    /**
     * id ID.
     */
    private Integer id;
    /**
     * processGroupType 合同群组类型, 0-合同批量发起, 1-模板批量发起.
     */
    private Integer processGroupType;

    private String extra;


}

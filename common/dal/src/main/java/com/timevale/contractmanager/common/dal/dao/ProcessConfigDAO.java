package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessConfigDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table process_config. 合同管理主流程配置表
 *
 * <AUTHOR> Kunpeng
 */
public interface ProcessConfigDAO {

    /**
     * desc:插入表:process_config.<br>
     *
     * @param entity entity
     * @return int
     */
    @Insert({
        "<script>",
        "insert into process_config(process_id,process_comment,config_info) values",
        "(#{processId},#{processComment},#{configInfo})",
        "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(ProcessConfigDO entity);

    @Insert({
            "<script>",
            "insert into process_config(process_id,process_comment,config_info) values",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.processId},#{item.processComment},#{item.configInfo})",
            "</foreach>",
            "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertBatch(@Param("list") List<ProcessConfigDO> list);

    @Update({
        "<script>",
        "update process_config a join (",
        "<foreach collection='list' item='item' separator='UNION'>",
        " SELECT #{item.id} AS id, #{item.configInfo} AS config_info ",
        "</foreach>",
        ") b USING(id)",
        "set a.config_info = b.config_info",
        "</script>"
    })
    int updateBatchById(@Param("list") List<ProcessConfigDO> list);

    /**
     * desc:根据唯一约束IdxProcessId获取数据:process_config.<br>
     *
     * @param processId processId
     * @return ProcessConfigDO
     */
    @Select({
        "<script>",
        "select * from process_config where process_id = #{processId} limit 1",
        "</script>"
    })
    ProcessConfigDO getByIdxProcessId(String processId);

    @Select({
            "<script>",
            "select * from process_config where process_id in ",
            "<foreach item='item' index='index' collection='list' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<ProcessConfigDO> getByProcessIds(@Param("list") List<String> processIds);

    @Update({
            "<script>",
            "update process_config set terminate_reason = #{reason}",
            "where process_id = #{processId}",
            "</script>"
    })
    int updateTerminationReason(@Param("processId") String processId, @Param("reason") String reason);


    @Delete("delete from process_config where process_id=#{processId}")
    int deleteProcess(String processId);
}

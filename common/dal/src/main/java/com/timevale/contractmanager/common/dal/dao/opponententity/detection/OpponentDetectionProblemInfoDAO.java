package com.timevale.contractmanager.common.dal.dao.opponententity.detection;

import com.timevale.contractmanager.common.dal.bean.opponententity.detection.OpponentDetectionProblemInfoDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-08-13 15:28
 */
public interface OpponentDetectionProblemInfoDAO {

	@Insert({"<script>",
			"insert into opponent_detection_problem_info (tenant_gid,detection_org_id, risk_level, problem_desc, problem_no, suggest_desc, detection_task_id)",
			"values",
			"<foreach collection='problemInfoDOList' item='item' index='index' separator=','>",
			" (#{item.tenantGid}, #{item.detectionOrgId}, #{item.riskLevel}, #{item.problemDesc},",
			"#{item.problemNo}, #{item.suggestDesc}, #{item.detectionTaskId})",
			"</foreach>",
			"</script>"
	})
	void batchInsert(@Param("problemInfoDOList") List<OpponentDetectionProblemInfoDO> problemInfoDOList);

	@Select({
			"<script>",
			"select * from opponent_detection_problem_info ",
			"WHERE tenant_gid = #{tenantGid,jdbcType=VARCHAR} AND detection_org_id in ",
			"(<foreach collection='orgIds' item='item' separator=','> #{item} </foreach>) ",
			"</script>"
	})
	List<OpponentDetectionProblemInfoDO> getList(@Param("tenantGid") String tenantGid,
												 @Param("orgIds") List<String> orgIds);

	/**
	 * 统计一段时间内的问题数量
	 * @param detectionTaskId
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@Select({"<script>",
			"select count(detection_task_id) from opponent_detection_problem_info where ",
			"detection_task_id = #{detectionTaskId}",
			"<if test='startTime!=null'> and create_time &gt;= #{startTime}</if>",
			"<if test='endTime!=null'> and create_time &lt;= #{endTime}</if>",
			"</script>"
	})
	long countProblemByTaskId(@Param("detectionTaskId") String detectionTaskId,
							  @Param("startTime") Date startTime,
							  @Param("endTime") Date endTime);

}

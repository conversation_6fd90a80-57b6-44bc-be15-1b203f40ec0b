package com.timevale.contractmanager.common.dal.dao.constant;

import com.google.common.collect.Maps;

import com.timevale.saas.hbase.handler.HBaseColumnHandler;
import com.timevale.saas.hbase.handler.impl.*;

import java.util.Map;

/** hbase列定义 */
public enum HBaseProcessOptLogColumnEnum {
    ROW_KEY( "rowKey",  new HBaseColumnRowKeyHandler()),
    ID( "id",  new HBaseColumnLongHandler()),
    PROCESS_ID(
            "processId",
            new HBaseColumnStringHandler()),
    SUB_PROCESS_ID( "subProcessId",  new HBaseColumnStringHandler()),
    SUB_PROCESS_TYPE(
            "subProcessType",
            new HBaseColumnIntegerHandler()),
    SERIAL_ID(
            "serialId",
            new HBaseColumnStringHandler()),
    OPERATOR_ID( "operatorId", new HBaseColumnStringHandler()),
    OPERATOR_NAME( "operatorName",  new HBaseColumnStringHandler()),
    SUBJECT_ID( "subjectId",  new HBaseColumnStringHandler()),
    SUBJECT_TYPE( "subjectType",  new HBaseColumnIntegerHandler()),
    SUBJECT_NAME( "subjectName", new HBaseColumnStringHandler()),
    OPERATE_TYPE( "operateType", new HBaseColumnIntegerHandler()),
    OPERATE_TIME(
            "operateTime",
            new HBaseColumnDateHandler()),
    OPERATE_DATA( "operateData",   new HBaseColumnStringHandler()),
    CREATE_TIME(
            "createTime",
            new HBaseColumnDateHandler()),
    MODIFY_TIME(
            "modifyTime",
            new HBaseColumnDateHandler()),
    ;

    private static Map<String,HBaseProcessOptLogColumnEnum> columnEnumMap;

    static {

        columnEnumMap=Maps.newHashMap();

        for (HBaseProcessOptLogColumnEnum item : values()) {
            columnEnumMap.put(item.getColumn(),item);
        }
    }

    private String column;
    private HBaseColumnHandler handler;

    HBaseProcessOptLogColumnEnum(
             String column,  HBaseColumnHandler handler) {
        this.column = column;
        this.handler = handler;
    }

    public String getColumn() {
        return column;
    }

    public HBaseColumnHandler getHandler() {
        return handler;
    }

    public void setHandler(HBaseColumnHandler handler) {
        this.handler = handler;
    }

    public static Map<String, HBaseProcessOptLogColumnEnum> getColumnEnumMap() {
        return columnEnumMap;
    }

}

package com.timevale.contractmanager.common.dal.bean.opponententity;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Tolerate;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-01-22 16:05
 */
@Data
@Builder
@ToString
public class OpponentEntityDO {
	/**
	 * id 自增主键.
	 */
	@JsonIgnore
	private Long id;
	/**
	 * deleted 是否删除 0-否；1-是
	 */
	private Integer deleted;
	/**
	 * createTime 创建时间.
	 */
	private Date createTime;
	/**
	 * modifyTime 更新时间.
	 */
	private Date modifiedTime;
	/**
	 * uuid 对外暴露的主键
	 */
	private String uuid;
	/**
	 *tenantGid 租户gid
	 */
	private String tenantGid;
	/**
	 *tenantGid 租户oid
	 */
	private String tenantOid;
	/**
	 * entityUniqueId 企业名称/手机号/邮箱
	 */
	private String entityUniqueId;
	/**
	 *entityType 实体类型 0-个人；1-企业
	 */
	private Integer entityType;
	/**
	 * entityOid 实体oid
	 */
	private String entityOid;
	/**
	 * entityGid 实体gid
	 */
	private String entityGid;
	/**
	 *attachedEntityId 个人所属企业id
	 */
	private Long attachedEntityId;
	/**
	 *createdyGid 创建人gid
	 */
	private String createByGid;
	/**
	 *createdyGid 创建人oid
	 */
	private String createByOid;
	/**
	 * modifiedByGid 最近一次修改人gid
	 */
	private String modifyByGid;
	/**
	 * modifiedByGid 最近一次修改人oid
	 */
	private String modifyByOid;
	/**
	 * createProcessId 创建相对方实体关联的流程id
	 */
	private String createProcessId;
	/**
	 * description 描述
	 */
	private String description;
	/**
	 * entityName 实体名称
	 */
	private String entityName;
	/**
	 * authorizeType 认证状态
	 */
	private Integer authorizeType;

	private Integer riskLevel;

	/**
	 * 证件代码
	 */
	private String socialCreditCode;

	/**
	 * 企业法人姓名
	 */
	private String legalRepresentativeName;
	/** 证件代码类型 */
	private Integer creditCodeType;

	private String detectionOrgId;

	@Tolerate
	public OpponentEntityDO(){}
}

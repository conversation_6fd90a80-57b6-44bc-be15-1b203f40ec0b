package com.timevale.contractmanager.common.dal.dao.opponententity;

import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityRelationDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2023/2/24
 */
public interface OpponentEntityRelationDAO {

    @Insert({
        " <script>",
        " replace into opponent_entity_relation (org_id,org_uuid,person_id,person_uuid) values ",
        " <foreach item='item' index='index' collection='entityDOList' separator=',' >",
        " (#{item.orgId}, #{item.orgUuid}, #{item.personId}, #{item.personUuid})",
        " </foreach>",
        " </script>"
    })
    void insert(@Param("entityDOList") List<OpponentEntityRelationDO> entityDOList);

    @Select("select org_id from opponent_entity_relation where person_id = #{personId}")
    List<Long> queryOrgIdByPersonId(@Param("personId") Long personId);

    @Select({
        " <script>",
        " select person_id from opponent_entity_relation where org_id in ",
        " <foreach item='item' index='index' collection='orgIdList' open='(' separator=',' close=')'>",
        " #{item}",
        " </foreach>",
        " </script>"
    })
    List<Long> queryPersonByOrgIdList(@Param("orgIdList") List<Long> orgIdList);

    @Delete({
        " <script>",
        " delete from opponent_entity_relation where",
        " person_id = #{personId} and org_id in ",
        " <foreach item='item' index='index' collection='orgIdList' open='(' separator=',' close=')'>",
        " #{item}",
        " </foreach>",
        " </script>"
    })
    void batchDeleteRelation(
            @Param("personId") Long personId, @Param("orgIdList") List<Long> orgIdList);

    @Delete({
        " <script>",
        " delete from opponent_entity_relation where",
        " org_id in ",
        " <foreach item='item' index='index' collection='orgIdList' open='(' separator=',' close=')'>",
        " #{item}",
        " </foreach>",
        " </script>"
    })
    void deleteByOrgId(@Param("orgIdList") List<Long> orgIdList);

    @Delete({
            " <script>",
            " delete from opponent_entity_relation where",
            " person_id in ",
            " <foreach item='item' index='index' collection='personIdList' open='(' separator=',' close=')'>",
            " #{item}",
            " </foreach>",
            " </script>"
    })
    void deleteByPersonId(@Param("personIdList") List<Long> personIdList);
}

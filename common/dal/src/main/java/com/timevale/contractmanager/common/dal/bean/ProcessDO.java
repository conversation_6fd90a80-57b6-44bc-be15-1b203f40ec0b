package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * The table.
 * 合同管理主流程表
 * <AUTHOR> Kunpeng
 */
@Data
public class ProcessDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * appId APP_ID.
     */
    private String appId;
    /**
     * processId 主流程id.
     */
    private String processId;
    /**
     * 合同组id
     */
    private String processGroupId;
    /**
     * subjectGid 发起主体gid.
     */
    private String subjectGid;
    /**
     * subjectOid 发起主体oid.
     */
    private String subjectOid;
    /**
     * subjectUid 发起主体uid.
     */
    private String subjectUid;
    /**
     * initiatorGid 发起人gid.
     */
    private String initiatorGid;
    /**
     * initiatorOid 发起人oid.
     */
    private String initiatorOid;
    /**
     * initiatorUid 发起人uid.
     */
    private String initiatorUid;
    /**
     * processTitle 流程名称.
     */
    private String processTitle;
    /**
     * processVersion 流程版本，为空表示默认版本，新增数据processVersion不为空
     */
    private Integer processVersion;
    /**
     * processInstanceId PROCESS_INSTANCE_ID.
     */
    private String processInstanceId;
    /**
     * status 1-填写中 2-签署中 3-用印审批中 4-已撤回 5-已拒绝 6-已过期 7-已删除 8-已完成.
     */
    private Integer status;
    /**
     * 用来标识流程是否被删除，此字段为了避免由于原子性问题。0-未删除 1-已删除
     */
    private Integer deleted;
    /**
     * createType 创建类型 1-模板 2-合同.
     */
    private Integer createType;

    /**
     * thirdProcessNo 三方业务流程id
     */
    private String thirdProcessNo;

    /**
     * processEndTime 流程截止时间
     */
    private Date processEndTime;

    /**
     * contractEndTime 合同截止时间
     */
    private Date contractEndTime;

    /**
     * 1 为epaas流程
     */
    private Integer epaasTag;

    public Integer getEpaasTag() {
        return epaasTag;
    }

    public void setEpaasTag(Integer epaasTag) {
        this.epaasTag = epaasTag;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set appId APP_ID.
     */
    public void setAppId(String appId){
        this.appId = appId;
    }

    /**
     * Get appId APP_ID.
     *
     * @return the string
     */
    public String getAppId(){
        return appId;
    }

    /**
     * Set processId 主流程id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 主流程id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    /**
     * Set subjectGid 发起主体gid.
     */
    public void setSubjectGid(String subjectGid){
        this.subjectGid = subjectGid;
    }

    /**
     * Get subjectGid 发起主体gid.
     *
     * @return the string
     */
    public String getSubjectGid(){
        return subjectGid;
    }

    /**
     * Set subjectOid 发起主体oid.
     */
    public void setSubjectOid(String subjectOid){
        this.subjectOid = subjectOid;
    }

    /**
     * Get subjectOid 发起主体oid.
     *
     * @return the string
     */
    public String getSubjectOid(){
        return subjectOid;
    }

    /**
     * Set subjectUid 发起主体uid.
     */
    public void setSubjectUid(String subjectUid){
        this.subjectUid = subjectUid;
    }

    /**
     * Get subjectUid 发起主体uid.
     *
     * @return the string
     */
    public String getSubjectUid(){
        return subjectUid;
    }

    /**
     * Set initiatorGid 发起人gid.
     */
    public void setInitiatorGid(String initiatorGid) {
        this.initiatorGid = initiatorGid;
    }

    /**
     * Get initiatorGid 发起人gid.
     *
     * @return the string
     */
    public String getInitiatorGid() {
        return initiatorGid;
    }

    /**
     * Set initiatorOid 发起人oid.
     */
    public void setInitiatorOid(String initiatorOid){
        this.initiatorOid = initiatorOid;
    }

    /**
     * Get initiatorOid 发起人oid.
     *
     * @return the string
     */
    public String getInitiatorOid(){
        return initiatorOid;
    }

    /**
     * Set initiatorUid 发起人uid.
     */
    public void setInitiatorUid(String initiatorUid){
        this.initiatorUid = initiatorUid;
    }

    /**
     * Get initiatorUid 发起人uid.
     *
     * @return the string
     */
    public String getInitiatorUid(){
        return initiatorUid;
    }

    /**
     * Set processTitle 流程名称.
     */
    public void setProcessTitle(String processTitle){
        this.processTitle = processTitle;
    }

    /**
     * Get processTitle 流程名称.
     *
     * @return the string
     */
    public String getProcessTitle(){
        return processTitle;
    }

    public Integer getProcessVersion() {
        return processVersion;
    }

    public void setProcessVersion(Integer processVersion) {
        this.processVersion = processVersion;
    }

    /**
     * Set processInstanceId PROCESS_INSTANCE_ID.
     */
    public void setProcessInstanceId(String processInstanceId){
        this.processInstanceId = processInstanceId;
    }

    /**
     * Get processInstanceId PROCESS_INSTANCE_ID.
     *
     * @return the string
     */
    public String getProcessInstanceId(){
        return processInstanceId;
    }

    /**
     * Set status 1-填写中 2-签署中 3-用印审批中 4-已撤回 5-已拒绝 6-已过期 7-已删除 8-已完成.
     */
    public void setStatus(Integer status){
        this.status = status;
    }

    /**
     * Get status 1-填写中 2-签署中 3-用印审批中 4-已撤回 5-已拒绝 6-已过期 7-已删除 8-已完成.
     *
     * @return the string
     */
    public Integer getStatus(){
        return status;
    }

    /**
     * Set deleted 0-未删除 1-已删除.
     */
    public void setDeleted(Integer deleted){
        this.deleted = deleted;
    }

    /**
     * Get deleted 0-未删除 1-已删除.
     *
     * @return the string
     */
    public Integer getDeleted(){
        return deleted;
    }

    /**
     * Set createType 创建类型 1-模板 2-合同.
     */
    public void setCreateType(Integer createType){
        this.createType = createType;
    }

    /**
     * Get createType 创建类型 1-模板 2-合同.
     *
     * @return the string
     */
    public Integer getCreateType(){
        return createType;
    }

    public String getThirdProcessNo() {
        return thirdProcessNo;
    }

    public void setThirdProcessNo(String thirdProcessNo) {
        this.thirdProcessNo = thirdProcessNo;
    }

    public Date getProcessEndTime() {
        return processEndTime;
    }

    public void setProcessEndTime(Date processEndTime) {
        this.processEndTime = processEndTime;
    }

    public Date getContractEndTime() {
        return contractEndTime;
    }

    public void setContractEndTime(Date contractEndTime) {
        this.contractEndTime = contractEndTime;
    }
}

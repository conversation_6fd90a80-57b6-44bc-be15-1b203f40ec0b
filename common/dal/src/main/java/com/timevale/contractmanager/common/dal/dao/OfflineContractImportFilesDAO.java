package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.OfflineContractImportFilesDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table offline_contract_import_files.
 * 纸质合同导入记录对应的文件列表
 * <AUTHOR> Kunpeng
 */
public interface OfflineContractImportFilesDAO{

    @Insert({
        "<script>",
        "INSERT INTO offline_contract_import_files(",
        " RECORD_ID,RECORD_PROCESS_ID,FILE_ID,FILE_MODE,FILE_NAME",
        " ,FILE_TYPE,ORIGIN_FILE_ID,CONTRACT_NO,CONTRACT_CATEGORY_ID,CONTRACT_CATEGORY_NAME) VALUES",
        " <foreach collection='list' item='item' separator=','>",
        "(#{item.recordId,jdbcType=VARCHAR}",
        " , #{item.recordProcessId,jdbcType=VARCHAR}",
        " , #{item.fileId,jdbcType=VARCHAR}",
        " , #{item.fileMode,jdbcType=VARCHAR}",
        " , #{item.fileName,jdbcType=VARCHAR}",
        " , #{item.fileType,jdbcType=VARCHAR}",
        " , #{item.originFileId,jdbcType=VARCHAR}",
        " , #{item.contractNo,jdbcType=VARCHAR}",
        " , #{item.contractCategoryId,jdbcType=VARCHAR}",
        " , #{item.contractCategoryName,jdbcType=VARCHAR}",
        " )",
        "</foreach>",
        "</script>",
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int batchInsert(List<OfflineContractImportFilesDO> list);

    @Update({
        "update offline_contract_import_files",
        " set contract_no = #{contractNo}",
        " where record_id = #{recordId} and file_id = #{fileId}"
    })
    int updateContractNoByRecordId(
            @Param("recordId") String recordId,
            @Param("fileId") String fileId,
            @Param("contractNo") String contractNo);

    @Delete({
            "<script>",
            "delete from offline_contract_import_files where record_id in",
            "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    int deleteByRecordIds(@Param("list") List<String> recordIds);

    @Select("select * from offline_contract_import_files where record_id = #{recordId}")
    List<OfflineContractImportFilesDO> getByRecordId(String recordId);

    @Select({
            "<script>",
            "select * from offline_contract_import_files where file_id in",
            "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<OfflineContractImportFilesDO> queryByFileIds(@Param("list") List<String> fileIds);

    @Select("select * from offline_contract_import_files where record_process_id = #{recordProcessId}")
    List<OfflineContractImportFilesDO> queryByRecordProcessId(String recordProcessId);
}

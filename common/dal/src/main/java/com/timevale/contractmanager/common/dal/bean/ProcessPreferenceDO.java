package com.timevale.contractmanager.common.dal.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021-05-17 19:28
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessPreferenceDO {
    /**
     * id ID
     */
    private Long id;
    /** 用户OID */
    private String accountOid;
    /** 用户GID */
    private String accountGid;
    /** 用户类型;0-个人,1-企业 */
    private Integer accountType;
    /** 偏好类型 */
    private String preferenceType;
    /** 偏好值 */
    private String preferenceValue;
    /** 状态;0-无效,1-有效 */
    private Integer status;
    /** 创建时间 */
    private Date createTime ;
    /** 更新时间 */
    private Date modifyTime ;

}

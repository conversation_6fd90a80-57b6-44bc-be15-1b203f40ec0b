package com.timevale.contractmanager.common.dal.bean.grouping;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * The table.
 * 菜单记录表（企业空间+个人空间）-以空间维度记录
 * <AUTHOR> Kunpeng
 */
public class MenuDO{

    /**
     * createTime CREATE_TIME.
     */
    private Date createTime;
    /**
     * updateTime UPDATE_TIME.
     */
    private Date updateTime;
    /**
     * id ID.
     */
    private Long id;
    /**
     * gid 企业空间下为企业空间gid，个人空间下为个人空间gid.
     */
    private String gid;
    /**
     * oid 企业空间下为企业空间oid，个人空间下为个人空间oid.
     */
    private String oid;
    /**
     * name 菜单名称.
     */
    private String name;
    /**
     * menuId 菜单ID.
     */
    private String menuId;
    /**
     * parentId 父菜单ID对应父菜单的menu_id，根菜单默认''.
     */
    private String parentId;
    /**
     * order 排序，从0开始升序，越小的越排前.
     */
    private Integer order;
    /**
     * deleted 逻辑删除0否，1是.
     */
    private Integer deleted;
    /**
     * 绑定的台账id
     */
    private String bindingFormId;
    /**
     * 绑定的台账名称
     */
    private String bindingFormName;
    /**
     * 是否显示子分类
     */
    @Getter
    @Setter
    private Integer showChild;

    /**
     * 是否统一台账, 0 否 1 是
     */
    @Getter
    @Setter
    private Integer unityForm;

    /**
     * 路径
     */
    @Getter
    @Setter
    private String path;

    /**
     * Set createTime CREATE_TIME.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime CREATE_TIME.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime UPDATE_TIME.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime UPDATE_TIME.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set gid 企业空间下为企业空间gid，个人空间下为个人空间gid.
     */
    public void setGid(String gid){
        this.gid = gid;
    }

    /**
     * Get gid 企业空间下为企业空间gid，个人空间下为个人空间gid.
     *
     * @return the string
     */
    public String getGid(){
        return gid;
    }

    /**
     * Set oid 企业空间下为企业空间oid，个人空间下为个人空间oid.
     */
    public void setOid(String oid){
        this.oid = oid;
    }

    /**
     * Get oid 企业空间下为企业空间oid，个人空间下为个人空间oid.
     *
     * @return the string
     */
    public String getOid(){
        return oid;
    }

    /**
     * Set name 菜单名称.
     */
    public void setName(String name){
        this.name = name;
    }

    /**
     * Get name 菜单名称.
     *
     * @return the string
     */
    public String getName(){
        return name;
    }

    /**
     * Set menuId 菜单ID.
     */
    public void setMenuId(String menuId){
        this.menuId = menuId;
    }

    /**
     * Get menuId 菜单ID.
     *
     * @return the string
     */
    public String getMenuId(){
        return menuId;
    }

    /**
     * Set parentId 父菜单ID对应父菜单的menu_id，根菜单默认''.
     */
    public void setParentId(String parentId){
        this.parentId = parentId;
    }

    /**
     * Get parentId 父菜单ID对应父菜单的menu_id，根菜单默认''.
     *
     * @return the string
     */
    public String getParentId(){
        return parentId;
    }

    /**
     * Set order 排序，从0开始升序，越小的越排前.
     */
    public void setOrder(Integer order){
        this.order = order;
    }

    /**
     * Get order 排序，从0开始升序，越小的越排前.
     *
     * @return the string
     */
    public Integer getOrder(){
        return order;
    }

    /**
     * Set deleted 逻辑删除0否，1是.
     */
    public void setDeleted(Integer deleted){
        this.deleted = deleted;
    }

    /**
     * Get deleted 逻辑删除0否，1是.
     *
     * @return the string
     */
    public Integer getDeleted(){
        return deleted;
    }

    public String getBindingFormId() {
        return bindingFormId;
    }

    public void setBindingFormId(String bindingFormId) {
        this.bindingFormId = bindingFormId;
    }

    public String getBindingFormName() {
        return bindingFormName;
    }

    public void setBindingFormName(String bindingFormName) {
        this.bindingFormName = bindingFormName;
    }
}

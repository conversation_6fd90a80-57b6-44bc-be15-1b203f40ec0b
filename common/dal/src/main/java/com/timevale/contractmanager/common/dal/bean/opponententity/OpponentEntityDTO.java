package com.timevale.contractmanager.common.dal.bean.opponententity;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Data;
import lombok.ToString;

import java.util.Objects;

/**
 * 相对方实体DTO
 *
 * <AUTHOR>
 * @since 2021-01-27 11:17
 */
@Data
@ToString
public class OpponentEntityDTO {

    private Long id;

    /** 企业空间gid->索引键 */
    private String gid;

    /** 实体类型 0-个人；1-企业 */
    private Integer entityType;

    //风险等级 1-白名单; 2-黑名单
    private Integer riskLevel;
    /** 分页参数 */
    private Integer offset;

    /** 每页大小 */
    private Integer pageSize;

    /** 认证状态 */
    private Integer authorizeType;

    /** 备注 */
    private String fuzzyDesc;

    /**
     * 企业名称/手机号/邮箱
     */
    private String entityUniqueId;

    /**
     * 实体名称
     */
    private String entityName;

    /**
     * 所属企业id
     */
    private Long attachedEntityId;

    //所属企业 1-有; 2-没有
    private Integer attachedEntityType;

    public OpponentEntityDTO(
            String gid,
            Integer entityType,
            Integer riskLevel,
            Integer offset,
            Integer pageSize,
            Integer authorizeType,
            String fuzzyDesc,
            String entityUniqueId,
            Integer attachedEntityType) {
        this.gid = gid;
        this.entityType = entityType;
        this.offset = offset;
        this.pageSize = pageSize;
        this.authorizeType = authorizeType;
        this.attachedEntityType = attachedEntityType;
        if (StringUtils.isNotBlank(fuzzyDesc)) {
            this.fuzzyDesc = fuzzyDesc;
        }
        if (StringUtils.isNotBlank(entityUniqueId)) {
            this.entityUniqueId = entityUniqueId;
        }
        if(!Objects.isNull(riskLevel)){
            this.riskLevel = riskLevel;
        }
    }

    public OpponentEntityDTO(){}
}

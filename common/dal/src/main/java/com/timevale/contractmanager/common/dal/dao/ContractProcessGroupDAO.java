package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ContractProcessGroupDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * The Table contract_process_group.
 * contract_process_group
 * <AUTHOR> Kunpeng
 */
public interface ContractProcessGroupDAO{

    /**
     * desc:插入表:contract_process_group.<br/>
     * @param entity entity
     * @return int
     */
    @Insert("INSERT INTO contract_process_group(" +
            "            PROCESS_GROUP_ID" +
            "            ,PROCESS_GROUP_NAME" +
            "            ,PROCESS_GROUP_CREATOR" +
            "            ,PROCESS_GROUP_OWNER" +
            "            ,PROCESS_GROUP_TYPE" +
            "            ,PROCESS_GROUP_APP_ID" +
            "            ,GMT_CREATE" +
            "            ,GMT_MODIFIED" +
            "            ,extra" +
            "        )VALUES(" +
            "            #{processGroupId,jdbcType=VARCHAR}" +
            "            , #{processGroupName,jdbcType=VARCHAR}" +
            "            , #{processGroupCreator,jdbcType=VARCHAR}" +
            "            , #{processGroupOwner,jdbcType=VARCHAR}" +
            "            , #{processGroupType,jdbcType=TINYINT}" +
            "            , #{processGroupAppId,jdbcType=VARCHAR}" +
            "            , now()" +
            "            , now()" +
            "            ,#{extra}" +
            "        )")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    public int insert(ContractProcessGroupDO entity);
    /**
     * desc:根据普通索引IdxProcessGroupId获取数据:contract_process_group.<br/>
     * @param processGroupId processGroupId
     * @return List<ContractProcessGroupDO>
     */
    @Select("select * from contract_process_group where process_group_id = #{processGroupId,jdbcType=VARCHAR}")
    public ContractProcessGroupDO queryByProcessGroupId(String processGroupId);

    @Update({
            "UPDATE contract_process_group SET PROCESS_GROUP_CREATOR = #{processGroupCreator,jdbcType=VARCHAR} ",
            "where process_group_id = #{processGroupId,jdbcType=VARCHAR}"
    })
    public int updateProcessGroupCreator(@Param("processGroupId") String processGroupId,@Param("processGroupCreator") String processGroupCreator);

    @Update({
            "UPDATE contract_process_group SET extra = #{extra,jdbcType=VARCHAR} ",
            "where process_group_id = #{processGroupId,jdbcType=VARCHAR}"
    })
    public int updateProcessGroupExtra(@Param("processGroupId") String processGroupId,@Param("extra") String extra);
    
    @Select({
            "<script>",
            "select process_group_id, process_group_type from contract_process_group where process_group_id in ",
            "<foreach item='item' index='index' collection='list' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    public List<ContractProcessGroupDO> batchQueryByGroupIds(@Param("list") List<String> groupIds);
}

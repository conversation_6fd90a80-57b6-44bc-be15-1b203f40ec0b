package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * The table. 纸质合同导入记录对应的文件列表
 *
 * <AUTHOR> Kunpeng
 */
@Data
public class OfflineContractImportFilesDO {

    /** id 自增id. */
    private Long id;
    /** recordId 导入记录id. */
    private String recordId;
    /** recordProcessId 导入记录流程id, 一个导入流程对应一个合同流程. */
    private String recordProcessId;
    /** originFileId 原始文件id. */
    private String originFileId;
    /** fileId 文件id. */
    private String fileId;
    /** fileName 文件名称. */
    private String fileName;
    /** fileMode 文件模型，ZIP-ZIP文件，PDF-PDF文件，OTHER-其他. */
    private String fileMode;
    /** fileType 文件类型，CONTRACT-合同文件，ATTACHMENT-附件. */
    private String fileType;
    /** contractNo 合同编号 */
    private String contractNo;
    /** contractCategoryId 合同类型id */
    private String contractCategoryId;
    /** contractCategoryName 合同类型名称 */
    private String contractCategoryName;
    /** createTime 创建时间. */
    private Date createTime;
    /** updateTime 更新时间. */
    private Date updateTime;
}

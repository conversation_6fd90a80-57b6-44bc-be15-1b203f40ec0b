package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessStartDataDO;
import com.timevale.contractmanager.common.dal.query.processstartdata.ProcessStartDataQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProcessStartDataDAO {

    /**
     * 删除数据局
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入数据
     */
    int insertSelective(ProcessStartDataDO record);

    /**
     * 根据dataId查询
     */
    ProcessStartDataDO getByDataId(String dataId);


    /**
     * 根据dataId查询
     */
    ProcessStartDataDO getByProcessId(String processId);

    /**
     * 批量查询
     */
    List<ProcessStartDataDO> listByDataIdList(@Param("dataIdList") List<String> dataIds);

    /**
     * 唯一键查询
     */
    ProcessStartDataDO getByFlowTemplateIdAndOuterDataId(@Param("flowTemplateId") String flowTemplateId,
                                                         @Param("outerDataId") String outerDataId);

    /**
     * 通过outerDataId查询
     */
    List<ProcessStartDataDO> listByOuterDataId(@Param("outerDataId") String outerDataId);

    List<ProcessStartDataDO> listByOwnerOidAndDataSourceChannel(ProcessStartDataQuery processStartDataQuery);

    /**
     * 根据dataId更新
     */
    int updateByDataId(ProcessStartDataDO record);


    /**
     * 根据多个 dataId 批量更新owner
     * @param records 包含要更新信息的 ProcessStartDataDO 列表，每个 record 需包含有效的 dataId
     * @return 成功更新的记录数量
     */
    int updateOwnerByDataIds(@Param("records") List<ProcessStartDataDO> records);

    /**
     * 根据dataid重置状态
     */
    int revokeByDataId(ProcessStartDataDO record);


    /**
     * 删除数据
     */
    int deleteByDataIds(@Param("dataIdList") List<String> dataIdList);

    /**
     * 物理删除数据
     */
    int trueDeleteByDataIds(@Param("dataIdList") List<String> dataIdList);

    /**
     * 查询列表
     */
    List<ProcessStartDataDO> listByQuery(ProcessStartDataQuery query);

    /**
     * 根据processid查询列表
     */
    List<ProcessStartDataDO> listByProcessIds(@Param("processIds") List<String> processIds);

    /**
     * 查询数量
     */
    int countByQuery(ProcessStartDataQuery query);


}

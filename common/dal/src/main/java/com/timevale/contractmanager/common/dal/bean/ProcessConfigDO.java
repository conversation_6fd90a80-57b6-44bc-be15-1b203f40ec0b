package com.timevale.contractmanager.common.dal.bean;

import java.util.Date;

/**
 * The table.
 * 合同管理主流程配置
 * <AUTHOR> Kunpeng
 */
public class ProcessConfigDO {

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * processId 主流程id.
     */
    private String processId;
    /**
     * processComment 流程备注.
     */
    private String processComment;

    /**
     * terminateReason 流程中止原因
     */
    private String terminateReason;

    /**
     * configInfo 流程配置信息，json字符串，notifyUrl-回调地址， redirectUrl-重定向地址，redirectDelay-重定向延迟时间，noticeType-通知方式（1-短信，2-邮件）
     */
    private String configInfo;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set processId 主流程id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 主流程id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    public String getProcessComment() {
        return processComment;
    }

    public void setProcessComment(String processComment) {
        this.processComment = processComment;
    }

    public String getTerminateReason() {
        return terminateReason;
    }

    public void setTerminateReason(String terminateReason) {
        this.terminateReason = terminateReason;
    }

    public String getConfigInfo() {
        return configInfo;
    }

    public void setConfigInfo(String configInfo) {
        this.configInfo = configInfo;
    }
}

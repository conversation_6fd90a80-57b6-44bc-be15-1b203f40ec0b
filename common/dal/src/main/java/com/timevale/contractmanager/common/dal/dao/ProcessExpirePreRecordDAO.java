package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessExpirePreRecordDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Date;

/**
 * The Table process_expire_pre_record.
 * 合同过期预处理记录
 * <AUTHOR> Kunpeng
 */
public interface ProcessExpirePreRecordDAO{

    /**
     * desc:插入表:process_expire_pre_record.<br>
     *
     * @param entity entity
     * @return int
     */
    @Insert(
            "INSERT INTO process_expire_pre_record(\n"
            + "            PROCESS_ID\n"
            + "            ,EXPIRE_TYPE\n"
            + "            ,EXPIRE_TIME\n"
            + "        )VALUES(\n"
            + "            #{processId,jdbcType=VARCHAR}\n"
            + "            , #{expireType,jdbcType=TINYINT}\n"
            + "            , #{expireTime,jdbcType=TIMESTAMP}\n"
            + "        )"
            + " ON DUPLICATE KEY UPDATE"
            + " EXPIRE_TIME = VALUES(EXPIRE_TIME)")
    public int insert(ProcessExpirePreRecordDO entity);
    /**
     * desc:根据processId删除数据:process_expire_pre_record.<br>
     *
     * @param processId
     * @return int
     */
    @Delete({
        "<script>",
        "delete from process_expire_pre_record where process_id = #{processId} and expire_type = #{expireType}",
        "</script>"
    })
    public int deleteByProcessId(
            @Param("processId") String processId, @Param("expireType") Integer expireType);
    /**
     * desc:根据processId获取数据:process_expire_pre_record.<br/>
     * @param processId
     * @return ProcessExpirePreRecordDO
     */
    @Select("select * from process_expire_pre_record where process_id = #{processId}")
    public List<ProcessExpirePreRecordDO> getByProcessId(String processId);
    /**
     * desc:根据普通索引IdxExpireTime获取数据:process_expire_pre_record.<br>
     *
     * @param expireFrom expireFrom
     * @param expireTo expireTo
     * @param expireType expireType
     * @return List<ProcessExpirePreRecordDO>
     */
    @Select({
        "<script>",
        "select * from process_expire_pre_record where expire_time between #{expireFrom} and #{expireTo} and id > #{idFrom}",
        "<if test='expireType != null'> and expire_type = #{expireType} </if>",
        "</script>"
    })
    public List<ProcessExpirePreRecordDO> queryByIdxExpireTime(
            @Param("expireFrom") Date expireFrom,
            @Param("expireTo") Date expireTo,
            @Param("expireType") Integer expireType,
            @Param("idFrom") Long idFrom);

    @Select({
            "<script>",
            "select * from process_expire_pre_record where expire_time &lt;= #{maxExpireTime} and expire_type = #{expireType} limit #{size}",
            "</script>"
    })
     List<ProcessExpirePreRecordDO> listByMaxExpireTime(
            @Param("maxExpireTime") Date maxExpireTime,
            @Param("expireType") Integer expireType,
            @Param("size") Integer size);


    @Delete({
            "<script>",
            "delete from process_expire_pre_record where process_id in <foreach collection='processIds' item='item' open='(' close=')' separator=','> #{item} </foreach> " +
            "and expire_type = #{expireType}",
            "</script>"
    })
    int deleteByProcessIds(
            @Param("processIds") List<String> processIds, @Param("expireType") Integer expireType);
}

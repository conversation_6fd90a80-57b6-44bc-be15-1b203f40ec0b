package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 待清理菜单表
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@NoArgsConstructor
public class MenuWaitClearDO {
    /** id */
    private Long id;
    /** 菜单id */
    private String menuId;
    /** 前置清除 */
    private Integer preClear;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    public MenuWaitClearDO(String menuId) {
        this.menuId = menuId;
    }
}

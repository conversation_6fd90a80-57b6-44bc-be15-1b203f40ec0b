package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.MenuWaitClearDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 待清理菜单表
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Mapper
public interface MenuWaitClearDAO {

    @Insert({
            "insert into menu_wait_clear (menu_id) values (#{menuId})",
    })
    int insert(MenuWaitClearDO menuWaitClearDO);

    @Insert({
            "<script>",
            "insert into menu_wait_clear (menu_id) values",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.menuId})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(@Param("list") List<MenuWaitClearDO> menuWaitClearDOList);

    @Delete({
            "delete from menu_wait_clear where id = #{id}"
    })
    int deleteById(Long id);

    @Select({
            "select * from menu_wait_clear order by id limit 1"
    })
    MenuWaitClearDO getNext();

    @Update({
            "update menu_wait_clear set pre_clear = #{preClear} where id = #{id}"
    })
    int updatePreClearById(@Param("id") Long id, @Param("preClear") Integer preClear);
}

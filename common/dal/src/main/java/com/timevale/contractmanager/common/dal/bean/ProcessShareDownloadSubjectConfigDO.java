package com.timevale.contractmanager.common.dal.bean;

import java.util.Date;

/**
 * The table.
 * 主体分享功能配置信息表
 * <AUTHOR> Kunpeng
 */
public class ProcessShareDownloadSubjectConfigDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * subjectGid 主体gid.
     */
    private String subjectGid;
    /**
     * subjectOid 主体oid.
     */
    private String subjectOid;
    /**
     * status 开启状态：1-开启，0-关闭，2-到期.
     */
    private Integer status;
    /**
     * deleted 删除状态：1-删除，0-正常.
     */
    private Integer deleted;
    /**
     * accessLimit 访问的数量限制.
     */
    private Integer accessLimit;
    /**
     * accessStatus 访问状态 0:关闭分享合同的访问，1:允许分享合同访问.
     */
    private Integer accessStatus;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set subjectGid 主体gid.
     */
    public void setSubjectGid(String subjectGid){
        this.subjectGid = subjectGid;
    }

    /**
     * Get subjectGid 主体gid.
     *
     * @return the string
     */
    public String getSubjectGid(){
        return subjectGid;
    }

    /**
     * Set subjectOid 主体oid.
     */
    public void setSubjectOid(String subjectOid){
        this.subjectOid = subjectOid;
    }

    /**
     * Get subjectOid 主体oid.
     *
     * @return the string
     */
    public String getSubjectOid(){
        return subjectOid;
    }

    /**
     * Set status 开启状态：1-开启，0-关闭，2-到期.
     */
    public void setStatus(Integer status){
        this.status = status;
    }

    /**
     * Get status 开启状态：1-开启，0-关闭，2-到期.
     *
     * @return the string
     */
    public Integer getStatus(){
        return status;
    }

    /**
     * Set deleted 删除状态：1-删除，0-正常.
     */
    public void setDeleted(Integer deleted){
        this.deleted = deleted;
    }

    /**
     * Get deleted 删除状态：1-删除，0-正常.
     *
     * @return the string
     */
    public Integer getDeleted(){
        return deleted;
    }

    /**
     * Set accessLimit 访问的数量限制.
     */
    public void setAccessLimit(Integer accessLimit){
        this.accessLimit = accessLimit;
    }

    /**
     * Get accessLimit 访问的数量限制.
     *
     * @return the string
     */
    public Integer getAccessLimit(){
        return accessLimit;
    }

    /**
     * Set accessStatus 访问状态 0:关闭分享合同的访问，1:允许分享合同访问.
     */
    public void setAccessStatus(Integer accessStatus){
        this.accessStatus = accessStatus;
    }

    /**
     * Get accessStatus 访问状态 0:关闭分享合同的访问，1:允许分享合同访问.
     *
     * @return the string
     */
    public Integer getAccessStatus(){
        return accessStatus;
    }
}

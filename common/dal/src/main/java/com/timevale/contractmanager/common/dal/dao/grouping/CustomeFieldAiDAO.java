package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.CustomeFieldAiDO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * The Table custome_field_ai.
 * 自定义字段配置-AI解析生成，以企业维度记录
 * <AUTHOR> Kunpeng
 */
public interface CustomeFieldAiDAO{

    @Select("SELECT ID,GID,OID,FIELD_ID,FIELD_CODE,FIELD_NAME,RULE_ID FROM custome_field_ai WHERE OID = #{oid,jdbcType=VARCHAR} AND DELETED = 0")
    List<CustomeFieldAiDO> queryByOid(String oid);
}

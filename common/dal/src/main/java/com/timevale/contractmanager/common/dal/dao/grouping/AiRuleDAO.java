package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.AiRuleDO;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * The Table ai_rule.
 * ai台账规则信息表-以空间维度记录多少个台账规则
 * <AUTHOR> Kunpeng
 */
public interface AiRuleDAO{

    @Select("SELECT RULE_ID FROM ai_rule WHERE OID = #{oid,jdbcType=VARCHAR} limit 1")
    public String getRuleIdByIdxRuleId(String oid);

    @Update({
            "<script>",
            "UPDATE ai_rule SET INVALID_DESC = #{invalidDesc},STATUS = 0 WHERE RULE_ID = #{ruleId,jdbcType=VARCHAR}",
            "</script>"
    })
    public int invalidRule(AiRuleDO entity);
}

package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * 合同摘要基础数据表
 */
@Data
public class ProcessSummaryBaseDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 合同id
     */
    private String processId;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * ai任务id
     */
    private String aiJobId;

    /**
     * 任务状态，running: 运行中 done: 完成
     */
    private String aiJobStatus;

    /**
     * 最后一次任务是否成功
     */
    private Integer lastJobSuccess;

    /**
     * 数据类型, summary:摘要, keyInfo:关键信息
     */
    private String dataType;

    /**
     * 结果
     */
    private String data;

    /**
     * 扩展数据
     */
    private String extendData;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}


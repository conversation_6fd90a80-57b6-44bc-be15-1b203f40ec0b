package com.timevale.contractmanager.common.dal.configuration;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;

import javax.annotation.concurrent.ThreadSafe;
import java.io.IOException;

@Slf4j
@ThreadSafe
public class HBaseConnectionFactory {

    private Configuration conf = HBaseConfiguration.create();
    private volatile Connection connection;

    public HBaseConnectionFactory(String zkQuorum, String username, String password) {
        this.conf.set("hbase.zookeeper.quorum", zkQuorum);
        this.conf.set("hbase.client.username", username);
        this.conf.set("hbase.client.password", password);
    }

    public Connection getConnection() throws IOException {
        if (this.connection == null) {
            synchronized (this) {
                if (this.connection == null) {
                    Connection conn = ConnectionFactory.createConnection(this.conf);
                    this.addShutdownHook(conn);
                    this.connection = conn;
                }
            }
        }

        return this.connection;
    }

    private void addShutdownHook(final Connection conn) {
        Runtime.getRuntime()
                .addShutdownHook(
                        new Thread(
                                () -> {
                                    if (!conn.isClosed()) {
                                        try {
                                            conn.close();
                                        } catch (IOException e) {
                                            log.warn("close hbase connection exception ", e);
                                        }
                                    }
                                }));
    }
}

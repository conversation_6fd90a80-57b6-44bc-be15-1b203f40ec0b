package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessShareDownloadConfigDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * The Table process_share_download_config.
 * 分享的合同配置信息表
 * <AUTHOR> Kunpeng
 */
public interface ProcessShareDownloadConfigDAO{
    String ALL_COLUMNS =
            "share_download_id,create_time,expire_time,update_time,id,salt,process_id,subject_gid,subject_oid,deleted,access_count,access_limit";

    /**
     * desc:插入表:process_share_download_config.<br/>
     * @param entity entity
     * @return int
     */
    @Insert({
            "<script>",
            "insert into process_share_download_config(subject_gid,subject_oid,access_limit,share_download_id,salt,process_id) values",
            "(#{subjectGid},#{subjectOid},#{accessLimit},#{shareDownloadId},#{salt},#{processId})",
            "</script>"
    })
     int insert(ProcessShareDownloadConfigDO entity);

    @Update({
        "<script>",
        "update process_share_download_config set access_count=#{accessCount} where share_download_id=#{shareDownloadId} and access_count=#{oriAccessCount}",
        "</script>"
    })
    int updateAccessCount(
            @Param("shareDownloadId") String shareDownloadId,
            @Param("accessCount") Integer accessCount,
            @Param("oriAccessCount") Integer oriAccessCount);

    @Select({"select ",ALL_COLUMNS," from process_share_download_config where share_download_id=#{shareDownloadId} and deleted=0"})
    ProcessShareDownloadConfigDO getByShareDownloadId(@Param("shareDownloadId") String shareDownloadId);
}

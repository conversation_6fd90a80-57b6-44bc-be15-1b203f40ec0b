package com.timevale.contractmanager.common.dal.bean.grouping;


import lombok.Data;

import java.util.Objects;

/**
 * The table.
 * 自定义字段配置-系统默认（所有企业共用）
 * <AUTHOR> Kunpeng
 */
@Data
public class CustomeFieldSysDO{

    /**
     * id ID.
     */
    private Long id;
    /**
     * fieldId 字段id.
     */
    private String fieldId;
    /**
     * fieldCode 字段名-对应表格的列名.
     */
    private String fieldCode;
    /**
     * fieldName 字段显示名-对应表格的列标题名.
     */
    private String fieldName;
    /**
     * 专有字段，该字段不可变更，在自定义列表中固定显示 0：否，1：是
     */
    private Integer proprietary;
    /**
     * 是否显示 0：否，1：是
     */
    private Integer display;
    /**
     * 是否冻结 0：否，1：是
     */
    private Integer freezed;
}

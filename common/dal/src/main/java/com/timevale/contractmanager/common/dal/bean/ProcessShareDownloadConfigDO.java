package com.timevale.contractmanager.common.dal.bean;

import java.util.Date;

/**
 * The table.
 * 分享的合同配置信息表
 * <AUTHOR> Kunpeng
 */
public class ProcessShareDownloadConfigDO{

    /**
     * 分享下载id
     */
    private String shareDownloadId;

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * expireTime 失效时间.
     */
    private Date expireTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * salt 盐值.
     */
    private String salt;
    /**
     * processId 合同id.
     */
    private String processId;
    /**
     * subjectGid 主体gid.
     */
    private String subjectGid;
    /**
     * subjectOid 主体oid.
     */
    private String subjectOid;
    /**
     * deleted 删除状态：1-删除，0-正常.
     */
    private Integer deleted;
    /**
     * accessCount 当前访问数量.
     */
    private Integer accessCount;
    /**
     * accessLimit 访问的数量限制.
     */
    private Integer accessLimit;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set expireTime 失效时间.
     */
    public void setExpireTime(Date expireTime){
        this.expireTime = expireTime;
    }

    /**
     * Get expireTime 失效时间.
     *
     * @return the string
     */
    public Date getExpireTime(){
        return expireTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set salt 盐值.
     */
    public void setSalt(String salt){
        this.salt = salt;
    }

    /**
     * Get salt 盐值.
     *
     * @return the string
     */
    public String getSalt(){
        return salt;
    }

    /**
     * Set processId 合同id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 合同id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    /**
     * Set subjectGid 主体gid.
     */
    public void setSubjectGid(String subjectGid){
        this.subjectGid = subjectGid;
    }

    /**
     * Get subjectGid 主体gid.
     *
     * @return the string
     */
    public String getSubjectGid(){
        return subjectGid;
    }

    /**
     * Set subjectOid 主体oid.
     */
    public void setSubjectOid(String subjectOid){
        this.subjectOid = subjectOid;
    }

    /**
     * Get subjectOid 主体oid.
     *
     * @return the string
     */
    public String getSubjectOid(){
        return subjectOid;
    }

    /**
     * Set deleted 删除状态：1-删除，0-正常.
     */
    public void setDeleted(Integer deleted){
        this.deleted = deleted;
    }

    /**
     * Get deleted 删除状态：1-删除，0-正常.
     *
     * @return the string
     */
    public Integer getDeleted(){
        return deleted;
    }

    /**
     * Set accessCount 当前访问数量.
     */
    public void setAccessCount(Integer accessCount){
        this.accessCount = accessCount;
    }

    /**
     * Get accessCount 当前访问数量.
     *
     * @return the string
     */
    public Integer getAccessCount(){
        return accessCount;
    }

    /**
     * Set accessLimit 访问的数量限制.
     */
    public void setAccessLimit(Integer accessLimit){
        this.accessLimit = accessLimit;
    }

    /**
     * Get accessLimit 访问的数量限制.
     *
     * @return the string
     */
    public Integer getAccessLimit(){
        return accessLimit;
    }

    public String getShareDownloadId() {
        return shareDownloadId;
    }

    public void setShareDownloadId(String shareDownloadId) {
        this.shareDownloadId = shareDownloadId;
    }
}

package com.timevale.contractmanager.common.dal.dao.opponententity;

import com.timevale.contractmanager.common.dal.bean.opponententity.MemberCountDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDO;
import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentEntityDTO;
import com.timevale.contractmanager.common.dal.query.opponent.OpponentEntityFixDataQuery;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @Author:jianyang
 * @since 2021-01-22 15:36
 */
public interface OpponentEntityDAO {
	/**
	 * 插入
	 * @param entity
	 * @return
	 */
	@Insert({"INSERT INTO opponent_entity (tenant_gid,entity_unique_id,entity_name,description," ,
			"entity_type,entity_oid,entity_gid,attached_entity_id,create_by_gid,modify_by_gid,uuid," ,
			"create_by_oid,modify_by_oid,authorize_type,tenant_oid,create_process_id," ,
			"social_credit_code,legal_representative_name,credit_code_type) " ,
			"VALUES(#{tenantGid},#{entityUniqueId},#{entityName},#{description},#{entityType},#{entityOid}" ,
			",#{entityGid},#{attachedEntityId},#{createByGid},#{modifyByGid},#{uuid},#{createByOid},#{modifyByOid}," ,
			"#{authorizeType},#{tenantOid},#{createProcessId},#{socialCreditCode},#{legalRepresentativeName},#{creditCodeType})"})
	@Options(useGeneratedKeys = true, keyProperty = "id")
	public int insert(OpponentEntityDO entity);

	/**
	 * 更新opponentEntityDO
	 * @param opponentEntityDO
	 * @return
	 */
	@Update({"Update opponent_entity set description = #{description},modify_by_gid = #{modifyByGid}, " ,
			"deleted = #{deleted}, create_by_gid = #{createByGid},create_time = #{createTime}," ,
			"modify_by_oid = #{modifyByOid},create_by_oid = #{createByOid}, entity_name = #{entityName}," ,
			"authorize_type = #{authorizeType},entity_type = #{entityType},entity_oid = #{entityOid}, entity_gid = #{entityGid}, " ,
			"social_credit_code = #{socialCreditCode}, legal_representative_name=#{legalRepresentativeName}," ,
			"attached_entity_id = #{attachedEntityId}",
			"where id = #{id}"})
	public int update(OpponentEntityDO opponentEntityDO);

	@Update({"UPDATE opponent_entity SET " ,
			"entity_unique_id = #{entityUniqueId},entity_name = #{entityName}" ,
			"where id = #{id}"})
	public void updateEntityName(OpponentEntityDO opponentEntityDO);

	/**
	 * 根据uuid删除
	 * @param uuid
	 * @param deleted
	 */
	@Update("update opponent_entity set attached_entity_id = null,deleted = #{deleted} where uuid = #{uuid}")
	public void updateDeleted(@Param("uuid") String  uuid,@Param("deleted") Integer deleted);

	/**
	 * 根据uuid批量删除实体
	 * @param uuids
	 * @param deleted
	 */
	@Update({"<script>",
			"update opponent_entity set attached_entity_id = null ,deleted = #{deleted} where uuid in",
			"(<foreach collection='uuids' item='item' separator=','> #{item} </foreach>)",
			"</script>"
	})
	public void batchUpdateDeleted(@Param("uuids") List<String> uuids,@Param("deleted") Integer deleted);

	@Update({ "<script>",
			"update opponent_entity set deleted = #{deleted} ,modified_time = now() where id in",
			"(<foreach collection='ids' item='item' separator=','> #{item} </foreach>)",
			"</script>"
	})
	public void batchDeleteIndividualsByIdList(@Param("ids") List<Long> ids,@Param("deleted") Integer deleted);

	@Update({ "<script>",
			"update opponent_entity set entity_unique_id =  #{entityUniqueId} where id in",
			"(<foreach collection='ids' item='item' separator=','> #{item} </foreach>)",
			"</script>"
	})
	public void batchUpdateIndividualsUniqueEntityIdByIdList(@Param("ids") List<Long> ids, @Param("entityUniqueId")String entityUniqueId);

	/**
	 * 根据entityUniqueId查询实体
	 *
	 * @param entityUniqueId
	 * @return
	 */
	@Select("SELECT * FROM opponent_entity " +
			"WHERE entity_unique_id = #{entityUniqueId} and tenant_gid = #{tenantGid} and entity_type = #{entityType} limit 1")
	public OpponentEntityDO getByEntityUniqueId(@Param("entityUniqueId") String entityUniqueId,
			@Param("tenantGid") String tenantGid,
			@Param("entityType") Integer entityType);

	@Select("SELECT * FROM opponent_entity " +
			"WHERE entity_unique_id = #{entityUniqueId} and tenant_gid = #{tenantGid} and entity_type = #{entityType}")
	List<OpponentEntityDO> listByTenantGidEntityUniqueIdEntityType(@Param("entityUniqueId") String entityUniqueId,
												@Param("tenantGid") String tenantGid,
												@Param("entityType") Integer entityType);

	@Select("SELECT * FROM opponent_entity " +
			"WHERE entity_unique_id = #{entityUniqueId} and tenant_gid = #{tenantGid} and entity_type = #{entityType} and " +
			"social_credit_code = #{socialCreditCode} limit 1")
	public OpponentEntityDO getByEntityUniqueIdAndCreditCode(@Param("entityUniqueId") String entityUniqueId,
												@Param("tenantGid") String tenantGid,
												@Param("socialCreditCode") String socialCreditCode,
												@Param("entityType") Integer entityType);
	@Select("SELECT * FROM opponent_entity " +
			"WHERE entity_unique_id = #{entityUniqueId} and tenant_gid = #{tenantGid} and entity_type = #{entityType} and " +
			"social_credit_code = #{socialCreditCode} and deleted = 0 limit 1")
	public OpponentEntityDO getValidByEntityUniqueIdAndCreditCode(@Param("entityUniqueId") String entityUniqueId,
												@Param("tenantGid") String tenantGid,
												@Param("socialCreditCode") String socialCreditCode,
												@Param("entityType") Integer entityType);

	@Select("SELECT * FROM opponent_entity " +
			"WHERE entity_unique_id = #{entityUniqueId} and tenant_gid = #{tenantGid} ")
	public List<OpponentEntityDO> getRiskLevelByEntityUniqueId(@Param("entityUniqueId") String entityUniqueId,
			@Param("tenantGid") String tenantGid);

    @Update({
        "<script>",
        "update opponent_entity ",
        "<set>",
        "<if test=\"entityName != null\"> entity_name = #{entityName},</if>",
        "<if test=\"entityOid != null\"> entity_oid = #{entityOid}, </if>",
        "<if test=\"entityGid != null\"> entity_gid = #{entityGid}, </if>",
        "<if test=\"authorizeType != null\"> authorize_type = #{authorizeType}, </if>",
        "<if test=\"attachedEntityId != null\"> attached_entity_id = #{attachedEntityId}, </if>",
        "<if test=\"entityUniqueId != null\"> entity_unique_id = #{entityUniqueId}, </if>",
        "<if test=\"description != null\"> description = #{description}, </if>",
        "<if test=\"modifyByOid != null\"> modify_by_oid = #{modifyByOid}, </if>",
        "<if test=\"modifyByGid != null\"> modify_by_gid = #{modifyByGid}, </if>",
		"<if test=\"modifiedTime != null\"> modified_time = #{modifiedTime}, </if>",
		"<if test=\"riskLevel != null\"> risk_level = #{riskLevel}, </if>",
		"<if test=\"socialCreditCode != null\"> social_credit_code = #{socialCreditCode}, </if>",
		"<if test=\"legalRepresentativeName != null\"> legal_representative_name = #{legalRepresentativeName}, </if>",
		"<if test=\"creditCodeType != null\"> credit_code_type = #{creditCodeType}, </if>",
        "</set>",
        "where id = #{id}",
			"</script>"
	})
	public int updateEntityInfo(OpponentEntityDO entityDO);

	@Select("SELECT * FROM opponent_entity WHERE uuid = #{uuid}")
	public OpponentEntityDO getByUuid(String uuid);

	@Select("SELECT * FROM  opponent_entity WHERE entity_unique_id = #{entityUniqueId}")
	public List<OpponentEntityDO> listByEntityUniqueId(@Param("entityUniqueId") String entityUniqueId);

	@Select("SELECT * FROM  opponent_entity WHERE entity_unique_id = #{entityUniqueId} and entity_type = #{entityType} and deleted=0")
	public List<OpponentEntityDO> listByEntityUniqueIdAndType(@Param("entityUniqueId") String entityUniqueId, @Param("entityType") Integer entityType);

	@Select({
			"<script>",
			"SELECT * FROM  opponent_entity WHERE ",
			"<if test='phone!=null and email==null'> entity_unique_id = #{phone} </if>",
			"<if test='phone==null and email!=null'> entity_unique_id = #{email} </if>",
			"<if test='phone!=null and email!=null'> entity_unique_id = #{phone}  or entity_unique_id = #{email} </if>",
			"</script>" })
	public List<OpponentEntityDO> listByMultiEntityUniqueId(@Param("phone") String phone, @Param("email") String email);

	@Update({
			"<script>",
			"UPDATE opponent_entity SET entity_oid = #{entityOid} WHERE id IN",
			"<foreach item='item' collection='idList' open='(' separator=',' close=')'> #{item} </foreach>",
			"</script>"
	})
	public void batchUpdateEntityOid(@Param("entityOid") String entityOid, @Param("idList") List<Long> idList);

	@Update({
			"<script>",
			"UPDATE opponent_entity SET risk_level = #{riskLevel} WHERE uuid IN ",
			"<foreach item='item' collection='uuids' open='(' separator=',' close=')'> #{item} </foreach>",
			"</script>"
	})
	public void batchUpdateRiskLevel(@Param("uuids") List<String> uuids, @Param("riskLevel") Integer riskLevel);

	@Select({ "<script>",
			"SELECT * FROM opponent_entity WHERE uuid in",
			"(<foreach collection='uuids' item='item' separator=','> #{item} </foreach>) and deleted = 0",
			"</script>"
	})
	public List<OpponentEntityDO> getIdsByUuid(@Param("uuids") Collection<String> uuids);

	@Select({ "<script>",
			"SELECT * FROM opponent_entity WHERE uuid in",
			"(<foreach collection='uuids' item='item' separator=','> #{item} </foreach>)",
			"<if test='deleted!=null'> and deleted = #{deleted} </if>",
			"</script>"
	})
	public List<OpponentEntityDO> getByUuids(@Param("uuids") List<String> uuids, @Param("deleted") Integer deleted);

	/**
	 * 根据id 查询实体信息
	 *
	 * @param id
	 * @return
	 */
	@Select("SELECT * FROM opponent_entity WHERE id = #{id}")
	public OpponentEntityDO getById(Long id);

	@Select({
        "<script>",
        "SELECT * FROM opponent_entity WHERE id in",
        "(<foreach collection='list' item='item' separator=','> #{item} </foreach>) and deleted = 0",
        "</script>"
    })
    public List<OpponentEntityDO> getByIdList(List<Long> list);

	@Select({
			"<script>",
			"SELECT * FROM opponent_entity WHERE ",
			"tenant_gid = #{gid,jdbcType=VARCHAR} ",
			"<if test='entityType!=null'> AND entity_type = #{entityType} </if>",
			"<if test='authorizeType!=null'> AND authorize_type = #{authorizeType} </if>",
			"<if test='attachedEntityId!=null'> AND attached_entity_id = #{attachedEntityId} </if>",
			"<if test='riskLevel!=null'> AND risk_level = #{riskLevel} </if>",
			"<if test='entityUniqueId!=null'> AND entity_unique_id LIKE CONCAT('%',#{entityUniqueId},'%') </if>",
			"<if test='fuzzyDesc!=null'> AND description LIKE CONCAT('%',#{fuzzyDesc},'%') </if>",
			"<if test='entityName!=null'> AND entity_name LIKE CONCAT('%',#{entityName},'%') </if>",
			"<if test='attachedEntityType == 1'> AND attached_entity_id is not null AND attached_entity_id != 0  </if>",
			"<if test='attachedEntityType == 2'> AND (attached_entity_id is null or attached_entity_id = 0)  </if>",
			"and deleted = 0 order by modified_time desc limit #{offset},#{pageSize}",
			"</script>"
	})
	public List<OpponentEntityDO> listOpponentEntity(OpponentEntityDTO dto);

	@Select({
			"<script>",
			"SELECT * FROM opponent_entity WHERE entity_type = #{entityType} ",
			"<if test='authorizeType!=null'> AND authorize_type = #{authorizeType} </if>",
			"<if test='attachedEntityId!=null'> AND attached_entity_id = #{attachedEntityId} </if>",
			"<if test='riskLevel!=null'> AND risk_level = #{riskLevel} </if>",
			"<if test='entityUniqueId!=null'> AND entity_unique_id LIKE CONCAT('%',#{entityUniqueId},'%') </if>",
			"<if test='fuzzyDesc!=null'> AND description LIKE CONCAT('%',#{fuzzyDesc},'%') </if>",
			"<if test='entityName!=null'> AND entity_name LIKE CONCAT('%',#{entityName},'%') </if>",
			"<if test='attachedEntityType == 1'> AND attached_entity_id is not null AND attached_entity_id != 0  </if>",
			"<if test='attachedEntityType == 2'> AND (attached_entity_id is null or attached_entity_id = 0)  </if>",
			"AND id &gt; #{id}",
			"and deleted = 0 order by id asc limit #{pageSize}",
			"</script>"
	})
	public List<OpponentEntityDO> scrollOpponentEntity(OpponentEntityDTO dto);

	@Select("SELECT * FROM opponent_entity WHERE entity_gid = #{entityGid, jdbcType=VARCHAR}")
	public List<OpponentEntityDO> listEntitiesByGid(@Param("entityGid") String entityGid);

	@Select({
			"<script>",
			"SELECT count(*) FROM opponent_entity WHERE ",
			"tenant_gid = #{gid,jdbcType=VARCHAR} ",
			"<if test='entityType!=null'> AND entity_type = #{entityType} </if>",
			"<if test='authorizeType!=null'> AND authorize_type = #{authorizeType} </if>",
			"<if test='riskLevel!=null'> AND risk_level = #{riskLevel} </if>",
			"<if test='attachedEntityId!=null'> AND attached_entity_id = #{attachedEntityId} </if>",
			"<if test='entityUniqueId!=null'> AND entity_unique_id LIKE CONCAT('%',#{entityUniqueId},'%') </if>",
			"<if test='fuzzyDesc!=null'> AND description LIKE CONCAT('%',#{fuzzyDesc},'%') </if>",
			"<if test='attachedEntityType == 1'> AND attached_entity_id is not null AND attached_entity_id != 0  </if>",
			"<if test='attachedEntityType == 2'> AND (attached_entity_id is null or attached_entity_id = 0)  </if>",
			"<if test='entityName!=null'> AND entity_name LIKE CONCAT('%',#{entityName},'%') </if> and deleted = 0",
			"</script>"
	})
	public long countOpponentEntity(OpponentEntityDTO dto);

    @Select({
        "<script>",
        "select attached_entity_id,count(*) `count` from opponent_entity ",
        "WHERE tenant_gid = #{gid,jdbcType=VARCHAR} AND attached_entity_id in ",
        "(<foreach collection='list' item='item' separator=','> #{item} </foreach>) and deleted = 0",
        "GROUP BY attached_entity_id",
        "</script>"
    })
    public List<MemberCountDO> listMemberCount(
            @Param("gid") String gid, @Param("list") List<Long> list);

    @Select({
        "select attached_entity_id,count(*) `count` from opponent_entity ",
        "WHERE tenant_gid = #{gid,jdbcType=VARCHAR} AND attached_entity_id = #{id} ",
        "and deleted = 0",
    })
    public MemberCountDO getMemberCount(@Param("gid") String gid, @Param("id") Long id);

    @Select("SELECT * FROM opponent_entity WHERE entity_unique_id = #{uniqueEntityId} AND entity_type = #{entityType} AND " +
			"social_credit_code = #{socialCreditCode}")
    public List<OpponentEntityDO> queryAllOpponents(@Param("uniqueEntityId")String uniqueEntityId,
													@Param("entityType")Integer entityType,
													@Param("socialCreditCode")String socialCreditCode);

	@Select("SELECT * FROM opponent_entity WHERE entity_oid = #{entityOid} AND entity_type = #{entityType} AND deleted = #{deleted} ")
	public List<OpponentEntityDO> queryPersonOpponents(@Param("entityOid")String entityOid,
													@Param("entityType")Integer entityType, @Param("deleted") int deleted);
	@Select({
			"<script>",
			"select id from opponent_entity where deleted = 0 and attached_entity_id in " ,
					"(<foreach collection='attachedEntityIdList' item='item' separator=','> #{item} </foreach>)" ,
					"</script>"
	})
	public List<Long> queryIdListByAttachedEntityIdList(
			@Param("attachedEntityIdList") List<Long> attachedEntityIdList);

	/**
	 * 通过实体oid获取租户下的黑名单
	 *
	 * @param tenantGid
	 * @param entityOids
	 * @return
	 */
	@Select({ "<script>",
			"select * from opponent_entity " ,
					"where tenant_gid = #{tenantGid} and risk_level = 2 and entity_oid in " ,
					"(<foreach collection='entityOids' item='item' separator=','> #{item} </foreach>)" ,
					"</script>"
	})
    public List<OpponentEntityDO> getTenantBlackListByEntityOids(@Param("tenantGid") String tenantGid,
													 @Param("entityOids") List<String> entityOids);

	/**
	 * 通过实体gid查询租户下的黑名单
	 * @param tenantGid
	 * @param entityGids
	 * @return
	 */
	@Select({"<script>",
			"select * from opponent_entity " ,
					"where tenant_gid = #{tenantGid} and risk_level = 2 and entity_Gid in " ,
					"(<foreach collection='entityGids' item='item' separator=','> #{item} </foreach>)" ,
					"</script>"
	})
	public List<OpponentEntityDO> getTenantBlackListByGid(@Param("tenantGid") String tenantGid,@Param("entityGids") List<String> entityGids);

	/**
	 * 通过实体gid查询的黑名单信息
	 * @param entityGids
	 * @return
	 */
	@Select({"<script>",
			"select * from opponent_entity " ,
					"where  risk_level = 2 and entity_Gid in " ,
					"(<foreach collection='entityGids' item='item' separator=','> #{item} </foreach>)" ,
					"</script>"
	})
	public List<OpponentEntityDO> getBlackListByEntityGid(@Param("entityGids") List<String> entityGids);

	/**
	 * 通过实体oid查询的黑名单信息
	 * @param entityOids
	 * @return
	 */
	@Select({"<script>",
			"select * from opponent_entity " ,
					"where  risk_level = 2 and entity_oid in " ,
					"(<foreach collection='entityOids' item='item' separator=','> #{item} </foreach>)" ,
					"</script>"
	})
	public List<OpponentEntityDO> getBlackListByEntityOid(@Param("entityOids") List<String> entityOids);


	@Select({"<script>",
			"select * from opponent_entity " ,
			"where  entity_oid =#{entityOid} and deleted = #{deleted  } " ,
			"</script>"
	})
	public List<OpponentEntityDO> getOpponentEntityByEntityOid(@Param("entityOid") String entityOid,
															   @Param("deleted") Integer deleted);

	@Select({"<script>",
			"select * from opponent_entity where tenant_gid = #{tenantGid} and risk_level = 2 " ,
			"and entity_type = #{entityType} and entity_unique_id in " ,
			"(<foreach collection='entityUniqueIds' item='item' separator=','> #{item} </foreach>)" ,
			"</script>"
	})
	public List<OpponentEntityDO> getTenantBlackListByEntityUniqueIds(@Param("tenantGid") String tenantGid,
																	  @Param("entityUniqueIds") List<String> entityUniqueIds,
																	  @Param("entityType") Integer entityType);


	/**
	 * 物理删除
	 * @param id
	 */
	@Delete("delete from opponent_entity where id = #{id}")
	public void delete(Long id);

	/**
	 * 批量物理删除
	 * @param ids
	 */
	@Delete({ "<script>",
			"delete from opponent_entity where id in",
			"(<foreach collection='ids' item='item' separator=','> #{item} </foreach>)",
			"</script>"
	})
	public void batchDelete(@Param("ids") List<Long> ids);


	/**
	 * 获取检测的相对方（只检测国内企业，及证件类型为1、2）
	 * @param tenantGid
	 * @param riskLevel
	 * @param deleted
	 * @return
	 */
	@Select({"<script>",
			"select entity_unique_id, social_credit_code from opponent_entity where ",
			"tenant_gid = #{tenantGid,jdbcType=VARCHAR}",
			"and entity_type = #{entityType,jdbcType=VARCHAR}",
			"and credit_code_type != 3",
			"<if test='startTime!=null'> AND create_time &gt;=#{startTime} </if>",
			"<if test='endTime!=null'> AND create_time &lt;=#{endTime} </if>",
			"<if test='deleted!=null'> AND deleted &lt;=#{deleted} </if>",
			"<if test='riskLevel!=null'> AND risk_level=#{riskLevel} </if>",
			"order by create_time asc",
			"</script>"
	})
	public List<OpponentEntityDO> getDomesticDetectionEntity(@Param("tenantGid") String tenantGid,
										   @Param("entityType") Integer entityType,
										   @Param("riskLevel") Integer riskLevel,
										   @Param("deleted") Integer deleted,
										   @Param("startTime")Date startTime,
										   @Param("endTime") Date endTime);

	/**
	 * 获取检测的相对方（只检测国内企业，及证件类型为1、2）
	 * @param tenantGid
	 * @param riskLevel
	 * @param deleted
	 * @return
	 */
	@Select({"<script>",
			"select count(tenant_gid) from opponent_entity where ",
			"tenant_gid = #{tenantGid,jdbcType=VARCHAR}",
			"and entity_type = #{entityType,jdbcType=VARCHAR}",
			"and credit_code_type != 3",
			"<if test='startTime!=null'> AND create_time &gt;=#{startTime} </if>",
			"<if test='endTime!=null'> AND create_time &lt;=#{endTime} </if>",
			"<if test='deleted!=null'> AND deleted &lt;=#{deleted} </if>",
			"<if test='riskLevel!=null'> AND risk_level=#{riskLevel} </if>",
			"</script>"
	})
	long countDomesticDetectionEntity(@Param("tenantGid") String tenantGid,
													 @Param("entityType") Integer entityType,
													 @Param("riskLevel") Integer riskLevel,
													 @Param("deleted") Integer deleted,
													 @Param("startTime")Date startTime,
													 @Param("endTime") Date endTime);



	/**
	 * 获取检测的相对方（只检测国内企业，及证件类型为1、2）
	 * @param tenantGid
	 * @param riskLevel
	 * @param deleted
	 * @return
	 */
	@Select({"<script>",
			"select entity_unique_id, social_credit_code from opponent_entity where ",
			"tenant_gid = #{tenantGid,jdbcType=VARCHAR}",
			"and entity_type = #{entityType,jdbcType=VARCHAR}",
			"and credit_code_type != 3",
			"<if test='startTime!=null'> AND create_time &gt;=#{startTime} </if>",
			"<if test='endTime!=null'> AND create_time &lt;=#{endTime} </if>",
			"<if test='deleted!=null'> AND deleted &lt;=#{deleted} </if>",
			"<if test='riskLevel!=null'> AND risk_level=#{riskLevel} </if>",
			"order by create_time asc",
			"limit #{offset},#{pageSize}",
			"</script>"
	})
	public List<OpponentEntityDO> getDomesticDetectionEntityByPage(@Param("tenantGid") String tenantGid,
												 @Param("entityType") Integer entityType,
												 @Param("riskLevel") Integer riskLevel,
												 @Param("deleted") Integer deleted,
												 @Param("startTime")Date startTime,
												 @Param("endTime") Date endTime,
												 @Param("offset") Integer offset,
												 @Param("pageSize") Integer pageSize);


	@Update("update opponent_entity set detection_org_id = #{detectionOrgId} where id = #{id}")
	int updateDetectionOrgId(@Param("detectionOrgId") String detectionOrgId,@Param("id") long id);

	int updateById(OpponentEntityDO data);


	/**
	 * 根据oid拉出来列表
	 * @param entityType 不是必传，不传只根据 entityOid 查询  OpponentEntityTypeEnum
	 */
	List<OpponentEntityDO> listByEntityOidAndEntityType(@Param("entityOid") String entityOid,
														@Param("entityType") Integer entityType);

    int clearAttachedEntityId(Long id);
}

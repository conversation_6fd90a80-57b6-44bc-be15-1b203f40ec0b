package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * The table. 纸质合同流程用户信息
 *
 * <AUTHOR> Kunpeng
 */
@Data
public class OfflineProcessAccountDO {

    /** id 自增id. */
    private Long id;
    /** processId 合同流程id. */
    private String processId;
    /** account 用户账号. */
    private String account;
    /** accountName 用户姓名. */
    private String accountName;
    /** subjectName 主体名称. */
    private String subjectName;
    /** subjectType 主体类型. */
    private Integer subjectType;
    /** role 主体角色. */
    private String role;
    /** createTime 创建时间. */
    private Date createTime;
    /** updateTime 更新时间. */
    private Date updateTime;
}

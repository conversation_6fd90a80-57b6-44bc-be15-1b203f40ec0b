package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.GroupingInfoDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * The Table grouping_info.
 * 合同归档记录表
 * <AUTHOR> Kunpeng
 */
public interface GroupingInfoDAO{

    /**
     * desc:批量插入表:grouping_info.<br/>
     * @param list list
     * @return int
     */
    @Insert({
            "<script>",
            "INSERT INTO grouping_info (MENU_ID, PROCESS_ID, CONTRACT_NO, SUBJECT_GID , SUBJECT_OID, OPERATOR_GID, OPERATOR_OID) VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.menuId,jdbcType=VARCHAR}, #{item.processId,jdbcType=VARCHAR}, #{item.contractNo,jdbcType=VARCHAR}, #{item.subjectGid,jdbcType=VARCHAR} , #{item.subjectOid,jdbcType=VARCHAR}, #{item.operatorGid,jdbcType=VARCHAR}, #{item.operatorOid,jdbcType=VARCHAR})",
            "</foreach>",
            "</script>"
    })
    public int insertBatch(List<GroupingInfoDO> list);

    @Delete({
            "<script>",
            "DELETE FROM grouping_info ",
            "WHERE ID IN",
            "(<foreach collection='list' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    public int deleteByIds(List<Long> list);

    @Delete({
            "<script>",
            "DELETE FROM grouping_info WHERE MENU_ID = #{menuId,jdbcType=VARCHAR}",
            "AND PROCESS_ID IN (<foreach collection='list' item='item' separator=','> #{item}</foreach>)",
            "AND SUBJECT_OID = #{subjectOid,jdbcType=VARCHAR}",
            "</script>"
    })
    public int deleteByProcessIdList(@Param("menuId") String menuId,@Param("list") List<String> list,@Param("subjectOid") String subjectOid);

    @Select("SELECT * FROM grouping_info WHERE PROCESS_ID = #{processId,jdbcType=VARCHAR} limit 1")
    public GroupingInfoDO getByProcessId(String processId);

    @Select("SELECT * FROM grouping_info WHERE PROCESS_ID = #{processId,jdbcType=VARCHAR}")
    public List<GroupingInfoDO> getAllByProcessId(String processId);

    /**
     * desc:根据普通索引IdxProcessMenuOid获取数据:grouping_info.<br/>
     * @param menuId menuId
     * @param processId processId
     * @param subjectOid subjectOid
     * @return List<GroupingInfoDO>
     */
    @Select({
            "SELECT * FROM grouping_info WHERE ",
            "PROCESS_ID = #{processId,jdbcType=VARCHAR}",
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR}",
            "AND SUBJECT_OID = #{subjectOid,jdbcType=VARCHAR}"
    })
    public List<GroupingInfoDO> queryByIdxProcessMenuOid(@Param("processId") String processId,@Param("menuId") String menuId,@Param("subjectOid") String subjectOid);

    @Select({
            "SELECT * FROM grouping_info WHERE ",
            "PROCESS_ID = #{processId,jdbcType=VARCHAR} ",
            "AND MENU_ID = #{menuId,jdbcType=VARCHAR}"
    })
    public List<GroupingInfoDO> queryByProcessMenu(@Param("processId") String processId,@Param("menuId") String menuId);

    @Select({
            "SELECT * FROM grouping_info WHERE ",
            "PROCESS_ID = #{processId,jdbcType=VARCHAR} ",
            "AND SUBJECT_OID = #{subjectOid,jdbcType=VARCHAR}"
    })
    public List<GroupingInfoDO> queryByProcessId(@Param("processId")String processId,@Param("subjectOid") String subjectOid);

    @Select({
            "SELECT ID,PROCESS_ID,SUBJECT_OID FROM grouping_info WHERE ",
            "MENU_ID = #{menuId,jdbcType=VARCHAR} LIMIT #{limit}",
    })
    public List<GroupingInfoDO> queryByMenuId(@Param("menuId") String menuId,@Param("limit") Integer limit);

    @Select({
            "<script>",
            "SELECT PROCESS_ID FROM grouping_info WHERE ",
            "MENU_ID = #{menuId,jdbcType=VARCHAR} ",
            "AND PROCESS_ID IN ",
            "(<foreach collection='processIds' item='processId' separator=','>",
            "#{processId}",
            "</foreach>)",
            "LIMIT #{limit}",
            "</script>"
    })
    public List<String> queryByMenuIdAndProcessIds(
            @Param("menuId") String menuId,@Param("processIds") List<String> processIds,@Param("limit") Integer limit);

    @Select({
            "<script>",
            "SELECT PROCESS_ID FROM grouping_info WHERE ",
            "MENU_ID IN",
            "(<foreach collection='menuIds' item='menuId' separator=','>",
            "#{menuId}",
            "</foreach>)",
            "AND PROCESS_ID IN ",
            "(<foreach collection='processIds' item='processId' separator=','>",
            "#{processId}",
            "</foreach>)",
            "LIMIT #{limit}",
            "</script>"
    })
    List<String> queryByMenuIdsAndProcessIds(
            @Param("menuIds") List<String> menuIds,@Param("processIds") List<String> processIds,@Param("limit") Integer limit);

    @Select({
            "select * from grouping_info where subject_gid = #{subjectGid} limit 1"
    })
    GroupingInfoDO queryOneGroupingInfoBySubjectGid(@Param("subjectGid") String subjectGid);
}

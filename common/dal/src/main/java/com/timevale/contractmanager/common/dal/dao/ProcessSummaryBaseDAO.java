package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ProcessSummaryBaseDO;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 合同摘要基础表
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Mapper
public interface ProcessSummaryBaseDAO {

    @Insert({
        "insert into process_summary_base (process_id, file_id, ai_job_id, ai_job_status, last_job_success, data_type, `data`)",
        "values (#{processId}, #{fileId}, #{aiJobId}, #{aiJobStatus}, #{lastJobSuccess}, #{dataType}, #{data})"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ProcessSummaryBaseDO processSummaryBaseDO);

    @Select({
        "select * from process_summary_base where process_id = #{processId} and file_id = #{fileId} and data_type = #{dataType}"
    })
    ProcessSummaryBaseDO findByProcessIdAndFileIdAndDataType(
            @Param("processId") String processId,
            @Param("fileId") String fileId,
            @Param("dataType") String dataType);

    @Select({
        "select * from process_summary_base where process_id = #{processId} and file_id = #{fileId}"
    })
    List<ProcessSummaryBaseDO> listByProcessIdAndFileId(@Param("processId") String processId, @Param("fileId") String fileId);

    @Select({
        "select * from process_summary_base where process_id = #{processId}"
    })
    List<ProcessSummaryBaseDO> listByProcessId(@Param("processId") String processId);

    @Select({
        "select * from process_summary_base where id=#{id}"
    })
    ProcessSummaryBaseDO getById(@Param("id") Long id);

    @Update({
        "update process_summary_base set data = #{data}, ai_job_status = #{aiJobStatus}, last_job_success=#{lastJobSuccess} where id = #{id}"
    })
    int updateDataAndAiJobStatusById(
            @Param("id") Long id,
            @Param("data") String data,
            @Param("aiJobStatus") String aiJobStatus,
            @Param("lastJobSuccess") Integer lastJobSuccess);

    @Update({
        "update process_summary_base set ai_job_id = #{aiJobId}, ai_job_status = #{aiJobStatus},",
        "extend_data = #{extend_data} where id = #{id}"
    })
    int updateJobInfoById(
            @Param("id") Long id,
            @Param("aiJobId") String aiJobId,
            @Param("aiJobStatus") String aiJobStatus,
            @Param("extend_data") String extend_data);
}

package com.timevale.contractmanager.common.dal.bean.rule;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;


@Data
@Builder
public class RuleConditionDO {
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 规则表Id
	 */
	private String ruleId;
	/**
	 * 算子Id
	 */
	private String operatorId;
	/**
	 * 父算子id
	 */
	private String parentOperatorId;
	/**
	 * 算子参数
	 */
	private String conditionParams;
	/**
	 * 算子类型
	 */
	private Integer operatorType;
	private String fieldId;
	private String fieldName;
	private Integer fieldType;
	private Integer deleted;
	private Integer matchType;
	/**
	 * 排序值
	 */
	private Integer operatorWeight;

	private Integer bizType;

	@Tolerate
	public RuleConditionDO(){}
}

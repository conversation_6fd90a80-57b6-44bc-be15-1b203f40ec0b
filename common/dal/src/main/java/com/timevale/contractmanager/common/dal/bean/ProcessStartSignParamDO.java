package com.timevale.contractmanager.common.dal.bean;

import java.util.Date;

/**
 * The table.
 * 流程发起签署参数临时存储表
 * <AUTHOR> Kunpeng
 */
public class ProcessStartSignParamDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 主键id.
     */
    private Long id;
    /**
     * processId 主流程id.
     */
    private String processId;
    /**
     * startSignParam 发起签署参数.
     */
    private String startSignParam;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 主键id.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 主键id.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set processId 主流程id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 主流程id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    /**
     * Set startSignParam 发起签署参数.
     */
    public void setStartSignParam(String startSignParam){
        this.startSignParam = startSignParam;
    }

    /**
     * Get startSignParam 发起签署参数.
     *
     * @return the string
     */
    public String getStartSignParam(){
        return startSignParam;
    }
}

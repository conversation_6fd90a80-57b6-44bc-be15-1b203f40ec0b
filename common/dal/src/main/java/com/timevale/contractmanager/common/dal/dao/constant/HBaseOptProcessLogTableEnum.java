package com.timevale.contractmanager.common.dal.dao.constant;


import org.apache.hadoop.hbase.util.Bytes;

/** hbase表信息 */
public enum HBaseOptProcessLogTableEnum{
    OPT_LOG("contract_manager:process_opt_log", "f", Bytes.toBytes("f")),
    ;

    /** hbase表名 */
    private String table;

    /** 列族 */
    private String family;

    /** hbase列族 */
    private byte[] familyBytes;

    HBaseOptProcessLogTableEnum(String table, String family, byte[] familyBytes) {
        this.table = table;
        this.family = family;
        this.familyBytes = familyBytes;
    }

    public String getTable() {
        return table;
    }

    public String getFamily() {
        return family;
    }

    public byte[] getFamilyBytes() {
        return familyBytes;
    }
}

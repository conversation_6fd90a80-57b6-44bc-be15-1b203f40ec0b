package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.ContractFileBizRelationDO;
import com.timevale.contractmanager.common.dal.bean.ProcessFileAuthDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * ProcessFileAuth
 *
 * <AUTHOR>
 * @since 2024/3/21 5:07 下午
 */
public interface ProcessFileAuthDAO {

    String BASE_FIELD =
            "process_id, file_id, file_type, auth_id, auth_extend, auth_type, permissions, oid, gid";

    @Select("select * from process_file_auth where process_id = #{processId}")
    List<ProcessFileAuthDO> queryByProcessId(@Param("processId") String processId);

    @Select("select * from process_file_auth where process_id = #{processId} and file_type = #{fileType}")
    List<ProcessFileAuthDO> queryByProcessIdAndFileType(@Param("processId") String processId, @Param("fileType") String fileType);

    @Select({
            "<script>",
            "select * from process_file_auth where process_id in",
            "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            " and file_type = #{fileType}",
            "</script>"
    })
    List<ProcessFileAuthDO> queryByProcessIdsAndFileType(@Param("list") List<String> processIds, @Param("fileType") String fileType);

    @Select("SELECT count(*) FROM process_file_auth WHERE  process_id = #{processId} ")
    long countByProcessId(@Param("processId") String processId);

    @Insert({
            "<script>",
            " INSERT INTO process_file_auth(" + BASE_FIELD + ")",
            " VALUES ",
            " <foreach collection='list' item='item' separator=','>",
            "   (#{item.processId},",
            "   #{item.fileId},",
            "   #{item.fileType},",
            "   #{item.authId},",
            "   #{item.authExtend},",
            "   #{item.authType},",
            "   #{item.permissions},",
            "   #{item.oid},",
            "   #{item.gid})",
            " </foreach>",
            "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int batchInsert(List<ProcessFileAuthDO> list);


    @Delete({
            "<script>",
            "delete from process_file_auth where id in",
            "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    int deleteByIds(@Param("list") List<String> ids);
}

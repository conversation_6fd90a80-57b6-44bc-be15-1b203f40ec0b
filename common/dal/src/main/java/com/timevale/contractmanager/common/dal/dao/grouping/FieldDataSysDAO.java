package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.FieldDataSysDO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * The Table field_data_sys.
 * 系统相关的字段数据源，如来源，平台等固定内容
 * <AUTHOR> Kunpeng
 */
public interface FieldDataSysDAO{
    /**
     * desc:根据普通索引IdxFieldCode获取数据:field_data_sys.<br/>
     * @param type 类型
     * @return List<FieldDataSysDO>
     */
    @Select("SELECT K,V FROM field_data_sys WHERE TYPE = #{type}")
    public List<FieldDataSysDO> queryByIdxType(Integer type);
}

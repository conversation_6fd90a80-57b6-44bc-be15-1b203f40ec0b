package com.timevale.contractmanager.common.dal.bean.grouping;


/**
 * The table.
 * 存储AI解析后的字段内容-企业维度
 * <AUTHOR> Kunpeng
 */
public class FieldDataAiDO{

    /**
     * id ID.
     */
    private Long id;
    /**
     * v 字段值.
     */
    private String v;
    /**
     * gid 空间gid.
     */
    private String gid;
    /**
     * oid 空间Oid.
     */
    private String oid;
    /**
     * fieldCode 字段code.
     */
    private String fieldCode;

    private String ruleId;

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set v 字段值.
     */
    public void setV(String v){
        this.v = v;
    }

    /**
     * Get v 字段值.
     *
     * @return the string
     */
    public String getV(){
        return v;
    }

    /**
     * Set gid 空间gid.
     */
    public void setGid(String gid){
        this.gid = gid;
    }

    /**
     * Get gid 空间gid.
     *
     * @return the string
     */
    public String getGid(){
        return gid;
    }

    /**
     * Set oid 空间Oid.
     */
    public void setOid(String oid){
        this.oid = oid;
    }

    /**
     * Get oid 空间Oid.
     *
     * @return the string
     */
    public String getOid(){
        return oid;
    }

    /**
     * Set fieldCode 字段code.
     */
    public void setFieldCode(String fieldCode){
        this.fieldCode = fieldCode;
    }

    /**
     * Get fieldCode 字段code.
     *
     * @return the string
     */
    public String getFieldCode(){
        return fieldCode;
    }
}

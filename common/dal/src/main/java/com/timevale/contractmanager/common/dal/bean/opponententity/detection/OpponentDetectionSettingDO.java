package com.timevale.contractmanager.common.dal.bean.opponententity.detection;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-08-11 11:00
 */
@Data
@Builder
public class OpponentDetectionSettingDO {
	/**
	 * id 自增主键.
	 */
	private Long id;
	/**
	 * deleted 是否删除 0-否；1-是
	 */
	private Integer deleted;
	/**
	 * createTime 创建时间.
	 */
	private Date createTime;
	/**
	 * modifyTime 更新时间.
	 */
	private Date modifyTime;
	/**
	 *tenantGid 租户gid
	 */
	private String tenantGid;
	/**
	 * 经营范围
	 */
	private String businessScope;
	/**
	 * 是否开启实时检测,1:开启,2:不开启
	 */
	private Integer realTimeDetection;
	/**
	 * 是否开启推送
	 */
	private Integer push;
	/**
	 * 推送对象
	 */
	private String pushObject;
	/**
	 * 是否开启立即推送,1:开启,2:不开启
	 */
	private Integer immediatelyPush;
	/**
	 * 已用检测数量
	 */
	private Integer usedDetectionNum;

	/**
	 * 总检测数量
	 */
	private Integer detectionNum;

	/**
	 * 检测项
	 */
	private String detectionItems;

	/**
	 * 是否开启经营范围检测
	 */
	private Integer businessScopeDetection;

	/**
	 * 是否系统自动保存相对方
	 */
	private Integer autoSaveOpponent;
	@Tolerate
	public OpponentDetectionSettingDO(){}
}

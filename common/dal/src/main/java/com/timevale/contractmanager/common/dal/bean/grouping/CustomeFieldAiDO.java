package com.timevale.contractmanager.common.dal.bean.grouping;


import lombok.Data;

/**
 * The table.
 * 自定义字段配置-AI解析生成，以企业维度记录
 * <AUTHOR> Kunpeng
 */
@Data
public class CustomeFieldAiDO{

    /**
     * id ID.
     */
    private Long id;
    /**
     * gid 空间gid.
     */
    private String gid;
    /**
     * oid 空间Oid.
     */
    private String oid;
    /**
     * fieldId 字段id.
     */
    private String fieldId;
    /**
     * fieldCode 字段名-对应表格的列名.
     */
    private String fieldCode;
    /**
     * fieldName 字段显示名-对应表格的列标题名.
     */
    private String fieldName;

    /**
     * fieldName 规则id
     */
    private String ruleId;

    /**
     * 区分是系统字段还是自定义字段
     */
    private Integer fieldType;

    /**
     * 自定义字段类型
     */
    private Integer ledgerFieldType;

    private Integer deleted;

    private Boolean searchable;

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getFieldType() {
        return fieldType;
    }

    public void setFieldType(Integer fieldType) {
        this.fieldType = fieldType;
    }

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set gid 空间gid.
     */
    public void setGid(String gid){
        this.gid = gid;
    }

    /**
     * Get gid 空间gid.
     *
     * @return the string
     */
    public String getGid(){
        return gid;
    }

    /**
     * Set oid 空间Oid.
     */
    public void setOid(String oid){
        this.oid = oid;
    }

    /**
     * Get oid 空间Oid.
     *
     * @return the string
     */
    public String getOid(){
        return oid;
    }

    /**
     * Set fieldId 字段id.
     */
    public void setFieldId(String fieldId){
        this.fieldId = fieldId;
    }

    /**
     * Get fieldId 字段id.
     *
     * @return the string
     */
    public String getFieldId(){
        return fieldId;
    }

    /**
     * Set fieldCode 字段名-对应表格的列名.
     */
    public void setFieldCode(String fieldCode){
        this.fieldCode = fieldCode;
    }

    /**
     * Get fieldCode 字段名-对应表格的列名.
     *
     * @return the string
     */
    public String getFieldCode(){
        return fieldCode;
    }

    /**
     * Set fieldName 字段显示名-对应表格的列标题名.
     */
    public void setFieldName(String fieldName){
        this.fieldName = fieldName;
    }

    /**
     * Get fieldName 字段显示名-对应表格的列标题名.
     *
     * @return the string
     */
    public String getFieldName(){
        return fieldName;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }
}

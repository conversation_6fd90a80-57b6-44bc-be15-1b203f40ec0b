package com.timevale.contractmanager.common.dal.bean.grouping;

import java.util.Date;

/**
 * The table.
 * 台账规则模板表
 * <AUTHOR> Kunpeng
 */
public class AiRuleTemplateDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id ID.
     */
    private Long id;
    /**
     * gid 空间gid.
     */
    private String gid;
    /**
     * oid 空间oid.
     */
    private String oid;
    /**
     * uuid 台账规则模板id.
     */
    private String uuid;
    /**
     * fileid 模板文件fileId.
     */
    private String fileId;
    /**
     * ruleId 台账规则id.
     */
    private String ruleId;
    /**
     * fileKey 模板文件fileKey.
     */
    private String fileKey;

    private String fileName;
    /**
     * templateId 模板id.
     */
    private String templateId;
    /**
     * type 台账规则模板类型 1为流程模板 2为本地模板.
     */
    private Integer type;

    private String menuId;

    /**
     * 是否解析 0否1是
     */
    private Integer analysisFlag;

    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set gid 空间gid.
     */
    public void setGid(String gid){
        this.gid = gid;
    }

    /**
     * Get gid 空间gid.
     *
     * @return the string
     */
    public String getGid(){
        return gid;
    }

    /**
     * Set oid 空间oid.
     */
    public void setOid(String oid){
        this.oid = oid;
    }

    /**
     * Get oid 空间oid.
     *
     * @return the string
     */
    public String getOid(){
        return oid;
    }

    /**
     * Set uuid 台账规则模板id.
     */
    public void setUuid(String uuid){
        this.uuid = uuid;
    }

    /**
     * Get uuid 台账规则模板id.
     *
     * @return the string
     */
    public String getUuid(){
        return uuid;
    }


    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    /**
     * Set ruleId 台账规则id.
     */
    public void setRuleId(String ruleId){
        this.ruleId = ruleId;
    }

    /**
     * Get ruleId 台账规则id.
     *
     * @return the string
     */
    public String getRuleId(){
        return ruleId;
    }

    /**
     * Set fileKey 模板文件fileKey.
     */
    public void setFileKey(String fileKey){
        this.fileKey = fileKey;
    }

    /**
     * Get fileKey 模板文件fileKey.
     *
     * @return the string
     */
    public String getFileKey(){
        return fileKey;
    }

    /**
     * Set templateId 模板id.
     */
    public void setTemplateId(String templateId){
        this.templateId = templateId;
    }

    /**
     * Get templateId 模板id.
     *
     * @return the string
     */
    public String getTemplateId(){
        return templateId;
    }

    /**
     * Set type 台账规则模板类型 1为流程模板 2为本地模板.
     */
    public void setType(Integer type){
        this.type = type;
    }

    /**
     * Get type 台账规则模板类型 1为流程模板 2为本地模板.
     *
     * @return the string
     */
    public Integer getType(){
        return type;
    }

    public Integer getAnalysisFlag() {
        return analysisFlag;
    }

    public void setAnalysisFlag(Integer analysisFlag) {
        this.analysisFlag = analysisFlag;
    }
}

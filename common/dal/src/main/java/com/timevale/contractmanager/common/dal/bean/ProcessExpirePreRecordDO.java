package com.timevale.contractmanager.common.dal.bean;

import java.util.Date;

/**
 * The table.
 * 合同过期预处理记录
 * <AUTHOR> Kunpeng
 */
public class ProcessExpirePreRecordDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * expireTime 过期时间.
     */
    private Date expireTime;
    /**
     * modifyTime 更新时间.
     */
    private Date modifyTime;
    /**
     * id 自增主键.
     */
    private Long id;
    /**
     * processId 合同流程id.
     */
    private String processId;
    /**
     * expireType 过期类型.
     */
    private Integer expireType;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set expireTime 过期时间.
     */
    public void setExpireTime(Date expireTime){
        this.expireTime = expireTime;
    }

    /**
     * Get expireTime 过期时间.
     *
     * @return the string
     */
    public Date getExpireTime(){
        return expireTime;
    }

    /**
     * Set modifyTime 更新时间.
     */
    public void setModifyTime(Date modifyTime){
        this.modifyTime = modifyTime;
    }

    /**
     * Get modifyTime 更新时间.
     *
     * @return the string
     */
    public Date getModifyTime(){
        return modifyTime;
    }

    /**
     * Set id 自增主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 自增主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set processId 合同流程id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 合同流程id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    /**
     * Set expireType 过期类型.
     */
    public void setExpireType(Integer expireType){
        this.expireType = expireType;
    }

    /**
     * Get expireType 过期类型.
     *
     * @return the string
     */
    public Integer getExpireType(){
        return expireType;
    }
}

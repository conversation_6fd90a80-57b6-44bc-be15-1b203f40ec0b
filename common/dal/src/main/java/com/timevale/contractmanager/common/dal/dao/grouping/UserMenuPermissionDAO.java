package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.UserMenuPermissionDO;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

/**
 * The Table user_menu_permission.
 * 用户拥有菜单的角色权限-只有企业空间下的用户才记录，个人空间无需记录
 * <AUTHOR> Kunpeng
 */
public interface UserMenuPermissionDAO{

    /**
     * desc:插入表:user_menu_permission.<br/>
     * @param entity entity
     * @return int
     */
    @Insert({
            "INSERT INTO user_menu_permission (OID, GID,NAME, EMAIL , MENU_ID, ROLE_ID, MOBILE_NO, AUTHORIZER, AUTHORIZE_TYPE, tenant_oid, tenant_gid) VALUES ",
            "(#{oid,jdbcType=VARCHAR}, #{gid,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR} ," +
            " #{menuId,jdbcType=VARCHAR}, #{roleId,jdbcType=VARCHAR}," +
            " #{mobileNo,jdbcType=VARCHAR}, #{authorizer,jdbcType=VARCHAR}, #{authorizeType,jdbcType=INTEGER}, #{tenantOid}, #{tenantGid})"
    })
    public int insert(UserMenuPermissionDO entity);


    @Insert({
            "<script>",
            "INSERT INTO user_menu_permission (OID, GID,NAME, EMAIL , MENU_ID, ROLE_ID, MOBILE_NO, AUTHORIZER, AUTHORIZE_TYPE, tenant_oid, tenant_gid) VALUES ",
            "<foreach collection='list' separator=',' item='item'>",
            "(#{item.oid}, #{item.gid}, #{item.name}, #{item.email}, #{item.menuId}, #{item.roleId}," +
            "#{item.mobileNo}, #{item.authorizer}, #{item.authorizeType}, #{item.tenantOid}, #{item.tenantGid})",
            "</foreach>",
            "</script>",
    })
    int batchInsert(@Param("list") List<UserMenuPermissionDO> list);

    @Update({
            "<script>",
            "<foreach collection='list' separator=';' item='item'>",
            "UPDATE user_menu_permission SET",
            "ROLE_ID = #{item.roleId} ,modify_time = now(), name = #{item.name}",
            "WHERE ID = #{item.id}",
            "</foreach>",
            "</script>",
    })
    int batchUpdateRoleAndName(List<UserMenuPermissionDO> list);

    @Delete({
            "<script>",
            "DELETE FROM user_menu_permission WHERE ",
            "MENU_ID = #{menuId,jdbcType=VARCHAR} ",
            "AND authorizer IN ",
            "(<foreach collection='authorizers' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    public int deleteByMenuIdOid(@Param("menuId") String menuId, @Param("authorizers") List<String> authorizers);

    @Delete({
            "<script>",
            "DELETE FROM user_menu_permission WHERE ",
            "MENU_ID IN ",
            "(<foreach collection='menuIds' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "AND authorizer = #{authorizer,jdbcType=VARCHAR}",
            "</script>"
    })
    public int deleteByMenuIdsAuthorizer(@Param("menuIds") List<String> menuIds, @Param("authorizer") String authorizer);

    @Delete( "DELETE FROM user_menu_permission WHERE MENU_ID = #{menuId,jdbcType=VARCHAR} ")
    public int deleteByMenuId(String menuId);

    @Select({
            "<script>",
            "SELECT * FROM user_menu_permission WHERE ",
            "MENU_ID = #{menuId,jdbcType=VARCHAR} ",
            "<if test='name!=null'> AND NAME LIKE CONCAT('%',#{name,jdbcType=INTEGER},'%') </if>",
            "limit #{offset},#{pageSize}",
            "</script>"
    })
    public List<UserMenuPermissionDO> queryByMenuIdPage(@Param("menuId") String menuId, @Param("name") String name,
                                                        @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    @Select({
            "<script>",
            "SELECT role_id, menu_id FROM user_menu_permission WHERE ",
            "authorizer IN ",
            "(<foreach collection='authorizers' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    @Deprecated
    public List<UserMenuPermissionDO> queryByAuthorizers(@Param("authorizers") List<String> authorizers);


    @Select({
            "<script>",
            "SELECT id, role_id, menu_id FROM user_menu_permission WHERE ",
            "tenant_gid = #{tenantGid}",
            "and authorizer IN ",
            "(<foreach collection='authorizers' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    List<UserMenuPermissionDO> queryByAuthorizersAndTenantGid(@Param("tenantGid") String tenantGid,
                                                              @Param("authorizers") List<String> authorizers);

    @Select({
            "<script>",
            "SELECT * FROM user_menu_permission WHERE MENU_ID = #{menuId,jdbcType=VARCHAR}",
            "AND authorizer IN ",
            "(<foreach collection='authorizers' item='item' separator=','>",
            "#{item}",
            "</foreach>)",
            "</script>"
    })
    public List<UserMenuPermissionDO> queryByAuthorizersMenuId(@Param("menuId") String menuId,
                                                               @Param("authorizers") List<String> authorizers);


    @Select({
            "<script>",
            "SELECT authorizer FROM user_menu_permission WHERE MENU_ID = #{menuId}",
            "AND authorize_type = #{authorizeType}",
            "</script>"
    })
    List<String> queryByMenuIdAndAuthorizeType(@Param("menuId") String menuId,
                                               @Param("authorizeType") Integer authorizeType);






    /**
     * 根据授权类型，查询企业分类id
     * @see MenuAuthorizeTypeEnum
     */
    @Select({
            "<script>",
            "SELECT id, menu_id, authorizer  FROM user_menu_permission WHERE tenant_gid = #{tenantGid}",
            "AND authorize_type = #{authorizeType} ",
            "AND <if test='lastId != null'>id > #{lastId}</if>",
            "order by id asc limit #{size} ",
            "</script>"
    })
    List<UserMenuPermissionDO> scrollQueryMenuIdByAuthorizeType(@Param("tenantGid") String tenantGid,
                                                                @Param("authorizeType") Integer authorizeType,
                                                                @Param("lastId") Long lastId, @Param("size") int size);


}

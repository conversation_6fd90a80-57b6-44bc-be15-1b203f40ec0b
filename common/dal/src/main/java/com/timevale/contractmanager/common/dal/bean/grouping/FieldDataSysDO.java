package com.timevale.contractmanager.common.dal.bean.grouping;


/**
 * The table.
 * 系统相关的字段数据源，如来源，平台等固定内容
 * <AUTHOR> Kunpeng
 */
public class FieldDataSysDO{

    /**
     * id ID.
     */
    private Long id;
    /**
     * v 字段值.
     */
    private String v;

    private String k;

    /**
     * fieldCode 字段code.
     */
    private Integer type;

    /**
     * Set id ID.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id ID.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set v 字段值.
     */
    public void setV(String v){
        this.v = v;
    }

    /**
     * Get v 字段值.
     *
     * @return the string
     */
    public String getV(){
        return v;
    }

    public String getK() {
        return k;
    }

    public void setK(String k) {
        this.k = k;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}

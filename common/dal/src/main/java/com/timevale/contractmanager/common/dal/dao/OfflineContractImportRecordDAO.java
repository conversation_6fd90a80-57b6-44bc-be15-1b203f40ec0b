package com.timevale.contractmanager.common.dal.dao;

import com.timevale.contractmanager.common.dal.bean.OfflineContractImportRecordDO;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Date;

/**
 * The Table offline_contract_import_record.
 * 纸质合同导入记录
 * <AUTHOR> Kunpeng
 */
public interface OfflineContractImportRecordDAO{

    @Insert({
        "INSERT INTO offline_contract_import_record",
            "(RECORD_ID,MENU_ID,IMPORT_WAY,EXTRACT_WAY,EXTRACT_CONFIG,STATUS,CONTRACT_SIZE",
            ",IMPORTER_OID,IMPORTER_GID,IMPORTER_NAME,SUBJECT_OID,SUBJECT_GID,SUBJECT_NAME, IMPORT_CLIENT)",
        "VALUES(",
            "#{recordId,jdbcType=VARCHAR}",
            " , #{menuId,jdbcType=VARCHAR}",
            " , #{importWay,jdbcType=VARCHAR}",
            " , #{extractWay,jdbcType=VARCHAR}",
            " , #{extractConfig,jdbcType=LONGVARCHAR}",
            " , #{status,jdbcType=VARCHAR}",
            " , #{contractSize,jdbcType=INTEGER}",
            " , #{importerOid,jdbcType=VARCHAR}",
            " , #{importerGid,jdbcType=VARCHAR}",
            " , #{importerName,jdbcType=VARCHAR}",
            " , #{subjectOid,jdbcType=VARCHAR}",
            " , #{subjectGid,jdbcType=VARCHAR}",
            " , #{subjectName,jdbcType=VARCHAR}",
            " , #{importClient,jdbcType=VARCHAR}",
            " )"
    })
    int insert(OfflineContractImportRecordDO entity);

    @Update("update offline_contract_import_record set status = #{status} where record_id = #{recordId}")
    int updateStatus(@Param("recordId") String recordId, @Param("status") String status);

    @Update("update offline_contract_import_record set success_size = success_size + 1 where record_id = #{recordId}")
    int increaseSuccess(@Param("recordId") String recordId);

    @Update("update offline_contract_import_record set failed_size = failed_size + 1 where record_id = #{recordId}")
    int increaseFailed(@Param("recordId") String recordId);

    @Update("update offline_contract_import_record set failed_size = 0 where record_id = #{recordId}")
    int clearFailed(@Param("recordId") String recordId);

    @Update({
        "<script>",
        "update offline_contract_import_record",
        " set status = #{newStatus}",
        " where record_id in",
        "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
        "#{item}",
        "</foreach>",
        " and status in",
        "<foreach collection='originStatusList' item='item' separator=',' open='(' close=')'>",
        "#{item}",
        "</foreach>",
        "</script>",
    })
    int updateStatusByRecordIds(
            @Param("list") List<String> recordIds,
            @Param("originStatusList") List<String> originStatusList,
            @Param("newStatus") String newStatus);

    @Delete({
            "<script>",
            "delete from offline_contract_import_record where record_id in",
            "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    int deleteByRecordIds(@Param("list") List<String> recordIds);

    @Select("select record_id from offline_contract_import_record where create_time < #{lastRemainDate}")
    List<String> selectRecordIdsByLastRemainDate(Date lastRemainDate);

    @Select("select * from offline_contract_import_record where record_id = #{recordId} limit 1")
    OfflineContractImportRecordDO getByRecordId(String recordId);

    @Select({
            "<script>",
            "select * from offline_contract_import_record where record_id in",
            "<foreach collection='list' item='item' separator=',' open='(' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<OfflineContractImportRecordDO> getByRecordIds(@Param("list") List<String> recordIds);

    @Select({
            "select * from offline_contract_import_record",
            " where subject_gid = #{subjectGid} and create_time >= #{createTime}",
            " order by create_time desc"
    })
    List<OfflineContractImportRecordDO> queryBySubjectGid(@Param("subjectGid") String subjectGid, @Param("createTime") Date createTime);

    @Select({
            "select * from offline_contract_import_record",
            " where subject_gid = #{subjectGid} and importer_gid = #{importerGid} and create_time >= #{createTime}",
            " order by create_time desc"
    })
    List<OfflineContractImportRecordDO> queryBySubjectGidAndImporterGid(@Param("subjectGid") String subjectGid,@Param("importerGid") String accountGid, @Param("createTime") Date createTime);
}

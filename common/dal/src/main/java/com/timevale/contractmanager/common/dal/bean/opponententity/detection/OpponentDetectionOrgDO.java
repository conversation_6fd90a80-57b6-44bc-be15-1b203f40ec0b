package com.timevale.contractmanager.common.dal.bean.opponententity.detection;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;

/**
 * @Author:jiany<PERSON>
 * @since 2021-08-13 15:15
 */
@Data
@Builder
public class OpponentDetectionOrgDO {
	/**
	 * id 自增主键.
	 */
	private Long id;
	/**
	 * createTime 创建时间.
	 */
	private Date createTime;
	/**
	 * modifyTime 更新时间.
	 */
	private Date modifyTime;
	/**
	 *tenantGid 租户gid
	 */
	private String tenantGid;

	/**
	 * 检测任务id
	 */
	private String detectionTaskId;

	/**
	 * 检测企业uuid
	 */
	private String detectionOrgId;

	/**
	 * 相对方企业uuid
	 */
	private String opponentEntityId;
	/**
	 * 问题映射
	 */
	private Integer problemMapping;

	/**
	 * 风险等级映射
	 */
	private Integer riskLevelMapping;

	/**
	 * 检测状态,1:完成发现问题、2:未检测、3:完成且未发现问题
	 */
	private Integer detectionStatus;
	/**
	 * 耗时
	 */
	private Long elapsedTime;

	/**
	 * 唯一值
	 */
	private String uniCode;

	private String orgName;

	private Integer blackLevel;

	private Integer orgNum;

	private String entityOid;

	private String socialCreditCode;
	@Tolerate
	public OpponentDetectionOrgDO(){}
}

package com.timevale.contractmanager.common.dal.dao.grouping;

import com.timevale.contractmanager.common.dal.bean.grouping.CustomeFieldSysDO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * The Table custome_field_sys.
 * 自定义字段配置-系统默认（所有企业共用）
 * <AUTHOR> Kunpeng
 */
public interface CustomeFieldSysDAO{

    @Select("SELECT ID,FIELD_ID,FIELD_CODE,FIELD_NAME,PROPRIETARY,DISPLAY,FREEZED FROM custome_field_sys WHERE TYPE = #{type}")
    List<CustomeFieldSysDO> queryByType(Integer type);
}

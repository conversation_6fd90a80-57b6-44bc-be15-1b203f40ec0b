package com.timevale.contractmanager.common.dal.dao.opponententity;

import com.timevale.contractmanager.common.dal.bean.opponententity.OpponentOrgSupplierMappingDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Author:jianyang
 * @since 2021-03-08 16:06
 */
public interface OpponentOrgSupplierMappingDAO {

	@Select("select * from opponent_org_supplier_mapping where org_name = #{orgName} and supplier_type = #{supplierType}")
	public OpponentOrgSupplierMappingDO selectByOrgNameAndSupplier(@Param("orgName") String orgName,@Param("supplierType") Integer supplierType);

	@Insert({"INSERT INTO opponent_org_supplier_mapping (org_name,supplier_type,org_code,entity_oid,entity_gid) " +
			"VALUES(#{orgName},#{supplierType},#{orgCode},#{entityOid},#{entityGid})"})
	@Options(useGeneratedKeys = true, keyProperty = "id")
	public int insert(OpponentOrgSupplierMappingDO entity);
}

package com.timevale.contractmanager.common.dal.bean.sharesign;

import java.util.Date;

/**
 * The table.
 * 分享签流程记录
 * <AUTHOR> Kunpeng
 */
public class ShareSignProcessDO{

    /**
     * createTime 创建时间.
     */
    private Date createTime;
    /**
     * updateTime 更新时间.
     */
    private Date updateTime;
    /**
     * id 自增主键.
     */
    private Long id;
    /**
     * processId 参与任务生成的流程id.
     */
    private String processId;
    /**
     * shareSignTaskId 分享签署任务id.
     */
    private String shareSignTaskId;
    /**
     * participantId 分享签署任务参与方id.
     */
    private String participantId;
    /**
     * participantType 分享签署任务参与方类型.
     */
    private Integer participantType;
    /**
     * account 参与人手机号或邮箱.
     */
    private String account;
    /**
     * accountOid 参与人oid.
     */
    private String accountOid;
    /**
     * accountGid 参与人gid.
     */
    private String accountGid;
    /**
     * subjectOid 参与人主体oid.
     */
    private String subjectOid;
    /**
     * subjectName 参与人主体名称.
     */
    private String subjectName;
    /**
     * deleted 是否删除.
     */
    private Integer deleted;
    /**
     * status 状态，0-进行中，1-发起成功.
     */
    private Integer status;

    /**
     * Set createTime 创建时间.
     */
    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    /**
     * Get createTime 创建时间.
     *
     * @return the string
     */
    public Date getCreateTime(){
        return createTime;
    }

    /**
     * Set updateTime 更新时间.
     */
    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    /**
     * Get updateTime 更新时间.
     *
     * @return the string
     */
    public Date getUpdateTime(){
        return updateTime;
    }

    /**
     * Set id 自增主键.
     */
    public void setId(Long id){
        this.id = id;
    }

    /**
     * Get id 自增主键.
     *
     * @return the string
     */
    public Long getId(){
        return id;
    }

    /**
     * Set processId 参与任务生成的流程id.
     */
    public void setProcessId(String processId){
        this.processId = processId;
    }

    /**
     * Get processId 参与任务生成的流程id.
     *
     * @return the string
     */
    public String getProcessId(){
        return processId;
    }

    /**
     * Set shareSignTaskId 分享签署任务id.
     */
    public void setShareSignTaskId(String shareSignTaskId){
        this.shareSignTaskId = shareSignTaskId;
    }

    /**
     * Get shareSignTaskId 分享签署任务id.
     *
     * @return the string
     */
    public String getShareSignTaskId(){
        return shareSignTaskId;
    }

    /**
     * Set participantId 分享签署任务参与方id.
     */
    public void setParticipantId(String participantId){
        this.participantId = participantId;
    }

    /**
     * Get participantId 分享签署任务参与方id.
     *
     * @return the string
     */
    public String getParticipantId(){
        return participantId;
    }

    /**
     * Set participantType 分享签署任务参与方类型.
     */
    public void setParticipantType(Integer participantType){
        this.participantType = participantType;
    }

    /**
     * Get participantType 分享签署任务参与方类型.
     *
     * @return the string
     */
    public Integer getParticipantType(){
        return participantType;
    }

    /**
     * Set account 参与人手机号或邮箱.
     */
    public void setAccount(String account){
        this.account = account;
    }

    /**
     * Get account 参与人手机号或邮箱.
     *
     * @return the string
     */
    public String getAccount(){
        return account;
    }

    /**
     * Set accountOid 参与人oid.
     */
    public void setAccountOid(String accountOid){
        this.accountOid = accountOid;
    }

    /**
     * Get accountOid 参与人oid.
     *
     * @return the string
     */
    public String getAccountOid(){
        return accountOid;
    }

    /**
     * Set accountGid 参与人gid.
     */
    public void setAccountGid(String accountGid){
        this.accountGid = accountGid;
    }

    /**
     * Get accountGid 参与人gid.
     *
     * @return the string
     */
    public String getAccountGid(){
        return accountGid;
    }

    /**
     * Set subjectOid 参与主体oid.
     */
    public void setSubjectOid(String subjectOid){
        this.subjectOid = subjectOid;
    }

    /**
     * Get subjectOid 参与主体oid.
     *
     * @return the string
     */
    public String getSubjectOid(){
        return subjectOid;
    }

    /**
     * Set subjectName 参与主体名称.
     */
    public void setSubjectName(String subjectName){
        this.subjectName = subjectName;
    }

    /**
     * Get subjectName 参与主体名称.
     *
     * @return the string
     */
    public String getSubjectName(){
        return subjectName;
    }

    /**
     * Set deleted 是否删除.
     */
    public void setDeleted(Integer deleted){
        this.deleted = deleted;
    }

    /**
     * Get deleted 是否删除.
     *
     * @return the string
     */
    public Integer getDeleted(){
        return deleted;
    }

    /**
     * Set status 状态，0-进行中，1-发起成功
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * Set status 状态，0-进行中，1-发起成功
     * @return the string
     */
    public Integer getStatus() {
        return status;
    }
}

package com.timevale.contractmanager.common.dal.bean;

import lombok.Data;

import java.util.Date;

/**
 * The table. 纸质合同导入记录对应的合同信息
 *
 * <AUTHOR> Kunpeng
 */
@Data
public class OfflineContractImportProcessDO {

    /** id 自增id. */
    private Long id;
    /** recordId 导入记录id. */
    private String recordId;
    /** recordProcessId 导入记录流程id, 一个导入流程对应一个合同流程. */
    private String recordProcessId;
    /** processId 合同流程id. */
    private String processId;
    /** processInfo 合同信息. */
    private String processInfo;
    /** status 导入状态. */
    private String status;
    /** status 失败原因. */
    private String failReason;
    /** createTime 创建时间. */
    private Date createTime;
    /** updateTime 更新时间. */
    private Date updateTime;
}

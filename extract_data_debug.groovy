// 获取 content 字段
def content = request.get("content")

// 调试：输出原始内容
println "原始内容: ${content}"
println "内容长度: ${content?.length()}"

// 提取 fileKey
def fileKeyPattern = /fileKey:\s*([^,]+)/
def fileKeyMatcher = content =~ fileKeyPattern
def fileKey = ""
if (fileKeyMatcher.find()) {
    fileKey = fileKeyMatcher.group(1).trim()
    println "找到 fileKey: ${fileKey}"
} else {
    println "未找到 fileKey"
}

// 提取 JSON 内容
def jsonPattern = /file fill content:\s*(\{.*\})/
def jsonMatcher = content =~ jsonPattern
def result = [:]

println "尝试匹配 JSON 模式..."
println "JSON 正则模式: ${jsonPattern}"

if (jsonMatcher.find()) {
    def jsonContent = jsonMatcher.group(1)
    println "找到 JSON 内容: ${jsonContent}"
    println "JSON 内容长度: ${jsonContent.length()}"
    
    // 添加 fileKey
    if (fileKey && !fileKey.isEmpty()) {
        result.fileKey = fileKey
        println "添加 fileKey 到结果"
    }
    
    // 定义需要提取的字段（根据实际数据调整）
    def fieldsToExtract = [
        'participantList', 'hashs', 'month', 'year', 'contractNo',
        'remark', 'title', 'day', 'participantValue', 'fileNames'
    ]
    
    println "开始提取字段..."
    
    // 提取非空字段
    fieldsToExtract.each { field ->
        // 尝试字符串值模式
        def stringPattern = /"${field}"\s*:\s*"([^"]*)"/
        def stringMatcher = jsonContent =~ stringPattern

        // 尝试数组值模式
        def arrayPattern = /"${field}"\s*:\s*(\[[^\]]*\])/
        def arrayMatcher = jsonContent =~ arrayPattern

        if (stringMatcher.find()) {
            def value = stringMatcher.group(1).trim()
            println "字段 ${field}: 字符串值='${value}'"

            if (value && !value.isEmpty()) {
                // 对于contractNo字段，如果值为"-"也不返回
                if (field == 'contractNo' && value == '-') {
                    println "  -> 跳过 ${field}，值为 '-'"
                } else {
                    result[field] = value
                    println "  -> 添加 ${field}: '${value}'"
                }
            } else {
                println "  -> 跳过 ${field}，值为空"
            }
        } else if (arrayMatcher.find()) {
            def arrayValue = arrayMatcher.group(1).trim()
            println "字段 ${field}: 数组值='${arrayValue}'"

            if (arrayValue && !arrayValue.isEmpty() && arrayValue != '[]') {
                // 检查数组是否只包含"-"
                if (field == 'contractNo' && arrayValue.contains('"-"')) {
                    println "  -> 跳过 ${field}，数组中包含 '-'"
                } else {
                    // 提取数组中的元素作为单独字段
                    def elementPattern = /"([^"]*)"/
                    def elementMatcher = arrayValue =~ elementPattern
                    def elements = []
                    while (elementMatcher.find()) {
                        elements.add(elementMatcher.group(1))
                    }

                    println "  -> 数组元素: ${elements}"

                    // 根据字段类型分配元素，为每个元素创建单独字段
                    if (field == 'participantList') {
                        for (int i = 0; i < elements.size(); i++) {
                            result["participant${i + 1}"] = elements[i]
                            println "  -> 添加 participant${i + 1}: '${elements[i]}'"
                        }
                    } else if (field == 'hashs') {
                        for (int i = 0; i < elements.size(); i++) {
                            result["hash${i + 1}"] = elements[i]
                            println "  -> 添加 hash${i + 1}: '${elements[i]}'"
                        }
                    } else if (field == 'fileNames') {
                        for (int i = 0; i < elements.size(); i++) {
                            result["fileName${i + 1}"] = elements[i]
                            println "  -> 添加 fileName${i + 1}: '${elements[i]}'"
                        }
                    } else if (field == 'contractNo') {
                        for (int i = 0; i < elements.size(); i++) {
                            result["contractNo${i + 1}"] = elements[i]
                            println "  -> 添加 contractNo${i + 1}: '${elements[i]}'"
                        }
                    }
                }
            } else {
                println "  -> 跳过 ${field}，数组为空"
            }
        } else {
            println "字段 ${field}: 未找到匹配"
        }
    }
    
    // 注意：这个数据格式中没有租户名称字段，跳过租户名称拼接
    println "当前数据格式中没有租户名称字段，跳过拼接步骤"
} else {
    println "未找到 JSON 内容"
    println "尝试查找 'file fill content' 字符串..."
    if (content.contains("file fill content")) {
        println "找到 'file fill content' 字符串"
        def startIndex = content.indexOf("file fill content")
        println "位置: ${startIndex}"
        println "后续内容: ${content.substring(startIndex, Math.min(startIndex + 200, content.length()))}"
    } else {
        println "未找到 'file fill content' 字符串"
    }
}

println "最终结果:"
result.each { key, value ->
    println "  ${key}: ${value}"
}

// 返回结果
return result

// 获取 content 字段
def content = request.get("content")

// 调试：输出原始内容
println "原始内容: ${content}"
println "内容长度: ${content?.length()}"

// 提取 fileKey
def fileKeyPattern = /fileKey:\s*([^,]+)/
def fileKeyMatcher = content =~ fileKeyPattern
def fileKey = ""
if (fileKeyMatcher.find()) {
    fileKey = fileKeyMatcher.group(1).trim()
    println "找到 fileKey: ${fileKey}"
} else {
    println "未找到 fileKey"
}

// 提取 JSON 内容
def jsonPattern = /file fill content:\s*(\{.*\})/
def jsonMatcher = content =~ jsonPattern
def result = [:]

if (jsonMatcher.find()) {
    def jsonContent = jsonMatcher.group(1)
    println "找到 JSON 内容: ${jsonContent}"
    
    // 添加 fileKey
    if (fileKey && !fileKey.isEmpty()) {
        result.fileKey = fileKey
        println "添加 fileKey 到结果"
    }
    
    // 定义需要提取的字段
    def fieldsToExtract = [
        'date', 'authResourceOrg', 'effectiveStartTime', 'effectiveEndTime',
        'authResourceTemplateDelete', 'authResourceApi', 'authResourceApproval',
        'authResourceContract', 'authResourceTemplateUse', 'authResourceTemplateAuth',
        'authResourceSeal', 'authResourceTemplate', 'authTenantName'
    ]
    
    println "开始提取字段..."
    
    // 提取非空字段
    fieldsToExtract.each { field ->
        def pattern = /"${field}"\s*:\s*"([^"]*)"/
        def matcher = jsonContent =~ pattern
        if (matcher.find()) {
            def value = matcher.group(1).trim()
            println "字段 ${field}: 原始值='${value}'"
            
            if (value && !value.isEmpty()) {
                // 对于以authResource开头的字段，如果值为"-"也不返回
                if (field.startsWith('authResource') && value == '-') {
                    println "  -> 跳过 ${field}，值为 '-'"
                } else {
                    // 移除字段值前后的括号
                    def cleanValue = value.replaceAll(/^\(|\)$/, '')
                    result[field] = cleanValue
                    println "  -> 添加 ${field}: '${cleanValue}'"
                }
            } else {
                println "  -> 跳过 ${field}，值为空"
            }
        } else {
            println "字段 ${field}: 未找到匹配"
        }
    }
    
    println "开始处理租户名称拼接..."
    
    // 按顺序拼接租户名称字段（不包括authTenantName）
    def tenantNames = []
    ['parentTenantName', 'subTenantName', 'childTenantName'].each { field ->
        def pattern = /"${field}"\s*:\s*"([^"]*)"/
        def matcher = jsonContent =~ pattern
        if (matcher.find()) {
            def value = matcher.group(1).trim()
            println "租户字段 ${field}: '${value}'"
            if (value && !value.isEmpty()) {
                // 保留原始值（包括括号）
                tenantNames.add(value)
                println "  -> 添加到租户列表: '${value}'"
            }
        } else {
            println "租户字段 ${field}: 未找到匹配"
        }
    }
    
    if (!tenantNames.isEmpty()) {
        result.concatenatedTenantNames = tenantNames.join('')
        println "拼接的租户名称: '${result.concatenatedTenantNames}'"
    } else {
        println "没有找到有效的租户名称"
    }
} else {
    println "未找到 JSON 内容"
}

println "最终结果:"
result.each { key, value ->
    println "  ${key}: ${value}"
}

// 返回结果
return result

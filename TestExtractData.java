import java.util.*;
import java.util.regex.*;

public class TestExtractData {
    
    public static void main(String[] args) {
        // 测试用例1：包含值为"-"的authResource字段
        String testData1 = "2025-09-09 17:34:04,479 INFO impl.AuthRelationCoreServiceImpl - [ConsumeMessageThread_1] [TrackId=10100011220T233T17574104433270893] [MsgId=FE800000000000006C0222FFFEDD1CF5000027002C5A2CF7E5932F79] generateRelationFile fileKey: $b766b14b-f98e-4976-a70d-33aac6e47ab8$4038515047, file fill content: {\"date\":\"2025 年 09 月 09 日\",\"authResourceOrg\":\"-\",\"effectiveEndTime\":\"2030 年 07 月 31 日\",\"authResourceTemplateDelete\":\"\",\"parentTenantName\":\"(esigntest多组织测试企业D1)\",\"authResourceApi\":\"\",\"authResourceApproval\":\"\",\"authResourceContract\":\"\",\"authResourceTemplateUse\":\"\",\"effectiveStartTime\":\"2025 年 09 月 09 日\",\"authResourceTemplateAuth\":\"\",\"subTenantName\":\"、(esigntest多组织测试企业S)\",\"authTenantName\":\"(esigntest多组织测试企业A1)\",\"authResourceSeal\":\"\",\"authResourceTemplate\":\"\",\"childTenantName\":\"、(esigntest多组织测试企业T)\"}";
        
        System.out.println("测试用例1：包含值为'-'的authResource字段");
        System.out.println("==================================================");
        Map<String, String> result1 = extractDataFromLog(testData1);
        
        for (Map.Entry<String, String> entry : result1.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        
        System.out.println("\n验证：authResourceOrg字段值为'-'，应该被过滤掉");
        System.out.println("authResourceOrg是否存在: " + result1.containsKey("authResourceOrg"));
        
        System.out.println("\n拼接的租户名称: " + result1.getOrDefault("concatenatedTenantNames", "无有效租户名称"));
        System.out.println("==================================================\n");
    }
    
    // 简单的 JSON 解析函数
    public static String parseJsonValue(String jsonContent, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]*)\"";
        Pattern p = Pattern.compile(pattern);
        Matcher matcher = p.matcher(jsonContent);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    public static Map<String, String> extractDataFromLog(String logLine) {
        Map<String, String> result = new HashMap<>();
        
        // 提取 fileKey
        Pattern fileKeyPattern = Pattern.compile("fileKey:\\s*([^,]+)");
        Matcher fileKeyMatcher = fileKeyPattern.matcher(logLine);
        if (fileKeyMatcher.find()) {
            String fileKey = fileKeyMatcher.group(1).trim();
            if (fileKey != null && !fileKey.isEmpty()) {
                result.put("fileKey", fileKey);
            }
        }
        
        // 提取 JSON 内容
        Pattern jsonPattern = Pattern.compile("file fill content:\\s*(\\{.*\\})");
        Matcher jsonMatcher = jsonPattern.matcher(logLine);
        if (jsonMatcher.find()) {
            String jsonContent = jsonMatcher.group(1);
            
            // 定义需要提取的字段（不包括用于拼接的租户字段）
            String[] fieldsToExtract = {
                "date", "authResourceOrg", "effectiveStartTime", "effectiveEndTime",
                "authResourceTemplateDelete", "authResourceApi", "authResourceApproval",
                "authResourceContract", "authResourceTemplateUse", "authResourceTemplateAuth",
                "authResourceSeal", "authResourceTemplate", "authTenantName"
            };
            
            // 提取非空字段
            for (String field : fieldsToExtract) {
                String value = parseJsonValue(jsonContent, field);
                if (value != null && !value.isEmpty()) {
                    // 对于以authResource开头的字段，如果值为"-"也不返回
                    if (field.startsWith("authResource") && value.trim().equals("-")) {
                        continue; // 跳过这个字段
                    }
                    // 移除字段值前后的括号
                    String cleanValue = value.replaceAll("^\\(|\\)$", "");
                    result.put(field, cleanValue);
                }
            }
            
            // 按顺序拼接租户名称字段（不包括authTenantName）
            List<String> tenantNames = new ArrayList<>();
            String[] tenantFields = {"parentTenantName", "subTenantName", "childTenantName"};
            
            for (String field : tenantFields) {
                String value = parseJsonValue(jsonContent, field);
                if (value != null && !value.isEmpty()) {
                    // 保留原始值（包括括号）
                    tenantNames.add(value);
                }
            }
            
            if (!tenantNames.isEmpty()) {
                result.put("concatenatedTenantNames", String.join("", tenantNames));
            }
        }
        
        return result;
    }
}

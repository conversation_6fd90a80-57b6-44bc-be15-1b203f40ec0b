#!/usr/bin/env groovy

// 示例日志数据
def logData = '''2025-09-10 14:49:48,807 INFO impl.AuthRelationCoreServiceImpl - [ConsumeMessageThread_1] [TrackId=10100004152T293T17574869875221089] [MsgId=0A6409D10000113B39E93187DEB40160] generateOrganizationAssociationConfirmationFileInfo fileKey: $840f3fd6-f8af-441a-ab2e-b41bf5426caf$761313317, file fill content: {"date":"2025 年 09 月 10 日","authResourceOrg":" 组织管理（机构、部门、员工和角色权限管理）","effectiveEndTime":"2030 年 07 月 30 日","authResourceTemplateDelete":"","parentTenantName":"(esigntest集测多组织测试企业G)","authResourceApi":"","authResourceApproval":"","authResourceContract":"","authResourceTemplateUse":"","effectiveStartTime":"2025 年 09 月 10 日","authResourceTemplateAuth":"","subTenantName":"","authTenantName":"(esigntest集测多组织测试企业F)","authResourceSeal":"","authResourceTemplate":"","childTenantName":""}'''

// 简单的 JSON 解析函数
def parseJsonValue(String jsonContent, String key) {
    def pattern = /"${key}"\s*:\s*"([^"]*)"/
    def matcher = jsonContent =~ pattern
    if (matcher.find()) {
        return matcher.group(1).trim()
    }
    return null
}

def extractDataFromLog(String logLine) {
    def result = [:]
    
    // 提取 fileKey
    def fileKeyPattern = /fileKey:\s*([^,]+)/
    def fileKeyMatcher = logLine =~ fileKeyPattern
    if (fileKeyMatcher.find()) {
        def fileKey = fileKeyMatcher.group(1).trim()
        if (fileKey && !fileKey.isEmpty()) {
            result.fileKey = fileKey
        }
    }
    
    // 提取 JSON 内容
    def jsonPattern = /file fill content:\s*(\{.*\})/
    def jsonMatcher = logLine =~ jsonPattern
    if (jsonMatcher.find()) {
        def jsonContent = jsonMatcher.group(1)
        
        // 定义需要提取的字段
        def fieldsToExtract = [
            'date', 'authResourceOrg', 'effectiveStartTime', 'effectiveEndTime',
            'authResourceTemplateDelete', 'authResourceApi', 'authResourceApproval',
            'authResourceContract', 'authResourceTemplateUse', 'authResourceTemplateAuth',
            'authResourceSeal', 'authResourceTemplate', 'authTenantName',
            'parentTenantName', 'subTenantName', 'childTenantName'
        ]
        
        // 提取非空字段
        fieldsToExtract.each { field ->
            def value = parseJsonValue(jsonContent, field)
            if (value != null && !value.isEmpty()) {
                // 移除字段值前后的括号
                def cleanValue = value.replaceAll(/^\(|\)$/, '')
                result[field] = cleanValue
            }
        }
        
        // 按顺序拼接租户名称字段（不包括authTenantName）
        def tenantNames = []
        ['parentTenantName', 'subTenantName', 'childTenantName'].each { field ->
            def value = result[field]
            if (value) {
                // 移除括号
                def cleanValue = value.replaceAll(/^\(|\)$/, '')
                tenantNames.add(cleanValue)
            }
        }
        
        if (!tenantNames.isEmpty()) {
            result.concatenatedTenantNames = tenantNames.join('')
        }
    }
    
    return result
}

// 执行提取
def extractedData = extractDataFromLog(logData)

// 输出结果
println "提取的数据:"
println "=" * 50

extractedData.each { key, value ->
    println "${key}: ${value}"
}

println "\n" + "=" * 50
println "拼接的租户名称: ${extractedData.concatenatedTenantNames ?: '无有效租户名称'}"

// 如果你想要处理多行日志，可以使用这个函数
def processMultipleLogLines(String logContent) {
    def results = []
    logContent.split('\n').each { line ->
        if (line.contains('generateOrganizationAssociationConfirmationFileInfo')) {
            def extracted = extractDataFromLog(line)
            if (!extracted.isEmpty()) {
                results.add(extracted)
            }
        }
    }
    return results
}

// 示例：处理单行日志的函数
def processSingleLogLine(String logLine) {
    return extractDataFromLog(logLine)
}

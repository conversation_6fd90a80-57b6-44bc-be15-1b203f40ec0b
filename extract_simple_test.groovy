// 获取 content 字段
def content = request.get("content")

println "=== 调试信息 ==="
println "原始内容: ${content}"
println "内容长度: ${content?.length()}"

def result = [:]

// 提取 fileKey
def fileKeyPattern = /fileKey:\s*([^,]+)/
def fileKeyMatcher = content =~ fileKeyPattern
if (fileKeyMatcher.find()) {
    def fileKey = fileKeyMatcher.group(1).trim()
    result.fileKey = fileKey
    println "✓ fileKey: ${fileKey}"
}

// 尝试多种 JSON 提取模式
println "\n=== 尝试不同的 JSON 提取模式 ==="

// 模式1：原始模式
def jsonPattern1 = /file fill content:\s*(\{.*\})/
def jsonMatcher1 = content =~ jsonPattern1
if (jsonMatcher1.find()) {
    println "✓ 模式1成功: ${jsonMatcher1.group(1).substring(0, Math.min(100, jsonMatcher1.group(1).length()))}..."
} else {
    println "✗ 模式1失败"
}

// 模式2：更宽松的模式
def jsonPattern2 = /file fill content[:\s]*(\{.*\})/
def jsonMatcher2 = content =~ jsonPattern2
if (jsonMatcher2.find()) {
    println "✓ 模式2成功: ${jsonMatcher2.group(1).substring(0, Math.min(100, jsonMatcher2.group(1).length()))}..."
} else {
    println "✗ 模式2失败"
}

// 模式3：非贪婪匹配
def jsonPattern3 = /file fill content:\s*(\{.*?\})/
def jsonMatcher3 = content =~ jsonPattern3
if (jsonMatcher3.find()) {
    println "✓ 模式3成功: ${jsonMatcher3.group(1).substring(0, Math.min(100, jsonMatcher3.group(1).length()))}..."
} else {
    println "✗ 模式3失败"
}

// 手动查找
println "\n=== 手动查找 ==="
def fillContentIndex = content.indexOf("file fill content")
if (fillContentIndex >= 0) {
    println "找到 'file fill content' 在位置: ${fillContentIndex}"
    def afterFillContent = content.substring(fillContentIndex)
    println "后续内容: ${afterFillContent.substring(0, Math.min(200, afterFillContent.length()))}"
    
    def openBraceIndex = afterFillContent.indexOf("{")
    if (openBraceIndex >= 0) {
        println "找到开始大括号在位置: ${openBraceIndex}"
        def jsonStart = fillContentIndex + openBraceIndex
        def jsonPart = content.substring(jsonStart)
        
        // 简单的大括号匹配
        def braceCount = 0
        def jsonEnd = -1
        for (int i = 0; i < jsonPart.length(); i++) {
            if (jsonPart.charAt(i) == '{') {
                braceCount++
            } else if (jsonPart.charAt(i) == '}') {
                braceCount--
                if (braceCount == 0) {
                    jsonEnd = i + 1
                    break
                }
            }
        }
        
        if (jsonEnd > 0) {
            def extractedJson = jsonPart.substring(0, jsonEnd)
            println "手动提取的 JSON: ${extractedJson.substring(0, Math.min(100, extractedJson.length()))}..."
            
            // 尝试解析这个 JSON
            def datePattern = /"date"\s*:\s*"([^"]*)"/
            def dateMatcher = extractedJson =~ datePattern
            if (dateMatcher.find()) {
                result.date = dateMatcher.group(1).trim()
                println "✓ 从手动提取的 JSON 中找到 date: ${result.date}"
            }
        }
    }
}

println "\n=== 最终结果 ==="
result.each { key, value ->
    println "${key}: ${value}"
}

return result

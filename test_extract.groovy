// 测试数据
def testContent = '''2025-09-09 17:34:04,479 INFO  impl.AuthRelationCoreServiceImpl - [ConsumeMessageThread_1] [TrackId=10100011220T233T17574104433270893] [MsgId=FE800000000000006C0222FFFEDD1CF5000027002C5A2CF7E5932F79] generateRelationFile fileKey: $b766b14b-f98e-4976-a70d-33aac6e47ab8$4038515047, file fill content: {"date":"2025 年 09 月 09 日","authResourceOrg":"-","effectiveEndTime":"2030 年 07 月 31 日","authResourceTemplateDelete":"","parentTenantName":"(esigntest多组织测试企业D1)","authResourceApi":"","authResourceApproval":"","authResourceContract":"","authResourceTemplateUse":"","effectiveStartTime":"2025 年 09 月 09 日","authResourceTemplateAuth":"","subTenantName":"、(esigntest多组织测试企业S)","authTenantName":"(esigntest多组织测试企业A1)","authResourceSeal":"","authResourceTemplate":"","childTenantName":"、(esigntest多组织测试企业T)"} '''

// 模拟 request 对象
def request = [:]
request.put("content", testContent)

// 获取 content 字段
def content = request.get("content")

// 提取 fileKey
def fileKeyPattern = /fileKey:\s*([^,]+)/
def fileKeyMatcher = content =~ fileKeyPattern
def fileKey = ""
if (fileKeyMatcher.find()) {
    fileKey = fileKeyMatcher.group(1).trim()
}

// 提取 JSON 内容
def jsonPattern = /file fill content:\s*(\{.*\})/
def jsonMatcher = content =~ jsonPattern
def result = [:]

if (jsonMatcher.find()) {
    def jsonContent = jsonMatcher.group(1)
    
    // 添加 fileKey
    if (fileKey && !fileKey.isEmpty()) {
        result.fileKey = fileKey
    }
    
    // 定义需要提取的字段
    def fieldsToExtract = [
        'date', 'authResourceOrg', 'effectiveStartTime', 'effectiveEndTime',
        'authResourceTemplateDelete', 'authResourceApi', 'authResourceApproval',
        'authResourceContract', 'authResourceTemplateUse', 'authResourceTemplateAuth',
        'authResourceSeal', 'authResourceTemplate', 'authTenantName'
    ]
    
    // 提取非空字段
    fieldsToExtract.each { field ->
        def pattern = /"${field}"\s*:\s*"([^"]*)"/
        def matcher = jsonContent =~ pattern
        if (matcher.find()) {
            def value = matcher.group(1).trim()
            if (value && !value.isEmpty()) {
                // 对于以authResource开头的字段，如果值为"-"也不返回
                if (field.startsWith('authResource') && value == '-') {
                    // 跳过这个字段，继续处理下一个
                } else {
                    // 移除字段值前后的括号
                    def cleanValue = value.replaceAll(/^\(|\)$/, '')
                    result[field] = cleanValue
                }
            }
        }
    }
    
    // 按顺序拼接租户名称字段（不包括authTenantName）
    def tenantNames = []
    ['parentTenantName', 'subTenantName', 'childTenantName'].each { field ->
        def pattern = /"${field}"\s*:\s*"([^"]*)"/
        def matcher = jsonContent =~ pattern
        if (matcher.find()) {
            def value = matcher.group(1).trim()
            if (value && !value.isEmpty()) {
                // 保留原始值（包括括号）
                tenantNames.add(value)
            }
        }
    }
    
    if (!tenantNames.isEmpty()) {
        result.concatenatedTenantNames = tenantNames.join('')
    }
}

// 输出结果
println "提取的数据:"
println "=" * 50
result.each { key, value ->
    println "${key}: ${value}"
}

println "\n验证：authResourceOrg字段值为'-'，应该被过滤掉"
println "authResourceOrg是否存在: ${result.containsKey('authResourceOrg')}"

// 返回结果
return result

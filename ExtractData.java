import java.util.*;
import java.util.regex.*;

public class ExtractData {
    
    public static void main(String[] args) {
        // 示例日志数据
        String logData = "2025-09-10 14:49:48,807 INFO impl.AuthRelationCoreServiceImpl - [ConsumeMessageThread_1] [TrackId=10100004152T293T17574869875221089] [MsgId=0A6409D10000113B39E93187DEB40160] generateOrganizationAssociationConfirmationFileInfo fileKey: $840f3fd6-f8af-441a-ab2e-b41bf5426caf$761313317, file fill content: {\"date\":\"2025 年 09 月 10 日\",\"authResourceOrg\":\" 组织管理（机构、部门、员工和角色权限管理）\",\"effectiveEndTime\":\"2030 年 07 月 30 日\",\"authResourceTemplateDelete\":\"\",\"parentTenantName\":\"(esigntest集测多组织测试企业G)\",\"authResourceApi\":\"\",\"authResourceApproval\":\"\",\"authResourceContract\":\"\",\"authResourceTemplateUse\":\"\",\"effectiveStartTime\":\"2025 年 09 月 10 日\",\"authResourceTemplateAuth\":\"\",\"subTenantName\":\"\",\"authTenantName\":\"(esigntest集测多组织测试企业F)\",\"authResourceSeal\":\"\",\"authResourceTemplate\":\"\",\"childTenantName\":\"\"}";
        
        // 执行提取
        Map<String, String> extractedData = extractDataFromLog(logData);
        
        // 输出结果
        System.out.println("提取的数据:");
        System.out.println("==================================================");
        
        for (Map.Entry<String, String> entry : extractedData.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        
        System.out.println("\n==================================================");
        System.out.println("拼接的租户名称: " + extractedData.getOrDefault("concatenatedTenantNames", "无有效租户名称"));
    }
    
    // 简单的 JSON 解析函数
    public static String parseJsonValue(String jsonContent, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]*)\"";
        Pattern p = Pattern.compile(pattern);
        Matcher matcher = p.matcher(jsonContent);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }
    
    public static Map<String, String> extractDataFromLog(String logLine) {
        Map<String, String> result = new HashMap<>();
        
        // 提取 fileKey
        Pattern fileKeyPattern = Pattern.compile("fileKey:\\s*([^,]+)");
        Matcher fileKeyMatcher = fileKeyPattern.matcher(logLine);
        if (fileKeyMatcher.find()) {
            String fileKey = fileKeyMatcher.group(1).trim();
            if (fileKey != null && !fileKey.isEmpty()) {
                result.put("fileKey", fileKey);
            }
        }
        
        // 提取 JSON 内容
        Pattern jsonPattern = Pattern.compile("file fill content:\\s*(\\{.*\\})");
        Matcher jsonMatcher = jsonPattern.matcher(logLine);
        if (jsonMatcher.find()) {
            String jsonContent = jsonMatcher.group(1);
            
            // 定义需要提取的字段
            String[] fieldsToExtract = {
                "date", "authResourceOrg", "effectiveStartTime", "effectiveEndTime",
                "authResourceTemplateDelete", "authResourceApi", "authResourceApproval",
                "authResourceContract", "authResourceTemplateUse", "authResourceTemplateAuth",
                "authResourceSeal", "authResourceTemplate", "authTenantName",
                "parentTenantName", "subTenantName", "childTenantName"
            };
            
            // 提取非空字段
            for (String field : fieldsToExtract) {
                String value = parseJsonValue(jsonContent, field);
                if (value != null && !value.isEmpty()) {
                    // 移除字段值前后的括号
                    String cleanValue = value.replaceAll("^\\(|\\)$", "");
                    result.put(field, cleanValue);
                }
            }
            
            // 按顺序拼接租户名称字段（不包括authTenantName）
            List<String> tenantNames = new ArrayList<>();
            String[] tenantFields = {"parentTenantName", "subTenantName", "childTenantName"};

            for (String field : tenantFields) {
                String value = result.get(field);
                if (value != null && !value.isEmpty()) {
                    tenantNames.add(value);
                }
            }
            
            if (!tenantNames.isEmpty()) {
                result.put("concatenatedTenantNames", String.join("", tenantNames));
            }
        }
        
        return result;
    }
    
    // 处理多行日志的函数
    public static List<Map<String, String>> processMultipleLogLines(String logContent) {
        List<Map<String, String>> results = new ArrayList<>();
        String[] lines = logContent.split("\n");
        
        for (String line : lines) {
            if (line.contains("generateOrganizationAssociationConfirmationFileInfo")) {
                Map<String, String> extracted = extractDataFromLog(line);
                if (!extracted.isEmpty()) {
                    results.add(extracted);
                }
            }
        }
        return results;
    }
    
    // 处理单行日志的函数
    public static Map<String, String> processSingleLogLine(String logLine) {
        return extractDataFromLog(logLine);
    }
}
